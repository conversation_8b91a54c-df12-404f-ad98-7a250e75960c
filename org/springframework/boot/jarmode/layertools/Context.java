package org.springframework.boot.jarmode.layertools;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.File;
import java.io.IOException;
import java.net.JarURLConnection;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Paths;
import java.security.CodeSource;
import java.security.ProtectionDomain;
import java.util.jar.JarFile;
import org.springframework.util.Assert;

/* loaded from: spring-boot-jarmode-layertools-2.6.6.jar:org/springframework/boot/jarmode/layertools/Context.class */
class Context {
    private final File archiveFile;
    private final File workingDir;
    private final String relativeDir;

    Context() {
        this(getSourceArchiveFile(), Paths.get(ConstantEnum.POINT, new String[0]).toAbsolutePath().normalize().toFile());
    }

    Context(File archiveFile, File workingDir) {
        Assert.state(isExistingFile(archiveFile) && isJarOrWar(archiveFile), "Unable to find source archive");
        this.archiveFile = archiveFile;
        this.workingDir = workingDir;
        this.relativeDir = deduceRelativeDir(archiveFile.getParentFile(), this.workingDir);
    }

    private boolean isExistingFile(File archiveFile) {
        return archiveFile != null && archiveFile.isFile() && archiveFile.exists();
    }

    private boolean isJarOrWar(File jarFile) {
        String name = jarFile.getName().toLowerCase();
        return name.endsWith(".jar") || name.endsWith(".war");
    }

    private static File getSourceArchiveFile() {
        try {
            ProtectionDomain domain = Context.class.getProtectionDomain();
            CodeSource codeSource = domain != null ? domain.getCodeSource() : null;
            URL location = codeSource != null ? codeSource.getLocation() : null;
            File source = location != null ? findSource(location) : null;
            if (source != null && source.exists()) {
                return source.getAbsoluteFile();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    private static File findSource(URL location) throws URISyntaxException, IOException {
        URLConnection connection = location.openConnection();
        if (connection instanceof JarURLConnection) {
            return getRootJarFile(((JarURLConnection) connection).getJarFile());
        }
        return new File(location.toURI());
    }

    private static File getRootJarFile(JarFile jarFile) {
        String name = jarFile.getName();
        int separator = name.indexOf("!/");
        if (separator > 0) {
            name = name.substring(0, separator);
        }
        return new File(name);
    }

    private String deduceRelativeDir(File sourceDirectory, File workingDir) {
        String sourcePath = sourceDirectory.getAbsolutePath();
        String workingPath = workingDir.getAbsolutePath();
        if (sourcePath.equals(workingPath) || !sourcePath.startsWith(workingPath)) {
            return null;
        }
        String relativePath = sourcePath.substring(workingPath.length() + 1);
        if (relativePath.isEmpty()) {
            return null;
        }
        return relativePath;
    }

    File getArchiveFile() {
        return this.archiveFile;
    }

    File getWorkingDir() {
        return this.workingDir;
    }

    String getRelativeArchiveDir() {
        return this.relativeDir;
    }
}
