package org.springframework.boot.jarmode.layertools;

import com.geely.gnds.ruoyi.common.constant.Constants;

/* loaded from: spring-boot-jarmode-layertools-2.6.6.jar:org/springframework/boot/jarmode/layertools/MissingValueException.class */
class MissingValueException extends RuntimeException {
    private final String optionName;

    MissingValueException(String optionName) {
        this.optionName = optionName;
    }

    @Override // java.lang.Throwable
    public String getMessage() {
        return Constants.GROUP_SPLIT + this.optionName;
    }
}
