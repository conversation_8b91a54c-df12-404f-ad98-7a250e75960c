# DataManagement API 接口文档

## 概述

本文档描述了 `/dataManagement/api/inner/getTesterToken` 接口的实现和使用方法。

## 接口列表

### 1. 获取测试器令牌

**接口地址：** `POST /dataManagement/api/inner/getTesterToken`

**请求参数：**
```json
{
    "method": "gnds.getTesterToken",
    "bizContent": "",
    "testerId": "测试器ID",
    "tenantCode": 1001,
    "timestamp": 1692000000000,
    "requestId": "请求ID",
    "sign": "签名"
}
```

**响应参数：**
```json
{
    "code": 200,
    "msg": "获取测试器令牌成功",
    "data": {
        "token": "tester_abc123...",
        "tokenType": "Bearer",
        "expiresIn": 7200,
        "refreshToken": "refresh_def456...",
        "scope": "tester:all",
        "testerId": "测试器ID",
        "tenantCode": 1001,
        "createTime": 1692000000000,
        "expireTime": 1692007200000
    }
}
```

### 2. 刷新测试器令牌

**接口地址：** `POST /dataManagement/api/inner/refreshTesterToken`

**请求参数：**
```json
{
    "method": "gnds.refreshTesterToken",
    "bizContent": "",
    "testerId": "测试器ID"
}
```

**响应参数：**
```json
{
    "code": 200,
    "msg": "刷新测试器令牌成功",
    "data": {
        "token": "tester_new123...",
        "tokenType": "Bearer",
        "expiresIn": 7200,
        "refreshToken": "refresh_new456...",
        "scope": "tester:all",
        "testerId": "测试器ID",
        "tenantCode": 1001,
        "createTime": 1692000000000,
        "expireTime": 1692007200000
    }
}
```

### 3. 验证测试器令牌

**接口地址：** `GET /dataManagement/api/inner/validateTesterToken`

**请求参数：**
- `token`: 要验证的令牌

**响应参数：**
```json
{
    "code": 200,
    "msg": "令牌验证成功",
    "data": true
}
```

## 实现特性

### 1. 令牌生成
- 使用 SHA-256 哈希算法生成令牌
- 令牌包含测试器ID、UUID和时间戳信息
- 默认过期时间为2小时（7200秒）

### 2. 令牌管理
- 内存缓存存储令牌信息
- 自动过期检查和清理
- 支持令牌撤销功能

### 3. 安全配置
- 内部接口路径已添加到安全白名单
- 不需要额外的认证即可访问
- 支持跨域请求

### 4. 错误处理
- 统一的错误响应格式
- 详细的日志记录
- 异常情况的优雅处理

## 使用示例

### Java 客户端示例

```java
// 创建请求对象
TesterTokenRequestDTO request = new TesterTokenRequestDTO();
request.setMethod("gnds.getTesterToken");
request.setBizContent("");
request.setTesterId("your-tester-id");

// 发送请求
RestTemplate restTemplate = new RestTemplate();
String url = "http://localhost:8080/dataManagement/api/inner/getTesterToken";
ResponseEntity<Result<TesterTokenResponseDTO>> response = 
    restTemplate.postForEntity(url, request, 
        new ParameterizedTypeReference<Result<TesterTokenResponseDTO>>() {});

// 处理响应
if (response.getBody().getCode() == 200) {
    TesterTokenResponseDTO tokenResponse = response.getBody().getData();
    String token = tokenResponse.getToken();
    // 使用令牌...
}
```

### cURL 示例

```bash
# 获取令牌
curl -X POST http://localhost:8080/dataManagement/api/inner/getTesterToken \
  -H "Content-Type: application/json" \
  -d '{
    "method": "gnds.getTesterToken",
    "bizContent": "",
    "testerId": "your-tester-id"
  }'

# 验证令牌
curl -X GET "http://localhost:8080/dataManagement/api/inner/validateTesterToken?token=your-token"
```

## 注意事项

1. **令牌安全性**：请妥善保管生成的令牌，避免泄露
2. **过期处理**：令牌过期后需要重新获取或刷新
3. **并发访问**：服务支持并发访问，使用线程安全的缓存实现
4. **监控日志**：建议监控接口调用日志，及时发现异常情况

## 扩展功能

未来可以考虑添加以下功能：
- 令牌持久化存储（Redis/数据库）
- 令牌使用统计和监控
- 更复杂的令牌权限控制
- 令牌黑名单机制
