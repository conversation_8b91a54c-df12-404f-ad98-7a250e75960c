package com.geely.gnds.tester.exception;

/* loaded from: TokenInvalidException.class */
public class TokenInvalidException extends RuntimeException {
    private static final long serialVersionUID = 1;
    private Integer code;
    private String message;

    public TokenInvalidException(String message) {
        this.message = message;
    }

    public TokenInvalidException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public TokenInvalidException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    @Override // java.lang.Throwable
    public String getMessage() {
        return this.message;
    }

    public Integer getCode() {
        return this.code;
    }
}
