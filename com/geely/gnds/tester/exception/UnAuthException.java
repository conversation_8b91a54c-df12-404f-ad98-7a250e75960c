package com.geely.gnds.tester.exception;

/* loaded from: UnAuthException.class */
public class UnAuthException extends RuntimeException {
    private static final long serialVersionUID = 1;
    private Integer code;
    private String message;

    public UnAuthException(String message) {
        this.message = message;
    }

    public UnAuthException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public UnAuthException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    @Override // java.lang.Throwable
    public String getMessage() {
        return this.message;
    }

    public Integer getCode() {
        return this.code;
    }
}
