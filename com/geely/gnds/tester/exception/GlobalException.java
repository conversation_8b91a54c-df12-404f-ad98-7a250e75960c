package com.geely.gnds.tester.exception;

import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import org.apache.commons.lang.StringUtils;

/* loaded from: GlobalException.class */
public class GlobalException extends RuntimeException {
    private String message;
    private int code;

    public GlobalException(Exception e, TesterErrorCodeEnum codeEnum) {
        super(TesterErrorCodeEnum.formatMsg(codeEnum), e);
    }

    public GlobalException(Exception e, int code, TesterErrorCodeEnum codeEnum) {
        super(TesterErrorCodeEnum.formatMsg(codeEnum), e);
        setCode(code);
    }

    public GlobalException(String message, int code, Throwable e) {
        super(message, e);
        setMessage(message);
        setCode(code);
    }

    public GlobalException(String message, Throwable e) {
        super(message, e);
        setMessage(message);
    }

    public GlobalException(Throwable e) {
        super(e);
    }

    public GlobalException(Throwable e, int code) {
        super(e);
        setCode(code);
    }

    public static GlobalException a(Exception e, String message) {
        if (e instanceof CloudInterfaceException) {
            throw new GlobalException(e);
        }
        throw new GlobalException(message, e);
    }

    public static GlobalException a(Exception e, TesterErrorCodeEnum codeEnum) {
        if (e instanceof CloudInterfaceException) {
            throw new GlobalException(e);
        }
        throw new GlobalException(e, codeEnum);
    }

    public static GlobalException a(Exception e, int code, TesterErrorCodeEnum codeEnum) {
        if (e instanceof CloudInterfaceException) {
            throw new GlobalException(e, code);
        }
        throw new GlobalException(e, code, codeEnum);
    }

    public static GlobalException a(Exception e, int code, String message) {
        if (e instanceof CloudInterfaceException) {
            throw new GlobalException(e);
        }
        throw new GlobalException(message, code, e);
    }

    public static GlobalException c(Exception e) {
        throw new GlobalException(e);
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return this.code;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override // java.lang.Throwable
    public String getMessage() {
        if (StringUtils.isBlank(this.message)) {
            return super.getMessage();
        }
        return this.message;
    }
}
