package com.geely.gnds.tester.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.tester.controller.SearchController;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

@Configuration
/* loaded from: OpenApiSignUtils.class */
public class OpenApiSignUtils {
    private static final Logger log = LoggerFactory.getLogger(SearchController.class);

    private static void parsejson2Map(Map jsonMap, String jsonStr, String parentKey) {
        JSONObject json = JSON.parseObject(jsonStr);
        json.remove("sign");
        for (Object k : json.keySet()) {
            Object v = json.get(k);
            String fullKey = (null == parentKey || "".equals(parentKey.trim())) ? k.toString() : parentKey + ConstantEnum.POINT + k;
            if (v instanceof JSONArray) {
                List listVo = (List) JSONArray.toJavaObject((JSONArray) v, List.class);
                for (int i = 0; i < listVo.size(); i++) {
                    String fullKey1 = fullKey + '[' + i + ']';
                    if (listVo.get(i) instanceof JSONObject) {
                        parsejson2Map(jsonMap, listVo.get(i).toString(), fullKey1);
                    } else {
                        jsonMap.put(fullKey, listVo.get(i));
                    }
                }
            } else if (isNested(v)) {
                parsejson2Map(jsonMap, v.toString(), fullKey);
            } else {
                jsonMap.put(fullKey, v);
            }
        }
    }

    private static boolean isNested(Object jsonObj) {
        return jsonObj.toString().contains("{");
    }

    public static String getSignOfOpenApi(Object message, String appSecret) {
        log.debug("原始消息：" + JSONObject.toJSONString(message));
        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(message);
        jsonObject.remove("sign");
        Object bizContent = jsonObject.getString("bizContent");
        jsonObject.remove("bizContent");
        jsonObject.remove("check");
        jsonObject.remove(ConstantEnum.TENANTCODE_STR);
        StringBuilder buff = new StringBuilder();
        Map<String, Object> map = new HashMap<>(16);
        parsejson2Map(map, jsonObject.toJSONString(), null);
        map.put("bizContent", bizContent);
        map.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEachOrdered(item -> {
            String key = (String) item.getKey();
            String val = String.valueOf(item.getValue());
            if (StringUtils.hasLength(key)) {
                buff.append(key).append("=").append(val).append("&");
            }
        });
        buff.append("key=").append(appSecret);
        return getSha256(buff.toString()).toUpperCase();
    }

    public static String getSha256(String str) {
        String encodeStr = "";
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00061), e);
        }
        return encodeStr;
    }

    private static String byte2Hex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte aByte : bytes) {
            String temp = Integer.toHexString(aByte & 255);
            if (temp.length() == 1) {
                sb.append("0");
            }
            sb.append(temp);
        }
        return sb.toString();
    }
}
