package com.geely.gnds.tester.util;

import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import oshi.hardware.NetworkIF;

/* loaded from: IpUtils.class */
public class IpUtils {
    private static final int BITS_PER_BYTE = 8;
    private static final int IP_VERSION_4 = 4;
    private static final Logger log = LoggerFactory.getLogger(IpUtils.class);
    private static Map<String, Integer> connectType = new ConcurrentHashMap();

    public static void addConnect(String ip, Integer type) {
        connectType.put(ip, type);
    }

    public static Integer getConnectType(String ip) {
        if (connectType.containsKey(ip)) {
            return connectType.get(ip);
        }
        int type = checkStatus(ip);
        addConnect(ip, Integer.valueOf(type));
        return Integer.valueOf(type);
    }

    public static void clean() {
        connectType.clear();
    }

    public static int checkStatus(String ip2) {
        log.info("checkStatus入参：{}", ip2);
        try {
            List<NetworkIF> inetAddressList = DoipAddressManager.getInstance().getInetAddress();
            for (NetworkIF networkIf : inetAddressList) {
                try {
                    String mask = "";
                    String ip1 = "";
                    String[] iPv4addr = networkIf.getIPv4addr();
                    if (iPv4addr.length > 0) {
                        ip1 = iPv4addr[0];
                    }
                    Short[] subnetMasks = networkIf.getSubnetMasks();
                    if (subnetMasks.length > 0) {
                        mask = binaryToDecimal(subnetMasks[0].shortValue());
                    }
                    boolean check = check(ip2, mask, ip1);
                    if (check) {
                        int ifType = networkIf.getIfType();
                        if (ifType == 6) {
                            return 1;
                        }
                        if (ifType == 71) {
                            return 2;
                        }
                    }
                } catch (Exception e) {
                }
            }
            return 0;
        } catch (Exception e2) {
            log.error("检查车辆连接类型异常", e2);
            return 0;
        }
    }

    private static boolean check(String ip2, String mask, String ip1) {
        if (isVaildMask(mask) && isVaildip(ip1) && isVaildip(ip2) && isSameSonIp(mask, ip1, ip2)) {
            return true;
        }
        return false;
    }

    public static boolean isSameSonIp(String mask, String ip1, String ip2) {
        String[] maski = mask.split("\\.");
        String[] ip1i = ip1.split("\\.");
        String[] ip2i = ip2.split("\\.");
        for (int i = 0; i < 2; i++) {
            if (Integer.parseInt(ip1i[i]) != Integer.parseInt(ip2i[i])) {
                return false;
            }
        }
        for (int i2 = 0; i2 < maski.length; i2++) {
            if ((Integer.parseInt(maski[i2]) & Integer.parseInt(ip1i[i2])) != (Integer.parseInt(maski[i2]) & Integer.parseInt(ip2i[i2]))) {
                return false;
            }
        }
        return true;
    }

    public static boolean isVaildip(String ip) {
        String[] ipi = ip.split("\\.");
        if (ipi.length != 4) {
            return false;
        }
        for (String ss : ipi) {
            if (Integer.parseInt(ss) < 0 || Integer.parseInt(ss) > 255) {
                return false;
            }
        }
        return true;
    }

    public static boolean isVaildMask(String mask) {
        if (!isVaildip(mask)) {
            return false;
        }
        String[] maski = mask.split("\\.");
        StringBuilder sb = new StringBuilder();
        for (String ss : maski) {
            String binary = Integer.toBinaryString(Integer.parseInt(ss));
            sb.append(String.format("%08d", Integer.valueOf(Integer.parseInt(binary))));
        }
        return sb.toString().lastIndexOf("1") < sb.toString().indexOf("0");
    }

    public static void init() throws SocketException {
        try {
            String netmask = binaryToDecimal(22);
            System.out.println("Netmask: " + netmask);
            InetAddress localHost = Inet4Address.getLocalHost();
            NetworkInterface networkInterface = NetworkInterface.getByInetAddress(localHost);
            for (InterfaceAddress address : networkInterface.getInterfaceAddresses()) {
                System.out.println((int) address.getNetworkPrefixLength());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String binaryToDecimal(int prefixLength) throws NumberFormatException {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 32; i++) {
            if (i < prefixLength) {
                sb.append("1");
            } else {
                sb.append("0");
            }
        }
        StringBuilder decimalMask = new StringBuilder();
        for (int i2 = 0; i2 < 32; i2 += 8) {
            int decimal = Integer.parseInt(sb.toString().substring(i2, i2 + 8), 2);
            decimalMask.append(decimal).append(ConstantEnum.POINT);
        }
        return decimalMask.deleteCharAt(decimalMask.length() - 1).toString();
    }
}
