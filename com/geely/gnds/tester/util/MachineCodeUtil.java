package com.geely.gnds.tester.util;

import com.sun.jna.platform.win32.WinReg;
import java.io.IOException;
import java.util.Scanner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: MachineCodeUtil.class */
public class MachineCodeUtil {
    private static final Logger LOG = LoggerFactory.getLogger(MachineCodeUtil.class);

    public static void main(String[] args) throws IOException {
        String windowsBiosUuid = getWindowsBiosUuid();
        System.out.println(windowsBiosUuid);
        getThread();
        System.out.println("smBIOS UUID：" + getWindowsBiosUuid());
        System.out.println("GUID:" + getMachineGuid());
        System.out.println("ProductId:" + getProductId());
    }

    public static String getWindowsBiosUuid() throws IOException {
        try {
            Process process = Runtime.getRuntime().exec(new String[]{"wmic", "path", "win32_computersystemproduct", "get", "uuid"});
            process.getOutputStream().close();
            Scanner sc = new Scanner(process.getInputStream());
            sc.next();
            return sc.next();
        } catch (Exception e) {
            LOG.error("获取windows系统主板uuid bios异常,e={}", e);
            return null;
        }
    }

    public static String getMachineGuid() {
        try {
            return RegistryUtil.readKeyFromRegistry(WinReg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Cryptography", "MachineGuid");
        } catch (Exception e) {
            LOG.error("获取windows系统全局唯一标识符异常,e={}", e);
            return null;
        }
    }

    public static String getProductId() {
        try {
            return RegistryUtil.readKeyFromRegistry(WinReg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", "ProductId");
        } catch (Exception e) {
            LOG.error("获取Windows产品ID信息,e={}", e);
            return null;
        }
    }

    public static void getThread() {
        ThreadGroup currentGroup;
        System.out.println("----------------线程信息----------------");
        ThreadGroup threadGroup = Thread.currentThread().getThreadGroup();
        while (true) {
            currentGroup = threadGroup;
            if (currentGroup.getParent() == null) {
                break;
            } else {
                threadGroup = currentGroup.getParent();
            }
        }
        int noThreads = currentGroup.activeCount();
        Thread[] lstThreads = new Thread[noThreads];
        currentGroup.enumerate(lstThreads);
        for (Thread thread : lstThreads) {
            System.out.println("线程数量：" + noThreads + " 线程id：" + thread.getId() + " 线程名称：" + thread.getName() + " 线程状态：" + thread.getState());
        }
    }
}
