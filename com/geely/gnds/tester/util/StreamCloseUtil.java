package com.geely.gnds.tester.util;

import com.geely.gnds.doip.client.FdHelper;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import org.slf4j.Logger;

/* loaded from: StreamCloseUtil.class */
public class StreamCloseUtil {
    public static void close(Logger logger, OutputStream outBuff, FileOutputStream output) throws IOException {
        if (outBuff != null) {
            try {
                outBuff.close();
            } catch (IOException e) {
                logger.error("IOException:" + e.getMessage());
            }
        }
        if (output != null) {
            try {
                output.close();
            } catch (IOException e2) {
                logger.error("IOException:" + e2.getMessage());
            }
        }
    }

    public static void closeAfterFlush(Logger logger, OutputStream outBuff, FileOutputStream output) throws IOException {
        try {
            if (outBuff != null) {
                try {
                    outBuff.flush();
                } catch (IOException e) {
                    logger.error(FdHelper.getExceptionAsString(e));
                    close(logger, outBuff, output);
                    return;
                }
            }
            close(logger, outBuff, output);
        } catch (Throwable th) {
            close(logger, outBuff, output);
            throw th;
        }
    }
}
