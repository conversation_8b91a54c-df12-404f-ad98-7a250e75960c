package com.geely.gnds.tester.util;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
/* loaded from: VbfParseUtils.class */
public class VbfParseUtils {
    private static final Logger log = LoggerFactory.getLogger(VbfParseUtils.class);
    private static VbfParseUtils instance = new VbfParseUtils();

    private VbfParseUtils() {
    }

    public static VbfParseUtils getInstance() {
        return instance;
    }

    public String getVbfPath(String vbfName) {
        TesterConfigDao testerConfigDao = (TesterConfigDao) SpringUtils.getBean(TesterConfigDao.class);
        TesterConfigDto config = testerConfigDao.getConfig();
        String filePath = config.getVbfPath() + File.separator + vbfName;
        return filePath;
    }

    public String getVbfTempPath(String vbfName, String vin) {
        TesterConfigDao testerConfigDao = (TesterConfigDao) SpringUtils.getBean(TesterConfigDao.class);
        TesterConfigDto config = testerConfigDao.getConfig();
        String filePath = config.getVbfPath() + File.separator + "Temp" + File.separator + vin + "_" + vbfName;
        return filePath;
    }

    public String getVbfTempPath(String vbfName) {
        TesterConfigDao testerConfigDao = (TesterConfigDao) SpringUtils.getBean(TesterConfigDao.class);
        TesterConfigDto config = testerConfigDao.getConfig();
        String filePath = config.getVbfPath() + File.separator + "Temp" + File.separator + vbfName;
        return filePath;
    }

    public String getVbfTempPath() {
        TesterConfigDao testerConfigDao = (TesterConfigDao) SpringUtils.getBean(TesterConfigDao.class);
        TesterConfigDto config = testerConfigDao.getConfig();
        String filePath = config.getVbfPath() + File.separator + "Temp" + File.separator;
        return filePath;
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r15v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r15v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 14, insn: 0x03c1: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r14 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('input' java.io.FileInputStream)]) A[TRY_LEAVE], block:B:77:0x03c1 */
    /* JADX WARN: Not initialized variable reg: 15, insn: 0x03c6: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r15 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:79:0x03c6 */
    /* JADX WARN: Type inference failed for: r14v0, names: [input], types: [java.io.FileInputStream] */
    /* JADX WARN: Type inference failed for: r15v0, types: [java.lang.Throwable] */
    public void parseHeader(int asciiCount, Map result, File file) throws Exception {
        HashMap map = new HashMap(2);
        HashMap map2 = new HashMap(4);
        map.put("Data_Type", "Dict");
        int conutByte = 0;
        try {
            try {
                FileInputStream fileInputStream = new FileInputStream(file);
                Throwable th = null;
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream));
                Throwable th2 = null;
                try {
                    try {
                        String str = "";
                        HashMap map3 = new HashMap(10);
                        boolean z = false;
                        while (true) {
                            String line = bufferedReader.readLine();
                            if (line == null) {
                                break;
                            }
                            conutByte += line.length();
                            if (conutByte > asciiCount) {
                                break;
                            }
                            if (z) {
                                String strReplaceAll = line.replaceAll("\\s*", "").replaceAll("[\\[\\]]", "");
                                if (strReplaceAll.length() > 1) {
                                    if (!"//".equals(strReplaceAll.substring(0, 2))) {
                                        if (strReplaceAll.contains("//")) {
                                            strReplaceAll = strReplaceAll.split("//")[0];
                                        }
                                        if (";".equals(strReplaceAll.substring(strReplaceAll.length() - 1))) {
                                            String[] strArrSplit = (str + strReplaceAll).split("//")[0].split("=");
                                            if (strArrSplit.length > 1) {
                                                String strReplace = strArrSplit[0].replace("header{", "");
                                                String strReplaceAll2 = strArrSplit[1].split(";")[0].replaceAll("\"", "").replaceAll("0x", "");
                                                if ("erase".equals(strReplace)) {
                                                    ArrayList arrayList = new ArrayList();
                                                    String[] strArrSplit2 = strReplaceAll2.split("},");
                                                    for (int i = 0; i < strArrSplit2.length; i++) {
                                                        ArrayList arrayList2 = new ArrayList();
                                                        String strReplaceAll3 = strArrSplit2[i].split(ConstantEnum.COMMA)[0].replaceAll("[{]|[}]", "");
                                                        String strReplaceAll4 = strArrSplit2[i].split(ConstantEnum.COMMA)[1].replaceAll("[{]|[}]", "");
                                                        arrayList2.add(strReplaceAll3);
                                                        arrayList2.add(strReplaceAll4);
                                                        arrayList.add(arrayList2);
                                                    }
                                                    map2.put("erase", arrayList);
                                                }
                                                log.info(strReplace + "---------------------" + strReplaceAll2);
                                                map3.put(strReplace, strReplaceAll2);
                                            }
                                            str = "";
                                        } else {
                                            str = str + strReplaceAll;
                                        }
                                    }
                                }
                            } else if (line.contains("{")) {
                                z = true;
                            }
                        }
                        log.info(map3.toString());
                        map2.put("sw_version", map3.get("sw_version"));
                        map2.put("sw_part_type", map3.get("sw_part_type"));
                        map2.put("sw_part_number", map3.get("sw_part_number"));
                        map2.put("ecu_address", map3.get("ecu_address"));
                        map2.put("data_format_identifier", map3.get("data_format_identifier"));
                        map2.put("sw_signature_dev", map3.get("sw_signature_dev"));
                        map2.put("sw_signature", map3.get("sw_signature"));
                        map2.put("call", map3.get("call"));
                        map2.put("sw_size", Integer.valueOf(fileInputStream.available()));
                        map2.put("file_checksum", map3.get("file_checksum"));
                        map.put("Data_Value", map2);
                        result.put("VBF_Header", map);
                        if (StringUtils.isBlank(((Map) ((Map) result.get("VBF_Header")).get("Data_Value")).get("ecu_address").toString())) {
                            throw new Exception();
                        }
                        if (bufferedReader != null) {
                            if (0 != 0) {
                                try {
                                    bufferedReader.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                bufferedReader.close();
                            }
                        }
                        if (fileInputStream != null) {
                            if (0 != 0) {
                                try {
                                    fileInputStream.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                fileInputStream.close();
                            }
                        }
                    } catch (Throwable th5) {
                        if (bufferedReader != null) {
                            if (th2 != null) {
                                try {
                                    bufferedReader.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                bufferedReader.close();
                            }
                        }
                        throw th5;
                    }
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00071), e);
            throw e;
        }
    }

    public Map parsePlus(String name) throws Exception {
        if (!name.contains(ConstantEnum.VBF)) {
            name = name + ConstantEnum.VBF;
        }
        String filePath = getVbfPath(name);
        try {
            AesUtils.decodeFileSelf(filePath);
            File file = new File(filePath);
            int asciiCount = 0;
            Map result = new HashMap(4);
            try {
                try {
                    FileInputStream reader = new FileInputStream(file);
                    Throwable th = null;
                    int asciiEnd = 0;
                    while (true) {
                        try {
                            try {
                                int a = reader.read();
                                if (a == -1) {
                                    break;
                                }
                                asciiCount++;
                                if (a == 123) {
                                    asciiEnd++;
                                }
                                if (a == 125) {
                                    asciiEnd--;
                                    if (asciiEnd == 0) {
                                        parseHeader(asciiCount, result, file);
                                        result.put("asciiCount", Integer.valueOf(asciiCount));
                                        break;
                                    }
                                }
                            } catch (Throwable th2) {
                                if (reader != null) {
                                    if (th != null) {
                                        try {
                                            reader.close();
                                        } catch (Throwable th3) {
                                            th.addSuppressed(th3);
                                        }
                                    } else {
                                        reader.close();
                                    }
                                }
                                throw th2;
                            }
                        } finally {
                        }
                    }
                    int count = ((Integer) result.get("asciiCount")).intValue();
                    log.info("vbf【{}】asciiCount【{}】", name, Integer.valueOf(count));
                    if (reader != null) {
                        if (0 != 0) {
                            try {
                                reader.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            reader.close();
                        }
                    }
                    return result;
                } catch (Exception e) {
                    log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00071), e);
                    throw e;
                }
            } finally {
                AesUtils.encodeFileSelf(filePath);
            }
        } catch (IOException e2) {
            throw e2;
        }
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r12v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r12v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r13v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r13v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 12, insn: 0x00e2: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r12 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('reader' java.io.FileInputStream)]) A[TRY_LEAVE], block:B:30:0x00e2 */
    /* JADX WARN: Not initialized variable reg: 13, insn: 0x00e7: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r13 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:32:0x00e7 */
    /* JADX WARN: Type inference failed for: r12v1, names: [reader], types: [java.io.FileInputStream] */
    /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.Throwable] */
    public Map parsePlus2(String name, String vin) throws Exception {
        if (!name.contains(ConstantEnum.VBF)) {
            name = name + ConstantEnum.VBF;
        }
        String filePath = getVbfTempPath(name, vin);
        File file = new File(filePath);
        int asciiCount = 0;
        Map result = new HashMap(4);
        try {
            try {
                FileInputStream fileInputStream = new FileInputStream(file);
                Throwable th = null;
                int i = 0;
                while (true) {
                    int i2 = fileInputStream.read();
                    if (i2 == -1) {
                        break;
                    }
                    asciiCount++;
                    if (i2 == 123) {
                        i++;
                    }
                    if (i2 == 125) {
                        i--;
                        if (i == 0) {
                            parseHeader(asciiCount, result, file);
                            result.put("asciiCount", Integer.valueOf(asciiCount));
                            break;
                        }
                    }
                }
                log.info("vbf【{}】asciiCount【{}】", name, Integer.valueOf(((Integer) result.get("asciiCount")).intValue()));
                if (fileInputStream != null) {
                    if (0 != 0) {
                        try {
                            fileInputStream.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        fileInputStream.close();
                    }
                }
                return result;
            } finally {
            }
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00071), e);
            throw e;
        }
    }

    public Map parsePlusDsa(String filePath) throws Exception {
        File file = new File(filePath);
        int asciiCount = 0;
        Map result = new HashMap(4);
        try {
            FileInputStream reader = new FileInputStream(file);
            Throwable th = null;
            int asciiEnd = 0;
            while (true) {
                try {
                    try {
                        int a = reader.read();
                        if (a == -1) {
                            break;
                        }
                        asciiCount++;
                        if (a == 123) {
                            asciiEnd++;
                        }
                        if (a == 125) {
                            asciiEnd--;
                            if (asciiEnd == 0) {
                                parseHeader(asciiCount, result, file);
                                result.put("asciiCount", Integer.valueOf(asciiCount));
                                break;
                            }
                        }
                    } finally {
                    }
                } finally {
                }
            }
            int count = ((Integer) result.get("asciiCount")).intValue();
            log.info("dsa vbf【{}】asciiCount【{}】", filePath, Integer.valueOf(count));
            if (reader != null) {
                if (0 != 0) {
                    try {
                        reader.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    reader.close();
                }
            }
            return result;
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00071), e);
            throw e;
        }
    }

    public String toHexString(byte[] byteArray) {
        if (byteArray == null || byteArray.length < 1) {
            throw new IllegalArgumentException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00116));
        }
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < byteArray.length; i++) {
            if ((byteArray[i] & 255) < 16) {
                hexString.append("0");
            }
            hexString.append(Integer.toHexString(255 & byteArray[i]));
        }
        return hexString.toString().toLowerCase();
    }

    public static String getVbfName(String url) {
        String fileName = url;
        if (url.contains("/")) {
            fileName = url.substring(url.lastIndexOf("/") + 1);
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.lastIndexOf("?"));
            }
        }
        return fileName;
    }

    public static void main(String[] args) {
        String vbfName = getVbfName("https://otasea-eu-cdn-aliyun.geely.com/otasea-prod-aliyun/49edcbd572d147d5d3a2aead009c0dec/1107952206PVV.vbf?auth_key=1731997512-f654fa9a3b5b4fc6905d92cfb33a592f-0-8ac5969ba77dd04a33247da73928cebe");
        System.out.println(vbfName);
    }
}
