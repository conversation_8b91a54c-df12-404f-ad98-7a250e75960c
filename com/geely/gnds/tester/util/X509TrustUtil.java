package com.geely.gnds.tester.util;

import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;
import javax.net.ssl.X509TrustManager;
import org.apache.http.util.TextUtils;

/* loaded from: X509TrustUtil.class */
public class X509TrustUtil implements X509TrustManager {
    private static String[] VERIFY_HOST_NAME_ARRAY = new String[0];

    /* loaded from: X509TrustUtil$TrustAnyHostnameVerifier.class */
    public class TrustAnyHostnameVerifier implements HostnameVerifier {
        public TrustAnyHostnameVerifier() {
        }

        @Override // javax.net.ssl.HostnameVerifier
        public boolean verify(String hostname, SSLSession session) {
            return (TextUtils.isEmpty(hostname) || Arrays.asList(X509TrustUtil.VERIFY_HOST_NAME_ARRAY).contains(hostname)) ? false : true;
        }
    }

    @Override // javax.net.ssl.X509TrustManager
    public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        try {
            chain[0].checkValidity();
        } catch (Exception e) {
            throw new CertificateException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00111));
        }
    }

    @Override // javax.net.ssl.X509TrustManager
    public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        try {
            chain[0].checkValidity();
        } catch (Exception e) {
            throw new CertificateException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00111));
        }
    }

    @Override // javax.net.ssl.X509TrustManager
    public X509Certificate[] getAcceptedIssuers() {
        return null;
    }
}
