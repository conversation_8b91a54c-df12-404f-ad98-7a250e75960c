package com.geely.gnds.tester.util;

import java.io.Serializable;

/* loaded from: UploadBean.class */
public class UploadBean implements Serializable {
    private String localPath;
    private String ossPath;
    private String id;

    public UploadBean(String localPath, String ossPath, String id) {
        this.localPath = localPath;
        this.ossPath = ossPath;
        this.id = id;
    }

    public String getLocalPath() {
        return this.localPath;
    }

    public String getOssPath() {
        return this.ossPath;
    }

    public String getId() {
        return this.id;
    }

    public boolean equals(Object obj) {
        if (obj instanceof UploadBean) {
            return getLocalPath().equalsIgnoreCase(((UploadBean) obj).getLocalPath());
        }
        return false;
    }
}
