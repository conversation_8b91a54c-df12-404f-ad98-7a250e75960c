package com.geely.gnds.tester.util;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
/* loaded from: VirtualVehicleUtils.class */
public class VirtualVehicleUtils {
    public static String getResponse(String messageStr, String posCode) {
        boolean success = messageStr.startsWith(posCode);
        Map<String, Object> data = new HashMap<>(2);
        Map<String, Object> value = new HashMap<>(2);
        Map<String, Object> type = new HashMap<>(2);
        value.put("Data_Type", ConstantEnum.STRING);
        value.put("Data_Value", messageStr);
        type.put("Data_Type", ConstantEnum.STRING);
        type.put("Data_Value", success ? "Positive" : "Negative");
        data.put("ECU_Response_value", value);
        data.put("ECU_Response_Type", type);
        String obj2JsonStr = ObjectMapperUtils.obj2JsonStr(data);
        return obj2JsonStr;
    }
}
