package com.geely.gnds.tester.util;

import cn.hutool.core.collection.CollectionUtil;
import java.lang.ref.SoftReference;
import java.util.List;
import java.util.Objects;

/* loaded from: TesterReadParamUtil.class */
public class TesterReadParamUtil {
    private static volatile SoftReference<List<List<ParameterValue>>> cache;

    public static void readInit() {
        cache = new SoftReference<>(CollectionUtil.newArrayList(new List[0]));
    }

    public static List<List<ParameterValue>> getReadValues() {
        return cache.get();
    }

    public static void addReadValues(List<ParameterValue> values) {
        if (cache == null || cache.get() == null) {
            readInit();
        }
        ((List) Objects.requireNonNull(cache.get())).add(values);
    }

    /* loaded from: TesterReadParamUtil$ParameterValue.class */
    public static class ParameterValue {
        private final String didId;
        private final String name;
        private final String value;
        private final String unit;
        private final String originalResponse;

        public ParameterValue(String didId, String name, String value, String unit, String originalResponse) {
            this.didId = didId;
            this.name = name;
            this.value = value;
            this.unit = unit;
            this.originalResponse = originalResponse;
        }

        public String getDidId() {
            return this.didId;
        }

        public String getName() {
            return this.name;
        }

        public String getValue() {
            return this.value;
        }

        public String getUnit() {
            return this.unit;
        }

        public String getOriginalResponse() {
            return this.originalResponse;
        }
    }
}
