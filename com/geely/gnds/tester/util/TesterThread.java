package com.geely.gnds.tester.util;

import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
/* loaded from: TesterThread.class */
public class TesterThread {

    @Autowired
    private ThreadPoolTaskExecutor executor;

    public ThreadPoolExecutor getPool() {
        return this.executor.getThreadPoolExecutor();
    }

    public void shutdown() {
        this.executor.shutdown();
    }
}
