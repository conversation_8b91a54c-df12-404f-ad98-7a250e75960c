package com.geely.gnds.tester.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import javax.xml.bind.DatatypeConverter;

/* loaded from: FileHash.class */
public enum FileHash {
    MD5("MD5"),
    SHA1("SHA1"),
    SHA256("SHA-256"),
    SHA512("SHA-512");

    private String name;

    FileHash(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public byte[] checksum(File input) throws IOException {
        try {
            InputStream in = new FileInputStream(input);
            Throwable th = null;
            try {
                try {
                    MessageDigest digest = MessageDigest.getInstance(getName());
                    byte[] block = new byte[4096];
                    while (true) {
                        int length = in.read(block);
                        if (length <= 0) {
                            break;
                        }
                        digest.update(block, 0, length);
                    }
                    byte[] bArrDigest = digest.digest();
                    if (in != null) {
                        if (0 != 0) {
                            try {
                                in.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            in.close();
                        }
                    }
                    return bArrDigest;
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String toHex(byte[] bytes) {
        return DatatypeConverter.printHexBinary(bytes);
    }
}
