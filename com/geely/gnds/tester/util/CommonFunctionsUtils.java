package com.geely.gnds.tester.util;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/* loaded from: CommonFunctionsUtils.class */
public class CommonFunctionsUtils {
    private static String[] binaryArray = {"0000", "0001", "0010", "0011", "0100", "0101", "0110", "0111", "1000", "1001", "1010", "1011", "1100", "1101", "1110", "1111"};

    public static int stringToUnsignedInt(String hex, int system) throws NumberFormatException {
        int result;
        if (hex.contains("0x") || hex.contains("0X")) {
            result = Integer.parseInt(hex.substring(2), 16);
        } else {
            result = Integer.parseInt(hex, system);
        }
        return result;
    }

    public static int bcdToUnsignedInt(String bcd) {
        byte[] b2 = str2Bcd(bcd);
        return bcd2Str(b2);
    }

    public static String binaryToUnsignedInt(String binarySting) throws NumberFormatException {
        int data = Integer.parseInt(binarySting, 2);
        return Integer.toHexString(data);
    }

    public static int binaryStingToHex(String binarySting) {
        return Integer.parseInt(binarySting, 2);
    }

    public static String hexToAscii(String hexStr) {
        StringBuilder output = new StringBuilder("");
        for (int i = 0; i < hexStr.length(); i += 2) {
            String str = hexStr.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }
        return output.toString();
    }

    public static float stringToFloat(String str) {
        return Float.parseFloat(str);
    }

    public static int stringToInt(String str) {
        return Integer.parseInt(str);
    }

    public static long stringToLong(String str) {
        return Long.parseLong(str);
    }

    public static int stringToUnsignedInt(String str) {
        return Integer.parseInt(str);
    }

    public static String reserveFloatDecimal(String format, float value) {
        DecimalFormat fnum = new DecimalFormat(format);
        String formatString = fnum.format(value);
        return formatString;
    }

    public static float hexStringToFloat(String str) {
        Long l = Long.valueOf(Long.parseLong(str, 16));
        return Float.intBitsToFloat(l.intValue());
    }

    public static String floatToHexString(float str) {
        int i = Float.floatToIntBits(str);
        return Integer.toHexString(i);
    }

    public static String asciiToHexString(String asciiStr) {
        char[] chars = asciiStr.toCharArray();
        StringBuilder hex = new StringBuilder();
        for (char ch : chars) {
            hex.append(Integer.toHexString(ch));
        }
        return hex.toString();
    }

    public static String intToHexString(int value, int length) {
        String hexString = Integer.toHexString(value);
        String stringCompletion = stringCompletion(hexString, length * 2);
        return stringCompletion;
    }

    public static String stringCompletion(String str, int length) {
        while (str.length() < length) {
            str = "0" + str;
        }
        return str;
    }

    public static int trunc(double value) {
        int trunc = (int) Math.floor(value);
        return trunc;
    }

    public static int hexStringToInt(String hex, int system) throws NumberFormatException {
        int value;
        if (hex.contains("0x") || hex.contains("0X")) {
            value = Integer.parseInt(hex.substring(2), 16);
        } else {
            value = Integer.parseInt(hex, system);
        }
        String hexString = Integer.toHexString(value);
        int symbol = 1;
        String binStr = bytes2BinStr(hexToByteArray(hexString));
        if (binStr != null && binStr.length() > 1) {
            if (binStr.startsWith("1")) {
                symbol = -1;
            }
            return Integer.parseInt(binStr.substring(1), 2) * symbol;
        }
        return 0;
    }

    public static int binaryStringToInt(String binaryString) {
        int symbol = 1;
        if (binaryString.startsWith("1")) {
            symbol = -1;
        }
        return Integer.parseInt(binaryString.substring(1), 2) * symbol;
    }

    public static boolean judgeByBit(String hex, int byteLocation, int bitLocation) {
        String byteString = hex.substring(byteLocation * 2, (byteLocation * 2) + 2);
        byte[] statusBits = ParseUtils.byteToBitOfArray((byte) Integer.parseInt(byteString, 16));
        if (statusBits[bitLocation] == 1) {
            return true;
        }
        return false;
    }

    public static String bytes2BinStr(byte[] bArray) {
        String outStr = "";
        for (byte b : bArray) {
            int pos = (b & 240) >> 4;
            String outStr2 = outStr + binaryArray[pos];
            int pos2 = b & 15;
            outStr = outStr2 + binaryArray[pos2];
        }
        return outStr;
    }

    public static byte[] hexToByteArray(String inHex) {
        byte[] result;
        int hexlen = inHex.length();
        if (hexlen % 2 == 1) {
            hexlen++;
            result = new byte[hexlen / 2];
            inHex = "0" + inHex;
        } else {
            result = new byte[hexlen / 2];
        }
        int j = 0;
        for (int i = 0; i < hexlen; i += 2) {
            result[j] = hexToByte(inHex.substring(i, i + 2));
            j++;
        }
        return result;
    }

    public static byte hexToByte(String inHex) {
        return (byte) Integer.parseInt(inHex, 16);
    }

    public static String hexString2binaryString(String hexString) {
        StringBuilder res = new StringBuilder();
        for (String s : hexString.split("")) {
            res.append(StringUtils.leftPad(new BigInteger(s, 16).toString(2), 4, '0'));
        }
        return res.toString();
    }

    public static int bcd2Str(byte[] bytes) {
        StringBuffer temp = new StringBuffer(bytes.length * 2);
        for (int i = 0; i < bytes.length; i++) {
            temp.append((int) ((byte) ((bytes[i] & 240) >>> 4)));
            temp.append((int) ((byte) (bytes[i] & 15)));
        }
        String intStr = "0".equalsIgnoreCase(temp.toString().substring(0, 1)) ? temp.toString().substring(1) : temp.toString();
        return Integer.parseInt(intStr);
    }

    public static byte[] str2Bcd(String asc) {
        int j;
        int i;
        int len = asc.length();
        int mod = len % 2;
        if (mod != 0) {
            asc = "0" + asc;
            len = asc.length();
        }
        byte[] bArr = new byte[len];
        if (len >= 2) {
            len /= 2;
        }
        byte[] bbt = new byte[len];
        byte[] abt = asc.getBytes();
        for (int p = 0; p < asc.length() / 2; p++) {
            if (abt[2 * p] >= 48 && abt[2 * p] <= 57) {
                j = abt[2 * p] - 48;
            } else if (abt[2 * p] >= 97 && abt[2 * p] <= 122) {
                j = (abt[2 * p] - 97) + 10;
            } else {
                j = (abt[2 * p] - 65) + 10;
            }
            if (abt[(2 * p) + 1] >= 48 && abt[(2 * p) + 1] <= 57) {
                i = abt[(2 * p) + 1] - 48;
            } else if (abt[(2 * p) + 1] >= 97 && abt[(2 * p) + 1] <= 122) {
                i = (abt[(2 * p) + 1] - 97) + 10;
            } else {
                i = (abt[(2 * p) + 1] - 65) + 10;
            }
            int k = i;
            int a = (j << 4) + k;
            byte b = (byte) a;
            bbt[p] = b;
        }
        return bbt;
    }

    public static String stringTransformAscii(String value) {
        StringBuffer sbu = new StringBuffer();
        char[] chars = value.toCharArray();
        for (char c : chars) {
            sbu.append(Integer.toHexString(c));
        }
        return sbu.toString();
    }

    public static void main(String[] args) throws NumberFormatException {
        String s = hexStringToAscii("333132453330324533313631323032303230323032303230323032303230323032303230323032303230323032303230323032303230323032303230");
        String s1 = hexStringToAscii(s);
        System.out.println(s1);
    }

    public static String hexStringToAscii(String hexStr) throws NumberFormatException {
        StringBuilder asciiBuilder = new StringBuilder();
        for (int i = 0; i < hexStr.length(); i += 2) {
            int decimalValue = Integer.parseInt(hexStr.substring(i, i + 2), 16);
            char character = (char) decimalValue;
            asciiBuilder.append(character);
        }
        return asciiBuilder.toString();
    }

    public static String asciiTransHexString(String value) {
        StringBuffer sbu = new StringBuffer();
        String[] chars = value.split(ConstantEnum.COMMA);
        for (String str : chars) {
            sbu.append((char) Integer.parseInt(str));
        }
        return sbu.toString();
    }

    public static String analysisParam(String hex) {
        try {
            if (hex.length() >= 16) {
                String hex2 = hex.substring(0, 16);
                String bcd = hex2.substring(0, 10);
                String asc1 = String.valueOf((char) Integer.parseInt(hex2.substring(10, 12), 16));
                String asc2 = String.valueOf((char) Integer.parseInt(hex2.substring(12, 14), 16));
                String asc3 = String.valueOf((char) Integer.parseInt(hex2.substring(14, 16), 16));
                StringBuilder sb = new StringBuilder(bcd);
                return sb.append(asc1).append(asc2).append(asc3).toString();
            }
            return hex;
        } catch (Exception e) {
            return "";
        }
    }

    public static List<String> splitString(String input, int length) {
        List<String> result = new ArrayList<>();
        int k = 0;
        for (int i = 0; i < input.length() / length; i++) {
            String str = input.substring(k, k + length);
            result.add(str);
            k += length;
        }
        return result;
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", Byte.valueOf(b)));
        }
        return sb.toString();
    }
}
