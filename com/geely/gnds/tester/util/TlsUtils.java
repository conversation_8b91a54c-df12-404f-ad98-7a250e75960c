package com.geely.gnds.tester.util;

import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import java.io.ByteArrayInputStream;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.DLSequence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: TlsUtils.class */
public class TlsUtils {
    private static final Logger LOG = LoggerFactory.getLogger(TlsUtils.class);

    public static void main(String[] args) throws Exception {
    }

    public static void ecdsaWithSha25622(String challengeServer) throws Exception {
        System.out.println("9F2550A9F4E885390402C4747B17C3462BF09AFCA9F8A11C1A9D09D063A73CF90000".length());
        getCertificateData();
        String signature = ecdsaWithSha256("85 02 17 CE D7 28 06 3C 65 F2 29 DB 98 67 7E 47 7C 1D 12 E0 85 45 5B D4 5B 59 56 37 2C 14 7D C9".replace(ConstantEnum.EMPTY, ""));
        System.out.println(signature);
    }

    public static String ecdsaWithSha256(String challengeServer) throws Exception {
        LOG.info("ecdsaWithSha256入参(Hex)：" + challengeServer);
        LOG.info("ecdsaWithSha256使用文件：private.key");
        String string = new String(SingletonManager.getPrivateKey());
        byte[] decodedBytes = Base64.getDecoder().decode(string.replace("-----BEGIN PRIVATE KEY-----", "").replace("-----END PRIVATE KEY-----", "").replaceAll("\\s+", ""));
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(decodedBytes);
        KeyFactory kf = KeyFactory.getInstance("EC");
        PrivateKey privateKey = kf.generatePrivate(spec);
        byte[] data = hexStringToByteArray(challengeServer);
        Signature ecdsaSign = Signature.getInstance("SHA256withECDSA");
        ecdsaSign.initSign(privateKey);
        ecdsaSign.update(data);
        byte[] signature = ecdsaSign.sign();
        String hex = bytesToHex(signature);
        LOG.info("转换前 ：" + hex);
        LOG.info("转换前字节长度 ：" + (hex.length() / 2));
        ASN1InputStream asn1InputStream = new ASN1InputStream(signature);
        DLSequence seq = asn1InputStream.readObject();
        BigInteger r = seq.getObjectAt(0).getValue();
        BigInteger s = seq.getObjectAt(1).getValue();
        asn1InputStream.close();
        String rHex = String.format("%064x", r);
        LOG.info("rHex " + rHex.toUpperCase());
        String sHex = String.format("%064x", s);
        LOG.info("sHex " + sHex.toUpperCase());
        String signatureOutput = (rHex + sHex).toUpperCase();
        LOG.info("转换后 " + signatureOutput.toUpperCase());
        LOG.info("转换后字节长度 ：" + (signatureOutput.length() / 2));
        String signatureHex = intToPaddedHex(signatureOutput.length() / 2, 2) + signatureOutput;
        LOG.info("ecdsaWithSha256出参(Hex)：" + signatureHex);
        return signatureHex;
    }

    public static String getCertificateData() throws Exception {
        CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
        byte[] cert = SingletonManager.getCert();
        X509Certificate x509Certificate = (X509Certificate) certificateFactory.generateCertificate(new ByteArrayInputStream(cert));
        byte[] certBytes = x509Certificate.getEncoded();
        String certHex = bytesToHex(certBytes);
        String s = intToPaddedHex(certHex.length() / 2, 2) + certHex;
        return s;
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02X", Byte.valueOf(b)));
        }
        return result.toString();
    }

    public static String intToPaddedHex(int value, int byteLength) {
        String hex = Integer.toHexString(value);
        int paddingLength = (byteLength * 2) - hex.length();
        if (paddingLength > 0) {
            StringBuilder padding = new StringBuilder();
            for (int i = 0; i < paddingLength; i++) {
                padding.append("0");
            }
            hex = padding.toString() + hex;
        }
        return hex.toUpperCase();
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }
}
