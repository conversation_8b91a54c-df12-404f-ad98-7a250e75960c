package com.geely.gnds.tester.util;

import cn.hutool.core.util.StrUtil;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;

/* loaded from: StringParseUtils.class */
public class StringParseUtils {
    public static String fillBlank(String num, int length) {
        if (StringUtils.isNotBlank(num)) {
            String s1 = num.substring(0, 10);
            String s2 = String.format("%" + length + "s", num.substring(10));
            num = s1 + s2;
        }
        return num;
    }

    public static List<String> getStrList(String inputString, int length) {
        int size = inputString.length() / length;
        if (inputString.length() % length != 0) {
            size++;
        }
        return getStrList(inputString, length, size);
    }

    public static List<String> getStrList(String inputString, int length, int size) {
        List<String> list = new ArrayList<>();
        for (int index = 0; index < size; index++) {
            String childStr = substring(inputString, index * length, (index + 1) * length);
            list.add(childStr);
        }
        return list;
    }

    public static Set<String> getStrListLimitLength(String inputString, int length) {
        int size = inputString.length() / length;
        if (inputString.length() % length != 0) {
            size++;
        }
        return getStrListLimitLength(inputString, length, size);
    }

    public static Set<String> getStrListLimitLength(String inputString, int length, int size) {
        Set<String> list = new LinkedHashSet<>();
        for (int index = 0; index < size; index++) {
            String childStr = substring(inputString, index * length, (index + 1) * length);
            list.add(childStr);
        }
        return list;
    }

    public static String substring(String str, int f, int t) {
        if (f > str.length()) {
            return null;
        }
        if (t > str.length()) {
            return str.substring(f, str.length());
        }
        return str.substring(f, t);
    }

    public static String hexString2binaryString(String hexString) {
        StringBuilder res = new StringBuilder();
        for (String s : hexString.split("")) {
            res.append(StringUtils.leftPad(new BigInteger(s, 16).toString(2), 4, '0'));
        }
        return res.toString();
    }

    public static String dtcLastByte(String s) {
        switch (s) {
            case "0":
                return "0";
            case "1":
            case "0A":
                return "General Electrical Failures";
            case "2":
                return "General Signal Failures";
            case "3":
            case "03":
                return "FM（Frequency Modulated） / PWM（Pluse WidthModulated）Failures";
            case "4":
            case "04":
                return "System Internal Failures";
            case "5":
            case "05":
                return "System Programming Failures";
            case "6":
            case "06":
                return "Algorithm Based Failures";
            case "7":
            case "07":
                return "Mechanical Failures";
            case "8":
            case "08":
                return "Bus Signal / Message Failures";
            case "9":
            case "09":
                return "Component Failures";
            case "01":
                return "General electrical failure";
            case "02":
                return "General signal failure";
            case "11":
                return "Circuit short to ground";
            case "12":
                return "Circuit short to battery";
            case "13":
                return "Circuit open";
            case "14":
                return "Circuit short to ground or open";
            case "15":
                return "Circuit short to battery or open";
            case "16":
                return "Circuit voltage below threshold";
            case "17":
                return "Circuit voltage above threshold";
            case "18":
                return "Circuit current below threshold";
            case "19":
                return "Circuit current above threshold";
            case "1A":
                return "Circuit resistance below threshold";
            case "1B":
                return "Circuit resistance above threshold";
            case "1C":
                return "Circuit voltage out of range";
            case "1D":
                return "Circuit current out of range";
            case "1E":
                return "Circuit resistance out of range";
            case "1F":
                return "Circuit intermittent";
            case "21":
                return "Signal amplitude < minimum";
            case "22":
                return "Signal amplitude > minimum";
            case "23":
                return "Signal stuck low";
            case "24":
                return "Signal stuck high";
            case "25":
                return "Signal shape / waveform failure";
            case "26":
                return "Signal rate of change below threshold";
            case "27":
                return "Signal rate of change above threshold";
            case "28":
                return "Signal bias level out of range / zero adjustment failure";
            case "29":
                return "Signal signal invalid";
            case "2A":
                return "Signal Stuck In Range";
            case "2B":
                return "Signal Cross Coupled";
            case "2F":
                return "Signal erratic";
            case "31":
                return "No signal";
            case "32":
                return "Signal low time < minimum";
            case "33":
                return "Signal low time > minimum";
            case "34":
                return "Signal high time < minimum";
            case "35":
                return "Signal high time > minimum";
            case "36":
                return "Signal frequency too low";
            case "37":
                return "Signal frequency too high";
            case "38":
                return "Signal frequency incorrect";
            case "39":
                return "Incorrect has too few pluses";
            case "3A":
                return "Incorrect has too many pluses";
            case "41":
                return "General checksum failure";
            case "42":
                return "General memory failure";
            case "43":
                return "Special memory failure";
            case "44":
                return "Data memory failure";
            case "45":
                return "Program memory failure";
            case "46":
                return "Calibration / parameter memory failure";
            case "47":
                return "Watchdog / safety checksum failure";
            case "48":
                return "Supervision software failure";
            case "49":
                return "Internal electronic failure";
            case "4A":
                return "Incorrect component installed";
            case "4B":
                return "Over temperature";
            case "51":
                return "Not programmed";
            case "52":
                return "Not activated";
            case "53":
                return "Deactivated";
            case "54":
                return "Missing calibration";
            case "55":
                return "Not configured";
            case "56":
                return "Invalid / incompatible configuration";
            case "57":
                return "Invalid / incompatible Software Component";
            case "61":
                return "Signal calibration failure";
            case "62":
                return "Signal compare failure";
            case "63":
                return "Circuit / component protection time-out";
            case "64":
                return "Signal plausibility failure";
            case "65":
                return "Signal has two few transitions / events";
            case "66":
                return "Signal has two many transitions / events";
            case "67":
                return "Signal incorrect after event";
            case "68":
                return "Event information";
            case "71":
                return "Actuator stuck";
            case "72":
                return "Actuator stuck open";
            case "73":
                return "Actuator stuck closed";
            case "74":
                return "Actuator slipping";
            case "75":
                return "Emergency position not reachable";
            case "76":
                return "Wrong mounting position";
            case "77":
                return "Commanded position not reachable";
            case "78":
                return "Alignment or adjustment incorrect";
            case "79":
                return "Mechanical linkage failure";
            case "7A":
                return "Fluid leak or seal failure";
            case "7B":
                return "Low fluid level";
            case "7C":
                return "Slow Response";
            case "7E":
                return "Actuator stuck On";
            case "7F":
                return "Actuator stuck Off";
            case "81":
                return "Invalid serial data received";
            case "82":
                return "Alive / sequence counter incorrect / not updated";
            case "83":
                return "Value of signal protection calculation incorrect";
            case "84":
                return "Signal below allowable range";
            case "85":
                return "Signal above allowable range";
            case "86":
                return "Signal invalid";
            case "87":
                return "Missing message";
            case "88":
                return "Bus off";
            case "8F":
                return "Erratic";
            case "91":
                return "Parametric";
            case "92":
                return "Performance or incorrect operation";
            case "93":
                return "No operation";
            case "94":
                return "Unexpected opertaion";
            case "95":
                return "Incorrect assembly";
            case "96":
                return "Component internal failure";
            case "97":
                return "Component or system operation obstructed or blocked";
            default:
                return "";
        }
    }

    public static String getStatusName(int index) {
        switch (index) {
            case 0:
                return "Warning indicator requested";
            case 1:
                return "Test not completed during this monitoring or operational cycle";
            case 2:
                return "Test failed since last clear";
            case 3:
                return "Test not completed since last clear";
            case 4:
                return "Confirmed DTC";
            case 5:
                return "Pending DTC";
            case 6:
                return "Test failed during this monitoring or operational cycle";
            case 7:
                return "Test failed";
            default:
                return "";
        }
    }

    public static String convert(String outDataType) {
        if (StrUtil.isBlank(outDataType)) {
            return "";
        }
        switch (outDataType) {
            case "01":
                return "dec";
            case "02":
                return "hex";
            case "03":
                return "bin";
            case "04":
                return "oct";
            case "05":
                return "bcd";
            case "06":
                return "ascii";
            case "07":
                return "4 bcd 3 ascii";
            case "08":
                return "5 bcd 3 ascii";
            default:
                return outDataType;
        }
    }

    public static String occDataConvert(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        Map<String, String> occMap = new HashMap<>(7);
        for (int i = 1; i <= 7; i++) {
            occMap.put("0" + i, "Operation cycle counter #" + i);
        }
        return occMap.get(name);
    }

    public static String fdcDataConvert(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        switch (name) {
            case "10":
                return "DTC fault detection counter";
            case "11":
                return "Maximum DTC fault detection counter this monitoring cycle";
            case "12":
                return "Maximum DTC fault detection counter since last clear";
            default:
                return name;
        }
    }

    public static String timestampDataConvert(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        switch (name) {
            case "20":
                return "DTC Time Stamp 20";
            case "21":
                return "DTC Time Stamp 21";
            default:
                return name;
        }
    }

    public static String convertDtcValue(String value) {
        if (StringUtils.isBlank(value)) {
            return "";
        }
        switch (value) {
            case "0":
                return "P0";
            case "1":
                return "P1";
            case "2":
                return "P2";
            case "3":
                return "P3";
            case "4":
                return "C0";
            case "5":
                return "C1";
            case "6":
                return "C2";
            case "7":
                return "C3";
            case "8":
                return "B0";
            case "9":
                return "B1";
            case "A":
                return "B2";
            case "B":
                return "B3";
            case "C":
                return "U0";
            case "D":
                return "U1";
            case "E":
                return "U2";
            case "F":
                return "U3";
            default:
                return value;
        }
    }

    public static void main(String[] args) {
        String newApplic = "(AA14&AB06)|AC01".replaceAll("[!()|&]", "");
        System.out.println("newApplic=" + newApplic);
        getStrList(newApplic, 4);
        for (String a : getStrList(newApplic, 4)) {
            System.out.println(a);
        }
    }

    public static String getYesOrNo(Boolean b) {
        if (Objects.isNull(b)) {
            return "";
        }
        return b.booleanValue() ? "Yes" : "No";
    }

    public static List<List> splitList(List list, int groupSize) {
        int length = list.size();
        int num = ((length + groupSize) - 1) / groupSize;
        List<List> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            int fromIndex = i * groupSize;
            int toIndex = (i + 1) * groupSize < length ? (i + 1) * groupSize : length;
            newList.add(list.subList(fromIndex, toIndex));
        }
        return newList;
    }
}
