package com.geely.gnds.tester.util;

import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.enums.TesterLoginStatus;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.collections4.map.HashedMap;

/* loaded from: TesterLoginUtils.class */
public class TesterLoginUtils {
    private static volatile String loginStatus = "";
    private static volatile String loginUserName = "";
    private static volatile Set<String> loginUsers = new HashSet();
    private static volatile Set<String> tokenSets = Sets.newConcurrentHashSet();
    private static volatile String startStatus = "";
    private static Map<String, LoginUser> vinConnectUser = new HashedMap();
    private static List<String> stateFinal = new ArrayList();

    private TesterLoginUtils() {
    }

    public static void offLine() {
        loginStatus = TesterLoginStatus.OFF_LINE.getValue();
    }

    public static void onLine() {
        loginStatus = TesterLoginStatus.ON_LINE.getValue();
    }

    public static String getLoginUserName() {
        return loginUserName;
    }

    public static void setLoginUserName(String loginUserName2) {
        loginUserName = loginUserName2;
    }

    public static boolean isOnLine() {
        return TesterLoginStatus.ON_LINE.getValue().equals(loginStatus);
    }

    public static void setLoginUserByVin(String vin, LoginUser user) {
        vinConnectUser.put(vin, user);
    }

    public static void clearLoginUserByVin(String vin) {
        vinConnectUser.remove(vin);
    }

    public static LoginUser getLoginUserByVin(TokenService tokenService, String vin) {
        LoginUser loginUser = null;
        if (tokenService != null) {
            loginUser = tokenService.c(ServletUtils.getRequest());
        }
        if (loginUser == null) {
            loginUser = vinConnectUser.get(vin);
        }
        return loginUser;
    }

    public static Set<String> getLoginUsers() {
        return loginUsers;
    }

    public static void addLoginUsers(String loginUser) {
        loginUsers.add(loginUser);
    }

    public static void removeLoginUsers(String loginUser) {
        if (loginUsers.contains(loginUser)) {
            loginUsers.remove(loginUser);
        }
    }

    public static String getStartStatus() {
        return startStatus;
    }

    public static void start() {
        startStatus = "start";
    }

    public static Set<String> getTokenSets() {
        return tokenSets;
    }

    public static void addToken(String token) {
        tokenSets.add(token);
    }

    public static List<String> getStateFinal() {
        return stateFinal;
    }

    public static void setStateFinal(String stateFinal2) {
        stateFinal.add(stateFinal2);
    }
}
