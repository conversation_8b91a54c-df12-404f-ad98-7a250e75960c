package com.geely.gnds.tester.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/* loaded from: ObjectMapperUtils.class */
public class ObjectMapperUtils {
    private static final ObjectMapper MAPPER = new ObjectMapper();
    public static final String ERROR_STR = "解析JSON出错";

    private ObjectMapperUtils() {
    }

    static {
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MAPPER.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
    }

    public static ObjectMapper getInstance() {
        return MAPPER;
    }

    public static String findStrByJsonNodeExpr(String jsonStr, String expr) {
        try {
            return ((JsonNode) getInstance().readValue(jsonStr, JsonNode.class)).at(expr).asText();
        } catch (Exception e) {
            throw new CustomException(ERROR_STR, e);
        }
    }

    public static Object findObjByJsonNodeExpr(String jsonStr, String expr) {
        try {
            JsonNode at = ((JsonNode) getInstance().readValue(jsonStr, JsonNode.class)).at(expr);
            if (at.isBigInteger()) {
                return Long.valueOf(at.asLong());
            }
            if (at.isInt()) {
                return Integer.valueOf(at.asInt());
            }
            if (at.isDouble() || at.isFloat()) {
                return Double.valueOf(at.asDouble());
            }
            if (at.isBoolean()) {
                return Boolean.valueOf(at.asBoolean());
            }
            if (at.isTextual()) {
                return at.asText();
            }
            return at.toString();
        } catch (Exception e) {
            throw new CustomException(ERROR_STR, e);
        }
    }

    public static String obj2JsonStr(Object obj) {
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new CustomException(ERROR_STR, (Throwable) e);
        }
    }

    public static Map<String, Object> jsonStr2Map(String json) {
        try {
            return (Map) jsonStr2Ref(json, new TypeReference<Map<String, Object>>() { // from class: com.geely.gnds.tester.util.ObjectMapperUtils.1
            });
        } catch (Exception e) {
            throw new CustomException(ERROR_STR, e);
        }
    }

    public static Map<String, String> jsonStr2MapStr(String json) {
        try {
            return (Map) jsonStr2Ref(json, new TypeReference<Map<String, String>>() { // from class: com.geely.gnds.tester.util.ObjectMapperUtils.2
            });
        } catch (Exception e) {
            throw new CustomException(ERROR_STR, e);
        }
    }

    public static <T> T jsonStr2Ref(String str, TypeReference<T> typeReference) {
        try {
            return (T) MAPPER.readValue(str, typeReference);
        } catch (Exception e) {
            throw new CustomException(ERROR_STR, e);
        }
    }

    public static <T> T jsonStr2Clazz(String str, Class<T> cls) {
        try {
            return (T) MAPPER.readValue(str, cls);
        } catch (Exception e) {
            throw new CustomException(ERROR_STR, e);
        }
    }

    public static <T> Map<String, T> jsonStr2Map(String json, Class<T> clazz) {
        try {
            MapType mapType = TypeFactory.defaultInstance().constructMapType(Map.class, String.class, clazz);
            return (Map) MAPPER.readValue(json, mapType);
        } catch (Exception e) {
            throw new CustomException(ERROR_STR, e);
        }
    }

    public static <T> List<T> jsonStr2List(String json, Class<T> clazz) {
        try {
            return (List) MAPPER.readValue(json, getCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            throw new CustomException(ERROR_STR, (Throwable) e);
        }
    }

    public static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        return MAPPER.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }
}
