package com.geely.gnds.tester.util;

import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.util.Locale;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

/* loaded from: MessageUtils.class */
public class MessageUtils {
    private static MessageSource messageSource = (MessageSource) SpringContextUtils.getBean("messageSource");

    public static String getMessage(int code) {
        return getMessage(code, new String[0]);
    }

    public static String getMessage(int code, String... params) {
        return messageSource.getMessage(code + "", params, LocaleContextHolder.getLocale());
    }

    public static String getMessage(TesterErrorCodeEnum keys) {
        return messageSource.getMessage(keys.code(), (Object[]) null, Locale.getDefault());
    }

    public static String getMessage(String value) {
        return messageSource.getMessage(value, (Object[]) null, Locale.getDefault());
    }

    public static String getMessage(String value, String args) {
        return messageSource.getMessage(value, new String[]{args}, Locale.getDefault());
    }
}
