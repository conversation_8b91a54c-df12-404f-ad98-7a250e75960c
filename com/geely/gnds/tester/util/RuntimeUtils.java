package com.geely.gnds.tester.util;

import com.sun.management.OperatingSystemMXBean;
import java.lang.management.ManagementFactory;
import org.springframework.boot.system.ApplicationHome;

/* loaded from: RuntimeUtils.class */
public class RuntimeUtils {
    private static final OperatingSystemMXBean SYSTEM_MX_BEAN = ManagementFactory.getOperatingSystemMXBean();
    private static final Runtime RUNTIME = Runtime.getRuntime();

    public static String getHomePath() {
        ApplicationHome home = new ApplicationHome();
        try {
            return home.getSource().getParentFile().getAbsolutePath();
        } catch (Exception e) {
            return System.getProperty("user.dir");
        }
    }

    public static Runtime getRunTime() {
        return RUNTIME;
    }

    public static long getTotalPhysicalMemorySize() {
        return SYSTEM_MX_BEAN.getTotalPhysicalMemorySize();
    }

    public static long getFreePhysicalMemorySize() {
        return SYSTEM_MX_BEAN.getFreePhysicalMemorySize();
    }

    public static long getUsedPhysicalMemorySize() {
        return SYSTEM_MX_BEAN.getTotalPhysicalMemorySize() - SYSTEM_MX_BEAN.getFreePhysicalMemorySize();
    }

    public static long getTotalSwapSpaceSize() {
        return SYSTEM_MX_BEAN.getTotalSwapSpaceSize();
    }

    public static long getFreeSwapSpaceSize() {
        return SYSTEM_MX_BEAN.getFreeSwapSpaceSize();
    }

    public static long getUsedSwapSpaceSize() {
        return SYSTEM_MX_BEAN.getTotalSwapSpaceSize() - SYSTEM_MX_BEAN.getFreeSwapSpaceSize();
    }

    public static long getJvmMaxMemory() {
        return RUNTIME.maxMemory();
    }

    public static long getJvmTotalMemory() {
        return RUNTIME.totalMemory();
    }

    public static long getJvmFreeMemory() {
        return RUNTIME.freeMemory();
    }

    public static long getJvmUsedMemory() {
        return RUNTIME.totalMemory() - RUNTIME.freeMemory();
    }

    public static double getSystemCpuLoad() {
        return SYSTEM_MX_BEAN.getSystemCpuLoad();
    }

    public static double getProcessCpuLoad() {
        return SYSTEM_MX_BEAN.getProcessCpuLoad();
    }
}
