package com.geely.gnds.tester.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.dto.VdnDataDto;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.ManufacturingYearEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
/* loaded from: VehicleUtils.class */
public class VehicleUtils {

    @Value("#{${vehicleInfo}}")
    public Map<String, String> vehicleInfoConfig;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(VehicleUtils.class);

    public Map handleVehicleInfo(String vehicleInfo) {
        Map<String, Object> info = new HashMap<>(this.vehicleInfoConfig.size());
        for (String key : this.vehicleInfoConfig.keySet()) {
            String s = "/" + this.vehicleInfoConfig.get(key);
            System.out.println(s);
            info.put(key, ObjectMapperUtils.findObjByJsonNodeExpr(vehicleInfo, s));
        }
        return info;
    }

    public Map settleVehicleInfo(String vehicleInfo) throws JsonProcessingException {
        ObjectMapper mapper = ObjectMapperUtils.getInstance();
        Map<String, Object> info = new HashMap<>(this.vehicleInfoConfig.size() + 2);
        if (StringUtils.isBlank(vehicleInfo)) {
            return info;
        }
        Map<String, Object> json = (Map) mapper.readValue(vehicleInfo, new TypeReference<Map<String, Object>>() { // from class: com.geely.gnds.tester.util.VehicleUtils.1
        });
        Map<String, Object> jsonMap = (Map) mapper.readValue(ObjectMapperUtils.obj2JsonStr(json.get("vehicleData")), new TypeReference<Map<String, Object>>() { // from class: com.geely.gnds.tester.util.VehicleUtils.2
        });
        String vin = jsonMap.get("vin") == null ? "" : jsonMap.get("vin").toString();
        String brandName = (String) jsonMap.get("brandName");
        String vehicleType = (String) jsonMap.get("pno18");
        String fyon = (String) jsonMap.get("fyon");
        String vehicleModel = jsonMap.get("vehicleModel") == null ? "" : jsonMap.get("vehicleModel").toString();
        info.put("vin", vin);
        info.put("brandName", brandName);
        if (vehicleType != null && vehicleType.length() > 2) {
            vehicleType = vehicleType.substring(0, 3);
        }
        info.put("vehicleType", vehicleType);
        info.put("fyon", fyon);
        this.manager.setGlobal(GlobalVariableEnum.lX, vehicleInfo, vin);
        info.put("carImage", jsonMap.get("carImage"));
        info.put("structureWeek", jsonMap.get("structureWeek"));
        info.put("brandId", jsonMap.get("brandId"));
        info.put("salesMarketId", jsonMap.get("salesMarketId"));
        info.put("salesMarketName", jsonMap.get("salesMarketName"));
        String vdns = ObjectMapperUtils.obj2JsonStr(json.get("vdnData"));
        info.put("platformCode", jsonMap.get("platformCode"));
        info.put("platformName", jsonMap.get("platformName"));
        this.manager.setGlobal("platformCode", jsonMap.get("platformCode"), vin);
        info.put("singleEcuSwitch", jsonMap.get("singleEcuSwitch"));
        List<VdnDataDto> vdnList = Arrays.asList((Object[]) mapper.readValue(vdns, VdnDataDto[].class));
        List<String> attributeList = new ArrayList<>(this.vehicleInfoConfig.size());
        Iterator<String> it = this.vehicleInfoConfig.keySet().iterator();
        while (it.hasNext()) {
            attributeList.add(this.vehicleInfoConfig.get(it.next()));
        }
        List<String> vdnStrList = new ArrayList<>(vdnList.size());
        String st = (String) jsonMap.get("structureWeek");
        info.put("st", st);
        String groupNum = "00";
        log.info("vehicle settleVehicleInfo vdnList.forEach开始");
        vdnList.forEach(vdn -> {
            String value = vdn.getValue();
            vdnStrList.add(value);
            if (value.startsWith(Constants.AA) && !value.equals(Constants.AA + groupNum)) {
                info.put(Constants.AA, value);
                info.put("AAdro", vdn.getName());
            }
            if (value.startsWith(Constants.MY) && !value.equals(Constants.MY + groupNum)) {
                info.put(Constants.MY, value);
                info.put("MYdro", vdn.getName());
            }
            if (value.startsWith(Constants.BA) && !value.equals(Constants.BA + groupNum)) {
                info.put(Constants.BA, value);
                info.put("BAdro", vdn.getName());
            }
            if (value.startsWith(Constants.CA) && !value.equals(Constants.CA + groupNum)) {
                info.put(Constants.CA, value);
                info.put("CAdro", vdn.getName());
            }
        });
        log.info("vehicle settleVehicleInfo vdnList.forEach结束");
        info.put("vdnStrList", vdnStrList);
        for (VdnDataDto vdnDataDto : vdnList) {
            String attribute = vdnDataDto.getAttribute();
            String value1 = vdnDataDto.getValue();
            String ld = vdnDataDto.getLd();
            String sd = vdnDataDto.getSd();
            if (attributeList.contains(attribute)) {
                Iterator<String> it2 = this.vehicleInfoConfig.keySet().iterator();
                while (true) {
                    if (it2.hasNext()) {
                        String key = it2.next();
                        String value = this.vehicleInfoConfig.get(key);
                        if (!value1.equals(value + "00") && value.equals(attribute)) {
                            if (Objects.equals(key, "manufacturingYear")) {
                                ld = ld.replace("年", "");
                            }
                            if ("zh-CN".equals(HttpUtils.getLanguage())) {
                                info.put(key, ld);
                            } else {
                                info.put(key, sd);
                            }
                        }
                    }
                }
            }
        }
        String manufacturingYear = (String) info.get("manufacturingYear");
        if (StringUtils.isEmpty(manufacturingYear)) {
            String transferYear = ManufacturingYearEnum.cn(vin.substring(9, 10));
            info.put("manufacturingYear", transferYear);
            log.info("manufacturingYear:{}转换{}", vin, transferYear);
        }
        if (StringUtils.isNotBlank(vehicleModel)) {
            info.put("vehicle", vehicleModel);
        }
        log.info("vehicle settleVehicleInfo for(VdnDataDto vdnDataDto:vdnList)");
        info.put("chassis", vin.substring(vin.length() - 6));
        return info;
    }
}
