package com.geely.gnds.tester.util;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import java.io.Serializable;

/* loaded from: Result.class */
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1;
    private int code = 0;
    private String msg = "success";
    private T data;

    public Result<T> ok(T data) {
        this.code = HttpStatus.SUCCESS;
        setData(data);
        return this;
    }

    public boolean success() {
        return this.code == 0 || this.code == 200;
    }

    public Result<T> error() {
        this.code = HttpStatus.ERROR;
        this.msg = MessageUtils.getMessage(this.code);
        return this;
    }

    public Result<T> error(int code) {
        this.code = code;
        this.msg = MessageUtils.getMessage(this.code);
        return this;
    }

    public Result<T> error(int code, String msg) {
        this.code = code;
        this.msg = msg;
        return this;
    }

    public Result<T> error(String msg) {
        this.code = HttpStatus.ERROR;
        this.msg = msg;
        return this;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return this.code == 0;
    }

    public String toString() {
        return "Result{code=" + this.code + ", msg='" + this.msg + "', data=" + this.data + '}';
    }
}
