package com.geely.gnds.tester.util;

import cn.hutool.crypto.SecureUtil;
import java.io.File;
import java.util.Iterator;
import java.util.List;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.ComputerSystem;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OSFileStore;

/* loaded from: MachineUtil.class */
public class MachineUtil {
    public static void main(String[] args) {
        SystemInfo systemInfo = new SystemInfo();
        HardwareAbstractionLayer hal = systemInfo.getHardware();
        String cpuSerial = getCpuSerial(hal.getProcessor());
        System.out.println("CPU序列号：" + cpuSerial);
        String diskSerial = getDiskSerial(systemInfo);
        System.out.println("磁盘序列号：" + diskSerial);
        String motherboardSerial = getMotherboardSerial(hal.getComputerSystem());
        System.out.println("主板序列号：" + motherboardSerial);
        String memorySerial = getMemorySerial(hal.getMemory());
        System.out.println("内存大小：" + memorySerial);
        String combinedInfo = cpuSerial + diskSerial + motherboardSerial + memorySerial;
        String machineCode = SecureUtil.sha256(combinedInfo);
        System.out.println("Unique Machine Code: " + machineCode);
    }

    public static String getMachineCode() {
        SystemInfo systemInfo = new SystemInfo();
        HardwareAbstractionLayer hal = systemInfo.getHardware();
        String cpuSerial = getCpuSerial(hal.getProcessor());
        String diskSerial = getDiskSerial(systemInfo);
        String motherboardSerial = getMotherboardSerial(hal.getComputerSystem());
        String memorySerial = getMemorySerial(hal.getMemory());
        String combinedInfo = cpuSerial + diskSerial + motherboardSerial + memorySerial;
        String machineCode = SecureUtil.sha256(combinedInfo);
        return machineCode;
    }

    private static String getCpuSerial(CentralProcessor processor) {
        return processor.getProcessorIdentifier().getProcessorID();
    }

    private static String getDiskSerial(SystemInfo si) {
        String systemDrive = System.getenv("SystemDrive") + File.separator;
        StringBuilder serials = new StringBuilder();
        List<OSFileStore> stores = si.getOperatingSystem().getFileSystem().getFileStores();
        Iterator<OSFileStore> it = stores.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            OSFileStore store = it.next();
            if (systemDrive.equals(store.getMount())) {
                serials.append(store.getUUID());
                break;
            }
        }
        return serials.toString().trim();
    }

    private static String getMotherboardSerial(ComputerSystem computerSystem) {
        return computerSystem.getBaseboard().getSerialNumber();
    }

    private static String getMemorySerial(GlobalMemory memory) {
        return String.valueOf(memory.getTotal());
    }
}
