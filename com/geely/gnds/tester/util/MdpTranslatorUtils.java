package com.geely.gnds.tester.util;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;
import javax.xml.bind.DatatypeConverter;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
/* loaded from: MdpTranslatorUtils.class */
public class MdpTranslatorUtils {
    private static final Logger log = LoggerFactory.getLogger(MdpTranslatorUtils.class);

    public static Map<String, String> parseResponse(String diagnosticResponse) throws IOException {
        boolean invalid;
        byte[] bytes = DatatypeConverter.parseHexBinary(diagnosticResponse);
        ByteArrayInputStream in = new ByteArrayInputStream(bytes);
        Map<String, String> map = new HashMap<>();
        try {
            int addressType = 0;
            do {
                try {
                    Map<String, Object> parseMap = parseHeader(in, addressType);
                    invalid = ((Boolean) parseMap.get("invalid")).booleanValue();
                    addressType = ((Integer) parseMap.get("addressType")).intValue();
                    int payloadLength = ((Integer) parseMap.get("payloadLength")).intValue();
                    ((Integer) parseMap.get("status")).intValue();
                    int i = in.read() & 255;
                    int expectContentLength = payloadLength - 1;
                    if (invalid) {
                        String payload = "";
                        int toReadLength = expectContentLength;
                        if (payloadLength > 0) {
                            if (toReadLength > in.available()) {
                                log.warn("toReadLength=[{}] > in.available()=[{}]", Integer.valueOf(toReadLength), Integer.valueOf(in.available()));
                                toReadLength = in.available();
                            }
                            byte[] lineBuffer = new byte[toReadLength];
                            int readLength = in.read(lineBuffer);
                            if (readLength != toReadLength) {
                                log.warn("read from in expect [{}] but actual [{}]", Integer.valueOf(toReadLength), Integer.valueOf(readLength));
                            }
                            payload = Hex.encodeHexString(lineBuffer).toUpperCase();
                        }
                        map.put(payload.substring(0, 4), payload.substring(4));
                    }
                    if (in.available() <= 0) {
                        break;
                    }
                } catch (IOException e) {
                    String message = String.format("parseResponse IOException: msg=[%s]", e);
                    log.error(message);
                    try {
                        in.close();
                    } catch (IOException e2) {
                        log.error("close in error: msg=[{}]", e2.getMessage());
                    }
                }
            } while (invalid);
            try {
                in.close();
            } catch (IOException e3) {
                log.error("close in error: msg=[{}]", e3.getMessage());
            }
            return map;
        } catch (Throwable th) {
            try {
                in.close();
            } catch (IOException e4) {
                log.error("close in error: msg=[{}]", e4.getMessage());
            }
            throw th;
        }
    }

    private static Map<String, Object> parseHeader(ByteArrayInputStream in, int addressType) throws IOException {
        byte status;
        byte[] byteBuffer2 = new byte[2];
        boolean invalid = true;
        int readLength = 0;
        int isAddress = 0;
        int payloadLength = 0;
        String address = "";
        byte b = 0;
        while (true) {
            status = b;
            if (in.available() > 0 && invalid && status < 3) {
                switch (status) {
                    case 0:
                        isAddress = in.read();
                        if (isAddress == 255 || isAddress == 0) {
                            addressType = isAddress & 255;
                        }
                        b = 1;
                        break;
                    case 1:
                        if (isAddress != 255 && isAddress != 0) {
                            byteBuffer2[0] = (byte) isAddress;
                            byteBuffer2[1] = (byte) in.read();
                            readLength = byteBuffer2.length;
                        } else {
                            readLength = in.read(byteBuffer2);
                        }
                        if (readLength < 2) {
                            invalid = false;
                        }
                        address = Hex.encodeHexString(byteBuffer2);
                        b = 2;
                        break;
                    case 2:
                        readLength = in.read(byteBuffer2);
                        if (readLength < 2) {
                            invalid = false;
                        }
                        payloadLength = ByteBuffer.wrap(byteBuffer2).getShort() & 65535;
                        if (payloadLength <= 0) {
                            invalid = false;
                            log.error("payloadLength expect > 0, actual [{}]", Integer.valueOf(payloadLength));
                        }
                        b = 3;
                        break;
                    default:
                        b = 0;
                        break;
                }
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("invalid", Boolean.valueOf(invalid));
        map.put("readLength", Integer.valueOf(readLength));
        map.put("addressType", Integer.valueOf(addressType));
        map.put("payloadLength", Integer.valueOf(payloadLength));
        map.put("address", address);
        map.put("status", Integer.valueOf(status));
        return map;
    }
}
