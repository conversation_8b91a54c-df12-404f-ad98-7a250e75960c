package com.geely.gnds.tester.util;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.dto.DiagnosisEcuDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: ParseUtils.class */
public class ParseUtils {
    private static final Logger log = LoggerFactory.getLogger(ParseUtils.class);

    public static byte[] byteToBitOfArray(byte b) {
        byte[] array = new byte[8];
        for (int i = ConstantEnum.SEVEN.intValue(); i >= 0; i--) {
            array[i] = (byte) (b & 1);
            b = (byte) (b >> 1);
        }
        return array;
    }

    public static List<DiagnosisEcuDto> parseDtcInfo(String dtcInfo) {
        List<DiagnosisEcuDto> diagnosisEcuDtos = new ArrayList<>();
        if (StringUtils.isNotBlank(dtcInfo)) {
            Map<String, Object> map = ObjectMapperUtils.jsonStr2Map(dtcInfo);
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                Object value = entry.getValue();
                String s = ObjectMapperUtils.obj2JsonStr(value);
                DiagnosisEcuDto diagnosisEcuDto = (DiagnosisEcuDto) ObjectMapperUtils.jsonStr2Clazz(s, DiagnosisEcuDto.class);
                diagnosisEcuDtos.add(diagnosisEcuDto);
            }
        }
        return diagnosisEcuDtos;
    }

    public static void main(String[] args) throws Exception {
        long l = parseDtcDate("180508122331");
        System.out.println(l);
    }

    public static long parseDtcDate(String hex) throws Exception {
        log.info("parseDtcDate解析入参：{}", hex);
        boolean start = true;
        StringBuilder sb = new StringBuilder();
        for (String s : splitStringByLength(hex, 2)) {
            int i = Integer.parseInt(s, 16);
            String time = i + "";
            if (time.length() == 1) {
                time = "0" + time;
            }
            if (start) {
                sb.append("20").append(time).append("-");
            } else {
                sb.append(time).append("-");
            }
            start = false;
        }
        long time2 = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS3).parse(sb.toString()).getTime();
        log.info("parseDtcDate解析出参：{}", Long.valueOf(time2));
        return time2;
    }

    public static List<String> splitStringByLength(String str, int length) {
        List<String> resultList = new ArrayList<>();
        int i = 0;
        while (true) {
            int index = i;
            if (index < str.length()) {
                if (index + length < str.length()) {
                    resultList.add(str.substring(index, index + length));
                } else {
                    resultList.add(str.substring(index));
                }
                i = index + length;
            } else {
                return resultList;
            }
        }
    }
}
