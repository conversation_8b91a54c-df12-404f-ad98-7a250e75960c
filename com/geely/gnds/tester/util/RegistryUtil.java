package com.geely.gnds.tester.util;

import com.sun.jna.platform.win32.Advapi32Util;
import com.sun.jna.platform.win32.WinReg;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/* loaded from: RegistryUtil.class */
public class RegistryUtil {
    private static Map<String, WinReg.HKEY> HKEY_MAP = new HashMap();

    static {
        HKEY_MAP.put("HKEY_CLASSES_ROOT", WinReg.HKEY_CLASSES_ROOT);
        HKEY_MAP.put("HKEY_CURRENT_USER", WinReg.HKEY_CURRENT_USER);
        HKEY_MAP.put("HKEY_LOCAL_MACHINE", WinReg.HKEY_LOCAL_MACHINE);
        HKEY_MAP.put("HKEY_USERS", WinReg.HKEY_USERS);
        HKEY_MAP.put("HKEY_PERFORMANCE_DATA", WinReg.HKEY_PERFORMANCE_DATA);
        HKEY_MAP.put("HKEY_PERFORMANCE_TEXT", WinReg.HKEY_PERFORMANCE_TEXT);
        HKEY_MAP.put("HKEY_PERFORMANCE_NLSTEXT", WinReg.HKEY_PERFORMANCE_NLSTEXT);
        HKEY_MAP.put("HKEY_CURRENT_CONFIG", WinReg.HKEY_CURRENT_CONFIG);
        HKEY_MAP.put("HKEY_DYN_DATA", WinReg.HKEY_DYN_DATA);
        HKEY_MAP.put("HKEY_CURRENT_USER_LOCAL_SETTINGS", WinReg.HKEY_CURRENT_USER_LOCAL_SETTINGS);
    }

    public static boolean writeToRegistry(String key, String value) {
        boolean isOkay = Advapi32Util.registryKeyExists(WinReg.HKEY_LOCAL_MACHINE, "SOFTWARE\\keyPath");
        if (!isOkay) {
            try {
                Advapi32Util.registryCreateKey(WinReg.HKEY_LOCAL_MACHINE, "SOFTWARE\\你要添加的keyPath");
            } catch (Exception e) {
                return false;
            }
        }
        try {
            Advapi32Util.registrySetStringValue(WinReg.HKEY_LOCAL_MACHINE, "SOFTWARE\\你要添加的keyPath", "key", "value");
            return true;
        } catch (Exception e2) {
            return false;
        }
    }

    public static boolean deleteRegistryKey(String keyPath) {
        try {
            Advapi32Util.registryDeleteKey(WinReg.HKEY_LOCAL_MACHINE, "SOFTWARE\\keyPath");
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static String readKeyFromRegistry(WinReg.HKEY hKey, String key, String subKey) {
        String value;
        try {
            value = Advapi32Util.registryGetStringValue(hKey, key, subKey);
        } catch (Exception e) {
            value = null;
        }
        return value;
    }

    public static WinReg.HKEY getHkeyByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return HKEY_MAP.get(name);
    }

    public static void main(String[] args) {
        System.out.println(readKeyFromRegistry(WinReg.HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\Cryptography", "MachineGuid"));
        System.out.println(getHkeyByName("HKEY_LOCAL_MACHINE"));
    }
}
