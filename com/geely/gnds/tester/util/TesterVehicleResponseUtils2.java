package com.geely.gnds.tester.util;

import com.geely.gnds.ruoyi.common.constant.UserConstants;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.enums.TesterNegativeResEnum;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.jexl3.JexlBuilder;
import org.apache.commons.jexl3.JexlEngine;
import org.apache.commons.jexl3.JexlExpression;
import org.apache.commons.jexl3.MapContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

/* loaded from: TesterVehicleResponseUtils2.class */
public class TesterVehicleResponseUtils2 {
    public static final String EQ = "=";
    public static final String NE = "!=";
    public static final String GT = "&gt;";
    public static final String GT_CHAR = ">";
    public static final String GE = "&gt;=";
    public static final String GE_CHAR = ">=";
    public static final String LT = "&lt;";
    public static final String LT_CHAR = "<";
    public static final String LE = "&lt;=";
    public static final String LE_CHAR = "<=";
    public static final String HEX_PRE_B = "0X";
    public static final String HEX_PRE_L = "0x";
    public static final String AND = "&amp;";
    public static final String AND_CHAR = "&";
    private static final String INT_MAX = "FFFFFFFF";
    private static final Logger log = LoggerFactory.getLogger(TesterVehicleResponseUtils2.class);
    public static final Map<String, Map<String, List<String>>> PIN_CODE = new HashMap();
    private static final Pattern ADN_PATTERN = Pattern.compile("^(X&|x&)[\\w]*[/|\\*|\\+|-|%]");
    public static final Map<String, String> PIN_CODE_LINK = new HashMap();
    static JexlEngine engine = new JexlBuilder().create();

    private TesterVehicleResponseUtils2() {
    }

    static {
        PIN_CODE_LINK.put("01", "000");
        PIN_CODE_LINK.put("02", "000");
        PIN_CODE_LINK.put("03", "003");
        PIN_CODE_LINK.put("04", "003");
        PIN_CODE_LINK.put("05", "005");
        PIN_CODE_LINK.put("06", "005");
        PIN_CODE_LINK.put("11", "009");
        PIN_CODE_LINK.put("12", "009");
        PIN_CODE_LINK.put("19", "019");
        PIN_CODE_LINK.put("1A", "019");
        PIN_CODE_LINK.put("31", "031");
        PIN_CODE_LINK.put("32", "031");
    }

    /* loaded from: TesterVehicleResponseUtils2$PinCodeDto.class */
    static class PinCodeDto {
        private String pincodeKey;
        private String value;

        PinCodeDto() {
        }

        public String getPincodeKey() {
            return this.pincodeKey;
        }

        public void setPincodeKey(String pincodeKey) {
            this.pincodeKey = pincodeKey;
        }

        public String getValue() {
            return this.value;
        }

        public List<String> getValueList() {
            if (StringUtils.isNotBlank(this.value)) {
                return ObjectMapperUtils.jsonStr2List(this.value.replace("'", "\""), String.class);
            }
            return new ArrayList();
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String toString() {
            return "PinCodeDto [pincodeKey=" + this.pincodeKey + ", value=" + this.value + "]";
        }
    }

    public static List getPinCodeByPinCodeKey(String pinCodeKey, Object pinCodeStr) {
        List<String> valueList = new ArrayList<>();
        if (StringUtils.isBlank(pinCodeKey) || pinCodeStr == null) {
            return valueList;
        }
        String obj2JsonStr = ObjectMapperUtils.obj2JsonStr(pinCodeStr);
        if (StringUtils.isBlank(obj2JsonStr)) {
            return valueList;
        }
        List<PinCodeDto> jsonStr2List = ObjectMapperUtils.jsonStr2List(obj2JsonStr, PinCodeDto.class);
        Iterator<PinCodeDto> it = jsonStr2List.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            PinCodeDto pinCodeDto = it.next();
            String key = pinCodeDto.getPincodeKey();
            if (StringUtils.isNotBlank(key) && key.equals(pinCodeKey)) {
                valueList = pinCodeDto.getValueList();
                break;
            }
        }
        return valueList;
    }

    public static void setPinCodeByEcu(String ecuAddress, Object pinCodeStr) {
        if (StringUtils.isBlank(ecuAddress) || pinCodeStr == null) {
            return;
        }
        String obj2JsonStr = ObjectMapperUtils.obj2JsonStr(pinCodeStr);
        if (StringUtils.isBlank(obj2JsonStr)) {
            return;
        }
        List<PinCodeDto> jsonStr2List = ObjectMapperUtils.jsonStr2List(obj2JsonStr, PinCodeDto.class);
        Map<String, List<String>> codeMap = new HashMap<>(jsonStr2List.size());
        for (PinCodeDto pinCodeDto : jsonStr2List) {
            if (StringUtils.isNotBlank(pinCodeDto.getPincodeKey())) {
                codeMap.put(pinCodeDto.getPincodeKey(), pinCodeDto.getValueList());
            }
        }
        if (!CollectionUtils.isEmpty(codeMap)) {
            PIN_CODE.put(ecuAddress, codeMap);
        }
    }

    public static String parseParam(String response, String did, String didSize, String itemOutType, String inDataType, String itemOffset, String itemSize, String itemFormula, String itemCompareValue) throws Exception {
        if (StringUtils.isBlank(response)) {
            throw new CustomException("没有应答数据");
        }
        if (!response.contains(did)) {
            throw new CustomException("应答数据内不包含此DID");
        }
        if (StringUtils.isBlank(didSize) || StringUtils.isBlank(itemOutType) || StringUtils.isBlank(inDataType) || StringUtils.isBlank(itemOffset) || StringUtils.isBlank(itemSize)) {
            throw new CustomException("解析应答必要条件不足");
        }
        int didLength = Integer.parseInt(didSize);
        if (response.length() < response.indexOf(did) + did.length() + (didLength * ConstantEnum.TWO.intValue())) {
            throw new CustomException("应答数据长度有误");
        }
        int begin = response.indexOf(did) + did.length();
        String subStr = response.substring(begin, begin + (didLength * 2));
        String outValue = offsetSubStr(itemOffset, itemSize, subStr);
        if ("01".equals(itemOutType)) {
            Object obj = getValueByInDataType(inDataType, outValue);
            return subDouble(readOutValue(itemFormula, itemCompareValue, obj));
        }
        if ("02".equals(itemOutType)) {
            Object obj2 = getValueByInDataType(inDataType, outValue);
            String readOutValue = subDouble(readOutValue(itemFormula, itemCompareValue, obj2));
            try {
                if (StringUtils.isNotBlank(readOutValue)) {
                    BigDecimal bigDecimal = new BigDecimal(readOutValue);
                    BigInteger tryValue = bigDecimal.toBigInteger();
                    String hexTemp = tryValue.toString(16);
                    int itemSizeLength = Integer.valueOf(itemSize, 16).intValue() * ConstantEnum.TWO.intValue();
                    StringBuilder hexSb = new StringBuilder("0x");
                    if (itemSizeLength > hexTemp.length()) {
                        for (int i = 0; i < itemSizeLength - hexTemp.length(); i++) {
                            hexSb.append("0");
                        }
                    }
                    return hexSb.append(hexTemp).toString();
                }
                return readOutValue;
            } catch (Exception e) {
                log.warn("返回值转为16进制时出错", e);
                throw e;
            }
        }
        if ("03".equals(itemOutType)) {
            Object obj3 = getValueByInDataType(inDataType, outValue);
            String readOutValue2 = subDouble(readOutValue(itemFormula, itemCompareValue, obj3));
            try {
                if (StringUtils.isNotBlank(readOutValue2)) {
                    BigDecimal bigDecimal2 = new BigDecimal(readOutValue2);
                    BigInteger tryValue2 = bigDecimal2.toBigInteger();
                    return "0b" + tryValue2.toString(2);
                }
            } catch (Exception e2) {
                log.warn("返回值转为2进制时出错", e2);
            }
            return readOutValue2;
        }
        if ("04".equals(itemOutType)) {
            Object obj4 = getValueByInDataType(inDataType, outValue);
            String readOutValue3 = subDouble(readOutValue(itemFormula, itemCompareValue, obj4));
            try {
                if (StringUtils.isNotBlank(readOutValue3)) {
                    BigDecimal bigDecimal3 = new BigDecimal(readOutValue3);
                    BigInteger tryValue3 = bigDecimal3.toBigInteger();
                    return "0" + tryValue3.toString(8);
                }
            } catch (Exception e3) {
                log.warn("返回值转为8进制时出错", e3);
            }
            return readOutValue3;
        }
        if ("05".equals(itemOutType)) {
            return outValue;
        }
        if ("06".equals(itemOutType)) {
            int ascSize = outValue.length() / 2;
            StringBuilder sb = new StringBuilder();
            if (ascSize > 0) {
                for (int i2 = 0; i2 < ascSize - 1; i2++) {
                    sb.append((char) Integer.parseInt(outValue.substring(i2 * 2, (i2 + 1) * 2), 16));
                }
                sb.append((char) Integer.parseInt(outValue.substring((ascSize - 1) * 2), 16));
            } else {
                sb.append((char) Integer.parseInt(outValue, 16));
            }
            return sb.toString();
        }
        if ("07".equals(itemOutType)) {
            if (outValue.length() != 14) {
                throw new CustomException("07类型数据长度有误");
            }
            String bcd = outValue.substring(0, 8);
            String asc1 = String.valueOf((char) Integer.parseInt(outValue.substring(8, 10), 16));
            String asc2 = String.valueOf((char) Integer.parseInt(outValue.substring(10, 12), 16));
            String asc3 = String.valueOf((char) Integer.parseInt(outValue.substring(12, 14), 16));
            StringBuilder sb2 = new StringBuilder(bcd);
            return sb2.append(asc1).append(asc2).append(asc3).toString();
        }
        if ("08".equals(itemOutType)) {
            if (outValue.length() != 16) {
                throw new CustomException("08类型数据长度有误");
            }
            String bcd2 = outValue.substring(0, 10);
            String asc12 = String.valueOf((char) Integer.parseInt(outValue.substring(10, 12), 16));
            String asc22 = String.valueOf((char) Integer.parseInt(outValue.substring(12, 14), 16));
            String asc32 = String.valueOf((char) Integer.parseInt(outValue.substring(14, 16), 16));
            StringBuilder sb3 = new StringBuilder(bcd2);
            return sb3.append(asc12).append(asc22).append(asc32).toString();
        }
        throw new CustomException("未知itemOutType数据类型");
    }

    private static Object getValueByInDataType(String inDataType, String outValue) {
        Object obj = null;
        BigInteger hexToIntValue = new BigInteger(outValue, 16);
        if ("02".equals(inDataType)) {
            obj = getSignalHex(outValue);
        } else if ("03".equals(inDataType)) {
            try {
                Long hexToLongValue = Long.valueOf(Long.parseLong(outValue, 16));
                obj = Double.valueOf(Double.longBitsToDouble(hexToLongValue.longValue()));
            } catch (Exception e) {
                log.error("十六进制转浮点数出错【{}】", outValue, e);
            }
        } else {
            obj = Double.valueOf(hexToIntValue.doubleValue());
        }
        if (obj == null) {
            log.warn("未能根据inDataType解析出正确的输入值");
            obj = Double.valueOf(hexToIntValue.doubleValue());
        }
        return obj;
    }

    public static void main(String[] args) {
        getValueByInDataType("01", "01010101020101010102010101040101010101010103010101000101010101010101010101010101010101010102020101010101010101010101010101020101010101010101330101010101010100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010102010102010201020201020101010202020102020101010101010102010101020101010202020101020102020102010101010201000001020102020100010100000001010101010202020102020201010101020101020202010101010201010100010101010102010100000000000202010101010101010101010101010101010201020101010201020101000000000000000000010101000100000000000000000002020000000000000102020201000000000000010101000000000000000000000000000000000000000000000000000000010101010101010101000100010101010101010101010101010101010101010101020202010202010201020000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000");
    }

    private static Object getSignalHex(String outValue) {
        Object obj = null;
        String firstHex = outValue.substring(0, 1);
        if (Integer.parseInt(firstHex, 16) >= 8) {
            StringBuilder sb = new StringBuilder();
            if (outValue.length() < 8) {
                int appendSize = 8 - outValue.length();
                for (int i = 0; i < appendSize; i++) {
                    sb.append(UserConstants.TYPE_BUTTON);
                }
            } else if (outValue.length() < 16) {
                int appendSize2 = 16 - outValue.length();
                for (int i2 = 0; i2 < appendSize2; i2++) {
                    sb.append(UserConstants.TYPE_BUTTON);
                }
            }
            sb.append(outValue);
            BigInteger hexToIntValue = new BigInteger(sb.toString(), 16);
            try {
                if (hexToIntValue.compareTo(new BigInteger(INT_MAX, 16)) == 1) {
                    obj = Double.valueOf(hexToIntValue.longValue());
                } else {
                    obj = Double.valueOf(hexToIntValue.intValue());
                }
            } catch (Exception e) {
                log.error("十六进制转int带符号数字出错【{}】", outValue, e);
            }
        } else {
            BigInteger hexToIntValue2 = new BigInteger(outValue, 16);
            try {
                if (hexToIntValue2.compareTo(new BigInteger(INT_MAX, 16)) == 1) {
                    obj = Double.valueOf(hexToIntValue2.longValue());
                } else {
                    obj = Double.valueOf(hexToIntValue2.intValue());
                }
            } catch (Exception e2) {
                log.error("十六进制转int带符号数字出错【{}】", outValue, e2);
            }
        }
        return obj;
    }

    private static String subDouble(String readIntegerValue) {
        if (StringUtils.isNotBlank(readIntegerValue) && readIntegerValue.contains(ConstantEnum.POINT)) {
            int indexOf = readIntegerValue.indexOf(ConstantEnum.POINT);
            if (indexOf + 3 < readIntegerValue.length()) {
                try {
                    BigDecimal bigDecimal = new BigDecimal(readIntegerValue);
                    readIntegerValue = bigDecimal.setScale(2, 4).toPlainString();
                } catch (Exception e) {
                }
            }
        }
        return readIntegerValue;
    }

    private static String readOutValue(String itemFormula, String itemCompareValue, Object outValue) {
        if (StringUtils.isBlank(itemCompareValue)) {
            if (StringUtils.isNotBlank(itemFormula)) {
                return executeExpr(itemFormula, outValue).toString();
            }
            return String.valueOf(outValue);
        }
        if (StringUtils.isNotBlank(itemFormula)) {
            Object formulaRes = executeExpr(itemFormula, outValue);
            String expr = getReplaceExpr(itemCompareValue);
            Object executeExpr = executeExpr(expr, formulaRes);
            if (Boolean.TRUE.equals(executeExpr)) {
                return formulaRes.toString();
            }
            return "";
        }
        log.error("需要进行运算比较，但没有itemFormula");
        return "";
    }

    private static String getReplaceExpr(String itemCompareValue) {
        StringBuilder expr;
        StringBuilder expr2 = new StringBuilder("X");
        if (itemCompareValue.indexOf("=") == 0) {
            expr = expr2.append("=").append(itemCompareValue.trim());
        } else if (itemCompareValue.indexOf("&gt;=") == 0 || itemCompareValue.indexOf(">=") == 0) {
            expr = expr2.append(itemCompareValue.trim().replace("&gt;=", ">="));
        } else if (itemCompareValue.indexOf("&gt;") == 0 || itemCompareValue.indexOf(">") == 0) {
            expr = expr2.append(itemCompareValue.trim().replace("&gt;", ">"));
        } else if (itemCompareValue.indexOf("&lt;=") == 0 || itemCompareValue.indexOf("<=") == 0) {
            expr = expr2.append(itemCompareValue.trim().replace("&lt;=", "<="));
        } else if (itemCompareValue.indexOf("&lt;") == 0 || itemCompareValue.indexOf("<") == 0) {
            expr = expr2.append(itemCompareValue.trim().replace("&lt;", "<"));
        } else if (itemCompareValue.indexOf("!=") == 0) {
            expr = expr2.append(itemCompareValue.trim());
        } else {
            log.error("未知itemCompareValue表达式【{}】", itemCompareValue);
            throw new CustomException("未知itemCompareValue表达式");
        }
        return expr.toString();
    }

    private static String offsetSubStr(String itemOffset, String itemSize, String subStr) {
        int itemOffsetLength = Integer.valueOf(itemOffset, 16).intValue();
        int itemSizeLength = Integer.valueOf(itemSize, 16).intValue();
        if (subStr.length() < (itemOffsetLength * ConstantEnum.TWO.intValue()) + (itemSizeLength * ConstantEnum.TWO.intValue())) {
            throw new CustomException("应答数据长度有误");
        }
        return subStr.substring(itemOffsetLength * 2, (itemOffsetLength * 2) + (itemSizeLength * 2));
    }

    public static Object executeExpr(String expr, Object res) {
        JexlExpression expression = engine.createExpression(replaceExpr(expr));
        MapContext mapContext = new MapContext();
        mapContext.set("X", res);
        mapContext.set("x", res);
        Object evaluate = expression.evaluate(mapContext);
        return evaluate;
    }

    public static Object executeExpr2(String expr, Object res) {
        JexlExpression expression = engine.createExpression(replaceExpr(expr));
        MapContext mapContext = new MapContext();
        mapContext.set("X", res);
        mapContext.set("x", res);
        Object evaluate = expression.evaluate(mapContext);
        return evaluate;
    }

    private static String replaceExpr(String expr) {
        String expr2 = expr.replace("&amp;", "&");
        if (expr2.contains("&")) {
            Matcher m = ADN_PATTERN.matcher(expr2);
            if (m.find()) {
                String group = m.group(0);
                String end = expr2.substring(group.length() - 1, expr2.length());
                StringBuilder sb = new StringBuilder();
                expr2 = sb.append("(").append(group.substring(0, group.length() - 1)).append(")").append(end).toString();
            } else {
                StringBuilder sb2 = new StringBuilder();
                expr2 = sb2.append("(").append(expr2).append(")").toString();
            }
        }
        return expr2;
    }

    public static void handleNegativeRes(String res, String sId) {
        if (StringUtils.isBlank(res) || StringUtils.isBlank(sId)) {
            return;
        }
        String str = "7F" + sId;
        for (TesterNegativeResEnum testerNegativeResEnum : TesterNegativeResEnum.values()) {
            if (res.equals(str + testerNegativeResEnum.getCode())) {
                TesterErrorCodeEnum errorEnum = testerNegativeResEnum.getErrorEnum();
                throw new CustomException(TesterErrorCodeEnum.formatMsg(errorEnum));
            }
        }
    }
}
