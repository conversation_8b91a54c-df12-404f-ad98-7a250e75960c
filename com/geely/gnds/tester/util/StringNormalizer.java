package com.geely.gnds.tester.util;

import java.text.Normalizer;
import org.springframework.util.StringUtils;

/* loaded from: StringNormalizer.class */
public class StringNormalizer {
    public static String normalize(String str) {
        if (!StringUtils.hasLength(str)) {
            return null;
        }
        return Normalizer.normalize(str, Normalizer.Form.NFKC);
    }

    public static String normalizec(String str) {
        if (!StringUtils.hasLength(str)) {
            return null;
        }
        return Normalizer.normalize(str, Normalizer.Form.NFC);
    }
}
