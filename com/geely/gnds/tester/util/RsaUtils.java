package com.geely.gnds.tester.util;

import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.Cipher;
import org.apache.commons.codec.binary.Base64;

/* loaded from: RsaUtils.class */
public class RsaUtils {
    public static final String KEY_ALGORITHM = "RSA";
    private static final String PUBLIC_KEY = "RSAPublicKey";
    private static final String PRIVATE_KEY = "RSAPrivateKey";
    private static RSAPublicKey publicKey = null;
    private static RSAPrivateKey privateKey = null;

    public static String getPublicKey(Map<String, Object> keyMap) throws Exception {
        Key key = (Key) keyMap.get(PUBLIC_KEY);
        return encryptBase64(key.getEncoded());
    }

    public static String getPrivateKey(Map<String, Object> keyMap) throws Exception {
        Key key = (Key) keyMap.get(PRIVATE_KEY);
        return encryptBase64(key.getEncoded());
    }

    public static byte[] decryptbase64(String key) throws Exception {
        return Base64.decodeBase64(key);
    }

    public static String encryptBase64(byte[] key) throws Exception {
        return Base64.encodeBase64String(key);
    }

    public static Map<String, Object> initKey() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        keyPairGen.initialize(2048);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        publicKey = (RSAPublicKey) keyPair.getPublic();
        privateKey = (RSAPrivateKey) keyPair.getPrivate();
        Map<String, Object> keyMap = new HashMap<>(2);
        keyMap.put(PUBLIC_KEY, publicKey);
        keyMap.put(PRIVATE_KEY, privateKey);
        return keyMap;
    }

    public static String encodeData(RSAPublicKey publicKey2, String data) throws Exception {
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(1, publicKey2);
        String outStr = Base64.encodeBase64String(cipher.doFinal(data.getBytes("UTF-8")));
        return outStr;
    }

    public static String decryptData(RSAPrivateKey privateKey2, String data) throws Exception {
        byte[] inputByte = Base64.decodeBase64(data.getBytes("UTF-8"));
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        cipher.init(2, privateKey2);
        return new String(cipher.doFinal(inputByte));
    }

    public static RSAPublicKey getPublicKey(String key) throws Exception {
        byte[] keyBytes = Base64.decodeBase64(key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        RSAPublicKey publicKey2 = (RSAPublicKey) keyFactory.generatePublic(keySpec);
        return publicKey2;
    }

    public static RSAPrivateKey getPrivateKey(String key) throws Exception {
        byte[] keyBytes = Base64.decodeBase64(key);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        RSAPrivateKey privateKey2 = (RSAPrivateKey) keyFactory.generatePrivate(keySpec);
        return privateKey2;
    }

    public static RSAPrivateKey getPrivateKey() throws Exception {
        return privateKey;
    }

    public static RSAPublicKey getPublicKey() throws Exception {
        return publicKey;
    }
}
