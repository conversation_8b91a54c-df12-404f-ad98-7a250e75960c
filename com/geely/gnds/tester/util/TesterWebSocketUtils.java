package com.geely.gnds.tester.util;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.task.TesterVehicleTask;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import javax.websocket.RemoteEndpoint;
import javax.websocket.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

/* loaded from: TesterWebSocketUtils.class */
public class TesterWebSocketUtils {
    private static final Logger LOG = LoggerFactory.getLogger(TesterWebSocketUtils.class);
    private static CopyOnWriteArrayList<Session> vehicleQuerySessionList = new CopyOnWriteArrayList<>();

    private TesterWebSocketUtils() {
    }

    public static boolean hasVehicleQuerySession() {
        return !CollectionUtils.isEmpty(vehicleQuerySessionList);
    }

    public static void sendVehicleQueryDataToAll(Result<List<VehicleDto>> query, List list2) {
        if (!CollectionUtils.isEmpty(vehicleQuerySessionList)) {
            Iterator<Session> it = vehicleQuerySessionList.iterator();
            while (it.hasNext()) {
                Session session = it.next();
                if (session != null) {
                    try {
                        Map<String, List<String>> requestParameterMap = session.getRequestParameterMap();
                        if (requestParameterMap != null) {
                            TokenService tokenService = (TokenService) SpringContextUtils.getBean(TokenService.class);
                            String header = tokenService.getHeader();
                            List<String> list = requestParameterMap.get(header);
                            if (!CollectionUtils.isEmpty(list)) {
                                LoginUser loginUser = tokenService.H(list.get(0));
                                String username = loginUser.getUsername();
                                SingletonManager manager = SingletonManager.getInstance();
                                Set<String> managerVehicles = manager.getVehicles(username);
                                if (!CollectionUtils.isEmpty(managerVehicles)) {
                                    Iterator<VehicleDto> iterator = query.getData().iterator();
                                    while (iterator.hasNext()) {
                                        VehicleDto vehicleDto = iterator.next();
                                        boolean connect = vehicleDto.getConnect().booleanValue();
                                        String vin = vehicleDto.getVin();
                                        if (connect && !managerVehicles.contains(vin)) {
                                            iterator.remove();
                                        }
                                    }
                                }
                            }
                        }
                        RemoteEndpoint.Async asyncRemote = session.getAsyncRemote();
                        String data = ObjectMapperUtils.obj2JsonStr(query);
                        LOG.info("搜索车辆websocket开始推送vin:{}", list2);
                        asyncRemote.sendText(data);
                    } catch (Exception e) {
                    }
                }
            }
        }
    }

    public static void sendVehicleQueryData(String data, Session session) {
        if (session != null) {
            RemoteEndpoint.Async asyncRemote = session.getAsyncRemote();
            asyncRemote.sendText(data);
        }
    }

    public static void addVehicleQuerySession(Session session) {
        if (session != null) {
            vehicleQuerySessionList.add(session);
            Result<Object> result = new Result<>();
            result.ok(TesterVehicleTask.getSendData());
            sendVehicleQueryData(JSONObject.toJSONString(result), session);
        }
    }

    public static void closeVehicleQuerySession(Session session) {
        if (session != null) {
            vehicleQuerySessionList.remove(session);
        }
    }
}
