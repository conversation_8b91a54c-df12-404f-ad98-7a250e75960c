package com.geely.gnds.tester.util;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import jdk.nashorn.internal.objects.NativeString;

/* loaded from: SecurityUtils.class */
public class SecurityUtils {
    private static final String HEX = "0123456789ABCDEF";
    private static byte[] K1;
    private static byte[] K2;
    private static byte[] const_Rb = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -121};

    public static List<String> string2Hex(String fixByte, int i) {
        List<String> list = new ArrayList<>();
        int lengh = fixByte.length();
        for (int index = 0; index < lengh / ConstantEnum.TWO.intValue(); index++) {
            String childStr = NativeString.substring(fixByte, index * i, (index + 1) * i);
            list.add(childStr);
        }
        return list;
    }

    public static List<String> string1Hex(String fixByte) {
        List<String> list = new ArrayList<>();
        int lengh = fixByte.length();
        for (int index = 0; index < lengh; index++) {
            String childStr = NativeString.substring(fixByte, index, index + 1);
            list.add(childStr);
        }
        return list;
    }

    public static String securityAccess(String fixByte, String seed) {
        System.out.println("安全算法入参：fixByte->" + fixByte + ";seed->" + seed);
        String c1 = "";
        String c2 = "";
        String c3 = "";
        String c4 = "";
        String c5 = "";
        String c6 = "";
        String c7 = "";
        String c8 = "";
        String c9 = "";
        String c10 = "";
        String c11 = "";
        String c12 = "";
        String c13 = "";
        String c14 = "";
        String c15 = "";
        String c16 = "";
        String c17 = "";
        String c18 = "";
        String c19 = "";
        String c20 = "";
        String c21 = "";
        String c22 = "";
        String c23 = "";
        String c24 = "";
        List<String> seedByte = string2Hex(seed, 2);
        List<String> fixByteList = string2Hex(fixByte, 2);
        String s1 = hexStrToBinaryStr(seedByte.get(0));
        String s2 = hexStrToBinaryStr(seedByte.get(1));
        String s3 = hexStrToBinaryStr(seedByte.get(2));
        String f1 = hexStrToBinaryStr(fixByteList.get(0));
        String f2 = hexStrToBinaryStr(fixByteList.get(1));
        String f3 = hexStrToBinaryStr(fixByteList.get(2));
        String f4 = hexStrToBinaryStr(fixByteList.get(3));
        String f5 = hexStrToBinaryStr(fixByteList.get(4));
        String initial = hexStrToBinaryStr("C541A9");
        String challengeBits = f5 + f4 + f3 + f2 + f1 + s3 + s2 + s1;
        String a = initial;
        for (int i = 0; i < 64; i++) {
            int aTemp = Integer.parseInt(a.substring(23, 24)) ^ Integer.parseInt(challengeBits.substring(63 - i, 64 - i));
            String temp = a.substring(0, 23);
            List<String> temp2 = string1Hex(temp);
            temp2.add(0, aTemp + "");
            c24 = temp2.get(0);
            c23 = temp2.get(1);
            c22 = temp2.get(2);
            c21 = (Integer.parseInt(temp2.get(3)) ^ Integer.parseInt(temp2.get(0))) + "";
            c20 = temp2.get(4);
            c19 = temp2.get(5);
            c18 = temp2.get(6);
            c17 = temp2.get(7);
            c16 = (Integer.parseInt(temp2.get(8)) ^ Integer.parseInt(temp2.get(0))) + "";
            c15 = temp2.get(9);
            c14 = temp2.get(10);
            c13 = (Integer.parseInt(temp2.get(11)) ^ Integer.parseInt(temp2.get(0))) + "";
            c12 = temp2.get(12);
            c11 = temp2.get(13);
            c10 = temp2.get(14);
            c9 = temp2.get(15);
            c8 = temp2.get(16);
            c7 = temp2.get(17);
            c6 = (Integer.parseInt(temp2.get(18)) ^ Integer.parseInt(temp2.get(0))) + "";
            c5 = temp2.get(19);
            c4 = (Integer.parseInt(temp2.get(20)) ^ Integer.parseInt(temp2.get(0))) + "";
            c3 = temp2.get(21);
            c2 = temp2.get(22);
            c1 = temp2.get(23);
            a = c24 + c23 + c22 + c21 + c20 + c19 + c18 + c17 + c16 + c15 + c14 + c13 + c12 + c11 + c10 + c9 + c8 + c7 + c6 + c5 + c4 + c3 + c2 + c1;
        }
        String responsebyte01 = c12 + c11 + c10 + c9 + c8 + c7 + c6 + c5;
        String responsebyte02 = c16 + c15 + c14 + c13 + c24 + c23 + c22 + c21;
        String responsebyte03 = c4 + c3 + c2 + c1 + c20 + c19 + c18 + c17;
        String result = binaryStrToHexStr(responsebyte01) + binaryStrToHexStr(responsebyte02) + binaryStrToHexStr(responsebyte03);
        return result.toUpperCase();
    }

    public static String hexStrToBinaryStr(String hexString) {
        String binaryStr;
        if (hexString == null || "".equals(hexString)) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < hexString.length(); i++) {
            String indexStr = hexString.substring(i, i + 1);
            String binaryString = Integer.toBinaryString(Integer.parseInt(indexStr, 16));
            while (true) {
                binaryStr = binaryString;
                if (binaryStr.length() < ConstantEnum.FORE.intValue()) {
                    binaryString = "0" + binaryStr;
                }
            }
            sb.append(binaryStr);
        }
        return sb.toString();
    }

    public static String binaryStrToHexStr(String binaryStr) {
        if (binaryStr == null || "".equals(binaryStr) || binaryStr.length() % ConstantEnum.FORE.intValue() != 0) {
            return null;
        }
        StringBuffer sbs = new StringBuffer();
        for (int i = 0; i < binaryStr.length() / ConstantEnum.FORE.intValue(); i++) {
            String subStr = binaryStr.substring(i * 4, (i * 4) + 4);
            String hexStr = Integer.toHexString(Integer.parseInt(subStr, 2));
            sbs.append(hexStr);
        }
        return sbs.toString();
    }

    public static char getCrc(int size, byte[] data, char crc) {
        char[] crctab = {0, 4129, 8258, 12387, 16516, 20645, 24774, 28903, 33032, 37161, 41290, 45419, 49548, 53677, 57806, 61935, 4657, 528, 12915, 8786, 21173, 17044, 29431, 25302, 37689, 33560, 45947, 41818, 54205, 50076, 62463, 58334, 9314, 13379, 1056, 5121, 25830, 29895, 17572, 21637, 42346, 46411, 34088, 38153, 58862, 62927, 50604, 54669, 13907, 9842, 5649, 1584, 30423, 26358, 22165, 18100, 46939, 42874, 38681, 34616, 63455, 59390, 55197, 51132, 18628, 22757, 26758, 30887, 2112, 6241, 10242, 14371, 51660, 55789, 59790, 63919, 35144, 39273, 43274, 47403, 23285, 19156, 31415, 27286, 6769, 2640, 14899, 10770, 56317, 52188, 64447, 60318, 39801, 35672, 47931, 43802, 27814, 31879, 19684, 23749, 11298, 15363, 3168, 7233, 60846, 64911, 52716, 56781, 44330, 48395, 36200, 40265, 32407, 28342, 24277, 20212, 15891, 11826, 7761, 3696, 65439, 61374, 57309, 53244, 48923, 44858, 40793, 36728, 37256, 33193, 45514, 41451, 53516, 49453, 61774, 57711, 4224, 161, 12482, 8419, 20484, 16421, 28742, 24679, 33721, 37784, 41979, 46042, 49981, 54044, 58239, 62302, 689, 4752, 8947, 13010, 16949, 21012, 25207, 29270, 46570, 42443, 38312, 34185, 62830, 58703, 54572, 50445, 13538, 9411, 5280, 1153, 29798, 25671, 21540, 17413, 42971, 47098, 34713, 38840, 59231, 63358, 50973, 55100, 9939, 14066, 1681, 5808, 26199, 30326, 17941, 22068, 55628, 51565, 63758, 59695, 39368, 35305, 47498, 43435, 22596, 18533, 30726, 26663, 6336, 2273, 14466, 10403, 52093, 56156, 60223, 64286, 35833, 39896, 43963, 48026, 19061, 23124, 27191, 31254, 2801, 6864, 10931, 14994, 64814, 60687, 56684, 52557, 48554, 44427, 40424, 36297, 31782, 27655, 23652, 19525, 15522, 11395, 7392, 3265, 61215, 65342, 53085, 57212, 44955, 49082, 36825, 40952, 28183, 32310, 20053, 24180, 11923, 16050, 3793, 7920};
        for (int i = 0; i < size; i++) {
            int tmp = (crc >> '\b') ^ (((char) data[i]) & 255);
            crc = (char) ((crc << '\b') ^ crctab[tmp]);
        }
        return crc;
    }

    public static byte[] getCrc2(int size, byte[] data) {
        char[] crctab = {0, 4129, 8258, 12387, 16516, 20645, 24774, 28903, 33032, 37161, 41290, 45419, 49548, 53677, 57806, 61935, 4657, 528, 12915, 8786, 21173, 17044, 29431, 25302, 37689, 33560, 45947, 41818, 54205, 50076, 62463, 58334, 9314, 13379, 1056, 5121, 25830, 29895, 17572, 21637, 42346, 46411, 34088, 38153, 58862, 62927, 50604, 54669, 13907, 9842, 5649, 1584, 30423, 26358, 22165, 18100, 46939, 42874, 38681, 34616, 63455, 59390, 55197, 51132, 18628, 22757, 26758, 30887, 2112, 6241, 10242, 14371, 51660, 55789, 59790, 63919, 35144, 39273, 43274, 47403, 23285, 19156, 31415, 27286, 6769, 2640, 14899, 10770, 56317, 52188, 64447, 60318, 39801, 35672, 47931, 43802, 27814, 31879, 19684, 23749, 11298, 15363, 3168, 7233, 60846, 64911, 52716, 56781, 44330, 48395, 36200, 40265, 32407, 28342, 24277, 20212, 15891, 11826, 7761, 3696, 65439, 61374, 57309, 53244, 48923, 44858, 40793, 36728, 37256, 33193, 45514, 41451, 53516, 49453, 61774, 57711, 4224, 161, 12482, 8419, 20484, 16421, 28742, 24679, 33721, 37784, 41979, 46042, 49981, 54044, 58239, 62302, 689, 4752, 8947, 13010, 16949, 21012, 25207, 29270, 46570, 42443, 38312, 34185, 62830, 58703, 54572, 50445, 13538, 9411, 5280, 1153, 29798, 25671, 21540, 17413, 42971, 47098, 34713, 38840, 59231, 63358, 50973, 55100, 9939, 14066, 1681, 5808, 26199, 30326, 17941, 22068, 55628, 51565, 63758, 59695, 39368, 35305, 47498, 43435, 22596, 18533, 30726, 26663, 6336, 2273, 14466, 10403, 52093, 56156, 60223, 64286, 35833, 39896, 43963, 48026, 19061, 23124, 27191, 31254, 2801, 6864, 10931, 14994, 64814, 60687, 56684, 52557, 48554, 44427, 40424, 36297, 31782, 27655, 23652, 19525, 15522, 11395, 7392, 3265, 61215, 65342, 53085, 57212, 44955, 49082, 36825, 40952, 28183, 32310, 20053, 24180, 11923, 16050, 3793, 7920};
        char crc = 65535;
        for (int i = 0; i < size; i++) {
            int tmp = (crc >> '\b') ^ (((char) data[i]) & 255);
            crc = (char) ((crc << '\b') ^ crctab[tmp]);
        }
        byte[] crcarray = {(byte) ((char) (crc >> '\b')), (byte) ((char) (crc & 255))};
        return crcarray;
    }

    public static byte[] calcCrc(char crc) {
        byte[] crcarray = {(byte) ((char) (crc >> '\b')), (byte) ((char) (crc & 255))};
        return crcarray;
    }

    public static void main(String[] args) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException, InvalidAlgorithmParameterException {
        char[] maskChar = {'+', '~', 21, 22, '(', 174, 210, 166, 171, 247, 21, 136, '\t', 207, 'O', '<'};
        char[] seedChar = {'k', 193, 190, 226, '.', '@', 159, 150, 233, '=', '~', 17, 's', 147, 23, '*'};
        byte[] maskByteArray = new byte[16];
        byte[] seedByteArray = new byte[16];
        for (int i = 0; i < 16; i++) {
            maskByteArray[i] = (byte) maskChar[i];
            seedByteArray[i] = (byte) seedChar[i];
        }
        String keyByteArray2 = cmac("e8d8e7850676dfa0446f0f7480c64398", "f8d280544b00dc7e8d3f6c17fc3e7261");
        System.out.print(keyByteArray2);
        System.out.println("");
    }

    public static String cmac(String mask, String seed) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException, InvalidAlgorithmParameterException {
        byte[] key = new byte[16];
        byte[] data = new byte[16];
        List<String> maskByte = string2Hex(mask, 2);
        List<String> seedByte = string2Hex(seed, 2);
        for (int i = 0; i < 16; i++) {
            key[i] = (byte) Integer.parseInt(maskByte.get(i), 16);
            data[i] = (byte) Integer.parseInt(seedByte.get(i), 16);
        }
        generateSubkey(key);
        byte[] mLast = xor128(data, K1);
        byte[] x = new byte[16];
        xor128(x, data);
        byte[] y = xor128(x, mLast);
        byte[] encResult = aesEncrypt(key, y);
        byte[] hashValue = new byte[16];
        System.arraycopy(encResult, encResult.length - hashValue.length, hashValue, 0, hashValue.length);
        return toHex(hashValue);
    }

    private static byte[] aesEncrypt(byte[] keys, byte[] data) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException, InvalidAlgorithmParameterException {
        try {
            SecretKey key = new SecretKeySpec(keys, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            IvParameterSpec ivps = new IvParameterSpec(new byte[16]);
            cipher.init(1, key, ivps);
            byte[] byteAes = cipher.doFinal(data);
            return byteAes;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String toHex(byte[] buf) {
        if (buf == null) {
            return "";
        }
        StringBuffer result = new StringBuffer(2 * buf.length);
        for (byte b : buf) {
            appendHex(result, b);
        }
        return result.toString();
    }

    private static void appendHex(StringBuffer sb, byte b) {
        sb.append(HEX.charAt((b >> 4) & 15)).append(HEX.charAt(b & 15));
    }

    private static byte[] xor128(byte[] a, byte[] b) {
        byte[] out = new byte[16];
        for (int i = 0; i < 16; i++) {
            out[i] = (byte) (a[i] ^ b[i]);
        }
        return out;
    }

    private static byte[] leftshiftOnebit(byte[] input) {
        char c;
        char overflow = 0;
        byte[] output = new byte[16];
        for (int i = 15; i >= 0; i--) {
            output[i] = (byte) (input[i] << 1);
            int i2 = i;
            output[i2] = (byte) (output[i2] | overflow);
            if ((input[i] & 128) == 128) {
                c = 1;
            } else {
                c = 0;
            }
            overflow = c;
        }
        return output;
    }

    private static void generateSubkey(byte[] key) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException, InvalidAlgorithmParameterException {
        byte[] l = aesEncrypt(key, new byte[16]);
        if ((l[0] & 128) == 0) {
            K1 = leftshiftOnebit(l);
        } else {
            byte[] tmp = leftshiftOnebit(l);
            K1 = xor128(tmp, const_Rb);
        }
        if ((K1[0] & 128) == 0) {
            K2 = leftshiftOnebit(K1);
        } else {
            byte[] tmp2 = leftshiftOnebit(K1);
            K2 = xor128(tmp2, const_Rb);
        }
    }
}
