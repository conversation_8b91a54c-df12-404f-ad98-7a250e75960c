package com.geely.gnds.tester.util;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: LockUtil.class */
public class LockUtil {
    private static Map<String, LockInfo> lockMap = new HashMap();
    private static Logger logger = LoggerFactory.getLogger(LockUtil.class);
    private static Lock myLock = new ReentrantLock();

    /* loaded from: LockUtil$LockInfo.class */
    private static class LockInfo {
        Lock lock;
        int count;

        private LockInfo() {
        }
    }

    public static void lock(String key) {
        myLock.lock();
        LockInfo lockInfo = lockMap.get(key);
        if (lockInfo == null) {
            lockInfo = new LockInfo();
            lockInfo.lock = new ReentrantLock();
            lockMap.put(key, lockInfo);
        }
        lockInfo.count++;
        myLock.unlock();
        lockInfo.lock.lock();
    }

    public static void unlock(String key) {
        myLock.lock();
        LockInfo lockInfo = lockMap.get(key);
        if (lockInfo != null) {
            lockInfo.lock.unlock();
            lockInfo.count--;
            if (lockInfo.count == 0) {
                lockMap.remove(key);
            }
        }
        myLock.unlock();
    }

    public static boolean tryLock(String key) {
        myLock.lock();
        LockInfo lockInfo = lockMap.get(key);
        if (lockInfo == null) {
            lockInfo = new LockInfo();
            lockInfo.lock = new ReentrantLock();
            lockMap.put(key, lockInfo);
        }
        lockInfo.count++;
        myLock.unlock();
        return lockInfo.lock.tryLock();
    }
}
