package com.geely.gnds.tester.task;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.dto.VehicleStatusDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.ConnectedService;
import com.geely.gnds.tester.service.SeqService;
import com.geely.gnds.tester.service.SynchronousService;
import com.geely.gnds.tester.socket.VehicleStatusReadWebSocket;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterWebSocketUtils;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import com.google.common.util.concurrent.RateLimiter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import javax.websocket.RemoteEndpoint;
import javax.websocket.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
/* loaded from: TesterVehicleTask.class */
public class TesterVehicleTask {

    @Autowired
    private ConnectedService hr;

    @Autowired
    private Cloud cloud;

    @Autowired
    private SynchronousService hn;

    @Autowired
    private SeqService ek;
    private RateLimiter limiter = RateLimiter.create(0.5d);
    private RateLimiter fi = RateLimiter.create(0.1d);
    private static final Logger LOG = LoggerFactory.getLogger(TesterVehicleTask.class);
    private static CopyOnWriteArrayList<VehicleDto> pI = new CopyOnWriteArrayList<>();

    public static String getSendData() {
        if (CollectionUtils.isEmpty(pI)) {
            return "[]";
        }
        return ObjectMapperUtils.obj2JsonStr(pI);
    }

    @Scheduled(fixedDelay = 100)
    public void aA() throws InterruptedException {
        Result<List<VehicleDto>> result = new Result<>();
        if (TesterWebSocketUtils.hasVehicleQuerySession()) {
            try {
                Set<String> loginUsers = TesterLoginUtils.getLoginUsers();
                String username = "";
                Iterator<String> iterator = loginUsers.iterator();
                if (iterator.hasNext()) {
                    username = iterator.next();
                }
                LOG.info("搜索附近车辆 定时任务搜索，使用用户名：{}", username);
                List<VehicleDto> query = this.hr.queryByTask(username);
                List list = new ArrayList();
                for (VehicleDto vehicleDto : query) {
                    list.add(vehicleDto.getVin());
                }
                LOG.info("搜索附近车辆 定时任务搜索结果", list);
                if (!CollectionUtils.isEmpty(query)) {
                    if (pI.size() != query.size() || !pI.containsAll(query)) {
                        TesterWebSocketUtils.sendVehicleQueryDataToAll(result.ok(query), list);
                        pI.clear();
                        pI.addAll(query);
                    }
                } else {
                    pI.clear();
                    TesterWebSocketUtils.sendVehicleQueryDataToAll(result.ok(query), list);
                }
                return;
            } catch (UnAuthException e) {
                result.setCode(HttpStatus.UNAUTHORIZED);
                result.setData(new ArrayList<>());
                TesterWebSocketUtils.sendVehicleQueryDataToAll(result, new ArrayList());
                return;
            } catch (Exception e2) {
                LOG.error("搜索车辆定时任务执行过程出错", e2);
                return;
            }
        }
        LOG.info("queryVehicleTask--当前无用户连接");
        try {
            Thread.sleep(4000L);
        } catch (InterruptedException e3) {
            e3.printStackTrace();
        }
    }

    @Scheduled(fixedDelay = 1200000)
    public void aB() {
        Set<String> loginUsers = TesterLoginUtils.getLoginUsers();
        if (!CollectionUtils.isEmpty(loginUsers)) {
            try {
                for (String username : loginUsers) {
                    ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                    LOG.info("用户" + username + ",云端token续命");
                    this.hn.synchronousUsers(username);
                }
            } catch (Exception e) {
                LOG.error("云端token续命出错", e);
            }
        }
    }

    @Scheduled(fixedDelay = 100)
    public void w() throws InterruptedException {
        if (!this.limiter.tryAcquire()) {
            try {
                Thread.sleep(500L);
                return;
            } catch (InterruptedException e) {
                e.printStackTrace();
                return;
            }
        }
        if (VehicleStatusReadWebSocket.v()) {
            CopyOnWriteArrayList<Session> vehicleStatusReadSessionList = VehicleStatusReadWebSocket.getVehicleStatusReadSessionList();
            InitializeUiDto initializeUiDto = new InitializeUiDto();
            initializeUiDto.setType(ConstantEnum.STATUS_READOUT);
            Iterator<Session> it = vehicleStatusReadSessionList.iterator();
            while (it.hasNext()) {
                Session session = it.next();
                Map<String, List<String>> paramMap = session.getRequestParameterMap();
                List<String> vins = paramMap.get("vin");
                if (!CollectionUtils.isEmpty(vins)) {
                    String vin = vins.get(0);
                    initializeUiDto.setVin(vin);
                    VehicleStatusDto vehicleStatusDto = this.ek.statusReadout(initializeUiDto);
                    LOG.info("执行诊断序列statusReadout接口出参----->{}", vehicleStatusDto);
                    RemoteEndpoint.Async asyncRemote = session.getAsyncRemote();
                    String data = ObjectMapperUtils.obj2JsonStr(vehicleStatusDto);
                    asyncRemote.sendText(data);
                }
            }
            return;
        }
        if (this.fi.tryAcquire()) {
            LOG.info("statusReadTask--当前无车辆连接");
        }
    }
}
