package com.geely.gnds.tester.task;

import com.geely.gnds.doip.client.pcap.PcapThreadGlobal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
/* loaded from: PcapGlobalTask.class */
public class PcapGlobalTask {
    private static final Logger logger = LoggerFactory.getLogger(PcapGlobalTask.class);

    @Autowired
    private PcapThreadGlobal pF;

    @Scheduled(initialDelay = 10000, fixedDelay = 2000)
    public void start() {
        this.pF.start();
    }
}
