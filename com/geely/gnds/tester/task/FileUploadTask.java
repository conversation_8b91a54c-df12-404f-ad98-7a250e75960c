package com.geely.gnds.tester.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.common.LogFileReslove;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.common.UploadCloudBean;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import java.io.File;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@Component
/* loaded from: FileUploadTask.class */
public class FileUploadTask {

    @Autowired
    private OssUtils ossUtils;

    @Value("${tester.tenantName}")
    private String tenantName;

    @Autowired
    private LogFileReslove logFileReslove;

    @Autowired
    private ThreadPoolTaskExecutor pE;
    private static final String RUN_LOG_DIR_PATH = StringUtils.cleanPath(FileUtil.file(AppConfig.getAppDataDir(), "applog").getAbsolutePath()) + File.separator;

    @Scheduled(initialDelay = 60000, fixedDelay = 60000)
    public void ay() {
        this.ossUtils.cronUpload();
    }

    @Scheduled(initialDelay = 600000, fixedDelay = 3600000)
    public void az() {
        this.ossUtils.uploadDro();
        this.logFileReslove.reslove();
    }

    public void cH(String testerCode) {
        Assert.hasText(testerCode, "客户端编号不能空");
        this.pE.execute(() -> {
            cI(testerCode);
        });
    }

    public void cI(String testerCode) {
        String keyPrefix = "log/" + this.tenantName + "/" + DateUtil.today() + "/system/" + testerCode + "/" + LogTypeEnum.SYS_PATH.getDirName() + "/";
        String ossInfoName = "system".concat("-").concat(DateUtil.today()).concat("-").concat("sys-info.log");
        UploadCloudBean bean1 = new UploadCloudBean("", "system", this.tenantName, RUN_LOG_DIR_PATH + "sys-info.log", keyPrefix + ossInfoName, testerCode, TestLogTypeEnum.run);
        this.ossUtils.uploadLogToCloud(bean1);
        String ossErrorName = "system".concat("-").concat(DateUtil.today()).concat("-").concat("sys-error.log");
        UploadCloudBean bean2 = new UploadCloudBean("", "system", this.tenantName, RUN_LOG_DIR_PATH + "sys-error.log", keyPrefix + ossErrorName, testerCode, TestLogTypeEnum.run);
        this.ossUtils.uploadLogToCloud(bean2);
        String ossWarnName = "system".concat("-").concat(DateUtil.today()).concat("-").concat("sys-warn.log");
        UploadCloudBean bean3 = new UploadCloudBean("", "system", this.tenantName, RUN_LOG_DIR_PATH + "sys-warn.log", keyPrefix + ossWarnName, testerCode, TestLogTypeEnum.run);
        this.ossUtils.uploadLogToCloud(bean3);
        String ossUserName = "system".concat("-").concat(DateUtil.today()).concat("-").concat("sys-client.log");
        UploadCloudBean bean4 = new UploadCloudBean("", "system", this.tenantName, RUN_LOG_DIR_PATH + "sys-client.log", keyPrefix + ossUserName, testerCode, TestLogTypeEnum.run);
        this.ossUtils.uploadLogToCloud(bean4);
    }
}
