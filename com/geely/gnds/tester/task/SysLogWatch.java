package com.geely.gnds.tester.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.watch.SimpleWatcher;
import cn.hutool.core.io.watch.WatchMonitor;
import cn.hutool.core.io.watch.WatchUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.common.UploadCloudBean;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import com.geely.gnds.tester.service.TesterConfigService;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.WatchEvent;
import java.util.Arrays;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
/* loaded from: SysLogWatch.class */
public class SysLogWatch implements InitializingBean {

    @Autowired
    private OssUtils ossUtils;

    @Value("${tester.tenantName}")
    private String tenantName;

    @Autowired
    private TesterConfigService cd;
    private static final Logger log = LoggerFactory.getLogger(SysLogWatch.class);
    private static final String RUN_LOG_DIR_PATH = OssUtils.getLogPath();
    private static final String pG = "(\\d{4}-\\d{2}-\\d{2})(_sys.+)";
    private static final String cf = "system";

    public void afterPropertiesSet() throws Exception {
        String watchPath = RUN_LOG_DIR_PATH.concat("backup");
        Arrays.asList("info", "error", "warn", "debug", "client").forEach(e -> {
            File dir = new File(watchPath, e);
            if (!FileUtil.isDirectory(dir)) {
                FileUtil.mkdir(dir);
            }
        });
        cK(watchPath);
    }

    private void cK(String dirPath) {
        WatchMonitor monitor = WatchUtil.createAll(dirPath, 2, new SimpleWatcher() { // from class: com.geely.gnds.tester.task.SysLogWatch.1
            public void onCreate(WatchEvent<?> event, Path currentPath) {
                try {
                    Optional.ofNullable(SysLogWatch.this.cd.getConfig()).ifPresent(cfg -> {
                        Object obj = event.context();
                        String filename = obj.toString();
                        if (StringUtils.endsWith(filename, ".ucp")) {
                            return;
                        }
                        String localPath = currentPath.toString().concat(File.separator).concat(filename);
                        if (FileUtil.isFile(localPath)) {
                            String day = ReUtil.get(SysLogWatch.pG, filename, 1);
                            String keyPrefix = "log/" + SysLogWatch.this.tenantName + "/" + ((String) Optional.ofNullable(day).filter(s -> {
                                return StrUtil.length(s) == 10;
                            }).orElse(DateUtil.today())) + "/" + SysLogWatch.cf + "/" + cfg.getTesterCode() + "/" + LogTypeEnum.SYS_PATH.getDirName() + "/";
                            SysLogWatch.log.info("增加的文件所在文件夹路径：{},增加文件的名称 {}", currentPath, obj);
                            try {
                                SysLogWatch.this.ossUtils.uploadLogToCloud(new UploadCloudBean("", SysLogWatch.cf, SysLogWatch.this.tenantName, localPath, keyPrefix + filename, cfg.getTesterCode(), TestLogTypeEnum.run));
                            } catch (Exception e) {
                                SysLogWatch.log.error("文件上传异常：{}", e.getMessage(), e);
                            }
                        }
                    });
                } catch (Exception e) {
                    SysLogWatch.log.error("系统日志监控失败，原因：{}", e.getMessage(), e);
                }
            }
        });
        monitor.setName("gnds-filewatch-task-sysLog");
        monitor.start();
    }
}
