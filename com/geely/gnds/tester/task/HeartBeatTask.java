package com.geely.gnds.tester.task;

import com.geely.gnds.tester.socket.GndsWebSocket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
/* loaded from: HeartBeatTask.class */
public class HeartBeatTask {
    private static final Logger LOG = LoggerFactory.getLogger(HeartBeatTask.class);

    @Scheduled(initialDelay = 10000, fixedDelay = 1000)
    public void I() {
        GndsWebSocket.I();
    }

    @Scheduled(initialDelay = 10000, fixedDelay = 3600000)
    public void aw() {
        GndsWebSocket.aw();
    }

    @Scheduled(initialDelay = 10000, fixedDelay = 1000)
    public void ax() {
        GndsWebSocket.ax();
    }
}
