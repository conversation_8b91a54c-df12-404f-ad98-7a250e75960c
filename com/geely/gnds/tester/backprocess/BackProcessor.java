package com.geely.gnds.tester.backprocess;

import cn.hutool.core.thread.ThreadUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.FdThread;
import java.util.Date;
import java.util.concurrent.ConcurrentLinkedQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: BackProcessor.class */
public abstract class BackProcessor<T> {
    private static final Logger logger = LoggerFactory.getLogger(BackProcessor.class);
    private ConcurrentLinkedQueue<BackData<T>> bl = new ConcurrentLinkedQueue<>();
    private long hE = 1000;
    private long hF = 500;
    private FdThread thread = null;
    private final String name;

    public abstract void a(BackData<T> backData);

    public void setProcessTime(long processTime) {
        this.hF = processTime;
    }

    public void setSleepTime(long sleepTime) {
        this.hE = sleepTime;
    }

    public void G() {
        if (this.thread != null) {
            this.thread.close();
            try {
                this.thread.join();
            } catch (Exception e) {
                logger.error(FdHelper.getExceptionAsString(e));
            }
        }
    }

    public BackProcessor(String tag, String name) {
        this.name = name;
    }

    public void a(Date time, T data) {
        this.bl.add(new BackData<>(time, data));
        if (this.thread != null) {
            this.thread.notify4Data();
        }
    }

    private void createInstance(String threadName) {
        this.thread = new FdThread(threadName) { // from class: com.geely.gnds.tester.backprocess.BackProcessor.1
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                BackProcessor.logger.info(BackProcessor.this.name + "启动完成。");
                while (isFdAlived()) {
                    BackData<T> data = (BackData) BackProcessor.this.bl.poll();
                    long startTime = System.currentTimeMillis() + BackProcessor.this.hF;
                    while (data != null) {
                        BackProcessor.this.a(data);
                        if (isFdAlived() && System.currentTimeMillis() > startTime) {
                            wait4BackProcessor(BackProcessor.this.hE);
                            startTime = System.currentTimeMillis() + BackProcessor.this.hF;
                        }
                        data = (BackData) BackProcessor.this.bl.poll();
                    }
                    if (isFdAlived()) {
                        wait4Data();
                    }
                }
                BackProcessor.logger.info(BackProcessor.this.name + "关闭。");
            }
        };
        this.thread.start();
        ThreadUtil.safeSleep(100L);
    }
}
