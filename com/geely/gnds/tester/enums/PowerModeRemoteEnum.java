package com.geely.gnds.tester.enums;

import com.geely.gnds.tester.util.MessageUtils;

/* loaded from: PowerModeRemoteEnum.class */
public enum PowerModeRemoteEnum {
    DORMANCY("休眠", "Abandoned"),
    NOT_WORKING("不工作", "Inactive"),
    CONVENIENCE("便利", "Convenience"),
    WORK("工作", "Active"),
    DRIVE("驾驶", "Driving"),
    CLOSED("闭合", "Closed"),
    OPEN("断开", "Open");

    private String value;
    private String valueEn;

    PowerModeRemoteEnum(String value, String valueEn) {
        this.value = value;
        this.valueEn = valueEn;
    }

    public String value() {
        return this.value;
    }

    public String valueEn() {
        return this.valueEn;
    }

    public static String co(String type) {
        for (PowerModeRemoteEnum powerModeRemoteEnum : values()) {
            if (powerModeRemoteEnum.value.equalsIgnoreCase(type) || powerModeRemoteEnum.valueEn.equalsIgnoreCase(type)) {
                return MessageUtils.getMessage(powerModeRemoteEnum.valueEn);
            }
        }
        return type;
    }
}
