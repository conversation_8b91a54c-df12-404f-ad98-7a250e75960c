package com.geely.gnds.tester.enums;

/* loaded from: ShowSystemNameEnum.class */
public enum ShowSystemNameEnum {
    GNDS_GEELY("GNDS-Geely", "GLDS2.0"),
    GNDS_GEELY_PRE("GNDS-Geely-Pre", "GLDS2.0-Pre"),
    GNDS_GEELY_TEST("GNDS-Geely-Test", "GLDS2.0-Test"),
    GNDS_GRI("GNDS-GRI", "GNDS"),
    GNDS_GRI_PRE("GNDS-GRI-Pre", "GNDS-Pre"),
    GNDS_GRI_TESE("GNDS-GRI-Test", "GNDS-Test"),
    GNDS_LYNKCO("GNDS-Lynkco", "MADS"),
    GNDS_LYNKCO_PRE("GNDS-Lynkco-Pre", "MADS-Pre"),
    GNDS_LYNKCO_TESE("GNDS-Lynkco-Test", "MADS-Test"),
    GNDS_GEELY_OG("GND<PERSON>-Geely-I", "GLDS2.0-I"),
    G<PERSON>S_GEELY_OG_PRE("GNDS-Geely-I-Pre", "GLDS2.0-I-Pre"),
    GNDS_GEELY_OG_TEST("GNDS-Geely-I-Test", "GLDS2.0-I-Test"),
    GNDS_LVDS("GNDS-LVDS", "LVDS"),
    GNDS_LVDS_PRE("GNDS-LVDS-Pre", "LVDS-Pre"),
    GNDS_LVDS_TEST("GNDS-LVDS-Test", "LVDS-Test");

    private String tenantName;
    private String nF;

    ShowSystemNameEnum(String tenantName, String showName) {
        this.tenantName = tenantName;
        this.nF = showName;
    }

    public String getTenantName() {
        return this.tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getShowName() {
        return this.nF;
    }

    public void setShowName(String showName) {
        this.nF = showName;
    }

    public static String cp(String tenantName) {
        if (null == tenantName) {
            return null;
        }
        if (tenantName.endsWith("-")) {
            tenantName = tenantName.substring(0, tenantName.length() - 1);
        }
        for (ShowSystemNameEnum value : values()) {
            if (value.getTenantName().equalsIgnoreCase(tenantName)) {
                return value.getShowName();
            }
        }
        return "";
    }
}
