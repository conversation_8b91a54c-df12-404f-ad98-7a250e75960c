package com.geely.gnds.tester.enums;

import com.geely.gnds.tester.util.MessageUtils;

/* loaded from: LogCollectionStatus.class */
public enum LogCollectionStatus {
    COMPRESS_LOG("日志采集中", "logCollection"),
    UPLOAD("上传中", "upload"),
    SUCCESS("日志上传成功", "Log upload succeed"),
    Fail("日志上传失败", "Log upload fail");

    private String value;
    private String valueEn;

    LogCollectionStatus(String value, String valueEn) {
        this.value = value;
        this.valueEn = valueEn;
    }

    public String getValue() {
        return MessageUtils.getMessage(this.valueEn);
    }
}
