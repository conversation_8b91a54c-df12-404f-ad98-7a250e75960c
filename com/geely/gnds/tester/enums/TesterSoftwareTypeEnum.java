package com.geely.gnds.tester.enums;

/* loaded from: TesterSoftwareTypeEnum.class */
public enum TesterSoftwareTypeEnum {
    SWK1("SWK1"),
    SWK2("SWK2"),
    <PERSON>W<PERSON>3("SWK3"),
    <PERSON><PERSON><PERSON>("SWLM"),
    SWT1("SWT1");

    private String value;

    TesterSoftwareTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public static boolean cq(String type) {
        if (SWT1.getValue().equals(type) || SWK1.getValue().equals(type) || SWK2.getValue().equals(type)) {
            return true;
        }
        return false;
    }
}
