package com.geely.gnds.tester.enums;

/* loaded from: GcidCheckStatusEnum.class */
public enum GcidCheckStatusEnum {
    NORMAL("正常", 0),
    EXCEPTION("异常", 1),
    NODATA("无数据", 2);

    private String value;
    private Integer status;

    GcidCheckStatusEnum(String value, Integer status) {
        this.value = value;
        this.status = status;
    }

    public String value() {
        return this.value;
    }

    public Integer ao() {
        return this.status;
    }
}
