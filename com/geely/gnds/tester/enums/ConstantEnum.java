package com.geely.gnds.tester.enums;

import com.geely.gnds.ruoyi.common.core.text.StrFormatter;

/* loaded from: ConstantEnum.class */
public class ConstantEnum {
    public static final Integer ZERO = 0;
    public static final Integer ONE = 1;
    public static final Integer TWO = 2;
    public static final Integer THREE = 3;
    public static final Integer FORE = 4;
    public static final Integer FIVE = 5;
    public static final Integer SIX = 6;
    public static final Integer SEVEN = 7;
    public static final Integer EIGHT = 8;
    public static final Integer NINE = 9;
    public static final Integer TEN = 10;
    public static final Integer ELEVEN = 11;
    public static final Integer THIRTEEN = 13;
    public static final Integer FIFTEEN = 15;
    public static final Integer SEVENTEEN = 17;
    public static final Integer MAX_NAME_LENGTH = 255;
    public static final Integer SIXTY_FIVE = 65;
    public static final Integer NINETY_ONE = 91;
    public static final Integer NINETY_SEVEN = 97;
    public static final Integer ONE_TWO_THREE = Integer.valueOf(StrFormatter.C_DELIM_START);
    public static final String COMMA = ",";
    public static final String POINT = ".";
    public static final String PARENT = "parent";
    public static final String CHILDREN = "children";
    public static final String EXT_ZIP = ".zip";
    public static final String EXT_EXCEL = ".xlsx";
    public static final String VBF = ".vbf";
    public static final String BVBF = ".VBF";
    public static final String JSON = ".json";
    public static final String XML = ".xml";
    public static final String NULL = "null";
    public static final String EMPTY = " ";
    public static final String LOCAL = "127.0.0.1";
    public static final String IP_ALL = "0.0.0.0";
    public static final String INFO_1904 = "1904_info";
    public static final String INFO_1906 = "1906_info";
    public static final String MSIE = "MSIE";
    public static final String FIREFOX = "Firefox";
    public static final String CHROME = "Chrome";
    public static final String UNKNOWN = "unknown";
    public static final String CLASS_PATH = "CLASS_PATH";
    public static final String LIMIT = "limit";
    public static final String PAGE = "page";
    public static final String OFFSET = "offset";
    public static final String STATUS_READOUT = "statusReadout";
    public static final String READ_OUT = "readOut";
    public static final String SOFT_UPDATE = "softUpdate";
    public static final String INDEX = "@<";
    public static final String STRING = "String";
    public static final String STRING_JSON = "String_JSON";
    public static final String INT = "Int";
    public static final String BYTE = "Byte";
    public static final String SHORT = "Short";
    public static final String LONG = "Long";
    public static final String FLOAT = "Float";
    public static final String DOUBLE = "Double";
    public static final String CHAR = "Char";
    public static final String BOOLEAN = "Boolean";
    public static final String LISTJAVA = "JavaList";
    public static final String MAP = "Map";
    public static final String JSON_OBJECT = "JSONObject";
    public static final String LIST = "List";
    public static final String CALL_MODULE = "CallModule";
    public static final String UDS_DATA = "UDS_Data";
    public static final String VAR_DECLARATION = "VarDeclaration";
    public static final String EVAL = "eval";
    public static final String WHILE = "While";
    public static final String FOR_EACH = "ForEach";
    public static final String FOR_EACH_JAVA = "ForEachJava";
    public static final String GET_SECURITY_KEY = "getSecurityKey";
    public static final String VBF_SWDL = "VBF_SWDL";
    public static final String SET_UI = "Set_UI";
    public static final String IF = "If";
    public static final String CALL_API = "CallAPI";
    public static final String SET_JSON_PROPERTY = "SetJsonProperty";
    public static final String GET_JSON_PROPERTY = "GetJsonProperty";
    public static final String LOGGER_XML = "Logger_XML";
    public static final String GET_VBF = "Get_vbf_info_by_BSS";
    public static final String RUN_SCRIPT = "RunScript";
    public static final String CALL_JAR = "CallJar";
    public static final String CALL_DLL = "CallDll";
    public static final String CONTINUE = "Continue";
    public static final String BREAK = "Break";
    public static final String RETURN = "Return";
    public static final String PARALLEL = "Parallel";
    public static final String BASE_UI = "Base_UI";
    public static final String STOP = "Stop";
    public static final String GET_UI_ATTRIBUTE = "Get_UI_Attribute";
    public static final String TRY = "Try";
    public static final String TRUE = "True";
    public static final String FALSE = "False";
    public static final String CHECK_PRECONDITION = "CheckPrecondition";
    public static final String COLON = ":";
    public static final String MOBILECODE_STR = "mobileCode";
    public static final String PRIVATEKEY_STR = "privateKey";
    public static final String TENANTCODE_STR = "tenantCode";
    public static final String USERNAME_STR = "username";
    public static final String WLAN = "wlan";
    public static final String SET_SEQ_RESULT = "Set_SeqResult";
    public static final String SET_ERROR = "Set_Error";
    public static final String ERROR_SOLUTION = "; 建议解决方案:";
    public static final String ERROR_SOLUTION_EN = "; Suggested solution:";
    public static final String SLEEP = "Sleep";
    public static final String GEEA2 = "GEEA2.0";
    public static final String GEEA3 = "GEEA3.0";
    public static final float POSITIVE_INFINITY = Float.POSITIVE_INFINITY;
    public static final float NEGATIVE_INFINITY = Float.NEGATIVE_INFINITY;
    public static final String LOG_HTML = "LOG_HTML";
    public static final String WIFI_SWITCH = "WIFI_Switch";

    private ConstantEnum() {
    }
}
