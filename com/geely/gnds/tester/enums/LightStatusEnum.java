package com.geely.gnds.tester.enums;

/* loaded from: LightStatusEnum.class */
public enum LightStatusEnum {
    <PERSON>(6),
    <PERSON>(5),
    <PERSON>(4),
    <PERSON>(3),
    <PERSON>(2),
    <PERSON>(1),
    <PERSON>(0);

    private int code;

    LightStatusEnum(int code) {
        this.code = code;
    }

    public static String getValue(int code) {
        LightStatusEnum[] lightStatusEnums = values();
        for (LightStatusEnum statusEnum : lightStatusEnums) {
            if (statusEnum.code == code) {
                return statusEnum.name();
            }
        }
        return "";
    }
}
