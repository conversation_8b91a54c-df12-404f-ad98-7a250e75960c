package com.geely.gnds.tester.enums;

import com.geely.gnds.ruoyi.common.constant.ScheduleConstants;
import com.geely.gnds.ruoyi.common.constant.UserConstants;
import com.geely.gnds.ruoyi.common.utils.MakeOrderNum;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import java.util.Calendar;

/* loaded from: ManufacturingYearEnum.class */
public enum ManufacturingYearEnum {
    CODE_1("1", 1),
    CODE_2(ScheduleConstants.MISFIRE_FIRE_AND_PROCEED, 2),
    CODE_3(ScheduleConstants.MISFIRE_DO_NOTHING, 3),
    CODE_4("4", 4),
    CODE_5("5", 5),
    CODE_6("6", 6),
    CODE_7("7", 7),
    CODE_8("8", 8),
    CODE_9("9", 9),
    CODE_A("A", 10),
    CODE_B("B", 11),
    CODE_C(UserConstants.TYPE_MENU, 12),
    CODE_D("D", 13),
    CODE_E("E", 14),
    CODE_F(UserConstants.TYPE_BUTTON, 15),
    CODE_G("G", 16),
    CODE_H("H", 17),
    CODE_J("J", 18),
    CODE_K("K", 19),
    CODE_L("L", 20),
    CODE_M(UserConstants.TYPE_DIR, 21),
    CODE_N("N", 22),
    CODE_P("P", 23),
    CODE_R(MakeOrderNum.RECHARGE_PREFIX, 24),
    CODE_S("S", 25),
    CODE_T("T", 26),
    CODE_V("V", 27),
    CODE_W("W", 28),
    CODE_X("X", 29),
    CODE_Y(UserConstants.YES, 30);

    private String code;
    private Integer sort;

    ManufacturingYearEnum(String code, Integer sort) {
        this.code = code;
        this.sort = sort;
    }

    private static Integer cm(String code) {
        ManufacturingYearEnum[] lightStatusEnums = values();
        for (ManufacturingYearEnum manufacturingYearEnum : lightStatusEnums) {
            if (manufacturingYearEnum.code.equals(code)) {
                return manufacturingYearEnum.sort;
            }
        }
        return null;
    }

    public static String cn(String code) {
        Integer sort;
        if (StringUtils.isEmpty(code) || (sort = cm(code)) == null) {
            return "";
        }
        Calendar calendar = Calendar.getInstance();
        int now = calendar.get(1);
        int diff = ((now % 2000) % 30) - sort.intValue();
        int manufacturingYear = Math.abs(diff) < 15 ? now - diff : diff <= -15 ? (now - diff) - 30 : (now - diff) + 30;
        return String.valueOf(manufacturingYear);
    }
}
