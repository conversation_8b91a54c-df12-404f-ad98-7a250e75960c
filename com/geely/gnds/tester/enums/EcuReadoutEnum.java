package com.geely.gnds.tester.enums;

/* loaded from: EcuReadoutEnum.class */
public enum EcuReadoutEnum {
    SERIAL_NUMBER("序列号", "F18C", "Serial Number"),
    ASSEMBLY_NUMBER("总成号", "F1<PERSON>", "Delivery Assembly Part No"),
    HARDWARE_NUMBER("硬件号", "F1AA", "Hardware Number"),
    DIAGNOSIS_NUMBER("诊断编号", "F1A0", "Diagnostic Number"),
    SOFTWARE_NUMBER("软件编号", "F1AE", "Software Number");

    private String value;
    private String key;
    private String lR;

    EcuReadoutEnum(String value, String key, String valueEu) {
        this.key = key;
        this.value = value;
        this.lR = valueEu;
    }

    public String am() {
        return this.key;
    }

    public String value() {
        return this.value;
    }

    public String an() {
        return this.lR;
    }
}
