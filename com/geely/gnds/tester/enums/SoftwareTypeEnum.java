package com.geely.gnds.tester.enums;

import org.apache.commons.lang3.ObjectUtils;

/* loaded from: SoftwareTypeEnum.class */
public enum SoftwareTypeEnum {
    CURRENT_BSS("CurrentBss", 5),
    CURRENT_BSS_CMA("CurrentBssCMA", 6),
    TARGET_BSS("TargetBss", 7),
    INPUT_BSS("inputBss", 8),
    TEST("Test", 1),
    MRD("MRD", 2),
    CRB("CRB", 3),
    PUBLISHED("Published", 4);

    private String value;
    private Integer status;

    SoftwareTypeEnum(String value, Integer status) {
        this.value = value;
        this.status = status;
    }

    public String getValue() {
        return this.value;
    }

    public Integer getStatus() {
        return this.status;
    }

    public static SoftwareTypeEnum e(Object status) throws NumberFormatException {
        if (ObjectUtils.isEmpty(status)) {
            return null;
        }
        try {
            Integer parse = Integer.valueOf(status.toString());
            for (SoftwareTypeEnum softwareTypeEnum : values()) {
                if (softwareTypeEnum.getStatus().equals(parse)) {
                    return softwareTypeEnum;
                }
            }
            return null;
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
