package com.geely.gnds.tester.enums;

import com.geely.gnds.ruoyi.common.constant.ScheduleConstants;

/* loaded from: SequenceStatusEnum.class */
public enum SequenceStatusEnum {
    DRAFT("草稿", "1"),
    REPUBLISHED("预发布", ScheduleConstants.MISFIRE_FIRE_AND_PROCEED),
    PUBLISHED("发布", ScheduleConstants.MISFIRE_DO_NOTHING),
    OBSOLETE("废弃", "-1");

    private String value;
    private String status;

    SequenceStatusEnum(String value, String status) {
        this.value = value;
        this.status = status;
    }

    public String value() {
        return this.value;
    }

    public String ap() {
        return this.status;
    }
}
