package com.geely.gnds.tester.enums;

import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.util.MessageUtils;
import org.apache.commons.lang3.StringUtils;

/* loaded from: TesterErrorCodeEnum.class */
public enum TesterErrorCodeEnum {
    S50016("S50016", "UDS模块返回代码0x10 - 普通的拒绝指令", "UDS_NRC_0x10 - A common refusal order"),
    S50017("S50017", "UDS模块返回代码0x11 - 无法识别服务码", "UDS_NRC_0x11 - The service code was not recognized"),
    S50018("S50018", "UDS模块返回代码0x12 - 无法识别服务码的子功能", "UDS_NRC_0x12 - The subfunction of the service code is not recognized"),
    S50019("S50019", "UDS模块返回代码0x13 - 错误的数据报格式或者长度", "UDS_NRC_0x13 - Incorrect datagram format or length"),
    S50020("S50020", "UDS模块返回代码0x14 - 过长的数据返回", "UDS_NRC_0x14 - Too long data is returned"),
    S50033("S50033", "UDS模块返回代码0x21 - 过忙需重复请求", "UDS_NRC_0x21 - Repeat the request if too busy"),
    S50034("S50034", "UDS模块返回代码0x22 - 条件不满足", "UDS_NRC_0x22 - Condition not met"),
    S50036("S50036", "UDS模块返回代码0x24 - 发送的顺序不对", "UDS_NRC_0x24 - They are sent in the wrong order"),
    S50037("S50037", "UDS模块返回代码0x25 - 处理的子网无法响应", "UDS_NRC_0x25 - The processing subnet cannot respond"),
    S50038("S50038", "UDS模块返回代码0x26 - DTC出现了错误的记录", "UDS_NRC_0x26 - The DTC has an incorrect record"),
    S50049("S50049", "UDS模块返回代码0x31 - 请求超出支持范围", "UDS_NRC_0x31 - The request is out of support"),
    S50051("S50051", "UDS模块返回代码0x33 - 安全访问被拒绝", "UDS_NRC_0x33 - Security access was denied. Procedure"),
    S50053("S50053", "UDS模块返回代码0x35 - 错误的访问key", "UDS_NRC_0x35 - Incorrect access key. Procedure"),
    S50054("S50054", "UDS模块返回代码0x36 - 超出了错误的安全访问次数", "UDS_NRC_0x36 - The number of incorrect security access attempts exceeded"),
    S50055("S50055", "UDS模块返回代码0x37 - 所需延时未到期", "UDS_NRC_0x37 - The required delay has not expired"),
    S50112("S50112", "UDS模块返回代码0x70 - 不支持上传及下载", "UDS_NRC_0x70 - Uploading and downloading are not supported"),
    S50113("S50113", "UDS模块返回代码0x71 - 传输数据中断", "UDS_NRC_0x71 - Transmission data interruption"),
    S50114("S50114", "UDS模块返回代码0x72 - 一般编程错误", "UDS_NRC_0x72 - General programming error"),
    S50115("S50115", "UDS模块返回代码0x73 - 错误的块计数器", "UDS_NRC_0x73 - Incorrect block counter"),
    S50120("S50120", "UDS模块返回代码0x78 - 等待响应", "UDS_NRC_0x78 - Waiting for a response"),
    S50126("S50126", "UDS模块返回代码0x7E - 当前会话中不支持子功能", "UDS_NRC_0x7E - Subfunctions are not supported in the current session"),
    S50127("S50127", "UDS模块返回代码0x7F - 当前会话中不支持服务", "UDS_NRC_0x7F - Services are not supported in the current session"),
    S50129("S50129", "UDS模块返回代码0x81 - 编程管理地址过高", "UDS_NRC_0x81 - The programming management address is too high"),
    S50130("S50130", "UDS模块返回代码0x82 - 编程管理地址过低", "UDS_NRC_0x82 - The programming management address is too low. Procedure"),
    S50131("S50131", "UDS模块返回代码0x83 - 引擎正在运行", "UDS_NRC_0x83 - The engine is running"),
    S50132("S50132", "UDS模块返回代码0x84 - 引擎未运行", "UDS_NRC_0x84 - Engine not running"),
    S50133("S50133", "UDS模块返回代码0x85 - 引擎运行时间过短", "UDS_NRC_0x85 - The engine running time is too short"),
    S50134("S50134", "UDS模块返回代码0x86 - 温度过高", "UDS_NRC_0x86 - High temperature"),
    S50135("S50135", "UDS模块返回代码0x87 - 温度过低", "UDS_NRC_0x87 - Too low temperature"),
    S50136("S50136", "UDS模块返回代码0x88 - 车速过高", "UDS_NRC_0x88 - Excessive speed"),
    S50137("S50137", "UDS模块返回代码0x89 - 车速过低", "UDS_NRC_0x89 - Driving too low"),
    S50138("S50138", "UDS模块返回代码0x8A - 踏板过高", "UDS_NRC_0x8A - Pedal too high"),
    S50139("S50139", "UDS模块返回代码0x8B - 踏板过低", "UDS_NRC_0x8B - Pedal too low"),
    S50140("S50140", "UDS模块返回代码0x8C - 不在空挡", "UDS_NRC_0x8C - Out of neutral"),
    S50141("S50141", "UDS模块返回代码0x8D - 不在指定挡位", "UDS_NRC_0x8D - Not in designated gear"),
    S50143("S50143", "UDS模块返回代码0x8F - 未开启制动开关", "UDS_NRC_0x8F - The brake switch is not turned on"),
    S50144("S50144", "UDS模块返回代码0x90 - 车辆不在P档", "UDS_NRC_0x90 - The vehicle is not in P gear"),
    S50145("S50145", "UDS模块返回代码0x91 - 变速离合开关无法满足要求", "UDS_NRC_0x91 - Variable speed clutch switch can not meet the requirements"),
    S50146("S50146", "UDS模块返回代码0x92 - 电压过高", "UDS_NRC_0x92 - High voltage"),
    S50147("S50147", "UDS模块返回代码0x93 - 电压过低", "UDS_NRC_0x93 - Too low voltage"),
    S110003("S110003", "VBF_SWDL - 缺少文件", "VBF_SWDL - Missing files"),
    S110014("S110014", "VBF_SWDL - 未指定文件", "VBF_SWDL - Not specified file"),
    SG00001("SG00001", "保存诊断序列文件到本地失败", "Failed to save diagnostic sequence file to local"),
    SG00002("SG00002", "离线模式,保存VBF文件到本地失败", "Description Failed to save the VBF file to the local PC in offline mode"),
    SG00003("SG00003", "下载VBF文件失败", "Failed to download the VBF file. Procedure"),
    SG00004("SG00004", "升级客户端,下载jar文件失败", "Failed to download the jar file during client upgrade. Procedure"),
    SG00005("SG00005", "升级客户端,下载jar文件失败,网络出错", "Client upgrade, jar file download failed, network error"),
    SG00006("SG00006", "升级客户端,下载SQL文件失败", "Failed to download the SQL file during client upgrade. Procedure"),
    SG00007("SG00007", "升级客户端,下载SQL文件失败,网络出错", "Client upgrade, SQL file download failed, network error"),
    SG00008("SG00008", "获取testerToken失败", "Description Failed to obtain testerToken"),
    SG00009("SG00009", "注册Java字符串到内存类加载器中失败", "Failed to register the Java string to the memory classloader"),
    SG00010("SG00010", "动态编译出错", "Dynamic compilation error"),
    SG00011("SG00011", "执行MainMoudle失败", "Failed to execute MainMoudle. Procedure"),
    SG00012("SG00012", "执行FinalMoudle失败", "Failed to execute FinalMoudle. Procedure"),
    SG00013("SG00013", "激活操作,复位失败", "Activation operation, reset failed"),
    SG00014("SG00014", "搜索附近车辆失败", "Search for nearby vehicles failed"),
    SG00015("SG00015", "车辆连接失败", "Vehicle connection failure"),
    SG00016("SG00016", "获取车辆详情失败", "Failed to obtain vehicle details"),
    SG00017("SG00017", "断开车辆连接失败", "Failed to disconnect the vehicle"),
    SG00018("SG00018", "车辆连接,脚本初始化失败", "Vehicle connection, script initialization failed"),
    SG00019("SG00019", "调用DTC概览接口异常", "Description Failed to invoke the DTC overview interface"),
    SG00020("SG00020", "调用DTC标定概览接口异常", "Call DTC Calibration overview interface exception"),
    SG00021("SG00021", "获取DTC列表接口异常", "Description The interface for obtaining the DTC list failed"),
    SG00022("SG00022", "查询ECU绑定的诊断序列失败", "Failed to query the diagnostic sequence of the ECU binding. Procedure"),
    SG00023("SG00023", "查询ECU列表失败", "Description Failed to query the ECU list"),
    SG00024("SG00024", "在线登录失败", "Online login Failure"),
    SG00025("SG00025", "诊断仪登录云端失败", "The diagnostic device failed to log in to the cloud center"),
    SG00026("SG00026", "申请RSA公钥失败", "Applying for an RSA public key fails. Procedure"),
    SG00027("SG00027", "申请秘钥失败", "Failed to apply for a key. Procedure"),
    SG00028("SG00028", "获取DME信息失败", "Description Failed to obtain DME information"),
    SG00029("SG00029", "系统检查是否存在缓存的DTC数据失败", "The system fails to check whether cached DTC data exists"),
    SG00030("SG00030", "获取车辆最近三次readout数据列表失败", "Failed to obtain the last three readout data lists of the vehicle"),
    SG00031("SG00031", "设置缓存的DTC、ECU信息到全局变量中失败", "Description Failed to set the cached DTC and ECU information to the global variable"),
    SG00032("SG00032", "查询最近车辆异常", "Example Query recent vehicle anomalies"),
    SG00033("SG00033", "获取快速链接列表异常", "Error Obtaining quick link list"),
    SG00034("SG00034", "获取发布说明列表异常", "Obtaining the release description list is abnormal"),
    SG00035("SG00035", "获取重载软件列表异常", "Obtaining the overloaded software list fails. Procedure"),
    SG00036("SG00036", "获取其他软件列表异常", "Obtaining the overloaded software list fails. Procedure"),
    SG00037("SG00037", "初始化重载软件刷写异常", "Initialize overloaded software write exception"),
    SG00038("SG00038", "保存最近车辆异常", "Save recent vehicle anomalies"),
    SG00039("SG00039", "诊断序列初始化UI接口异常", "Troubleshooting sequence UI interface initialization exception"),
    SG00040("SG00040", "诊断序列CallMainModule执行失败", "Execution of the diagnostic sequence CallMainModule fails. Procedure"),
    SG00041("SG00041", "诊断序列RefreshUi接口异常", "Diagnostic sequence The RefreshUi interface is abnormal"),
    SG00042("SG00042", "诊断序列CallFinalModule接口异常", "Diagnostic Sequence The CallFinalModule interface is abnormal"),
    SG00043("SG00043", "诊断序列Click事件执行失败", "The diagnostic sequence Click event failed to execute"),
    SG00044("SG00044", "获取诊断序列ID失败", "Failed to obtain the diagnostic sequence ID. Procedure"),
    SG00045("SG00045", "唤醒父诊断序列接口异常", "Wake up the parent diagnostic sequence interface exception"),
    SG00046("SG00046", "车辆状态读取报错", "Vehicle status reading error"),
    SG00047("SG00047", "同步用户异常", "User Synchronization Exception"),
    SG00048("SG00048", "同步离线诊断序列异常", "The synchronous offline diagnostic sequence is abnormal"),
    SG00049("SG00049", "初始化静态文件失败", "Failed to initialize the static file. Procedure"),
    SG00050("SG00050", "获取升级软件列表异常", "Obtaining the upgrade software list fails. Procedure"),
    SG00051("SG00051", "软件升级,下载VBF失败", "Description Software upgrade failed to download VBF"),
    SG00052("SG00052", "软件升级,下载VBF失败,网络出错", "Software upgrade, failed to download VBF, network error"),
    SG00053("SG00053", "获取下载ID异常", "Description Failed to obtain the download ID"),
    SG00054("SG00054", "获取下载进度异常", "The download progress is abnormal. Procedure"),
    SG00055("SG00055", "取消下载异常", "Cancel download exception"),
    SG00056("SG00056", "初始化升级软件刷写异常", "Initial upgrade software flush exception"),
    SG00057("SG00057", "获取版本升级信息失败", "Description Failed to obtain version upgrade information"),
    SG00058("SG00058", "执行SQL失败", "SQL execution failed"),
    SG00059("SG00059", "获取版本号失败", "Failed to obtain the version number. Procedure"),
    SG00060("SG00060", "关闭客户端失败", "Failed to close the client. Procedure"),
    SG00061("SG00061", "SHA256加密失败", "SHA256 encryption failed. Procedure"),
    SG00062("SG00062", "车辆读取DTC信息时保存缓存数据到本地数据库出错", "The vehicle failed to save the cached data to the local database when reading DTC information"),
    SG00063("SG00063", "setJsonProperty的path有空格", "The path of setJsonProperty has Spaces"),
    SG00064("SG00064", "无法为非JSON数据执行getJsonProperty", "Cannot execute getJsonProperty for non-JSON data"),
    SG00065("SG00065", "软件刷写脚本执行失败", "Description Failed to execute the software scrub script"),
    SG00066("SG00066", "获取证书VBF文件失败", "Failed to obtain the certificate VBF file. Procedure"),
    SG00067("SG00067", "软件刷写后删除SWK1、SWK2文件出错", "The SWK1 and SWK2 files failed to be deleted after the software was scrubbed"),
    SG00068("SG00068", "VBF刷写失败", "VBF flushing failed. Procedure"),
    SG00069("SG00069", "ECU刷写超时错误", "ECU flush timeout error"),
    SG00070("SG00070", "VBF校验出错", "VBF check error"),
    SG00071("SG00071", "VBF解析失败", "VBF resolution failed. Procedure"),
    SG00072("SG00072", "执行诊断序列时下载VBF文件保存记录到本地数据库出错", "Failed to download the VBF file and save records to the local database while executing the diagnostic sequence"),
    SG00073("SG00073", "车辆连接状态断开", "The vehicle is disconnected"),
    SG00074("SG00074", "seqProcessCount没有初始化", "seqProcessCount is not initialized"),
    SG00075("SG00075", "诊断序列等待失败", "Diagnostic sequence wait failed"),
    SG00076("SG00076", "Q操作线程指令执行失败", "Q Operation thread instruction execution failed"),
    SG00077("SG00077", "等待接收新的Q操作失败", "Failed to receive a new Q operation"),
    SG00078("SG00078", "等待关闭Q操作子线程失败", "Failed to wait to close the Q operation subthread"),
    SG00079("SG00079", "Q操作完成的等待失败", "Q Failed to wait for operation completion"),
    SG00080("SG00080", "等待接收Q操作指令失败", "Waiting for receiving Q operation instruction failed"),
    SG00081("SG00081", "获取激活列表失败", "Failed to obtain the activation list. Procedure"),
    SG00082("SG00082", "激活失败", "Activation failure"),
    SG00083("SG00083", "解析DTC1904数据失败", "Failed to parse DTC1904 data"),
    SG00084("SG00084", "获取DID分割规则异常", "The DID split rule exception was obtained. Procedure"),
    SG00085("SG00085", "获取DID分割规则为空", "The DID partition rule is null"),
    SG00086("SG00086", "获取DID ResponseItem分割规则异常", "Gets the DID ResponseItem split rule exception"),
    SG00087("SG00087", "获取参数列表失败", "Failed to obtain the parameter list. Procedure"),
    SG00088("SG00088", "参数读取出错", "Parameter reading error"),
    SG00089("SG00089", "解析DID的应答数据出错", "Parsing DID response data failed. Procedure"),
    SG00090("SG00090", "根据诊断零件号获取PINCODE失败", "Failed to obtain the PINCODE according to the diagnostic part number"),
    SG00091("SG00091", "下载VBF文件保存记录到本地数据库出错", "Failed to download the VBF file and save records to the local database"),
    SG00092("SG00092", "离线模式,诊断序列上传失败", "Symptom In offline mode, uploading the diagnostic sequence failed"),
    SG00093("SG00093", "离线模式,VBF文件上传失败", "Symptom In offline mode, uploading the diagnostic sequence failed"),
    SG00094("SG00094", "离线模式,VBF解析失败", "Description In offline mode,VBF resolution failed"),
    SG00095("SG00095", "离线模式,诊断序列解析失败", "In offline mode, diagnostic sequence resolution failed. Procedure"),
    SG00096("SG00096", "未识别的字符,请检查输入的内容是否合法", "Unrecognized characters. Check whether the entered content is valid"),
    SG00097("SG00097", "数据接收超时", "Data receiving timeout"),
    SG00098("SG00098", "PCAP文件存储位置配置错误", "The PCAP file storage location is incorrectly configured"),
    SG00099("SG00099", "线程意外中断", "Unexpected thread interruption"),
    SG00100("SG00100", "暂不支持此类型的DOIP消息", "This type of DOIP message is not supported yet"),
    SG00101("SG00101", "获取用户账户异常", "Description Failed to obtain the user account"),
    SG00102("SG00102", "用户名或密码错误", "The user name or password is incorrect"),
    SG00103("SG00103", "密码不正确", "The password is incorrect."),
    SG00104("SG00104", "RSA解密异常", "RSA decryption Exception"),
    SG00105("SG00105", "部门停用，不允许新增", "Departments are disabled and cannot be added"),
    SG00106("SG00106", "执行启动服务出错", "Failed to start the service"),
    SG00107("SG00107", "执行停止服务出错", "Error performing stop service"),
    SG00108("SG00108", "Windows服务关闭失败", "Failed to shut down the Windows service. Procedure"),
    SG00109("SG00109", "没有以管理员身份执行，Windows服务关闭失败", "Failed to shut down the Windows service because the operation is not performed as an administrator. Procedure"),
    SG00110("SG00110", "找不到web浏览器", "The web browser cannot be found"),
    SG00111("SG00111", "证书无效或不受信任", "The certificate is invalid or untrusted"),
    SG00112("SG00112", "脚本文件为空", "The script file is empty"),
    SG00113("SG00113", "访问受限，授权过期", "Access restricted, authorization expired"),
    SG00114("SG00114", "无法执行setJsonProperty", "Unable to execute setJsonProperty"),
    SG00115("SG00115", "车辆未连接", "Vehicle not connected"),
    SG00116("SG00116", "此byteArray不能为null或空", "This byteArray cannot be null or empty"),
    SG00117("SG00117", "未匹配到适用的诊断序列", "No suitable diagnostic sequence was matched"),
    SG00118("SG00118", "该vin没有查到broadcasts信息", "The vin did not identify broadcasts"),
    SG00119("SG00119", "未找到诊断序列", "No diagnostic sequence was found"),
    SG00120("SG00120", "激活选项有误", "Incorrect activation option"),
    SG00121("SG00121", "未匹配到ECU的PINCODE", "The PINCODE does not match the ECU"),
    SG00122("SG00122", "激活安全未通过", "Activation security failed. Procedure"),
    SG00123("SG00123", "激活未收到响应", "No response was received for activation"),
    SG00124("SG00124", "上传OSS异常", "Uploading OSS Exception"),
    SG00125("SG00125", "参数安全未通过", "Parameter security failed. Procedure"),
    SG00126("SG00126", "参数有误", "Parameter error"),
    SG00127("SG00127", "Q操作线程已经错位", "Q The operation thread is misplaced"),
    SG00128("SG00128", "Q操作的非第一顺位线程休眠失败", "The non-first-order thread of the Q operation failed to sleep"),
    SG00129("SG00129", "本诊断序列不支持Q操作，请在诊断序列中增加enableQop指令启动Q操作", "This diagnostic sequence does not support the Q operation. Please add the enableQop command to the diagnostic sequence to enable the Q operation"),
    SG00130("SG00130", "验签失败", "Visa inspection failed"),
    SG00131("SG00131", "激活SBL出错", "Failed to activate the SBL. Procedure"),
    SG00132("SG00132", "非pincode原因导致的安全访问未通过", "Security access failed for reasons other than pincode"),
    SG00133("SG00133", "证书VBF刷写失败", "Description Failed to flush the certificate VBF"),
    SG00134("SG00134", "获取维修序列列表异常", "Get repair sequence list exception"),
    SG00135("SG00135", "初始化DoipAddressManager失败", "Description Failed to initialize DoipAddressManager"),
    SG00136("SG00136", "没有以管理员身份执行，Windows服务启动失败", "The Windows service fails to start because the system is not executed as an administrator. Procedure"),
    SG00137("SG00137", "调用云端接口超时", "Invoking the cloud interface timed out"),
    SG00138("SG00138", "诊断必要参数缺失", "Diagnostic parameters are missing"),
    SG00139("SG00139", "数据缺失,获取DTC详细信息失败", "Failed to obtain DTC details because data is missing. Procedure"),
    SG00140("SG00140", "刷新DTC详细信息失败", "Description Failed to refresh DTC details"),
    SG00141("SG00141", "账号不存在", "The account does not exist"),
    SG00142("SG00142", "您的账号已禁用，请联系管理员", "Your account has been disabled. Please contact the administrator"),
    SG00143("SG00143", "数据接收超时————78响应超过", "Data reception timeout ————78 Response more than "),
    SG00144("SG00144", "数据接收超时————脚本总时长超时", "Data receiving timeout ———— The total script duration expires "),
    SG00145("SG00145", "数据接收超时————缺省", "Data receiving timeout ———— The default is "),
    SG00146("SG00146", "收到8003负反馈", "8003 negative feedback received"),
    SG00147("SG00147", "云端返回的证书VBF内容有误", "The certificate VBF returned by the cloud is incorrect"),
    SG00148("SG00148", "未找到VBF模板文件", "VBF template file not found"),
    SG00149("SG00149", "证书VBF下载失败", "Failed to download the VBF certificate. Procedure"),
    SG00150("SG00150", "VBF模板文件转base64出错", "Error in converting VBF template file to base64"),
    SG00151("SG00151", "Windows服务启动失败", "The Windows service fails to be started. Procedure"),
    SG00152("SG00152", "DoipException", "DoipException"),
    SG00153("SG00153", "API调用失败", "API call failed"),
    SG00154("SG00154", "未配置手机号码", "The mobile phone number is not configured"),
    SG00155("SG00155", "验证码无效", "The verification code is invalid."),
    SG00156("SG00156", "完整性校验失败", "Integrity check fails"),
    SG00157("SG00157", "您没有在此客户端上的使用权限，请联系管理员", "You do not have permission to use this client. Please contact the administrator"),
    SG00158("SG00158", "本客户端已被禁用，无法使用，请联系管理员", "The client has been disabled and cannot be used. Contact the administrator"),
    SG00159("SG00159", "客户端硬件信息异常，无法使用，请联系管理员", "The client hardware information is abnormal and cannot be used. Contact the administrator"),
    SG00160("SG00160", "数据错误", "Data error"),
    SG00161("SG00161", "收到8003负反馈", "8003 negative feedback received"),
    SG00162("SG00162", "等待8002超过", "Wait for 8002 for more than "),
    SG00163("SG00163", "车辆详细信息，参数缺少VIN号", "Vehicle details, parameters missing VIN number"),
    SG00164("SG00164", "TXT文件存储位置配置错误", "The location where the TXT file is stored is incorrectly configured"),
    SG00165("SG00165", "适用性匹配失败", "Suitability matching failed"),
    SG00166("SG00166", "获取Broadcast信息失败", "Failed to obtain Broadcast information. Procedure"),
    SG00167("SG00167", "没有离线模式权限", "No offline mode permission is available"),
    SG00168("SG00168", "FEMA列表，缺少VIN号", "FEMA list, missing VIN number"),
    SG00169("SG00169", "诊断序列被用户中止", "The diagnostic sequence was aborted by the user"),
    SG00170("SG00170", "车辆连接异常", "Abnormal vehicle connection"),
    SG00171("SG00171", "VBF文件不存在", "The VBF file does not exist"),
    SG00172("SG00172", "解析VBF_HEADER失败", "Parsing VBF_HEADER failed. Procedure"),
    SG00173("SG00173", "VBF文件不完整", "The VBF file is incomplete"),
    SG00174("SG00174", "VBF文件完整性校验出错，请重新购买", "VBF file integrity check error, please re-purchase"),
    SG00175("SG00175", "下载暂停，请检查网络稍后再试", "Download suspended, Please check the network and try again later"),
    SG00176("SG00176", "系统繁忙", "The system is Busy"),
    SG00177("SG00177", "该vin的broadcasts信息中未查到ecu信息", "ecu information was not detected by the vin broadcasts"),
    SG00178("SG00178", "发送消息失败", "Failed to send a message"),
    SG00179("SG00179", "该车已经被连接", "The vehicle has been connected"),
    SG00180("SG00180", "该车正在连接中", "The car is being connected"),
    SG00181("SG00181", "url为空", "The url is empty"),
    SG00182("SG00182", "查询远程诊断历史异常", "Example Query remote diagnosis history exceptions"),
    SG00183("SG00183", "查看远程诊断任务异常", "Check the remote diagnosis task exception"),
    SG00184("SG00184", "远程诊断连接异常", "The remote diagnosis connection is abnormal"),
    SG00185("SG00185", "远程诊断读取DTC异常", "The remote diagnosis fails to read DTC"),
    SG00186("SG00186", "VBF文件夹默认大小不能小于40G", "The default size of the VBF folder cannot be smaller than 40 GB"),
    SG00187("SG00187", "获取研发诊断序列列表异常", "Get an exception to the R&D diagnostic sequence list"),
    SG00188("SG00188", "诊断仪存在已经连接的车辆，请断开车辆后再更改路径", "A connected vehicle exists in the diagnostic device. Please disconnect the vehicle and then change the path"),
    SG00189("SG00189", "诊断仪存在已经连接的车辆，请断开车辆后再清理", "A connected vehicle exists in the diagnostic instrument. Please disconnect the vehicle before cleaning it"),
    SG00190("SG00190", "盘空间不足，请检查并清理，避免影响应用的正常使用", " disk space Insufficient, please check and clean it to avoid affecting the normal use of the application"),
    SG00191("SG00191", "磁盘空间已满，写入文件失败，请检查", "Disk space is used up, failed to write the file, please check"),
    SG00192("SG00192", "登录次数用尽，请联系管理员", "If the login times are exhausted, contact the administrator"),
    SG00193("SG00193", "路径不存在，保存失败", "Failed to save because the path does not exist"),
    SG00194("SG00194", "已在其他设备登录，不能重复登录！", "You have logged in to another device. You cannot log in again!"),
    SG00195("SG00195", "vbf文件crc校验失败", "VBF file crc verification failed"),
    SG00196("SG00196", "管理员断开车辆连接异常", "The administrator disconnects the vehicle incorrectly. Procedure"),
    SG00197("SG00197", "获取车辆管理列表接口异常", "The interface for obtaining the vehicle management list is abnormal. Procedure"),
    SG00198("SG00198", "该车已被其他用户连接", "The car has been connected to another user"),
    SG00199("SG00199", "文件IO异常", "File I/O Exception"),
    SG00200("SG00200", "调用云端失败", "Failed to invoke the cloud."),
    SG00201("SG00201", "正在对车辆进行诊断，是否断开连接", "Diagnosing the vehicle, disconnecting or not"),
    SG00202("SG00202", "正在对车辆进行软件刷写，是否断开连接", "Software is being scrubbed on the vehicle, whether to disconnect"),
    SG00203("SG00203", "管理员确认断开车辆连接异常", "The administrator confirms that the disconnected vehicle is abnormal"),
    SG00204("SG00204", "账号已过期，请联系管理员", "The account has expired. Please contact the administrator"),
    SG00205("SG00205", "查询一键OTA历史异常", "The VIN has no broadcast information"),
    SG00206("SG00206", "一键OTA安装异常", "One-click OTA installation is abnormal"),
    SG00207("SG00207", "更换阀体总成异常", "Replacement valve assembly abnormal"),
    SG00208("SG00208", "更换PCM总成异常", "The PCM assembly is abnormal"),
    SG00209("SG00209", "获取车辆连接状态异常", "The vehicle connection status is abnormal"),
    SG00210("SG00210", "记录DSA日志异常", "Description Failed to record DSA logs"),
    SG00211("SG00211", "DSA日志未初始化", "The DSA log is not initialized"),
    SG00212("SG00212", "长度不等于12位，请重新输入", "The length is not 12 characters. Please enter a new one"),
    SG00213("SG00213", "初始化DSA日志异常", "Failed to Initialization DSA logs"),
    SG00214("SG00214", "DSA发送UDS异常", "The DSA fails to send UDS data"),
    SG00215("SG00215", "DSA安全算法异常", "The DSA security algorithm is abnormal"),
    SG00216("SG00216", "文件已存在", "File already exists"),
    SG00217("SG00217", "安全软件缺失,无法登录。请安装安全软件后再次尝试登录MADS系统", "The security software is missing, unable to log in. Please install the security software and try to log in to the MADS system again"),
    SG00218("SG00218", "sddb文件读取失败", "Sddb file read failed"),
    SG00219("SG00219", "根据诊断零件号和服务id获取数据失败", "Failed to obtain data according to diagnostic part number and service id"),
    SG00220("SG00219", "获取历史访问记录失败", "Failed to get the history access record"),
    SG00221("SG00221", "初始化DSA的PCAP日志异常", "The PCAP log of initializing the DSA is abnormal"),
    SG00222("SG00222", "日志类型错误", "Log type error"),
    SG00223("SG00223", "文件格式错误", "File format error"),
    SG00224("SG00224", "vbf文件解析失败", "Failed to parse vbf file"),
    SG00225("SG00225", "ECU识别失败", "ECU identification failed"),
    SG00226("SG00226", "DSA刷写异常", "DSA flush failed"),
    SG00227("SG00227", "获取快捷链接列表失败", "Failed to get the quick link list"),
    SG00228("SG00228", "新增快捷链接失败", "Failed to add a quick link"),
    SG00229("SG00229", "修改快捷链接失败", "Failed to update a quick link"),
    SG00230("SG00230", "删除快捷链接失败", "Failed to delete a quick link"),
    SG00231("SG00231", "获取快捷链接失败", "Failed to get a quick link"),
    SG00232("SG00232", "Pincode错误", "Pincode Error"),
    SG00233("SG00233", "移动失败", "Move failed"),
    SG00234("SG00234", "发送1002失败", "Failed to send 1002"),
    SG00235("SG00235", "车辆连接异常,OBD线断开", "Vehicle connection is abnormal,OBD line is disconnected"),
    SG00236("SG00236", "未找到该按钮对应的脚本，请联系管理员。", "The script corresponding to the button is not found, contact the administrator."),
    SG00237("SG00237", "获取节点失败", "Failed to obtain node."),
    SG00238("SG00238", "SDDB文件未上传", "SDDB file not uploaded."),
    SG00239("SG00239", "该车未连接到诊断仪，请重新搜索车辆后再试", "The car is not connected to the diagnostic instrument, please search the car again and try again"),
    SG00240("SG00240", "获取csc失败", "Failed to get cscs."),
    SG00241("SG00241", "创建维修单失败", "Failed to create maintenance task."),
    SG00242("SG00242", "获取维修任务失败", "Failed to get maintenance task."),
    SG00243("SG00243", "执行故障测试失败", "Failed to perform fault test."),
    SG00244("SG00244", "撤销故障测试失败", "Failed to undo fault test."),
    SG00245("SG00245", "维修结果反馈失败", "Repair result feedback failed."),
    SG00246("SG00246", "登录失败，该电脑终端已被其他用户绑定", "Login failed, the client binded by another account."),
    SG00247("SG00247", "获取维修任务关闭选项失败", "Failed to get the repair task close option"),
    SG00248("SG00248", "关闭维修任务失败", "Failed to close maintenance task"),
    SG00249("SG00249", "作废工单失败", "Failed to delete maintenance task"),
    SG00250("SG00250", "撤销已执行的维修方案失败", "Failed to revoke the executed maintenance plan"),
    SG00251("SG00251", "获取已有维修任务失败", "Failed to existing maintenance tasks"),
    SG00252("SG00252", "获取日志收集进度失败", "Failed to obtain the log collection progress"),
    SG00253("SG00253", "提交支持工单失败", "Failed to submit a support work order"),
    SG00254("SG00254", "认证信息已过期，请联网登录认证", "The authentication information has expired. Please log in online for authentication"),
    SG00255("SG00255", "诊断序列执行异常", "Diagnostic sequence execution error"),
    SG00256("SG00256", "保存本地日志失败", "Failed to save the local log"),
    SG00257("SG00257", "删除本地日志失败", "Failed to delete the local log"),
    SG00258("SG00258", "保持会话发送失败", "Failed to hold the session"),
    SG00259("SG00259", "网络异常，请检查你的网络环境，重启客户端重试，如仍然失败，请下载最新安装包进行安装", "Network abnormality. Please check your network environment, restart the DS client, and try again. If it still fails, please download the latest installation package for installation."),
    SG00260("SG00260", "升级失败，请重启客户端重试，如仍然失败，请下载最新安装包进行安装", "Upgrade failed, please restart DS client and try again. If it still fails, please download the latest installation package for installation."),
    SG00261("SG00261", "变速器号信息数据不存在", "Transmission number information data does not exist."),
    SG00262("SG00262", "一键OTA任务撤销异常", "Ota task withdrawn error."),
    SG00263("SG00263", "脚本文件不存在，请联网在线登陆后，重试", "Script file does not exist, please log in online and try again"),
    SG00264("SG00264", "获取技术指引数据异常", "Obtaining technical guidance data is abnormal"),
    SG00265("SG00265", "智能检索获取搜索条件异常", "The intelligent search fails to obtain the search criteria"),
    SG00266("SG00266", "获取CEP配置信息失败", "Failed to obtain the CEP configuration information"),
    SG00267("SG00267", "state不一致", "state inconsistency"),
    SG00268("SG00268", "请求SWT1公钥信息异常", "Request for SWT1 public key information exception"),
    SG00269("SG00269", "加载本地bss异常", "Loading the local bss failed"),
    SG00270("SG00270", "加载本地VDN异常", "Loading the local VDN failed"),
    SG00271("SG00271", "获取在线VDN异常", "Description Failed to obtain the online VDN"),
    SG00272("SG00272", "软件结算异常", "Software settlement exception"),
    SG00273("SG00273", "pincode长度与期望长度不一致", "Software settlement exception"),
    SG00274("SG00274", "未收到车辆激活响应消息", "Vehicle activation response message not received"),
    SG00275("SG00275", "车辆激活失败:", "Vehicle activation failed:"),
    SG00276("SG00276", "vin号长度不正确，请重新输入", "The vin number length is incorrect, please re-enter"),
    SG00277("SG00277", "证书申请失败", "The certificate application failed"),
    SG00278("SG00278", "一键OTA远程升级异常", "One click OTA remote upgrade exception"),
    SG00280("SG00280", "远程诊断读取参数异常", "Remote diagnosis reading parameters is abnormal"),
    SG00281("SG00281", "自动检测异常", "Automatic anomaly detection"),
    SG00282("SG00282", "获取集成测试脚本列表失败", "Failed to retrieve the list of integration test scripts"),
    SG00283("SG00283", "工单附件下载失败", "Failed to download work order attachment"),
    SG00284("SG00284", "获取防盗功能序列列表异常", "Exception in obtaining anti-theft function sequence list"),
    SG00285("SG00285", "获取列表异常", "Exception in getting list"),
    SG00286("SG00286", "FEMA列表，缺少DTC ID", "FEMA list, missing DTC ID"),
    SG00287("SG00287", "FEMA列表，缺少诊断零件号", "FEMA list, missing diagnostic number");

    private String code;
    private String value;
    private String valueEn;

    TesterErrorCodeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    TesterErrorCodeEnum(String code, String value, String valueEn) {
        this.code = code;
        this.value = value;
        this.valueEn = valueEn;
    }

    public String code() {
        return this.code;
    }

    public String value() {
        return this.value;
    }

    public String valueEn() {
        return this.valueEn;
    }

    public String valueByLanguage() {
        if ("zh-CN".equals(HttpUtils.getLanguage())) {
            return this.value;
        }
        return this.valueEn;
    }

    public static String formatMsg(TesterErrorCodeEnum errorCode) {
        String message = MessageUtils.getMessage(errorCode);
        return errorCode.code + ":" + message;
    }

    public static TesterErrorCodeEnum getEnumByCodeOrStr(String msg) {
        if (StringUtils.isBlank(msg)) {
            return null;
        }
        int indexOf = msg.indexOf(":", 1);
        if (indexOf != -1) {
            msg = msg.substring(0, indexOf);
        }
        try {
            return valueOf(msg);
        } catch (Exception e) {
            return null;
        }
    }

    public static String getValue(String enueString) {
        TesterErrorCodeEnum testerErrorCodeEnum = valueOf(enueString);
        return formatMsg(testerErrorCodeEnum);
    }
}
