package com.geely.gnds.tester.enums;

/* loaded from: TesterNegativeResEnum.class */
public enum TesterNegativeResEnum {
    RES_11("11", TesterErrorCodeEnum.S50017),
    RES_12("12", TesterErrorCodeEnum.S50018),
    RES_13("13", TesterErrorCodeEnum.S50019),
    RES_14("14", TesterErrorCodeEnum.S50020),
    RES_22("22", TesterErrorCodeEnum.S50034),
    RES_24("24", TesterErrorCodeEnum.S50036),
    RES_31("31", TesterErrorCodeEnum.S50049),
    RES_33("33", TesterErrorCodeEnum.S50051),
    RES_35("35", TesterErrorCodeEnum.S50053),
    RES_36("36", TesterErrorCodeEnum.S50054),
    RES_37("37", TesterErrorCodeEnum.S50055),
    RES_70("70", TesterErrorCodeEnum.S50112),
    RES_71("71", TesterErrorCodeEnum.S50113),
    RES_72("72", TesterErrorCodeEnum.S50114),
    RES_73("73", TesterErrorCodeEnum.S50115),
    RES_78("78", TesterErrorCodeEnum.S50120),
    RES_7E("7E", TesterErrorCodeEnum.S50126),
    RES_7F("7F", TesterErrorCodeEnum.S50127),
    RES_83("83", TesterErrorCodeEnum.S50131),
    RES_84("84", TesterErrorCodeEnum.S50132),
    RES_85("85", TesterErrorCodeEnum.S50133),
    RES_92("92", TesterErrorCodeEnum.S50146),
    RES_93("93", TesterErrorCodeEnum.S50147);

    private String code;
    private TesterErrorCodeEnum oD;

    TesterNegativeResEnum(String code, TesterErrorCodeEnum errorEnum) {
        this.code = code;
        this.oD = errorEnum;
    }

    public String getCode() {
        return this.code;
    }

    public TesterErrorCodeEnum getErrorEnum() {
        return this.oD;
    }

    public static String a(TesterErrorCodeEnum testerErrorCodeEnum) {
        String res = "";
        for (TesterNegativeResEnum value : values()) {
            boolean equals = value.getErrorEnum().equals(testerErrorCodeEnum);
            if (equals) {
                res = value.getCode();
            }
        }
        return res;
    }
}
