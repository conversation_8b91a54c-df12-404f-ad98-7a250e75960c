package com.geely.gnds.tester.socket;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.http.NetQualityLevel;
import com.geely.gnds.ruoyi.common.utils.http.NetQualityUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.dto.GndsMsgBeanDTO;
import com.geely.gnds.tester.dto.GndsResultDTO;
import com.geely.gnds.tester.dto.UpgradeDownLoadDTO;
import com.geely.gnds.tester.service.LoginService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import javax.annotation.PostConstruct;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@ServerEndpoint("/api/v1/socket/global")
@Component
/* loaded from: GndsWebSocket.class */
public class GndsWebSocket {
    private static final Logger LOG = LoggerFactory.getLogger(GndsWebSocket.class);
    private static Set<Session> px = new CopyOnWriteArraySet();
    private static Map<String, Date> py = new ConcurrentHashMap();
    private static Map<String, String> pz = new ConcurrentHashMap();
    private static Map<String, String> pA = new ConcurrentHashMap();
    private static List<String> pB = new ArrayList();
    private static String pC = "";

    @Value("${fd.cloud.url}")
    private String baseUrl;
    private static String pD;

    public static String getCloud() {
        return pD;
    }

    public static void a(GndsMsgCodeEnum gndsMsgCodeEnum, UpgradeDownLoadDTO downLoadDTO) {
        try {
            GndsResultDTO gndsResultDTO = new GndsResultDTO();
            for (Session session : px) {
                if (null != session && StringUtils.isNotBlank(session.getId()) && px.contains(session)) {
                    gndsResultDTO.setCode(gndsMsgCodeEnum);
                    gndsResultDTO.setData(JSONUtil.toJsonStr(downLoadDTO));
                    session.getAsyncRemote().sendText(JSON.toJSONString(gndsResultDTO));
                }
            }
        } catch (Exception e) {
            LOG.error("下载进度通知异常：{}", e.getMessage());
        }
    }

    @PostConstruct
    public void getCloudUrl() {
        pD = this.baseUrl;
    }

    public static boolean cB(String id) {
        if (StringUtils.isBlank(id)) {
            return false;
        }
        return py.containsKey(id);
    }

    public static void cC(String id) {
        py.remove(id);
        LoginService loginService = (LoginService) SpringUtils.getBean(LoginService.class);
        loginService.heartBeatStop();
    }

    public static void I() {
        py.forEach((id, date) -> {
            if (DateUtil.date().getTime() - date.getTime() > 60000) {
                if (!pB.contains(id)) {
                    LOG.warn("--->长时间未检测到 {} 心跳，程序将主动断开", id);
                    cC(id);
                } else {
                    LOG.warn("--->长时间未检测到 electron {} 心跳，", id);
                }
            }
        });
    }

    public static void aw() {
        FileCache fileCache = (FileCache) SpringUtils.getBean(FileCache.class);
        try {
            fileCache.I();
        } catch (CustomException e) {
            LOG.error("检查磁盘空间：" + e.getMessage());
            pC = e.getMessage();
        } catch (Exception e2) {
            throw new RuntimeException(e2);
        }
    }

    public static void ax() {
        try {
            NetQualityLevel netQualityLevel = NetQualityUtils.checkNetworkLevel(pD, 10);
            GndsResultDTO gndsResultDTO = new GndsResultDTO();
            for (Session session : px) {
                if (null != session && StringUtils.isNotBlank(session.getId()) && px.contains(session) && netQualityLevel != null) {
                    gndsResultDTO.setCode(GndsMsgCodeEnum.NETWORK_STATUS);
                    gndsResultDTO.setData(netQualityLevel.toString());
                    session.getAsyncRemote().sendText(JSON.toJSONString(gndsResultDTO));
                }
            }
        } catch (Exception e) {
            LOG.error("检查网络质量异常：" + e.getMessage());
        }
    }

    @OnOpen
    public void onOpen(Session session) {
        LOG.warn("心跳socket打开：" + session.getId());
        if (null != session && StringUtils.isNotBlank(session.getId())) {
            Map<String, List<String>> paramMap = session.getRequestParameterMap();
            LOG.info("心跳socket打开 --> 请求参数：" + paramMap.toString());
            List<String> typeList = paramMap.get("type");
            if (!CollectionUtils.isEmpty(typeList)) {
                String type = typeList.get(0);
                if ("electron".equalsIgnoreCase(type)) {
                    pB.add(session.getId());
                }
                LOG.info("onOpen时session=={},electronIds=={}", session.getId(), pB);
            }
            px.add(session);
        }
    }

    @OnClose
    public void onClose(Session session) {
        LOG.warn("心跳socket关闭：" + session.getId());
        if (session != null && StringUtils.isNotBlank(session.getId())) {
            String id = pz.get(session.getId());
            px.remove(session);
            Optional optionalOfNullable = Optional.ofNullable(id);
            Map<String, Date> map = py;
            map.getClass();
            optionalOfNullable.ifPresent((v1) -> {
                r1.remove(v1);
            });
            pz.remove(session.getId());
            if (pB.contains(id)) {
                LOG.info("onClose时session=={},electronIds=={}", session.getId(), pB);
                pB.remove(id);
            } else {
                cC(id);
            }
        }
    }

    @OnMessage
    public void onMessage(Session session, String message) {
        GndsResultDTO gndsResultDTO = new GndsResultDTO();
        if (null != session && StringUtils.isNotBlank(session.getId()) && px.contains(session) && JSONUtil.isJson(message)) {
            if (StringUtils.isNotBlank(pC)) {
                gndsResultDTO.setCode(GndsMsgCodeEnum.DISK_ERROR);
                gndsResultDTO.setData(pC);
                session.getAsyncRemote().sendText(JSON.toJSONString(gndsResultDTO));
                pC = "";
            }
            GndsMsgBeanDTO bean = (GndsMsgBeanDTO) JSONUtil.toBean(message, GndsMsgBeanDTO.class);
            String beanData = bean.getData();
            boolean disconnect = cF(beanData);
            if (disconnect) {
                gndsResultDTO.setCode(GndsMsgCodeEnum.ADMIN_DISCONNECT);
                gndsResultDTO.setData("管理员已断开您的车辆连接");
                gndsResultDTO.setVin(cD(beanData));
                cE(beanData);
                LOG.info("管理员已断开您的车辆连接:{}", JSON.toJSONString(gndsResultDTO));
                session.getAsyncRemote().sendText(JSON.toJSONString(gndsResultDTO));
            }
            if (null != bean && BooleanUtil.isTrue(bean.getStatus()) && bean.getDataType().compareTo(GndsMsgCodeEnum.HEART_BEAT) == 0) {
                py.put(beanData, new Date());
                pz.put(session.getId(), beanData);
                if (pB.contains(session.getId())) {
                    pB.remove(session.getId());
                    pB.add(beanData);
                    LOG.info("onMessage时sessionID=={}，beanData=={}，electronIds=={}", new Object[]{session.getId(), beanData, pB});
                    return;
                }
                return;
            }
            return;
        }
        LOG.warn("--->接收到无效的全局Websocket消息：{}", message);
        gndsResultDTO.setCode(GndsMsgCodeEnum.INVALID_MESSAGE);
        gndsResultDTO.setData("Invalid message content");
        session.getAsyncRemote().sendText(JSON.toJSONString(gndsResultDTO));
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        LOG.error("WebSocket连接错误", throwable);
    }

    public static void af(String id, String vin) {
        LOG.info("GndsWebSocket新增断开的车辆vin：{}，id：{}", vin, id);
        pA.put(id, vin);
    }

    public static String cD(String id) {
        if (pA.containsKey(id)) {
            return pA.get(id);
        }
        return "";
    }

    public static void cE(String id) {
        if (pA.containsKey(id)) {
            pA.remove(id);
        }
    }

    public static boolean cF(String id) {
        if (pA.containsKey(id)) {
            return true;
        }
        return false;
    }

    public static void cG(String lang) {
        try {
            GndsResultDTO gndsResultDTO = new GndsResultDTO();
            for (Session session : px) {
                if (null != session && StringUtils.isNotBlank(session.getId()) && px.contains(session)) {
                    gndsResultDTO.setCode(GndsMsgCodeEnum.CHANGE_LANGUAGE);
                    gndsResultDTO.setData((String) null);
                    gndsResultDTO.setLang(lang);
                    session.getAsyncRemote().sendText(JSON.toJSONString(gndsResultDTO));
                }
            }
        } catch (Exception e) {
            LOG.error("检查网络质量异常：" + e.getMessage());
        }
    }
}
