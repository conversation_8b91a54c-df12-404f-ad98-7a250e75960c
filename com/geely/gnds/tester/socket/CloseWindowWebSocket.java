package com.geely.gnds.tester.socket;

import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.service.SeqService;
import com.geely.gnds.tester.util.SpringContextUtils;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import javax.websocket.CloseReason;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@ServerEndpoint("/api/v1/socket/closeWindow")
@Component
/* loaded from: CloseWindowWebSocket.class */
public class CloseWindowWebSocket {
    private static final Logger LOG = LoggerFactory.getLogger(CloseWindowWebSocket.class);
    private static CopyOnWriteArrayList<Session> pv = new CopyOnWriteArrayList<>();
    private ScriptHandler pw;
    private String vin;

    @OnOpen
    public void onOpen(Session session) {
        if (session != null) {
            pv.add(session);
            Map<String, List<String>> paramMap = session.getRequestParameterMap();
            LOG.info("closeWindowTips --> 关闭窗口请求参数：" + paramMap.toString());
            System.err.println(paramMap.toString());
            InitializeUiDto initializeUiDto = e(paramMap);
            String windowType = initializeUiDto.getWindowType();
            Map<String, String> vbfFlagMap = SeqManager.getInstance().getVbfFlag();
            if ("electron".equals(windowType)) {
                a(session, "ElectronConnected");
                LOG.info("closeWindowTips --> 接收到Electron关闭窗口请求，连接成功！");
                System.err.println("接收到Electron关闭窗口请求，连接成功！");
                System.err.println("vbf刷写map" + vbfFlagMap.toString());
                if (vbfFlagMap.size() != 0) {
                    a(session, "SeqRunning");
                    LOG.info("closeWindowTips --> 有脚本在刷写，不可以直接关闭窗口，提示用户是否要关闭");
                    System.err.println("有脚本在刷写，不可以直接关闭窗口，提示用户是否要关闭");
                    return;
                } else {
                    a(session, "NoSeqRunning");
                    LOG.info("closeWindowTips --> 没有脚本在执行，可以直接关闭窗口");
                    System.err.println("没有脚本在执行，可以直接关闭窗口");
                    return;
                }
            }
            if ("seqWindows".equals(windowType)) {
                a(session, "SeqWindowConnected");
                LOG.info("closeWindowTips --> 接收到诊断序列关闭窗口请求，连接成功！");
                System.err.println("接收到诊断序列关闭窗口请求，连接成功！");
                this.pw = o(initializeUiDto);
                this.vin = initializeUiDto.getVin();
                String seqCode = vbfFlagMap.get(initializeUiDto.getVin());
                if (StringUtils.isBlank(seqCode) || this.pw == null || !seqCode.equals(this.pw.getDxSequence().getSeqCode())) {
                    a(session, "NormalSeqRunning");
                    LOG.info("closeWindowTips --> 当前脚本不是刷写脚本，可以直接关闭窗口");
                    System.err.println("当前脚本不是刷写脚本，可以直接关闭窗口");
                    try {
                        try {
                            this.pw.setCloseWindowFlag(true);
                            LOG.info("closeWindowTips --> 用户在有脚本正在执行刷写操作的情况下确定关闭脚本窗口操作！");
                            System.err.println("用户在有脚本正在执行刷写操作的情况下确定关闭脚本窗口操作！");
                            av();
                            LOG.info("脚本已经终止！");
                            a(session, "success");
                        } catch (Exception e) {
                            LOG.error("脚本终止异常！", e);
                            a(session, "success");
                        }
                        return;
                    } catch (Throwable th) {
                        a(session, "success");
                        throw th;
                    }
                }
                a(session, "SeqRunning");
                LOG.info("closeWindowTips --> 当前脚本是刷写脚本，不可以直接关闭窗口，提示用户是否要关闭");
                System.err.println("当前脚本是刷写脚本，不可以直接关闭窗口，提示用户是否要关闭");
            }
        }
    }

    @OnClose
    public void a(Session session, CloseReason reason) {
        LOG.info("Closed: " + reason.getCloseCode() + ", " + reason.getReasonPhrase());
        if (session != null) {
            pv.remove(session);
            LOG.info("closeWindowTips --> 关闭窗口长连接关闭事件执行！");
            System.err.println("关闭连接");
        }
    }

    @OnMessage
    public void onMessage(Session session, String message) throws InterruptedException {
        if ("cancelClose".equals(message)) {
            try {
                LOG.info("closeWindowTips --> 用户取消关闭窗口操作！");
                System.err.println("用户取消关闭窗口操作！");
                session.close();
                return;
            } catch (IOException ioException) {
                LOG.error("closeWindowTips --> 用户取消关闭窗口操作，关闭连接失败！", ioException);
                return;
            }
        }
        if ("sureCloseElectronWindow".equals(message)) {
            LOG.info("closeWindowTips --> 用户在有脚本正在执行刷写操作的情况下确定关闭窗口操作！");
            System.err.println("用户在有脚本正在执行刷写操作的情况下确定关闭Electron窗口操作！");
            as();
            a(session, "success");
            return;
        }
        if ("sureCloseSeqWindow".equals(message)) {
            LOG.info("closeWindowTips --> 用户在有脚本正在执行刷写操作的情况下确定关闭脚本窗口操作！");
            System.err.println("用户在有脚本正在执行刷写操作的情况下确定关闭脚本窗口操作！");
            av();
            LOG.error("脚本已经终止！");
            a(session, "success");
        }
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        LOG.error("closeWindowTips --> 关闭窗口长链接异常中断", throwable);
    }

    public synchronized void a(Session session, String msg) {
        if (session.isOpen()) {
            LOG.info("closeWindowTips --> 发送消息给前端{},session:{}", msg, session);
            try {
                session.getBasicRemote().sendText(msg);
                return;
            } catch (Exception e) {
                LOG.error("closeWindowTips --> 发送消息给前端{}时异常,session:{}", new Object[]{msg, session, e});
                return;
            }
        }
        LOG.error("closeWindowTips --> 发送消息给前端{}时session已经关闭,session:{}", msg, session);
    }

    public void as() throws InterruptedException {
        Map<String, ScriptHandler> handlerMap = SeqManager.getInstance().getHandlerMap();
        for (String key : handlerMap.keySet()) {
            ScriptHandler scriptHandler = handlerMap.get(key);
            scriptHandler.setCloseWindowFlag(true);
        }
        while (true) {
            Map<String, String> vbfFlagMap = SeqManager.getInstance().getVbfFlag();
            LOG.info("closeWindowTips --> 关闭Electron窗口等待，当前有" + vbfFlagMap.size() + "个脚本正在执行刷写");
            System.err.println("当前有" + vbfFlagMap.size() + "个脚本正在执行刷写");
            if (vbfFlagMap.size() > 0) {
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                LOG.info("closeWindowTips --> 关闭Electron窗口等待，所有刷写脚本刷写完成，可以关闭Electron窗口");
                System.err.println("关闭Electron窗口等待，所有刷写脚本刷写完成，可以关闭Electron窗口");
                return;
            }
        }
    }

    public void at() throws InterruptedException {
        Map<String, ScriptHandler> handlerMap = SeqManager.getInstance().getHandlerMap();
        for (String key : handlerMap.keySet()) {
            ScriptHandler scriptHandler = handlerMap.get(key);
            scriptHandler.setCloseWindowFlag(true);
        }
        for (String key2 : handlerMap.keySet()) {
            ScriptHandler scriptHandler2 = handlerMap.get(key2);
            while (true) {
                int seqCount = scriptHandler2.getSeqCount();
                if (seqCount > 0) {
                    LOG.info("closeWindowTips --> 关闭脚本窗口等待，当前有刷写脚本正在执行刷写:" + seqCount);
                    System.err.println("关闭脚本窗口等待，当前有刷写脚本正在执行刷写:" + seqCount);
                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
            LOG.info("closeWindowTips --> 关闭脚本窗口等待，当前刷写脚本刷写完成，可以关闭窗口");
            System.err.println("关闭脚本窗口等待，当前刷写脚本刷写完成，可以关闭窗口");
        }
    }

    public void au() throws InterruptedException {
        this.pw.setCloseWindowFlag(true);
        while (true) {
            Map<String, String> vbfFlagMap = SeqManager.getInstance().getVbfFlag();
            String seqCode = vbfFlagMap.get(this.vin);
            if (StringUtils.isNotBlank(seqCode)) {
                this.pw.notifySeq();
                LOG.info("closeWindowTips --> 关闭脚本窗口等待，当前有刷写脚本正在执行刷写:" + seqCode);
                System.err.println("关闭脚本窗口等待，当前有刷写脚本正在执行刷写:" + seqCode);
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                LOG.info("closeWindowTips --> 关闭脚本窗口等待，当前刷写脚本刷写完成，可以关闭窗口");
                System.err.println("关闭脚本窗口等待，当前刷写脚本刷写完成，可以关闭窗口");
                return;
            }
        }
    }

    public void av() throws InterruptedException {
        this.pw.setCloseWindowFlag(true);
        while (true) {
            this.pw.notifySeq();
            int seqCount = this.pw.getSeqCount();
            if (seqCount > 0) {
                LOG.info("closeWindowTips --> 关闭脚本窗口等待，当前有刷写脚本正在执行刷写:" + seqCount);
                System.err.println("关闭脚本窗口等待，当前有刷写脚本正在执行刷写:" + seqCount);
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                LOG.info("closeWindowTips --> 关闭脚本窗口等待，当前刷写脚本刷写完成，可以关闭窗口");
                System.err.println("关闭脚本窗口等待，当前刷写脚本刷写完成，可以关闭窗口");
                this.pw.createEdr();
                return;
            }
        }
    }

    public ScriptHandler o(InitializeUiDto initializeUiDto) {
        ScriptHandler scriptHandler = null;
        try {
            SeqService seqService = (SeqService) SpringContextUtils.getBean(SeqService.class);
            scriptHandler = seqService.getScriptHandlerFun(initializeUiDto);
        } catch (Exception e) {
            LOG.error("closeWindowTips --> 关闭窗口逻辑处理：获取scriptHandler失败！", e);
            LOG.error("closeWindowTips --> " + initializeUiDto.toString());
        }
        return scriptHandler;
    }

    private InitializeUiDto e(Map<String, List<String>> paramMap) {
        InitializeUiDto dto = new InitializeUiDto();
        List<String> typeList = paramMap.get("type");
        if (!CollectionUtils.isEmpty(typeList)) {
            String type = typeList.get(0);
            dto.setWindowType(type);
        }
        List<String> vinList = paramMap.get("vin");
        if (!CollectionUtils.isEmpty(vinList)) {
            String vin = vinList.get(0);
            dto.setVin(vin);
        }
        List<String> seqCodeList = paramMap.get("seqCode");
        if (!CollectionUtils.isEmpty(seqCodeList)) {
            String seqCode = seqCodeList.get(0);
            if (!"undefined".equals(seqCode)) {
                dto.setSeqCode(seqCode);
            }
        }
        List<String> btnTypeList = paramMap.get("btnType");
        if (!CollectionUtils.isEmpty(btnTypeList)) {
            String btnType = btnTypeList.get(0);
            dto.setType(btnType);
        }
        return dto;
    }
}
