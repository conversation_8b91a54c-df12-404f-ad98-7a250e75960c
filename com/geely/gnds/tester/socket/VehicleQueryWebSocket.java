package com.geely.gnds.tester.socket;

import com.geely.gnds.tester.util.TesterWebSocketUtils;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@ServerEndpoint("/api/v1/socket/queryVehicle")
@Component
/* loaded from: VehicleQueryWebSocket.class */
public class VehicleQueryWebSocket {
    private static final Logger LOG = LoggerFactory.getLogger(VehicleQueryWebSocket.class);

    @OnOpen
    public void onOpen(Session session) {
        TesterWebSocketUtils.addVehicleQuerySession(session);
    }

    @OnClose
    public void onClose(Session session) {
        TesterWebSocketUtils.closeVehicleQuerySession(session);
    }

    @OnMessage
    public void onMessage(Session session, String message) {
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        TesterWebSocketUtils.closeVehicleQuerySession(session);
        LOG.error("查询附近车辆长链接异常", throwable);
    }
}
