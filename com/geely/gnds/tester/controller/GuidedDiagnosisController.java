package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.GuidedDiagnosisService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.vo.ComActivationVo;
import com.geely.gnds.tester.vo.DiaGcidVo;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/gd"})
@RestController
/* loaded from: GuidedDiagnosisController.class */
public class GuidedDiagnosisController {
    private static final Logger log = LoggerFactory.getLogger(GuidedDiagnosisController.class);

    @Autowired
    private GuidedDiagnosisService iZ;

    @GetMapping({"getComponentOtherList/{vin}"})
    public Result<List<DiaGcidVo>> bt(@PathVariable("vin") String vin) {
        Result<List<DiaGcidVo>> result = new Result<>();
        try {
            result.setData(this.iZ.getComponentOtherList(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("获取元件其他列表接口异常", e);
        } catch (Exception e2) {
            result.setCode(HttpStatus.ERROR);
            result.setMsg(e2.getMessage());
            log.error("获取元件其他列表接口异常", e2);
        }
        return result;
    }

    @PostMapping({"getComActivationList"})
    public Result<List<DiagResItemGroupDto>> a(@RequestBody ComActivationVo vo) {
        log.info("======> 客户端开始请求api/v1/gd下的的getComActivationList接口 <======");
        Result<List<DiagResItemGroupDto>> result = new Result<>();
        try {
            log.info("======> 客户端开始调用guidedDiagnosisService的getComActivationList方法 <======");
            result.setData(this.iZ.getComActivationList(vo.getVin(), vo.getGcidName(), vo.getLocation()));
            log.info("======> 客户端调用guidedDiagnosisService的getComActivationList方法结束 <======");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("客户端点击元件其他数据显示的激活列表接口异常", e);
        } catch (Exception e2) {
            result.setCode(HttpStatus.ERROR);
            result.setMsg(e2.getMessage());
            log.error("客户端点击元件其他数据显示的激活列表接口异常", e2);
        }
        log.info("======> 客户端请求api/v1/gd下的的getComActivationList接口结束 <======");
        return result;
    }

    @PostMapping({"getComParamList"})
    public Result<List<DiagResItemGroupDto>> b(@RequestBody ComActivationVo vo) {
        log.info("======> 客户端开始请求api/v1/gd下的的getComParamList接口 <======");
        Result<List<DiagResItemGroupDto>> result = new Result<>();
        try {
            log.info("======> 客户端开始调用guidedDiagnosisService的getComParamList方法 <======");
            result.setData(this.iZ.getComParamList(vo.getVin(), vo.getGcidName(), vo.getLocation()));
            log.info("======> 客户端调用guidedDiagnosisService的getComParamList方法结束 <======");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("客户端点击元件其他数据显示的参数列表接口异常", e);
        } catch (Exception e2) {
            result.setCode(HttpStatus.ERROR);
            result.setMsg(e2.getMessage());
            log.error("客户端点击元件其他数据显示的参数列表接口异常", e2);
        }
        log.info("======> 客户端请求api/v1/gd下的的getComParamList接口结束 <======");
        return result;
    }

    @PostMapping({"getFmeaActivationList"})
    public Result<List<DiagResItemGroupDto>> c(@RequestBody ComActivationVo vo) {
        Result<List<DiagResItemGroupDto>> result = new Result<>();
        try {
            result.setData(this.iZ.getFmeaActivationList(vo.getVin(), vo.getGcidName(), vo.getLocation(), vo.getFmeaName()));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("客户端fmea激活列表接口异常", e);
        } catch (Exception e2) {
            result.setCode(HttpStatus.ERROR);
            result.setMsg(e2.getMessage());
            log.error("客户端fmea激活列表接口异常", e2);
        }
        return result;
    }
}
