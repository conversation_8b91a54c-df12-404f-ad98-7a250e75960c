package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.OtherSeqDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.entity.TesterSequenceEntity;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.service.SequenceService;
import com.geely.gnds.tester.service.impl.SeqServiceImpl;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/sequence"})
@RestController
/* loaded from: SequenceController.class */
public class SequenceController {

    @Autowired
    private SequenceService jq;
    private static final Logger log = LoggerFactory.getLogger(SeqServiceImpl.class);

    @PutMapping({"saveSequence"})
    public Result<Object> a(@RequestBody ReloadDto reloadDto) {
        Result<Object> result = new Result<>();
        this.jq.save(reloadDto);
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }

    @GetMapping({"query"})
    public Result<List<TesterSequenceEntity>> R() {
        Result<List<TesterSequenceEntity>> result = new Result<>();
        result.setData(this.jq.query());
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }

    @GetMapping({"component/other/getSeqList"})
    public Result<List<OtherSeqDto>> k(@RequestParam String vin, String gcid, String gcidName, String location, String wdid) throws Exception {
        log.info("进入sequenceController getSeqList");
        if (ObjectUtils.isEmpty(vin)) {
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        List<OtherSeqDto> seqList = this.jq.getSeqList(vin, gcid, gcidName, location, wdid);
        log.info("sequenceController getSeqList返回数据");
        return new Result().ok(seqList);
    }

    @GetMapping({"/getDebugSeqList"})
    public Result<List<OtherSeqDto>> bR(String vin) throws Exception {
        if (ObjectUtils.isEmpty(vin)) {
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        List<OtherSeqDto> seqList = this.jq.getDebugSeqList(vin);
        return new Result().ok(seqList);
    }

    @GetMapping({"faultTracing/getFaultTracingSeqList"})
    public Result<List<OtherSeqDto>> l(@RequestParam String vin, String gcid, String gcidName, String location, String wdid) throws Exception {
        if (ObjectUtils.isEmpty(vin)) {
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        List<OtherSeqDto> seqList = this.jq.getSeqList(vin, gcid, gcidName, location, wdid);
        return new Result().ok(seqList);
    }

    @GetMapping({"network/getNetworkSeqList"})
    public Result<List<OtherSeqDto>> m(@RequestParam String vin, String gcid, String gcidName, String location, String wdid) throws Exception {
        if (ObjectUtils.isEmpty(vin)) {
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        List<OtherSeqDto> seqList = this.jq.getSeqList(vin, gcid, gcidName, location, wdid);
        return new Result().ok(seqList);
    }
}
