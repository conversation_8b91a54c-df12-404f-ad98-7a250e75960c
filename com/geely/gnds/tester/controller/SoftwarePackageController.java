package com.geely.gnds.tester.controller;

import com.geely.gnds.tester.service.SoftwarePackageService;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/softwarePackage"})
@RestController
/* loaded from: SoftwarePackageController.class */
public class SoftwarePackageController {

    @Autowired
    private SoftwarePackageService js;

    @GetMapping({"/preview"})
    public Result<String> bS(String ossFileName) throws Exception {
        if (ObjectUtils.isEmpty(ossFileName)) {
            throw new Exception(MessageUtils.getMessage("The preview file name cannot be empty"));
        }
        return new Result().ok(this.js.preview(ossFileName));
    }
}
