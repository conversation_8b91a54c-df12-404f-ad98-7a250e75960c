package com.geely.gnds.tester.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/ai/diagnosis"})
@RestController
/* loaded from: AiDiagnosisController.class */
public class AiDiagnosisController {
    private static final Logger log = LoggerFactory.getLogger(AiDiagnosisController.class);

    @Autowired
    private TokenService tokenService;

    @Autowired
    private Cloud cloud;

    /* JADX WARN: Multi-variable type inference failed */
    @GetMapping({"/login"})
    public Result<JsonNode> Q() {
        log.info("======> 客户端开始请求吉利星睿AI登录接口 <======");
        Result<JsonNode> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            String res = this.cloud.N();
            result.setCode(HttpStatus.SUCCESS);
            result.setData(ObjectMapperUtils.getInstance().readValue(res, JsonNode.class));
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00024);
        }
        log.info("======> 吉利星睿AI登录接口，耗时：{}ms <======", Long.valueOf(System.currentTimeMillis() - start));
        return result;
    }
}
