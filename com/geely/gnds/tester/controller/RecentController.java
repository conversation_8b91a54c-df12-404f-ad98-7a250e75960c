package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.service.RecentService;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/recent"})
@RestController
/* loaded from: RecentController.class */
public class RecentController {

    @Autowired
    private RecentService jj;
    private static final Logger log = LoggerFactory.getLogger(RecentController.class);

    @GetMapping({"query"})
    public Result<Object> Y() {
        Result<Object> result = new Result<>();
        log.info("------------------------执行最近车辆列表接口-------------------------------");
        try {
            result.setData(this.jj.query());
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00032);
        }
        return result;
    }
}
