package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.entity.TesterSoftwareEntity;
import com.geely.gnds.tester.service.SoftwareService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/software"})
@RestController
/* loaded from: SoftwareController.class */
public class SoftwareController {

    @Autowired
    private SoftwareService jr;

    @GetMapping({"query"})
    public Result<List<TesterSoftwareEntity>> R() {
        Result<List<TesterSoftwareEntity>> result = new Result<>();
        result.setData(this.jr.query());
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }
}
