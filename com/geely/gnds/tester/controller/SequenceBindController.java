package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.SequenceBindService;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.Result;
import java.util.Date;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/sequence/bind"})
@RestController
/* loaded from: SequenceBindController.class */
public class SequenceBindController {
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(FixSeqController.class);

    @Autowired
    private SequenceBindService jp;

    @GetMapping({"/querySeqList"})
    public Result<Object> Q(@RequestParam String vin, @RequestParam String type) {
        String msg;
        log.info("======> 客户端开始请求api/v1/sequence/bind下的的queryList接口,参数：vin:{},type:{} <======", vin, type);
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        switch (type) {
            case "1":
                msg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00284);
                break;
            default:
                msg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00285);
                break;
        }
        try {
            String finalMsg = msg;
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), MessageUtils.getMessage(finalMsg), Long.valueOf(System.currentTimeMillis()), "Info", ("=== " + MessageUtils.getMessage(finalMsg) + " ===").getBytes());
            });
            log.info("======> 客户端开始调用fixSeqService的queryFixSeqList方法 <======");
            result.setData(this.jp.querySeqList(vin, type));
            log.info("======> 客户端调用fixSeqService的queryFixSeqList方法结束 <======");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(msg);
            log.error(msg, e);
        } catch (Exception e2) {
            String finalMsg2 = msg;
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), MessageUtils.getMessage(finalMsg2), Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, finalMsg2.getBytes());
            });
            GlobalException.a(e2, finalMsg2);
        }
        log.info("======> 客户端请求api/v1/fix下的的queryFixSeqList接口结束 <======");
        return result;
    }
}
