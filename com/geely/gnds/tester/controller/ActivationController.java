package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ActivationService;
import com.geely.gnds.tester.util.Result;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/activation"})
@RestController
/* loaded from: ActivationController.class */
public class ActivationController {
    private static final Logger log = LoggerFactory.getLogger(ActivationController.class);

    @Autowired
    private ActivationService iP;
    private SingletonManager manager = SingletonManager.getInstance();

    @GetMapping({"list"})
    public Result<List<DiagResItemGroupDto>> H(@RequestParam("vin") String vin, @RequestParam("diagnosticPartNumber") String diagnosticPartNumber, @RequestParam(value = "ecuName", required = false) String ecuName) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        Optional.ofNullable(tcpClient).ifPresent(client -> {
            client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), "Info", "=== 获取参数激活列表 ===".getBytes());
        });
        List<DiagResItemGroupDto> list = this.iP.list(vin, StringUtils.cleanPath(diagnosticPartNumber), ecuName);
        return new Result().ok(list);
    }

    @PostMapping({"send"})
    public Result<Object> a(@RequestBody DiagResItemGroupDto activation, @RequestParam("vin") String vin) {
        log.info("======> 客户端开始请求/api/v1/activation下的send接口 <======");
        this.manager.addSeqProcessCount(vin);
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        log.info("======> 根据vin：{}获取tcpClient <======", vin);
        try {
            try {
                Optional.ofNullable(tcpClient).ifPresent(client -> {
                    client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), "Info", "=== 发送激活指令 ===".getBytes());
                });
                this.iP.send(activation, vin);
                this.manager.reduceSeqProcessCount(vin);
            } catch (Exception e) {
                Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                    client2.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, ("发送激活指令异常:" + e.getMessage()).getBytes());
                });
                GlobalException.c(e);
                this.manager.reduceSeqProcessCount(vin);
            }
            log.info("======> 客户端请求/api/v1/activation下的send接口结束 <======");
            return new Result().ok("success");
        } catch (Throwable th) {
            this.manager.reduceSeqProcessCount(vin);
            throw th;
        }
    }

    @PostMapping({"sendRevert"})
    public Result<Object> I(@RequestParam("vin") String vin, @RequestParam("ecuAddress") String ecuAddress, @RequestParam("dataIdentifierId") String dataIdentifierId) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), "Info", "=== 发送UDS1001进行复位 ===".getBytes());
            });
            this.iP.sendRevert(vin, ecuAddress, dataIdentifierId);
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00013), e);
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00013).getBytes());
            });
        }
        return new Result().ok("success");
    }
}
