package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ReloadOrOtherService;
import com.geely.gnds.tester.util.Result;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/reloadOrOther"})
@RestController
/* loaded from: ReloadOrOtherController.class */
public class ReloadOrOtherController {

    @Autowired
    private ReloadOrOtherService jl;

    @Autowired
    private TokenService tokenService;
    private static final Logger log = LoggerFactory.getLogger(ReloadOrOtherController.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @GetMapping({"queryReloadSeqList"})
    public Result<Object> bm(@RequestParam String vin) {
        log.info("进入reloadOrOtherControoler queryReloadSeqList");
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            result.setData(this.jl.queryReloadSeqList(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00035));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00035), e);
        } catch (Exception e2) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "SoftwareReload", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, (TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00035) + e2.getMessage()).getBytes());
            });
            GlobalException.a(e2, TesterErrorCodeEnum.SG00035);
        }
        return result;
    }

    @GetMapping({"queryOtherSeqList"})
    public Result<Object> bC(@RequestParam String vin) {
        log.info("------------------------执行其他类型序列列表接口-------------------------------");
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            result.setData(this.jl.queryOtherSeqList(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00036));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00036), e);
        } catch (Exception e2) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "SoftwareOthers", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, (TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00036) + e2.getMessage()).getBytes());
            });
            GlobalException.a(e2, TesterErrorCodeEnum.SG00036);
        }
        return result;
    }

    @GetMapping({"queryDevelopSeqList"})
    public Result<Object> bD(@RequestParam String vin) {
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            result.setData(this.jl.queryDevelopSeqList(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00187));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00187), e);
        } catch (Exception e2) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "SoftwareOthers", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, (TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00187) + e2.getMessage()).getBytes());
            });
            GlobalException.a(e2, TesterErrorCodeEnum.SG00187);
        }
        return result;
    }

    @PutMapping({"initFlush"})
    public Result<Object> e(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("------------------------执行初始化重载软件刷写接口-------------------------------");
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(initializeUiDto.getVin());
        String reload = "reload";
        if (StringUtils.isNotBlank(initializeUiDto.getTabType())) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), reload.equals(initializeUiDto.getTabType()) ? "SoftwareReload" : "SoftwareOthers", Long.valueOf(System.currentTimeMillis()), "Info", (reload.equals(initializeUiDto.getTabType()) ? "SoftwareReloadOperation" : "SoftwareOthersOperation").getBytes());
                client.geTxtLogger().write(new Date(), reload.equals(initializeUiDto.getTabType()) ? "SoftwareReload" : "SoftwareOthers", Long.valueOf(System.currentTimeMillis()), "Info", initializeUiDto.getSeqCode().getBytes());
            });
            log.info("reloadOrOtherController client.geTxtLogger().write");
        } else {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), "Script", Long.valueOf(System.currentTimeMillis()), "Info", ("Running Script: " + initializeUiDto.getSeqCode()).getBytes());
            });
            log.info("reloadOrOtherController client.geTxtLogger().write");
        }
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            log.info("reloadOrOtherController tokenService.getLoginUser");
            result.setData(this.jl.initFlush(initializeUiDto, loginUser));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("reloadOrOtherController reloadOrOtherService.initFlush");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00037));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00037), e);
        } catch (Exception e2) {
            if (StringUtils.isNotBlank(initializeUiDto.getTabType())) {
                Optional.ofNullable(tcpClient).ifPresent(client3 -> {
                    client3.geTxtLogger().write(new Date(), reload.equals(initializeUiDto.getTabType()) ? "SoftwareReloadOperation" : "SoftwareOthers", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, (TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00037) + e2.getMessage()).getBytes());
                });
            } else {
                Optional.ofNullable(tcpClient).ifPresent(client4 -> {
                    client4.geTxtLogger().write(new Date(), "Script", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, (TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00037) + e2.getMessage()).getBytes());
                });
            }
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00037);
        }
        return result;
    }
}
