package com.geely.gnds.tester.controller;

import com.geely.gnds.tester.service.UploadService;
import com.geely.gnds.tester.util.Result;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"/api/v1"})
@RestController
/* loaded from: UploadController.class */
public class UploadController {

    @Autowired
    private UploadService jL;

    @PostMapping({"/uploadSeq"})
    public Result uploadSeq(@RequestParam("file") MultipartFile file) throws IOException {
        Result result = this.jL.uploadSeq(file);
        return result;
    }

    @PostMapping({"/uploadVbf"})
    public Result uploadVbf(@RequestParam("file") MultipartFile file) throws IOException {
        Result result = this.jL.uploadVbf(file);
        return result;
    }
}
