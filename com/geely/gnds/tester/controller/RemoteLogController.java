package com.geely.gnds.tester.controller;

import com.geely.gnds.tester.dto.RemoteLogCollectionDTO;
import com.geely.gnds.tester.dto.RemoteLogConfigDTO;
import com.geely.gnds.tester.dto.RemoteLogUploadProgressDTO;
import com.geely.gnds.tester.service.RemoteLogService;
import com.geely.gnds.tester.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/remoteLog"})
@RestController
/* loaded from: RemoteLogController.class */
public class RemoteLogController {

    @Autowired
    private RemoteLogService jn;

    @GetMapping({"/queryConfig"})
    public Result<RemoteLogConfigDTO> k(String vin) throws Exception {
        return new Result().ok(this.jn.query(vin));
    }

    @GetMapping({"/detail"})
    public Result<RemoteLogCollectionDTO> bK(String taskId) throws Exception {
        return new Result().ok(this.jn.remoteLogDetail(taskId));
    }

    @PostMapping({"/setLevel"})
    public Result<RemoteLogCollectionDTO> b(String vin, Integer updateIHULogLevel, Integer oldUpdateIHULogLevel, Boolean isIHUOperate, String model) throws Exception {
        return new Result().ok(this.jn.remoteLogSetLevel(vin, updateIHULogLevel, oldUpdateIHULogLevel, isIHUOperate, model));
    }

    @PostMapping({"/setLogConfig"})
    public Result<RemoteLogCollectionDTO> b(String vin, Integer updateIHULogLevel, Integer oldUpdateIHULogLevel, Boolean isIHUOperate, String model, String startTimeIHU, String endTimeIHU) throws Exception {
        return new Result().ok(this.jn.remoteLogSetLogConfig(vin, updateIHULogLevel, oldUpdateIHULogLevel, isIHUOperate, model, startTimeIHU, endTimeIHU));
    }

    @PostMapping({"/setTime"})
    public Result<RemoteLogCollectionDTO> S(String taskId, String startTimeIHU, String endTimeIHU) throws Exception {
        return new Result().ok(this.jn.remoteLogSetTime(taskId, startTimeIHU, endTimeIHU));
    }

    @GetMapping({"/getOssFile"})
    public Result<RemoteLogCollectionDTO> bL(String taskId) throws Exception {
        return new Result().ok(this.jn.remoteLogGetOssPath(taskId));
    }

    @PostMapping({"/cancel"})
    public Result<Boolean> bM(String taskId) throws Exception {
        return new Result().ok(this.jn.remoteLogCancel(taskId));
    }

    @PostMapping({"/getVehicleUploadProgress"})
    public Result<RemoteLogUploadProgressDTO> T(String vin, String ecuName, String taskId) throws Exception {
        return new Result().ok(this.jn.getVehicleUploadProgress(vin, ecuName, taskId));
    }
}
