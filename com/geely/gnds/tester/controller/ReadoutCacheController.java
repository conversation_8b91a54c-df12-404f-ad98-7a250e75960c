package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.TesterReadoutCacheDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.service.ReadoutCacheService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.vo.DtcCacheQueryVo;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/cache"})
@RestController
/* loaded from: ReadoutCacheController.class */
public class ReadoutCacheController {

    @Autowired
    private ReadoutCacheService ji;
    private static final Logger log = LoggerFactory.getLogger(ReadoutCacheController.class);

    @GetMapping({"checkDtcInfo/{vin}"})
    public Result<TesterReadoutCacheDTO> bA(@PathVariable("vin") String vin) {
        log.info("======> 客户端开始请求/api/v1/cache下的checkDtcInfo接口,参数vin:{} <======", vin);
        Result<TesterReadoutCacheDTO> result = new Result<>();
        try {
            result.setData(this.ji.checkDtcInfo(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00029);
        }
        log.info("======> 客户端开始请求/api/v1/cache下的checkDtcInfo接口结束 <======");
        return result;
    }

    @GetMapping({"list/{vin}"})
    public Result<List<TesterReadoutCacheDTO>> bB(@PathVariable("vin") String vin) {
        log.info("======> 客户端开始请求/api/v1/cache下的list接口,参数vin:{} <======", vin);
        Result<List<TesterReadoutCacheDTO>> result = new Result<>();
        try {
            result.setData(this.ji.getLasted(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00030);
        }
        log.info("======> 客户端开始请求/api/v1/cache下的list接口结束 <======");
        return result;
    }

    @PostMapping({"getInfo"})
    public Result a(@RequestBody DtcCacheQueryVo cacheQueryVo) {
        log.info("进入readoutCacheController getInfo，入参【{}】", cacheQueryVo);
        Result<List<DtcInfoDTO>> result = new Result<>();
        try {
            this.ji.getInfo(cacheQueryVo);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            log.error("getInfo接口异常", e);
            result.setCode(HttpStatus.ERROR);
            result.setMsg(TesterErrorCodeEnum.SG00031.value());
            GlobalException.a(e, TesterErrorCodeEnum.SG00031);
        }
        log.info("readoutCacheController getInfo返回数据");
        return result;
    }
}
