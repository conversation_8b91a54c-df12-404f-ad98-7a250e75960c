package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.DrCscNode;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.DiagnosisReasonerService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.vo.DrCreateMaintenanceTaskVO;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/diagnosisReasoner"})
@RestController
/* loaded from: DiagnosisReasonerController.class */
public class DiagnosisReasonerController {
    private static final Logger log = LoggerFactory.getLogger(DiagnosisReasonerController.class);

    @Autowired
    private DiagnosisReasonerService iR;

    @GetMapping({"getCscList"})
    public Result<Object> S() {
        Result<Object> result = new Result<>();
        try {
            List<DrCscNode> cscList = this.iR.getCscList();
            result.ok(cscList);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00240));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00240), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00240), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00240);
        }
        return result;
    }

    @PostMapping({"createMaintenanceTask"})
    public Result<Object> b(@RequestBody DrCreateMaintenanceTaskVO vo) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.createMaintenanceTask(vo);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00241));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00241), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00241), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00241);
        }
        return result;
    }

    @GetMapping({"getExistingMaintenanceTask"})
    public Result<Object> L(String vin, String engineNumber, String caseNo) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.getExistingMaintenanceTask(vin, engineNumber, caseNo);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00251));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00251), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00251), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00251);
        }
        return result;
    }

    @GetMapping({"getMaintenanceTask"})
    public Result<Object> bj(String taskUid) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.getMaintenanceTask(taskUid);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00242));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00242), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00242), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00242);
        }
        return result;
    }

    @GetMapping({"faultTest"})
    public Result<Object> M(String faultUid, String testId, String value) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.faultTest(faultUid, testId, value);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00243));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00243), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00243), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00243);
        }
        return result;
    }

    @GetMapping({"revokeFaultTest"})
    public Result<Object> N(String faultUid, String testId, String value) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.revokeFaultTest(faultUid, testId, value);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00244));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00244), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00244), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00244);
        }
        return result;
    }

    @GetMapping({"executeMaintenancePlanFixed"})
    public Result<Object> b(String faultUid, int caId, int rating, String comment) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.executeMaintenancePlanFixed(faultUid, caId, rating, comment);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00245));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00245), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00245), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00245);
        }
        return result;
    }

    @GetMapping({"executeMaintenancePlanNotFixed"})
    public Result<Object> c(String faultUid, int caId) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.executeMaintenancePlanNotFixed(faultUid, caId);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00245));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00245), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00245), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00245);
        }
        return result;
    }

    @GetMapping({"getMaintenanceTaskCloseOptions"})
    public Result<Object> getMaintenanceTaskCloseOptions() {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.getMaintenanceTaskCloseOptions();
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00247));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00247), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00247), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00247);
        }
        return result;
    }

    @GetMapping({"closeMaintenanceTask"})
    public Result<Object> b(String taskUid, Integer closeOption, String solution) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.closeMaintenanceTask(taskUid, closeOption, solution);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00248));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00248), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00248), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00248);
        }
        return result;
    }

    @GetMapping({"deleteTask"})
    public Result<Object> bk(String taskId) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.deleteTask(taskId);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00249));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00249), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00249), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00249);
        }
        return result;
    }

    @GetMapping({"revokeMaintenancePlan"})
    public Result<Object> b(String faultUid, Integer caId) {
        Result<Object> result = new Result<>();
        try {
            Object task = this.iR.revokeMaintenancePlan(faultUid, caId);
            result.ok(task);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00250));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00250), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00250), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00250);
        }
        return result;
    }
}
