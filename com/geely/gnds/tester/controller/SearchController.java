package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.SearchService;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/search"})
@RestController
/* loaded from: SearchController.class */
public class SearchController {

    @Autowired
    private SearchService jo;
    private static final Logger log = LoggerFactory.getLogger(SearchController.class);

    @GetMapping({"search"})
    public Result<Object> bN(@RequestParam("vin") String vin) {
        log.info("搜索车辆接口入参-----vin={}", vin);
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jo.search(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00016));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00016), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00016);
        }
        log.info("搜索车辆接口-----返回参数");
        return result;
    }

    @GetMapping({"saveVehicle"})
    public Result<Object> bO(@RequestParam("vin") String vin) {
        log.info("保存最近车辆接口入参-----vin={}", vin);
        Result<Object> result = new Result<>();
        try {
            this.jo.saveVehicle(vin);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00038));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00038), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00038);
        }
        return result;
    }
}
