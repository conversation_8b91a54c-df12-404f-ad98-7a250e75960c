package com.geely.gnds.tester.controller;

import com.alibaba.fastjson.JSON;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.file.Base64Utils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.dto.LogCollectionDto;
import com.geely.gnds.tester.dto.LogCollectionProgressDto;
import com.geely.gnds.tester.dto.SupportWorkOrderDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.SupportService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/support"})
@RestController
/* loaded from: SupportController.class */
public class SupportController {
    private static final Logger log = LoggerFactory.getLogger(SupportController.class);

    @Autowired
    private SupportService jt;

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private TokenService tokenService;

    @PostMapping({"/logCollection"})
    public Result<Object> a(@RequestBody LogCollectionDto logCollectionDto) {
        log.info("======> 客户端开始请求api/v1/support下的logCollection接口，入参：{} <======", logCollectionDto);
        Result<Object> result = new Result<>();
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String username = loginUser.getUsername();
        this.testerThread.getPool().execute(() -> {
            try {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                this.jt.logCollection(logCollectionDto, username);
            } catch (Throwable e) {
                log.error("日志收集失败", e);
            }
        });
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }

    @GetMapping({"getLogCollectionProgress"})
    public Result<LogCollectionProgressDto> bT(@RequestParam String id) throws InterruptedException {
        Result<LogCollectionProgressDto> result = new Result<>();
        try {
            Thread.sleep(1000L);
            result.setData(this.jt.getLogCollectionProgress(id));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00054), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00252);
        }
        return result;
    }

    @PostMapping({"/submit"})
    public Result<Object> a(SupportWorkOrderDto supportWorkOrderDto) {
        log.info("======> 客户端开始请求api/v1/support下的submit接口，入参：{} <======", supportWorkOrderDto);
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jt.submitSupportWorkOrder(supportWorkOrderDto));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00054), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00253);
        }
        return result;
    }

    @GetMapping({"/attachment/download"})
    public void attachmentDownload(@RequestParam String attachedFilePath, HttpServletResponse response) {
        try {
            this.jt.attachmentDownload(attachedFilePath, response);
        } catch (Exception e) {
            log.error("下载附件失败", e);
            GlobalException.a(e, TesterErrorCodeEnum.SG00283);
        }
    }

    public static void main(String[] args) throws Exception {
        SupportWorkOrderDto supportWorkOrderDto = new SupportWorkOrderDto();
        supportWorkOrderDto.setId(32164L);
        supportWorkOrderDto.setVin("GNDSTESTWHB900001");
        supportWorkOrderDto.setImageUrl("logCollection/GNDS-GRI/2023-05/gaojiale/321.jpg");
        supportWorkOrderDto.setLogUrl("logCollection/GNDS-GRI/2023-05/gaojiale/321.zip");
        System.out.println(JSON.toJSONString(supportWorkOrderDto));
    }

    public static void bU(String base64) throws IOException {
        try {
            String savePath = new File("D:\\editor").getAbsolutePath() + File.separator + "xmls" + File.separator + "123654987.jpg";
            byte[] imgbytes = Base64Utils.base64DecodeBytes(base64);
            OutputStream out = new FileOutputStream(savePath);
            out.write(imgbytes);
            out.flush();
            out.close();
        } catch (Exception e) {
            log.error("生成图片失败", e);
        }
    }

    @PostMapping({"/saveLog"})
    public Result<Object> b(@RequestBody LogCollectionDto logCollectionDto) {
        log.info("======> 客户端开始请求api/v1/support下的saveLog接口，入参：{} <======", logCollectionDto);
        Result<Object> result = new Result<>();
        try {
            this.jt.saveLog(logCollectionDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00256);
        }
        return result;
    }

    @GetMapping({"/deleteLog"})
    public Result<Object> bV(@RequestParam String id) {
        log.info("======> 客户端开始请求api/v1/support下的deleteLog接口，入参：{} <======", id);
        Result<Object> result = new Result<>();
        try {
            this.jt.deleteLog(id);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00257);
        }
        return result;
    }

    @GetMapping({"/getLogUrl"})
    public Result<Object> bW(@RequestParam String id) {
        log.info("======> 客户端开始请求api/v1/support下的getLogUrl接口，入参：{} <======", id);
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jt.getLogUrl(id));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00257);
        }
        return result;
    }

    @GetMapping({"/queryByPageForClient"})
    public Result<Object> b(@RequestParam int page, @RequestParam int limit, @RequestParam Long userId, @RequestParam String problemDesc, @RequestParam String consultContent) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jt.queryByPageForClient(page, limit, userId, problemDesc, consultContent));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00257);
        }
        return result;
    }

    @GetMapping({"/queryNotice"})
    public Result<Object> i(@RequestParam Long userId) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jt.queryNotice(userId));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00257);
        }
        return result;
    }
}
