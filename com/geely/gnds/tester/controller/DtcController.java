package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.TechnicalDTO;
import com.geely.gnds.tester.dto.TechnicalReqDTO;
import com.geely.gnds.tester.dto.dtc.DtcCalibrationInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcDmeGeneralInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcExtendedDataRecordDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.CloudInterfaceException;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.DtcService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.vo.DtcDetailReqVO;
import com.geely.gnds.tester.vo.DtcDetailedVO;
import com.geely.gnds.tester.vo.DtcExtendedDataVO;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/dtc"})
@RestController
/* loaded from: DtcController.class */
public class DtcController {

    @Autowired
    private DtcService eA;

    @Autowired
    private Cloud cloud;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(DtcController.class);

    @GetMapping({"/data/general"})
    public Result<DtcDmeGeneralInfoDTO> m(String diagnosticNumber, String dtcId, String vin, String ecuName) {
        log.info("======> 客户端开始请求api/v1/dtc/data/general下的getDtcGeneralInfo接口 <======");
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        if (ObjectUtils.isEmpty(dtcId)) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138).getBytes());
            });
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        Result<DtcDmeGeneralInfoDTO> result = new Result<>();
        try {
            log.info("======> 客户端开始调用dtcService下的getDtcGeneralInfo接口 <======");
            result.ok(this.eA.getGeneralInfo(StringUtils.cleanPath(diagnosticNumber), dtcId, vin, ecuName));
            log.info("======> 客户端调用dtcService下的getDtcGeneralInfo接口结束 <======");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00019));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00019), e);
        } catch (Exception e2) {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00019).getBytes());
            });
            GlobalException.a(e2, TesterErrorCodeEnum.SG00019);
        }
        log.info("======> 客户端请求api/v1/dtc/data/general下的getDtcGeneralInfo接口结束 <======");
        return result;
    }

    @GetMapping({"/data/calibration/general"})
    public Result<DtcCalibrationInfoDTO> N(String diagnosticNumber, String dtcId) {
        if (ObjectUtils.isEmpty(dtcId)) {
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        Result<DtcCalibrationInfoDTO> result = new Result<>();
        try {
            result.ok(this.eA.getCalibrationGeneralInfo(diagnosticNumber, dtcId));
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00020));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00020), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00020);
        }
        return result;
    }

    @GetMapping({"/data/getDtcList"})
    public Result<Object> n(@RequestParam("type") String type, @RequestParam(value = "diagnosticNumber", required = false) String diagnosticNumber, @RequestParam("vin") String vin, @RequestParam(value = "ecuName", required = false) String ecuName) {
        log.info("======> 客户端开始请求api/v1/dtc/data的getDtcList接口 <======");
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            log.info("======> 客户端开始调用dtcService的getDtcList接口 <======");
            result.setData(this.eA.getDtcList(StringUtils.cleanPath(diagnosticNumber), type, vin, ecuName));
            log.info("======> 客户端调用dtcService的getDtcList接口结束 <======");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (CloudInterfaceException e) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200).getBytes());
            });
            GlobalException.c(e);
        } catch (UnAuthException e2) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00021));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00021), e2);
        } catch (Exception e3) {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00021).getBytes());
            });
            GlobalException.a(e3, TesterErrorCodeEnum.SG00021);
        }
        log.info("======> 客户端请求api/v1/dtc/data的getDtcList接口结束 <======");
        return result;
    }

    @GetMapping({"/data/extended/detail"})
    public Result<DtcDetailedVO> a(DtcDetailReqVO dtcDetailReqVO) throws Exception {
        Date date;
        if (ObjectUtils.isEmpty(dtcDetailReqVO.getCreateGlobalTime())) {
            date = DateUtils.parseDate(dtcDetailReqVO.getCreateTime(), new String[]{DateUtils.YYYY_MM_DD_HH_MM_SS_SSS});
        } else {
            date = DateUtils.parseDate(dtcDetailReqVO.getCreateGlobalTime(), new String[]{DateUtils.YYYY_MM_DD_HH_MM_SS_SSS});
        }
        log.info("======> 客户端开始请求api/v1/dtc/data/extended/detail下的getDtcExtendedDetailInfo2接口 <======");
        FdTcpClient tcpClient = this.manager.getFdTcpClient(dtcDetailReqVO.getVin());
        if (ObjectUtils.isEmpty(dtcDetailReqVO.getDtcId())) {
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        log.info("======> 客户端开始调用dtcService下的getDetailedInfo方法 <======");
        DtcDetailedVO detailedInfo = this.eA.getDetailedInfo(dtcDetailReqVO.getTitle(), StringUtils.cleanPath(dtcDetailReqVO.getDiagnosticNumber()), dtcDetailReqVO.getDtcId(), dtcDetailReqVO.getVin(), dtcDetailReqVO.getEcuAddress(), dtcDetailReqVO.getEcuName(), date, dtcDetailReqVO.getGlobalTime());
        log.info("======> 客户端调用dtcService下的getDetailedInfo结束 <======");
        if (ObjectUtils.isEmpty(detailedInfo)) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00139).getBytes());
            });
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00139));
        }
        log.info("======> 客户端请求api/v1/dtc/data/extended/detail下的getDtcExtendedDetailInfo2接口结束 <======");
        return new Result().ok(detailedInfo);
    }

    @GetMapping({"/data/extended/detail/refresh"})
    public Result<DtcDetailedVO> o(@RequestParam("diagnosticNumber") String diagnosticNumber, @RequestParam("dtcId") String dtcId, @RequestParam("vin") String vin, @RequestParam("ecuAddress") String ecuAddress) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        Optional.ofNullable(tcpClient).ifPresent(client -> {
            client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", "GetFDC10Operation".getBytes());
        });
        if (ObjectUtils.isEmpty(dtcId)) {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138).getBytes());
            });
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        DtcDetailedVO vo = new DtcDetailedVO();
        this.manager.addSeqProcessCount(vin);
        boolean flag = false;
        try {
            try {
                DtcExtendedDataRecordDTO dto = this.eA.getRefreshDtcExtendedDetailInfo(diagnosticNumber, dtcId, vin, ecuAddress);
                if (ObjectUtils.isEmpty(dto)) {
                    flag = true;
                } else {
                    vo.setExtendedData(this.eA.covertExtendedDataVO(dto, this.eA.getDtcStatus(vin, dtcId, ecuAddress), new DtcExtendedDataVO()));
                }
            } catch (Exception e) {
                GlobalException.c(e);
                this.manager.reduceSeqProcessCount(vin);
            }
            if (flag) {
                Optional.ofNullable(tcpClient).ifPresent(client3 -> {
                    client3.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00140).getBytes());
                });
                return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00140));
            }
            return new Result().ok(vo);
        } finally {
            this.manager.reduceSeqProcessCount(vin);
        }
    }

    @GetMapping({"/data/getTechnologyList"})
    public Result<List<TechnicalDTO>> c(TechnicalReqDTO technicalReqDTO) {
        if (ObjectUtils.isEmpty(technicalReqDTO.getDtcCodes()) || ObjectUtils.isEmpty(technicalReqDTO.getVin())) {
            return new Result().error(HttpStatus.BAD_REQUEST, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00138));
        }
        Result<List<TechnicalDTO>> result = new Result<>();
        try {
            result.ok(this.eA.getTechnologyList(technicalReqDTO));
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00020));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00020), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00264);
        }
        return result;
    }
}
