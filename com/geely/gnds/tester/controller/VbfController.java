package com.geely.gnds.tester.controller;

import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.dto.VbfDto;
import com.geely.gnds.tester.service.VbfService;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"api/v1/vbf"})
@RestController
/* loaded from: VbfController.class */
public class VbfController {

    @Autowired
    private FileCache fileCache;

    @Autowired
    private VbfService jM;
    private static final Logger LOG = LoggerFactory.getLogger(VbfController.class);

    @GetMapping({"/updateDefault"})
    public Result<Object> b(@RequestParam int size, @RequestParam String path) throws Exception {
        LOG.info("updateDefault接口入参：size【{}】，path【{}】", Integer.valueOf(size), path);
        this.jM.update(size, path);
        return new Result<>();
    }

    @GetMapping({"/query"})
    public Result<Object> R() throws Exception {
        return new Result().ok(Boolean.valueOf(this.jM.queryDefaultSize()));
    }

    @GetMapping({"/ignore"})
    public Result<Object> ah() throws Exception {
        this.jM.clean();
        return new Result<>();
    }

    @GetMapping({"/recoveryDefault"})
    public Result<Object> ai() throws Exception {
        this.jM.recoveryDefault();
        return new Result<>();
    }

    @GetMapping({"/getDefault"})
    public Result<VbfDto> getDefault() throws Exception {
        VbfDto vbfDto = this.jM.getDefault();
        return new Result().ok(vbfDto);
    }

    @GetMapping({"/delete"})
    public Result<Boolean> aj() throws Exception {
        this.fileCache.J();
        return new Result().ok(true);
    }
}
