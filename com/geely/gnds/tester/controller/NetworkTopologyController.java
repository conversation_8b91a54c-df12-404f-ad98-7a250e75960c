package com.geely.gnds.tester.controller;

import com.geely.gnds.tester.service.NetworkTopologyService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.vo.DiaNetworkEcuVo;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"api/v1/networkTopology"})
@RestController
/* loaded from: NetworkTopologyController.class */
public class NetworkTopologyController {

    @Autowired
    NetworkTopologyService jc;

    @GetMapping({"getNetworkTopology"})
    public void a(@RequestParam("fileId") Long fileId, String vin, HttpServletResponse response) throws Exception {
        this.jc.getNetworkTopology(fileId, response, vin);
    }

    @GetMapping({"getEcuRelation"})
    public Result<DiaNetworkEcuVo> getEcuRelation(@RequestParam("vin") String vin) throws Exception {
        return this.jc.getEcuRelation(vin);
    }
}
