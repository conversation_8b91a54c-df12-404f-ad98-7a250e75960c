package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.EcuMessageDTO;
import com.geely.gnds.tester.dto.SysDictDTO;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.service.TesterConfigService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/testerConfig"})
@RestController
/* loaded from: TesterConfigController.class */
public class TesterConfigController {

    @Autowired
    private TesterConfigService cd;

    @Autowired
    private Cloud cloud;

    @GetMapping({"getConfig"})
    public Result<TesterConfigDto> getConfig() {
        Result<TesterConfigDto> result = new Result<>();
        TesterConfigDto config = this.cd.getConfig();
        result.setData(config);
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }

    @GetMapping({"getVdnImage"})
    public Result<String> getVdnImage(String vin) throws Exception {
        Result<String> result = this.cd.getVdnImage(vin);
        return result;
    }

    @GetMapping({"/color"})
    public Result<Map<String, Object>> getAllColor() throws Exception {
        String allColor = this.cloud.getAllColor();
        if (StringUtils.isEmpty(allColor)) {
            return new Result().ok(null);
        }
        return new Result().ok(ObjectMapperUtils.jsonStr2Map(allColor));
    }

    @GetMapping({"/ecuList"})
    public Result<List<EcuMessageDTO>> R(String vin, String platform) throws Exception {
        String allEcuConfig = this.cloud.A(vin, platform);
        if (StringUtils.isEmpty(allEcuConfig)) {
            throw new CustomException("云端pincode无数据");
        }
        return new Result().ok(ObjectMapperUtils.jsonStr2List(allEcuConfig, EcuMessageDTO.class));
    }

    @GetMapping({"/getDictData"})
    public Result<List<SysDictDTO>> f(String dictType, Long parentId) throws Exception {
        String data = this.cloud.c(dictType, parentId);
        if (StringUtils.isEmpty(data)) {
            throw new CustomException("云端字典无数据");
        }
        return new Result().ok(ObjectMapperUtils.jsonStr2List(data, SysDictDTO.class));
    }

    @GetMapping({"/init"})
    public Result<Boolean> aa() throws Exception {
        TokenManager.setSecret(this.cloud.getSecret());
        return new Result().ok(true);
    }
}
