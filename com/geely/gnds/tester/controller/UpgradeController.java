package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.component.SoftwareDownloadSeq_4;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.exception.file.FileCheckMd5FailException;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.cache.Download;
import com.geely.gnds.tester.cache.DownloadManager;
import com.geely.gnds.tester.dto.DownloadDto;
import com.geely.gnds.tester.dto.DownloadProgressDto;
import com.geely.gnds.tester.dto.EcuDto;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.UpgradeDto;
import com.geely.gnds.tester.enums.SoftwareTypeEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.CloudInterfaceException;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.UpgradeService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.net.SocketTimeoutException;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/upgrade"})
@RestController
/* loaded from: UpgradeController.class */
public class UpgradeController {

    @Autowired
    private UpgradeService jK;

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private TokenService tokenService;
    private static final Logger log = LoggerFactory.getLogger(UpgradeController.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @GetMapping({"query"})
    public Result<Object> a(@RequestParam String vin, SoftwareTypeEnum bizType, String bssId) {
        log.info("------------------------执行获取升级软件列表接口-------------------------------");
        if (ObjectUtils.isEmpty(bizType)) {
            bizType = SoftwareTypeEnum.TARGET_BSS;
        }
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            UpgradeDto query = this.jK.query(vin, bizType, bssId);
            log.info("upgradeController query upgradeService.query");
            result.setData(query);
            result.setMsg("success");
            result.setCode(HttpStatus.SUCCESS);
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), "Info", "GetSoftwareListOperation".getBytes());
                List<EcuDto> ecuDtoList = query.getEcuInstallationInstructions();
                if (!CollectionUtils.isEmpty(ecuDtoList)) {
                    ecuDtoList.forEach(ecu -> {
                        client.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), "Info", ecu.getEcuName().getBytes());
                    });
                }
            });
            log.info("upgradeController query Optional.ofNullable(tcpClient)");
        } catch (CloudInterfaceException e) {
            result.setCode(HttpStatus.SETTLEMENT_FAIL);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200) + e.getMessage());
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200), e);
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, (TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200) + e.getMessage()).getBytes());
            });
        } catch (UnAuthException e2) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00050));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00050), e2);
        } catch (Exception e3) {
            Optional.ofNullable(tcpClient).ifPresent(client3 -> {
                client3.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00050).getBytes());
            });
            GlobalException.a(e3, TesterErrorCodeEnum.SG00050);
        }
        log.info("upgradeController query 返回结果");
        return result;
    }

    @PostMapping({"/download"})
    public Result<Object> a(@RequestBody DownloadDto downloadDto) {
        Result<Object> result = new Result<>();
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        FdTcpClient tcpClient = this.manager.getFdTcpClient(downloadDto.getVin());
        List<EcuDto> ecuDtoList = downloadDto.getEcuDtoList();
        Optional.ofNullable(tcpClient).ifPresent(client -> {
            client.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), "Info", "OrderSoftwareOperation".getBytes());
        });
        ecuDtoList.forEach(ecu -> {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), "Info", ecu.getEcuName().getBytes());
            });
        });
        LoginUser loginUser = this.tokenService.getLoginUser();
        this.testerThread.getPool().execute(() -> {
            int retry = 3;
            DownloadManager instance = DownloadManager.getInstance();
            Download download = instance.Y(downloadDto.getId());
            while (retry > 0) {
                try {
                    DownloadDto downloadDtoCopy = (DownloadDto) a(downloadDto);
                    if (loginUser != null) {
                        ThreadLocalUtils.CURRENT_USER.set(loginUser);
                    }
                    this.jK.download(downloadDtoCopy);
                    retry = 0;
                } catch (CustomException e) {
                    retry = 0;
                    log.error("完整性校验出错", e);
                    download.setIntegrity(false);
                } catch (IOException e2) {
                    log.error("下载失败：", e2);
                    retry--;
                    if (e2.getMessage().contains("磁盘空间不足")) {
                        retry = 1;
                        download.setNoSpace(true);
                    }
                    if (download.getSupportDownload().booleanValue()) {
                        if (retry == 0) {
                            download.setDisconnection(true);
                            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                                client2.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00052).getBytes());
                            });
                            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00052), e2);
                        }
                        log.info("升级下载VBF失败，尝试第{}次", Integer.valueOf(3 - retry));
                    }
                } catch (Exception e3) {
                    retry = 0;
                    log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00051), e3);
                    Optional.ofNullable(tcpClient).ifPresent(client3 -> {
                        client3.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00051).getBytes());
                    });
                    download.setErrorCodeEnum(TesterErrorCodeEnum.SG00051);
                }
            }
        });
        return result;
    }

    public static <T extends Serializable> T a(T obj) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(bos);
        oos.writeObject(obj);
        ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bis);
        return (T) ois.readObject();
    }

    @PostMapping({"upgradeTester"})
    public Result<Object> b(@RequestBody DownloadDto downloadDto) {
        Result<Object> result = new Result<>();
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        LoginUser loginUser = this.tokenService.getLoginUser();
        this.testerThread.getPool().execute(() -> {
            if (loginUser != null) {
                try {
                    ThreadLocalUtils.CURRENT_USER.set(loginUser);
                } catch (FileCheckMd5FailException e) {
                    DownloadManager instance = DownloadManager.getInstance();
                    instance.Y(downloadDto.getId()).setCheckMd5(false);
                    return;
                } catch (SocketTimeoutException e2) {
                    DownloadManager instance2 = DownloadManager.getInstance();
                    instance2.Y(downloadDto.getId());
                    log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00259), e2);
                    return;
                } catch (Exception e3) {
                    DownloadManager instance3 = DownloadManager.getInstance();
                    Download download = instance3.Y(downloadDto.getId());
                    download.setDisconnection(true);
                    download.setErrorCodeEnum(TesterErrorCodeEnum.SG00259);
                    log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00259), e3);
                    return;
                }
            }
            log.info("开始升级客户端，data={}", downloadDto);
            this.jK.upgradeTester(downloadDto);
        });
        return result;
    }

    @GetMapping({"getDownloadId"})
    public Result<String> getDownloadId() {
        Result<String> result = new Result<>();
        try {
            result.setData(this.jK.getDownloadId());
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00053));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00053), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00053);
        }
        return result;
    }

    @GetMapping({"getDownloadProgress"})
    public Result<DownloadProgressDto> ch(@RequestParam String id) {
        log.info("进入upgradeController getDownloadProgress");
        Result<DownloadProgressDto> result = new Result<>();
        try {
            result.setData(this.jK.getDownloadProgress(id));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00054), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00054);
        }
        log.info("upgradeController getDownloadProgress返回数据");
        return result;
    }

    @GetMapping({"stopDownload"})
    public Result<Download> ci(@RequestParam String id) {
        Result<Download> result = new Result<>();
        try {
            log.info("用户取消下载");
            result.setData(this.jK.stopDownload(id));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00055));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00055), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00055);
        }
        return result;
    }

    @PutMapping({"/initFlush"})
    public Result<Object> e(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("进入upgradeController initFlush");
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(initializeUiDto.getVin());
        Optional.ofNullable(tcpClient).ifPresent(client -> {
            client.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), "Info", "SWDLOperation".getBytes());
        });
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            result.setData(this.jK.initFlush(initializeUiDto, loginUser));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00056));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00056), e);
        } catch (Exception e2) {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00056).getBytes());
            });
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00056);
        }
        return result;
    }
}
