package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.SynchronousService;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/synchronize"})
@RestController
/* loaded from: SynchronizeController.class */
public class SynchronizeController {

    @Autowired
    private SynchronousService hn;

    @Autowired
    private TokenService tokenService;
    private static final Logger log = LoggerFactory.getLogger(SynchronizeController.class);

    @GetMapping({"synchronousSeqs"})
    public Result<Object> Z() {
        Result<Object> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            this.hn.synchronousSeqs(loginUser.getUsername());
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00048));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00048), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00048);
        }
        return result;
    }
}
