package com.geely.gnds.tester.controller;

import cn.hutool.core.util.NumberUtil;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.DiagDmeDidDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ParamService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/param"})
@RestController
/* loaded from: ParamController.class */
public class ParamController {
    private static final Logger log = LoggerFactory.getLogger(ParamController.class);

    @Autowired
    private ParamService jf;
    private SingletonManager manager = SingletonManager.getInstance();

    @GetMapping({"list"})
    public Result<Map<String, Object>> d(@RequestParam("vin") String vin, @RequestParam("ecuName") String ecuName, @RequestParam("diagnosticPartNumber") String diagnosticPartNumber, String p, String did, String page) {
        log.info("/api/v1/param/list 进入controller");
        try {
            return new Result().ok(this.jf.list(vin, StringUtils.cleanPath(diagnosticPartNumber), ecuName, p, did, PageRequest.of(NumberUtil.parseInt(page), 50)));
        } catch (Exception e) {
            GlobalException.c(e);
            log.info("/api/v1/param/list controller返回结果");
            return null;
        }
    }

    @GetMapping({"getFmeaParamList"})
    public Result<List<DiagResItemGroupDto>> h(@RequestParam("vin") String vin, @RequestParam("fmeaName") String fmeaName, String p, String did, String page) {
        try {
            return new Result().ok(this.jf.getFmeaParamList(vin, fmeaName, p, did, PageRequest.of(NumberUtil.parseInt(page), 50)));
        } catch (Exception e) {
            GlobalException.c(e);
            return null;
        }
    }

    /* JADX WARN: Can't wrap try/catch for region: R(6:(11:3|14|12|13|27|39|28|29|37|38|20)|39|28|29|37|38) */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x00c6, code lost:
    
        r10 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x00c8, code lost:
    
        java.util.Optional.of(r0).ifPresent((v1) -> { // java.util.function.Consumer.accept(java.lang.Object):void
            b(r1, v1);
        });
        com.geely.gnds.tester.exception.GlobalException.c(r10);
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x00de, code lost:
    
        r4.manager.reduceSeqProcessCount(r6);
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v45, types: [java.util.List] */
    @org.springframework.web.bind.annotation.PostMapping({"readParam"})
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public com.geely.gnds.tester.util.Result<java.util.List<com.geely.gnds.tester.dto.DiagResItemParseResultDto>> f(@org.springframework.web.bind.annotation.RequestBody java.util.List<com.geely.gnds.tester.dto.DiagResItemGroupDto> r5, @org.springframework.web.bind.annotation.RequestParam("vin") java.lang.String r6) {
        /*
            Method dump skipped, instructions count: 301
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.controller.ParamController.f(java.util.List, java.lang.String):com.geely.gnds.tester.util.Result");
    }

    @GetMapping({"getDidDme"})
    public Result<DiagDmeDidDto> e(@RequestParam("vin") String vin, @RequestParam("identifier") String identifier, @RequestParam("parameterName") String parameterName, @RequestParam(value = "diagnosticPartNumber", required = false) String diagnosticPartNumber, @RequestParam(value = "ecuName", required = false) String ecuName, @RequestParam(value = "type", required = false) String type) {
        log.info("======> 客户端开始请求api/v1/param下的的getDidDme接口 <======");
        Result<DiagDmeDidDto> result = new Result<>();
        try {
            log.info("======> 客户端开始调用paramService.getDidDme方法 <======");
            DiagDmeDidDto didDme = this.jf.getDidDme(vin, StringUtils.cleanPath(identifier), parameterName, diagnosticPartNumber, ecuName, type);
            log.info("======> 客户端调用paramService.getDidDme方法结束 <======");
            return result.ok(didDme);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00028));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00028), e);
            log.info("======> 客户端请求api/v1/param下的的getDidDme接口结束 <======");
            return result;
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00028);
            log.info("======> 客户端请求api/v1/param下的的getDidDme接口结束 <======");
            return result;
        }
    }

    @PostMapping({"initPinCode"})
    public Result<Object> Q(@RequestParam("vin") String vin, @RequestParam("diagnosticPartNumber") String diagnosticPartNumber, @RequestParam(value = "ecuName", required = false) String ecuName) {
        log.info("/api/v1/param/initPinCode 进入controller");
        this.jf.initPinCode(vin, StringUtils.cleanPath(diagnosticPartNumber), ecuName);
        log.info("/api/v1/param/initPinCode 返回参数");
        return new Result().ok("success");
    }

    @GetMapping({"exportReadResult"})
    public void exportReadParamResult(HttpServletResponse response) {
        this.jf.exportReadParamResult(response);
    }
}
