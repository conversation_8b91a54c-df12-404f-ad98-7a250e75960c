package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.ReleaseNoteDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.ReleaseNoteService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/release/note"})
@RestController
/* loaded from: ReleaseNoteController.class */
public class ReleaseNoteController {

    @Autowired
    private ReleaseNoteService jk;
    private static final Logger log = LoggerFactory.getLogger(ReleaseNoteController.class);

    @GetMapping({"list"})
    public Result<List<ReleaseNoteDTO>> g(Long brandId) {
        try {
            List<ReleaseNoteDTO> list = this.jk.getList();
            return new Result().ok(list);
        } catch (UnAuthException e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00034), e);
            return new Result().error(HttpStatus.UNAUTHORIZED, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00034));
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00034);
            return null;
        }
    }
}
