package com.geely.gnds.tester.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DroRecordDTO;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ConnectedService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.Result;
import com.github.pagehelper.PageInfo;
import java.io.File;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"/api/v1/connected"})
@RestController
/* loaded from: ConnectedController.class */
public class ConnectedController {

    @Autowired
    private ConnectedService hr;

    @Autowired
    private TokenService tokenService;
    private static final Logger log = LoggerFactory.getLogger(ConnectedController.class);

    @Autowired
    Cloud cloud;

    @GetMapping({"query"})
    public Result<Object> R() {
        Result<Object> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            log.info("执行connected/query, 行号77耗时{}毫秒", Long.valueOf(System.currentTimeMillis() - start));
            String username = loginUser.getUsername();
            List<VehicleDto> query = this.hr.query(username);
            log.info("执行connected/query, 行号80耗时{}毫秒", Long.valueOf(System.currentTimeMillis() - start));
            SingletonManager manager = SingletonManager.getInstance();
            Set<String> managerVehicles = manager.getVehicles(username);
            if (!CollectionUtils.isEmpty(managerVehicles)) {
                Iterator<VehicleDto> iterator = query.iterator();
                while (iterator.hasNext()) {
                    VehicleDto vehicleDto = iterator.next();
                    boolean connect = vehicleDto.getConnect().booleanValue();
                    String vin = vehicleDto.getVin();
                    if (connect && !managerVehicles.contains(vin)) {
                        iterator.remove();
                    }
                }
            }
            log.info("执行connected/query, 行号96耗时{}毫秒", Long.valueOf(System.currentTimeMillis() - start));
            result.setData(query);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00014));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00014), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00014);
        }
        return result;
    }

    @PostMapping({"readVirtual"})
    public Result<VehicleDto> c(@RequestParam("file") MultipartFile file) {
        Result<VehicleDto> result = new Result<>();
        try {
            result.setData(this.hr.queryVirtualVehicle(file));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00016));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00016), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00016);
        }
        return result;
    }

    @GetMapping({"getVehicleInfo"})
    public Result<Object> bd(@RequestParam("vin") String vin) {
        log.info("查询车辆详细信息接口入参-----vin={}", vin);
        Result<Object> result = new Result<>();
        try {
            result.setData(this.hr.getVehicleInfo(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00016));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00016), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00016);
        }
        return result;
    }

    @GetMapping({"getVehicle"})
    public Result<Object> be(@RequestParam("vin") String vin) {
        log.info("查询车辆详细信息接口入参-----vin={}", vin);
        Result<Object> result = new Result<>();
        try {
            result.setData(this.hr.getVehicle(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00016));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00016), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00016);
        }
        return result;
    }

    @PostMapping({"disconnect"})
    public Result<Object> m(@RequestParam("vin") String vin) {
        log.info("======> 客户端开始请求api/v1/connected下的disconnect断开车辆连接接口，入参vin：{} <======", vin);
        Result<Object> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            log.info("======> 客户端获取用户信息，耗时：{}ms <======", Long.valueOf(System.currentTimeMillis() - start));
            String username = loginUser.getUsername();
            this.hr.disconnect(username, vin);
            log.info("======> 客户端断开用户:{}，vin:{}的车辆连接，耗时：{}ms <======", new Object[]{username, vin, Long.valueOf(System.currentTimeMillis() - start)});
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        log.info("======> 客户端请求api/v1/connected下的disconnect断开车辆连接接口结束，入参vin：{} <======", vin);
        return result;
    }

    @PutMapping({"connectTcp"})
    public Result<Object> a(@RequestBody VehicleDto vehicleDto) {
        log.info("------------------------执行车辆连接接口-------------------------------");
        log.info("车辆连接接口入参----->{}", JSON.toJSON(vehicleDto));
        Result<Object> result = new Result<>();
        try {
            this.hr.connectTcp(vehicleDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015) + DoipAddressManager.getInstance().getIps());
        }
        return result;
    }

    @PutMapping({"connectCreate"})
    public Result<String> d(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("======> 客户端开始请求/api/v1/connected下的connectCreate接口 入口参数initializeUiDto:{}<======", JSON.toJSON(initializeUiDto));
        Result<String> result = new Result<>();
        try {
            log.info("======> 客户端开始获取登录用户的信息 <======");
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            log.info("======> 客户端获取登录用户的信息结束 <======");
            loginUser.getUsername();
            log.info("======> 客户端开始调用connectedService.connectCreate方法 <======");
            String initUi = this.hr.connectCreate(initializeUiDto, loginUser);
            log.info("======> 客户端调用connectedService.connectCreate方法结束 <======");
            result.setData(initUi);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            if (!StringUtils.isBlank(initUi)) {
                log.info("------------------------车辆连接脚本成功-------------------------------");
            } else {
                log.info("------------------------车辆连接脚本失败-------------------------------");
            }
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00018));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00018), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00018);
        }
        return result;
    }

    @PostMapping({"connectedVehicle"})
    public Result<Object> bf(@RequestParam("vin") String vin) {
        log.info("======> 客户端开始请求api/v1/connected下的connectedVehicle断开车辆连接接口，入参vin：{} <======", vin);
        Result<Object> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            log.info("======> 客户端获取用户信息，耗时：{}ms <======", Long.valueOf(System.currentTimeMillis() - start));
            String username = loginUser.getUsername();
            this.hr.connected(username, vin);
            log.info("======> 客户端已连接车两判断是否是同一用户:{}，vin:{}的车辆连接，耗时：{}ms <======", new Object[]{username, vin, Long.valueOf(System.currentTimeMillis() - start)});
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        log.info("======> 客户端请求api/v1/connected下的connected断开车辆连接接口结束，入参vin：{} <======", vin);
        return result;
    }

    @PostMapping({"getValveBodyAssemblyData"})
    public Result<Object> c(@RequestParam("pCode") String pCode, @RequestParam("tCode") String tCode, @RequestParam("vCode") String vCode, @RequestParam("pCodePcm") String pCodePcm, @RequestParam("tCodePcm") String tCodePcm, @RequestParam("vCodePcm") String vCodePcm) {
        Result<Object> result = new Result<>();
        System.currentTimeMillis();
        try {
            String replacePcmAssemblyData = this.cloud.getValveBodyAssemblyData(pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm);
            result.setData(replacePcmAssemblyData);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        return result;
    }

    @PostMapping({"updatePcmBind"})
    public Result<Object> c(@RequestParam("pCode") String pCode, @RequestParam("tCode") String tCode, @RequestParam("vCode") String vCode, @RequestParam("pCodePcm") String pCodePcm, @RequestParam("tCodePcm") String tCodePcm, @RequestParam("vCodePcm") String vCodePcm, @RequestParam("user") String user) {
        Result<Object> result = new Result<>();
        System.currentTimeMillis();
        try {
            this.cloud.b(pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm, user);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        return result;
    }

    @PostMapping({"getReplacePcmAssemblyData"})
    public Result<Object> l(@RequestParam("pCode") String pCode, @RequestParam("tCode") String tCode, @RequestParam("vCode") String vCode, @RequestParam("type") String type) throws Exception {
        String res;
        String code;
        String msg;
        Result<Object> result = new Result<>();
        System.currentTimeMillis();
        try {
            res = this.cloud.getReplacePcmDataByType(pCode, tCode, vCode, type);
            code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
            msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        if ("0".equals(code)) {
            String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
            result.setData(data);
            return result;
        }
        throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00208) + ":" + msg);
    }

    @PostMapping({"successUpload"})
    public Result<Boolean> bg(String vin) throws Exception {
        if (StringUtils.isEmpty(vin)) {
            return new Result().ok(false);
        }
        return new Result().ok(this.hr.connectSuccessUploadServer(vin));
    }

    @GetMapping({"/droHistoryPage"})
    public Result<PageInfo<DroRecordDTO>> d(@RequestParam Map<String, Object> params) throws Exception {
        PageInfo<DroRecordDTO> pageList = this.cloud.c(params);
        return new Result().ok(pageList);
    }

    @GetMapping({"/getDroUrl"})
    public Result<String> bh(String path) throws Exception {
        return new Result().ok(this.cloud.aY(path));
    }

    @GetMapping(value = {"/getDroJson"}, produces = {"application/json;charset=UTF-8"})
    public Result<String> bi(String path) throws Exception {
        if (StrUtil.endWith(path, ConstantEnum.EXT_ZIP)) {
            String name = path.substring(path.lastIndexOf("/") + 1).replace(ConstantEnum.EXT_ZIP, ConstantEnum.JSON);
            File zipPath = AppConfig.getDroCacheFile(DigestUtil.md5Hex(path) + ConstantEnum.EXT_ZIP);
            File jsonPath = AppConfig.getDroCacheFile(DigestUtil.md5Hex(path));
            if (FileUtil.isFile(jsonPath + File.separator + name)) {
                return new Result().ok(FileUtil.readString(jsonPath + File.separator + name, StandardCharsets.UTF_8));
            }
            String ossDroUrl = this.cloud.aY(path);
            long size = HttpUtil.downloadFile(ossDroUrl, zipPath);
            if (size > 0) {
                ZipUtil.unzip(zipPath, jsonPath, StandardCharsets.UTF_8);
                return new Result().ok(FileUtil.readString(jsonPath + File.separator + name, StandardCharsets.UTF_8));
            }
            return new Result().error("download file fail");
        }
        String ossDroUrl2 = this.cloud.aY(path);
        return new Result().ok(FileUtil.readString(new URL(ossDroUrl2), StandardCharsets.UTF_8));
    }
}
