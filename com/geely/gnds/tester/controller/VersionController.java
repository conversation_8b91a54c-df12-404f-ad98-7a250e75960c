package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.dto.ReleaseNoteDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.VersionService;
import com.geely.gnds.tester.util.Result;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/version"})
@RestController
/* loaded from: VersionController.class */
public class VersionController {

    @Autowired
    private VersionService jQ;
    private static final Logger log = LoggerFactory.getLogger(VersionController.class);

    @GetMapping({"getVersionInfo"})
    public Result<Object> getVersionInfo() {
        log.info("------------------------执行获取版本升级信息接口-------------------------------");
        Result<Object> result = new Result<>();
        try {
            ReleaseNoteDTO versionInfo = this.jQ.getVersionInfo();
            if (versionInfo != null && StringUtils.isNotBlank(versionInfo.getJarUrl()) && !versionInfo.getJarUrl().endsWith(".exe")) {
                versionInfo.setNewVersion(false);
            }
            result.setData(versionInfo);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00057));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00057), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00057);
        }
        return result;
    }

    @GetMapping({"getVersion"})
    public Result<String> getVersion() {
        Result<String> result = new Result<>();
        try {
            result.setData("1.36.0.2");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00059));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00059), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00059);
        }
        return result;
    }

    @GetMapping({"shutdown"})
    public Result<Object> ak() {
        Result<Object> result = new Result<>();
        try {
            al();
            HttpUtils.sendPostJson("http://localhost:46666/actuator/shutdown", new HashMap(1), "utf-8");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00060));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00060), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00060);
        }
        return result;
    }

    private static void al() throws IOException {
        String baseDir = AppConfig.getAppHomeDir().getAbsolutePath();
        File base = new File(new File(baseDir), "Temp");
        if (!base.exists()) {
            base.mkdirs();
            return;
        }
        File exeSuccessFile = new File(base, "\\exe.success");
        if (exeSuccessFile.exists()) {
            for (File file : base.listFiles()) {
                if (file.getName().endsWith(".exe")) {
                    try {
                        ProcessBuilder processBuilder = new ProcessBuilder(file.getPath());
                        processBuilder.start();
                    } catch (Exception e) {
                        System.out.println("执行安装失败,e=" + e);
                    }
                }
            }
        }
    }
}
