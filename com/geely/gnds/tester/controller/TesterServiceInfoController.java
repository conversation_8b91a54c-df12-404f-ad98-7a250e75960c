package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.common.ChangeServer;
import com.geely.gnds.tester.dto.ServerChildMenu;
import com.geely.gnds.tester.dto.TesterServiceInfoDto;
import com.geely.gnds.tester.enums.ShowSystemNameEnum;
import com.geely.gnds.tester.enums.TesterRunningState;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.util.TesterLoginUtils;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/testerService"})
@RestController
/* loaded from: TesterServiceInfoController.class */
public class TesterServiceInfoController {
    private static final Logger log = LoggerFactory.getLogger(TesterServiceInfoController.class);

    @Value("${tester.tenantName}")
    private String tenantName;

    @Value("${spring.profiles.active}")
    private String environment;

    @Autowired
    private ChangeServer ju;

    @RequestMapping({"/info"})
    public Result<TesterServiceInfoDto> R() throws InterruptedException {
        TesterServiceInfoDto testerServiceInfoDto = new TesterServiceInfoDto();
        testerServiceInfoDto.setVersion("1.36.0.2");
        testerServiceInfoDto.setTenantName(this.tenantName);
        testerServiceInfoDto.setEnvironment(this.environment);
        String name = ManagementFactory.getRuntimeMXBean().getName();
        String pid = name.split("@")[0];
        testerServiceInfoDto.setPid(pid);
        testerServiceInfoDto.setRunningState(TesterRunningState.WAIT_CONNECT);
        testerServiceInfoDto.setInitStatus(TesterLoginUtils.getStartStatus());
        String showName = ShowSystemNameEnum.cp(testerServiceInfoDto.getTenantName() + "-" + testerServiceInfoDto.getEnvironment());
        testerServiceInfoDto.setTenantShowName(showName);
        testerServiceInfoDto.setCloudId(AppConfig.getCloudId());
        testerServiceInfoDto.setVehicleDataRegion(AppConfig.getActiveRegion());
        testerServiceInfoDto.setLanguage(HttpUtils.getDefaultLanguage());
        testerServiceInfoDto.setSelfUrl(AppConfig.getSelfUrl());
        return new Result().ok(testerServiceInfoDto);
    }

    @PostMapping({"/serverList/{id}"})
    public Result<List<ServerChildMenu>> bX(@PathVariable String id) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("cloudServers", "cloud_server");
        params.put("vehicleDataRegion", "vehicle_data_region");
        Assert.hasText(params.get(id), "Parameter is incorrect");
        return new Result().ok(this.ju.getServerList(params.get(id)));
    }

    @PostMapping({"/changeVehicleDataRegion"})
    public void ab() throws IOException {
        AppConfig.reloadProperties();
        log.info("重新载入配置文件成功");
    }

    @PostMapping({"/changeVehicleDataRegion/{region}"})
    public Result bY(@PathVariable String region) throws IOException {
        log.info("设置车辆数据区域：{}", region);
        AppConfig.setProperty("tester.vehicleDataRegion", region);
        AppConfig.reloadProperties();
        return new Result().ok("");
    }

    @PostMapping({"/changeServer/{cloudId}"})
    public Result bZ(@PathVariable String cloudId) {
        log.info("设置服务器：{}", cloudId);
        CompletableFuture.runAsync(() -> {
            log.info("切换云服务器为：{}，当前环境：{}，当前租户：{}", new Object[]{cloudId, AppConfig.getProperty("spring.profiles.active").toUpperCase(), AppConfig.getTenantName()});
            this.ju.downLoadFile(cloudId, AppConfig.getProperty("spring.profiles.active").toUpperCase(), AppConfig.getTenantName());
        });
        return new Result().ok("");
    }
}
