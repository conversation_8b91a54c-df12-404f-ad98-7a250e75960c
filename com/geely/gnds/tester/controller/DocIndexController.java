package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.IndexSearchDto;
import com.geely.gnds.tester.dto.TechnicalReqDTO;
import com.geely.gnds.tester.dto.TechnicalResDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.DocIndexService;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"api/v1/docIndex"})
@RestController
/* loaded from: DocIndexController.class */
public class DocIndexController {
    private static final Logger log = LoggerFactory.getLogger(DocIndexController.class);

    @Autowired
    private DocIndexService iT;

    @GetMapping({"getIndexCondition"})
    public Result<Object> a(IndexSearchDto indexSearchDto) {
        log.info("======> 客户端开始请求查询搜索条件接口 <======{}", indexSearchDto);
        Result<Object> result = new Result<>();
        try {
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            result.setData(this.iT.getIndexCondition(indexSearchDto));
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00022));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00022), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00265);
        }
        log.info("======> 客户端开始请求查询搜索条件接口结束 <======");
        return result;
    }

    @GetMapping({"/getDocList"})
    public Result<TechnicalResDTO> b(TechnicalReqDTO technicalReqDTO) {
        Result<TechnicalResDTO> result = new Result<>();
        try {
            result.ok(this.iT.getTechnologyList(technicalReqDTO));
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00020));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00020), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00264);
        }
        return result;
    }
}
