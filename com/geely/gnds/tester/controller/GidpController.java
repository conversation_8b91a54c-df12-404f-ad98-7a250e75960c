package com.geely.gnds.tester.controller;

import com.geely.gnds.tester.dto.DrCscNode;
import com.geely.gnds.tester.dto.gbop.gidp.GidpActivityDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisOrderDetailOrderDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTjDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTrDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpUpdateDiagnosisOrderDTO;
import com.geely.gnds.tester.service.GidpService;
import com.geely.gnds.tester.util.Result;
import com.github.pagehelper.PageInfo;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/gidp"})
@RestController
/* loaded from: GidpController.class */
public class GidpController {
    private static final Logger log = LoggerFactory.getLogger(GidpController.class);

    @Autowired
    private GidpService iY;

    @GetMapping({"/getToken"})
    public Result<String> br(String username) throws Exception {
        return new Result().ok(this.iY.getGidpToken(username));
    }

    @PostMapping({"/save"})
    public Result<GidpDiagnosisDTO> a(@RequestBody GidpDiagnosisOrderDetailOrderDTO detailOrder) throws Exception {
        return new Result().ok(this.iY.saveGidpDiagnosis(detailOrder));
    }

    @PostMapping({"/update"})
    public Result<GidpDiagnosisDTO> a(@RequestBody GidpUpdateDiagnosisOrderDTO updateOrder) throws Exception {
        return new Result().ok(this.iY.updateGidpDiagnosis(updateOrder));
    }

    @PostMapping({"/getPageDiagnosisList"})
    public Result<PageInfo<GidpDiagnosisDTO>> a(String serviceStationCode, Integer orderStatus, Integer pageNum, Integer pageSize) throws Exception {
        return new Result().ok(this.iY.getPageDiagnosisList(serviceStationCode, orderStatus, pageNum, pageSize));
    }

    @PostMapping({"/getPageDiagnosisListByVin"})
    public Result<PageInfo<GidpDiagnosisDTO>> a(String serviceStationCode, String vin, Integer pageNum, Integer pageSize, String diagnosisMode) throws Exception {
        return new Result().ok(this.iY.getPageDiagnosisListByVin(serviceStationCode, vin, pageNum, pageSize, diagnosisMode));
    }

    @PostMapping({"/getGidpIssueFunctions"})
    public Result<List<DrCscNode>> getGidpIssueFunctions() throws Exception {
        return new Result().ok(this.iY.getGidpIssueFunctions());
    }

    @PostMapping({"/getGidpActivity"})
    public Result<PageInfo<GidpActivityDTO>> a(String diagnosticNumber, String vin, Integer pageNum, Integer pageSize) throws Exception {
        return new Result().ok(this.iY.getGidpActivity(diagnosticNumber, vin, pageNum, pageSize));
    }

    @PostMapping({"/responseActivityAction"})
    public Result<Map> p(String diagnosticNumber, String activityNumber, String executionStatus, String feedBack) throws Exception {
        return new Result().ok(this.iY.responseActivityAction(diagnosticNumber, activityNumber, executionStatus, feedBack));
    }

    @PostMapping({"/getTjDone"})
    public Result<PageInfo<GidpTjDTO>> a(String diagnosticNumber, String vin, String vehicleType, String vehicleStructureWeek, String vdnList, Integer pageNum, Integer pageSize) throws Exception {
        return new Result().ok(this.iY.searchTjDone(diagnosticNumber, vin, vehicleType, vehicleStructureWeek, vdnList, pageNum, pageSize));
    }

    @PostMapping({"/responseTjAction"})
    public Result<Map> q(String diagnosticNumber, String activityNumber, String pushExecutionStatus, String pushFeedBack) throws Exception {
        return new Result().ok(this.iY.responseTjAction(diagnosticNumber, activityNumber, pushExecutionStatus, pushFeedBack));
    }

    @PostMapping({"/getTrList"})
    public Result<PageInfo<GidpTrDTO>> b(String diagnosticNumber, String vin, Integer pageNum, Integer pageSize) throws Exception {
        return new Result().ok(this.iY.getTrList(diagnosticNumber, vin, pageNum, pageSize));
    }

    @PostMapping({"/getCreateTrUrl"})
    public Result<GidpTrDTO> getCreateTrUrl() throws Exception {
        return new Result().ok(this.iY.getCreateTrUrl());
    }

    @GetMapping({"/getWorkOrderMenuList"})
    public Result<String> bs(String workOrderType) throws Exception {
        return new Result().ok(this.iY.getWorkOrderMenuList(workOrderType));
    }
}
