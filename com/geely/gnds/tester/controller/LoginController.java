package com.geely.gnds.tester.controller;

import cn.hutool.core.util.StrUtil;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.common.TesterConfigProperties;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.AppSsoConfigDTO;
import com.geely.gnds.tester.dto.AppSsoExtDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.LoginService;
import com.geely.gnds.tester.service.TesterConfigService;
import com.geely.gnds.tester.task.FileUploadTask;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.vo.language.LanguageVo;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/login"})
@RestController
/* loaded from: LoginController.class */
public class LoginController {

    @Autowired
    private LoginService hs;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private Cloud cloud;

    @Autowired
    private FileUploadTask fileUploadTask;

    @Autowired
    private TesterConfigService cd;

    @Autowired
    private TesterConfigProperties ja;
    public static final String jb = "ZXCMNBVASDLKJHGF";
    private static final Logger log = LoggerFactory.getLogger(LoginController.class);

    @Value("${tester.cert.enabled:true}")
    private Boolean certEnabled;

    @PostMapping({"logout"})
    public Result<Object> bu(String username) {
        Result<Object> result = new Result<>();
        try {
            Optional.ofNullable(this.cd.getConfig()).ifPresent(config -> {
                String testerCode = config.getTesterCode();
                this.fileUploadTask.cH(testerCode);
                this.hs.logout(username);
            });
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("在线退出登录异常，异常原因--->", e);
        } catch (Exception e2) {
            GlobalException.a(e2, MessageUtils.getMessage("Online logout and login exception"));
        }
        return result;
    }

    @GetMapping({"loginCloud"})
    public Result<Object> T() {
        log.info("------------------------执行诊断仪能否登录云端接口-------------------------------");
        Result<Object> result = new Result<>();
        try {
            boolean flag = this.hs.loginCloud().booleanValue();
            if (flag) {
                result.setCode(HttpStatus.SUCCESS);
                result.setMsg("success");
            } else {
                result.setCode(HttpStatus.ERROR);
                result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00025));
            }
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00025));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00025), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00025);
        }
        return result;
    }

    @GetMapping({"getPublicKey"})
    public Result<Object> f(HttpServletRequest request) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.hs.getPublicKey(request));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00026));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00026), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00026);
        }
        return result;
    }

    @GetMapping({"getSsoConfig"})
    public Result<AppSsoConfigDTO> g(HttpServletRequest request) {
        Result<AppSsoConfigDTO> result = new Result<>();
        try {
            AppSsoConfigDTO appSsoConfig = this.hs.getAppSsoConfig(request);
            result.setData(new AppSsoExtDTO(appSsoConfig, this.ja.getUi().isShowSsoLink(), this.ja.getUi().isRedirectSsoLink()));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00266);
        }
        return result;
    }

    @GetMapping({"toLogin"})
    public Result<Map<String, Object>> a(@RequestParam String redirect_uri, HttpServletResponse response) throws Exception {
        HashMap map = new HashMap();
        map.put("showSsoLink", Boolean.valueOf(this.ja.getUi().isShowSsoLink()));
        map.put("redirectSsoLink", Boolean.valueOf(this.ja.getUi().isRedirectSsoLink()));
        map.put("url", "/api/v1/login/toOauth2?returnUrl=" + StrUtil.trimToEmpty(redirect_uri));
        return new Result().ok(map);
    }

    @GetMapping({"toOauth2"})
    public void b(@RequestParam String returnUrl, HttpServletResponse response) throws Exception {
        response.sendRedirect(this.cloud.aX(returnUrl));
    }

    @GetMapping({"getRequestKey"})
    public Result<Object> getRequestKey() {
        Result<Object> result = new Result<>();
        try {
            result.setData(jb);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00027));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00027), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00027);
        }
        return result;
    }

    @PostMapping({"getUserVdn"})
    public Result<String> getUserVdn() {
        log.info("------------------------执行getUserVdn接口-------------------------------");
        Result<String> result = new Result<>();
        try {
            if (TesterLoginUtils.isOnLine()) {
                LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
                result.setData(this.cloud.as(loginUser.getUsername()));
            } else {
                result.setData("");
            }
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("请求：getUserVdn，异常原因--->", e);
        } catch (Exception e2) {
            GlobalException.a(e2, "获取UserVDN失败");
        }
        return result;
    }

    @PostMapping({"heartBeatStop"})
    public Result<Object> U() {
        Result<Object> result = new Result<>();
        try {
            this.hs.heartBeatStop();
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("在线退出登录异常，异常原因--->", e);
        } catch (Exception e2) {
            GlobalException.a(e2, "在线退出登录异常");
        }
        return result;
    }

    @GetMapping({"defaultLanguage"})
    public Result<Object> bv(@RequestParam String language) {
        log.info("------------------------多语言切换接口入参【{}】", language);
        Locale.setDefault(Locale.forLanguageTag(language));
        Result<Object> result = new Result<>();
        HttpUtils.setDefaultLanguage(language);
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }

    @GetMapping({"requestCert"})
    public Result<Object> V() {
        log.info("------------------------执行requestCert接口-------------------------------");
        Result<Object> result = new Result<>();
        try {
            if (this.certEnabled.booleanValue()) {
                LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
                log.info("登陆成功开始获取证书");
                this.cloud.aT(loginUser.getUsername());
            }
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00277), e);
        } catch (Exception e2) {
            log.error("获取证书失败", e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00277);
        }
        return result;
    }

    @GetMapping({"/getUiLanguageResource"})
    public Result<Object> bw(@RequestParam String systemCode) throws Exception {
        log.info("------------------------Ui多语言获取接口【{}】", systemCode);
        List<LanguageVo> objList = this.hs.getUiLanguageResource(systemCode);
        Result<Object> result = new Result<>();
        result.setData(objList);
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }
}
