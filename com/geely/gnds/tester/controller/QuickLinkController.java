package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.QuickLinkDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.QuickLinkService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/quicklink"})
@RestController
/* loaded from: QuickLinkController.class */
public class QuickLinkController {

    @Autowired
    private QuickLinkService jg;
    private static final Logger log = LoggerFactory.getLogger(QuickLinkController.class);

    @GetMapping({"list"})
    public Result<List<QuickLinkDTO>> g(Long brandId) {
        log.info("------------------------执行快速链接口-------------------------------");
        try {
            List<QuickLinkDTO> list = this.jg.getList(brandId);
            return new Result().ok(list);
        } catch (UnAuthException e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00033), e);
            return new Result().error(HttpStatus.UNAUTHORIZED, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00033));
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00033);
            return null;
        }
    }
}
