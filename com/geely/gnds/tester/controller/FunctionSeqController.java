package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.FunctionSeqService;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.Result;
import java.util.Date;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/function"})
@RestController
/* loaded from: FunctionSeqController.class */
public class FunctionSeqController {

    @Autowired
    private FunctionSeqService iX;

    @Autowired
    private TokenService tokenService;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(FunctionSeqController.class);

    @GetMapping({"queryFunctionSeqList"})
    public Result<Object> bq(@RequestParam String vin) {
        log.info("======> 客户端开始请求api/v1/fix下的的queryFunctionSeqList接口 <======");
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), MessageUtils.getMessage("Functional change sequence"), Long.valueOf(System.currentTimeMillis()), "Info", ("=== " + MessageUtils.getMessage("Obtain a list of functional change sequences") + " ===").getBytes());
            });
            log.info("======> 客户端开始调用functionSeqService的queryFunctionSeqList方法 <======");
            result.setData(this.iX.queryFunctionSeqList(vin));
            log.info("======> 客户端调用functionSeqService的queryFunctionSeqList方法结束 <======");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00134));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00134), e);
        } catch (Exception e2) {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), "维修序列", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00134).getBytes());
            });
            GlobalException.a(e2, TesterErrorCodeEnum.SG00134);
        }
        log.info("======> 客户端请求api/v1/fix下的的queryFixSeqList接口结束 <======");
        return result;
    }

    @PutMapping({"initFlush"})
    public Result<Object> e(@RequestBody InitializeUiDto initializeUiDto) {
        Result<Object> result = new Result<>();
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        FdTcpClient tcpClient = this.manager.getFdTcpClient(initializeUiDto.getVin());
        try {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), MessageUtils.getMessage("Functional change sequence"), Long.valueOf(System.currentTimeMillis()), "Info", ("=== " + MessageUtils.getMessage("Initialize function change sequence flashing") + " ===").getBytes());
            });
            result.setData(this.iX.initFlush(initializeUiDto, loginUser));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("初始化功能变更序列刷写异常", e);
        } catch (Exception e2) {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), MessageUtils.getMessage("Functional change sequence"), Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, MessageUtils.getMessage("Abnormal flashing of initialization function change sequence").getBytes());
            });
            GlobalException.a(e2, HttpStatus.CREATED, MessageUtils.getMessage("Abnormal flashing of initialization function change sequence"));
        }
        return result;
    }
}
