package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.enums.DsaAddressConst;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.VehicleStatusDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.seq.UdsSend;
import com.geely.gnds.tester.service.SeqService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.util.TesterReadParamUtil;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.util.Date;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/seq"})
@RestController
/* loaded from: SeqController.class */
public class SeqController {

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private SeqService ek;

    @Autowired
    private TokenService tokenService;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(SeqController.class);

    @PostMapping({"init"})
    public Result<String> a(@RequestBody InitializeUiDto initializeUiDto) {
        String init;
        log.info("------------------------执行诊断序列初始化UI接口-------------------------------");
        log.info("诊断序列初始化UI接口口入参----->{}", initializeUiDto);
        Result<String> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            if (ConstantEnum.STATUS_READOUT.equals(initializeUiDto.getType())) {
                init = this.ek.initStatusReadout(initializeUiDto, loginUser);
                log.info("seqController init initStatusReadout");
            } else {
                init = this.ek.init(initializeUiDto, loginUser);
                log.info("seqController init init");
            }
            result.setData(init);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------诊断序列初始化UI成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00039);
        }
        return result;
    }

    @PostMapping({"execute"})
    public Result<String> f(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("------------------------执行诊断序列callMainModule接口-------------------------------");
        log.info("执行诊断序列callMainModule接口入参----->{}", initializeUiDto);
        Result<String> result = new Result<>();
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        this.testerThread.getPool().execute(() -> {
            try {
                log.info("执行诊断序列callMainModule新线程获取1-----");
                ThreadLocalUtils.CURRENT_USER_NAME.set(loginUser.getUsername());
                this.ek.excute(initializeUiDto);
            } catch (Exception e) {
                log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00040), e);
            }
        });
        log.info("seqController execute 创建线程结束");
        TesterReadParamUtil.readInit();
        return result;
    }

    @PostMapping({"refreshUi"})
    public Result<String> g(@RequestBody InitializeUiDto initializeUiDto) {
        log.debug("执行诊断序列refreshUi接口入参----->{}", initializeUiDto.toString());
        Result<String> result = new Result<>();
        try {
            result.setData(this.ek.refreshUi(initializeUiDto));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00041));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00041), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00041);
        }
        log.info("执行诊断序列refreshUi接口,返回数据");
        return result;
    }

    @PostMapping({"callFinalModule"})
    public Result<String> h(@RequestBody InitializeUiDto initializeUiDto) throws Exception {
        ScriptHandler handler;
        LoginUser loginUser;
        Result<String> result = new Result<>();
        log.info("------------------------执行诊断序列callFinalModule接口-------------------------------");
        try {
            String key = initializeUiDto.getVin() + initializeUiDto.getSeqCode();
            handler = SeqManager.getInstance().getScriptHandler(key);
            loginUser = this.tokenService.c(ServletUtils.getRequest());
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00042));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00042), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00042);
        }
        if (handler == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00042));
        }
        this.testerThread.getPool().execute(() -> {
            try {
                ThreadLocalUtils.CURRENT_USER_NAME.set(loginUser.getUsername());
                handler.callFinalModule();
            } catch (Exception e3) {
                log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00042), e3);
            }
        });
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        log.info("------------------------诊断序列callFinalModule调用成功-------------------------------");
        return result;
    }

    @PostMapping({"handleClickEvent"})
    public Result<String> i(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("进入seqController handleClickEvent");
        Result<String> result = new Result<>();
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        log.info("进入seqController 创建线程开始");
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        this.testerThread.getPool().execute(() -> {
            try {
                ThreadLocalUtils.CURRENT_USER_NAME.set(loginUser.getUsername());
                this.ek.handleClickEvent(initializeUiDto);
            } catch (Exception e) {
                log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00043), e);
            }
        });
        log.info("进入seqController 创建线程结束");
        return result;
    }

    @PostMapping({"getSeqId"})
    public Result<String> j(@RequestBody InitializeUiDto initializeUiDto) {
        Result<String> result = new Result<>();
        try {
            result.setData(this.ek.getSeqId(initializeUiDto));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00044));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00044), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00044);
        }
        return result;
    }

    @PostMapping({"notifySeq"})
    public Result<String> k(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("------------------------执行唤醒父诊断序列接口-------------------------------");
        log.info("执行唤醒父诊断序列接口入参----->{}", initializeUiDto);
        Result<String> result = new Result<>();
        try {
            this.ek.notifySeq(initializeUiDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------唤醒父诊断序列调用成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00045));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00045), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00045);
        }
        return result;
    }

    @GetMapping({"notifySeq2"})
    public Result<String> bP(@RequestParam String com2) {
        Result<String> result = new Result<>();
        try {
            UdsSend udsSend = new UdsSend();
            udsSend.udsData(DsaAddressConst.FUNCTIONAL_ADDRESSING, com2, "GNDSTEST000000055");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------唤醒父诊断序列调用成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00045));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00045));
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00045);
        }
        return result;
    }

    @PostMapping({ConstantEnum.STATUS_READOUT})
    public Result<Object> l(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("执行诊断序列statusReadout接口入参----->{}", initializeUiDto);
        Result<Object> result = new Result<>();
        VehicleStatusDto vehicleStatusDto = this.ek.statusReadout(initializeUiDto);
        result.setData(vehicleStatusDto);
        log.info("执行诊断序列statusReadout接口出参----->{}", vehicleStatusDto);
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }

    @PutMapping({"initBtnSeq"})
    public Result<String> m(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("按钮绑定脚本初始化接口入参----->{}", initializeUiDto);
        Result<String> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            result.setData(this.ek.initBtnSeq(initializeUiDto, loginUser));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            FdTcpClient tcpClient = this.manager.getFdTcpClient(initializeUiDto.getVin());
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039).getBytes());
            });
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00039);
        }
        log.info("seqController initBtnSeq 返回结果");
        return result;
    }

    @GetMapping({"getSeqStatus"})
    public Result<Integer> bQ(@RequestParam(required = false) String vin) {
        log.info("进入seqController getSeqStatus");
        Result<Integer> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            log.info("seqController tokenService.getLoginUser");
            String username = loginUser.getUsername();
            result.setData(this.ek.getSeqStatus(username, vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("获取脚本执行状态接口异常", e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, "获取脚本执行状态接口异常");
        }
        log.info("seqController getSeqStatus返回结果");
        return result;
    }

    @PostMapping({"handleUiData"})
    public Result<String> n(@RequestBody InitializeUiDto initializeUiDto) {
        Result<String> result = new Result<>();
        try {
            this.ek.handleUiData(initializeUiDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("诊断序列UI数据赋值接口异常", e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, "诊断序列UI数据赋值接口异常");
        }
        return result;
    }
}
