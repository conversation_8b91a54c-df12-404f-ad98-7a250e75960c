package com.geely.gnds.tester.controller;

import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.service.VehicleDetailsService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.vo.MaintainResetVo;
import com.geely.gnds.tester.vo.VehicleBroadcastVO;
import com.geely.gnds.tester.vo.VehicleMaintainVO;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/vehicleDetails"})
@RestController
/* loaded from: VehicleDetailsController.class */
public class VehicleDetailsController {

    @Autowired
    private VehicleDetailsService jN;

    @GetMapping({"/broadCast"})
    public Result<VehicleBroadcastVO> cj(String vin) throws Exception {
        if (ObjectUtils.isEmpty(vin)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00163));
        }
        VehicleBroadcastVO vo = this.jN.getVehicleBroadcast(vin);
        vo.setConfigVO(this.jN.vehicleConfig(vo, vin));
        return new Result().ok(vo);
    }

    @PutMapping({"/realtime/check"})
    public Result<?> S(@RequestParam("vin") String vin, @RequestParam("carModel") String carModel) {
        Result<Boolean> result = new Result().ok(true);
        try {
            this.jN.vehicleRealtimeCheck(vin, carModel);
        } catch (Exception e) {
            result.setMsg("该车型不支持车机服务大厅保养功能，请到维修功能页面进行保养重置");
            result.setData(false);
        }
        return result;
    }

    @GetMapping({"/health/inspection"})
    public Result<?> T(@RequestParam("vin") String vin, @RequestParam("carModel") String carModel) {
        Result<List<VehicleMaintainVO>> result = new Result().ok(Collections.emptyList());
        try {
            result.setData(this.jN.vehicleHealthInspection(vin, carModel));
        } catch (Exception e) {
            result.setMsg(e.getMessage());
        }
        return result;
    }

    @PostMapping({"/maintain/reset"})
    public Result<?> a(@RequestBody MaintainResetVo request) {
        Result<Boolean> result = new Result().ok(true);
        try {
            this.jN.vehicleMaintainReset(request.getVin(), request.getCarModel(), request.getMaintainItems());
        } catch (Exception e) {
            result.setMsg(e.getMessage());
            result.setData(false);
        }
        return result;
    }
}
