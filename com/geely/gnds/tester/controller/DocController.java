package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.DocService;
import com.geely.gnds.tester.util.ContentTypeUtil;
import com.geely.gnds.tester.util.Result;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/doc"})
@RestController
/* loaded from: DocController.class */
public class DocController {

    @Autowired
    private DocService iS;
    private static final Logger log = LoggerFactory.getLogger(DocController.class);

    @Value("${fd.resource.docPath}")
    private String docPath;
    private File hS = new File(ConstantEnum.POINT);

    @GetMapping({"/getDoc"})
    public Result<Object> a(@RequestParam("vin") String vin, @RequestParam(value = "gcid", required = false) String gcid, @RequestParam(value = "location", required = false) String location, @RequestParam(value = "fmea", required = false) String fmea, @RequestParam("docType") Integer docType) {
        log.info("======> 客户端开始请求api/v1/doc下的getDoc接口，入参vin：{}，gcid:{},location:{},fmea:{},docType:{} <======", new Object[]{vin, gcid, location, fmea, docType});
        Result<Object> result = new Result<>();
        try {
            result.setData(this.iS.getDocUrl(vin, gcid, location, fmea, docType));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("获取文档地址接口异常", e);
        } catch (Exception e2) {
            GlobalException.c(e2);
        }
        log.info("======> 客户端请求api/v1/doc下的getDoc接口结束 <======");
        return result;
    }

    @GetMapping({"getFile/**"})
    public void a(HttpServletRequest request, HttpServletResponse response) throws IOException {
        File dir = new File(this.hS, this.docPath);
        if (dir.exists()) {
            String path = dir.getAbsolutePath() + File.separator + request.getRequestURI().substring(request.getRequestURI().lastIndexOf("getFile") + 7);
            if (StringUtils.isNotBlank(path)) {
                try {
                    FileInputStream fileInputStream = new FileInputStream(path);
                    Throwable th = null;
                    try {
                        try {
                            File file = new File(path);
                            response.reset();
                            response.setHeader("Content-Type", ContentTypeUtil.getContentType(request.getRequestURI().substring(request.getRequestURI().lastIndexOf(ConstantEnum.POINT) + 1)));
                            response.setHeader("Content-Length", String.valueOf(file.length()));
                            ServletOutputStream outputStream = response.getOutputStream();
                            byte[] read = new byte[DoipUtil.MAX_BYTE_ARRAY_SIZE];
                            while (fileInputStream.read(read) != -1) {
                                outputStream.write(read);
                            }
                            outputStream.flush();
                            if (fileInputStream != null) {
                                if (0 != 0) {
                                    try {
                                        fileInputStream.close();
                                    } catch (Throwable th2) {
                                        th.addSuppressed(th2);
                                    }
                                } else {
                                    fileInputStream.close();
                                }
                            }
                        } finally {
                        }
                    } catch (Throwable th3) {
                        th = th3;
                        throw th3;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("DownloadFile error", e);
                }
            }
        }
    }
}
