package com.geely.gnds.tester.controller;

import cn.hutool.json.JSONUtil;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.xml.XmlFault;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.common.ActionContext;
import com.geely.gnds.tester.common.TesterContext;
import com.geely.gnds.tester.dto.ErrorLogDto;
import com.geely.gnds.tester.dto.TimeOutLogDto;
import com.geely.gnds.tester.util.Result;
import java.util.Date;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/log"})
@RestController
/* loaded from: LogController.class */
public class LogController {
    private static final Logger log = LoggerFactory.getLogger("client");

    @PostMapping({"timeoutLog"})
    public Result<Object> a(@RequestBody TimeOutLogDto timeOutLogDto) {
        ErrorLogDto dto = new ErrorLogDto();
        dto.setParams(timeOutLogDto.getParams());
        dto.setUri(timeOutLogDto.getName());
        dto.setCode("SH00005");
        dto.setMsg("请求 " + timeOutLogDto.getName() + " 超时");
        return a(dto);
    }

    @PostMapping({"clientLog"})
    public Result<Object> b(@RequestBody TimeOutLogDto timeOutLogDto) {
        log.error("系统接口请求错误:请求路径->{},请求参数->{}", timeOutLogDto.getName(), StringUtils.substring(JSONUtil.toJsonStr(timeOutLogDto.getParams()), 0, 100));
        return new Result().ok("");
    }

    @PostMapping({"errorLog"})
    public Result<Object> a(@RequestBody ErrorLogDto logDto) {
        if (null != logDto && logDto.getUri().contains("api/v1/log/errorLog")) {
            return new Result().ok("");
        }
        Result<Object> result = new Result<>();
        log.error("系统接口请求错误:请求路径->{},错误码->{},错误消息->{},请求参数->{}", new Object[]{logDto.getUri(), logDto.getCode(), logDto.getMsg(), StringUtils.substring(JSONUtil.toJsonStr(logDto.getParams()), 0, 100)});
        b(logDto);
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }

    private void b(ErrorLogDto logDto) {
        FdTcpClient client;
        try {
            String vin = ActionContext.getRequest().getParameter("vin");
            if (StringUtils.isBlank(vin) && logDto != null && null != logDto.getParams()) {
                vin = Optional.ofNullable(logDto.getParams().get("vin")).orElse("").toString();
            }
            if (StringUtils.isNotBlank(vin) && null != (client = TesterContext.getFdTcpClient(vin))) {
                String uri = (String) Optional.ofNullable(logDto.getUri()).orElse("");
                client.getXmlLogger().a(new XmlFault(false, logDto.getCode(), logDto.getMsg(), new Date(), uri));
            }
        } catch (Exception e) {
            log.error("xml日志记录失败:{}", e.getMessage(), e);
        }
    }
}
