package com.geely.gnds.tester.controller;

import com.geely.gnds.tester.dto.NoticeMessageDTO;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.service.NoticeMessageService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/notice"})
@RestController
/* loaded from: NoticeMessageController.class */
public class NoticeMessageController {
    private static final Logger log = LoggerFactory.getLogger(NoticeMessageController.class);

    @Resource
    private NoticeMessageService jd;

    @GetMapping({"/message/list"})
    public Result<List<NoticeMessageDTO>> X() throws Exception {
        return new Result().ok(this.jd.queryMessages());
    }

    @GetMapping({"/message/download"})
    public void downLoadFile(@RequestParam("id") String id, HttpServletResponse response) {
        try {
            this.jd.downLoadFile(id, response);
        } catch (Exception e) {
            log.error("下载消息通知附件失败", e);
            GlobalException.c(e);
        }
    }
}
