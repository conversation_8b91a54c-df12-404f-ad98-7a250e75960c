package com.geely.gnds.tester.controller;

import com.geely.gnds.dsa.dto.CircuitDiagramOverviewReqDTO;
import com.geely.gnds.tester.dto.CircuitDiagramOverviewDTO;
import com.geely.gnds.tester.dto.DiaImgDTO;
import com.geely.gnds.tester.dto.TranWithOrigin;
import com.geely.gnds.tester.service.CircuitDiagramService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"api/v1/circuitDiagram"})
@RestController
/* loaded from: CircuitDiagramController.class */
public class CircuitDiagramController {
    private static final Logger log = LoggerFactory.getLogger(CircuitDiagramController.class);

    @Autowired
    private CircuitDiagramService iQ;

    @GetMapping({"getCircuitDiagramList"})
    public Result<List<TranWithOrigin>> L(@RequestParam("wdid") String wdid, @RequestParam("vin") String vin) throws Exception {
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getCircuitDiagramList接口,参数wdid:{},vin:{} <======", wdid, vin);
        Result<List<TranWithOrigin>> result = new Result<>();
        List<TranWithOrigin> list = this.iQ.getCircuitDiagramList(wdid, vin);
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getCircuitDiagramList接口结束<======");
        return result.ok(list);
    }

    @GetMapping({"getCircuitDiagram"})
    public void getCircuitDiagram(@RequestParam("id") String id, String vin, HttpServletResponse response) throws Exception {
        this.iQ.getCircuitDiagram(id, vin, response);
    }

    @GetMapping({"getCircuitInfo"})
    public Result<Map<String, Object>> J(@RequestParam("vin") String vin, @RequestParam("type") String type, @RequestParam("ref") String ref) throws Exception {
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getCircuitInfo接口,参数type:{},ref:{} <======", type, ref);
        Map<String, Object> circuitInfo = this.iQ.getCircuitInfo(vin, type, ref);
        log.info("======> 客户端请求/api/v1/circuitDiagram下的getCircuitInfo接口结束 <======");
        return new Result().ok(circuitInfo);
    }

    @GetMapping({"getCircuitExtend"})
    public Result<List<Object>> j(@RequestParam("type") String type, @RequestParam("ref") String ref, @RequestParam("vin") String vin, @RequestParam(value = "id", required = false) String id) throws Exception {
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getCircuitExtend接口,参数ref:{},vin:{} <======", ref, vin);
        List<Object> circuitExtend = this.iQ.getCircuitExtend(type, ref, vin, id);
        log.info("======> 客户端请求/api/v1/circuitDiagram下的getCircuitExtend接口结束 <======");
        return new Result().ok(circuitExtend);
    }

    @GetMapping({"getHarnessImage"})
    public void getHarnessImage(@RequestParam("type") String type, @RequestParam("ref") String ref, @RequestParam("vin") String vin, HttpServletResponse response) throws Exception {
        this.iQ.getHarnessImage(type, ref, vin, response);
    }

    @GetMapping({"getImageByWdid"})
    public void getImageByWdid(@RequestParam("type") String type, @RequestParam("wdid") String wdid, @RequestParam("vin") String vin, HttpServletResponse response) throws Exception {
        this.iQ.getImageByWdid(type, wdid, vin, response);
    }

    @GetMapping({"getImageTypeList"})
    public Result<List<DiaImgDTO>> K(@RequestParam("gcid") String gcid, @RequestParam("location") String location, @RequestParam("vin") String vin) throws Exception {
        return new Result().ok(this.iQ.getImageTypeList(gcid, location, vin));
    }

    @GetMapping({"getImage"})
    public void getImage(@RequestParam("nevisImage") String nevisImage, HttpServletResponse response, String vin) throws Exception {
        this.iQ.getImage(nevisImage, response, vin);
    }

    @GetMapping({"getEcuImageTypeList"})
    public Result<List<DiaImgDTO>> k(@RequestParam("wdid") String wdid, @RequestParam("vin") String vin, @RequestParam(value = "gcid", required = false) String gcid, @RequestParam(value = "location", required = false) String location) throws Exception {
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getEcuImageTypeList接口,参数wdid:{},vin:{} <======", wdid, vin);
        List<DiaImgDTO> ecuImageTypeList = this.iQ.getEcuImageTypeList(wdid, vin, gcid, location);
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getEcuImageTypeList接口结束 <======");
        return new Result().ok(ecuImageTypeList);
    }

    @GetMapping({"filterCircuitDiagramNode"})
    public Result<List<String>> M(@RequestParam("id") String id, @RequestParam("vin") String vin) throws Exception {
        return new Result().ok(this.iQ.filterCircuitDiagramNode(id, vin));
    }

    @GetMapping({"systemCircuitDiagram"})
    public Result<List<TranWithOrigin>> bc(@RequestParam("vin") String vin) throws Exception {
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getSystemCircuitDiagramList接口,参数vin:{} <======", vin);
        Result<List<TranWithOrigin>> result = new Result<>();
        List<TranWithOrigin> list = this.iQ.getSystemCircuitDiagramList(vin);
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getSystemCircuitDiagramList接口结束<======");
        return result.ok(list);
    }

    @PostMapping({"systemCircuitDiagramOverview"})
    public Result<List<CircuitDiagramOverviewDTO>> a(@RequestBody CircuitDiagramOverviewReqDTO circuitDiagramOverviewReqDTO) throws Exception {
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getSystemCircuitDiagramOverviewList接口,参数circuitDiagramOverviewReqDTO {} <======", circuitDiagramOverviewReqDTO);
        Result<List<CircuitDiagramOverviewDTO>> result = new Result<>();
        List<CircuitDiagramOverviewDTO> list = this.iQ.getSystemCircuitDiagramOverviewList(circuitDiagramOverviewReqDTO.getWdids(), circuitDiagramOverviewReqDTO.getVin());
        log.info("======> 客户端开始请求/api/v1/circuitDiagram下的getSystemCircuitDiagramOverviewList接口结束<======");
        return result.ok(list);
    }
}
