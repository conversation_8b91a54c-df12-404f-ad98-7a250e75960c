package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.RemoteConnectDto;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.GidpService;
import com.geely.gnds.tester.service.RemoteDiagnosisService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/remoteDiagnosis"})
@RestController
/* loaded from: RemoteDiagnosisController.class */
public class RemoteDiagnosisController {

    @Autowired
    private RemoteDiagnosisService jm;

    @Autowired
    private GidpService iY;
    private static final Logger log = LoggerFactory.getLogger(RemoteDiagnosisController.class);

    @GetMapping({"remoteTask"})
    public Result<Object> bE(String vin) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.remoteTask(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00182);
        }
        return result;
    }

    @GetMapping({"queryDtc"})
    public Result<Object> a(String vin, String taskId, Integer type) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.queryDtc(vin, taskId, type));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00183);
        }
        return result;
    }

    @GetMapping({"queryParam"})
    public Result<Object> bF(String taskId) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.queryParam(taskId));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00183);
        }
        return result;
    }

    @GetMapping({"queryDtcDetail"})
    public Result<Object> d(String title, String taskId, String diagnosticNumber, String dtcId, String vin, String ecuAddress, String ecuName) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.queryDtcDetail(title, taskId, StringUtils.cleanPath(diagnosticNumber), dtcId, vin, ecuAddress, ecuName));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00183);
        }
        return result;
    }

    @GetMapping({"remoteTask3"})
    public Result<Object> bG(String vin) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.remoteTask3(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00182);
        }
        return result;
    }

    @GetMapping({"queryDtc3"})
    public Result<Object> b(String vin, String taskId, Integer type) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.queryDtc3(vin, taskId, type));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00183);
        }
        return result;
    }

    @GetMapping({"queryParam3"})
    public Result<Object> bH(String taskId) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.queryParam3(taskId));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00183);
        }
        return result;
    }

    @GetMapping({"queryDtcDetail3"})
    public Result<Object> e(String title, String taskId, String diagnosticNumber, String dtcId, String vin, String ecuAddress, String ecuName) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.queryDtcDetail3(title, taskId, StringUtils.cleanPath(diagnosticNumber), dtcId, vin, ecuAddress, ecuName));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00185);
        }
        return result;
    }

    @GetMapping({"remoteControl"})
    public Result<Object> bI(String vin) {
        Result<Object> result = new Result<>();
        log.info("------------------------执行远程诊断连接-------------------------------");
        try {
            result.setData(this.jm.remoteControl(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00184);
        }
        return result;
    }

    @PostMapping({"/readoutParam3"})
    public Result<Object> c(@RequestBody List<DiagResItemGroupDto> parmas, @RequestParam("vin") String vin, @RequestParam("vehicle") String vehicle, @RequestParam(name = "first", required = false) String first, @RequestParam(name = "second", required = false) String second, @RequestParam(name = "vehicle", required = false) String model) {
        Result<Object> result = new Result<>();
        log.info("------------------------执行远程诊断读取参数3.0-------------------------------");
        try {
            result.setData(this.jm.readoutParam3(parmas, vin, vehicle, first, second, model));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00280);
        }
        return result;
    }

    @GetMapping({"getEcuList"})
    public Result<Object> bl(String vin) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.getEcuList(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00023));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00023), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00023);
        }
        return result;
    }

    @GetMapping({"/readoutDtc"})
    public Result<Object> i(String vin, String vehicle, String first, String second, String model) {
        Result<Object> result = new Result<>();
        log.info("------------------------执行远程诊断读取DTC-------------------------------");
        try {
            result.setData(this.jm.readoutDtc(vin, vehicle, first, second, model));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00185);
        }
        return result;
    }

    @GetMapping({"/clearDtc"})
    public Result<Object> j(String vin, String vehicle, String first, String second, String model) {
        Result<Object> result = new Result<>();
        log.info("------------------------执行远程诊断清除DTC-------------------------------");
        try {
            result.setData(this.jm.clearDtc(vin, vehicle, first, second, model));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00185);
        }
        return result;
    }

    @PostMapping({"/readoutParam"})
    public Result<Object> d(@RequestBody List<DiagResItemGroupDto> parmas, @RequestParam("vin") String vin, @RequestParam("vehicle") String vehicle, @RequestParam(name = "first", required = false) String first, @RequestParam(name = "second", required = false) String second, @RequestParam(name = "vehicle", required = false) String model) {
        Result<Object> result = new Result<>();
        log.info("------------------------执行远程诊断读取DTC-------------------------------");
        try {
            result.setData(this.jm.readoutParam(parmas, vin, vehicle, first, second, model));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00185);
        }
        return result;
    }

    @GetMapping({"/ecuIdentify"})
    public Result<Object> b(String vin, String vehicle, String ecuAddress, String ecuName, String diagnosticPartNumber, String first, String second, String model) {
        Result<Object> result = new Result<>();
        log.info("------------------------执行远程诊断读取DTC-------------------------------");
        try {
            result.setData(this.jm.ecuIdentify(vin, vehicle, ecuAddress, ecuName, diagnosticPartNumber, first, second, model));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00185);
        }
        return result;
    }

    @PostMapping({"/updateGidpOrder/{diagnosticNumber}"})
    public Result<GidpDiagnosisDTO> d(@PathVariable("diagnosticNumber") String diagnosticNumber, @RequestBody List<DtcInfoDTO> dtcList) {
        if (CollectionUtils.isEmpty(dtcList)) {
            return new Result().ok(null);
        }
        return new Result().ok(this.iY.remoteUpdateGidpDiagnosis(diagnosticNumber, dtcList));
    }

    @GetMapping({"getRemoteSeqByEcu"})
    public Result<Object> R(String ecuName, String vin, String type) {
        log.info("======> 客户端开始请求api/v1/ecu下的的getSeqByEcu接口 <======");
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.getRemoteSeqByEcu(ecuName, vin, type));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00022));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00022), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00022);
        }
        return result;
    }

    @PostMapping({"connect"})
    public Result<Object> a(@RequestBody RemoteConnectDto remoteConnectDto) {
        Result<Object> result = new Result<>();
        log.info("------------------------执行远程诊断连接-------------------------------");
        try {
            result.setData(this.jm.connect(remoteConnectDto.getVin(), remoteConnectDto.getVehicle(), remoteConnectDto.getFirst(), remoteConnectDto.getSecond(), remoteConnectDto.getModel(), remoteConnectDto.getSeqList(), remoteConnectDto.getTaskRemoteType()));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00184);
        }
        return result;
    }

    @GetMapping({"querySeqDetail"})
    public Result<Object> bJ(String id) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.jm.querySeqDetail(id));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00185);
        }
        return result;
    }

    @GetMapping({"hideApp"})
    public Result<Object> a(@RequestParam String vin, @RequestParam Boolean icon) {
        Result<Object> result = new Result<>();
        try {
            this.jm.hideApp(vin, icon.booleanValue());
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00184);
        }
        return result;
    }
}
