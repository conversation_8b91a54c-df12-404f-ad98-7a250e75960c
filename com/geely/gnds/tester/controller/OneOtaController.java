package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.tester.dto.SimpleResultDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.OneOtaService;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.Result;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/oneOta"})
@RestController
/* loaded from: OneOtaController.class */
public class OneOtaController {

    @Autowired
    private OneOtaService je;
    private static final Logger log = LoggerFactory.getLogger(OneOtaController.class);

    @GetMapping({"getOneOtaList"})
    public Result<Object> bx(String vin) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.je.getOneOtaList(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00187));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00187), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00205);
        }
        return result;
    }

    @GetMapping({"installation"})
    public Result<Object> f(Long id) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.je.installation(id));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00206);
        }
        return result;
    }

    @PostMapping({"/withdrawn"})
    public Result<Object> by(String vin) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.je.withdrawn(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00262);
        }
        return result;
    }

    @GetMapping({"/remoteUpgrade"})
    public Result<Object> bz(@RequestParam String vin) {
        Result<Object> result = new Result<>();
        if (StringUtils.isEmpty(vin)) {
            throw new CustomException(MessageUtils.getMessage("Vehicle VIN number cannot be empty"));
        }
        try {
            SimpleResultDTO simpleResultDTO = this.je.remoteUpgrade(vin);
            result.setCode(simpleResultDTO.getSuccess().booleanValue() ? HttpStatus.SUCCESS : HttpStatus.ERROR);
            result.setMsg(simpleResultDTO.getMessage());
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00278);
        }
        return result;
    }
}
