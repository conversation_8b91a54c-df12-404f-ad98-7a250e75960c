package com.geely.gnds.tester.controller;

import com.alibaba.fastjson.JSONArray;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.QuickLinksDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.QuickLinksService;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/quickLinks"})
@RestController
/* loaded from: QuickLinksController.class */
public class QuickLinksController {
    private static final Logger log = LoggerFactory.getLogger(QuickLinksController.class);

    @Autowired
    private QuickLinksService jh;

    @Autowired
    private Cloud cloud;

    @GetMapping({"getList"})
    public Result<Object> getList() throws Exception {
        Result<Object> result = new Result<>();
        try {
            String quickLinksListStr = this.cloud.getQuickLinksList();
            JSONArray jsonArray = JSONArray.parseArray(quickLinksListStr);
            result.ok(jsonArray);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00227));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00227), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00227), e2);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00227));
        }
        return result;
    }

    @GetMapping({"{id}"})
    public Result<Object> c(@PathVariable("id") Integer id) throws Exception {
        Result<Object> result = new Result<>();
        try {
            QuickLinksDTO dto = this.jh.queryById(id);
            result.ok(dto);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00231));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00231), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00231), e2);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00231));
        }
        return result;
    }

    @PostMapping
    public Result<Object> d(QuickLinksDTO quickLinksDto) throws Exception {
        Result<Object> result = new Result<>();
        try {
            String dto = this.cloud.a(quickLinksDto);
            result.ok(dto);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00228));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00228), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00228), e2);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00228));
        }
        return result;
    }

    @PutMapping
    public Result<Object> e(QuickLinksDTO quickLinksDto) throws Exception {
        Result<Object> result = new Result<>();
        try {
            this.cloud.b(quickLinksDto);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00229));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00229), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00229), e2);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00229));
        }
        return result;
    }

    @GetMapping({"updateSort"})
    public Result<Object> f(QuickLinksDTO quickLinksDto) throws Exception {
        Result<Object> result = new Result<>();
        try {
            this.cloud.c(quickLinksDto);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00233));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00233), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00233), e2);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00233));
        }
        return result;
    }

    @DeleteMapping({"{id}"})
    public Result<Object> h(@PathVariable("id") Long id) throws Exception {
        Result<Object> result = new Result<>();
        try {
            this.cloud.c(id);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00230));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00230), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00230), e2);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00230));
        }
        return result;
    }
}
