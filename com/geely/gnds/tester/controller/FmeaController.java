package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.DiaGcidCheckDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.service.FmeaService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.vo.DiaCscVO;
import com.geely.gnds.tester.vo.DiaFmeaAndCscVo;
import com.geely.gnds.tester.vo.DiaFmeaVO;
import com.geely.gnds.tester.vo.DiaGcidCheckDataVo;
import com.geely.gnds.tester.vo.DiaGcidVo;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"api/v1/"})
@RestController
/* loaded from: FmeaController.class */
public class FmeaController {

    @Autowired
    private FmeaService iW;
    private static final Logger LOG = LoggerFactory.getLogger(FmeaController.class);

    @GetMapping({"/fmea/network/list"})
    public Result<List<DiaFmeaVO>> bn(@RequestParam String vin) throws Exception {
        if (ObjectUtils.isEmpty(vin)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00168));
        }
        return new Result().ok(this.iW.getNetworkList(vin, "Network"));
    }

    @GetMapping({"/fmea/standard/list"})
    public Result<List<DiaFmeaVO>> bo(@RequestParam String vin) throws Exception {
        if (ObjectUtils.isEmpty(vin)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00168));
        }
        return new Result().ok(this.iW.getStandardList(vin, "Standard"));
    }

    @GetMapping({"/fmea/standard/csc/list"})
    public Result<List<DiaCscVO>> d(@RequestParam String vin, @RequestParam Long fmeaId) throws Exception {
        if (ObjectUtils.isEmpty(vin) || ObjectUtils.isEmpty(fmeaId)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00168));
        }
        return new Result().ok(this.iW.getStandardCscList(vin, fmeaId));
    }

    @GetMapping({"fmea/fmeaAndCsc/list"})
    public Result<List<DiaFmeaAndCscVo>> bp(@RequestParam String vin) throws Exception {
        if (ObjectUtils.isEmpty(vin)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00168));
        }
        return new Result().ok(this.iW.getStandardFemaAndCscList(vin, "Standard"));
    }

    @GetMapping({"/fmea/network/gcid/list"})
    public Result<List<DiaGcidVo>> e(@RequestParam String vin, @RequestParam Long fmeaId) throws Exception {
        if (ObjectUtils.isEmpty(fmeaId) || ObjectUtils.isEmpty(vin)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00168));
        }
        return new Result().ok(this.iW.getGcidList(vin, fmeaId));
    }

    @GetMapping({"/fmea/faultTracing/updateScore"})
    public Result<Object> b(@RequestParam int type, @RequestParam Long fmeaId, @RequestParam Long gcId, @RequestParam String csc) throws Exception {
        LOG.info("用户反馈意见打分接口入参：type【{}】，fmeaId【[]】，gcId【[]】，csc【[]】", new Object[]{Integer.valueOf(type), fmeaId, gcId, csc});
        this.iW.updateScore(type, fmeaId, gcId, csc);
        return new Result<>();
    }

    @GetMapping({"/fmea/standard/gcid/list"})
    public Result<List<DiaGcidVo>> b(@RequestParam String vin, @RequestParam Long fmeaId, String csc) throws Exception {
        if (ObjectUtils.isEmpty(fmeaId) || ObjectUtils.isEmpty(vin)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00168));
        }
        return new Result().ok(this.iW.getStandardGcidList(vin, fmeaId, csc));
    }

    @GetMapping({"/dtc/fmea/list"})
    public Result<List<DiaFmeaVO>> O(String vin, @RequestParam String dtcId, String ecuName) throws Exception {
        if (ObjectUtils.isEmpty(dtcId)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00168));
        }
        return new Result().ok(this.iW.getDtcFmeaList(vin, dtcId, ecuName));
    }

    @GetMapping({"/dtc/fema/function/list"})
    public Result<List<DiaFmeaAndCscVo>> P(@RequestParam String vin, @RequestParam String dtcId, @RequestParam String diagnosticNumber) throws Exception {
        if (StringUtils.isBlank(dtcId)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00168));
        }
        if (StringUtils.isBlank(vin)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00286));
        }
        if (StringUtils.isBlank(diagnosticNumber)) {
            return new Result().error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00287));
        }
        return new Result().ok(this.iW.getDtcFemaFunctionList(vin, dtcId, diagnosticNumber));
    }

    @GetMapping({"/fmea/getDtcList"})
    public Result<List<DtcInfoDTO>> a(String listType, String vin, Long fmeaId, String dtcId) throws Exception {
        return new Result().ok(this.iW.getDtcList(listType, vin, fmeaId, dtcId));
    }

    @PostMapping({"/fmea/network/gcid/check"})
    public Result<List<DiaGcidCheckDataVo>> a(@RequestBody DiaGcidCheckDTO dto) throws Exception {
        Result<List<DiaGcidCheckDataVo>> result = new Result<>();
        try {
            LOG.info("======> 客户端开始请求/api/v1/fmea/network/gcid/check下的getNetworkGcidCheckData接口,参数vin:{} <======", dto.getVin());
            result.setData(this.iW.getGcidCheckdata(dto));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            LOG.info("======> 客户端结束请求/api/v1/fmea/network/gcid/check下的getNetworkGcidCheckData接口,参数vin:{} <======", dto.getVin());
        } catch (Exception e) {
            LOG.error("getNetworkGcidCheckData接口异常：", e);
            GlobalException.a(e, TesterErrorCodeEnum.SG00281);
        }
        return result;
    }
}
