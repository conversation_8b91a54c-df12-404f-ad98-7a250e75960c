package com.geely.gnds.tester.controller;

import cn.hutool.core.io.IoUtil;
import com.geely.gnds.ruoyi.common.utils.file.MimeTypeUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({""})
@RestController
/* loaded from: UiController.class */
public class UiController {

    @Value("${tester.ui.contentType}")
    private String contentType;
    private final String jv = "\\.";
    private final String jw = "assets/";
    private final String jx = "application/octet-stream";
    private final String jy = "image/x-icon";
    private final String jz = "application/x-javascript";
    private final String GROUP_SPLIT = ConstantEnum.COMMA;
    private final String jA = "\\|";
    private Map<String, String> jB = null;
    private final String jC = "/ds";
    private final String jD = "ds/";
    private final String jE = "index.html";
    private final String jF = "print.html";
    private final String jG = "\r\n";
    private final String jH = "";
    private boolean jI = false;
    private String jJ = null;
    private static final Logger LOG = LoggerFactory.getLogger(UiController.class);

    private String ac() throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("ds/index.html");
        if (classPathResource.exists()) {
            return IoUtil.read(classPathResource.getInputStream(), StandardCharsets.UTF_8);
        }
        return null;
    }

    private void a(Closeable closeable) throws IOException {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
            }
        }
    }

    private boolean cb(String str) {
        return str == null || "".equals(str.trim());
    }

    private Map<String, String> cc(String contentTypeString) {
        Map<String, String> imgList = new HashMap<>(20);
        if (contentTypeString != null && !"".equals(contentTypeString.trim())) {
            String[] paths = contentTypeString.split(ConstantEnum.COMMA);
            for (String path : paths) {
                if (path != null && path.trim().length() > 0) {
                    String[] strs = path.split("\\|");
                    if (strs.length == 2 && !cb(strs[0]) && !cb(strs[1])) {
                        imgList.put(strs[0].toLowerCase(), strs[1]);
                    }
                }
            }
        }
        return imgList;
    }

    private void a(HttpServletResponse response, String name) throws IOException {
        InputStream inputStream = null;
        try {
            try {
                inputStream = new ClassPathResource("ds/" + StringUtils.cleanPath(name)).getInputStream();
                ServletOutputStream outputStream = response.getOutputStream();
                byte[] buffer = new byte[4096];
                while (true) {
                    int len = inputStream.read(buffer);
                    if (len != -1) {
                        outputStream.write(buffer, 0, len);
                    } else {
                        outputStream.flush();
                        a(inputStream);
                        return;
                    }
                }
            } catch (IOException e) {
                throw e;
            }
        } catch (Throwable th) {
            a(inputStream);
            throw th;
        }
    }

    @PostConstruct
    public void init() {
        this.jB = cc(this.contentType);
        ad();
    }

    private void ad() {
        if (!this.jI) {
            try {
                this.jJ = ac();
                this.jI = true;
            } catch (IOException e) {
                LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00049), e);
            }
        }
    }

    @RequestMapping({""})
    public void a(HttpServletResponse response) throws IOException {
        LOG.debug("root");
        response.sendRedirect("/ds");
    }

    @RequestMapping({"/"})
    public void b(HttpServletResponse response) throws IOException {
        LOG.debug("root/");
        response.sendRedirect("/ds");
    }

    @RequestMapping({"/ds"})
    public String ae() {
        LOG.debug("home");
        ad();
        return this.jJ;
    }

    @RequestMapping({"/ds/assets/{filename}"})
    public void b(HttpServletResponse response, @PathVariable("filename") String filename) throws IOException {
        try {
            String[] strs = filename.split("\\.");
            String contextType = null;
            if (strs.length > 1) {
                String ext = strs[strs.length - 1].toLowerCase();
                contextType = this.jB.get(ext);
            }
            if (contextType == null) {
                contextType = "application/octet-stream";
            }
            response.setContentType(contextType);
            a(response, "assets/" + filename);
        } catch (IOException e) {
            throw e;
        }
    }

    @RequestMapping({"/ds/font-awesome/{filename}"})
    public void c(HttpServletResponse response, @PathVariable("filename") String filename) throws IOException {
        try {
            String[] strs = filename.split("\\.");
            String contextType = null;
            if (strs.length > 1) {
                String ext = strs[strs.length - 1].toLowerCase();
                contextType = this.jB.get(ext);
            }
            if (contextType == null) {
                contextType = "application/octet-stream";
            }
            response.setContentType(contextType);
            a(response, "font-awesome/" + filename);
        } catch (IOException e) {
            throw e;
        }
    }

    @RequestMapping({"/ds/favicon.ico"})
    public void c(HttpServletResponse response) throws IOException {
        LOG.debug("favicon.ico");
        response.setContentType("image/x-icon");
        a(response, "favicon.ico");
    }

    @RequestMapping({"/ds/"})
    public String af() {
        LOG.debug("index:::::::::::::::::/ds/");
        ad();
        return this.jJ;
    }

    @RequestMapping({"/ds/{name}"})
    public String cd(@PathVariable("name") String name) {
        LOG.debug("name:::::::::::::::::/ds/" + name);
        ad();
        return this.jJ;
    }

    @RequestMapping({"/ds/print.html"})
    public String ag() throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("ds/print.html");
        if (classPathResource.exists()) {
            return IoUtil.read(classPathResource.getInputStream(), StandardCharsets.UTF_8);
        }
        return null;
    }

    @RequestMapping({"/ds/system/{level2}"})
    public String ce(@PathVariable("level2") String level2) {
        LOG.debug("name:::::::::::::::::/ds/system/" + level2);
        ad();
        return this.jJ;
    }

    @RequestMapping({"/ds/monitor/{level2}"})
    public String cf(@PathVariable("level2") String level2) {
        LOG.debug("name:::::::::::::::::/ds/monitor/" + level2);
        ad();
        return this.jJ;
    }

    @RequestMapping({"/ds/tool/{level2}"})
    public String cg(@PathVariable("level2") String level2) {
        LOG.debug("name:::::::::::::::::/ds/tool/" + level2);
        ad();
        return this.jJ;
    }

    @RequestMapping({"/ds/jsencrypt.min.js"})
    public void d(HttpServletResponse response) throws IOException {
        LOG.debug("jsencrypt.min.js");
        response.setContentType("application/x-javascript");
        a(response, "jsencrypt.min.js");
    }

    @RequestMapping({"/ds/html2canvas.min.js"})
    public void e(HttpServletResponse response) throws IOException {
        LOG.debug("html2canvas.min.js");
        response.setContentType("application/x-javascript");
        a(response, "html2canvas.min.js");
    }

    @RequestMapping({"/ds/template.js"})
    public void f(HttpServletResponse response) throws IOException {
        LOG.debug("template.js");
        response.setContentType("application/x-javascript");
        a(response, "template.js");
    }

    @RequestMapping({"/ds/svg.min.js"})
    public void g(HttpServletResponse response) throws IOException {
        LOG.debug("svg.min.js");
        response.setContentType("application/x-javascript");
        a(response, "svg.min.js");
    }

    @RequestMapping({"/ds/help/**"})
    public void b(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String url = URLDecoder.decode(request.getRequestURI(), "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource(url);
        if (url.contains(".js")) {
            response.setContentType("application/x-javascript");
        }
        if (url.contains(".html") || url.contains(".htm")) {
            response.setContentType("text/html;charset=UTF-8");
        }
        if (url.contains(".css")) {
            response.setContentType("text/css");
        }
        if (url.contains(".png")) {
            response.setContentType(MimeTypeUtils.IMAGE_PNG);
        }
        if (classPathResource.exists()) {
            InputStream inputStream = null;
            ServletOutputStream outputStream = null;
            try {
                try {
                    inputStream = classPathResource.getInputStream();
                    outputStream = response.getOutputStream();
                    byte[] buffer = new byte[4096];
                    while (true) {
                        int len = inputStream.read(buffer);
                        if (len != -1) {
                            outputStream.write(buffer, 0, len);
                        } else {
                            outputStream.flush();
                            a(inputStream);
                            a((Closeable) outputStream);
                            return;
                        }
                    }
                } catch (IOException e) {
                    throw e;
                } catch (ClientAbortException e2) {
                    response.setContentType("application/json");
                    a(inputStream);
                    a((Closeable) outputStream);
                }
            } catch (Throwable th) {
                a(inputStream);
                a((Closeable) outputStream);
                throw th;
            }
        }
    }
}
