package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.dto.VehicleManagementDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.VehicleManagementService;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/vehicleManagement"})
@RestController
/* loaded from: VehicleManagementController.class */
public class VehicleManagementController {

    @Autowired
    private VehicleManagementService jO;

    @Autowired
    private TokenService tokenService;
    private static final Logger log = LoggerFactory.getLogger(VehicleManagementController.class);

    @GetMapping({"getVehicleList"})
    public Result<Object> getVehicleList() {
        Result<Object> result = new Result<>();
        log.info("------------------------执行获取车辆管理列表接口-------------------------------");
        try {
            result.setData(this.jO.getVehicleList());
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00197));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00197), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00197), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00197);
        }
        return result;
    }

    @PostMapping({"disconnectVehicle"})
    public Result<Object> a(@RequestBody VehicleManagementDto vehicleManagementDto) {
        Result<Object> result = new Result<>();
        log.info("------------------------执行获取车辆管理列表接口-------------------------------");
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            String username = loginUser.getUsername();
            result.setData(this.jO.disconnectVehicle(vehicleManagementDto, username));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00196));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00196), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00196), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00196);
        }
        return result;
    }

    @PostMapping({"confirmDisconnect"})
    public Result<Object> b(@RequestBody VehicleManagementDto vehicleManagementDto) {
        Result<Object> result = new Result<>();
        log.info("------------------------管理员确认断开车辆连接-------------------------------");
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            String username = loginUser.getUsername();
            this.jO.confirmDisconnect(vehicleManagementDto, username);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00203));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00203), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00203), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00203);
        }
        return result;
    }
}
