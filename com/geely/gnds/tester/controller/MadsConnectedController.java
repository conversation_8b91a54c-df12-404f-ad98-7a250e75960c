package com.geely.gnds.tester.controller;

import com.alibaba.fastjson.JSON;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.MadsOrderDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/mads"})
@RestController
/* loaded from: MadsConnectedController.class */
public class MadsConnectedController {
    private static final Logger log = LoggerFactory.getLogger(MadsConnectedController.class);

    @Autowired
    private TokenService tokenService;

    @Autowired
    private Cloud cloud;

    @GetMapping({"/connectedVehicle"})
    public Result<Object> bf(@RequestParam("vin") String vin) {
        log.info("======> 客户端开始请求mads连车检查接口，入参vin：{} <======", vin);
        Result<Object> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            String res = this.cloud.ba(vin);
            result.setCode(HttpStatus.SUCCESS);
            result.setData(res);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        log.info("======> mads连车检查接口，耗时：{}ms <======", Long.valueOf(System.currentTimeMillis() - start));
        return result;
    }

    @PostMapping({"/createOrder"})
    public Result<Object> a(@RequestBody MadsOrderDto dto) {
        log.info("======> 客户端开始请求mads创建工单接口，入参:{} <======", JSON.toJSON(dto));
        Result<Object> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            String res = this.cloud.G(dto.getVin(), dto.getReason(), dto.getModel());
            result.setCode(HttpStatus.SUCCESS);
            result.setData(res);
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        log.info("======> mads创建工单接口，耗时：{}ms <======", Long.valueOf(System.currentTimeMillis() - start));
        return result;
    }

    @GetMapping({"/orderBalance"})
    public Result<Integer> W() {
        log.info("======> 客户端开始请求查询mads用户工单余额接口 <======");
        Result<Integer> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            String res = this.cloud.M();
            result.setCode(HttpStatus.SUCCESS);
            result.setData(Integer.valueOf(res));
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        log.info("======> 查询mads用户工单余额接口，耗时：{}ms <======", Long.valueOf(System.currentTimeMillis() - start));
        return result;
    }
}
