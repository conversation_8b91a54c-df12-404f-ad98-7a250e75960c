package com.geely.gnds.tester.controller;

import com.geely.gnds.doip.client.enums.XmlStatusEnum;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.dto.FeedbackDTO;
import com.geely.gnds.tester.dto.VehicleTodoDTO;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.service.VehicleTodoService;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.Result;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/vehicleTodo"})
@RestController
/* loaded from: VehicleTodoController.class */
public class VehicleTodoController {
    private static final Logger log = LoggerFactory.getLogger(VehicleTodoController.class);

    @Autowired
    private VehicleTodoService jP;

    @GetMapping({"/list"})
    public Result<List<VehicleTodoDTO>> ck(@RequestParam String vin) throws Exception {
        Result<List<VehicleTodoDTO>> result = new Result().ok(new ArrayList());
        if (StringUtils.isEmpty(vin)) {
            log.error("请求车辆待办列表时VIN号为空");
            return result;
        }
        try {
            result.ok(this.jP.getVehicleTodoList(vin));
        } catch (Exception e) {
            log.error("请求车辆待办列表异常,e={}", e);
        }
        return result;
    }

    @PostMapping({"/feedback"})
    public Result<Boolean> b(String vin, Long vehicleTodoId, Integer state) throws Exception {
        if (StringUtils.isEmpty(vin)) {
            log.error("请求车辆待办结果反馈时VIN号为空");
            throw new Exception(MessageUtils.getMessage("VIN number cannot be empty"));
        }
        return c(vin, vehicleTodoId, state);
    }

    private Result<Boolean> c(String vin, Long vehicleTodoId, Integer state) throws Exception {
        log.info("getFeedbackResult接口入参 vin:{},vehicleTodoId:{},state:{}", new Object[]{vin, vehicleTodoId, state});
        Result<Boolean> result = new Result<>();
        if (ObjectUtils.isEmpty(vehicleTodoId)) {
            log.error("请求车辆待办结果反馈时vehicleTodoId为空");
            throw new Exception(MessageUtils.getMessage("Vehicle pending ID cannot be empty"));
        }
        if (ObjectUtils.isEmpty(state)) {
            log.error("请求车辆待办结果反馈时state为空");
            throw new Exception(MessageUtils.getMessage("The status cannot be empty"));
        }
        if (StringUtils.isEmpty(vin)) {
            log.error("请求车辆待办结果反馈时VIN号为空");
            throw new Exception(MessageUtils.getMessage("VIN number cannot be empty"));
        }
        result.ok(this.jP.vehicleTodoFeedback(vin, vehicleTodoId, state));
        return result;
    }

    @PostMapping({"/autoFeedback"})
    public Result<Boolean> a(@RequestBody FeedbackDTO feedbackDTO) throws Exception {
        ScriptHandler handler;
        String vin = feedbackDTO.getVin();
        Long vehicleTodoId = feedbackDTO.getVehicleTodoId();
        String seqCode = feedbackDTO.getSeqCode();
        log.info("autoFeedback接口入参 vin:{},vehicleTodoId:{},seqCode:{}", new Object[]{vin, vehicleTodoId, seqCode});
        if (!StringUtils.isEmpty(seqCode) && (handler = SeqManager.getInstance().getScriptHandler(vin + seqCode)) != null) {
            String seqResult = handler.getSeqResult();
            boolean resultSetBySeq = handler.isResultSetBySeq();
            log.info("autoFeedback获取到seqResult：{},resultSetBySeq:{}", seqResult, Boolean.valueOf(resultSetBySeq));
            if (XmlStatusEnum.STATUS_SUCCESS.getValue().equals(seqResult) && resultSetBySeq) {
                return c(vin, vehicleTodoId, 2);
            }
        }
        return c(vin, vehicleTodoId, -1);
    }
}
