package com.geely.gnds.tester.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.EcuService;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/ecu"})
@RestController
/* loaded from: EcuController.class */
public class EcuController {

    @Autowired
    private EcuService iU;
    private static final Logger log = LoggerFactory.getLogger(EcuController.class);

    @GetMapping({"getSeqByEcu"})
    public Result<Object> O(String ecuName, String vin) {
        log.info("======> 客户端开始请求api/v1/ecu下的的getSeqByEcu接口 <======");
        Result<Object> result = new Result<>();
        try {
            log.info("======> 客户端开始调用ecuService的getSeqByEcu方法 <======");
            result.setData(this.iU.getSeqByEcu(ecuName, vin));
            log.info("======> 客户端调用ecuService的getSeqByEcu方法结束 <======");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00022));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00022), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00022);
        }
        log.info("======> 客户端请求api/v1/ecu下的的getSeqByEcu接口结束 <======");
        return result;
    }

    @GetMapping({"getEcuList"})
    public Result<Object> bl(String vin) {
        log.info("======> 客户端开始请求api/v1/ecu下的的getDtcList接口 <======");
        Result<Object> result = new Result<>();
        try {
            log.info("======> 客户端开始调用ecuService的getEcuList方法 <======");
            result.setData(this.iU.getEcuList(vin));
            log.info("======> 客户端调用ecuService的getEcuList方法结束 <======");
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00023));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00023), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00023);
        }
        log.info("======> 客户端请求api/v1/ecu下的的getDtcList接口结束 <======");
        return result;
    }

    @GetMapping({"/getEcuReadout"})
    public Result<Object> P(@RequestParam("vin") String vin, @RequestParam("ecuName") String ecuName) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.iU.getEcuReadout(vin, ecuName));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(e.getMessage());
            log.error("获取ECU识别信息接口异常", e);
        } catch (Exception e2) {
            GlobalException.c(e2);
        }
        return result;
    }
}
