package com.geely.gnds.tester.dao;

import com.geely.gnds.tester.entity.DsaSequenceEntity;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
/* loaded from: DsaSequenceDao.class */
public interface DsaSequenceDao {
    List<DsaSequenceEntity> queryList(@Param("params") Map<String, Object> map);

    void create(DsaSequenceEntity dsaSequenceEntity);

    void update(DsaSequenceEntity dsaSequenceEntity);

    DsaSequenceEntity selectByBtnCode(String str);

    List<DsaSequenceEntity> selectByBtnCodes(List list);
}
