package com.geely.gnds.tester.dao;

import com.geely.gnds.tester.entity.TesterSoftwareEntity;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
/* loaded from: TesterSoftwareDao.class */
public interface TesterSoftwareDao {
    List<TesterSoftwareEntity> queryList(@Param("params") Map<String, Object> map);

    void create(TesterSoftwareEntity testerSoftwareEntity);

    void update(TesterSoftwareEntity testerSoftwareEntity);

    TesterSoftwareEntity selectByFileName(String str);

    void delete(Long l);
}
