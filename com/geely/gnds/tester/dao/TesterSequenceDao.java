package com.geely.gnds.tester.dao;

import com.geely.gnds.tester.entity.TesterSequenceEntity;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
/* loaded from: TesterSequenceDao.class */
public interface TesterSequenceDao {
    List<TesterSequenceEntity> queryList(@Param("params") Map<String, Object> map);

    void create(TesterSequenceEntity testerSequenceEntity);

    void update(TesterSequenceEntity testerSequenceEntity);

    TesterSequenceEntity selectBySeqCode(String str);
}
