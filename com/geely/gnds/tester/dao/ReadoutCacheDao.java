package com.geely.gnds.tester.dao;

import com.geely.gnds.tester.dto.TesterReadoutCacheDTO;
import com.geely.gnds.tester.entity.TesterReadoutCacheEntity;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
/* loaded from: ReadoutCacheDao.class */
public interface ReadoutCacheDao {
    void save(TesterReadoutCacheEntity testerReadoutCacheEntity);

    TesterReadoutCacheEntity getById(Long l);

    List<TesterReadoutCacheDTO> getLasted(@Param("vin") String str);

    void updateReadoutCache(TesterReadoutCacheEntity testerReadoutCacheEntity);

    TesterReadoutCacheEntity getInfo(@Param("vin") String str, @Param("createTime") String str2);

    TesterReadoutCacheEntity getLastVehicleConfig(@Param("vin") String str);

    int deleteById(Long l);

    List<TesterReadoutCacheEntity> getAllReadoutCache(@Param("updateTime") String str);

    int deleteByIds(@Param("ids") List<Long> list);
}
