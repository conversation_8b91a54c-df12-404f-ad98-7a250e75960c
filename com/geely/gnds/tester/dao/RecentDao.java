package com.geely.gnds.tester.dao;

import com.geely.gnds.tester.entity.TesterRecentEntity;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
/* loaded from: RecentDao.class */
public interface RecentDao {
    List<TesterRecentEntity> getVehicles(@Param("operator") String str);

    List<TesterRecentEntity> getVehicle(@Param("operator") String str, @Param("vin") String str2);

    void addVehicle(TesterRecentEntity testerRecentEntity);

    void updateVehicle(TesterRecentEntity testerRecentEntity);
}
