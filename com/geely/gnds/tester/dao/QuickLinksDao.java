package com.geely.gnds.tester.dao;

import com.geely.gnds.tester.dto.QuickLinksDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
/* loaded from: QuickLinksDao.class */
public interface QuickLinksDao {
    QuickLinksDTO queryById(Integer num);

    List<QuickLinksDTO> getList(String str);

    int insert(QuickLinksDTO quickLinksDTO);

    int update(QuickLinksDTO quickLinksDTO);

    void updateSort(QuickLinksDTO quickLinksDTO);

    int deleteById(Long l);
}
