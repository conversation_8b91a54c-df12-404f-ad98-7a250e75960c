package com.geely.gnds.tester.component;

import com.geely.gnds.tester.cache.FileCache;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
/* loaded from: CloudFactory.class */
public class CloudFactory {

    @Autowired
    private Cloud cloud;

    @Autowired
    private FileCache fileCache;
    private static Cloud iw;
    private static FileCache ix;

    @PostConstruct
    public void init() {
        iw = this.cloud;
        ix = this.fileCache;
    }

    public static Cloud getCloud() {
        return iw;
    }

    public static FileCache getFileCache() {
        return ix;
    }
}
