package com.geely.gnds.tester.component;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.xml.XmlFault;
import com.geely.gnds.dsa.dto.WorkOrderAuthorityQueryDTO;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.common.utils.http.NetQualityUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.ruoyi.framework.web.page.TableSupport;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.tester.CloudApi;
import com.geely.gnds.tester.backprocess.BackData;
import com.geely.gnds.tester.backprocess.BackProcessor;
import com.geely.gnds.tester.cache.MapDBCache;
import com.geely.gnds.tester.cache.MapDBUtil;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.common.CacheUtils;
import com.geely.gnds.tester.common.EncryptRequest;
import com.geely.gnds.tester.common.TesterContext;
import com.geely.gnds.tester.common.UploadLimit;
import com.geely.gnds.tester.config.ICloudURI;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.CertResultDTO;
import com.geely.gnds.tester.dto.DrCscNode;
import com.geely.gnds.tester.dto.DroDTO;
import com.geely.gnds.tester.dto.DroRecordDTO;
import com.geely.gnds.tester.dto.EcuDto;
import com.geely.gnds.tester.dto.EcuPinCodeDto;
import com.geely.gnds.tester.dto.LogUploadServerDto;
import com.geely.gnds.tester.dto.LoginDto;
import com.geely.gnds.tester.dto.OrtResultDTO;
import com.geely.gnds.tester.dto.QuickLinksDTO;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.dto.RemoteReadoutParamDto;
import com.geely.gnds.tester.dto.ServerListMenuDTO;
import com.geely.gnds.tester.dto.SimpleResultDTO;
import com.geely.gnds.tester.dto.SoftwareDto;
import com.geely.gnds.tester.dto.SupportWorkOrderDto;
import com.geely.gnds.tester.dto.TechnicalReqDTO;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.dto.UpgradeDto;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.dto.gbop.gidp.GidpActivityDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisOrderDetailOrderDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTjDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTokenDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTrDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpUpdateDiagnosisOrderDTO;
import com.geely.gnds.tester.dto.xml.SysRequestDTO;
import com.geely.gnds.tester.entity.DsaSequenceEntity;
import com.geely.gnds.tester.enums.CloudURI;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.I18nKeyEnums;
import com.geely.gnds.tester.enums.SoftwareTypeEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.CloudInterfaceException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.security.CloudApiRsaUtils;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.LoginService;
import com.geely.gnds.tester.util.AesUtils;
import com.geely.gnds.tester.util.MachineUtil;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.RsaUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import com.geely.gnds.tester.util.VehicleUtils;
import com.geely.gnds.tester.vo.DrCreateMaintenanceTaskVO;
import com.geely.gnds.tester.vo.language.DtcListVo;
import com.geely.gnds.tester.vo.language.DtcQueryListVo;
import com.geely.gnds.tester.vo.language.GetDataIdentifierParam;
import com.geely.gnds.tester.vo.language.LanguageVo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Predicate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;
import javax.annotation.PreDestroy;
import javax.servlet.ServletContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.WebApplicationContext;

@Order(0)
@Component
/* loaded from: Cloud.class */
public class Cloud implements CloudApi, InitializingBean {

    @Value("${fd.cloud.url}")
    private String baseUrl;

    @Value("${fd.cloud.seqType}")
    private String seqType;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private LoginService hs;

    @Autowired
    private TesterConfigDao eC;

    @Autowired
    private CacheUtils hp;

    @Autowired
    private MapDBUtil mapDbUtil;

    @Autowired
    private ICloudURI in;

    @Autowired
    private VehicleUtils io;

    @Autowired
    private TesterThread testerThread;

    @Value("${tester.vlink.enabled:false}")
    private Boolean vLinkEnabled;
    private static final String METHOD_SECRET = "gnds.getSecret";
    private static final String METHOD_LOGIN = "gnds.login";
    private static final String ir = "gnds.getTesterToken";
    private SingletonManager manager = SingletonManager.getInstance();
    private SeqManager seqManager = SeqManager.getInstance();
    private BackProcessor<CloudApiLogData> is = new BackProcessor<CloudApiLogData>("apiLog", "云端接口调用情况记录器") { // from class: com.geely.gnds.tester.component.Cloud.1
        @Override // com.geely.gnds.tester.backprocess.BackProcessor
        public void a(BackData<CloudApiLogData> data) {
            Cloud.LOG.info("云端接口:{}于调用调用{}成功;返回数据：{}", new Object[]{data.getData().getMethodName(), DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS, data.getTime()), data.getData().getData()});
        }
    };

    @Value("${tester.tenantCode}")
    private String testerTenantCode;

    @Value("${tester.tenantName}")
    private String tenantName;
    private static final Logger LOG = LoggerFactory.getLogger(Cloud.class);
    private static Set<String> iq = new CopyOnWriteArraySet();
    private static JSONArray eQ = JSONArray.parseArray("[\"gnds.getTesterToken\",\"gnds.login\",\"gnds.getOssConfig\",\"gnds.getTargetSoftwareVehicles\",\"data.common.ota.searchVehicleTragetSoftware\",\"gnds.getSecret\",\"data.inner.getUsersDetail\",\"gnds.getPinCode\"]");
    private static List<String> it = new ArrayList();

    @PreDestroy
    public void destroy() {
        LOG.info("调用我关闭");
        this.is.G();
    }

    public String getTesterTenantCode() {
        return this.testerTenantCode;
    }

    public void afterPropertiesSet() throws Exception {
        TokenManager.setSecret(getSecret());
    }

    private Object a(Map params, String url) throws Exception {
        Map map = b(params);
        String res = HttpUtils.sendNewPost(url, map);
        c(params, res);
        return res;
    }

    private String b(Map params, String url) throws Exception {
        Map map = b(params);
        try {
            String resData = HttpUtils.sendNewPost(url, map);
            String res = d(params, resData);
            return res;
        } catch (Exception e) {
            Object bizContent = params.get("bizContent");
            a(url, e.getMessage(), bizContent);
            throw e;
        }
    }

    private Object a(Map params, String url, int count, int time) throws Exception {
        try {
            Map map = b(params);
            String res = HttpUtils.sendNewPost(url, map, count, time);
            c(params, res);
            return res;
        } catch (Exception e) {
            Object bizContent = params.get("bizContent");
            a(url, e.getMessage(), bizContent);
            throw e;
        }
    }

    private String b(Map params, String url, int count, int time) throws Exception {
        Map map = b(params);
        try {
            String resData = HttpUtils.sendNewPost(url, map, count, time);
            String res = d(params, resData);
            return res;
        } catch (Exception e) {
            Object bizContent = params.get("bizContent");
            a(url, e.getMessage(), bizContent);
            throw e;
        }
    }

    public String getToken() throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", ir);
        params.put("bizContent", "");
        String token = b(params, this.baseUrl + "/dataManagement/api/inner/getTesterToken").toString();
        return token;
    }

    @UploadLimit
    public String l(String type, String vin) throws Exception {
        return a(type, vin, (Boolean) false);
    }

    @UploadLimit
    public String a(String type, String vin, Boolean dsa) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("btnCode", type);
        content.put("status", an(vin));
        content.put("vin", vin);
        content.put("dsa", dsa);
        params.put("method", "gnds.getSeqCodeByBtnCode");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String seqCode = b(params, this.baseUrl + "/dataManagement/api/inner/getSeqCodeByBtnCode").toString();
        if (StringUtils.isBlank(seqCode)) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00236));
        }
        return seqCode;
    }

    @UploadLimit
    public String c(List<String> vins, String username) throws Exception {
        return a(vins, username, true);
    }

    @UploadLimit
    public String a(List<String> vins, String username, boolean needThrowException) throws Exception {
        Object token;
        String res = "[]";
        try {
            ServletContext servletContext = this.webApplicationContext.getServletContext();
            token = servletContext.getAttribute(username.toLowerCase() + "-token");
        } catch (Exception e) {
            LOG.info("getVehicleInfo异常,e={}", e);
            if (needThrowException) {
                throw e;
            }
        }
        if (token == null) {
            throw new UnAuthException("云端token失效", Integer.valueOf(HttpStatus.UNAUTHORIZED));
        }
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("vins", vins);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", "gnds.getBaseinfoVehicles");
        params.put(Constants.TOKEN, token);
        String data = b(params, this.baseUrl + "/dataManagement/api/common/getBaseinfoVehicles");
        LOG.info("getVehicleInfo data ={}", data);
        Object object = ObjectMapperUtils.jsonStr2Map(data).get("vehicleDatas");
        if (object != null) {
            res = ObjectMapperUtils.obj2JsonStr(object);
            List<Object> broadcasts = Arrays.asList((Object[]) ObjectMapperUtils.getInstance().readValue(res, Object[].class));
            if (!CollectionUtils.isEmpty(broadcasts)) {
                for (int i = 0; i < broadcasts.size(); i++) {
                    String bc = ObjectMapperUtils.obj2JsonStr(broadcasts.get(i));
                    String vin = ObjectMapperUtils.findStrByJsonNodeExpr(bc, "/vehicleData/vin");
                    JSONObject json = JSONObject.parseObject(bc);
                    Object eval = JSONPath.eval(json, "$.vehicleData");
                    if (eval != null) {
                        this.manager.setGlobal(GlobalVariableEnum.lY, eval.toString(), vin);
                    }
                }
            }
        }
        return res;
    }

    public List<String> d(List<String> vins, String username) throws Exception {
        Object token;
        List<String> vdns = new ArrayList<>();
        try {
            ServletContext servletContext = this.webApplicationContext.getServletContext();
            token = servletContext.getAttribute(username.toLowerCase() + "-token");
        } catch (Exception e) {
            LOG.error("getVehicleInfo 获取在线VDN异常,e={}", e);
        }
        if (token == null) {
            throw new UnAuthException("云端token失效", Integer.valueOf(HttpStatus.UNAUTHORIZED));
        }
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("vins", vins);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", "gnds.getBaseinfoVehicles");
        params.put(Constants.TOKEN, token);
        String data = b(params, this.baseUrl + "/dataManagement/api/common/getBaseinfoVehicles");
        LOG.info("getVehicleInfo data ={}", data);
        Object object = ObjectMapperUtils.jsonStr2Map(data).get("vehicleDatas");
        if (object != null) {
            String res = ObjectMapperUtils.obj2JsonStr(object);
            List<Object> broadcasts = Arrays.asList((Object[]) ObjectMapperUtils.getInstance().readValue(res, Object[].class));
            if (!CollectionUtils.isEmpty(broadcasts)) {
                for (int i = 0; i < broadcasts.size(); i++) {
                    String bc = ObjectMapperUtils.obj2JsonStr(broadcasts.get(i));
                    String vin = ObjectMapperUtils.findStrByJsonNodeExpr(bc, "/vehicleData/vin");
                    if (vins.contains(vin)) {
                        JSONObject json = JSONObject.parseObject(bc);
                        Object vdnObject = JSONPath.eval(json, "$.vehicleData.vdns");
                        if (vdnObject != null) {
                            vdns = ObjectMapperUtils.jsonStr2List(vdnObject.toString(), String.class);
                        }
                    }
                }
            }
        }
        return vdns;
    }

    @UploadLimit
    public String j(String vin, String type, String bssId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(4);
        String publicKey = CloudApiRsaUtils.getPublicKey();
        content.put("publicKey", publicKey);
        params.put("method", "gnds.getTargetSoftwareVehicles");
        content.put("language", HttpUtils.getLanguage());
        content.put("bizType", type);
        content.put("vin", vin);
        content.put("currentBssId", this.manager.getGlobal("currentBssId", vin));
        content.put("bssPackageStatus", "");
        if (SoftwareTypeEnum.TARGET_BSS.getValue().equals(type)) {
            content.put("bssPackageStatus", am(vin));
        }
        content.put("bssID", "");
        if (SoftwareTypeEnum.INPUT_BSS.getValue().equals(type)) {
            content.put("bssID", bssId);
        }
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/common/getTargetSoftwareVehicles").toString();
        m(data, "getUpgradeSoftwareList");
        return data;
    }

    private String m(String data, String methodName) {
        try {
            UpgradeDto upgradeDto = (UpgradeDto) ObjectMapperUtils.jsonStr2Clazz(data, UpgradeDto.class);
            if (upgradeDto != null) {
                LOG.info("云端接口:{}调用成功;返回数据：{}", "gnds.getTargetSoftwareVehicles", JSON.toJSONString(upgradeDto));
                Boolean encrypted = upgradeDto.getEncrypted();
                if (!ObjectUtils.isEmpty(encrypted) && encrypted.booleanValue() && "callApi".equals(methodName)) {
                    List<EcuDto> ecuDtos = upgradeDto.getEcuInstallationInstructions();
                    for (int i = 0; i < ecuDtos.size(); i++) {
                        EcuDto ecuDto = ecuDtos.get(i);
                        List<EcuPinCodeDto> ecuPinCodes = ecuDto.getEcuPinCodes();
                        if (!CollectionUtils.isEmpty(ecuPinCodes)) {
                            for (EcuPinCodeDto ecuPinCode : ecuPinCodes) {
                                String actualValue = ecuPinCode.getActualValue();
                                String defaultValue = ecuPinCode.getDefaultValue();
                                if (StringUtils.isNotEmpty(actualValue)) {
                                    ecuPinCode.setActualValue(CloudApiRsaUtils.cr(actualValue));
                                }
                                if (StringUtils.isNotEmpty(defaultValue)) {
                                    ecuPinCode.setDefaultValue(CloudApiRsaUtils.cr(defaultValue));
                                }
                            }
                        }
                        for (SoftwareDto softwareDto : ecuDto.getSoftwares()) {
                            String url = softwareDto.getUrl();
                            if (StringUtils.isNotBlank(url)) {
                                softwareDto.setUrl(CloudApiRsaUtils.cr(url));
                            }
                        }
                    }
                }
                return ObjectMapperUtils.obj2JsonStr(upgradeDto);
            }
        } catch (Exception e) {
            LOG.error("云端接口:gnds.getTargetSoftwareVehicles记录日志失败", e);
        }
        return data;
    }

    private String am(String vin) throws NumberFormatException {
        LoginUser loginUser = TesterLoginUtils.getLoginUserByVin(this.tokenService, vin);
        SoftwareTypeEnum softwareTypeEnum = SoftwareTypeEnum.PUBLISHED;
        if (loginUser != null) {
            Integer userStatus = loginUser.getUser().getSoftwareStatus();
            SoftwareTypeEnum enumByStatus = SoftwareTypeEnum.e(userStatus);
            if (enumByStatus != null) {
                softwareTypeEnum = enumByStatus;
            }
        }
        return softwareTypeEnum.getValue();
    }

    @UploadLimit
    public String k(String vin, String seqType, String username) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        content.put("vin", vin);
        content.put("type", "CurrentBss");
        content.put("status", seqType);
        content.put("version", "1.36.0.2");
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put(ConstantEnum.USERNAME_STR, username);
        params.put("method", "gnds.getReloadSeqListV2");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getReloadSeqList/v2").toString();
        return data;
    }

    public String an(String vin) {
        Integer userStatus;
        LoginUser loginUser = TesterLoginUtils.getLoginUserByVin(this.tokenService, vin);
        String seqStatus = this.seqType;
        if (loginUser != null && (userStatus = loginUser.getUser().getSeqStatus()) != null) {
            seqStatus = userStatus.toString();
        }
        return seqStatus;
    }

    @UploadLimit
    public String n(String vin, String seqType) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        content.put("vin", vin);
        content.put("type", "OtherSeq");
        content.put("status", seqType);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getOtherSeqList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getOtherSeqList").toString();
        return data;
    }

    @UploadLimit
    public String o(String vin, String seqType) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        content.put("vin", vin);
        content.put("type", "DevelopSeq");
        content.put("status", seqType);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getDevelopSeqList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getOtherSeqList").toString();
        return data;
    }

    @UploadLimit
    public String p(String vin, String seqType) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("status", seqType);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getFixSeqList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getFixSeqList").toString();
        return data;
    }

    @UploadLimit
    public String q(String vin, String seqType) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        content.put("vin", vin);
        content.put("status", seqType);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getFunctionSeqList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getFunctionSeqList").toString();
        return data;
    }

    @Cacheable({"getQuickLinkList"})
    public String b(Long brandId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("brandId", brandId);
        params.put("method", "gnds.getQuickLinkList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getQuickLinkList").toString();
        return data;
    }

    @Cacheable({"getReleaseNoteList#1800"})
    public String ao(String language) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("language", language);
        content.put("version", "1.36.0.2");
        params.put("method", "gnds.getReleaseNoteList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getReleaseNoteList").toString();
        return data;
    }

    @Cacheable({"getVersionInfo#1800"})
    public String b(TesterConfigDto config) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("version", "1.36.0.2");
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        if (!ObjectUtils.isEmpty(config)) {
            content.put("testerCode", config.getTesterCode());
        } else {
            content.put("testerCode", null);
        }
        params.put("method", "gnds.getLatestUpgradePackage");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getLatestUpgradePackage").toString();
        return data;
    }

    public String d(String vin, String diagnosticNumber, String dtcId, String ecuName) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("identifier", dtcId);
        content.put("diagnosticNumber", diagnosticNumber);
        content.put("ecuName", ecuName);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getDmeDtcByIdentifier");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getDmeDtcByIdentifier");
        return data;
    }

    public String a(DtcListVo vo) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getDtcMapByDtcIds");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(vo));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getDtcMapByDtcIds");
        return data;
    }

    public String l(String vin, String diagnosticPartNumber, String ecuName) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("diagPartNumber", diagnosticPartNumber);
        content.put("ecuName", ecuName);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getDtcListByDiagPartNumber");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getDtcListByDiagPartNumber", 1, 16);
        return data;
    }

    public String i(List<String> ids) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(1);
        content.put("ids", ids);
        params.put("method", "gnds.getDtcListByIds");
        params.put("bizContent", ids);
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getDtcListByIds");
        return data;
    }

    public String ap(String username) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.inner.getUsersDetail");
        Map<String, Object> content = new HashMap<>(1);
        content.put(ConstantEnum.USERNAME_STR, username);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getUsersDetail").toString();
        return data;
    }

    public String aq(String username) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("status", this.seqType);
        params.put("method", "data.inner.syncOfflineSeq");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/syncOfflineSeq").toString();
        return data;
    }

    @UploadLimit
    public String callApi(String apiName, JSONObject dataJson, String username) throws Exception {
        if ("data.common.ota.searchVehicleTragetSoftware".equals(apiName)) {
            if (dataJson.containsKey("vin")) {
                String vin = dataJson.getString("vin");
                dataJson.put("bssPackageStatus", am(vin));
            }
            if (dataJson.containsKey("bizType")) {
                String biztype = dataJson.getString("bizType");
                if (StringUtils.isNotBlank(biztype) && "CurrentBss".equalsIgnoreCase(biztype)) {
                    dataJson.put("bizType", "Reload");
                }
            }
            String publicKey = CloudApiRsaUtils.getPublicKey();
            dataJson.put("publicKey", publicKey);
        } else if ("data.common.ota.createOtaTask".equals(apiName) && dataJson.containsKey("vin")) {
            String vin2 = dataJson.getString("vin");
            dataJson.put("model", aW(vin2));
        }
        Map<String, Object> params = new HashMap<>(8);
        String bizContent = ObjectMapperUtils.obj2JsonStr(dataJson);
        params.put("bizContent", bizContent.replaceAll("\\\\\"", "\""));
        params.put("method", apiName);
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        String data = a(params, this.baseUrl + "/dataManagement/api/common/service/distribute", 5, 8).toString();
        if ("data.common.ota.uploadVehicleInfo".equals(apiName)) {
            this.hp.clear("getVehicleInfo");
            this.hp.clear("getReloadSoftwareList");
            this.hp.clear("getUpgradeSoftwareList");
        }
        if ("data.common.ota.searchVehicleTragetSoftware".equals(apiName)) {
            JSONObject jsonObject = JSONObject.parseObject(data);
            String apiData = jsonObject.getString(AjaxResult.gC);
            jsonObject.put(AjaxResult.gC, m(apiData, "callApi"));
            data = jsonObject.toJSONString();
        }
        return data;
    }

    @EncryptRequest
    public String a(LoginDto loginDto, String publicKey) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        String pk = CloudApiRsaUtils.getPublicKey();
        content.put("pk", pk);
        content.put("publicKey", publicKey);
        content.put(ConstantEnum.USERNAME_STR, loginDto.getUsername());
        content.put("password", loginDto.getPassword());
        content.put("testerCode", loginDto.getTesterCode());
        content.put("motherBoard", LoginDto.getMotherBoard());
        content.put("bios", LoginDto.getBios());
        content.put("biosUuid", LoginDto.getBiosUuid());
        content.put("productId", LoginDto.getProductId());
        content.put("machineGuid", LoginDto.getMachineGuid());
        content.put("processorId", LoginDto.getProcessorId());
        content.put("biosAes", AesUtils.getBiosAes());
        content.put("diskSn", LoginDto.getDiskSn());
        content.put("cpuInfo", LoginDto.getCpuInfo());
        content.put("memoryInfo", LoginDto.getMemoryInfo());
        content.put("diskInfo", LoginDto.getDiskInfo());
        content.put("networkInfo", LoginDto.getNetworkInfo());
        content.put("mac", LoginDto.getMac());
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put(ConstantEnum.MOBILECODE_STR, loginDto.getMobileCode());
        content.put("loginType", Integer.valueOf(loginDto.getLoginType()));
        content.put("language", HttpUtils.getLanguage());
        content.put("version", "1.36.0.2");
        content.put("securityCheckResult", loginDto.getSecurityCheckResult());
        content.put("securitySoftwarePath", loginDto.getSecuritySoftwarePath());
        content.put(AjaxResult.gA, loginDto.getCode());
        content.put("redirectUri", loginDto.getRedirectUri());
        content.put("displayName", loginDto.getDisplayName());
        Boolean isGeelySsoLogin = loginDto.getGeelySsoLogin();
        if (isGeelySsoLogin != null && isGeelySsoLogin.booleanValue()) {
            content.put("geelySsoLogin", true);
            content.put("thirdPartyCode", loginDto.getThirdPartyCode());
        }
        Boolean cepLogin = loginDto.getCepLogin();
        if (cepLogin != null && cepLogin.booleanValue()) {
            content.put("cep", true);
            content.put("thirdPartyCode", loginDto.getThirdPartyCode());
        }
        content.put("cepBind", loginDto.getBindCep());
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        Object testerSessionId = servletContext.getAttribute("testerSessionId");
        content.put("testerSessionId", testerSessionId == null ? "" : testerSessionId.toString());
        if (loginDto.getRequestKey() != null) {
            content.put("requestKey", RsaUtils.encryptBase64(loginDto.getRequestKey().getEncoded()));
        }
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", CloudURI.LOGIN.getMethod());
        return a(params, this.baseUrl + this.in.getUri(CloudURI.LOGIN)).toString();
    }

    @EncryptRequest
    public String b(LoginDto loginDto, String publicKey) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        String pk = CloudApiRsaUtils.getPublicKey();
        content.put("pk", pk);
        content.put("state", loginDto.getState());
        content.put("publicKey", publicKey);
        content.put("provider", StrUtil.blankToDefault(loginDto.getProvider(), AppConfig.getProperty("tester.provider")));
        content.put(ConstantEnum.USERNAME_STR, loginDto.getUsername());
        content.put("password", loginDto.getPassword());
        content.put("testerCode", loginDto.getTesterCode());
        content.put("motherBoard", LoginDto.getMotherBoard());
        content.put("bios", LoginDto.getBios());
        content.put("biosUuid", LoginDto.getBiosUuid());
        content.put("productId", LoginDto.getProductId());
        content.put("machineGuid", LoginDto.getMachineGuid());
        content.put("processorId", LoginDto.getProcessorId());
        content.put("biosAes", AesUtils.getBiosAes());
        content.put("diskSn", LoginDto.getDiskSn());
        content.put("cpuInfo", LoginDto.getCpuInfo());
        content.put("memoryInfo", LoginDto.getMemoryInfo());
        content.put("diskInfo", LoginDto.getDiskInfo());
        content.put("networkInfo", LoginDto.getNetworkInfo());
        content.put("mac", LoginDto.getMac());
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put(ConstantEnum.MOBILECODE_STR, loginDto.getMobileCode());
        content.put("loginType", Integer.valueOf(loginDto.getLoginType()));
        content.put("language", HttpUtils.getLanguage());
        content.put("version", "1.36.0.2");
        content.put("securityCheckResult", loginDto.getSecurityCheckResult());
        content.put("securitySoftwarePath", loginDto.getSecuritySoftwarePath());
        content.put(AjaxResult.gA, loginDto.getCode());
        content.put("redirectUri", loginDto.getRedirectUri());
        content.put("displayName", loginDto.getDisplayName());
        Boolean isGeelySsoLogin = loginDto.getGeelySsoLogin();
        if (isGeelySsoLogin != null && isGeelySsoLogin.booleanValue()) {
            content.put("geelySsoLogin", true);
            content.put("thirdPartyCode", loginDto.getThirdPartyCode());
        }
        Boolean cepLogin = loginDto.getCepLogin();
        if (cepLogin != null && cepLogin.booleanValue()) {
            content.put("cep", true);
            content.put("thirdPartyCode", loginDto.getThirdPartyCode());
        }
        content.put("cepBind", loginDto.getBindCep());
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        Object testerSessionId = servletContext.getAttribute("testerSessionId");
        content.put("testerSessionId", testerSessionId == null ? "" : testerSessionId.toString());
        if (loginDto.getRequestKey() != null) {
            content.put("requestKey", RsaUtils.encryptBase64(loginDto.getRequestKey().getEncoded()));
        }
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", METHOD_LOGIN);
        return a(params, this.baseUrl + "/dataManagement/api/inner/login/oauth2").toString();
    }

    public String r(String testerCode, String username) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        content.put(ConstantEnum.USERNAME_STR, username);
        content.put("testerCode", testerCode);
        content.put("version", "1.36.0.2");
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", "gnds.logout");
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        Object attribute = servletContext.getAttribute(username.toLowerCase() + "-token");
        if (ObjectUtils.isEmpty(attribute)) {
            return null;
        }
        params.put(Constants.TOKEN, attribute);
        return a(params, this.baseUrl + "/dataManagement/api/inner/logout").toString();
    }

    private void a(String uri, String msg, Object bizContent) {
        FdTcpClient client;
        String vin = "";
        if (null != bizContent) {
            try {
                if (bizContent instanceof Map) {
                    vin = MapUtil.getStr((Map) bizContent, "vin");
                }
            } catch (Exception e) {
                LOG.info("记录logCloudErrorBackGround失败", e);
                return;
            }
        }
        LOG.info("记录logCloudErrorBackGround---vin：{},uri：{},msg：{}", new Object[]{vin, uri, msg});
        if (StringUtils.isBlank(vin)) {
            vin = getVin();
        }
        if (StrUtil.isNotBlank(vin) && null != (client = TesterContext.getFdTcpClient(vin))) {
            TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(msg);
            if (enumByCodeOrStr == null) {
                TesterErrorCodeEnum enumByCodeOrStr2 = TesterErrorCodeEnum.SG00200;
                client.getXmlLogger().a(new XmlFault(false, enumByCodeOrStr2.code(), TesterErrorCodeEnum.formatMsg(enumByCodeOrStr2) + msg, new Date(), uri));
            } else if (enumByCodeOrStr == TesterErrorCodeEnum.SG00137 && NetQualityUtils.timeout) {
                if (!it.contains(uri)) {
                    client.getXmlLogger().a(new XmlFault(false, enumByCodeOrStr.code(), msg, new Date(), uri));
                    it.add(uri);
                }
            } else {
                client.getXmlLogger().a(new XmlFault(false, enumByCodeOrStr.code(), msg, new Date(), uri));
            }
        }
    }

    private String getVin() {
        String username = "";
        try {
            if (!ObjectUtils.isEmpty(ServletUtils.getRequest())) {
                LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
                if (!ObjectUtils.isEmpty(loginUser)) {
                    username = loginUser.getUsername();
                }
            } else {
                username = ThreadLocalUtils.CURRENT_USER_NAME.get();
            }
            if (StringUtils.isBlank(username)) {
                Set<String> loginUsers = TesterLoginUtils.getLoginUsers();
                if (!CollectionUtils.isEmpty(loginUsers)) {
                    Iterator<String> iterator = loginUsers.iterator();
                    if (iterator.hasNext()) {
                        username = iterator.next();
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("获取VIN失败:{}", e.getMessage(), e);
        }
        if (StrUtil.isNotBlank(username)) {
            for (String vehicle : this.manager.getVehicles(username)) {
                if (StringUtils.isNotBlank(vehicle)) {
                    return vehicle;
                }
            }
            return "";
        }
        return "";
    }

    private void c(Map params, String res) throws Exception {
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        if (!"0".equals(code)) {
            if ("401".equals(code)) {
                if (!"gnds.logout".equals(params.get("method"))) {
                    this.hs.logout();
                }
                LOG.error("调用云端接口失败:{};失败原因:{};token失效", params.get("method"), msg);
                throw new UnAuthException("云端token失效", Integer.valueOf(HttpStatus.UNAUTHORIZED));
            }
            LOG.error("调用云端接口:{}失败;失败原因：{}", params.get("method"), msg);
            return;
        }
        CloudApiLogData logData = new CloudApiLogData(params.get("method"), data);
        if (!eQ.contains(params.get("method"))) {
            this.is.a(new Date(), logData);
        }
    }

    private String d(Map params, String res) throws CloudInterfaceException {
        return a(params, res, true);
    }

    private String a(Map params, String res, boolean tokenExpired) throws CloudInterfaceException {
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        if ("0".equals(code)) {
            if (!eQ.contains(params.get("method"))) {
                CloudApiLogData logData = new CloudApiLogData(params.get("method"), data);
                this.is.a(new Date(), logData);
            }
            if (ConstantEnum.NULL.equals(data)) {
                data = "";
            }
            return data;
        }
        if ("401".equals(code)) {
            if (tokenExpired) {
                this.hs.logout();
                ServletContext servletContext = this.webApplicationContext.getServletContext();
                if (servletContext != null) {
                    String token = (String) servletContext.getAttribute(Constants.TOKEN);
                    this.tokenService.I(token);
                }
            }
            LOG.error("调用云端接口失败:{};失败原因:{};token失效", params.get("method"), msg);
            throw new UnAuthException("云端token失效", Integer.valueOf(HttpStatus.UNAUTHORIZED));
        }
        String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200);
        LOG.error("{}:{};失败原因:{}", new Object[]{formatMsg, params.get("method"), msg});
        throw new CloudInterfaceException(msg);
    }

    private Map b(Map params) throws Exception {
        if (params.get(Constants.TOKEN) == null) {
            params.put(Constants.TOKEN, "");
        }
        LoginUser loginUser = null;
        try {
            if (!ObjectUtils.isEmpty(ServletUtils.getRequest())) {
                LOG.info("设置参数：【有】HttpServletRequest");
                loginUser = this.tokenService.c(ServletUtils.getRequest());
                if (!ObjectUtils.isEmpty(loginUser)) {
                    ServletContext servletContext = this.webApplicationContext.getServletContext();
                    String token = (String) servletContext.getAttribute(loginUser.getUsername().toLowerCase() + "-token");
                    String tokenPrint = token.length() > 10 ? token.substring(token.length() - 10) : "";
                    LOG.info("设置参数--ServletUtils.getRequest()的用户名【{}】，token【{}】", loginUser.getUsername(), tokenPrint);
                    if (StringUtils.isNotBlank(token)) {
                        params.put(Constants.TOKEN, token);
                    }
                }
            } else {
                String username = ThreadLocalUtils.CURRENT_USER_NAME.get();
                LOG.info("设置参数：【无】HttpServletRequest,以线程方式调用");
                LOG.info("设置参数--ServletUtils.getRequest()else的用户名【{}】", username);
                if (StringUtils.isNotBlank(username)) {
                    ServletContext servletContext2 = this.webApplicationContext.getServletContext();
                    String token2 = (String) servletContext2.getAttribute(username.toLowerCase() + "-token");
                    String tokenPrint2 = token2.length() > 10 ? token2.substring(token2.length() - 10) : "";
                    LOG.info("设置参数--ServletUtils.getRequest()else的用户名【{}】，token【{}】", username, tokenPrint2);
                    if (StringUtils.isNotBlank(token2)) {
                        params.put(Constants.TOKEN, token2);
                    }
                }
            }
        } catch (Exception e) {
            LOG.info("设置参数异常：{}", e.getMessage(), e);
        }
        LOG.info("设置参数--请求用户：{}", loginUser == null ? "" : loginUser.getUsername());
        String secret = TokenManager.getSecret();
        if (!METHOD_SECRET.equals(params.get("method")) && StringUtils.isBlank(secret)) {
            String secret2 = getSecret();
            TokenManager.setSecret(secret2);
        }
        return params;
    }

    @UploadLimit
    public String m(String ecuName, String vin, String seqType) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        content.put("ecuName", ecuName);
        content.put("status", seqType);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getEcuSeqList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String seqList = b(params, this.baseUrl + "/dataManagement/api/inner/getEcuSeqList");
        return seqList;
    }

    public String a(String vin, String identifier, String name, String diagnosisPartNumber, String ecuName, String type) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        content.put("vin", vin);
        content.put("identifier", identifier);
        content.put("parameterName", name);
        content.put("diagnosisPartNumber", diagnosisPartNumber);
        content.put("ecuName", ecuName);
        content.put("type", type);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.queryDidDme");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/queryDidDme");
    }

    @Cacheable({"getDidByDiaPartNumber#300"})
    public String c(String vin, String diagPartNumber, String serviceId, String ecuName, String langage) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("diagPartNumber", diagPartNumber);
        content.put("serviceId", serviceId);
        content.put("language", langage);
        content.put("ecuName", ecuName);
        params.put("method", "gnds.getDidByDiaPartNumber");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getDidByDiaPartNumber", 1, 16);
    }

    public String s(String vin, String fmeaName) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("fmea", fmeaName);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getFmeaParam");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/getParamList");
    }

    public String e(String vin, String diagPartNumber, String serviceId, String ecuName) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("diagPartNumber", diagPartNumber);
        content.put("serviceId", serviceId);
        content.put("ecuName", ecuName);
        params.put("method", "gnds.getDataIdentifierByDiaPartNumber");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getDataIdentifierByDiaPartNumber");
    }

    public String a(GetDataIdentifierParam param) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getDataIdentifierByDidIds");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(param));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getDataIdentifierByDidIds");
        return data;
    }

    public String n(String vin, String diagnosticPartNumber, String ecuName) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("diagnosticPartNumber", diagnosticPartNumber);
        content.put("ecuName", ecuName);
        params.put("method", "gnds.getEcuByDiagPartNum");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getEcuByDiagPartNum");
    }

    public String ar(String content) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.common.ota.getCertificate");
        params.put("bizContent", content);
        return b(params, this.baseUrl + "/dataManagement/api/common/getCertificate", 2, 45);
    }

    public synchronized String a(LogUploadServerDto dto, String username) throws Exception {
        String filename = dto.getFilename();
        if (StringUtils.isNotBlank(filename) && iq.contains(filename)) {
            return "";
        }
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.receiveFileRecord");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(dto));
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        String res = b(params, this.baseUrl + "/filesReceiving/api/inner/receiveFileRecord");
        iq.add(filename);
        return res;
    }

    public String a(LogUploadServerDto dto) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.updateFileRecord");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(dto));
        return b(params, this.baseUrl + "/filesReceiving/api/inner/updateFileRecord");
    }

    @EncryptRequest
    @Cacheable({"getOssConfig#3600"})
    public String getOssConfig(String vin) throws Exception {
        RSA rsa = new RSA();
        Object publicKey = Base64Utils.encodeToUrlSafeString(rsa.getPublicKey().getEncoded());
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(2);
        content.put("publicKey", publicKey);
        content.put("version", "1.36.0.2");
        params.put("method", "gnds.getOssConfig");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/filesReceiving/api/inner/getOssConfig");
        return rsa.decryptStr(data, KeyType.PrivateKey);
    }

    @EncryptRequest
    @Cacheable({"getBlobConfig#3600"})
    public String getBlobConfig() throws Exception {
        RSA rsa = new RSA();
        Object publicKey = Base64Utils.encodeToUrlSafeString(rsa.getPublicKey().getEncoded());
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(1);
        content.put("publicKey", publicKey);
        params.put("method", "gnds.getBlobConfig");
        params.put("bizContent", content);
        String data = b(params, this.baseUrl + "/filesReceiving/api/inner/getBlobConfig");
        return rsa.decryptStr(data, KeyType.PrivateKey);
    }

    public synchronized String getSecret() throws Exception {
        LOG.info(I18nKeyEnums.INITIALIZE_SECRET_KEY.getKey());
        String publicKey = CloudApiRsaUtils.getPublicKey();
        HashMap map = new HashMap(1);
        map.put("publicKey", publicKey);
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", METHOD_SECRET);
        params.put("bizContent", map);
        String token = b(params, this.baseUrl + "/dataManagement/api/inner/getSecret");
        try {
            token = CloudApiRsaUtils.cr(token);
        } catch (Exception e) {
            LOG.info("getSecret解密失败，e={},token ={}", e, token);
        }
        return token;
    }

    public void k(SysUser user) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(user));
        params.put("method", "gnds.sendUserConfig");
        a(params, this.baseUrl + "/dataManagement/api/inner/sendUserConfig");
    }

    @EncryptRequest
    public String sendMobileCode(String username, String password, int sendCodeType, Boolean cepLogin, Boolean geelySsoLogin, String thirdPartyCode) throws Exception {
        Map<String, Object> params = new HashMap<>(3);
        Map<String, Object> content = new HashMap<>(3);
        content.put(ConstantEnum.USERNAME_STR, username);
        content.put("password", password);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("sendCodeType", Integer.valueOf(sendCodeType));
        content.put("language", HttpUtils.getLanguage());
        if (cepLogin != null && cepLogin.booleanValue()) {
            content.put("cepLogin", true);
        }
        if (geelySsoLogin != null && geelySsoLogin.booleanValue()) {
            content.put("geelySsoLogin", true);
        }
        content.put("thirdPartyCode", thirdPartyCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", "gnds.sendMobileCode");
        return a(params, this.baseUrl + "/dataManagement/api/inner/sendMobileCode").toString();
    }

    public String K() throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.inner.refreshToken");
        Map<String, Object> content = new HashMap<>(1);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/refreshToken");
        return data;
    }

    public String as(String username) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(1);
        content.put(ConstantEnum.USERNAME_STR, username);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", "gnds.getUserVdn");
        try {
            return b(params, this.baseUrl + "/dataManagement/api/inner/getUserVdn");
        } catch (Exception e) {
            LOG.error("客户端用户请求服务端获取VDN信息失败", e);
            throw e;
        }
    }

    @Cacheable({"getTenantByCode#3600"})
    public String getTenantByCode() throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getTenantByCode");
        Map<String, Object> content = new HashMap<>(1);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = a(params, this.baseUrl + "/dataManagement/api/inner/getTenantByCode").toString();
        return data;
    }

    public String t(String wdid, String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getCircuitDiagramList");
        Map<String, Object> content = new HashMap<>(2);
        content.put("wdid", wdid);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        content.put("status", an(vin));
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getCircuitDiagramList").toString();
    }

    public String u(String svgName, String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getCircuitDiagram");
        Map<String, Object> content = new HashMap<>(1);
        content.put("svgName", svgName);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getCircuitDiagram").toString();
    }

    public String o(String vin, String type, String ref) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getCircuitInfo");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("ref", ref);
        content.put("type", type);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getCircuitInfo").toString();
    }

    public String f(String type, String ref, String vin, String id) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getCircuitExtend");
        Map<String, Object> content = new HashMap<>(5);
        content.put("ref", ref);
        content.put("type", type);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        content.put("id", id);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getCircuitExtend").toString();
    }

    public String p(String gcid, String location, String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getImageTypeList");
        Map<String, Object> content = new HashMap<>(3);
        content.put("gcid", gcid);
        content.put("location", location);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getImageTypeList").toString();
    }

    public String v(String nevisImage, String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getImageTypeList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("nevisImage", nevisImage);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getImage").toString();
    }

    public String q(String type, String wdid, String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getImageTypeList");
        Map<String, Object> content = new HashMap<>(3);
        content.put("type", type);
        content.put("wdid", wdid);
        content.put("vin", vin);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getImageByWdid").toString();
    }

    public String g(String wdid, String vin, String gcid, String location) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getImageTypeList");
        Map<String, Object> content = new HashMap<>(5);
        content.put("wdid", wdid);
        content.put("vin", vin);
        content.put("gcid", gcid);
        content.put("location", location);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getEcuImageTypeList").toString();
    }

    public String w(String vin, String customerFunctionType) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getNetworkFmeaList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("customerFunctionType", customerFunctionType);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/network/list").toString();
    }

    public String x(String vin, String customerFunctionType) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getStandardFmeaList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("customerFunctionType", customerFunctionType);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/standard/list").toString();
    }

    public String y(String vin, String customerFunctionType) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getStandardFmeaList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("customerFunctionType", customerFunctionType);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/fmeaAndCsc/list").toString();
    }

    public String c(List<Long> fmeaIds, List<Long> gcidIds) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getFmeaDtcList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("fmeaIds", fmeaIds);
        content.put("gcidIds", gcidIds);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/dtc/list").toString();
    }

    public String at(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getEcuWdidListByVdnMatch");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/ecuwdid/vdnMatch");
    }

    public String au(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getComponentOtherList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getComponentOtherList");
    }

    public String a(String vin, String gcidName, String location, Map<String, String> ecuMap) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getComActivationList");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        content.put("gcidName", gcidName);
        content.put("location", location);
        content.put("ecuMap", ecuMap);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getComActivationList");
    }

    public String b(String vin, String gcidName, String location, Map<String, String> ecuMap) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getComParamList");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        content.put("gcidName", gcidName);
        content.put("location", location);
        content.put("ecuMap", ecuMap);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getComParamList");
    }

    public String d(String vin, String gcid, String gcidName, String location, String wdid) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getCompontOtherSeqByVdnMatch");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("gcid", gcid);
        content.put("gcidName", gcidName);
        content.put("location", location);
        content.put("wdid", wdid);
        content.put("status", an(vin));
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getCompontOtherSeq/list");
    }

    public String av(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getDebugSeq");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("status", an(vin));
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner//getDebugSeq/list");
    }

    public String aw(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.remoteTask");
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/remoteTask/list");
    }

    public String ax(String id) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.queryDtc");
        Map<String, Object> content = new HashMap<>(2);
        content.put("id", id);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/remoteTask/queryDtc");
    }

    public String r(String taskId, String vin, String ecuAddress) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.queryDtcDetail");
        Map<String, Object> content = new HashMap<>(3);
        content.put("taskId", taskId);
        content.put("vin", vin);
        content.put("ecuAddress", ecuAddress);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/remoteTask/queryDtcDetail");
    }

    public String ay(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.remoteControl");
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteControl");
    }

    public String az(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.remoteTask");
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/remoteTask/list3");
    }

    public String aA(String id) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.queryDtc3");
        Map<String, Object> content = new HashMap<>(2);
        content.put("id", id);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/remoteTask/queryDtc3");
    }

    public String s(String taskId, String vin, String ecuAddress) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.queryDtcDetail3");
        Map<String, Object> content = new HashMap<>(3);
        content.put("taskId", taskId);
        content.put("vin", vin);
        content.put("ecuAddress", ecuAddress);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/remoteTask/queryDtcDetail3");
    }

    public String e(String vin, String vehicle, String first, String second, String model) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.readoutDtc");
        Map<String, Object> content = new HashMap<>(5);
        content.put("vin", vin);
        content.put("vehicle", vehicle);
        content.put("language", HttpUtils.getLanguage());
        content.put("first", first);
        content.put("second", second);
        content.put("model", model);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/readoutDtc");
    }

    public String f(String vin, String vehicle, String first, String second, String model) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.clearDtc");
        Map<String, Object> content = new HashMap<>(5);
        content.put("vin", vin);
        content.put("vehicle", vehicle);
        content.put("language", HttpUtils.getLanguage());
        content.put("first", first);
        content.put("second", second);
        content.put("model", model);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/clearDtc");
    }

    public String a(List<RemoteReadoutParamDto> parmas, String vin, String vehicle, String first, String second, String model) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.readoutParam");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("vehicle", vehicle);
        content.put("params", parmas);
        content.put("first", first);
        content.put("second", second);
        content.put("model", model);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/readoutParam");
    }

    public String a(String vin, String vehicle, String ecuAddress, String ecuName, String diagnosticPartNumber, String first, String second, String model) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.ecuIdentify");
        Map<String, Object> content = new HashMap<>(6);
        content.put("vin", vin);
        content.put("vehicle", vehicle);
        content.put("ecuAddress", ecuAddress);
        content.put("ecuName", ecuName);
        content.put("diagnosticPartNumber", diagnosticPartNumber);
        content.put("first", first);
        content.put("second", second);
        content.put("model", model);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/ecuIdentify");
    }

    public String getDocUrl(String vin, String gcid, String location, String fmea, Integer docType) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(10);
        content.put("vin", vin);
        content.put("gcid", gcid);
        content.put("location", location);
        content.put("fmea", fmea);
        content.put("status", this.seqType);
        content.put("language", HttpUtils.getLanguage());
        content.put("docType", docType);
        params.put("method", "gnds.getDocUrl");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/doc/url");
        return data;
    }

    public String a(String vin, Long fmeaId) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getNetworkGcidlist");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("fmeaId", fmeaId);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/network/gcid/list");
    }

    public String a(String vin, Long fmeaId, String csc) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getStandardGcidlist");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("model", aW(vin));
        content.put("fmeaId", fmeaId);
        content.put("csc", csc);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/standard/gcid/list");
    }

    public String b(String vin, Long fmeaId) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getStandardFmeaCscList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("fmeaId", fmeaId);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/standard/csc/list");
    }

    public String a(int type, Long fmeaId, Long gcId, String csc) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getGcidList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("type", Integer.valueOf(type));
        content.put("fmeaId", fmeaId);
        content.put("gcId", gcId);
        content.put("csc", csc);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/setCscScore");
    }

    public String a(String vin, String gcidName, String location, String fmeaName, Map<String, String> ecuMap) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getFmeaActivationList");
        Map<String, Object> content = new HashMap<>(6);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        content.put("gcidName", gcidName);
        content.put("location", location);
        content.put("fmeaName", fmeaName);
        content.put("ecuMap", ecuMap);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/fmea/activation");
    }

    public String t(String vin, String dtcId, String ecuName) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getDtcFmeaList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("dtcId", dtcId);
        content.put("ecuName", ecuName);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/dtc/fmea/list");
    }

    public String u(String vin, String dtcId, String diagnosticNumber) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getDtcFemaFunctionList");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("dtcId", dtcId);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("diagnosticNumber", diagnosticNumber);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/dtc/fema/function/list");
    }

    public String z(String id, String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.filterCircuitDiagramNode");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        content.put("id", id);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/filterCircuitDiagramNode");
    }

    public String aB(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getVdnImage");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/vdn/getVdnImage");
    }

    public String b(Long fileId, String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getNetworkTopology");
        Map<String, Object> content = new HashMap<>(1);
        content.put("fileId", fileId);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getNetworkTopology").toString();
    }

    public String aC(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getNetworkEcuRelation");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getNetworkEcuRelation");
    }

    @Cacheable({"getDidByDiaPartNumberDro#3600"})
    public String a(String vin, String diagPartNumber, String serviceId, String ecuName, JSONArray ids) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("diagPartNumber", diagPartNumber);
        content.put("serviceId", serviceId);
        content.put("language", HttpUtils.getLanguage());
        content.put("ecuName", ecuName);
        content.put("didList", ids.toJSONString());
        params.put("method", "data.inner.getDidByDiaPartNumberDro");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getDidByDiaPartNumberDro", 2, 30);
    }

    public String aD(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getOneOtaList");
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getOneOtaList");
    }

    public String installation(Long id) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.installation");
        Map<String, Object> content = new HashMap<>(1);
        content.put("id", id);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/installation");
    }

    public String withdrawn(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.withdrawnOneOtaTask");
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/withdrawnOneOtaTask");
    }

    public Map<String, String> j(List<String> errorCodes) throws Exception {
        String jsonStr = this.mapDbUtil.a(MapDBCache.DATA_LANGUAGE);
        List<LanguageVo> list = (List) JSON.parseObject(jsonStr, new TypeReference<List<LanguageVo>>() { // from class: com.geely.gnds.tester.component.Cloud.2
        }, new Feature[0]);
        return (Map) list.stream().filter(vo -> {
            return StrUtil.equalsIgnoreCase(vo.getSystemCode(), "Solution");
        }).filter(vo2 -> {
            return errorCodes.contains(vo2.getLanguageKey());
        }).filter(vo3 -> {
            return StrUtil.equalsIgnoreCase(vo3.getLanguageType(), HttpUtils.getLanguage());
        }).collect(Collectors.toMap((v0) -> {
            return v0.getLanguageKey();
        }, (v0) -> {
            return v0.getLanguageContent();
        }));
    }

    public String aE(String msg) {
        String res = msg;
        try {
            TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(msg);
            if (enumByCodeOrStr != null) {
                String errorCode = enumByCodeOrStr.code();
                List<String> errorCodes = new ArrayList<>();
                errorCodes.add(errorCode);
                Map<String, String> map = j(errorCodes);
                if (map.containsKey(errorCode)) {
                    res = "zh-CN".equals(HttpUtils.getLanguage()) ? msg + ConstantEnum.ERROR_SOLUTION + map.get(errorCode) : msg + ConstantEnum.ERROR_SOLUTION_EN + map.get(errorCode);
                }
            }
        } catch (Exception e) {
            LOG.error("获取解决方案失败", e);
        }
        return res;
    }

    public String aF(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getVehicleTodoList");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("status", this.seqType);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getVehicleTodoList");
    }

    public String a(String vin, Long vehicleTodoId, Integer state) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getVehicleTodoList");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("vehicleTodoId", vehicleTodoId);
        content.put("state", state);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/vehicleTodo/feedback");
    }

    public String v(String pCode, String tCode, String vCode) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "data.inner.getValveBodyData");
        content.put("pCode", pCode);
        content.put("tCode", tCode);
        content.put("vCode", vCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getValveBodyData", 2, 30);
    }

    public String b(String pCode, String tCode, String vCode, String pCodePcm, String tCodePcm, String vCodePcm) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "data.inner.getValveBodyData");
        content.put("pCode", pCode);
        content.put("tCode", tCode);
        content.put("vCode", vCode);
        content.put("pCodePcm", pCodePcm);
        content.put("tCodePcm", tCodePcm);
        content.put("vCodePcm", vCodePcm);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getValveBodyData", 2, 30);
    }

    public String a(String pCode, String tCode, String vCode, String pCodePcm, String tCodePcm, String vCodePcm, String user) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "data.inner.updatePcmBind");
        content.put("pCode", pCode);
        content.put("tCode", tCode);
        content.put("vCode", vCode);
        content.put("pCodePcm", pCodePcm);
        content.put("tCodePcm", tCodePcm);
        content.put("vCodePcm", vCodePcm);
        content.put("user", user);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/updatePcmBind", 2, 30);
    }

    public String w(String pCode, String tCode, String vCode) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "data.inner.getPcmReplaceAssemblyData");
        content.put("pCode", pCode);
        content.put("tCode", tCode);
        content.put("vCode", vCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getPcmReplaceAssemblyData", 2, 30);
    }

    public String getValveBodyAssemblyData(String pCode, String tCode, String vCode) throws Exception {
        LOG.info("更换阀体总成数据入参：{}-{}-{}", new Object[]{pCode, tCode, vCode});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        String res = v(pCode, tCode, vCode);
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        if ("0".equals(code)) {
            String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
            LOG.info("更换阀体总成数据：{}", data);
            return data;
        }
        String msg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00207) + ":" + msg;
        LOG.error(msg2);
        throw new Exception(msg2);
    }

    public String getReplacePcmAssemblyData(String pCode, String tCode, String vCode) throws Exception {
        LOG.info("更换PCM总成入参：{}-{}-{}", new Object[]{pCode, tCode, vCode});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        String res = w(pCode, tCode, vCode);
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        if ("0".equals(code)) {
            String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
            LOG.info("更换PCM总成数据：{}", data);
            return data;
        }
        String msg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00208) + ":" + msg;
        LOG.error(msg2);
        throw new Exception(msg2);
    }

    public String aG(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.vehicleConnectSuccess");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/vehicle/connectSuccess");
    }

    public String getQuickLinksList() throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getQuickLinksList");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/quickLinks/getList");
    }

    public String a(QuickLinksDTO quickLinksDto) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.addQuickLinks");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("title", quickLinksDto.getTitle());
        content.put("url", quickLinksDto.getUrl());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/quickLinks/insert");
    }

    public String b(QuickLinksDTO quickLinksDto) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.updateQuickLinks");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("id", quickLinksDto.getId());
        content.put("title", quickLinksDto.getTitle());
        content.put("url", quickLinksDto.getUrl());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/quickLinks/update");
    }

    public String c(QuickLinksDTO quickLinksDto) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.updateQuicklinksSort");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("id", quickLinksDto.getId());
        content.put("moveFlag", quickLinksDto.getMoveFlag());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/quickLinks/updateSort");
    }

    public String c(Long id) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.deleteQuicklinks");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("id", id);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/quickLinks/delete");
    }

    @Cacheable({"getAllColor#36000"})
    public String getAllColor() throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getAllColor");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/color");
    }

    public String A(String vin, String platform) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getAllEcuConfig");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("vin", vin);
        content.put("platform", platform);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/v2/ecuMessage");
    }

    public ReloadDto a(String seqCode, String vin, boolean nested, boolean nestedCache, String version, String username) throws Exception {
        LOG.info("根据脚本编号获取脚本内容---seqCode：【{}】，vin：【{}】，nested：【{}】，nestedCache：【{}】", new Object[]{seqCode, vin, Boolean.valueOf(nested), Boolean.valueOf(nestedCache)});
        ReloadDto reloadDto = new ReloadDto();
        String language = HttpUtils.getLanguage();
        String seqContentCache = "";
        if (nested) {
            seqContentCache = this.seqManager.getSeqContentCache(seqCode, "", language, "nested");
        } else {
            if (StringUtils.isBlank(version)) {
                version = D(seqCode, vin);
                LOG.info("根据脚本编号获取脚本内容---getVersionBySeqCode：【{}】", version);
            }
            if (StringUtils.isNotBlank(version)) {
                seqContentCache = this.seqManager.getSeqContentCache(seqCode, version, language, "common");
            }
        }
        if (StringUtils.isNotBlank(seqContentCache)) {
            reloadDto.setSeqCode(seqCode);
            reloadDto.setSeqContentString(seqContentCache);
            reloadDto.setVersion(version);
            if (nestedCache) {
                this.seqManager.addSeqVersionCache(seqCode, version);
                this.seqManager.addSeqContentCache(seqCode, "", language, "nested", seqContentCache);
            }
        } else {
            reloadDto = C(seqCode, vin);
            if (reloadDto != null) {
                String dtoVersion = reloadDto.getVersion();
                String seqContentString = reloadDto.getSeqContentString();
                if (StringUtils.isNotBlank(seqContentString)) {
                    this.seqManager.addSeqContentCache(seqCode, dtoVersion, language, "common", seqContentString);
                    if (nestedCache) {
                        this.seqManager.addSeqVersionCache(seqCode, dtoVersion);
                        this.seqManager.addSeqContentCache(seqCode, "", language, "nested", seqContentString);
                    }
                }
            }
        }
        String requestId = reloadDto.getRequestId();
        if (StringUtils.isBlank(requestId)) {
            String requestId2 = h(seqCode, vin, reloadDto.getName(), version);
            reloadDto.setRequestId(requestId2);
        }
        return reloadDto;
    }

    public ReloadDto B(String seqCode, String vin) throws Exception {
        return a(seqCode, vin, false, false, "", "");
    }

    private List<String> getChildSeqCodes(String scriptJson) {
        List<String> seqCodes = new ArrayList<>();
        List<Map> list = (List) JsonPath.parse(scriptJson).read("$..[?(@.seqType=='RunScript' && @.seqInfo.RunScript.Input.NevisLink.GRINumber)]", new Predicate[0]);
        for (Map map : list) {
            JSONObject object = JSONObject.parseObject(JSON.toJSONString(map));
            String seqCode = JSONPath.eval(object, "$.seqInfo.RunScript.Input.NevisLink.GRINumber").toString();
            seqCodes.add(seqCode);
        }
        return seqCodes;
    }

    private void x(String seqContentString, String vin, String username) {
        List<String> childSeqCodes = getChildSeqCodes(seqContentString);
        if (!CollectionUtils.isEmpty(childSeqCodes)) {
            for (String seqCodeChild : childSeqCodes) {
                try {
                    a(seqCodeChild, vin, false, true, "", username);
                } catch (Exception e) {
                    LOG.error("嵌套脚本【{}】缓存失败", seqCodeChild, e);
                }
            }
        }
    }

    @UploadLimit
    public ReloadDto C(String seqCode, String vin) throws Exception {
        LOG.warn("从云端获取{}脚本内容", seqCode);
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("seqCode", seqCode);
        content.put("status", an(vin));
        content.put("vin", vin);
        content.put("model", aW(vin));
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getSeqBycode");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getSeqBycode");
        ReloadDto reloadDto = (ReloadDto) ObjectMapperUtils.jsonStr2Clazz(data, ReloadDto.class);
        return reloadDto;
    }

    @UploadLimit
    public String D(String seqCode, String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("seqCode", seqCode);
        content.put("status", an(vin));
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getVersionBySeqCode");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getVersionBySeqCode");
        LOG.warn("从云端获取{}脚本版本号成功{}", seqCode, data);
        return data;
    }

    @UploadLimit
    public String h(String seqCode, String vin, String name, String version) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("seqCode", seqCode);
        content.put("status", an(vin));
        content.put("name", name);
        content.put("version", version);
        content.put("vin", vin);
        content.put("model", aW(vin));
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getSeqRequestId");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getSeqRequestId");
        LOG.warn("从云端获取{}脚本版本号成功{}", seqCode, data);
        return data;
    }

    public String b(String vin, List<String> pinCodeNames) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getPinCode");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("vin", vin);
        content.put("pinCodeNames", pinCodeNames);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/getTargetSoftwarePincode");
    }

    public String getCscList() throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getCscList");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getCscList", 2, 30);
    }

    public String y(String vin, String engineNumber, String caseNo) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getExistingMaintenanceTask");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("vin", vin);
        content.put("engineNumber", engineNumber);
        content.put("caseNo", caseNo);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getExistingMaintenanceTask", 2, 30);
    }

    public String a(DrCreateMaintenanceTaskVO vo) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.createMaintenanceTask");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("externalUsername", vo.getExternalUsername());
        content.put("serviceStationId", vo.getServiceStationId());
        content.put("serviceStation", vo.getServiceStation());
        content.put("vin", vo.getVin());
        content.put("model", vo.getModel());
        content.put("electronicControlSystem", vo.getElectronicControlSystem());
        content.put("vdsSymptoms", vo.getVdsSymptoms());
        content.put("ecuInfoList", vo.getEcuInfoList());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/createMaintenanceTask", 2, 30);
    }

    public String aH(String taskUid) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getMaintenanceTask");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("taskUid", taskUid);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getMaintenanceTask", 2, 30);
    }

    public String z(String faultUid, String testId, String value) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.faultTest");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("faultUid", faultUid);
        content.put("testId", testId);
        content.put("value", value);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/faultTest", 2, 30);
    }

    public String A(String faultUid, String testId, String value) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.revokeFaultTest");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("faultUid", faultUid);
        content.put("testId", testId);
        content.put("value", value);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/revokeFaultTest", 2, 30);
    }

    public String a(String faultUid, int caId, int rating, String comment) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.executeMaintenancePlanFixed");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("faultUid", faultUid);
        content.put("caId", Integer.valueOf(caId));
        content.put("rating", Integer.valueOf(rating));
        content.put("comment", comment);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/executeMaintenancePlanFixed", 2, 30);
    }

    public String b(String faultUid, int caId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.executeMaintenancePlanNotFixed");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("faultUid", faultUid);
        content.put("caId", Integer.valueOf(caId));
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/executeMaintenancePlanNotFixed", 2, 30);
    }

    public String getMaintenanceTaskCloseOptions() throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.getMaintenanceTaskCloseOptions");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getMaintenanceTaskCloseOptions", 2, 30);
    }

    public String a(String taskUid, Integer closeOption, String solution) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.closeMaintenanceTask");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("taskUid", taskUid);
        content.put("closeOption", closeOption);
        content.put("solution", solution);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/closeMaintenanceTask", 2, 30);
    }

    public String aI(String taskId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.deleteTask");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("taskId", taskId);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/deleteTask", 2, 30);
    }

    public String a(String faultUid, Integer caId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.revokeMaintenancePlan");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        content.put("faultUid", faultUid);
        content.put("caId", caId);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/revokeMaintenancePlan", 2, 30);
    }

    public String submitSupportWorkOrder(SupportWorkOrderDto supportWorkOrderDto) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.supportWorkOrder");
        supportWorkOrderDto.setTenantCode(Long.valueOf(Long.parseLong(this.testerTenantCode)));
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(supportWorkOrderDto));
        return b(params, this.baseUrl + "/dataManagement/api/inner/supportWorkOrder");
    }

    public String E(String fileName, String username) throws Exception {
        Map<String, Object> content = new HashMap<>(4);
        content.put("fileName", fileName);
        Map<String, Object> params = new HashMap<>(8);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", "gnds.getMediaData");
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getMediaData");
    }

    public String aJ(String ossFileName) throws Exception {
        Map<String, Object> content = new HashMap<>(4);
        content.put("ossFileName", ossFileName);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        Map<String, Object> params = new HashMap<>(8);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", "gnds.getSoftwarePackageMediaData");
        return b(params, this.baseUrl + "/dataManagement/api/inner/softwarePackage/mediaData");
    }

    public String aK(String ossPath) throws Exception {
        Map<String, Object> content = new HashMap<>(2);
        content.put("ossPath", ossPath);
        Map<String, Object> params = new HashMap<>(8);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        params.put("method", "gnds.getWorkOrderLogUrl");
        this.webApplicationContext.getServletContext();
        return b(params, this.baseUrl + "/dataManagement/api/inner/getWorkOrderLogUrl");
    }

    public String a(int page, int limit, Long submitUserId, String problemDesc, String consultContent) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.queryByPageForClient");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("submitUserId", submitUserId);
        content.put(ConstantEnum.PAGE, Integer.valueOf(page));
        content.put("problemDesc", problemDesc);
        content.put("consultContent", consultContent);
        content.put(ConstantEnum.LIMIT, Integer.valueOf(limit));
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/queryByPageForClient", 2, 30);
    }

    @Cacheable({"queryNotice#600"})
    public String d(Long submitUserId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.queryNotice");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("submitUserId", submitUserId);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/queryNotice", 2, 30);
    }

    public String getValveBodyAssemblyData(String pCode, String tCode, String vCode, String pCodePcm, String tCodePcm, String vCodePcm) throws Exception {
        LOG.info("更换阀体总成数据入参：{}-{}-{}---{}-{}-{}", new Object[]{pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        String res = b(pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm);
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        if ("0".equals(code)) {
            String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
            LOG.info("更换阀体总成数据：{}", data);
            return data;
        }
        String msg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00207) + ":" + msg;
        LOG.error(msg2);
        throw new Exception(msg2);
    }

    public void b(String pCode, String tCode, String vCode, String pCodePcm, String tCodePcm, String vCodePcm, String user) throws Exception {
        LOG.info("阀体号更新绑定对应的PCM变速器总成号入参：{}-{}-{}---{}-{}-{}", new Object[]{pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        a(pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm, user);
    }

    public String e(List<DsaSequenceEntity> dsaSequenceEntities, String username) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        content.put("dsaSeqs", dsaSequenceEntities);
        params.put("method", "gnds.getDsaSeqList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getDsaSeqList", 2, 30).toString();
        return data;
    }

    public String F(String username, String seqStatus) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        params.put("method", "gnds.querySoftwareDownload");
        content.put("status", seqStatus);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/querySoftwareDownload", 2, 30).toString();
        return data;
    }

    public String aL(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.common.rvdc.remoteLogConfig");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vin", vin);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteLog/config");
    }

    public String aM(String taskId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.queryRemoteLog");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("taskId", taskId);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteLog/detail");
    }

    public String a(String vin, Integer updateIhuLogLevel, Integer oldUpdateIhuLogLevel, Boolean isIhuOperate, String model) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.common.rvdc.remoteLogSetLevel");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vin", vin);
        content.put("updateIHULogLevel", updateIhuLogLevel);
        content.put("oldUpdateIHULogLevel", oldUpdateIhuLogLevel);
        content.put("isIHUOperate", isIhuOperate);
        content.put("model", model);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteLog/setLevel");
    }

    public String a(String vin, Integer updateIhuLogLevel, Integer oldUpdateIhuLogLevel, Boolean isIhuOperate, String model, String startTimeIhu, String endTimeIhu) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.common.rvdc.remoteLogSetLogConfig");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vin", vin);
        content.put("updateIHULogLevel", updateIhuLogLevel);
        content.put("oldUpdateIHULogLevel", oldUpdateIhuLogLevel);
        content.put("isIHUOperate", isIhuOperate);
        content.put("model", model);
        content.put("startTimeIHU", startTimeIhu);
        content.put("endTimeIHU", endTimeIhu);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteLog/setLogConfig");
    }

    public String B(String taskId, String startTimeIhu, String endTimeIhu) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.common.rvdc.remoteLogSetTime");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("taskId", taskId);
        content.put("startTimeIHU", startTimeIhu);
        content.put("endTimeIHU", endTimeIhu);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteLog/setTime");
    }

    public String aN(String taskId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.common.rvdc.remoteLogGetOssPath");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("taskId", taskId);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteLog/getOssPath");
    }

    public String aO(String taskId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.common.rvdc.remoteLogCancel");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("taskId", taskId);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteLog/cancel");
    }

    public String getReplacePcmDataByType(String pCode, String tCode, String vCode, String type) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "data.inner.getReplacePcmDataByType");
        content.put("pCode", pCode);
        content.put("tCode", tCode);
        content.put("vCode", vCode);
        content.put("type", type);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getReplacePcmDataByType", 2, 30);
    }

    public String a(TechnicalReqDTO technicalReqDTO) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getTechnologyList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(technicalReqDTO));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getTechnologyList");
        return data;
    }

    @Cacheable({"getCscList#3600"})
    public String C(String type, String searchData1, String searchData2) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(1);
        content.put("type", type);
        content.put("searchData1", searchData1);
        content.put("searchData2", searchData2);
        params.put("method", "gnds.getCscIndexList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getCscIndexList");
        return data;
    }

    public String c(String dictType, Long parentId) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnd.getSysDictData");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("dictType", dictType);
        content.put("parentId", parentId);
        content.put("languageType", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getSysDictData");
    }

    public String aP(String content) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "data.common.ota.getSwt1Software");
        params.put("bizContent", content);
        return b(params, this.baseUrl + "/dataManagement/api/common/getSwt1Software", 0, 90);
    }

    public String aQ(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(1);
        content.put("vin", vin);
        params.put("method", "data.common.ota.getSwt1publicKey");
        params.put("bizContent", content);
        return b(params, this.baseUrl + "/dataManagement/api/common/getSwt1publicKey");
    }

    @EncryptRequest
    public String getAppSsoConfig() throws Exception {
        RSA rsa = new RSA();
        Object publicKey = Base64Utils.encodeToUrlSafeString(rsa.getPublicKey().getEncoded());
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(1);
        content.put("publicKey", publicKey);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        params.put("method", "gnds.getAppSsoConfig");
        params.put("bizContent", content);
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getAppSsoConfig");
        return rsa.decryptStr(data, KeyType.PrivateKey);
    }

    @EncryptRequest
    public String G(String code, String redirectUri) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(1);
        content.put(AjaxResult.gA, code);
        content.put("redirectUri", redirectUri);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        params.put("method", "gnds.getCepLoginName");
        params.put("bizContent", content);
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getCepLoginNameV2");
        return data;
    }

    @Cacheable({"getRidByDiaPartNumber#300"})
    public String g(String vin, String diagPartNumber, String subfunction, String ecuName, String langage) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("diagPartNumber", diagPartNumber);
        content.put("subfunction", subfunction);
        content.put("language", langage);
        content.put("ecuName", ecuName);
        params.put("method", "gnds.getRidByDiaPartNumber");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getRidByDiaPartNumber");
    }

    public GidpTokenDTO aR(String username) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put(ConstantEnum.USERNAME_STR, username);
        params.put("method", "gnds.getGidpToken");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/getGidpToken");
        GidpTokenDTO token = (GidpTokenDTO) ObjectMapperUtils.jsonStr2Clazz(dataJson, GidpTokenDTO.class);
        return token;
    }

    public PageInfo<GidpDiagnosisDTO> getPageDiagnosisList(String serviceStationCode, Integer orderStatus, Integer pageNum, Integer pageSize) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("dealerCode", serviceStationCode);
        content.put("orderStatus", orderStatus);
        content.put(TableSupport.hh, pageNum);
        content.put(TableSupport.hi, pageSize);
        params.put("method", "gnds.searchDiagnosisByCodeDoingOrAll");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/getGidpDiagnosisList");
        PageInfo<GidpDiagnosisDTO> pageInfo = (PageInfo) ObjectMapperUtils.jsonStr2Clazz(dataJson, PageInfo.class);
        return pageInfo;
    }

    public PageInfo<GidpDiagnosisDTO> getPageDiagnosisListByVin(String serviceStationCode, String vin, Integer pageNum, Integer pageSize, String diagnosisMode) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("dealerCode", serviceStationCode);
        content.put("vin", vin);
        content.put(TableSupport.hh, pageNum);
        content.put(TableSupport.hi, pageSize);
        content.put("diagnosisMode", diagnosisMode);
        params.put("method", "gnds.searchDiagnosisByCodeAndVinDoing");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/getGidpDiagnosisListByVin");
        PageInfo<GidpDiagnosisDTO> pageInfo = (PageInfo) ObjectMapperUtils.jsonStr2Clazz(dataJson, PageInfo.class);
        return pageInfo;
    }

    public GidpDiagnosisDTO saveGidpDiagnosis(GidpDiagnosisOrderDetailOrderDTO detailOrder) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.saveGidpDiagnosis");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(detailOrder));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/saveGidpDiagnosis");
        GidpDiagnosisDTO dto = (GidpDiagnosisDTO) ObjectMapperUtils.jsonStr2Clazz(dataJson, GidpDiagnosisDTO.class);
        return dto;
    }

    public GidpDiagnosisDTO updateGidpDiagnosis(GidpUpdateDiagnosisOrderDTO updateOrder) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.saveGidpDiagnosis");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(updateOrder));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/updateGidpDiagnosis");
        GidpDiagnosisDTO dto = (GidpDiagnosisDTO) ObjectMapperUtils.jsonStr2Clazz(dataJson, GidpDiagnosisDTO.class);
        return dto;
    }

    public List<DrCscNode> getGidpIssueFunctions() throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        Map<String, Object> content = new HashMap<>(2);
        params.put("method", "gnds.getGidpIssueFunctions");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/getGidpIssueFunctions");
        return ObjectMapperUtils.jsonStr2List(dataJson, DrCscNode.class);
    }

    public PageInfo<GidpActivityDTO> getGidpActivity(String diagnosticNumber, String vin, Integer pageNum, Integer pageSize) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(4);
        params.put("method", "gnds.getGidpActivity");
        content.put("diagnosticNumber", diagnosticNumber);
        content.put("vin", vin);
        content.put(TableSupport.hh, pageNum);
        content.put(TableSupport.hi, pageSize);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/getGidpActivity");
        PageInfo<GidpActivityDTO> dto = (PageInfo) ObjectMapperUtils.jsonStr2Clazz(dataJson, PageInfo.class);
        return dto;
    }

    public Map responseActivityAction(String diagnosticNumber, String activityNumber, String executionStatus, String feedBack) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(4);
        params.put("method", "gnds.responseActivityAction");
        content.put("diagnosticNumber", diagnosticNumber);
        content.put("activityNumber", activityNumber);
        content.put("executionStatus", executionStatus);
        content.put("feedBack", feedBack);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/responseActivityAction");
        return (Map) ObjectMapperUtils.jsonStr2Clazz(dataJson, Map.class);
    }

    public PageInfo<GidpTjDTO> searchTjDone(String diagnosticNumber, String vin, String vehicleType, String vehicleStructureWeek, String vdnList, Integer pageNum, Integer pageSize) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(8);
        params.put("method", "gnds.searchTjDone");
        content.put("diagnosticNumber", diagnosticNumber);
        content.put("vin", vin);
        content.put("vehicleType", vehicleType);
        content.put("vehicleStructureWeek", vehicleStructureWeek);
        content.put("vdnList", vdnList);
        content.put(TableSupport.hh, pageNum);
        content.put(TableSupport.hi, pageSize);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/searchTjDone");
        PageInfo<GidpTjDTO> dto = (PageInfo) ObjectMapperUtils.jsonStr2Clazz(dataJson, PageInfo.class);
        return dto;
    }

    public Map responseTjAction(String diagnosticNumber, String tjNumber, String pushExecutionStatus, String pushFeedBack) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(4);
        params.put("method", "gnds.responseTjAction");
        content.put("diagnosticNumber", diagnosticNumber);
        content.put("tjNumber", tjNumber);
        content.put("pushExecutionStatus", pushExecutionStatus);
        content.put("pushFeedBack", pushFeedBack);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/responseTjAction");
        return (Map) ObjectMapperUtils.jsonStr2Clazz(dataJson, Map.class);
    }

    public PageInfo<GidpTrDTO> getTrList(String diagnosticNumber, String vin, Integer pageNum, Integer pageSize) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("diagnosticNumber", diagnosticNumber);
        content.put("vin", vin);
        content.put(TableSupport.hh, pageNum);
        content.put(TableSupport.hi, pageSize);
        params.put("method", "gnds.getTrList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/getTrList");
        PageInfo<GidpTrDTO> pageInfo = (PageInfo) ObjectMapperUtils.jsonStr2Clazz(dataJson, PageInfo.class);
        return pageInfo;
    }

    public GidpTrDTO getCreateTrUrl() throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        params.put("method", "gnds.getCreateTrUrl");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/common/getCreateTrUrl");
        GidpTrDTO create = (GidpTrDTO) ObjectMapperUtils.jsonStr2Clazz(dataJson, GidpTrDTO.class);
        return create;
    }

    public String a(String username, SysRequestDTO sysRequestDTO) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.uploadXmlImmediate");
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        sysRequestDTO.setTenantCode(this.testerTenantCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(sysRequestDTO));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/uploadXmlImmediate").toString();
        return data;
    }

    public String a(DroDTO dto, String username) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.createDro");
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
        Map<String, Object> content = new HashMap<>(2);
        content.put("droJson", JSON.toJSONString(dto));
        content.put("vin", dto.getVin());
        VehicleDto vehicle = this.manager.getVehicle(dto.getVin());
        String model = "";
        if (vehicle != null) {
            Map broadcastMap = vehicle.getBroadcastMap();
            if (broadcastMap.containsKey("vehicle")) {
                model = broadcastMap.get("vehicle").toString();
            }
        }
        content.put("module", model);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/createDro").toString();
        return data;
    }

    public String a(DtcQueryListVo vo) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getDtcMapByDtcIds");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(vo));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/getDtcMapByDtcIdsShield", 1, 16);
        return data;
    }

    public String aS(String region) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.verifyCert");
        Map<String, Object> content = new HashMap<>(2);
        content.put("cert", SingletonManager.getCertContent());
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("tenantName", this.tenantName);
        content.put("region", region);
        TesterConfigDto config = this.eC.getConfig();
        String testerCode = config.getTesterCode();
        content.put("testerCode", testerCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String data = b(params, this.baseUrl + "/dataManagement/api/inner/verifyCert");
        return data;
    }

    public void aT(String username) throws Exception {
        try {
            Map<String, Object> params = new HashMap<>(2);
            params.put("method", "gnds.requestCert");
            Map<String, Object> content = new HashMap<>(2);
            String publicKey = CloudApiRsaUtils.getPublicKey();
            ServletContext servletContext = this.webApplicationContext.getServletContext();
            params.put(Constants.TOKEN, servletContext.getAttribute(username.toLowerCase() + "-token"));
            content.put("publicKey", publicKey);
            TesterConfigDto config = this.eC.getConfig();
            String testerCode = config.getTesterCode();
            content.put("testerCode", testerCode);
            content.put("tenantName", this.tenantName);
            content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
            content.put("machineCode", MachineUtil.getMachineCode());
            params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
            String data = b(params, this.baseUrl + "/dataManagement/api/inner/requestCert");
            CertResultDTO certResultDTO = (CertResultDTO) ObjectMapperUtils.jsonStr2Clazz(data, CertResultDTO.class);
            SingletonManager.setCert(certResultDTO);
        } catch (Exception e) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00277) + ":" + e.getMessage());
        }
    }

    public String b(List<RemoteReadoutParamDto> parmas, String vin, String vehicle, String first, String second, String model) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.readoutParam3");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("vehicle", vehicle);
        content.put("params", parmas);
        content.put("first", first);
        content.put("second", second);
        content.put("model", model);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/readoutParam3");
    }

    public SimpleResultDTO remoteUpgrade(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("method", "gnds.remoteUpgrade");
        Map<String, Object> content = new HashMap<>();
        content.put("vin", vin);
        content.put("model", aW(vin));
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/inner/remoteUpgrade");
        SimpleResultDTO simpleResultDTO = (SimpleResultDTO) ObjectMapperUtils.jsonStr2Clazz(dataJson, SimpleResultDTO.class);
        return simpleResultDTO;
    }

    public String aU(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("method", "gnds.getSystemCircuitDiagramList");
        Map<String, Object> content = new HashMap<>();
        content.put("vin", vin);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getSystemCircuitDiagramList").toString();
    }

    public String D(String vin, String ecuName, String taskId) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("method", "data.common.rvdc.getVehicleUploadProgress");
        Map<String, Object> content = new HashMap<>();
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vin", vin);
        content.put("ecuName", ecuName);
        content.put("taskId", taskId);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/remoteLog/getVehicleUploadProgress").toString();
    }

    @UploadLimit
    public String i(String ecuName, String vin, String seqType, String type) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(3);
        content.put("ecuName", ecuName);
        content.put("status", seqType);
        content.put("type", type);
        content.put("vin", vin);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getRemoteEcuSeqList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String seqList = b(params, this.baseUrl + "/dataManagement/api/inner/getRemoteEcuSeqList");
        return seqList;
    }

    public String E(String platform, String vin, String category) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(4);
        content.put("status", an(vin));
        content.put("vin", vin);
        content.put("platform", platform);
        content.put("category", category);
        content.put("language", HttpUtils.getLanguage());
        params.put("method", "gnds.getDsaIntegrationTestSeq");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getDsaIntegrationTestSeq");
    }

    public String aV(String taskId) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.querySeqDetail");
        Map<String, Object> content = new HashMap<>(3);
        content.put("taskId", taskId);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/remoteTask/querySeqDetail");
    }

    public String a(String vin, String vehicle, String first, String second, String model, List<ReloadDto> seqList, Integer remoteTaskType) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.readoutDtc");
        Map<String, Object> content = new HashMap<>(5);
        content.put("vin", vin);
        content.put("vehicle", vehicle);
        content.put("language", HttpUtils.getLanguage());
        content.put("first", first);
        content.put("second", second);
        if (remoteTaskType != null) {
            content.put("remoteTaskType", remoteTaskType);
        }
        if (!CollectionUtils.isEmpty(seqList)) {
            content.put("seqList", JSON.toJSONString(seqList));
        }
        content.put("model", model);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/readoutDtc3");
    }

    public String aW(String vin) throws Exception {
        if (StringUtils.isNotBlank(vin)) {
            try {
                Object broadcastQuery = SingletonManager.getInstance().getGlobal(GlobalVariableEnum.lX, vin);
                String bc = "";
                if (broadcastQuery != null) {
                    bc = broadcastQuery.toString();
                } else {
                    List<String> vins = new ArrayList<>();
                    vins.add(vin);
                    String data = c(vins, getUsername());
                    ObjectMapper mapper = new ObjectMapper();
                    List<Object> broadcasts = Arrays.asList((Object[]) mapper.readValue(data, Object[].class));
                    if (!CollectionUtils.isEmpty(broadcasts)) {
                        bc = ObjectMapperUtils.obj2JsonStr(broadcasts.get(0));
                    }
                }
                if (!StringUtils.isBlank(bc)) {
                    Map map = this.io.settleVehicleInfo(bc);
                    return map.getOrDefault("vehicle", "").toString();
                }
                return "";
            } catch (Exception e) {
                LOG.error("获取车辆getModel信息失败", e);
                return "";
            }
        }
        return "";
    }

    public String c(String vin, List<String> wdids) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("method", "gnds.getSystemCircuitDiagramOverviewList");
        Map<String, Object> content = new HashMap<>();
        content.put("vin", vin);
        content.put("wdIds", wdids);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getSystemCircuitDiagramOverviewList").toString();
    }

    @EncryptRequest
    public String aX(String redirectUri) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.getOAuthLoginUrl");
        ImmutableMap<String, String> param = ImmutableMap.of("redirectUri", redirectUri, "provider", AppConfig.getProperty("tester.provider"));
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(param));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getOAuthLoginUrl");
    }

    public String getUiLanguageData() throws Exception {
        HashMap<String, Object> bizContentMap = Maps.newHashMap();
        Map<String, Object> params = new HashMap<>(3);
        params.put("method", "gnds.resourceList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(bizContentMap));
        return b(params, this.baseUrl + "/dataManagement/api/inner/resourceList");
    }

    @EncryptRequest
    public String getDataLanguageData() throws Exception {
        HashMap<String, Object> bizContentMap = Maps.newHashMap();
        bizContentMap.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        Map<String, Object> params = new HashMap<>(3);
        params.put("method", "gnds.dataLanguage");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(bizContentMap));
        return b(params, this.baseUrl + "/dataManagement/api/inner/dataLanguage");
    }

    public PageInfo<DroRecordDTO> c(Map<String, Object> bizContentMap) throws Exception {
        Map<String, Object> params = new HashMap<>(3);
        params.put("method", "gnds.droHistoryPage");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(bizContentMap));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/inner/droHistoryPage");
        PageInfo<DroRecordDTO> dtos = (PageInfo) ObjectMapperUtils.jsonStr2Clazz(dataJson, PageInfo.class);
        return dtos;
    }

    public String aY(String path) throws Exception {
        Map<String, Object> params = new HashMap<>(3);
        params.put("method", "gnds.getDroPath");
        HashMap<String, Object> bizContentMap = Maps.newHashMap();
        bizContentMap.put("name", path);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(bizContentMap));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getDroPath");
    }

    public String getWorkOrderMenuList(String workerOrderType) throws Exception {
        Map<String, Object> params = new HashMap<>(3);
        params.put("method", "gnds.getWorkOrderMenuList");
        Maps.newHashMap();
        WorkOrderAuthorityQueryDTO workOrderAuthorityQueryDTO = new WorkOrderAuthorityQueryDTO();
        workOrderAuthorityQueryDTO.setLanguage(HttpUtils.getLanguage());
        workOrderAuthorityQueryDTO.setTenantCode(this.testerTenantCode);
        workOrderAuthorityQueryDTO.setWorkerOrderType(workerOrderType);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(workOrderAuthorityQueryDTO));
        return b(params, this.baseUrl + "/dataManagement/api/inner/getWorkOrderMenuList");
    }

    public String L() throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.queryNoticeMessage");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/notice/messages", 2, 30);
    }

    public String e(Long id) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.queryNoticeMessageFileUrl");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("id", id);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/notice/messages/file/url", 2, 30);
    }

    private String getUsername() {
        String username;
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        if (!ObjectUtils.isEmpty(loginUser)) {
            username = loginUser.getUsername();
        } else {
            username = ThreadLocalUtils.CURRENT_USER_NAME.get();
        }
        if (StringUtils.isEmpty(username)) {
            username = TesterLoginUtils.getLoginUserName();
        }
        return username;
    }

    public String k(List<String> vins) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("method", "gnds.getDsaPermission");
        Map<String, Object> content = new HashMap<>();
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vins", vins);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/dsa/getDsaPermission", 2, 30);
    }

    public String a(String vin, SysUser user) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("method", "gnds.recoreFirstConnectTime");
        Map<String, Object> content = new HashMap<>();
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vins", Arrays.asList(vin));
        content.put("firstConnectionTime", new Date());
        content.put("serviceStationAccount", user.getUserName());
        content.put("serviceStationCode", user.getServiceStationCode());
        content.put("serviceStationName", user.getServiceStationName());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/dsa/recoreFirstConnectTime", 2, 30);
    }

    public String H(String vin, String carModel) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.vehicleRealtimeCheck");
        Map<String, Object> content = new HashMap<>(4);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vin", vin);
        content.put("carModel", carModel);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/vehicle/realtime/check", 2, 30);
    }

    public String I(String vin, String carModel) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.vehicleHealthInspection");
        Map<String, Object> content = new HashMap<>(5);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vin", vin);
        content.put("carModel", carModel);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/vehicle/health/inspection", 2, 30);
    }

    public String a(String vin, String carModel, List<String> maintainItems) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.vehicleMaintainReset");
        Map<String, Object> content = new HashMap<>(5);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("vin", vin);
        content.put("carModel", carModel);
        content.put("maintainItems", maintainItems);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/vehicle/maintain/reset", 2, 30);
    }

    public String vehiclePortrait(String vin, String remark) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.vehiclePortrait");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("remark", remark);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/dataManagement/api/inner/vehiclePortrait");
        OrtResultDTO ortResultDTO = (OrtResultDTO) ObjectMapperUtils.jsonStr2Clazz(dataJson, OrtResultDTO.class);
        return ortResultDTO.getSuccess();
    }

    public byte[] J(String attachedFilePath, String tenantcode) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.attachmentDownload");
        Map<String, Object> content = new HashMap<>(4);
        params.put("attachedFilePath", attachedFilePath);
        content.put(ConstantEnum.TENANTCODE_STR, tenantcode);
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        String dataJson = b(params, this.baseUrl + "/sys/api/inner/supportWorkOrder/attachment/download");
        return Base64Utils.decode(dataJson.getBytes());
    }

    public ServerListMenuDTO aZ(String dictType) throws Exception {
        String url = String.format(this.baseUrl + "/public/tenantConfig/%s/%s", AppConfig.getTesterCode(), dictType);
        String json = HttpUtils.sendNewGet(url, new HashMap());
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(json);
        Integer code = jsonObject.getInt(AjaxResult.gA);
        String data = jsonObject.getStr(AjaxResult.gC);
        if (code.intValue() == 0) {
            return (ServerListMenuDTO) ObjectMapperUtils.jsonStr2Clazz(data, ServerListMenuDTO.class);
        }
        return null;
    }

    @UploadLimit
    public String F(String vin, String seqType, String type) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("status", seqType);
        content.put("language", HttpUtils.getLanguage());
        content.put("scriptType", type);
        params.put("method", "gnds.getFixSeqList");
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/querySeqList").toString();
    }

    public String ba(String vin) throws Exception {
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.madsVehicleConnectCheck");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/mads/connectVehicleCheck");
    }

    public String G(String vin, String reason, String model) throws Exception {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.createMadsOrder");
        Map<String, Object> content = new HashMap<>(4);
        content.put("vin", vin);
        content.put("reason", reason);
        content.put("model", model);
        content.put("submitUserName", loginUser.getUser().getUserName());
        content.put("submitServiceStationCode", loginUser.getUser().getServiceStationCode());
        content.put("submitServiceStationName", loginUser.getUser().getServiceStationName());
        content.put("creator", loginUser.getUser().getUserId());
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/mads/order/create");
    }

    public void bb(String vin) throws Exception {
        if (this.vLinkEnabled.booleanValue()) {
            String username = getUsername();
            this.testerThread.getPool().execute(() -> {
                try {
                    ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                    Map<String, Object> params = new HashMap<>(8);
                    params.put("method", "gnds.sendVLink");
                    Map<String, Object> content = new HashMap<>(2);
                    content.put("vin", vin);
                    params.put("bizContent", content);
                    b(params, this.baseUrl + "/dataManagement/api/inner/vehicle/sendVLink");
                } catch (Exception e) {
                    LOG.error("推送车辆数据异常", e);
                }
            });
        }
    }

    public String M() throws Exception {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.madsQueryOrderBalance");
        Map<String, Object> content = new HashMap<>(4);
        content.put("submitUserName", loginUser.getUsername());
        content.put(ConstantEnum.TENANTCODE_STR, this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/mads/user/order/balance");
    }

    @EncryptRequest
    public String N() throws Exception {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        Map<String, Object> params = new HashMap<>(8);
        params.put("method", "gnds.madsVehicleConnectCheck");
        Map<String, Object> content = new HashMap<>(4);
        content.put("userId", loginUser.getUser().getUserId());
        content.put("tenantId", this.testerTenantCode);
        content.put("language", HttpUtils.getLanguage());
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/inner/ai/diagnosis/login");
    }

    public String b(String vin, boolean icon) throws Exception {
        Map<String, Object> params = new HashMap<>(2);
        params.put("method", "gnds.hideApp");
        Map<String, Object> content = new HashMap<>(2);
        content.put("vin", vin);
        content.put("icon", Boolean.valueOf(icon));
        params.put("bizContent", ObjectMapperUtils.obj2JsonStr(content));
        return b(params, this.baseUrl + "/dataManagement/api/common/hideApp");
    }
}
