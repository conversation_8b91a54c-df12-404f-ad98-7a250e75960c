package com.geely.gnds.tester.component;

import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.dto.LoginDto;
import com.geely.gnds.tester.util.AesUtils;
import com.geely.gnds.tester.util.MachineCodeUtil;
import com.profesorfalken.jpowershell.PowerShell;
import com.profesorfalken.jpowershell.PowerShellResponse;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import oshi.SystemInfo;
import oshi.hardware.Baseboard;
import oshi.hardware.Firmware;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HWDiskStore;
import oshi.hardware.HWPartition;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.hardware.NetworkIF;
import oshi.software.os.OperatingSystem;
import oshi.util.FormatUtil;

@Component
/* loaded from: TesterDevice.class */
public class TesterDevice {
    private static final Logger LOG = LoggerFactory.getLogger(TesterDevice.class);
    private static final String iC = "\r\n主板：\r\n{0}\r\nBIOS：{1}\r\nCPU处理器ID：{2}\r\n首张磁盘序列号：{3}\r\n操作系统版本：{4}\r\n处理器：{5}\r\nRAM：{6}\r\n可用RAM：{7}\r\n主板UUID：{8}\r\nMachine GUID：{9}\r\n产品ID：{10}";
    private static final String iD = "{0} {1} {2} {3}";
    private static final String iE = "{0}-{1}-{2}-{3}-{4}";
    private String processorId = "";
    private String iF = "";
    private String gh = "";
    private String iG = "";
    private String iH = "";
    private String iI = "";
    private Bios iJ = new Bios();
    private MotherBoard iK = new MotherBoard();

    @Autowired
    private TaskExecutor executor;

    public String getOs() {
        return this.gh;
    }

    public String getProcessorId() {
        return this.processorId;
    }

    public String getDisk0Sn() {
        return this.iF;
    }

    public String getBiosSn() {
        return this.iJ.iN;
    }

    public String getMotherBoardSn() {
        return this.iK.iN;
    }

    @PostConstruct
    public void init() {
        this.executor.execute(this::P);
    }

    private void P() {
        SystemInfo si = new SystemInfo();
        HardwareAbstractionLayer hw = si.getHardware();
        List<NetworkIF> networkIfs = hw.getNetworkIFs();
        DoipAddressManager.getInstance().setNetList(networkIfs);
        this.processorId = hw.getProcessor().getProcessorIdentifier().getProcessorID();
        this.iG = hw.getProcessor().toString();
        GlobalMemory memory = hw.getMemory();
        float totalRamFloat = (((memory.getTotal() * 100) / 1024) / 1024) / 1024;
        DecimalFormat df = new DecimalFormat("##0.0");
        this.iH = df.format(totalRamFloat / 100.0f);
        float avaRamFloat = (((memory.getAvailable() * 100) / 1024) / 1024) / 1024;
        this.iI = df.format(avaRamFloat / 100.0f);
        List<HWDiskStore> disks = hw.getDiskStores();
        if (disks.size() > 0) {
            this.iF = disks.get(0).getSerial();
        }
        Firmware firmware = hw.getComputerSystem().getFirmware();
        this.iJ.iL = firmware.getManufacturer();
        this.iJ.iM = firmware.getReleaseDate();
        this.iJ.iN = hw.getComputerSystem().getSerialNumber();
        this.iJ.version = firmware.getVersion();
        Baseboard baseboard = hw.getComputerSystem().getBaseboard();
        this.iK.iL = baseboard.getManufacturer();
        this.iK.model = baseboard.getModel();
        this.iK.iN = baseboard.getSerialNumber();
        try {
            OperatingSystem osObj = si.getOperatingSystem();
            OperatingSystem.OSVersionInfo version = osObj.getVersionInfo();
            this.gh = MessageFormat.format(iD, osObj.getFamily(), version.getCodeName(), version.getVersion(), version.getBuildNumber());
        } catch (Exception e) {
            LOG.error("os信息获取异常,e={}", e);
        }
        String format = MessageFormat.format(iE, this.processorId, this.iF, this.iJ.iN, this.iK.iN);
        LOG.info(format);
        AesUtils.setKeyByBios(format);
        LoginDto.setBios(this.iJ.toString());
        LoginDto.setDiskSn(this.iF);
        LoginDto.setMotherBoard(this.iK.toString());
        LoginDto.setProcessorId(this.processorId);
        LoginDto.setBiosUuid(MachineCodeUtil.getWindowsBiosUuid());
        LoginDto.setMachineGuid(MachineCodeUtil.getMachineGuid());
        LoginDto.setProductId(MachineCodeUtil.getProductId());
        try {
            LoginDto.setCpuInfo(this.iG);
            LoginDto.setMemoryInfo(memory.toString());
            LoginDto.setDiskInfo(l(disks));
            LoginDto.setNetworkInfo(m(networkIfs));
            LOG.info(toString());
        } catch (Exception e2) {
            LOG.error("获取硬件信息失败！e={}", e2);
        }
    }

    public String toString() {
        return MessageFormat.format(iC, this.iK, this.iJ, this.processorId, this.iF, this.gh, this.iG, this.iH, this.iI, LoginDto.getBiosUuid(), LoginDto.getMachineGuid(), LoginDto.getProductId());
    }

    /* loaded from: TesterDevice$Bios.class */
    public class Bios {
        private static final String iC = "\t制造商：{0}\r\n\t发布日期：{1}\r\n\t序列号：{2}\r\n\t版本：{3}";
        private String iL = null;
        private String version = null;
        private String iM = null;
        private String iN = null;

        public Bios() {
        }

        public String getManufacturer() {
            return this.iL;
        }

        public String getVersion() {
            return this.version;
        }

        public String getReleaseDate() {
            return this.iM;
        }

        public String getSn() {
            return this.iN;
        }

        public String toString() {
            return MessageFormat.format(iC, this.iL, this.iM, this.iN, this.version);
        }
    }

    /* loaded from: TesterDevice$MotherBoard.class */
    public class MotherBoard {
        private static final String iC = "\t制造商：{0}\r\n\tModel：{1}\r\n\t序列号：{2}";
        private String iL = null;
        private String model = null;
        private String iN = null;

        public MotherBoard() {
        }

        public String getManufacturer() {
            return this.iL;
        }

        public String getModel() {
            return this.model;
        }

        public String getSn() {
            return this.iN;
        }

        public String toString() {
            return MessageFormat.format(iC, this.iL, this.model, this.iN);
        }
    }

    private static String l(List<HWDiskStore> diskStores) {
        StringBuilder sb = new StringBuilder();
        sb.append("诊断仪安装位置:").append("\r\n");
        sb.append("\t" + AppConfig.getAppHomeDir()).append("\r\n");
        for (HWDiskStore disk : diskStores) {
            sb.append(disk.getName()).append(":( model:").append(disk.getModel()).append("- S/N").append(disk.getSerial()).append(")").append("读写花费时间，单位每毫秒:").append(disk.getTransferTime());
            List<HWPartition> partitions = disk.getPartitions();
            if (partitions != null) {
                for (HWPartition part : partitions) {
                    sb.append("\r\n").append("\t").append(part.getMountPoint());
                }
            }
        }
        sb.append("\r\n");
        try {
            PowerShellResponse powerShellResponse = PowerShell.executeSingleCommand(" Get-PhysicalDisk |ft SerialNumber,FriendlyName,MediaType");
            String commandOutput = powerShellResponse.getCommandOutput();
            sb.append(commandOutput).append("\r\n");
        } catch (Exception e) {
            LOG.error("获取磁盘硬件信息失败,e={}", e);
        }
        return sb.toString();
    }

    private static String m(List<NetworkIF> networkIfs) {
        StringBuilder sb = new StringBuilder();
        sb.append("Network interfaces:").append("\r\n");
        for (NetworkIF net : networkIfs) {
            sb.append(" Name: ").append(net.getName()).append("(").append(net.getDisplayName()).append(")").append("\r\n").append("\t MAC Address: ").append(net.getMacaddr()).append("\r\n").append("\t MTU: ").append(net.getMTU()).append(",Speed: ").append(net.getSpeed()).append("bps").append("\r\n").append("\t IPv4: ").append(Arrays.toString(net.getIPv4addr())).append("\r\n").append("\t IPv6: ").append(Arrays.toString(net.getIPv6addr())).append("\r\n");
            boolean hasData = net.getBytesRecv() > 0 || net.getBytesSent() > 0 || net.getPacketsRecv() > 0 || net.getPacketsSent() > 0;
            sb.append("\t Traffic: received ").append(hasData ? net.getPacketsRecv() + " packets" : "?").append("/").append(hasData ? FormatUtil.formatBytes(net.getBytesRecv()) : "?").append(hasData ? " (" + net.getInErrors() + " err)" : "").append("; transmitted ").append(hasData ? net.getPacketsSent() + " packets" : "?").append("/").append(hasData ? FormatUtil.formatBytes(net.getBytesSent()) : "?").append(hasData ? " (" + net.getOutErrors() + " err)" : "").append("\r\n");
        }
        return sb.toString();
    }
}
