package com.geely.gnds.tester.component;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.seq.Logger;
import java.io.File;
import java.util.Date;
import java.util.regex.Pattern;

/* loaded from: Gc.class */
public class Gc {
    private static final Gc iy = new Gc();
    private Pattern iz = null;
    private final long iA = 300000;
    File iB = new File(AppConfig.getAppDataDir(), "applog\\gc_logs");

    private Gc() {
    }

    public static Gc getInstance() {
        return iy;
    }

    public void init(String gcId) {
        this.iz = Pattern.compile("gc-" + gcId + "[.]log[.][0-9]+[.]current");
    }

    public int O() {
        return this.iB.listFiles().length;
    }

    public File getGcDir() {
        return this.iB;
    }

    public void e(File targetDir) {
        if (!this.iB.exists()) {
            return;
        }
        for (File file : this.iB.listFiles()) {
            String fileName = file.getName();
            if (!fileName.endsWith("current") || (file.lastModified() <= System.currentTimeMillis() - 300000 && (this.iz == null || !this.iz.matcher(fileName).matches()))) {
                String newName = fileName + ConstantEnum.POINT + DateUtils.parseDateToStr(DateUtils.YYYYMMDDHHMMSS, new Date());
                File target = new File(targetDir, newName);
                if (!file.renameTo(target)) {
                    Logger.error("GC文件" + fileName + "移动失败！");
                }
            }
        }
    }
}
