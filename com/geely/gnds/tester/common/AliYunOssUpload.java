package com.geely.gnds.tester.common;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.CompleteMultipartUploadRequest;
import com.aliyun.oss.model.InitiateMultipartUploadRequest;
import com.aliyun.oss.model.InitiateMultipartUploadResult;
import com.aliyun.oss.model.PartETag;
import com.aliyun.oss.model.UploadPartRequest;
import com.aliyun.oss.model.UploadPartResult;
import com.geely.gnds.tester.upload.FileUploader;
import com.geely.gnds.tester.upload.provider.IAliyunOSSUpload;
import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
/* loaded from: AliYunOssUpload.class */
public class AliYunOssUpload implements IAliyunOSSUpload {
    private static final Logger log = LoggerFactory.getLogger(AliYunOssUpload.class);
    private final AliyunOssConfigKit ossConfigKit;

    public AliYunOssUpload(AliyunOssConfigKit ossConfigKit) {
        this.ossConfigKit = ossConfigKit;
    }

    public void upload(String localPath, String remotePath) throws Exception {
        uploadFile(localPath, remotePath);
    }

    private void uploadFile(String localpath, String key) throws Exception {
        OSS ossClient = this.ossConfigKit.getOssClient();
        if (ossClient == null) {
            log.info("...未登录，或者无法获取到OSS配置信息，则取消上传");
            return;
        }
        try {
            InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(this.ossConfigKit.getBucketName(), key);
            InitiateMultipartUploadResult upresult = ossClient.initiateMultipartUpload(request);
            String uploadId = upresult.getUploadId();
            List<PartETag> partEtags = new ArrayList<>();
            File sampleFile = new File(localpath);
            long fileLength = sampleFile.length();
            int partCount = (int) (fileLength / 10485760);
            if (fileLength % 10485760 != 0) {
                partCount++;
            }
            for (int i = 0; i < partCount; i++) {
                long startPos = i * 10485760;
                long curPartSize = i + 1 == partCount ? fileLength - startPos : 10485760L;
                InputStream instream = Files.newInputStream(sampleFile.toPath(), new OpenOption[0]);
                Throwable th = null;
                try {
                    try {
                        instream.skip(startPos);
                        UploadPartRequest uploadPartRequest = new UploadPartRequest();
                        uploadPartRequest.setBucketName(this.ossConfigKit.getBucketName());
                        uploadPartRequest.setKey(key);
                        uploadPartRequest.setUploadId(uploadId);
                        uploadPartRequest.setInputStream(instream);
                        uploadPartRequest.setPartSize(curPartSize);
                        uploadPartRequest.setPartNumber(i + 1);
                        uploadPartRequest.setUseChunkEncoding(true);
                        uploadPartRequest.setTrafficLimit(FileUploader.getLimit() * 8);
                        UploadPartResult uploadPartResult = ossClient.uploadPart(uploadPartRequest);
                        partEtags.add(uploadPartResult.getPartETag());
                        if (instream != null) {
                            if (0 != 0) {
                                try {
                                    instream.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                instream.close();
                            }
                        }
                    } finally {
                    }
                } finally {
                }
            }
            CompleteMultipartUploadRequest completeMultipartUploadRequest = new CompleteMultipartUploadRequest(this.ossConfigKit.getBucketName(), key, uploadId, partEtags);
            ossClient.completeMultipartUpload(completeMultipartUploadRequest);
        } catch (Exception oe) {
            log.error("uploadFileToAliyun出现异常,e={}", oe);
            throw oe;
        }
    }
}
