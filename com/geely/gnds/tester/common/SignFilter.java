package com.geely.gnds.tester.common;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.tester.enums.I18nKeyEnums;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StreamUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

@Component
@Order(50)
/* loaded from: SignFilter.class */
public class SignFilter extends OncePerRequestFilter {
    private final HandlerExceptionResolver handlerExceptionResolver;
    private static final String ERROR_PARAM_MESSAGE = "Invalid parameter signature";
    private static final String ERROR_MESSAGE = "Parameter signature fail";
    private static final String REQ_PREFIX = "gnds:req:";
    private static TimedCache<String, String> cache;
    private static final Logger LOG = LoggerFactory.getLogger(SignFilter.class);
    private static List<String> whitesUri = new ArrayList();
    private static final List<String> METHODS = Arrays.asList("put", "delete", "post");
    private static final List<String> HEADERS = Arrays.asList("sign", "requestId", "timestamp");

    static {
        LOG.info(I18nKeyEnums.LOADING_RULE.getKey());
        cache = CacheUtil.newTimedCache(5000L);
        whitesUri.add("/api/v1/socket/**");
        whitesUri.add("/api/v1/doc/**");
        whitesUri.add("/api/v1/circuitDiagram/**");
        whitesUri.add("/api/v1/networkTopology/**");
        whitesUri.add("/api/v1/testerService/**");
        whitesUri.add("/api/v1/login/toOauth2");
        whitesUri.add("/actuator/**");
        whitesUri.add("/favicon.ico");
        whitesUri.add("/ds/**");
    }

    public SignFilter(HandlerExceptionResolver handlerExceptionResolver) {
        this.handlerExceptionResolver = handlerExceptionResolver;
    }

    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String uri = request.getRequestURI();
        boolean flag = whitesUri.stream().anyMatch(pattern -> {
            return isMatch(pattern, uri);
        });
        return flag;
    }

    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        try {
            validSign(request);
            chain.doFilter(request, response);
        } catch (Exception e) {
            this.handlerExceptionResolver.resolveException(request, response, (Object) null, e);
        }
    }

    private void validSign(HttpServletRequest request) throws IOException, IllegalArgumentException {
        if (METHODS.contains(request.getMethod().toLowerCase())) {
            if (StrUtil.isNotBlank(request.getContentType()) && request.getContentType().startsWith(ContentType.MULTIPART.toString())) {
                return;
            }
            Long dateTimestamp = getDateTimestamp((String) Optional.ofNullable(request.getHeader("timestamp")).orElseThrow(() -> {
                return new IllegalArgumentException(ERROR_PARAM_MESSAGE);
            }));
            String requestId = getRequestId((String) Optional.ofNullable(request.getHeader("requestId")).orElseThrow(() -> {
                return new IllegalArgumentException(ERROR_PARAM_MESSAGE);
            }));
            String sign = (String) Optional.ofNullable(request.getHeader("sign")).orElseThrow(() -> {
                return new IllegalArgumentException(ERROR_PARAM_MESSAGE);
            });
            String queryParam = getParamMap(HttpUtil.decodeParamMap(request.getQueryString(), Charset.forName(request.getCharacterEncoding())));
            String bodyParam = getBodyJson(request);
            String method = request.getMethod().toUpperCase();
            checkSign(method, sign, dateTimestamp, requestId, bodyParam, queryParam);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static boolean isMatch(String pattern, String url) {
        AntPathMatcher matcher = new AntPathMatcher();
        return matcher.match(pattern, url);
    }

    private boolean isJsonRequest(HttpServletRequest request) {
        String header = request.getContentType();
        return StringUtils.startsWithIgnoreCase(header, "application/json");
    }

    private String getBodyJson(HttpServletRequest request) throws IOException {
        byte[] bodyBytes = StreamUtils.copyToByteArray(request.getInputStream());
        return new String(bodyBytes, request.getCharacterEncoding());
    }

    private static void checkSign(String method, String sign, Long dateTimestamp, String requestId, String bodyJson, String queryParam) {
        String str = method + StrUtil.emptyToDefault(bodyJson, StrFormatter.EMPTY_JSON) + requestId + dateTimestamp;
        String tempSign = DigestUtil.sha256Hex(str);
        if (!tempSign.equals(sign)) {
            LOG.error("参数签名验证失败，地址：{}", ActionContext.getRequest().getRequestURI());
            throw new IllegalArgumentException(ERROR_MESSAGE);
        }
    }

    private static String getParamMap(Map<String, String> queryParams) {
        TreeMap<String, String> treeMap = MapUtil.newTreeMap(queryParams, Comparator.naturalOrder());
        return JSONUtil.toJsonStr(treeMap);
    }

    private static Long getDateTimestamp(String timestamp) throws NumberFormatException {
        long timestampVal = Long.parseLong(timestamp);
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - timestampVal > 60000) {
            throw new IllegalArgumentException(ERROR_PARAM_MESSAGE);
        }
        return Long.valueOf(timestampVal);
    }

    private String getRequestId(String requestId) {
        if (StrUtil.isNotBlank(requestId) && StrUtil.isNotBlank((CharSequence) cache.get(REQ_PREFIX + requestId))) {
            throw new IllegalArgumentException("Duplicate submit is not allowed");
        }
        if (StrUtil.isNotBlank(requestId)) {
            cache.put(REQ_PREFIX + requestId, "1", 5000L);
        }
        return requestId;
    }

    public static void main(String[] args) {
        Long dateTimestamp = Long.valueOf(System.currentTimeMillis());
        Map<String, String> query = new HashMap<>();
        query.put("shortName", "gri");
        query.put("_t", "1713170112360");
        TreeMap<String, String> treeMap = MapUtil.newTreeMap(query, Comparator.naturalOrder());
        String queryParam = JSONUtil.toJsonStr(treeMap);
        System.out.println("=========queryParam:" + queryParam);
        System.out.println("=========bodyJson:{\"mm\":33,\"bb\":\"44\"}");
        String str = "POST" + queryParam + StrUtil.nullToEmpty("{\"mm\":33,\"bb\":\"44\"}") + "528aa4eae7d68c110e7c90e59d24abab" + dateTimestamp;
        String tempSign = DigestUtil.sha256Hex(str);
        System.out.println("=========sign:" + tempSign);
        checkSign("POST", tempSign, dateTimestamp, "528aa4eae7d68c110e7c90e59d24abab", "{\"mm\":33,\"bb\":\"44\"}", queryParam);
    }
}
