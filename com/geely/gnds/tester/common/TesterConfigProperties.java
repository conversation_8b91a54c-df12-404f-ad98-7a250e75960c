package com.geely.gnds.tester.common;

import java.io.Serializable;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "tester")
@EnableConfigurationProperties
@Configuration
/* loaded from: TesterConfigProperties.class */
public class TesterConfigProperties implements Serializable {

    @Value("${tester.login-page.enabled:#{null}}")
    private Boolean showLoginPage;
    private Ui ui = new Ui();

    public Ui getUi() {
        return this.ui;
    }

    public void setUi(Ui ui) {
        this.ui = ui;
    }

    /* loaded from: TesterConfigProperties$Ui.class */
    public class Ui {
        private boolean redirectSsoLink;
        private boolean showSsoLink;
        private String contentType;

        public Ui() {
        }

        public boolean isRedirectSsoLink() {
            return ((Boolean) Optional.ofNullable(TesterConfigProperties.this.showLoginPage).map(s -> {
                return Boolean.valueOf(!s.booleanValue());
            }).orElse(Boolean.valueOf(this.redirectSsoLink))).booleanValue();
        }

        public void setRedirectSsoLink(boolean redirectSsoLink) {
            this.redirectSsoLink = redirectSsoLink;
        }

        public boolean isShowSsoLink() {
            return ((Boolean) Optional.ofNullable(TesterConfigProperties.this.showLoginPage).orElse(Boolean.valueOf(this.showSsoLink))).booleanValue();
        }

        public void setShowSsoLink(boolean showSsoLink) {
            this.showSsoLink = showSsoLink;
        }

        public String getContentType() {
            return this.contentType;
        }

        public void setContentType(String contentType) {
            this.contentType = contentType;
        }
    }
}
