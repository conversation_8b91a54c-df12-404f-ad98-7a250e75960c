package com.geely.gnds.tester.common;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
/* loaded from: GndsHealthIndicator.class */
public class GndsHealthIndicator implements HealthIndicator {

    @Value("${tester.tenantName}")
    private String tenantName;

    @Value("${spring.profiles.active}")
    private String environment;

    public Health health() {
        return Health.up().withDetail("serviceName", StringUtils.capitalize(this.tenantName + "-" + this.environment).toUpperCase()).build();
    }
}
