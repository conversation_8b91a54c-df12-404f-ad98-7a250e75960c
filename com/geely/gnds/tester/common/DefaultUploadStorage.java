package com.geely.gnds.tester.common;

import com.geely.gnds.tester.upload.FileTypeEnum;
import com.geely.gnds.tester.upload.FileUpload;
import com.geely.gnds.tester.upload.ProviderStrategy;
import com.geely.gnds.tester.upload.StorageProvider;
import com.geely.gnds.tester.upload.provider.UploadProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
/* loaded from: DefaultUploadStorage.class */
public class DefaultUploadStorage implements StorageProvider {

    @Autowired
    private ProviderStrategy providerStrategy;

    public FileUpload getStrategy(FileTypeEnum fileType) {
        return this.providerStrategy.getStrategy(UploadProvider.ALIYUN);
    }
}
