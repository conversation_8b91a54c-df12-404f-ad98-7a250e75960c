package com.geely.gnds.tester.common;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.awt.AWTException;
import java.awt.MouseInfo;
import java.awt.PointerInfo;
import java.awt.Robot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

@Component
/* loaded from: SleepUtils.class */
public class SleepUtils {

    @Autowired
    private TaskExecutor executor;
    private static volatile boolean runing = false;
    private static final Logger LOG = LoggerFactory.getLogger(SleepUtils.class);

    public void doNotSleep() {
        LOG.info("鼠标抖动开始doNotSleep");
        if (!runing) {
            runing = true;
            this.executor.execute(() -> {
                try {
                    LOG.info("鼠标抖动开始");
                    while (runing) {
                        shake();
                        Thread.sleep(10000L);
                    }
                } catch (InterruptedException e) {
                    LOG.error("鼠标抖动异常,e={}", e);
                } catch (AWTException e2) {
                    LOG.error("鼠标抖动异常,e={}", e2);
                }
            });
        }
    }

    public void cancel() {
        LOG.info("鼠标抖动结束");
        runing = false;
    }

    private void shake() throws AWTException {
        int mx;
        int my;
        Robot robot = new Robot();
        PointerInfo pointerInfo = MouseInfo.getPointerInfo();
        int mx2 = pointerInfo.getLocation().x;
        int my2 = pointerInfo.getLocation().y;
        if (mx2 <= 0) {
            mx = mx2 + 1;
        } else {
            mx = mx2 - 1;
        }
        if (my2 <= 0) {
            my = my2 + 1;
        } else {
            my = my2 - 1;
        }
        robot.mouseMove(mx, my);
        robot.delay(100);
        robot.mouseMove(mx2, my2);
        LOG.debug(Thread.currentThread().getName() + "-监听鼠标:(" + mx + ConstantEnum.COMMA + my + ")");
    }
}
