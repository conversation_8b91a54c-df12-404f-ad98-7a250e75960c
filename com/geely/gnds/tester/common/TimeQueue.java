package com.geely.gnds.tester.common;

import java.util.Date;
import java.util.LinkedList;

/* loaded from: TimeQueue.class */
public class TimeQueue<E> {
    private long timeout;
    private static long CLEAN_TIME = 1000;
    private LinkedList<Element<E>> queue = new LinkedList<>();
    private volatile boolean isAlive = true;

    public TimeQueue(long timeout) {
        if (timeout <= 0) {
            throw new IllegalArgumentException("timeout must be positive");
        }
        this.timeout = timeout;
        Thread cleaner = new Thread(new Cleaner(this));
        cleaner.setDaemon(true);
        cleaner.start();
    }

    public synchronized void add(E e) {
        this.queue.add(new Element<>(e, new Date()));
    }

    public synchronized E poll() {
        if (this.queue.isEmpty()) {
            return null;
        }
        return this.queue.remove().getValue();
    }

    public synchronized boolean isEmpty() {
        if (this.queue.isEmpty()) {
            return true;
        }
        return false;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public synchronized void cleanup() {
        long now = System.currentTimeMillis();
        while (!this.queue.isEmpty() && now - this.queue.getFirst().getTime().getTime() >= this.timeout) {
            this.queue.removeFirst();
        }
    }

    /* loaded from: TimeQueue$Element.class */
    private static class Element<E> {
        private E value;
        private Date time;

        public Element(E value, Date time) {
            this.value = value;
            this.time = time;
        }

        public E getValue() {
            return this.value;
        }

        public Date getTime() {
            return this.time;
        }
    }

    /* loaded from: TimeQueue$Cleaner.class */
    private static class Cleaner implements Runnable {
        private TimeQueue<?> queue;

        public Cleaner(TimeQueue<?> queue) {
            this.queue = queue;
        }

        @Override // java.lang.Runnable
        public void run() throws InterruptedException {
            while (true) {
                try {
                    Thread.sleep(TimeQueue.CLEAN_TIME);
                } catch (InterruptedException e) {
                }
                this.queue.cleanup();
            }
        }
    }
}
