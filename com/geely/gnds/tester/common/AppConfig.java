package com.geely.gnds.tester.common;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.setting.dialect.Props;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.util.RuntimeUtils;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Optional;
import java.util.Properties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.io.FileSystemResource;

@Configuration
/* loaded from: AppConfig.class */
public class AppConfig {
    private static final Logger log = LoggerFactory.getLogger(AppConfig.class);
    private Environment environment;
    public static final String COMPANYNAME = "Geely";

    @Bean
    public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
        PropertySourcesPlaceholderConfigurer properties = new PropertySourcesPlaceholderConfigurer();
        properties.setLocation(new FileSystemResource(RuntimeUtils.getHomePath().concat(File.separator + "config" + File.separator + "application.properties")));
        return properties;
    }

    public static void reloadProperties() throws IOException {
        String configFile = RuntimeUtils.getHomePath().concat(File.separator + "config" + File.separator + "application.properties");
        try {
            Properties properties = new Properties();
            properties.load(Files.newInputStream(Paths.get(configFile, new String[0]), new OpenOption[0]));
            MapPropertySource newPropertySource = new MapPropertySource("manualConfig", properties);
            ConfigurableEnvironment environment = (ConfigurableEnvironment) SpringUtil.getBean(ConfigurableEnvironment.class);
            PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
            configurer.setLocation(new FileSystemResource(configFile));
            environment.getPropertySources().addFirst(newPropertySource);
            log.info("重新载入配置文件");
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        log.info("当前OTA车辆区域：{}", getActiveRegion());
    }

    @Autowired
    public void setEnvironment(Environment env) {
        this.environment = env;
    }

    public static void setProperty(String key, String value) {
        String configFile = RuntimeUtils.getHomePath().concat(File.separator + "config" + File.separator + "application.properties");
        Props props = new Props(configFile);
        props.setProperty(key, value);
        props.store(configFile);
    }

    public static String getProperty(String key) {
        return ((Environment) SpringUtil.getBean(Environment.class)).getProperty(key);
    }

    public static File getAppDataDir() {
        try {
            return Files.createDirectories(Paths.get(getAppHomeDir().getAbsolutePath(), AjaxResult.gC, getCloudId()), new FileAttribute[0]).toFile();
        } catch (IOException e) {
            log.error("无法创建目录：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public static String getIniProperty(String key) {
        String configFile = getAppHomeDir().getAbsolutePath() + File.separator + "tester.ini";
        if (!FileUtil.isFile(configFile)) {
            return "";
        }
        Props props = new Props(configFile);
        return props.getStr(key);
    }

    public static File getAppDataDir(String cloudId) {
        try {
            return Files.createDirectories(Paths.get(getAppHomeDir().getAbsolutePath(), AjaxResult.gC, cloudId), new FileAttribute[0]).toFile();
        } catch (IOException e) {
            log.error("无法创建目录：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public static void writeIni(String key, String value) {
        String configFile = getAppHomeDir().getAbsolutePath() + File.separator + "tester.ini";
        Props props = new Props(configFile);
        props.setProperty(key, value);
        props.store(configFile);
    }

    public static File getAppHomeDir() {
        return new File(StrUtil.blankToDefault(System.getenv("GNDS_HOME"), System.getProperty("user.dir")));
    }

    public static String getCloudId() {
        String configFile = FileUtil.file(getAppHomeDir(), "tester.ini").getAbsolutePath();
        Props props = new Props(configFile);
        return props.getStr("tester.cloud.id", "");
    }

    public static String getSelfUrl() {
        return getProperty("tester.selfUrl");
    }

    public static String getActiveRegion() {
        return (String) Optional.ofNullable(getProperty("tester.vehicleDataRegion")).orElse("cn");
    }

    public static void setTesterCode(String testerCode) {
        setProperty("tester.testerCode", testerCode);
    }

    public static String getAppName() {
        return getProperty("tester.tenantName");
    }

    public static String tenantCode() {
        return getProperty("tester.tenantCode");
    }

    public static String getTenantName() {
        return getAppName();
    }

    public static File getCacheFile() {
        return new File(getCacheDir() + File.separator + "cache.db");
    }

    public static File getCacheDir() {
        try {
            return Files.createDirectories(Paths.get(RuntimeUtils.getHomePath(), "cache"), new FileAttribute[0]).toFile();
        } catch (IOException e) {
            log.error("无法创建目录：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public static File getDroCacheFile(String fileName) {
        return new File(FileUtil.mkdir(getCacheDir().getAbsolutePath() + File.separator + "dro").getAbsolutePath() + File.separator + fileName);
    }

    public static String getTesterCode() {
        return getProperty("tester.tenantCode");
    }

    private static Path getAppDir() {
        try {
            String roamingDir = System.getenv("APPDATA");
            if (roamingDir != null) {
                Path appDataDir = Paths.get(roamingDir, new String[0]).resolve("Geely/" + getAppName() + "/" + getTesterCode());
                return Files.createDirectories(appDataDir, new FileAttribute[0]);
            }
            Path userHome = Paths.get(System.getProperty("user.home"), new String[0]);
            Path appDataDir2 = userHome.resolve("AppData/Roaming/Geely/" + getAppName() + "/" + getTesterCode());
            return Files.createDirectories(appDataDir2, new FileAttribute[0]);
        } catch (IOException e) {
            log.error("无法创建目录：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
