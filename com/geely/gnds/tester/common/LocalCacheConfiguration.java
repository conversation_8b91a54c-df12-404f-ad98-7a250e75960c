package com.geely.gnds.tester.common;

import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

@Configuration
@EnableCaching
/* loaded from: LocalCacheConfiguration.class */
public class LocalCacheConfiguration implements BeanFactoryAware, SmartInitializingSingleton {
    private DefaultListableBeanFactory beanFactory;

    @Autowired
    private CaffeineCacheManager caffeineCacheManager;
    private static final int DEFAULT_TIMEOUT = 60;

    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = (DefaultListableBeanFactory) beanFactory;
    }

    public void afterSingletonsInstantiated() {
        Map<String, Object> beansWithAnnotation = this.beanFactory.getBeansWithAnnotation(Component.class);
        if (MapUtils.isNotEmpty(beansWithAnnotation)) {
            for (Object cacheValue : beansWithAnnotation.values()) {
                ReflectionUtils.doWithMethods(cacheValue.getClass(), method -> {
                    ReflectionUtils.makeAccessible(method);
                    boolean cacheAnnotationPresent = method.isAnnotationPresent(Cacheable.class);
                    if (cacheAnnotationPresent) {
                        Cacheable cacheable = method.getAnnotation(Cacheable.class);
                        for (String name : cacheable.cacheNames()) {
                            Caffeine caffeine = Caffeine.newBuilder().recordStats().initialCapacity(128).maximumSize(10240L);
                            if (StrUtil.contains(name, "#")) {
                                String[] nameExpire = name.split("#");
                                caffeine.expireAfterWrite(Long.parseLong(nameExpire[1]), TimeUnit.SECONDS);
                            } else {
                                caffeine.expireAfterWrite(60L, TimeUnit.SECONDS);
                            }
                            this.caffeineCacheManager.registerCustomCache(name, caffeine.build());
                        }
                        for (String name2 : cacheable.value()) {
                            Caffeine caffeine2 = Caffeine.newBuilder().recordStats().initialCapacity(128).maximumSize(10240L);
                            if (StrUtil.contains(name2, "#")) {
                                String[] nameExpire2 = name2.split("#");
                                caffeine2.expireAfterWrite(Long.parseLong(nameExpire2[1]), TimeUnit.SECONDS);
                            } else {
                                caffeine2.expireAfterWrite(60L, TimeUnit.SECONDS);
                            }
                            this.caffeineCacheManager.registerCustomCache(name2, caffeine2.build());
                        }
                    }
                });
            }
        }
    }
}
