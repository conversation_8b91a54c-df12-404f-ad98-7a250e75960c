package com.geely.gnds.tester.common;

import com.geely.gnds.tester.enums.TestLogTypeEnum;
import java.io.Serializable;

/* loaded from: UploadCloudBean.class */
public class UploadCloudBean implements Serializable {
    private String vin;
    private String username;
    private String tenantName;
    private String logPath;
    private String newPath;
    private String testerId;
    private TestLogTypeEnum testLogTypeEnum;
    private String id;

    public UploadCloudBean() {
    }

    public UploadCloudBean(String vin, String username, String tenantName, String logPath, String newPath, String testerId, TestLogTypeEnum testLogTypeEnum) {
        this.vin = vin;
        this.username = username;
        this.tenantName = tenantName;
        this.logPath = logPath;
        this.newPath = newPath;
        this.testerId = testerId;
        this.testLogTypeEnum = testLogTypeEnum;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getTenantName() {
        return this.tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getLogPath() {
        return this.logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public String getNewPath() {
        return this.newPath;
    }

    public void setNewPath(String newPath) {
        this.newPath = newPath;
    }

    public String getTesterId() {
        return this.testerId;
    }

    public void setTesterId(String testerId) {
        this.testerId = testerId;
    }

    public TestLogTypeEnum getTestLogTypeEnum() {
        return this.testLogTypeEnum;
    }

    public void setTestLogTypeEnum(TestLogTypeEnum testLogTypeEnum) {
        this.testLogTypeEnum = testLogTypeEnum;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
