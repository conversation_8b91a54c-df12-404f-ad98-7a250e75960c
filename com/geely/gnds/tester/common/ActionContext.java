package com.geely.gnds.tester.common;

import java.util.Optional;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/* loaded from: ActionContext.class */
public class ActionContext {
    private static final ThreadLocal<ActionContext> CONTEXT_THREAD_LOCAL = new ThreadLocal<>();
    private HttpServletRequest request;

    public static void init(HttpServletRequest request) {
        ActionContext dataContext = new ActionContext();
        dataContext.request = request;
        CONTEXT_THREAD_LOCAL.set(dataContext);
    }

    public static void destroy() {
        CONTEXT_THREAD_LOCAL.remove();
    }

    public static ActionContext getInstance() {
        return CONTEXT_THREAD_LOCAL.get();
    }

    public static HttpServletRequest getRequest() {
        return (HttpServletRequest) Optional.ofNullable(getInstance()).map(request -> {
            return request.request;
        }).orElse(null);
    }

    public static HttpSession getSession() {
        return getRequest().getSession();
    }

    public static String getServerUrl() {
        if (getRequest().getServerPort() == 80 || getRequest().getServerPort() == 443) {
            return getRequest().getScheme() + "://" + getRequest().getServerName() + getRequest().getContextPath();
        }
        return getRequest().getScheme() + "://" + getRequest().getServerName() + ":" + getRequest().getServerPort() + getRequest().getContextPath();
    }
}
