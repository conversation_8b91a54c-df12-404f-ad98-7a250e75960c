package com.geely.gnds.tester.common;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.geely.gnds.dsa.service.impl.DsaLogServiceImpl;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.TesterConfigService;
import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.TreeSet;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Component
/* loaded from: LogFileReslove.class */
public class LogFileReslove {

    @Autowired
    private TesterConfigService configService;

    @Autowired
    private OssUtils ossUtils;

    @Value("${tester.tenantName}")
    private String tenantName;
    private String testerId;
    private static final Logger log = LoggerFactory.getLogger(LogFileReslove.class);
    private static final String RUN_LOG_DIR_PATH = StringUtils.cleanPath(AppConfig.getAppDataDir().getAbsolutePath() + File.separator + "applog" + File.separator);

    private List<UploadCloudBean> getSysLogUploadBeanList() {
        if (null == this.configService.getConfig()) {
            return new ArrayList();
        }
        this.testerId = this.configService.getConfig().getTesterCode();
        List<UploadCloudBean> list = new ArrayList<>();
        if (StringUtils.hasText(this.testerId)) {
            List<File> files = FileUtil.loopFiles(new File(RUN_LOG_DIR_PATH.concat("backup")));
            List<File> uploadFiles = (List) files.stream().filter((v0) -> {
                return v0.isFile();
            }).filter(c -> {
                return !StrUtil.startWith(c.getName(), DateUtil.today(), true);
            }).filter(x -> {
                return DsaLogServiceImpl.eF.equalsIgnoreCase(FileUtil.extName(x));
            }).collect(Collectors.toList());
            for (File file : uploadFiles) {
                String day = (String) Optional.ofNullable(ReUtil.get("(\\d{4}-\\d{2}-\\d{2})(_sys.+)", file.getName(), 1)).orElse(DateUtil.today());
                String ossPath = "log/" + this.tenantName + "/" + day + "/system/" + this.testerId + "/" + LogTypeEnum.SYS_PATH.getDirName() + "/" + file.getName();
                UploadCloudBean bean = new UploadCloudBean("", "system", this.tenantName, file.getAbsolutePath(), ossPath, this.testerId, TestLogTypeEnum.run);
                list.add(bean);
            }
        }
        return list;
    }

    private List<UploadCloudBean> getBizLogUploadBeanList(LogTypeEnum type) {
        SingletonManager instance = SingletonManager.getInstance();
        if (null == this.configService.getConfig()) {
            return new ArrayList();
        }
        this.testerId = this.configService.getConfig().getTesterCode();
        List<File> files = FileUtil.loopFiles(new File(RUN_LOG_DIR_PATH.concat(type.getDirName())));
        List<UploadCloudBean> list = new ArrayList<>();
        if (StringUtils.hasText(this.testerId)) {
            List<File> uploadFiles = (List) files.stream().filter((v0) -> {
                return v0.isFile();
            }).filter(x -> {
                return FileUtil.extName(x).equalsIgnoreCase(type.getExtName());
            }).collect(Collectors.toList());
            for (File file : uploadFiles) {
                String fileName = file.getName();
                if (!instance.isLogUse(fileName) && ReUtil.isMatch("(.+)-([A-Za-z0-9]{17})-(\\d{17})(\\.[a-z]{1,})", fileName)) {
                    String username = ReUtil.get("(.+)-([A-Za-z0-9]{17})-(\\d{17})(\\.[a-z]{1,})", file.getName(), 1);
                    String vin = ReUtil.get("(.+)-([A-Za-z0-9]{17})-(\\d{17})(\\.[a-z]{1,})", file.getName(), 2);
                    if (!ReUtil.get("(.+)-([A-Za-z0-9]{17})-(\\d{17})(\\.[a-z]{1,})", fileName, 3).startsWith(DateUtil.date().toString("yyyy-MM-dd"))) {
                        String day = ReUtil.get("(.+)-([A-Za-z0-9]{17})-(\\d{17})(\\.[a-z]{1,})", file.getName(), 3);
                        DateTime date = DateUtil.parse(day, DateUtils.YYYYMMDDHHMMSSSSS);
                        String day2 = date.toString("yyyy-MM-dd");
                        String ossPath = "log/" + this.tenantName + "/" + day2 + "/" + username + "/" + this.testerId + "/" + type.getDirName() + "/" + file.getName();
                        boolean needUpload = OssUtils.needUpload(file.getAbsolutePath());
                        if (needUpload) {
                            UploadCloudBean bean = new UploadCloudBean(vin, username, this.tenantName, file.getAbsolutePath(), ossPath, this.testerId, type.convert());
                            list.add(bean);
                        }
                    }
                }
            }
        }
        return list;
    }

    public void reslove() {
        List<UploadCloudBean> beanList = getSysLogUploadBeanList();
        beanList.addAll(getBizLogUploadBeanList(LogTypeEnum.PCAP_PATH));
        beanList.addAll(getBizLogUploadBeanList(LogTypeEnum.TXT_PATH));
        beanList.addAll(getBizLogUploadBeanList(LogTypeEnum.XML_PATH));
        log.info("LogFileReslove before,logpath={}", beanList.stream().map((v0) -> {
            return v0.getLogPath();
        }).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(beanList)) {
            beanList = (List) beanList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                return new TreeSet(Comparator.comparing((v0) -> {
                    return v0.getLogPath();
                }));
            }), (v1) -> {
                return new ArrayList(v1);
            }));
        }
        log.info("LogFileReslove after,logpath={}", beanList.stream().map((v0) -> {
            return v0.getLogPath();
        }).collect(Collectors.toList()));
        for (UploadCloudBean bean : beanList) {
            try {
                this.ossUtils.uploadLogToCloud(bean);
            } catch (Exception e) {
                log.error("LogFileReslove->reslove方法异常,e={}", e);
            }
        }
    }
}
