package com.geely.gnds.tester.common;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

@Aspect
@Component
/* loaded from: UploadLimitAspect.class */
public class UploadLimitAspect implements InitializingBean {
    int count = 30;

    @Pointcut("@annotation(com.geely.gnds.tester.common.UploadLimit)")
    public void uploadLimitPointCut() {
    }

    @Around("uploadLimitPointCut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        this.count = 30;
        OssUtils.setUserLimit();
        return joinPoint.proceed();
    }

    public void afterPropertiesSet() throws Exception {
        ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
        Runnable task = () -> {
            this.count--;
            if (this.count < 0) {
                OssUtils.setUnLimit();
            }
        };
        executorService.scheduleAtFixedRate(task, 0L, 10L, TimeUnit.SECONDS);
    }
}
