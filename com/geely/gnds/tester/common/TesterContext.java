package com.geely.gnds.tester.common;

import cn.hutool.extra.spring.SpringUtil;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.TesterConfigDto;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/* loaded from: TesterContext.class */
public class TesterContext {
    private static Map<String, FdTcpClient> mapClient = new ConcurrentHashMap();

    public static void init(String vin, FdTcpClient fdTcpClient) {
        Optional.ofNullable(vin).ifPresent(v -> {
            mapClient.put(v, fdTcpClient);
        });
    }

    public static FdTcpClient getFdTcpClient(String vin) {
        return mapClient.get(vin);
    }

    public static Map<String, FdTcpClient> getMapClient() {
        return mapClient;
    }

    public static TesterConfigDto getTesterConfigDto() {
        return ((TesterConfigDao) SpringUtil.getBean(TesterConfigDao.class)).getConfig();
    }

    public static String getLanguage() {
        return Locale.getDefault().toLanguageTag();
    }
}
