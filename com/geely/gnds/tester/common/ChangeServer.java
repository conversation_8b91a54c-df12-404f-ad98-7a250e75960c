package com.geely.gnds.tester.common;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import com.geely.gnds.tester.common.DownloadUtil;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.ServerChildMenu;
import com.geely.gnds.tester.dto.ServerListMenuDTO;
import com.geely.gnds.tester.dto.SysDictDTO;
import com.geely.gnds.tester.dto.UpgradeDownLoadDTO;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.socket.GndsMsgCodeEnum;
import com.geely.gnds.tester.socket.GndsWebSocket;
import com.geely.gnds.tester.util.TesterUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
/* loaded from: ChangeServer.class */
public class ChangeServer {
    private static final Logger LOG = LoggerFactory.getLogger(ChangeServer.class);

    @Autowired
    private Cloud cloud;

    public List<ServerChildMenu> getServerList(String dictType) throws Exception {
        List<ServerChildMenu> list = new ArrayList<>();
        List<String> selectDict = new ArrayList<>();
        ServerListMenuDTO servers = this.cloud.aZ(dictType);
        if (null != servers) {
            switch (dictType) {
                case "cloud_server":
                    selectDict.addAll(StrUtil.split(servers.getCloudServers(), ConstantEnum.COMMA));
                    break;
                case "vehicle_data_region":
                    selectDict.addAll(StrUtil.split(servers.getVehicleDataRegion(), ConstantEnum.COMMA));
                    break;
                default:
                    return list;
            }
            List<SysDictDTO> dtoList = servers.getSysDict();
            Stream<R> map = dtoList.stream().filter(dto -> {
                return selectDict.contains(dto.getDictValue());
            }).map(dto2 -> {
                ServerChildMenu serverChildMenu = new ServerChildMenu();
                serverChildMenu.setId(dto2.getDictValue());
                serverChildMenu.setLabel(StrUtil.replace(dto2.getDictLabel(), "support_work_problem_type.", ""));
                serverChildMenu.setType("item");
                serverChildMenu.setValue(dto2.getDictValue());
                serverChildMenu.setAction("");
                return serverChildMenu;
            });
            list.getClass();
            map.forEach((v1) -> {
                r1.add(v1);
            });
        }
        return list;
    }

    public void downLoadFile(String cloudId, String env, String tenant) {
        String resourceUrl = StrUtil.indexedFormat("https://ota-public-all.geely.com/gnds/setup/server/{0}/{1}/{2}/{3}", new Object[]{cloudId, env, tenant, "latest"});
        LOG.info("下载文件地址：{}", resourceUrl);
        Map<String, String> downloadUrls = new LinkedHashMap<>();
        downloadUrls.put(resourceUrl + "/application.properties", FileUtil.file(AppConfig.getAppDataDir(cloudId), new String[]{"config", "application.properties"}).getAbsolutePath());
        downloadUrls.put(resourceUrl + "/dq.db", FileUtil.file(AppConfig.getAppDataDir(cloudId), "dq.db").getAbsolutePath());
        downloadUrls.put(resourceUrl + "/version.txt", FileUtil.file(AppConfig.getAppDataDir(cloudId), "version.txt").getAbsolutePath());
        downloadUrls.put(resourceUrl + "/tester.jar", FileUtil.file(AppConfig.getAppDataDir(cloudId), "tester.jar").getAbsolutePath());
        downloadUrls.put(resourceUrl + "/tester-boot.jar", FileUtil.file(AppConfig.getAppDataDir(cloudId), "tester-boot.jar").getAbsolutePath());
        downloadUrls.put(resourceUrl + "/lib.zip", FileUtil.file(AppConfig.getAppDataDir(cloudId), "lib.zip").getAbsolutePath());
        if (StrUtil.equalsIgnoreCase("true", AppConfig.getIniProperty("tester.cloud.latest"))) {
            FileUtil.del(FileUtil.file(AppConfig.getAppDataDir(cloudId), new String[0]));
        }
        downLoadFile(cloudId, downloadUrls);
    }

    private void downLoadFile(String cloudId, Map<String, String> downloadUrls) {
        downloadUrls.entrySet().removeIf(entry -> {
            return FileUtil.isFile((String) entry.getValue());
        });
        try {
            LOG.info("开始下载文件");
            download(downloadUrls);
            AppConfig.writeIni("tester.cloud.id", cloudId);
            killAndRestart();
        } catch (Exception ex) {
            LOG.error("下载文件异常,{}", ex.getMessage(), ex);
        }
    }

    private void download(Map<String, String> downloadUrls) throws Exception {
        final int index = 0;
        final int totalFiles = downloadUrls.size();
        for (Map.Entry<String, String> entry : downloadUrls.entrySet()) {
            String url = entry.getKey();
            final String filePath = entry.getValue();
            index++;
            DownloadUtil.download(url, filePath, new DownloadUtil.DownloadListener() { // from class: com.geely.gnds.tester.common.ChangeServer.1
                @Override // com.geely.gnds.tester.common.DownloadUtil.DownloadListener
                public void onFinish(String path) {
                    ChangeServer.LOG.info("下载完成，文件路径：{}", path);
                    UpgradeDownLoadDTO downLoadDTO = new UpgradeDownLoadDTO(path, Integer.valueOf(totalFiles), Integer.valueOf(index), 100);
                    GndsWebSocket.a(GndsMsgCodeEnum.CLOSE_DOWNlOAD, downLoadDTO);
                }

                @Override // com.geely.gnds.tester.common.DownloadUtil.DownloadListener
                public void onProgress(Integer progress) {
                    UpgradeDownLoadDTO downLoadDTO = new UpgradeDownLoadDTO((String) null, Integer.valueOf(totalFiles), Integer.valueOf(index), progress);
                    GndsWebSocket.a(GndsMsgCodeEnum.SHOW_DOWNlOAD, downLoadDTO);
                    ChangeServer.LOG.info("文件：{} 通知下载进度：{}", filePath, progress);
                }
            });
        }
    }

    private void killAndRestart() {
        TesterUtils.killBoot();
        TesterUtils.killBrowser();
        RuntimeUtil.exec((String[]) null, AppConfig.getAppHomeDir(), new String[]{AppConfig.getAppHomeDir().getAbsolutePath() + File.separator + "tester.exe"});
        TesterUtils.killMe();
    }
}
