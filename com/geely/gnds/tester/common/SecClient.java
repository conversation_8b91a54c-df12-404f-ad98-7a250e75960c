package com.geely.gnds.tester.common;

import com.sun.jna.Library;
import com.sun.jna.Native;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;

/* loaded from: SecClient.class */
public class SecClient {
    private static final Logger log = LoggerFactory.getLogger(SecClient.class);
    private static ClientSo clientSo;
    private static String machineID;

    /* loaded from: SecClient$ClientSo.class */
    public interface ClientSo extends Library {
        String EncryptAes(String str);

        String DecryptAes(String str);

        String GenerateMachineID();
    }

    static {
        init();
    }

    public static void init() {
        if (clientSo != null) {
            return;
        }
        try {
            clientSo = load();
        } catch (Exception e) {
            e.printStackTrace();
        }
        new Thread(() -> {
            machineID = clientSo.GenerateMachineID();
        }).start();
    }

    public static EncData encrypt(String input) {
        try {
            return new EncData(true, clientSo.EncryptAes(input));
        } catch (Exception e) {
            return new EncData(false, input);
        }
    }

    public static EncData decrypt(String input) {
        try {
            return new EncData(true, clientSo.DecryptAes(input));
        } catch (Exception e) {
            return new EncData(false, input);
        }
    }

    public static String generateMachineID() {
        return (String) Optional.ofNullable(machineID).orElseGet(() -> {
            return clientSo.GenerateMachineID();
        });
    }

    public static byte[] encrypt(byte[] input) {
        EncData encData = encrypt(Base64.getEncoder().encodeToString(input));
        if (!encData.isSuccess()) {
            return input;
        }
        return encData.getData().getBytes(StandardCharsets.UTF_8);
    }

    private static ClientSo load() throws Exception {
        String javaHome = System.getProperty("java.home");
        File jdkDir = new File(javaHome).getParentFile();
        File jdkBinDir = new File(jdkDir, "bin");
        File dllFile = new File(jdkBinDir, new String(Base64Utils.decodeFromString("amVkLmRsbA==")));
        if (dllFile.exists()) {
            return (ClientSo) Native.load(dllFile.getAbsolutePath(), ClientSo.class);
        }
        throw new IllegalArgumentException("Resource not found: DLL");
    }

    /* loaded from: SecClient$EncData.class */
    public static class EncData {
        private boolean success;
        private String data;

        public EncData(boolean success, String data) {
            this.success = success;
            this.data = data;
        }

        public boolean isSuccess() {
            return this.success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getData() {
            return this.data;
        }

        public void setData(String data) {
            this.data = data;
        }
    }
}
