package com.geely.gnds.tester.common;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/* loaded from: UploadBean.class */
public class UploadBean implements Serializable {
    private String localPath;
    private String ossPath;
    private String id;
    private Double size;
    private String type;
    private Date uploadTime;

    public UploadBean(String localPath, String ossPath, String id, Double size) {
        this.localPath = localPath;
        this.ossPath = ossPath;
        this.id = id;
        this.size = size;
    }

    public UploadBean(String localPath, String ossPath, String id, Double size, String type) {
        this.localPath = localPath;
        this.ossPath = ossPath;
        this.id = id;
        this.size = size;
        this.type = type;
    }

    public UploadBean(String localPath, String ossPath, String id, Double size, String type, Date uploadTime) {
        this.localPath = localPath;
        this.ossPath = ossPath;
        this.id = id;
        this.size = size;
        this.type = type;
        this.uploadTime = uploadTime;
    }

    public String getLocalPath() {
        return this.localPath;
    }

    public String getOssPath() {
        return this.ossPath;
    }

    public String getId() {
        return this.id;
    }

    public Double getSize() {
        return Double.valueOf(null == this.size ? 0.0d : this.size.doubleValue());
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Date getUploadTime() {
        return this.uploadTime;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UploadBean that = (UploadBean) o;
        return Objects.equals(this.localPath, that.localPath) && Objects.equals(this.ossPath, that.ossPath) && Objects.equals(this.id, that.id) && Objects.equals(this.size, that.size) && Objects.equals(this.type, that.type);
    }

    public int hashCode() {
        return Objects.hash(this.localPath, this.ossPath, this.id, this.size, this.type);
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String toString() {
        return "UploadBean{localPath='" + this.localPath + "', ossPath='" + this.ossPath + "', id='" + this.id + "', size=" + this.size + ", type='" + this.type + "'}";
    }
}
