package com.geely.gnds.tester.common;

import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

@Component
/* loaded from: CacheUtils.class */
public class CacheUtils {
    private static final Logger LOG = LoggerFactory.getLogger(CacheUtils.class);

    @Autowired
    private CacheManager cacheManager;

    public void clearAll() {
        LOG.info("----->清空当前设备所有缓存");
        this.cacheManager.getCacheNames().forEach(this::clear);
    }

    public void clear(String value) {
        LOG.info("----->清空 {} 缓存记录", value);
        Optional.ofNullable(this.cacheManager.getCache(value)).ifPresent(cache -> {
            cache.clear();
        });
    }
}
