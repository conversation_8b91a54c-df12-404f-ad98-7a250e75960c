package com.geely.gnds.tester.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

@EnableAspectJAutoProxy(proxyTargetClass = true)
@Aspect
@Component
/* loaded from: ServiceCallingAspect.class */
public class ServiceCallingAspect {
    private static final Logger logger = LoggerFactory.getLogger(ServiceCallingAspect.class);
    private TimeInterval timer = null;

    @Pointcut("execution(* com.geely.gnds..*Controller.*(..)) || execution(public * com.geely.gnds.tester.component.Cloud.*(..))")
    private void controllerMethodAspect() {
    }

    @Before("controllerMethodAspect()")
    public void doBefore() {
        this.timer = DateUtil.timer();
    }

    @AfterReturning(value = "controllerMethodAspect()", returning = "ret")
    public void after(JoinPoint call, Object ret) {
        String className = call.getTarget().getClass().getName();
        String methodName = call.getSignature().getName();
        String time = this.timer.intervalPretty();
        long t = this.timer.intervalRestart();
        if (t > 1000) {
            logger.warn("========>>>>>>>执行 {} 类的 {} 方法，耗时：{} 毫秒<<<<<<<========", new Object[]{className, methodName, time});
        }
    }
}
