package com.geely.gnds.tester.common;

import cn.hutool.core.thread.ThreadUtil;
import com.geely.gnds.tester.cache.MapDBUtil;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.TesterConfigService;
import com.geely.gnds.tester.task.FileUploadTask;
import java.util.Optional;
import javax.annotation.PreDestroy;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

@Component
/* loaded from: AppLifeCycleLinstener.class */
public class AppLifeCycleLinstener implements ServletContextListener, DisposableBean, ApplicationListener<ContextClosedEvent>, SmartLifecycle {
    private static final Logger LOG = LoggerFactory.getLogger(AppLifeCycleLinstener.class);

    @Autowired
    private FileUploadTask fileUploadTask;

    @Autowired
    private TesterConfigService configService;

    @Autowired
    private MapDBUtil mapDbUtil;
    private boolean runningFlag = false;

    public boolean isRunningFlag() {
        return this.runningFlag;
    }

    public void setRunningFlag(boolean runningFlag) {
        this.runningFlag = runningFlag;
    }

    public void onApplicationEvent(ContextClosedEvent event) {
        LOG.info("===>ContextClosedEvent...");
        stopGnds();
    }

    @PreDestroy
    public void preClose() {
        LOG.info("===>preClose...");
    }

    public void contextDestroyed(ServletContextEvent event) {
        this.mapDbUtil.close();
    }

    public void destroy() throws Exception {
        LOG.info("===>准备关停FdClient...");
        SingletonManager.getFdTcpClientMap().forEach((k, v) -> {
            Optional.ofNullable(v).ifPresent(fdTcpClient -> {
                LOG.info("===>开始关闭线程FdClient {} ", k);
                fdTcpClient.closeDoip();
            });
        });
        LOG.info("#Making the final preparations for system shutdown, waiting for 10 seconds");
        waitLog(10);
        LOG.info("#Service stopped");
    }

    private void stopGnds() {
        LOG.info("#Service stopping ...");
        Optional.ofNullable(this.configService.getConfig()).ifPresent(cfg -> {
            this.fileUploadTask.cI(cfg.getTesterCode());
        });
    }

    public void stop(Runnable callback) {
        callback.run();
        setRunningFlag(false);
    }

    public void start() {
        LOG.info("#Service is starting...");
        setRunningFlag(true);
    }

    public void stop() {
        LOG.info("#Service is stopping...");
        setRunningFlag(false);
    }

    public int getPhase() {
        return Integer.MAX_VALUE;
    }

    public boolean isRunning() {
        return isRunningFlag();
    }

    public boolean isAutoStartup() {
        return true;
    }

    private static void waitLog(int t) {
        if (t > 0) {
            LOG.info("#Waitting {}s...", Optional.of(Integer.valueOf(t)));
            ThreadUtil.sleep(1000L);
            waitLog(t - 1);
        }
    }
}
