package com.geely.gnds.tester.common;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.AliyunOssConfig;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import javax.servlet.ServletContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.WebApplicationContext;

@Component
/* loaded from: AliyunOssConfigKit.class */
public class AliyunOssConfigKit {
    private static final Logger log = LoggerFactory.getLogger(AliyunOssConfigKit.class);
    private final Cloud cloud;
    private final WebApplicationContext applicationContext;
    private volatile AliyunOssConfig config = null;
    private OSS ossClient;

    public AliyunOssConfigKit(Cloud cloud, WebApplicationContext applicationContext) {
        this.cloud = cloud;
        this.applicationContext = applicationContext;
    }

    public String getBucketName() {
        return getAliyunOssConfig().getBucketName();
    }

    public AliyunOssConfig getAliyunOssConfig() {
        AliyunOssConfig localConfig = this.config;
        if (localConfig == null) {
            synchronized (this) {
                localConfig = this.config;
                if (localConfig == null) {
                    localConfig = getOssConfig();
                    this.config = localConfig;
                }
            }
        }
        return localConfig;
    }

    public OSS getOssClient() {
        if (this.config == null) {
            this.config = getAliyunOssConfig();
            Optional.ofNullable(this.config).ifPresent(cfg -> {
                this.ossClient = new OSSClientBuilder().build(cfg.getEndpoint(), cfg.getAccessKeyId(), cfg.getAccessKeySecret(), cfg.getSecurityToken());
            });
        } else {
            Instant expInstant = Instant.parse(this.config.getExpiration());
            Instant now = Instant.now();
            if (!expInstant.isAfter(now)) {
                this.config = null;
                return getOssClient();
            }
        }
        return this.ossClient;
    }

    private AliyunOssConfig getOssConfig() {
        try {
            String username = ThreadLocalUtils.CURRENT_USER_NAME.get();
            log.info("日志查询---tokenObject开始username:【{}】", username);
            ServletContext servletContext = this.applicationContext.getServletContext();
            Object tokenObject = servletContext.getAttribute(username.toLowerCase() + "-token");
            if (null != tokenObject && StringUtils.hasText(tokenObject.toString())) {
                String ossConfig = this.cloud.getOssConfig("");
                log.info("OSS上传Token获取--->【{}】", ossConfig);
                this.config = convertOssConfig(ossConfig);
                return this.config;
            }
            return null;
        } catch (Exception e) {
            log.warn("无法获取云端的oss配置信息，等待下一次用户登录后获取。");
            return null;
        }
    }

    private AliyunOssConfig convertOssConfig(String ossConfig) {
        AliyunOssConfig config = new AliyunOssConfig();
        Map<String, Object> ossMap = ObjectMapperUtils.jsonStr2Map(ossConfig);
        config.setEndpoint((String) ossMap.get("endpoint"));
        config.setAccessKeyId((String) ossMap.get("accessKeyId"));
        config.setAccessKeySecret((String) ossMap.get("accessKeySecret"));
        config.setBucketName((String) ossMap.get("bucketName"));
        config.setSecurityToken((String) ossMap.get("securityToken"));
        config.setExpiration((String) ossMap.get("expiration"));
        return config;
    }
}
