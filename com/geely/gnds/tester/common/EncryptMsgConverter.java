package com.geely.gnds.tester.common;

import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.pattern.ConverterKeys;
import org.apache.logging.log4j.core.pattern.LogEventPatternConverter;

@ConverterKeys({"encryptMsg"})
@Plugin(name = "EncryptMsgConverter", category = "Converter")
/* loaded from: EncryptMsgConverter.class */
public class EncryptMsgConverter extends LogEventPatternConverter {
    protected EncryptMsgConverter(String[] options) {
        super("encryptMsg", "encryptMsg");
    }

    public static EncryptMsgConverter newInstance(String[] options) {
        return new EncryptMsgConverter(options);
    }

    public void format(LogEvent event, StringBuilder output) {
        String encryptedMsg = SecClient.encrypt(event.getMessage().getFormattedMessage()).getData();
        output.append("[$$").append(encryptedMsg).append("$$]");
    }
}
