package com.geely.gnds.tester.common;

import cn.hutool.core.io.StreamProgress;
import cn.hutool.core.thread.NamedThreadFactory;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.geely.gnds.ruoyi.common.utils.file.FileUploadUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpDns;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.net.SocketTimeoutException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DownloadUtil.class */
public class DownloadUtil {
    private static final int BUFFER_SIZE = 65536;
    private static final Logger log = LoggerFactory.getLogger(DownloadUtil.class);
    private static OkHttpClient okHttpClient = new OkHttpClient.Builder().connectTimeout(30, TimeUnit.SECONDS).readTimeout(60, TimeUnit.SECONDS).writeTimeout(60, TimeUnit.SECONDS).dns(HttpDns.getDns()).retryOnConnectionFailure(true).build();

    /* loaded from: DownloadUtil$DownloadListener.class */
    interface DownloadListener {
        void onProgress(Integer num);

        void onFinish(String str);
    }

    public static void download(String url, final String outputPath, final DownloadListener downloadListener) throws Exception {
        final long contentLength = getFileSize(url);
        log.info("Total file size: {} bytes", Long.valueOf(contentLength));
        if (contentLength <= 0) {
            throw new IOException("Unable to get file size: " + contentLength);
        }
        int threadCount = 1;
        if (contentLength > 10485760) {
            threadCount = 2;
        }
        if (contentLength > FileUploadUtils.DEFAULT_MAX_SIZE) {
            threadCount = 4;
        }
        if (threadCount > 1) {
            ExecutorService executor = Executors.newFixedThreadPool(threadCount, new NamedThreadFactory("DownloadThread", false));
            AtomicLong downloadedBytes = new AtomicLong(0L);
            RandomAccessFile outputFile = new RandomAccessFile(outputPath, "rw");
            outputFile.setLength(contentLength);
            outputFile.close();
            long chunkSize = contentLength / threadCount;
            int i = 0;
            while (i < threadCount) {
                long start = i * chunkSize;
                long end = (i == threadCount - 1 ? contentLength : start + chunkSize) - 1;
                executor.submit(() -> {
                    try {
                        downloadChunk(url, outputPath, start, end, downloadedBytes);
                    } catch (IOException e) {
                        log.error(e.getMessage(), e);
                        throw new RuntimeException(e);
                    }
                });
                i++;
            }
            executor.shutdown();
            while (!executor.isTerminated()) {
                long currentDownloaded = downloadedBytes.get();
                int percent = (int) ((currentDownloaded * 100) / contentLength);
                downloadListener.onProgress(Integer.valueOf(percent));
                Thread.sleep(500L);
            }
            downloadListener.onFinish(outputPath);
            log.info("Download complete.");
            return;
        }
        HttpUtil.downloadFile(url, new File(outputPath), new StreamProgress() { // from class: com.geely.gnds.tester.common.DownloadUtil.1
            public void start() {
            }

            public void progress(long total, long progressSize) {
                int percent2 = (int) ((progressSize * 100) / contentLength);
                downloadListener.onProgress(Integer.valueOf(percent2));
            }

            public void finish() {
                downloadListener.onFinish(outputPath);
            }
        });
    }

    /* JADX WARN: Finally extract failed */
    private static void downloadChunk(String url, String outputPath, long start, long end, AtomicLong downloadedBytes) throws IOException {
        Response response;
        Throwable th;
        int attempt = 0;
        boolean success = false;
        while (attempt < 3 && !success) {
            attempt++;
            try {
                Request request = new Request.Builder().url(url).addHeader("Range", "bytes=" + start + "-" + end).build();
                response = okHttpClient.newCall(request).execute();
                th = null;
            } catch (SocketTimeoutException e) {
                log.error("Timeout occurred for range: {}-{}, attempt: {}", new Object[]{Long.valueOf(start), Long.valueOf(end), Integer.valueOf(attempt)});
                if (attempt == 3) {
                    throw new IOException("Max retries reached for range: " + start + "-" + end, e);
                }
            } catch (IOException e2) {
                log.error("IOException for range: {}-{}, attempt: {}", new Object[]{Long.valueOf(start), Long.valueOf(end), Integer.valueOf(attempt)});
                if (attempt == 3) {
                    throw new IOException("Max retries reached for range: " + start + "-" + end, e2);
                }
            }
            try {
                RandomAccessFile file = new RandomAccessFile(outputPath, "rw");
                Throwable th2 = null;
                try {
                    try {
                        if (!response.isSuccessful()) {
                            throw new IOException("Failed to download chunk: " + response);
                        }
                        file.seek(start);
                        InputStream inputStream = response.body().byteStream();
                        byte[] buffer = new byte[BUFFER_SIZE];
                        while (true) {
                            int bytesRead = inputStream.read(buffer);
                            if (bytesRead == -1) {
                                break;
                            }
                            file.write(buffer, 0, bytesRead);
                            downloadedBytes.addAndGet(bytesRead);
                        }
                        success = true;
                        if (file != null) {
                            if (0 != 0) {
                                try {
                                    file.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                file.close();
                            }
                        }
                        if (response != null) {
                            if (0 != 0) {
                                try {
                                    response.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                response.close();
                            }
                        }
                    } catch (Throwable th5) {
                        if (file != null) {
                            if (th2 != null) {
                                try {
                                    file.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                file.close();
                            }
                        }
                        throw th5;
                    }
                } catch (Throwable th7) {
                    th2 = th7;
                    throw th7;
                }
            } catch (Throwable th8) {
                if (response != null) {
                    if (0 != 0) {
                        try {
                            response.close();
                        } catch (Throwable th9) {
                            th.addSuppressed(th9);
                        }
                    } else {
                        response.close();
                    }
                }
                throw th8;
            }
        }
    }

    private static long getFileSize(String fileUrl) {
        HttpResponse response = HttpRequest.head(fileUrl).execute();
        Throwable th = null;
        try {
            String contentLength = response.header("Content-Length");
            if (StrUtil.isNotBlank(contentLength)) {
                long fileSize = Long.parseLong(contentLength);
                log.info("文件: {} 大小为：{} 字节", fileUrl, Long.valueOf(fileSize));
                if (response != null) {
                    if (0 != 0) {
                        try {
                            response.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        response.close();
                    }
                }
                return fileSize;
            }
            log.warn("无法获取文件大小: {}", fileUrl);
            if (response != null) {
                if (0 != 0) {
                    try {
                        response.close();
                    } catch (Throwable th3) {
                        th.addSuppressed(th3);
                    }
                } else {
                    response.close();
                }
            }
            return -1L;
        } catch (Throwable th4) {
            if (response != null) {
                if (0 != 0) {
                    try {
                        response.close();
                    } catch (Throwable th5) {
                        th.addSuppressed(th5);
                    }
                } else {
                    response.close();
                }
            }
            throw th4;
        }
    }
}
