package com.geely.gnds.tester.common;

import com.aliyun.oss.event.ProgressEvent;
import com.aliyun.oss.event.ProgressEventType;
import com.aliyun.oss.event.ProgressListener;
import com.geely.gnds.tester.dto.LogCollectionProgressDto;
import com.geely.gnds.tester.enums.LogCollectionStatus;
import javax.servlet.http.HttpSession;
import jdk.nashorn.internal.runtime.logging.Logger;
import org.slf4j.LoggerFactory;

@Logger
/* loaded from: PutObjectProgressListener.class */
public class PutObjectProgressListener implements ProgressListener {
    private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(PutObjectProgressListener.class);
    private HttpSession session;
    private int percentStart;
    private LogCollectionProgressDto progressDto;
    private long bytesWritten = 0;
    private long totalBytes = -1;
    private boolean succeed = false;
    private int percent = 0;
    private String id = "";

    public PutObjectProgressListener(LogCollectionProgressDto progressDto) {
        this.percentStart = 0;
        this.progressDto = progressDto;
        this.percentStart = progressDto.getProgress();
    }

    public void progressChanged(ProgressEvent progressEvent) {
        long bytes = progressEvent.getBytes();
        ProgressEventType eventType = progressEvent.getEventType();
        switch (AnonymousClass1.$SwitchMap$com$aliyun$oss$event$ProgressEventType[eventType.ordinal()]) {
            case 2:
                this.totalBytes = bytes;
                break;
            case 3:
                this.bytesWritten += bytes;
                if (this.totalBytes != -1) {
                    this.percent = (int) ((this.bytesWritten * 100.0d) / this.totalBytes);
                    int add = ((int) ((this.percent / 100.0f) * (100 - this.percentStart))) + this.percentStart;
                    if (add > 100) {
                        add = 100;
                    }
                    LOG.info("日志收集进度{}", Integer.valueOf(add));
                    this.progressDto.setStatus(LogCollectionStatus.UPLOAD.getValue());
                    this.progressDto.setStatusInt(1);
                    this.progressDto.setProgress(add);
                    break;
                }
                break;
            case 4:
                this.succeed = true;
                break;
        }
    }

    /* renamed from: com.geely.gnds.tester.common.PutObjectProgressListener$1, reason: invalid class name */
    /* loaded from: PutObjectProgressListener$1.class */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] $SwitchMap$com$aliyun$oss$event$ProgressEventType = new int[ProgressEventType.values().length];

        static {
            try {
                $SwitchMap$com$aliyun$oss$event$ProgressEventType[ProgressEventType.TRANSFER_STARTED_EVENT.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$com$aliyun$oss$event$ProgressEventType[ProgressEventType.REQUEST_CONTENT_LENGTH_EVENT.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$com$aliyun$oss$event$ProgressEventType[ProgressEventType.REQUEST_BYTE_TRANSFER_EVENT.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$com$aliyun$oss$event$ProgressEventType[ProgressEventType.TRANSFER_COMPLETED_EVENT.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$com$aliyun$oss$event$ProgressEventType[ProgressEventType.TRANSFER_FAILED_EVENT.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    public boolean isSucceed() {
        return this.succeed;
    }
}
