package com.geely.gnds.tester.common;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.UploadFileRequest;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.dto.AliyunOssConfig;
import com.geely.gnds.tester.dto.LogCollectionProgressDto;
import java.io.File;
import java.io.InputStream;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
/* loaded from: SupportUploadService.class */
public class SupportUploadService {
    private static final Logger log = LoggerFactory.getLogger(SupportUploadService.class);
    private OSS ossClient = null;
    private final AliyunOssConfigKit aliyunOssConfigKit;

    public SupportUploadService(AliyunOssConfigKit aliyunOssConfigKit) {
        this.aliyunOssConfigKit = aliyunOssConfigKit;
    }

    private synchronized OSS getOssClient2() {
        AliyunOssConfig aliyunOssConfig = this.aliyunOssConfigKit.getAliyunOssConfig();
        Optional.ofNullable(aliyunOssConfig).ifPresent(config -> {
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            conf.setMaxConnections(HttpStatus.SUCCESS);
            conf.setSocketTimeout(HttpStatus.SETTLEMENT_FAIL);
            conf.setConnectionTimeout(HttpStatus.SETTLEMENT_FAIL);
            conf.setConnectionRequestTimeout(1000);
            conf.setIdleConnectionTime(10000L);
            conf.setMaxErrorRetry(1);
            conf.setSupportCname(true);
            this.ossClient = new OSSClientBuilder().build(config.getEndpoint(), config.getAccessKeyId(), config.getAccessKeySecret(), config.getSecurityToken(), conf);
        });
        return this.ossClient;
    }

    public void uploadFileToAliyunForLogCollection(String localpath, String key, boolean needUpdateProgress, String id, LogCollectionProgressDto progressDto, String username) throws Throwable {
        this.ossClient = getOssClient2();
        if (this.ossClient == null) {
            log.info("...未登录，或者无法获取到OSS配置信息，则取消上传");
            return;
        }
        ObjectMetadata meta = new ObjectMetadata();
        UploadFileRequest uploadFileRequest = new UploadFileRequest(this.aliyunOssConfigKit.getBucketName(), key);
        uploadFileRequest.setUploadFile(localpath);
        uploadFileRequest.setTaskNum(10);
        uploadFileRequest.setPartSize(10485760L);
        uploadFileRequest.setEnableCheckpoint(true);
        uploadFileRequest.setObjectMetadata(meta);
        PutObjectProgressListener putObjectProgressListener = new PutObjectProgressListener(progressDto);
        if (needUpdateProgress) {
            uploadFileRequest.setProgressListener(putObjectProgressListener);
        }
        this.ossClient.uploadFile(uploadFileRequest);
        if (needUpdateProgress) {
            File file = new File(localpath);
            if (file.exists()) {
                file.delete();
            }
        }
    }

    public void uploadFileToAliyun(InputStream inputStream, String key, String username) {
        try {
            this.ossClient = getOssClient2();
            if (this.ossClient == null) {
                log.info("...未登录，或者无法获取到OSS配置信息，则取消上传");
            } else {
                this.ossClient.putObject(this.aliyunOssConfigKit.getBucketName(), key, inputStream);
            }
        } catch (Exception e) {
            log.error("文件上传oss失败", e);
        }
    }
}
