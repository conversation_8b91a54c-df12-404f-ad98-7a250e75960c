package com.geely.gnds.tester.common;

import cn.hutool.core.util.StrUtil;
import com.geely.gnds.ruoyi.common.enums.HttpMethod;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.component.TesterDevice;
import com.google.common.util.concurrent.RateLimiter;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

@Component
@Order(1)
/* loaded from: RequestLimterFilter.class */
public class RequestLimterFilter extends OncePerRequestFilter {
    private static final Logger LOG = LoggerFactory.getLogger(RequestLimterFilter.class);
    private RateLimiter limiter = RateLimiter.create(10.0d);
    private static final long TIMEOUT = 2;
    private final HandlerExceptionResolver handlerExceptionResolver;

    public RequestLimterFilter(HandlerExceptionResolver handlerExceptionResolver) {
        this.handlerExceptionResolver = handlerExceptionResolver;
    }

    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String method = (String) Optional.ofNullable(request.getMethod()).orElse("");
        String requestTime = request.getHeader("RequestTime");
        LOG.info("接收到前端请求【{}】发起请求时间戳【{}】,参数：{}", new Object[]{request.getRequestURI(), requestTime, request.getQueryString()});
        if (StrUtil.contains(request.getContentType(), "json")) {
            if (StringUtils.isNotBlank(requestTime)) {
                try {
                    long i = System.currentTimeMillis() - Long.parseLong(requestTime);
                    if (i > 1000) {
                        TesterDevice device = (TesterDevice) SpringUtils.getBean(TesterDevice.class);
                        LOG.error("前端请求到达后端传输时间异常，时间间隔【{}ms】硬件信息【{}】", Long.valueOf(i), device.toString());
                    }
                } catch (Exception e) {
                    LOG.error("记录设备配置信息失败", e);
                }
            }
            if (method.equalsIgnoreCase(HttpMethod.POST.name())) {
                if (this.limiter.tryAcquire(TIMEOUT, TimeUnit.SECONDS)) {
                    filterChain.doFilter(request, response);
                    return;
                } else {
                    this.handlerExceptionResolver.resolveException(request, response, (Object) null, new IllegalAccessException("操作频繁"));
                    return;
                }
            }
            filterChain.doFilter(request, response);
            return;
        }
        filterChain.doFilter(request, response);
    }
}
