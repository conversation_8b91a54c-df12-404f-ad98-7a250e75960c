package com.geely.gnds.tester.common;

import com.geely.gnds.dsa.service.impl.DsaLogServiceImpl;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import java.util.HashMap;
import java.util.Map;

/* loaded from: LogTypeEnum.class */
public enum LogTypeEnum {
    SYS_PATH("sys_logs"),
    PCAP_PATH("pcap_logs"),
    XML_PATH("xml_logs"),
    TXT_PATH("txt_logs"),
    VBF_PATH("vbf_files");

    private String dirName;

    LogTypeEnum(String dirName) {
        this.dirName = dirName;
    }

    public String getDirName() {
        return this.dirName;
    }

    public TestLogTypeEnum convert() {
        if (compareTo(PCAP_PATH) == 0) {
            return TestLogTypeEnum.communication;
        }
        if (compareTo(TXT_PATH) == 0) {
            return TestLogTypeEnum.txt;
        }
        if (compareTo(SYS_PATH) == 0) {
            return TestLogTypeEnum.run;
        }
        if (compareTo(XML_PATH) == 0) {
            return TestLogTypeEnum.diagnosis;
        }
        return TestLogTypeEnum.run;
    }

    public String getExtName() {
        Map<LogTypeEnum, String> map = new HashMap<>();
        map.put(PCAP_PATH, DsaLogServiceImpl.eG);
        map.put(TXT_PATH, "txt");
        map.put(SYS_PATH, DsaLogServiceImpl.eF);
        map.put(XML_PATH, "xml");
        return map.get(this);
    }
}
