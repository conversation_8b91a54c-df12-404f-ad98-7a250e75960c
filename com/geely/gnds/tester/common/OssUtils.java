package com.geely.gnds.tester.common;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DroDTO;
import com.geely.gnds.tester.dto.LogUploadServerDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import com.geely.gnds.tester.upload.FileTypeEnum;
import com.geely.gnds.tester.upload.FileUploader;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
/* loaded from: OssUtils.class */
public class OssUtils implements DisposableBean, InitializingBean {

    @Autowired
    private Cloud cloud;

    @Autowired
    private FileUploader fileUploader;

    @Autowired
    private ThreadPoolTaskExecutor executor;
    private static final int LOW_LIMIT = 30720;
    private static final String FILE_NAME = "FileToUpload.json";
    private static final String NOT_SAVE_FILE = ".NOT_SAVE_FILE.json";
    private static final String UPLOADED_FILE_NAME = "UploadedFile.json";
    private static final Logger log = LoggerFactory.getLogger(OssUtils.class);
    private static boolean uploadPcapLog = true;
    private static boolean uploadSysLog = true;
    private static boolean timeout = false;
    private static int limit = 0;
    private static int userLimit = 2097152;
    private static volatile boolean isWork = false;
    private static Set<UploadBean> uploadBeanList = new CopyOnWriteArraySet();
    private static Map<String, LogUploadServerDto> dtoMap = new ConcurrentHashMap();
    private static Set<UploadBean> uploadedBeanList = new CopyOnWriteArraySet();
    private static volatile boolean uploadingTxt = false;
    private static volatile boolean uploadingSystemLog = false;
    private static volatile boolean uploadingPcap = false;
    private static volatile boolean uploadingXml = false;
    private static volatile boolean uploadingDro = false;
    private static volatile boolean uploadingEdr = false;

    public static void setUserLimit(int userLimit2) {
        userLimit = userLimit2 * DoipUtil.MAX_BYTE_ARRAY_SIZE;
    }

    public static void setUserLimit() {
        if (limit <= LOW_LIMIT && limit > 0) {
            limit = LOW_LIMIT;
        }
        if (!timeout) {
            limit = userLimit;
        }
        FileUploader.setLimit(limit);
    }

    public static void setTimeoutLimit() {
        timeout = true;
        limit = LOW_LIMIT;
        FileUploader.setLimit(limit);
    }

    public static void setUnLimit() {
        limit = 0;
        FileUploader.setLimit(limit);
    }

    public static boolean needUpload(String filePath) {
        for (UploadBean bean : uploadBeanList) {
            if (filePath.equals(bean.getLocalPath())) {
                return false;
            }
        }
        return true;
    }

    public void afterPropertiesSet() throws Exception {
        File jsonFile = new File(getLogPath().concat(NOT_SAVE_FILE));
        try {
            if (FileUtil.isFile(jsonFile)) {
                try {
                    String json = FileUtil.readString(jsonFile, StandardCharsets.UTF_8);
                    Map<String, LogUploadServerDto> mapObj = (Map) JSON.parseObject(json, new TypeReference<ConcurrentHashMap<String, LogUploadServerDto>>() { // from class: com.geely.gnds.tester.common.OssUtils.1
                    }, new Feature[0]);
                    dtoMap.putAll(mapObj);
                    FileUtil.del(jsonFile);
                } catch (Exception e) {
                    log.error("文件无法序列化：{}", e.getMessage());
                    FileUtil.del(jsonFile);
                }
            }
            File uploadedFile = new File(getLogPath().concat(UPLOADED_FILE_NAME));
            if (FileUtil.isFile(uploadedFile)) {
                try {
                    String json2 = FileUtil.readString(uploadedFile, StandardCharsets.UTF_8);
                    List<UploadBean> uploadedBeans = JSON.parseArray(json2, UploadBean.class);
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    List<UploadBean> todayUploadedBeans = (List) uploadedBeans.stream().filter(item -> {
                        return format.format(new Date()).equals(format.format(item.getUploadTime()));
                    }).collect(Collectors.toList());
                    uploadedBeanList.addAll(todayUploadedBeans);
                    writeUploadedJson();
                    uploadedBeans.removeAll(todayUploadedBeans);
                    uploadedBeans.forEach(item2 -> {
                        FileUtil.del(item2.getLocalPath());
                    });
                } catch (Exception e2) {
                    log.error("文件无法序列化：{}", e2.getMessage());
                }
            }
        } catch (Throwable th) {
            FileUtil.del(jsonFile);
            throw th;
        }
    }

    public void destroy() throws Exception {
        File jsonFile = new File(getLogPath().concat(NOT_SAVE_FILE));
        FileUtil.writeString(JSONUtil.toJsonStr(dtoMap), jsonFile, StandardCharsets.UTF_8);
    }

    public void cronUpload() {
        log.info("日志查询---定时任务开始【{}】", Boolean.valueOf(isWork));
        if (!isWork) {
            startUpload();
        }
    }

    private static boolean isNetWorkAvailable() {
        try {
            return HttpRequest.get("https://oss-cn-hangzhou.aliyuncs.com").execute().getStatus() == 403;
        } catch (Exception e) {
            log.warn("检测到网络无法连接，取消上传。");
            return false;
        }
    }

    private void startUpload() {
        Set<String> loginUsers = TesterLoginUtils.getLoginUsers();
        String username = "";
        if (!CollectionUtils.isEmpty(loginUsers)) {
            Iterator<String> iterator = loginUsers.iterator();
            if (iterator.hasNext()) {
                username = iterator.next();
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
            }
        }
        if (StrUtil.isBlank(username)) {
            log.info("...等待用户登录后再上传文件");
            return;
        }
        log.info("日志查询---startUpload开始uploading:【{}】,【{}】,【{}】,【{}】,【{}】", new Object[]{Boolean.valueOf(uploadingDro), Boolean.valueOf(uploadingPcap), Boolean.valueOf(uploadingTxt), Boolean.valueOf(uploadingSystemLog), Boolean.valueOf(uploadingXml)});
        if (isNetWorkAvailable()) {
            File jsonFile = new File(getLogPath().concat(FILE_NAME));
            if (FileUtil.isFile(jsonFile)) {
                String json = FileUtil.readString(jsonFile, StandardCharsets.UTF_8);
                List<UploadBean> uploadBeans = JSON.parseArray(json, UploadBean.class);
                uploadBeanList.addAll(uploadBeans);
                uploadBeanList = (Set) uploadBeanList.stream().sorted(Comparator.comparing((v0) -> {
                    return v0.getSize();
                }, Comparator.nullsLast((v0, v1) -> {
                    return v0.compareTo(v1);
                }))).collect(Collectors.toCollection(LinkedHashSet::new));
                String finalUsername = username;
                if (!uploadingXml) {
                    this.executor.execute(() -> {
                        ThreadLocalUtils.CURRENT_USER_NAME.set(finalUsername);
                        uploadingXml = true;
                        startUploadByType(TestLogTypeEnum.diagnosis.name());
                        uploadingXml = false;
                    });
                }
                if (!uploadingTxt) {
                    this.executor.execute(() -> {
                        ThreadLocalUtils.CURRENT_USER_NAME.set(finalUsername);
                        uploadingTxt = true;
                        startUploadByType(TestLogTypeEnum.txt.name());
                        uploadingTxt = false;
                    });
                }
                if (!uploadingSystemLog) {
                    this.executor.execute(() -> {
                        ThreadLocalUtils.CURRENT_USER_NAME.set(finalUsername);
                        uploadingSystemLog = true;
                        startUploadByType(TestLogTypeEnum.run.name());
                        uploadingSystemLog = false;
                    });
                }
                if (!uploadingPcap) {
                    this.executor.execute(() -> {
                        ThreadLocalUtils.CURRENT_USER_NAME.set(finalUsername);
                        uploadingPcap = true;
                        startUploadByType(TestLogTypeEnum.communication.name());
                        uploadingPcap = false;
                    });
                }
                if (!uploadingEdr) {
                    this.executor.execute(() -> {
                        ThreadLocalUtils.CURRENT_USER_NAME.set(finalUsername);
                        uploadingEdr = true;
                        startUploadByType(TestLogTypeEnum.edr.name());
                        uploadingEdr = false;
                    });
                }
            }
        }
    }

    public void uploadDro() {
        Set<String> loginUsers = TesterLoginUtils.getLoginUsers();
        if (CollectionUtils.isEmpty(loginUsers)) {
            log.info("...等待用户登录");
            return;
        }
        String username = "";
        if (!CollectionUtils.isEmpty(loginUsers)) {
            Iterator<String> iterator = loginUsers.iterator();
            if (iterator.hasNext()) {
                username = iterator.next();
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
            }
        }
        String finalUsername = username;
        this.executor.execute(() -> {
            uploadingDro = true;
            File file = new File(new File(ConstantEnum.POINT), "dro");
            if (file.exists()) {
                for (File fileJson : file.listFiles()) {
                    if (fileJson.getName().contains(ConstantEnum.JSON)) {
                        try {
                            String json = FileUtil.readString(fileJson, StandardCharsets.UTF_8);
                            DroDTO droDTO = (DroDTO) ObjectMapperUtils.jsonStr2Clazz(json, DroDTO.class);
                            UploadCloudBean uploadCloudBean = droDTO.getUploadCloudBean();
                            if (StringUtils.isBlank(uploadCloudBean.getId())) {
                                String id = saveLogToCloudDro(uploadCloudBean);
                                if (org.springframework.util.StringUtils.hasText(id)) {
                                    uploadCloudBean.setId(id);
                                }
                            }
                            this.cloud.a(droDTO, finalUsername);
                            fileJson.delete();
                        } catch (Exception e) {
                            log.error("本地dro上传失败，待下次上传", e);
                        }
                    }
                }
            }
            uploadingDro = false;
        });
    }

    /* JADX WARN: Finally extract failed */
    private void startUploadByType(String type) {
        try {
            Iterator<UploadBean> iterator = uploadBeanList.iterator();
            while (iterator.hasNext()) {
                UploadBean bean = iterator.next();
                if (bean != null) {
                    String localPath = bean.getLocalPath();
                    String logType = bean.getType();
                    if (StrUtil.isNotBlank(logType) && type.equalsIgnoreCase(logType)) {
                        log.info("日志查询---startUploadByType开始UploadBean:【{}】", bean);
                        try {
                            try {
                                String id = bean.getId();
                                File localFile = new File(localPath);
                                if (!FileUtil.isFile(localFile)) {
                                    iterator.remove();
                                    writeJson();
                                } else if (FileUtil.size(localFile) < 1) {
                                    writeJson();
                                } else {
                                    String logPath = bean.getLocalPath();
                                    String ossPath = bean.getOssPath();
                                    log.info("日志查询--------->>>>启动【{}】类型的文件上传,本地路径【{}】<<<<------", type, localPath);
                                    updateFileState(id, 1, logPath);
                                    this.fileUploader.upload(FileTypeEnum.of(type), localPath, ossPath);
                                    log.info("日志查询--------->>>>上传到阿里云成功【{}】类型的文件上传,本地路径【{}】<<<<------", type, localPath);
                                    updateFileState(id, 2, logPath);
                                    if (!localPath.endsWith(".log")) {
                                        try {
                                            FileUtil.del(localPath);
                                            log.error("日志查询---文件上传成功后，删除原始文件成功,文件：{}", localPath);
                                        } catch (Exception e) {
                                            log.error("日志查询---文件上传成功后，删除原始文件失败：{},\n文件：{}", e.getMessage(), localPath);
                                        }
                                    } else if (StrUtil.containsAny(localPath, new CharSequence[]{"applog/backup", "applog\\backup"})) {
                                        try {
                                            bean.setUploadTime(new Date());
                                            uploadedBeanList.add(bean);
                                            writeUploadedJson();
                                            log.error("日志查询---文件上传成功后，写入UploadedFile.json文件成功,文件：{}", localPath);
                                        } catch (Exception e2) {
                                            log.error("日志查询---文件上传成功后，写入UploadedFile.json文件失败：{},\n文件：{}", e2.getMessage(), localPath);
                                        }
                                    }
                                    iterator.remove();
                                    log.info("日志查询---########文件上传完成#######");
                                    writeJson();
                                }
                            } catch (Throwable e3) {
                                log.error("日志查询---########文件上传失败#######", e3);
                                writeJson();
                            }
                        } catch (Throwable th) {
                            writeJson();
                            throw th;
                        }
                    }
                }
            }
            uploadDto();
        } catch (Exception e4) {
            log.error(e4.getMessage(), e4);
        }
    }

    private void updateFileState(String id, int state, String localPath) throws Exception {
        LogUploadServerDto dto = new LogUploadServerDto();
        dto.setState(Integer.valueOf(state));
        dto.setId(id);
        dto.setLogPath(localPath);
        dto.setFileSize(Double.valueOf(getFileSize(localPath)));
        setFileLocalTime(dto);
        this.cloud.a(dto);
    }

    private synchronized void writeJson() {
        if (!uploadSysLog) {
            Iterator<UploadBean> iterator = uploadBeanList.iterator();
            while (iterator.hasNext()) {
                UploadBean bean = iterator.next();
                if (org.springframework.util.StringUtils.endsWithIgnoreCase(bean.getLocalPath(), ".log")) {
                    iterator.remove();
                }
            }
        }
        if (!uploadPcapLog) {
            Iterator<UploadBean> iterator2 = uploadBeanList.iterator();
            while (iterator2.hasNext()) {
                UploadBean bean2 = iterator2.next();
                if (org.springframework.util.StringUtils.endsWithIgnoreCase(bean2.getLocalPath(), ".pcap")) {
                    iterator2.remove();
                }
            }
        }
        log.info("OssUtils before,oss={}", uploadBeanList.stream().map((v0) -> {
            return v0.getOssPath();
        }).collect(Collectors.toSet()));
        if (!CollectionUtils.isEmpty(uploadBeanList)) {
            Map<String, UploadBean> temp = new HashMap<>();
            for (UploadBean uploadBean : uploadBeanList) {
                if (temp.containsKey(uploadBean.getOssPath())) {
                    uploadBeanList.remove(uploadBean);
                }
                temp.put(uploadBean.getOssPath(), uploadBean);
            }
        }
        log.info("OssUtils after,oss={}", uploadBeanList.stream().map((v0) -> {
            return v0.getOssPath();
        }).collect(Collectors.toSet()));
        File jsonFile = new File(getLogPath().concat(FILE_NAME));
        FileUtil.writeString(JSONUtil.toJsonStr(uploadBeanList), jsonFile, StandardCharsets.UTF_8);
    }

    private synchronized void writeUploadedJson() {
        File jsonFile = new File(getLogPath().concat(UPLOADED_FILE_NAME));
        FileUtil.writeString(JSONUtil.toJsonStr(uploadedBeanList), jsonFile, StandardCharsets.UTF_8);
    }

    private void uploadFile(UploadBean uploadBean) {
        Optional.ofNullable(uploadBean).ifPresent(m -> {
            uploadBeanList.add(uploadBean);
            writeJson();
        });
    }

    private void uploadDto() {
        Map<String, LogUploadServerDto> m = new ConcurrentHashMap<>(dtoMap);
        for (Map.Entry<String, LogUploadServerDto> dtoEntry : m.entrySet()) {
            LogUploadServerDto v = dtoEntry.getValue();
            String id = saveLogToCloud(v);
            if (org.springframework.util.StringUtils.hasText(id)) {
                uploadBeanList.add(new UploadBean(v.getLogPath(), v.getFilename(), id, v.getFileSize(), v.getLogType()));
            }
        }
        writeJson();
        dtoMap.clear();
    }

    public void uploadLogToCloud(UploadCloudBean bean) {
        Set<String> loginUsers = TesterLoginUtils.getLoginUsers();
        if (CollectionUtils.isEmpty(loginUsers)) {
            log.info("...等待用户登录");
            return;
        }
        if (bean.getTestLogTypeEnum().compareTo(TestLogTypeEnum.communication) == 0 && !uploadPcapLog) {
            delLocalFile(TestLogTypeEnum.communication, 10737418240L);
            return;
        }
        if (bean.getTestLogTypeEnum().compareTo(TestLogTypeEnum.run) == 0 && !uploadSysLog) {
            delLocalFile(TestLogTypeEnum.run, 2147483648L);
            return;
        }
        bean.setLogPath(org.springframework.util.StringUtils.cleanPath(bean.getLogPath()));
        String id = saveLogToCloud(bean);
        if (org.springframework.util.StringUtils.hasText(id)) {
            uploadFile(new UploadBean(bean.getLogPath(), bean.getNewPath(), id, Double.valueOf(getFileSize(bean.getLogPath())), bean.getTestLogTypeEnum().name()));
        }
    }

    private void delLocalFile(TestLogTypeEnum logType, long size) {
        String path = getLogPath();
        if (logType.compareTo(TestLogTypeEnum.run) == 0) {
            path = path + "backup";
        }
        if (logType.compareTo(TestLogTypeEnum.communication) == 0) {
            path = path + "pcap_logs";
        }
        File file = new File(path);
        List<File> list = FileUtil.loopFiles(file);
        Collections.sort(list, (a, b) -> {
            return a.lastModified() - b.lastModified() > 0 ? 1 : 0;
        });
        long totalSize = FileUtils.sizeOfDirectory(file);
        delLocalFile(list, totalSize, size);
    }

    private static void delLocalFile(List<File> list, long totalSize, long size) {
        if (totalSize > size) {
            try {
                totalSize -= list.get(0).length();
                log.info("文件夹大小：{},文件数量：{}", Long.valueOf(totalSize), Integer.valueOf(list.size()));
                log.info("删除文件：{}", list.get(0).getAbsolutePath());
                list.remove(0);
                FileUtil.del(list.get(0));
                delLocalFile(list, totalSize, size);
            } catch (Exception e) {
                log.error("文件{}删除失败，原因：{}", list.get(0), e.getMessage());
                if (list.size() > 0) {
                    delLocalFile(list, totalSize, size);
                }
            }
        }
    }

    public static void main(String[] args) {
        List<File> list = FileUtil.loopFiles(new File(FileUtil.getTmpDirPath()));
        Collections.sort(list, (a, b) -> {
            return a.lastModified() - b.lastModified() > 0 ? 1 : 0;
        });
        long totalSize = FileUtils.sizeOfDirectory(FileUtil.getTmpDir());
        delLocalFile(list, totalSize, 10485760L);
    }

    public String saveLogToCloud(UploadCloudBean bean) {
        return saveLogToCloud(org.springframework.util.StringUtils.hasText(bean.getVin()) ? bean.getVin() : "", bean.getUsername(), bean.getTenantName(), bean.getTesterId(), bean.getLogPath(), bean.getNewPath(), bean.getTestLogTypeEnum());
    }

    private String saveLogToCloud(String vin, String username, String tenantName, String testerId, String logPath, String newPath, TestLogTypeEnum testLogTypeEnum) {
        LogUploadServerDto logDto = new LogUploadServerDto();
        logDto.setTenantId(tenantName);
        logDto.setTesterId(testerId);
        logDto.setUsername(username);
        logDto.setFilename(newPath);
        logDto.setVin(StrUtil.trimToEmpty(vin));
        logDto.setState(0);
        logDto.setLogPath(logPath);
        logDto.setFileSize(Double.valueOf(getFileSize(logPath)));
        logDto.setLogType(testLogTypeEnum.name());
        logDto.setYear(Integer.valueOf(LocalDateTime.now().getYear()));
        logDto.setMonth(Integer.valueOf(LocalDateTime.now().getMonth().getValue()));
        logDto.setDay(Integer.valueOf(LocalDateTime.now().getDayOfMonth()));
        logDto.setLogCreateTime(new Date());
        return saveLogToCloud(logDto);
    }

    public String saveLogToCloudDro(UploadCloudBean bean) throws Exception {
        return saveLogToCloudDro(org.springframework.util.StringUtils.hasText(bean.getVin()) ? bean.getVin() : "", bean.getUsername(), bean.getTenantName(), bean.getTesterId(), bean.getLogPath(), bean.getNewPath(), bean.getTestLogTypeEnum());
    }

    private String saveLogToCloudDro(String vin, String username, String tenantName, String testerId, String logPath, String newPath, TestLogTypeEnum testLogTypeEnum) throws Exception {
        LogUploadServerDto logDto = new LogUploadServerDto();
        logDto.setTenantId(tenantName);
        logDto.setTesterId(testerId);
        logDto.setUsername(username);
        logDto.setFilename(newPath);
        logDto.setVin(StrUtil.trimToEmpty(vin));
        logDto.setState(0);
        logDto.setLogPath(logPath);
        logDto.setFileSize(Double.valueOf(getFileSize(logPath)));
        logDto.setLogType(testLogTypeEnum.name());
        logDto.setYear(Integer.valueOf(LocalDateTime.now().getYear()));
        logDto.setMonth(Integer.valueOf(LocalDateTime.now().getMonth().getValue()));
        logDto.setDay(Integer.valueOf(LocalDateTime.now().getDayOfMonth()));
        logDto.setLogCreateTime(new Date());
        logDto.setFileCreateTime(new Date());
        logDto.setFileUpdateTime(new Date());
        return this.cloud.a(logDto, username);
    }

    private String saveLogToCloud(LogUploadServerDto logDto) {
        if (!isNetWorkAvailable()) {
            log.error("网络不可用，日志记录临存，待下次重试");
            dtoMap.put(logDto.getFilename(), logDto);
            return null;
        }
        try {
            File localFile = new File(logDto.getLogPath());
            if (!FileUtil.isFile(localFile) || FileUtil.size(localFile) < 1) {
                return "";
            }
            setFileLocalTime(logDto);
            Set<String> loginUsers = TesterLoginUtils.getLoginUsers();
            String username = "";
            if (!CollectionUtils.isEmpty(loginUsers)) {
                ArrayList<String> usernameList = new ArrayList<>(loginUsers);
                username = usernameList.get(usernameList.size() - 1);
                log.info("日志文件上传，使用用户名：{}", username);
            }
            return this.cloud.a(logDto, username);
        } catch (Exception e) {
            log.error("日志保存至云端失败，本地路径：{},云端路径：{},原因：{}", new Object[]{logDto.getLogPath(), logDto.getFilename(), e.getMessage(), e});
            log.info("日志记录临存，待下次重试");
            dtoMap.put(logDto.getFilename(), logDto);
            return null;
        }
    }

    private void setFileLocalTime(LogUploadServerDto logDto) throws IOException {
        String logPath = "";
        try {
            logPath = logDto.getLogPath();
            File file = new File(logPath);
            Path path = Paths.get(file.getAbsolutePath(), new String[0]);
            BasicFileAttributes attrs = Files.readAttributes(path, (Class<BasicFileAttributes>) BasicFileAttributes.class, new LinkOption[0]);
            FileTime creationTime = attrs.creationTime();
            FileTime modifiedTime = attrs.lastModifiedTime();
            logDto.setFileCreateTime(new Date(creationTime.toMillis()));
            logDto.setFileUpdateTime(new Date(modifiedTime.toMillis()));
        } catch (IOException e) {
            log.error("setFileLocalTime 出现异常,e={},logPath={}", e, logPath);
        }
    }

    private static double getFileSize(String filename) {
        File file = new File(filename);
        if (!file.exists() || !file.isFile()) {
            return 0.0d;
        }
        return Math.ceil(file.length() / 1024.0d);
    }

    public static String getLogPath() {
        return org.springframework.util.StringUtils.cleanPath(FileUtil.file(AppConfig.getAppDataDir(), "applog").getAbsolutePath()) + File.separator;
    }

    public static String getFullLogPath(LogTypeEnum logType) {
        return getLogPath().concat(logType.getDirName()).concat(File.separator);
    }

    public void setUploadPcapLog(boolean isUpload) {
        uploadPcapLog = isUpload;
    }

    public void setUploadSysLog(boolean isUpload) {
        uploadSysLog = isUpload;
    }
}
