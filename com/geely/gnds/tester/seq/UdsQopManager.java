package com.geely.gnds.tester.seq;

import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import org.slf4j.LoggerFactory;

/* loaded from: UdsQopManager.class */
public class UdsQopManager implements IQopRelease, IQopUdsDataQueuer {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(UdsQopManager.class);
    private static final long SLEEP4_CREATE_THREAD = 10;
    private final IQopUdsProcessor processor;
    private DoipException doipException = null;
    private volatile Object dataLock = new Object();
    private volatile Object threadLock = new Object();
    private CountDownLatch latch = null;
    private volatile int activeCount = 0;
    private volatile Object activeLock = new Object();
    private volatile boolean closing = false;
    private ConcurrentLinkedQueue<UdsQopThread> idleThreads = new ConcurrentLinkedQueue<>();
    private ConcurrentLinkedQueue<UdsQopThread> senderThreads = new ConcurrentLinkedQueue<>();
    private List<UdsQopThread> qopThreads = new LinkedList();

    static /* synthetic */ int access$510(UdsQopManager x0) {
        int i = x0.activeCount;
        x0.activeCount = i - 1;
        return i;
    }

    public UdsQopManager(IQopUdsProcessor processor) {
        this.processor = processor;
    }

    public void initQop(String vin) throws InterruptedException {
        if (this.latch != null) {
            return;
        }
        this.latch = new CountDownLatch(2);
        createThread(this.latch, vin, 1);
        createThread(this.latch, vin, 2);
    }

    public boolean isSupportQop() {
        return this.latch != null;
    }

    private void createThread(CountDownLatch latch, String vin, int num) throws InterruptedException {
        UdsQopThread thread = new UdsQopThread(latch, vin, num) { // from class: com.geely.gnds.tester.seq.UdsQopManager.1
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                UdsQopManager.logger.info("启动Q操作线程：{}", getName());
                while (!UdsQopManager.this.closing) {
                    wait4Data();
                    if (!UdsQopManager.this.closing) {
                        try {
                            UdsQopManager.this.processor.udsData(this.udsData, UdsQopManager.this);
                        } catch (Exception e) {
                            String formatMsg = e.getMessage();
                            UdsQopManager.logger.error("{}{}", new Object[]{formatMsg, getName(), e});
                            UdsQopManager.this.setDoipException(formatMsg);
                            UdsQopManager.this.closing = true;
                        }
                        UdsQopManager.this.releaseQopThread(this);
                        synchronized (UdsQopManager.this.activeLock) {
                            UdsQopManager.access$510(UdsQopManager.this);
                            UdsQopManager.this.activeLock.notifyAll();
                        }
                    }
                }
                countDown();
                UdsQopManager.logger.info("关闭Q操作线程：{}", getName());
            }
        };
        this.idleThreads.add(thread);
        this.qopThreads.add(thread);
        thread.start();
        try {
            Thread.sleep(SLEEP4_CREATE_THREAD);
        } catch (InterruptedException e) {
            logger.info("正在等待启动Q操作子线程(" + thread.getName() + ")... ...");
        }
    }

    public UdsQopThread getQopThread() throws Exception {
        int threadCount;
        if (this.latch == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00129));
        }
        UdsQopThread thread = null;
        while (true) {
            if (thread != null) {
                break;
            }
            debug("开始取Q操作线程。。。 。。。activeCount=" + this.activeCount);
            synchronized (this.threadLock) {
                thread = this.idleThreads.poll();
                threadCount = this.idleThreads.size();
            }
            debug("剩余Q操作线程数 " + threadCount);
            if (thread != null) {
                debug("取到Q操作线程 " + thread.getName() + "; activeCount =" + this.activeCount + ";senderThreads的个数=" + this.senderThreads.size());
                synchronized (this.senderThreads) {
                    this.senderThreads.add(thread);
                }
                synchronized (this.activeLock) {
                    this.activeCount++;
                }
                break;
            }
            debug("没有执行线程，需要等待。。。 。。。");
            wait4Qop();
            if (this.closing) {
                break;
            }
        }
        return thread;
    }

    @Override // com.geely.gnds.tester.seq.IQopRelease
    public void releaseQopThread(UdsQopThread thread) {
        int threadCount;
        debug("Q操作线程 " + thread.getName() + " 开始空闲");
        synchronized (this.threadLock) {
            this.idleThreads.add(thread);
            threadCount = this.idleThreads.size();
        }
        debug("可用Q操作线程数 " + threadCount);
        notify4Qop();
    }

    private void debug(String msg) {
        if (logger.isDebugEnabled()) {
            logger.debug(msg);
        }
    }

    private void wait4Qop() {
        debug("UdsQopManager睡了。。。 。。。 " + Thread.currentThread().getName());
        try {
            synchronized (this.dataLock) {
                this.dataLock.wait();
                debug("UdsQopManager  wait醒了。。。 。。。 " + Thread.currentThread().getName());
            }
        } catch (InterruptedException e) {
            logger.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00077));
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    private void notify4Qop() {
        debug("UdsQopManager醒了。。。 。。。 " + Thread.currentThread().getName());
        synchronized (this.dataLock) {
            this.dataLock.notify();
            debug("UdsQopManager 醒了notify。。。 。。。 " + Thread.currentThread().getName());
        }
    }

    public void closeQop() throws InterruptedException {
        this.closing = true;
        if (this.latch == null) {
            return;
        }
        for (UdsQopThread thread : this.qopThreads) {
            thread.close();
        }
        try {
            this.latch.await();
        } catch (InterruptedException e) {
            logger.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00078));
            logger.error(FdHelper.getExceptionAsString(e));
        }
        this.latch = null;
        this.idleThreads.clear();
        this.qopThreads.clear();
    }

    @Override // com.geely.gnds.tester.seq.IQopUdsDataQueuer
    public void checkQopSender() throws DoipException {
        Thread senderThread = Thread.currentThread();
        boolean isFirstThread = false;
        while (!isFirstThread) {
            synchronized (this.senderThreads) {
                Thread topThread = this.senderThreads.element();
                isFirstThread = senderThread == topThread;
                if (isFirstThread) {
                    debug("checkQopSender成功:::::::topThread==" + topThread.getName() + ";releaseQopSender==" + senderThread.getName() + "; activeCount =" + this.activeCount);
                    return;
                }
                debug("checkQopSender失败:::::::topThread==" + topThread.getName() + ";releaseQopSender==" + senderThread.getName() + "; activeCount =" + this.activeCount);
                try {
                    this.senderThreads.wait();
                } catch (InterruptedException e) {
                    throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00128), e);
                }
            }
        }
    }

    @Override // com.geely.gnds.tester.seq.IQopUdsDataQueuer
    public void releaseQopSender() throws DoipException {
        Thread senderThread = Thread.currentThread();
        synchronized (this.senderThreads) {
            Thread topThread = this.senderThreads.element();
            if (senderThread != topThread) {
                debug(" releaseQopSender 失败:::::::topThread==" + topThread.getName() + "; releaseQopSender ==" + senderThread.getName() + "; activeCount =" + this.activeCount);
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00127));
            }
            this.senderThreads.poll();
            this.senderThreads.notifyAll();
            debug(" releaseQopSender 成功:::::::topThread==" + topThread.getName() + "; releaseQopSender ==" + senderThread.getName() + "; activeCount =" + this.activeCount);
        }
    }

    public void waitQopComplete() {
        synchronized (this.activeLock) {
            while (this.activeCount > 0) {
                try {
                    this.activeLock.wait();
                } catch (InterruptedException e) {
                    logger.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00079), e);
                }
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void setDoipException(String msg) {
        logger.info("设置异常");
        if (this.doipException == null) {
            logger.info("设置异常2{}", msg);
            this.doipException = new DoipException(msg);
        }
    }

    public DoipException getLastDoipException() {
        logger.info("获取异常{}", this.doipException);
        return this.doipException;
    }

    public boolean isClosing() {
        return this.closing;
    }
}
