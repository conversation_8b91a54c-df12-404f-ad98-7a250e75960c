package com.geely.gnds.tester.seq;

import org.slf4j.LoggerFactory;

/* loaded from: Logger.class */
public class Logger {
    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(Logger.class);

    public static void debug(String message) {
        LOGGER.debug(message);
    }

    public static void error(String message) {
        LOGGER.error(message);
    }

    public static void error(String message, Exception e) {
        LOGGER.error(message, e);
    }

    public static void info(String message) {
        LOGGER.info(message);
    }

    public static void warn(String message) {
        LOGGER.warn(message);
    }
}
