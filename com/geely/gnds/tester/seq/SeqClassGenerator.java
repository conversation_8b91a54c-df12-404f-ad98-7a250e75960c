package com.geely.gnds.tester.seq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.ruoyi.common.utils.CloseUtil;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.compile.MemoryClassLoader;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Predicate;
import com.jayway.jsonpath.ReadContext;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.io.FileUtils;
import org.slf4j.LoggerFactory;
import org.springframework.boot.loader.util.SystemPropertyUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/* loaded from: SeqClassGenerator.class */
public class SeqClassGenerator {
    private String scriptJson;
    private JSONObject scriptJsonObject;
    private ReadContext readContext;
    private static final String PATH_LOGGER_XML_LOG = "$.Logger_info.log_name";
    private static final String PATH_LOGGER_XML_VALUE = "$.Logger_info.Val";
    private static final String PATH_LOGGER_XML_COMPONENT_ID = "$.ComponentId";
    private static final String COMMENT_LOGGER_XML = "\t\t//log XML of {0}\r\n";
    private static final String STATEMENT_LOGGER_XML = "\t\tlogXml({0}, {1}, {2});\r\n";
    private static final String STATEMENT_LOGGER_ATTRIBUTE = "\t\tlogAttribute(\"{0}\", \"\" + {1}, \"{2}\", \"{3}\");\r\n";
    private static final String VARAIBLE_LOGGER_XML_COMPONENT_ID = "_data_component_id";
    private static final String VARAIBLE_LOGGER_XML_LOG_NAME = "_data_log_name";
    private static final String VARAIBLE_DEFINE = "\t\tString {0} = \"{1}\";\r\n";
    private static final String VARAIBLE_LOGGER_ATTRIBUTE_VALUE = "_data_value";
    private static final String DRO = "DRO";
    private static final String VARAIBLE_FOREACH = "_data_list";
    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(SeqClassGenerator.class);
    private static String SET_UI_END_head = escapeString("{\"Entrane_Name\":\"");
    private static String SET_UI_END_tail = escapeString("\",\"Logical_control\":\"SET_UI_END\"}");
    private static Map<String, String> varDeclarationMap = new ConcurrentHashMap();
    private AtomicInteger taskCount = new AtomicInteger();
    private StringBuffer sourceCode = null;
    private StringBuffer sourceCallDllCode = null;
    private String dllInterFaceName = "";
    private List<String> codeList = null;
    boolean mainStringFlag = true;
    private Class<?> classSub = null;
    private List<Class> moduleArguments = new ArrayList();
    private List<String> moduleGenArgs = new ArrayList();
    private List<String> varList = new ArrayList();
    private StringBuffer methodSourceCode = null;
    int indentLevel = 0;

    public Class<?> getClassName() {
        return this.classSub;
    }

    public void setClassName(Class<?> klass) {
        this.classSub = klass;
    }

    public void addVarDeclarationType(String key, String type) {
        if (StringUtils.isNotBlank(key)) {
            varDeclarationMap.put(key, type);
        }
    }

    public String getVarDeclarationType(String key) {
        if (varDeclarationMap.containsKey(key)) {
            return varDeclarationMap.get(key);
        }
        return "";
    }

    public List<Class> getModuleArguments() {
        return this.moduleArguments;
    }

    public SeqClassGenerator(String scriptJson) {
        this.scriptJson = null;
        this.scriptJsonObject = null;
        this.readContext = null;
        this.scriptJson = scriptJson;
        this.scriptJsonObject = JSONObject.parseObject(scriptJson);
        this.readContext = JsonPath.parse(this.scriptJson);
    }

    public boolean getMdp() {
        boolean mdp = false;
        Object eval = JSONPath.eval(this.scriptJsonObject, "$.MDP");
        if (eval != null) {
            String droString = eval.toString();
            if (ConstantEnum.TRUE.equalsIgnoreCase(droString)) {
                mdp = true;
            }
        }
        return mdp;
    }

    public int getTimeout() throws NumberFormatException {
        int timeout = 0;
        Object eval = JSONPath.eval(this.scriptJsonObject, "$.Timeout");
        if (eval != null) {
            String time = eval.toString();
            if (StringUtils.isNotBlank(time)) {
                timeout = Integer.parseInt(time);
            }
        }
        return timeout;
    }

    public boolean getDro() {
        boolean dro = false;
        Object eval = JSONPath.eval(this.scriptJsonObject, "$.DRO");
        if (eval != null) {
            String droString = eval.toString();
            if (ConstantEnum.TRUE.equalsIgnoreCase(droString)) {
                dro = true;
            }
        }
        return dro;
    }

    public String getEdr() {
        String edr = "";
        Object eval = JSONPath.eval(this.scriptJsonObject, "$.LoggerXML_Name");
        if (eval != null) {
            edr = eval.toString();
        }
        return edr;
    }

    public String getSeqName() {
        Object eval = JSONPath.eval(this.scriptJsonObject, "$.Name");
        if (eval == null) {
            return "";
        }
        return eval.toString();
    }

    public List<String> getMaskCode() {
        List<String> list = new ArrayList<>();
        Object eval = JSONPath.eval(this.scriptJsonObject, "$.MaskErrorCode");
        if (eval != null) {
            JSONArray maskCodes = (JSONArray) eval;
            Iterator it = maskCodes.iterator();
            while (it.hasNext()) {
                Object jo = it.next();
                list.add(jo.toString());
            }
        }
        list.add("SG00169");
        LOGGER.info("MaskErrorCode列表：{}", list.toString());
        return list;
    }

    private String getIndent() {
        StringBuilder sIndent = new StringBuilder();
        for (int i = 2; i < this.indentLevel; i++) {
            sIndent.append("\t");
        }
        return sIndent.toString();
    }

    public static boolean isScriptVariable(String str) {
        if (str != null && str.startsWith(ConstantEnum.INDEX) && str.endsWith(">")) {
            return true;
        }
        return false;
    }

    public static String getScriptVariableName(String str) {
        if (isScriptVariable(str)) {
            return str.substring(2, str.length() - 1);
        }
        return str;
    }

    public static String escapeString(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("\"", "\\\\\"");
    }

    public static String escapeString2(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("\\\\\"", "\"").replaceAll("\"", "\\\\\"").replaceAll("\\\\\"\\+", "\"+").replaceAll("\\+\\\\\"", "+\"");
    }

    private void processudsData(JSONObject node) throws Exception {
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        Object respondInfo = input.get("Respond_info_Result_Data");
        List<String> info = new ArrayList<>();
        if (respondInfo != null) {
            info = ObjectMapperUtils.jsonStr2List(respondInfo.toString(), String.class);
        }
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        String args = escapeString(input.toString());
        if (info.size() > 0) {
            String id = UUID.randomUUID().toString().replace("-", "");
            this.sourceCode.append("\t\t").append(returnVariableName).append(" = udsDataParse(\"").append(quoteVariable(args)).append("\",\"").append(id).append("\");\r\n");
            for (String variable : info) {
                if (this.varList.contains(variable)) {
                    this.sourceCode.append("\t\t");
                } else {
                    this.varList.add(variable);
                    this.sourceCode.append("\t\t").append("String ");
                }
                this.sourceCode.append(variable).append(" = getUdsData(\"").append(id).append("\",\"").append(variable).append("\");\r\n");
            }
        } else {
            this.sourceCode.append("\t\t").append(returnVariableName).append(" = udsData(\"").append(quoteVariable(args)).append("\");\r\n");
        }
        generateCheck();
    }

    private void processCallApi(String componentId, JSONObject node) throws Exception {
        String jsonVar;
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        String apiNameVar = input.getString("API_Name");
        boolean isApiNameScript = isScriptVariable(apiNameVar);
        String apiNameVar2 = quoteVariable2(apiNameVar);
        if (!isApiNameScript) {
            apiNameVar2 = "\"" + apiNameVar2 + "\"";
        }
        String jsonVar2 = StrFormatter.EMPTY_JSON;
        if (input.containsKey("InputJson")) {
            String inputJson = input.getString("InputJson");
            if (StringUtils.isNotBlank(inputJson)) {
                jsonVar2 = inputJson;
            }
        }
        boolean isJsonScript = isScriptVariable(jsonVar2);
        if (!isJsonScript) {
            this.sourceCode.append("\t\tString ").append(componentId).append("_data_json = \"").append(quoteVariable(jsonVar2.replaceAll("\"", "\\\\\\\""))).append("\";\r\n");
            jsonVar = componentId + "_data_json";
        } else {
            jsonVar = quoteVariable2(jsonVar2);
        }
        this.sourceCode.append("\t\t").append(returnVariableName).append(" = callApi(").append(apiNameVar2);
        this.sourceCode.append(", toJson(");
        this.sourceCode.append(jsonVar);
        this.sourceCode.append("), \"");
        this.sourceCode.append(componentId);
        this.sourceCode.append("\");\r\n");
        generateCheck();
    }

    private void processLoggerXml(JSONObject node) throws Exception {
        String value = quoteVariable(JSONPath.eval(node, PATH_LOGGER_XML_VALUE).toString());
        String logName = quoteVariable(JSONPath.eval(node, PATH_LOGGER_XML_LOG).toString());
        String componentId = JSONPath.eval(node, PATH_LOGGER_XML_COMPONENT_ID).toString();
        this.sourceCode.append(MessageFormat.format(COMMENT_LOGGER_XML, componentId));
        this.sourceCode.append(MessageFormat.format(VARAIBLE_DEFINE, componentId + VARAIBLE_LOGGER_XML_LOG_NAME, logName));
        this.sourceCode.append(MessageFormat.format(VARAIBLE_DEFINE, componentId + VARAIBLE_LOGGER_XML_COMPONENT_ID, componentId));
        this.sourceCode.append(MessageFormat.format(STATEMENT_LOGGER_XML, componentId + VARAIBLE_LOGGER_XML_LOG_NAME, "\"" + value + "\"", componentId + VARAIBLE_LOGGER_XML_COMPONENT_ID));
    }

    private void processGetJsonProperty(JSONObject node) throws Exception {
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        this.sourceCode.append("\t\t//set JsonProperty of ").append(node.getString("ComponentId")).append("\r\n");
        this.sourceCode.append("\t\t").append(returnVariableName).append(" = getJsonProperty(").append(getScriptVariableName(input.getString("JsonVariable"))).append(", \"").append(quoteVariable(input.getString("Path"))).append("\");\r\n");
        generateCheck();
    }

    private void processSetJsonProperty(JSONObject node) throws Exception {
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        this.sourceCode.append("\t\t//set JsonProperty of ").append(node.getString("ComponentId")).append("\r\n");
        this.sourceCode.append("\t\t").append(returnVariableName).append(" = setJsonProperty(").append(input.getString("JsonVariable")).append(", \"").append(quoteVariable(input.getString("Path"))).append("\", \"").append(quoteVariable(input.getString("Value"))).append("\");\r\n");
        generateCheck();
    }

    private void processcalldll(JSONObject node) throws Exception {
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        String args = escapeString(input.toString());
        this.sourceCode.append("\t\t").append(returnVariableName).append(" = callDll(\"").append(quoteVariable(args)).append("\");\r\n");
        generateCheck();
    }

    private void processsetUi(JSONObject node) throws Exception {
        generateCheck();
        this.sourceCode.append("\t\t\taddSetUi(\"").append(escapeString2(quoteVariable4(node.toString()))).append("\");\r\n");
    }

    private void processvbfSwdl(JSONObject node) throws Exception {
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        escapeString(input.toString());
        this.sourceCode.append("\t\t").append(returnVariableName).append(" = vbfswdl(\"").append(escapeString2(quoteVariable4(input.toString()))).append("\");\r\n");
        generateCheck();
    }

    private void processGetSecurityKey(JSONObject node) throws Exception {
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        String args = escapeString(input.toString());
        this.sourceCode.append("\t\t").append(returnVariableName).append(" = getSecurityKey(\"").append(quoteVariable(args)).append("\");\r\n");
        generateCheck();
    }

    private static String quoteVariable(String str) throws Exception {
        if (str != null && str.indexOf(ConstantEnum.INDEX) >= 0) {
            StringBuffer sb = new StringBuffer();
            while (str.indexOf(ConstantEnum.INDEX) >= 0) {
                sb.append(str.substring(0, str.indexOf(ConstantEnum.INDEX)));
                String str2 = str.substring(str.indexOf(ConstantEnum.INDEX) + 2);
                String varName = str2.substring(0, str2.indexOf(">"));
                str = str2.substring(str2.indexOf(">") + 1);
                sb.append("\"+").append(varName).append("+\"");
            }
            sb.append(str);
            str = sb.toString();
        }
        return str;
    }

    private static String quoteVariable3(String str) throws Exception {
        if (str != null && str.indexOf(ConstantEnum.INDEX) >= 0) {
            StringBuffer sb = new StringBuffer();
            while (str.indexOf(ConstantEnum.INDEX) >= 0) {
                sb.append(str.substring(0, str.indexOf(ConstantEnum.INDEX)));
                String str2 = str.substring(str.indexOf(ConstantEnum.INDEX) + 2);
                String varName = str2.substring(0, str2.indexOf(">"));
                str = str2.substring(str2.indexOf(">") + 1);
                sb.append("@+").append(varName).append("+@");
            }
            sb.append(str);
            str = sb.toString();
        }
        return str;
    }

    private static String quoteVariable2(String str) throws Exception {
        if (str != null && str.indexOf(ConstantEnum.INDEX) >= 0) {
            StringBuffer sb = new StringBuffer();
            while (str.indexOf(ConstantEnum.INDEX) >= 0) {
                sb.append(str.substring(0, str.indexOf(ConstantEnum.INDEX)));
                String str2 = str.substring(str.indexOf(ConstantEnum.INDEX) + 2);
                String varName = str2.substring(0, str2.indexOf(">"));
                str = str2.substring(str2.indexOf(">") + 1);
                sb.append(varName);
            }
            sb.append(str);
            str = sb.toString();
        }
        return str;
    }

    private static String quoteVariable4(String str) throws Exception {
        if (str != null && str.indexOf(ConstantEnum.INDEX) >= 0) {
            StringBuffer sb = new StringBuffer();
            while (str.indexOf(ConstantEnum.INDEX) >= 0) {
                sb.append(str.substring(0, str.indexOf(ConstantEnum.INDEX)));
                String str2 = str.substring(str.indexOf(ConstantEnum.INDEX) + 2);
                String varName = str2.substring(0, str2.indexOf(">"));
                str = str2.substring(str2.indexOf(">") + 1);
                sb.append("+JSON.toJSONString(").append(varName).append(")+");
            }
            sb.append(str);
            str = sb.toString();
        }
        return str;
    }

    private static String quoteVariable5(String str) throws Exception {
        if (str != null && str.indexOf(ConstantEnum.INDEX) >= 0) {
            StringBuffer sb = new StringBuffer();
            while (str.indexOf(ConstantEnum.INDEX) >= 0) {
                sb.append(str.substring(0, str.indexOf(ConstantEnum.INDEX)));
                String str2 = str.substring(str.indexOf(ConstantEnum.INDEX) + 2);
                String varName = str2.substring(0, str2.indexOf(">"));
                str = str2.substring(str2.indexOf(">") + 1);
                sb.append("\"+JSON.toJSONString(").append(varName).append(")+\"");
            }
            sb.append(str);
            str = sb.toString();
        }
        return str;
    }

    private void processForEach(JSONObject node) throws Exception {
        String componentId = node.getString("ComponentId");
        Object listObj = JSONPath.eval(node, "$.Enumerable");
        Object forEachVarId = JSONPath.eval(node, "$.ForEachVarId");
        if (forEachVarId != null) {
            componentId = forEachVarId.toString();
        }
        if (listObj instanceof String) {
            this.sourceCode.append("\t\tJSONArray ").append(componentId).append(VARAIBLE_FOREACH).append(" = (JSONArray)JSONPath.extract(").append(quoteVariable2((String) listObj)).append(", \"$\");\r\n");
        } else if (listObj instanceof JSONArray) {
            String listContent = listObj.toString().replaceAll("\"", "\\\\\"");
            this.sourceCode.append("\t\tJSONArray ").append(componentId).append(VARAIBLE_FOREACH).append(" = JSONArray.parseArray(\"").append(listContent).append("\");\n");
        } else {
            String listContent2 = listObj.toString().replaceAll("\"", "\\\\\"");
            this.sourceCode.append("\t\tJSONArray ").append(componentId).append(VARAIBLE_FOREACH).append(" = (JSONArray) JSONPath.eval(\"").append(listContent2).append("\", \"$\");\r\n");
        }
        this.sourceCode.append("\t\tfor(").append("Object").append(ConstantEnum.EMPTY).append(componentId).append(" : ").append(componentId).append(VARAIBLE_FOREACH).append("){\r\n");
        this.sourceCode.append("\t\tLogger.debug(\"loop for ").append(componentId).append(" ...\");\r\n");
        JSONArray loops = (JSONArray) JSONPath.eval(node, "$.Loop");
        Iterator it = loops.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject component = (JSONObject) jo;
            processComponent(component);
        }
        this.sourceCode.append("\t\t}\r\n");
    }

    private void processForEachJava(JSONObject node) throws Exception {
        String componentId = node.getString("ComponentId");
        Object forEachVarId = JSONPath.eval(node, "$.ForEachJavaVarId");
        if (forEachVarId != null) {
            componentId = forEachVarId.toString();
        }
        String forType = node.getString("forType");
        Object listObj = JSONPath.eval(node, "$.Enumerable");
        this.sourceCode.append("\t\tfor(").append(forType).append(ConstantEnum.EMPTY).append(componentId).append(" : ").append(quoteVariable2(listObj.toString())).append("){\r\n");
        this.sourceCode.append("\t\tLogger.debug(\"loop for ").append(componentId).append(" ...\");\r\n");
        JSONArray loops = (JSONArray) JSONPath.eval(node, "$.Loop");
        Iterator it = loops.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject component = (JSONObject) jo;
            processComponent(component);
        }
        this.sourceCode.append("\t\t}\r\n");
    }

    private void processIf(JSONObject node) throws Exception {
        String componentId = node.getString("ComponentId");
        String logicStatement = String.valueOf(JSONPath.eval(node, "$.LogicStatement.Expression"));
        this.sourceCode.append("\t\tif(").append(logicStatement).append("){\r\n");
        this.sourceCode.append("\t\t\tLogger.debug(\"if for ").append(componentId).append(" ...\");\r\n");
        JSONArray trues = (JSONArray) JSONPath.eval(node, "$.True");
        processComponentList(trues);
        this.sourceCode.append("\t\t} else{\r\n");
        JSONArray falses = (JSONArray) JSONPath.eval(node, "$.Else");
        processComponentList(falses);
        this.sourceCode.append("\t\t}\r\n");
        this.sourceCode.append("\t\tboolean ").append(componentId).append(VARAIBLE_LOGGER_ATTRIBUTE_VALUE).append(" = ").append(logicStatement).append(";\r\n");
        this.sourceCode.append(MessageFormat.format(STATEMENT_LOGGER_ATTRIBUTE, "", componentId + VARAIBLE_LOGGER_ATTRIBUTE_VALUE, logicStatement.replaceAll("\"", "\\\\\""), componentId));
    }

    private void processTry(JSONObject node) throws Exception {
        node.getString("ComponentId");
        this.sourceCode.append("\t\ttry {\r\n");
        JSONArray tryList = (JSONArray) JSONPath.eval(node, "$.try");
        processComponentList(tryList);
        this.sourceCode.append("\t\t}catch (Exception e){\r\n");
        JSONArray exceptList = (JSONArray) JSONPath.eval(node, "$.except");
        processComponentList(exceptList);
        String throwFlag = node.getString("throw");
        if (StringUtils.isNotBlank(throwFlag) && ConstantEnum.TRUE.equalsIgnoreCase(throwFlag)) {
            this.sourceCode.append("throw e;");
        }
        this.sourceCode.append("\t\t}finally {\r\n");
        JSONArray finallyList = (JSONArray) JSONPath.eval(node, "$.finally");
        processComponentList(finallyList);
        this.sourceCode.append("\t\t}\r\n");
    }

    private void processCheckPrecondition(JSONObject node) throws Exception {
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        JSONObject precondition = (JSONObject) JSONPath.eval(node, "$.Precondition");
        this.sourceCode.append("\t\t").append(returnVariableName).append(" = checkPrecondition(\"").append(JSONObject.toJSONString(precondition).replaceAll("\\\"", "\\\\\"")).append("\");\r\n");
        generateCheck();
    }

    private void processComponentList(JSONArray arrays) throws Exception {
        if (!arrays.isEmpty()) {
            Iterator it = arrays.iterator();
            while (it.hasNext()) {
                Object obj = it.next();
                JSONObject component = (JSONObject) obj;
                processComponent(component);
            }
        }
    }

    private void processWhile(JSONObject node) throws Exception {
        String componentId = node.getString("ComponentId");
        String logicStatement = String.valueOf(JSONPath.eval(node, "$.LogicStatement.Expression"));
        this.sourceCode.append("\t\twhile(").append(logicStatement).append("){\r\n");
        this.sourceCode.append("\t\tLogger.debug(\"loop for ").append(componentId).append(" ...\");\r\n");
        this.sourceCode.append("\t\tboolean ").append(componentId).append(VARAIBLE_LOGGER_ATTRIBUTE_VALUE).append(" = ").append(logicStatement).append(";\r\n");
        this.sourceCode.append(MessageFormat.format(STATEMENT_LOGGER_ATTRIBUTE, "", componentId + VARAIBLE_LOGGER_ATTRIBUTE_VALUE, logicStatement.replaceAll("\"", "\\\\\""), componentId));
        JSONArray loops = (JSONArray) JSONPath.eval(node, "$.Loop");
        Iterator it = loops.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject component = (JSONObject) jo;
            processComponent(component);
        }
        this.sourceCode.append("\t\t}\r\n");
    }

    private void processContinue(JSONObject node) throws Exception {
        this.sourceCode.append("\t\t").append("continue;").append("\r\n");
    }

    private void processBreak(JSONObject node) throws Exception {
        this.sourceCode.append("\t\t").append("break;").append("\r\n");
    }

    private void processReturn(JSONObject node) throws Exception {
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Return_info").toString());
        this.sourceCode.append("\t\t").append("if(1==1){ ").append("\r\n");
        this.sourceCode.append("\t\t").append("return ").append(returnVariableName).append(";}").append("\r\n");
    }

    private void processParallel(JSONObject node) throws Exception {
        String componentId = node.getString("ComponentId");
        LOGGER.info("processParallel componentId:{}", componentId);
        JSONArray componentInfos = (JSONArray) JSONPath.eval(node, "$.ComponentInfo");
        String name = "latch" + this.taskCount.incrementAndGet();
        this.sourceCode.append("\t\tCountDownLatch ").append(name).append(" =new CountDownLatch(").append(componentInfos.size()).append(");").append("\r\n");
        Iterator it = componentInfos.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject jb = (JSONObject) jo;
            this.sourceCode.append("\t\t").append("TokenManager.getPool().execute(() -> {").append("\r\n");
            this.sourceCode.append("\t\t").append("try {").append("\r\n");
            processComponent(jb);
            this.sourceCode.append("\t\t").append("} catch (Exception e) {\ne.printStackTrace();\n}finally {\n" + name + ".countDown();\n}\n}\n);").append("\r\n");
        }
        this.sourceCode.append("\t\t").append(name).append(".await();").append("\r\n");
        generateCheck();
    }

    private void processCallModule(JSONObject node) throws Exception {
        String arg;
        String moduleName = JSONPath.eval(node, "$.Input.Module").toString();
        String resultName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        JSONArray args = (JSONArray) JSONPath.eval(node, "$.Input.Arguments");
        this.sourceCode.append("\t\t").append(resultName).append(" = ").append(moduleName).append("(");
        int argCount = 0;
        Iterator it = args.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject jb = (JSONObject) jo;
            String arg2 = jb.getString((String) jb.keySet().iterator().next());
            if (isScriptVariable(arg2)) {
                arg = getScriptVariableName(arg2);
            } else {
                arg = "\"" + arg2 + "\"";
            }
            if (argCount > 0) {
                this.sourceCode.append(", " + arg);
            } else {
                this.sourceCode.append(arg);
            }
            argCount++;
        }
        this.sourceCode.append(");\r\n");
    }

    private void processRunScript2(JSONObject node) throws Exception {
        String seqCode = JSONPath.eval(node, "$.RunScript.Input.NevisLink.GRINumber").toString();
        this.sourceCode.append("runScript(\"" + seqCode + "\");").append("\r\n");
    }

    private void processRunScript(JSONObject node) throws Exception {
        JSONArray jsonArray;
        String seqCode = JSONPath.eval(node, "$.RunScript.Input.NevisLink.GRINumber").toString();
        String componentId = JSONPath.eval(node, PATH_LOGGER_XML_COMPONENT_ID).toString();
        Object eval = JSONPath.eval(node, "$.RunScript.Input.Arguments");
        String resultName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        String varDeclarationType = getVarDeclarationType(resultName);
        String respondType = ConstantEnum.STRING;
        if (StringUtils.isNotBlank(varDeclarationType)) {
            respondType = varDeclarationType;
        }
        if (ObjectUtils.isEmpty(eval)) {
            jsonArray = new JSONArray();
        } else {
            jsonArray = (JSONArray) eval;
        }
        String mapName = "init_" + componentId.replaceAll(ConstantEnum.EMPTY, "") + UUID.randomUUID().toString().replace("-", "");
        this.sourceCode.append("List<Object> ").append(mapName).append("=new ArrayList<>();").append("\r\n");
        Iterator it = jsonArray.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject jb = (JSONObject) jo;
            String name = (String) jb.keySet().iterator().next();
            String arg = jb.getString(name);
            if (isScriptVariable(arg)) {
                arg = quoteVariable2(arg);
            }
            this.sourceCode.append(mapName).append(".add(").append(arg).append(");").append("\r\n");
        }
        this.sourceCode.append(resultName).append(" = ").append("(").append(respondType).append(")").append("runScript(\"" + seqCode + "\",").append(mapName).append(");").append("\r\n");
        this.sourceCode.append("if(isException){throwException();}").append("\r\n");
        generateCheck();
    }

    private void processEval(JSONObject node) throws Exception {
        Object eval = JSONPath.eval(node, "$.eval.expression");
        this.sourceCode.append("\t\t//eval source of ").append(node.getString("ComponentId")).append("\r\n");
        this.sourceCode.append("\t\t").append(eval).append("\r\n");
        if (eval != null && this.mainStringFlag) {
            if (eval.toString().contains("setSeqResult(true)")) {
                this.sourceCode.append("\t\t").append("if(1==1){ ").append("\r\n");
                this.sourceCode.append("\t\t").append("return \"Pass\";}").append("\r\n");
            }
            if (eval.toString().contains("setSeqResult(false)")) {
                this.sourceCode.append("\t\t").append("if(1==1){ ").append("\r\n");
                this.sourceCode.append("\t\t").append("return \"Fail\";}").append("\r\n");
            }
        }
        generateCheck();
    }

    private void processVarDeclaration(JSONArray jsonArray) throws Exception {
        Iterator it = jsonArray.iterator();
        while (it.hasNext()) {
            Object obj = it.next();
            JSONObject var = (JSONObject) obj;
            if (!"Global".equals(var.getString("Scope"))) {
                processVarDeclaration(var);
            }
        }
    }

    private void processVarDeclaration(JSONObject var) throws Exception {
        String type = var.getString("Type");
        String initialValue = var.getString("Initial_value");
        String componentId = var.getString("ComponentId");
        this.varList.add(componentId);
        addVarDeclarationType(componentId, getJavaType(type));
        if (ConstantEnum.STRING.equalsIgnoreCase(type) || ConstantEnum.STRING_JSON.equalsIgnoreCase(type)) {
            if (initialValue == null) {
                this.sourceCode.append("\tString ").append(componentId).append(" = null").append(";\r\n");
                return;
            } else {
                this.sourceCode.append("\tString ").append(componentId.replaceAll("\"", "\\\\\"")).append(" = \"").append(initialValue).append("\";\r\n");
                return;
            }
        }
        if (ConstantEnum.INT.equalsIgnoreCase(type)) {
            this.sourceCode.append("\tint ").append(componentId).append(" = ").append(var.getIntValue("Initial_value")).append(";\r\n");
            return;
        }
        if (ConstantEnum.JSON_OBJECT.equalsIgnoreCase(type)) {
            this.sourceCode.append("\tJSONObject ").append(componentId).append(" = ").append("(JSONObject) JSON.parse(\"").append(initialValue.replaceAll("\"", "\\\\\"")).append("\");\r\n");
            return;
        }
        if (ConstantEnum.LIST.equalsIgnoreCase(type)) {
            String listContent = initialValue.replaceAll("\"", "\\\\\"");
            this.sourceCode.append("\t\tJSONArray ").append(componentId).append(" = JSONArray.parseArray(\"").append(listContent).append("\");\n");
            return;
        }
        if (ConstantEnum.SHORT.equalsIgnoreCase(type)) {
            this.sourceCode.append("\tshort ").append(componentId).append(" = ").append((int) var.getShortValue("Initial_value")).append(";\r\n");
            return;
        }
        if (ConstantEnum.LONG.equalsIgnoreCase(type)) {
            this.sourceCode.append("\tlong ").append(componentId).append(" = ").append(var.getLongValue("Initial_value")).append(";\r\n");
            return;
        }
        if (ConstantEnum.FLOAT.equalsIgnoreCase(type)) {
            this.sourceCode.append("\tfloat ").append(componentId).append(" = (float) ").append(var.getFloatValue("Initial_value")).append(";\r\n");
            return;
        }
        if (ConstantEnum.DOUBLE.equalsIgnoreCase(type)) {
            this.sourceCode.append("\tdouble ").append(componentId).append(" = ").append(var.getDoubleValue("Initial_value")).append(";\r\n");
            return;
        }
        if (ConstantEnum.CHAR.equalsIgnoreCase(type)) {
            char init = 0;
            if (initialValue != null && initialValue.length() > 0) {
                init = initialValue.charAt(0);
            }
            this.sourceCode.append("\tchar ").append(componentId).append(" = ").append(init).append(";\r\n");
            return;
        }
        if (ConstantEnum.BOOLEAN.equalsIgnoreCase(type)) {
            this.sourceCode.append("\tboolean ").append(componentId).append(" = ").append(var.getBooleanValue("Initial_value")).append(";\r\n");
            return;
        }
        if (ConstantEnum.BYTE.equalsIgnoreCase(type)) {
            this.sourceCode.append("\tbyte ").append(componentId).append(" = ").append((int) var.getByteValue("Initial_value")).append(";\r\n");
            return;
        }
        if (type.contains(ConstantEnum.LISTJAVA)) {
            this.sourceCode.append(type.replace(ConstantEnum.LISTJAVA, ConstantEnum.LIST)).append(ConstantEnum.EMPTY).append(componentId).append(" = new ArrayList();").append(";\r\n");
        } else if (type.contains(ConstantEnum.MAP)) {
            this.sourceCode.append(type).append(ConstantEnum.EMPTY).append(componentId).append(" = new HashMap();").append(";\r\n");
        } else {
            Logger.warn("genGlobalVariables() skipped type: " + type);
        }
    }

    private void genGlobalVariables() throws Exception {
        this.sourceCode.append("\t//Global Variables definition\r\n");
        JSONArray varDeclarations = (JSONArray) JSONPath.eval(this.scriptJsonObject, "$.seqInfo.seqInfo[seqType = 'VarDeclaration']");
        if (!CollectionUtils.isEmpty(varDeclarations)) {
            List<String> componentIds = new ArrayList<>();
            Iterator it = varDeclarations.iterator();
            while (it.hasNext()) {
                Object varDeclaration = it.next();
                JSONArray jsonArray = (JSONArray) JSONPath.eval(varDeclaration, "$.seqInfo");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    Iterator it2 = jsonArray.iterator();
                    while (it2.hasNext()) {
                        Object obj = it2.next();
                        JSONObject var = (JSONObject) obj;
                        if ("Global".equals(var.getString("Scope"))) {
                            String componentId = var.getString("ComponentId");
                            if (!componentIds.contains(componentId)) {
                                processVarDeclaration(var);
                                componentIds.add(componentId);
                            }
                        }
                    }
                }
            }
        }
        this.sourceCode.append("\r\n");
    }

    private void genModules() throws Exception {
        String moduleName;
        JSONArray jsonArray = (JSONArray) JSONPath.eval(this.scriptJsonObject, "$.seqInfo");
        Iterator it = jsonArray.iterator();
        while (it.hasNext()) {
            Object obj = it.next();
            this.varList.clear();
            boolean moudleFlag = false;
            boolean mainMoudleFlag = false;
            JSONObject seqInfo = (JSONObject) obj;
            String seqType = seqInfo.getString("seqType");
            String respondType = getJavaType(seqInfo.getString("OutPut_var"));
            boolean mainString = false;
            if ("Main".equals(seqType)) {
                moduleName = "mainModule";
                mainMoudleFlag = true;
                if (ConstantEnum.STRING.equals(respondType)) {
                    mainString = true;
                } else {
                    this.mainStringFlag = false;
                }
            } else if ("Final".equals(seqType)) {
                moduleName = "finalModule";
            } else if ("Module".equals(seqType)) {
                moudleFlag = true;
                moduleName = seqInfo.getString("Name");
            } else if (!"Initialize_UI".equals(seqType)) {
                Logger.warn("genModules() skipped seqType: " + seqType);
            }
            this.sourceCode.append("\tpublic ").append(respondType).append(ConstantEnum.EMPTY).append(moduleName).append("(");
            if (moudleFlag || mainMoudleFlag) {
                JSONArray inPutVar = (JSONArray) JSONPath.eval(seqInfo, "$.InPut_var");
                if (!CollectionUtils.isEmpty(inPutVar)) {
                    for (int i = 0; i < inPutVar.size(); i++) {
                        JSONObject var = (JSONObject) inPutVar.get(i);
                        Set<String> keys = var.keySet();
                        if (!CollectionUtils.isEmpty(keys)) {
                            for (String key : keys) {
                                String type = var.get(key).toString();
                                String javaType = getJavaType(type);
                                this.sourceCode.append(javaType).append(ConstantEnum.EMPTY);
                                if (mainMoudleFlag) {
                                    this.moduleArguments.add(getJavaTypeClass(type));
                                }
                                this.sourceCode.append(key);
                                if (i < inPutVar.size() - 1) {
                                    this.sourceCode.append(ConstantEnum.COMMA);
                                }
                            }
                        }
                    }
                }
            }
            this.sourceCode.append(") throws Exception {\n");
            this.sourceCode.append("\t\tLogger.info(\"Enter into ").append(moduleName).append("() ...\");\r\n");
            JSONArray components = (JSONArray) JSONPath.eval(seqInfo, "$.seqInfo");
            Iterator it2 = components.iterator();
            while (it2.hasNext()) {
                Object comObj = it2.next();
                processComponent((JSONObject) comObj);
            }
            this.sourceCode.append("\t\treturn ").append(getDefaultReturn(respondType, mainString)).append(";\r\n");
            this.sourceCode.append("\t}\r\n");
            this.sourceCode.append("\r\n");
        }
    }

    private void processComponent(JSONObject component) throws Exception {
        LOGGER.info("processComponent() componentType: " + component.toString());
        String componentType = component.getString("seqType");
        String seqEnable = component.getString("SeqEnable");
        if (!StringUtils.isBlank(seqEnable) && !ConstantEnum.TRUE.equalsIgnoreCase(seqEnable)) {
            return;
        }
        if (ConstantEnum.CALL_MODULE.equals(componentType)) {
            processCallModule((JSONObject) JSONPath.eval(component, "$.seqInfo.CallModule"));
            return;
        }
        if (ConstantEnum.UDS_DATA.equals(componentType)) {
            processudsData((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.VAR_DECLARATION.equals(componentType)) {
            processVarDeclaration((JSONArray) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.EVAL.equals(componentType)) {
            processEval((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.WHILE.equals(componentType)) {
            processWhile((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.FOR_EACH.equals(componentType)) {
            processForEach((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.GET_SECURITY_KEY.equals(componentType)) {
            processGetSecurityKey((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.VBF_SWDL.equals(componentType)) {
            processvbfSwdl((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.SET_UI.equals(componentType)) {
            processsetUi((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.IF.equals(componentType)) {
            processIf((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.CALL_API.equalsIgnoreCase(componentType)) {
            processCallApi((String) JSONPath.eval(component, "$.seqInfo.ComponentId"), (JSONObject) JSONPath.eval(component, "$.seqInfo.CallAPI"));
            return;
        }
        if (ConstantEnum.SET_JSON_PROPERTY.equals(componentType)) {
            processSetJsonProperty((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.GET_JSON_PROPERTY.equals(componentType)) {
            processGetJsonProperty((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.LOGGER_XML.equals(componentType)) {
            processLoggerXml((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.GET_VBF.equals(componentType)) {
            processGetVbfInfo((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.RUN_SCRIPT.equals(componentType)) {
            processRunScript((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.CONTINUE.equals(componentType)) {
            processContinue((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.BREAK.equals(componentType)) {
            processBreak((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.RETURN.equals(componentType)) {
            processReturn((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.PARALLEL.equals(componentType)) {
            processParallel((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.BASE_UI.equals(componentType)) {
            processBaseUi(component);
            return;
        }
        if (ConstantEnum.STOP.equals(componentType)) {
            processStop((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.TRY.equals(componentType)) {
            processTry((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.CHECK_PRECONDITION.equals(componentType)) {
            processCheckPrecondition((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.SET_SEQ_RESULT.equals(componentType)) {
            processSetSeqResult((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.CALL_JAR.equals(componentType)) {
            processCallJar((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.CALL_DLL.equals(componentType)) {
            processCallDll2((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.SET_ERROR.equals(componentType)) {
            processSetError((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.FOR_EACH_JAVA.equals(componentType)) {
            processForEachJava((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.SLEEP.equals(componentType)) {
            processSleep((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.GET_UI_ATTRIBUTE.equals(componentType)) {
            processGetUiAttribute((JSONObject) JSONPath.eval(component, "$.seqInfo"));
            return;
        }
        if (ConstantEnum.LOG_HTML.equals(componentType)) {
            processLogHtml((JSONObject) JSONPath.eval(component, "$.seqInfo"));
        } else if (ConstantEnum.WIFI_SWITCH.equals(componentType)) {
            processWifiSwitch((JSONObject) JSONPath.eval(component, "$.seqInfo"));
        } else {
            Logger.warn("genModules() skipped componentType: " + componentType);
            Logger.info(JSONPath.eval(component, "$.seqInfo").toString());
        }
    }

    private void processSleep(JSONObject node) throws Exception {
        JSONPath.eval(node, PATH_LOGGER_XML_COMPONENT_ID).toString();
        long timeout = node.getLongValue("Timeout");
        this.sourceCode.append("\t\t         Thread.sleep(").append(timeout).append(");").append("\r\n");
        generateCheck();
    }

    private void processGetUiAttribute(JSONObject node) throws Exception {
        JSONPath.eval(node, PATH_LOGGER_XML_COMPONENT_ID).toString();
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        String args = escapeString(input.toString());
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Output.Respond_info").toString());
        String varDeclarationType = getVarDeclarationType(returnVariableName);
        String respondType = ConstantEnum.STRING;
        if (StringUtils.isNotBlank(varDeclarationType)) {
            respondType = varDeclarationType;
        }
        this.sourceCode.append("\t\t         ").append(returnVariableName).append(" = (").append(respondType).append(") ").append("getUiAttribute(\"").append(quoteVariable(args)).append("\");\r\n");
        generateCheck();
    }

    private void processSetError(JSONObject node) throws Exception {
        String code = quoteVariable(JSONPath.eval(node, "$.Error_info.Error_Code").toString());
        String value = quoteVariable(JSONPath.eval(node, "$.Error_info.Error_Value").toString());
        String ecu = quoteVariable(JSONPath.eval(node, "$.Error_info.Error_ECU").toString());
        String componentId = JSONPath.eval(node, PATH_LOGGER_XML_COMPONENT_ID).toString();
        this.sourceCode.append("setError(\"" + code + "\",").append("\"" + value + "\",").append("\"" + componentId + "\",").append("\"" + ecu + "\"").append(");").append("\r\n");
    }

    private void processCallDll(JSONObject node) throws Exception {
        JSONArray jsonArray;
        String componentId = JSONPath.eval(node, PATH_LOGGER_XML_COMPONENT_ID).toString();
        String dllPath = JSONPath.eval(node, "$.CallJar.Input.DllPath").toString().replace("\\", "/");
        String methodName = JSONPath.eval(node, "$.CallJar.Input.MethodName").toString();
        String evalCode = JSONPath.eval(node, "$.CallJar.Input.eval").toString();
        String resultName = getScriptVariableName(JSONPath.eval(node, "$CallJar.Output.Respond_info").toString());
        String calssName = "calssName" + componentId.replace(ConstantEnum.EMPTY, "");
        if (this.sourceCallDllCode == null) {
            this.sourceCallDllCode = new StringBuffer();
        }
        this.sourceCallDllCode.append(" MyDll ").append(calssName).append(" =Native.load(\"").append(dllPath).append("\", MyDll.class);");
        this.sourceCallDllCode.append(evalCode);
        this.sourceCode.append(resultName).append(" =").append("MyDll.").append(calssName).append(ConstantEnum.POINT).append(methodName).append("(");
        Object eval = JSONPath.eval(node, "$.CallJar.Input.Arguments");
        if (ObjectUtils.isEmpty(eval)) {
            jsonArray = new JSONArray();
        } else {
            jsonArray = (JSONArray) eval;
        }
        int argCount = 0;
        Iterator it = jsonArray.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject jb = (JSONObject) jo;
            Object value = jb.get("Value");
            String arg = value.toString();
            if (isScriptVariable(arg)) {
                value = getScriptVariableName(arg);
            }
            if (argCount > 0) {
                this.sourceCode.append(", " + value);
            } else {
                this.sourceCode.append(value);
            }
            argCount++;
        }
        this.sourceCode.append(");").append("\r\n");
        generateCheck();
    }

    private void processCallDll2(JSONObject node) throws Exception {
        JSONArray jsonArray;
        String componentId = JSONPath.eval(node, PATH_LOGGER_XML_COMPONENT_ID).toString();
        String dllPath = JSONPath.eval(node, "$.CallDLL.Input.DllPath").toString().replace("\\", "/");
        String methodName = JSONPath.eval(node, "$.CallDLL.Input.MethodName").toString();
        String evalCode = JSONPath.eval(node, "$.CallDLL.Input.eval").toString();
        String resultName = getScriptVariableName(JSONPath.eval(node, "$.CallDLL.Output.Respond_info").toString());
        String calssName = "calssName" + componentId.replace(ConstantEnum.EMPTY, "");
        String dllInterFaceName = UUID.randomUUID().toString().replace("-", "") + "dll";
        this.dllInterFaceName = dllInterFaceName;
        if (this.sourceCallDllCode == null) {
            this.sourceCallDllCode = new StringBuffer();
            this.sourceCallDllCode.append("package com.geely.gnds.tester.seq.gen;\n\nimport com.sun.jna.Library;\nimport com.sun.jna.Native;\n").append("public interface ").append(dllInterFaceName).append(" extends Library {\n");
        }
        this.sourceCallDllCode.append(dllInterFaceName + ConstantEnum.EMPTY).append(calssName).append(" =Native.load(\"").append(dllPath).append("\", " + dllInterFaceName + ".class);");
        this.sourceCallDllCode.append(evalCode).append(SystemPropertyUtils.PLACEHOLDER_SUFFIX);
        this.sourceCode.append(resultName).append(" =").append(dllInterFaceName + ConstantEnum.POINT).append(calssName).append(ConstantEnum.POINT).append(methodName).append("(");
        Object eval = JSONPath.eval(node, "$.CallDLL.Input.Arguments");
        if (ObjectUtils.isEmpty(eval)) {
            jsonArray = new JSONArray();
        } else {
            jsonArray = (JSONArray) eval;
        }
        int argCount = 0;
        Iterator it = jsonArray.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject jb = (JSONObject) jo;
            Object value = jb.get("Value");
            String arg = value.toString();
            if (isScriptVariable(arg)) {
                value = getScriptVariableName(arg);
            }
            if (argCount > 0) {
                this.sourceCode.append(", " + value);
            } else {
                this.sourceCode.append(value);
            }
            argCount++;
        }
        this.sourceCode.append(");").append("\r\n");
        generateCheck();
    }

    private void processCallJar(JSONObject node) throws Exception {
        JSONArray jsonArray;
        String componentId = JSONPath.eval(node, PATH_LOGGER_XML_COMPONENT_ID).toString();
        String jarPath = JSONPath.eval(node, "$.CallJar.Input.JarPath").toString().replace("\\", "/");
        String className = JSONPath.eval(node, "$.CallJar.Input.ClassName").toString();
        String methodName = JSONPath.eval(node, "$.CallJar.Input.MethodName").toString();
        String calssName = "calssName" + componentId.replace(ConstantEnum.EMPTY, "");
        String resultName = getScriptVariableName(JSONPath.eval(node, "$CallJar.Output.Respond_info").toString());
        String resultType = JSONPath.eval(node, "$CallJar.Output.Respond_type").toString();
        this.sourceCode.append("Class<?> ").append(calssName).append("=callJar(\"").append(jarPath).append("\",").append("\"" + className + "\"").append(");\n");
        this.sourceCode.append(resultName).append(" =").append("(" + resultType + ")").append(calssName + ".getDeclaredMethod(\"").append(methodName).append("\",");
        Object eval = JSONPath.eval(node, "$.CallJar.Input.Arguments");
        StringBuffer sb = new StringBuffer();
        if (ObjectUtils.isEmpty(eval)) {
            jsonArray = new JSONArray();
        } else {
            jsonArray = (JSONArray) eval;
        }
        int argCount = 0;
        Iterator it = jsonArray.iterator();
        while (it.hasNext()) {
            Object jo = it.next();
            JSONObject jb = (JSONObject) jo;
            String name = jb.getString("Type") + ".class";
            Object value = jb.get("Value");
            String arg = value.toString();
            if (isScriptVariable(arg)) {
                value = getScriptVariableName(arg);
            }
            if (argCount > 0) {
                this.sourceCode.append(", " + name);
                sb.append(", " + value);
            } else {
                this.sourceCode.append(name);
                sb.append(value);
            }
            argCount++;
        }
        this.sourceCode.append(").invoke(").append(calssName).append(".newInstance(),").append(sb).append(");").append("\r\n");
        generateCheck();
    }

    private void processSetSeqResult(JSONObject node) throws Exception {
        String seqResult = node.getString("SeqResult");
        if ("Pass".equals(seqResult)) {
            this.sourceCode.append("setSeqResult(true);").append("\r\n");
            if (this.mainStringFlag) {
                this.sourceCode.append("\t\t").append("if(1==1){ ").append("\r\n");
                this.sourceCode.append("\t\t").append("return \"Pass\";}").append("\r\n");
                return;
            }
            return;
        }
        this.sourceCode.append("setSeqResult(false);").append("\r\n");
        if (this.mainStringFlag) {
            this.sourceCode.append("\t\t").append("if(1==1){ ").append("\r\n");
            this.sourceCode.append("\t\t").append("return \"Fail\";}").append("\r\n");
        }
    }

    private void processStop(JSONObject node) throws Exception {
        generateCheck();
    }

    private void processBaseUi(JSONObject node) throws Exception {
        String json = node.toJSONString();
        if (json.contains("Message_Box")) {
            try {
                Object extract = JSONPath.extract(json, "$.seqInfo.Output.Respond_info");
                if (extract != null) {
                    String s = extract.toString();
                    JSONPath.set(node, "$.seqInfo.Output.Respond_info", s.replaceAll(ConstantEnum.INDEX, "").replaceAll(">", ""));
                }
            } catch (Exception e) {
                LOGGER.info("处理Message_Box失败", e);
            }
        }
        this.sourceCode.append("\t\t\taddSetUi(\"").append(escapeString2(quoteVariable4(node.toJSONString()))).append("\");\r\n");
    }

    private void processGetVbfInfo(JSONObject node) throws Exception {
        this.sourceCode.append("download();");
        this.sourceCode.append("\r\n");
        generateCheck();
    }

    private void genClickEventHandlers() throws Exception {
        List<Map> list = (List) this.readContext.read("$..[?(@.seqType=='Base_UI' && @.seqInfo.Input.ClickEventHandler)]", new Predicate[0]);
        for (Map map : list) {
            String uiName = map.get("Name").toString();
            JSONObject jsonObj = (JSONObject) JSONPath.eval(this.scriptJsonObject, "$.seqInfo.seqInfo[Name = '" + uiName + "'][0]");
            this.sourceCode.append("\tpublic String " + uiName + "_clickEventHandler() throws Exception {\r\n");
            this.sourceCode.append("\t\tLogger.info(\"Enter into ").append(uiName).append("_clickEventHandler() ...\");\r\n");
            String seqType = JSONPath.eval(jsonObj, "$.seqInfo.Input.ClickEventHandler.seqType").toString();
            if (ConstantEnum.CALL_MODULE.equals(seqType)) {
                JSONObject callModuleNode = (JSONObject) JSONPath.eval(jsonObj, "$.seqInfo.Input.ClickEventHandler.seqInfo.CallModule");
                processCallModule(callModuleNode);
            }
            this.sourceCode.append("\t\treturn \"").append("").append("\";\r\n");
            this.sourceCode.append("\t}\r\n");
            this.sourceCode.append("\r\n");
        }
    }

    private String generateJavaString(String className) throws Exception {
        try {
            this.sourceCode = new StringBuffer();
            this.sourceCode.append("package com.geely.gnds.tester.seq.gen;\r\n");
            this.sourceCode.append("\r\n");
            this.sourceCode.append("import com.geely.gnds.tester.seq.Logger;\r\n");
            this.sourceCode.append("import com.geely.gnds.tester.seq.DxSequence;\r\n");
            this.sourceCode.append("import com.alibaba.fastjson.JSONObject;\r\n");
            this.sourceCode.append("import com.alibaba.fastjson.JSONArray;\r\n");
            this.sourceCode.append("import com.alibaba.fastjson.JSONPath;\r\n");
            this.sourceCode.append("import com.alibaba.fastjson.JSON;\r\n");
            this.sourceCode.append("import java.util.concurrent.CountDownLatch;\r\n");
            this.sourceCode.append("import com.geely.gnds.tester.cache.TokenManager;\r\n");
            this.sourceCode.append("import com.geely.gnds.doip.client.exception.CancelByUserException;\r\n");
            this.sourceCode.append("import com.geely.gnds.tester.util.*;\r\n");
            this.sourceCode.append("import com.sun.jna.Library;\r\n");
            this.sourceCode.append("import com.sun.jna.Native;\r\n");
            this.sourceCode.append("import java.util.*;\r\n");
            this.sourceCode.append("import org.slf4j.LoggerFactory;\r\n");
            this.sourceCode.append("\r\n");
            this.sourceCode.append("public class ").append(className).append(" extends DxSequence {\r\n");
            this.sourceCode.append("\r\n");
            this.sourceCode.append("private static final org.slf4j.Logger logger = LoggerFactory.getLogger(\"" + className + "\");\r\n");
            this.sourceCode.append("     boolean MDP=").append(getMdp()).append("; \r\n");
            this.sourceCode.append("     int Timeout=").append(getTimeout()).append("; \r\n");
            genGlobalVariables();
            genModules();
            genClickEventHandlers();
            this.sourceCode.append("}\r\n");
            return this.sourceCode.toString();
        } catch (Exception e) {
            LOGGER.error("解析脚本出错,已解析sourceCode：" + this.sourceCode.toString());
            throw e;
        }
    }

    public DxSequence getSequeneceClass(String seqCode, String version) throws Exception {
        String className = "Seq_" + seqCode.replaceAll("-", "_") + "_" + UUID.randomUUID().toString().replace("-", "");
        String javaCode = generateJavaString(className);
        String name = "com.geely.gnds.tester.seq.gen." + className;
        MemoryClassLoader memoryClassLoader = MemoryClassLoader.genInstance();
        if (this.sourceCallDllCode != null) {
            String dllName = "com.geely.gnds.tester.seq.gen." + this.dllInterFaceName;
            Logger.info("sourceCallDllCode dll代码:" + this.sourceCallDllCode.toString());
            memoryClassLoader.registerJava2(dllName, this.sourceCallDllCode.toString());
        }
        getSourceCodeList(javaCode);
        LOGGER.info("动态编译name:{},javaCode:{}", name, javaCode);
        Class<?> klass = memoryClassLoader.registerJava2(name, javaCode);
        setClassName(klass);
        DxSequence seqObj = (DxSequence) klass.newInstance();
        Method setScriptDocumentMethod = klass.getMethod("setScriptJsonObject", JSONObject.class);
        setScriptDocumentMethod.invoke(seqObj, this.scriptJsonObject);
        CloseUtil.close("SeqClassGenerator类中的getSequeneceClass方法出现异常,异常原因--->{}", memoryClassLoader);
        return seqObj;
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r8v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r8v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r9v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r9v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 8, insn: 0x00cd: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r8 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('stream' java.io.ByteArrayInputStream)]) A[TRY_LEAVE], block:B:42:0x00cd */
    /* JADX WARN: Not initialized variable reg: 9, insn: 0x00d1: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r9 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:44:0x00d1 */
    /* JADX WARN: Type inference failed for: r8v1, names: [stream], types: [java.io.ByteArrayInputStream] */
    /* JADX WARN: Type inference failed for: r9v0, types: [java.lang.Throwable] */
    public void getSourceCodeList(String javaCode) throws Exception {
        try {
            try {
                ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(javaCode.getBytes());
                Throwable th = null;
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(byteArrayInputStream));
                Throwable th2 = null;
                try {
                    try {
                        this.codeList = new LinkedList();
                        while (true) {
                            String line = bufferedReader.readLine();
                            if (line == null) {
                                break;
                            } else {
                                this.codeList.add(line);
                            }
                        }
                        if (bufferedReader != null) {
                            if (0 != 0) {
                                try {
                                    bufferedReader.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                bufferedReader.close();
                            }
                        }
                        if (byteArrayInputStream != null) {
                            if (0 != 0) {
                                try {
                                    byteArrayInputStream.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                byteArrayInputStream.close();
                            }
                        }
                    } catch (Throwable th5) {
                        if (bufferedReader != null) {
                            if (th2 != null) {
                                try {
                                    bufferedReader.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                bufferedReader.close();
                            }
                        }
                        throw th5;
                    }
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            LOGGER.error("生成源代码队列失败", e);
            throw e;
        }
    }

    public String getLineCode(int lineNumber) {
        String code = this.codeList.get(lineNumber - 1);
        LOGGER.info("获取到第{}行的数据{}", Integer.valueOf(lineNumber), code);
        return code;
    }

    private void generateCheck() {
        this.sourceCode.append("if(closeWindowFlag){ throw new CancelByUserException();}").append("\r\n");
    }

    private void generatecheckConnect() {
        this.sourceCode.append("checkConnect();").append("\r\n");
    }

    private void genMethod() {
        if (this.methodSourceCode != null) {
            this.sourceCode.append(this.methodSourceCode);
        }
    }

    private void genCallDll() {
        if (this.sourceCallDllCode != null) {
            this.sourceCode.append("public interface MyDll extends Library {\n").append(this.sourceCallDllCode).append("}\n");
        }
    }

    private void genCallDll2() {
        if (this.sourceCallDllCode != null) {
            this.sourceCode.append("public interface MyDll extends Library {\n").append(this.sourceCallDllCode).append("}\n");
        }
    }

    public static List<String> getChildSeqCodes(String scriptJson) {
        List<String> seqCodes = new ArrayList<>();
        List<Map> list = (List) JsonPath.parse(scriptJson).read("$..[?(@.seqType=='RunScript' && @.seqInfo.RunScript.Input.NevisLink)]", new Predicate[0]);
        for (Map map : list) {
            JSONObject object = JSONObject.parseObject(JSON.toJSONString(map));
            String seqCode = JSONPath.eval(object, "$.seqInfo.RunScript.Input.NevisLink").toString();
            seqCodes.add(seqCode);
        }
        return seqCodes;
    }

    public static void main(String[] args) throws IOException {
        File file = new File("D:\\客户端\\seqs\\GRI-00000001最终.json");
        List<String> childSeqCodes = getChildSeqCodes(FileUtils.readFileToString(file));
        System.out.println(childSeqCodes);
    }

    public String getJavaType(String type) {
        String res = ConstantEnum.STRING;
        if (StringUtils.isBlank(type)) {
            return res;
        }
        if (ConstantEnum.INT.equalsIgnoreCase(type)) {
            res = "int";
        } else if (ConstantEnum.JSON_OBJECT.equalsIgnoreCase(type)) {
            res = ConstantEnum.JSON_OBJECT;
        } else if (ConstantEnum.LIST.equalsIgnoreCase(type)) {
            res = "JSONArray";
        } else if (ConstantEnum.SHORT.equalsIgnoreCase(type)) {
            res = "short";
        } else if (ConstantEnum.LONG.equalsIgnoreCase(type)) {
            res = "long";
        } else if (ConstantEnum.FLOAT.equalsIgnoreCase(type)) {
            res = "float";
        } else if (ConstantEnum.DOUBLE.equalsIgnoreCase(type)) {
            res = "double";
        } else if (ConstantEnum.CHAR.equalsIgnoreCase(type)) {
            res = "char";
        } else if (ConstantEnum.BOOLEAN.equalsIgnoreCase(type)) {
            res = "boolean";
        } else if (ConstantEnum.BYTE.equalsIgnoreCase(type)) {
            res = "byte";
        } else if (type.contains(ConstantEnum.LISTJAVA)) {
            res = ConstantEnum.LIST;
        } else if (type.contains(ConstantEnum.MAP)) {
            res = ConstantEnum.MAP;
        }
        return res;
    }

    public Class getJavaTypeClass(String type) {
        Class res = String.class;
        if (ConstantEnum.INT.equalsIgnoreCase(type)) {
            res = Integer.TYPE;
        } else if (ConstantEnum.JSON_OBJECT.equalsIgnoreCase(type)) {
            res = JSONObject.class;
        } else if (ConstantEnum.LIST.equalsIgnoreCase(type)) {
            res = JSONArray.class;
        } else if (ConstantEnum.SHORT.equalsIgnoreCase(type)) {
            res = Short.TYPE;
        } else if (ConstantEnum.LONG.equalsIgnoreCase(type)) {
            res = Long.TYPE;
        } else if (ConstantEnum.FLOAT.equalsIgnoreCase(type)) {
            res = Float.TYPE;
        } else if (ConstantEnum.DOUBLE.equalsIgnoreCase(type)) {
            res = Double.TYPE;
        } else if (ConstantEnum.CHAR.equalsIgnoreCase(type)) {
            res = Character.TYPE;
        } else if (ConstantEnum.BOOLEAN.equalsIgnoreCase(type)) {
            res = Boolean.TYPE;
        } else if (ConstantEnum.BYTE.equalsIgnoreCase(type)) {
            res = Byte.TYPE;
        } else if (type.contains(ConstantEnum.LISTJAVA)) {
            res = List.class;
        } else if (type.contains(ConstantEnum.MAP)) {
            res = Map.class;
        }
        return res;
    }

    public String getDefaultReturn(String type, boolean mainString) {
        String res = "\"\"";
        if (mainString) {
            res = "\"Pass\"";
        }
        if (StringUtils.isBlank(type)) {
            return res;
        }
        if (ConstantEnum.INT.equalsIgnoreCase(type)) {
            res = "0";
        } else if (ConstantEnum.JSON_OBJECT.equalsIgnoreCase(type)) {
            res = "new JSONObject()";
        } else if (ConstantEnum.LIST.equalsIgnoreCase(type)) {
            res = "new JSONArray()";
        } else if (ConstantEnum.SHORT.equalsIgnoreCase(type)) {
            res = "0";
        } else if (ConstantEnum.LONG.equalsIgnoreCase(type)) {
            res = "0L";
        } else if (ConstantEnum.FLOAT.equalsIgnoreCase(type)) {
            res = "0.0f";
        } else if (ConstantEnum.DOUBLE.equalsIgnoreCase(type)) {
            res = "0.0d";
        } else if (ConstantEnum.CHAR.equalsIgnoreCase(type)) {
            res = "0";
        } else if (ConstantEnum.BOOLEAN.equalsIgnoreCase(type)) {
            res = "false";
        } else if (ConstantEnum.BYTE.equalsIgnoreCase(type)) {
            res = "0";
        } else if (type.contains(ConstantEnum.LISTJAVA)) {
            res = "new ArrayList();";
        } else if (type.contains(ConstantEnum.MAP)) {
            res = "new HashMap<>()";
        }
        return res;
    }

    private void processLogHtml(JSONObject node) throws Exception {
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        String type = input.getString("Type");
        JSONObject value = (JSONObject) JSONPath.eval(input, "$.Value");
        if ("Chapter".equals(type)) {
            String title = value.getString("Title");
            this.sourceCode.append("\t\tlogHtmlChapter(\"").append(quoteVariable(title)).append("\");\r\n");
        } else if ("Table".equals(type)) {
            JSONArray headers = value.getJSONArray("Headers");
            String chapterTitle = value.getString("ChapterTitle");
            String tableTitle = value.getString("TableTitle");
            this.sourceCode.append("\t\tlogHtmlTable(\"").append(quoteVariable(chapterTitle)).append("\",").append("\"").append(quoteVariable(tableTitle)).append("\",").append("JSONArray.parseArray(\"").append(JSON.toJSONString(headers).replaceAll("\"", "\\\\\"")).append("\"));\r\n");
        } else if ("Line".equals(type)) {
            String chapterTitle2 = value.getString("ChapterTitle");
            String tableTitle2 = value.getString("TableTitle");
            String line = value.getString("Line");
            this.sourceCode.append("\t\tlogHtmlLine(\"").append(quoteVariable(chapterTitle2)).append("\",").append("\"").append(quoteVariable(tableTitle2)).append("\",\"").append(quoteVariable(line)).append("\");\r\n");
        } else if ("Row".equals(type)) {
            String chapterTitle3 = value.getString("ChapterTitle");
            String tableTitle3 = value.getString("TableTitle");
            String result = value.getString("Result");
            Object object = value.get("Contents");
            this.sourceCode.append("\t\tlogHtmlRow(\"").append(quoteVariable(chapterTitle3)).append("\",").append("\"").append(quoteVariable(tableTitle3)).append("\",");
            if (object instanceof JSONArray) {
                JSONArray contents = value.getJSONArray("Contents");
                this.sourceCode.append("JSONArray.parseArray(\"").append(JSON.toJSONString(contents).replaceAll("\"", "\\\\\"")).append("\")");
            } else {
                this.sourceCode.append(quoteVariable2(object.toString()));
            }
            this.sourceCode.append(",\"").append(quoteVariable(result)).append("\");\r\n");
        }
        generateCheck();
    }

    private void processWifiSwitch(JSONObject node) throws Exception {
        String returnVariableName = getScriptVariableName(JSONPath.eval(node, "$.Input.Output.Respond_info").toString());
        JSONObject input = (JSONObject) JSONPath.eval(node, "$.Input");
        JSONArray address = input.getJSONArray("ECU_Address");
        String uds = input.getString("UDS_Switch");
        this.sourceCode.append("\t\t").append(returnVariableName).append(" = createServeSocket(JSONArray.parseArray(\"").append(JSON.toJSONString(address).replaceAll("\"", "\\\\\"")).append("\"), \"").append(quoteVariable(uds)).append("\");\r\n");
        generateCheck();
    }
}
