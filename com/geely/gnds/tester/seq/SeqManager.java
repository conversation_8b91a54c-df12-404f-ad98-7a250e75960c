package com.geely.gnds.tester.seq;

import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.dto.InitializeUiDto;
import java.util.Map;
import java.util.Stack;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;

/* loaded from: SeqManager.class */
public class SeqManager {
    private volatile Object lock = new Object();
    private static final SeqManager INSTANCE = new SeqManager();
    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(SeqManager.class);
    private static Map<String, Stack<String>> seqMap = new ConcurrentHashMap();
    private static Map<String, ScriptHandler> handlerMap = new ConcurrentHashMap();
    private static Map<String, String> vbfFlagMap = new ConcurrentHashMap();
    private static Map<String, String> seqContentCacheMap = new ConcurrentHashMap();
    private static Map<String, String> seqVersionCacheMap = new ConcurrentHashMap();

    private SeqManager() {
    }

    public static SeqManager getInstance() {
        return INSTANCE;
    }

    public void addScriptHandler(String key, ScriptHandler handler) {
        handlerMap.put(key, handler);
    }

    public ScriptHandler getScriptHandler(String key) {
        ScriptHandler scriptHandler = handlerMap.get(key);
        return scriptHandler;
    }

    public void addVbfFlag(String vin, String seqCode) {
        vbfFlagMap.put(vin, seqCode);
    }

    public Map<String, String> getVbfFlag() {
        return vbfFlagMap;
    }

    public void removeVbfFlag(String vin) {
        vbfFlagMap.remove(vin);
    }

    public static void cleanSeqMap(String vin) {
        seqMap.remove(vin);
    }

    public void initSeqMap(String vin) {
        Stack<String> stack = new Stack<>();
        seqMap.put(vin, stack);
    }

    public Stack<String> getSeqStack(String vin) {
        Stack<String> stack = seqMap.get(vin);
        return stack;
    }

    public void addSeq(String vin, String seqCode) {
        synchronized (this.lock) {
            Stack<String> stack = seqMap.get(vin);
            if (stack == null) {
                stack = new Stack<>();
            }
            if (stack.contains(seqCode)) {
                stack.remove(seqCode);
            }
            stack.push(seqCode);
            seqMap.put(vin, stack);
        }
    }

    public void removeSeq(String vin, String seqCode) {
        synchronized (this.lock) {
            Stack<String> stack = seqMap.get(vin);
            if (stack != null && stack.contains(seqCode)) {
                stack.remove(seqCode);
            }
        }
    }

    public String getSeq(String vin) {
        synchronized (this.lock) {
            Stack<String> stack = seqMap.get(vin);
            if (stack == null) {
                return "";
            }
            return stack.pop() == null ? "" : stack.pop().toString();
        }
    }

    public void handleException(String vin, String seqCode) {
        ScriptHandler handler = getScriptHandler(vin + seqCode);
        if (handler != null) {
            handler.setCloseWindowFlag(true);
            try {
                handler.notifySeq(new InitializeUiDto());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public Map<String, ScriptHandler> getHandlerMap() {
        return handlerMap;
    }

    public String getSeqContentCache(String seqCode, String version, String language, String type) {
        String key = type + "_" + seqCode + "_" + version + "_" + language;
        if ((!"common".equals(type) || !StringUtils.isBlank(version)) && seqContentCacheMap.containsKey(key)) {
            String content = seqContentCacheMap.get(key);
            if ("nested".equals(type)) {
                seqContentCacheMap.remove(key);
            }
            return content;
        }
        return "";
    }

    public void addSeqContentCache(String seqCode, String version, String language, String type, String seqContent) {
        String key = type + "_" + seqCode + "_" + version + "_" + language;
        if ("common".equals(type) && StringUtils.isBlank(version)) {
            return;
        }
        seqContentCacheMap.put(key, seqContent);
    }

    public String getSeqVersionCache(String seqCode) {
        if (seqVersionCacheMap.containsKey(seqCode)) {
            String version = seqVersionCacheMap.get(seqCode);
            seqVersionCacheMap.remove(seqCode);
            return version;
        }
        return "";
    }

    public void addSeqVersionCache(String seqCode, String version) {
        seqVersionCacheMap.put(seqCode, version);
    }
}
