package com.geely.gnds.tester.seq;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.doip.client.DoipInstructionType;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.tcp.DoipMessageContanier;
import com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.txt.FdTxtLogger;
import com.geely.gnds.tester.cache.DownloadManager;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.VirtualVehicleUtils;
import java.util.Date;
import org.slf4j.LoggerFactory;

/* loaded from: UdsSend.class */
public class UdsSend {
    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(UdsSend.class);
    private SingletonManager manager = SingletonManager.getInstance();
    private DoipUtil doipUtil = DoipUtil.getInstance();

    public String udsData(String targetAdd, String command, String vin) throws Exception {
        LOGGER.info("线程【{}-{}】发送UDS指令：{}", new Object[]{Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName(), command});
        String udsString = getUdsString(targetAdd, command);
        FdTcpClient client = this.manager.getFdTcpClient(vin);
        if (client == null) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115);
            LOGGER.error("{}【{}】", formatMsg, vin);
            throw new DoipException(formatMsg);
        }
        JSONObject inputObj = JSONObject.parseObject(udsString);
        String doipInstructionCode = inputObj.getString("Data");
        String targetAddress = inputObj.getString("Target_address");
        boolean is36 = false;
        String send = doipInstructionCode.length() > 9 ? doipInstructionCode.substring(0, 10) : doipInstructionCode;
        FdTxtLogger fdTxtLogger = client.geTxtLogger();
        if (fdTxtLogger != null) {
            String content = "VehComm  request: Ecu " + targetAddress + ",Message " + doipInstructionCode;
            if (doipInstructionCode.startsWith("36")) {
                is36 = true;
                boolean sub = false;
                if (command.length() > 20) {
                    command = command.substring(0, 20);
                    sub = true;
                }
                content = "VehComm  request: Ecu " + targetAddress + ",Message" + (sub ? "(截取前20位字符)" : ConstantEnum.EMPTY) + command;
            }
            fdTxtLogger.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
        }
        if (client.isVirtualVehicle()) {
            String virtualRes = getVirtualRes(doipInstructionCode.substring(0, 2), targetAddress, doipInstructionCode, vin);
            return virtualRes;
        }
        int targetAddressInt = this.doipUtil.hexString2Int(targetAddress);
        DoipMessageContanier contanier = new DoipMessageContanier();
        contanier.setTxtLogger(client.geTxtLogger());
        try {
            FdDoipTcpReceiveListener listener = client.getIdleReceiveListener();
            listener.setDoipInfo(doipInstructionCode, targetAddressInt, contanier, send, targetAddress, client.isSwitch(targetAddress));
            client.sendDoipMessage(inputObj.getString("Target_address"), doipInstructionCode, listener, (IQopUdsDataQueuer) null, is36);
            DoipException e = listener.getLastDoipException();
            if (e != null) {
                throw e;
            }
            if (contanier.isDiagnosticMessageNegAck()) {
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00161));
            }
            String response = contanier.getResponse(doipInstructionCode.substring(2));
            String strByJsonNodeExpr = ObjectMapperUtils.findStrByJsonNodeExpr(response, "/ECU_Response_value/Data_Value");
            return strByJsonNodeExpr;
        } catch (DoipException e2) {
            FdHelper.getExceptionAsString(e2);
            throw e2;
        }
    }

    private String getVirtualRes(String doipInstructionCode, String ecuAddress, String request, String vin) throws DoipException {
        DoipInstructionType doipInstructionType = this.doipUtil.getDoipInstructionType(doipInstructionCode);
        String posAckCode = doipInstructionType.getPosAckCode();
        String response = DownloadManager.getInstance().i(vin, ecuAddress, request);
        LOGGER.info("虚拟车发送指令【{}-{}】,收到回复【{}】", new Object[]{ecuAddress, request, response});
        return VirtualVehicleUtils.getResponse(response, posAckCode);
    }

    public String getUdsString(String targetAddress, String data) {
        String res = "{\"Source_address\": \"0E80\",\"Target_address\": \"" + targetAddress + "\",\"Data\": \"" + data + "\",\"Retries\": \"0\",\"Delay\": \"500\",\"ErrorHandlingType\": \"2\"}";
        return res;
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x00d8, code lost:
    
        r13 = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.Boolean getSecurityAccess(java.lang.String r8, java.lang.String r9, java.lang.String r10, java.lang.String r11, java.util.List<java.lang.String> r12) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 425
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.seq.UdsSend.getSecurityAccess(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.util.List):java.lang.Boolean");
    }

    private boolean checkFixByte(String fixByte, String platform) {
        if (ConstantEnum.GEEA2.equals(platform)) {
            if (fixByte.length() == 10) {
                return true;
            }
            return false;
        }
        if (ConstantEnum.GEEA3.equals(platform) && fixByte.length() == 32) {
            return true;
        }
        return false;
    }
}
