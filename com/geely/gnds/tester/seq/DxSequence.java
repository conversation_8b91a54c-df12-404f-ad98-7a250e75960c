package com.geely.gnds.tester.seq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.doip.client.DoipInstructionType;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.dro.document.event.DiagnosticReadoutEvent;
import com.geely.gnds.doip.client.dro.document.event.Ecu;
import com.geely.gnds.doip.client.dro.document.event.Item;
import com.geely.gnds.doip.client.dro.document.event.Parameter;
import com.geely.gnds.doip.client.dro.document.event.PartInformation;
import com.geely.gnds.doip.client.dro.document.event.Request;
import com.geely.gnds.doip.client.dro.document.event.SoftwarePartNumbers;
import com.geely.gnds.doip.client.enums.XmlStatusEnum;
import com.geely.gnds.doip.client.exception.CancelByUserException;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.exception.DoipRetryException;
import com.geely.gnds.doip.client.tcp.DoipMessageContanier;
import com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener;
import com.geely.gnds.doip.client.tcp.FdFunctionalAddressingReceiveListener;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.tcp.FdTcpClientServer;
import com.geely.gnds.doip.client.txt.FdTxtLogger;
import com.geely.gnds.doip.client.xml.FdXmlLogger;
import com.geely.gnds.doip.client.xml.XmlAttribute;
import com.geely.gnds.doip.client.xml.XmlBuriedPoint;
import com.geely.gnds.doip.client.xml.XmlFault;
import com.geely.gnds.doip.client.xml.XmlSeq;
import com.geely.gnds.dsa.dto.CalculationEcuDTO;
import com.geely.gnds.dsa.dto.DiaDtcDTO;
import com.geely.gnds.dsa.dto.HtmlChapterDTO;
import com.geely.gnds.dsa.dto.HtmlRowDTO;
import com.geely.gnds.dsa.dto.HtmlTableDTO;
import com.geely.gnds.dsa.dto.RunScriptDTO;
import com.geely.gnds.dsa.dto.TestOrderDTO;
import com.geely.gnds.dsa.dto.TesterOrderParamDTO;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.UserConstants;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.exception.file.Base64Utils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.common.utils.http.NetQualityLevel;
import com.geely.gnds.ruoyi.common.utils.http.NetQualityUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.cache.DownloadManager;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.common.ActionContext;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.common.SleepUtils;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.component.CloudFactory;
import com.geely.gnds.tester.dto.BroadcastVehicleDataDTO;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.dto.DroUdsDto;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.EcuDto;
import com.geely.gnds.tester.dto.EcuPinCodeDto;
import com.geely.gnds.tester.dto.SoftwareDto;
import com.geely.gnds.tester.dto.VdnDataDto;
import com.geely.gnds.tester.dto.xml.SysLogXmlFaultCodesImmediateDTO;
import com.geely.gnds.tester.dto.xml.SysLogXmlImmediateDTO;
import com.geely.gnds.tester.dto.xml.SysLogXmlLoggersImmediateDTO;
import com.geely.gnds.tester.dto.xml.SysRequestDTO;
import com.geely.gnds.tester.entity.TesterReadoutCacheEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.enums.TesterNegativeResEnum;
import com.geely.gnds.tester.enums.TesterSoftwareTypeEnum;
import com.geely.gnds.tester.service.DtcService;
import com.geely.gnds.tester.service.ParamService;
import com.geely.gnds.tester.service.impl.ParamServiceImpl;
import com.geely.gnds.tester.service.impl.ReadoutCacheServiceImpl;
import com.geely.gnds.tester.service.impl.SoftwareServiceImpl;
import com.geely.gnds.tester.socket.GndsWebSocket;
import com.geely.gnds.tester.util.AesUtils;
import com.geely.gnds.tester.util.CommonFunctionsUtils;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.ParseUtils;
import com.geely.gnds.tester.util.SecurityUtils;
import com.geely.gnds.tester.util.StringParseUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterVehicleResponseUtils;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import com.geely.gnds.tester.util.TlsUtils;
import com.geely.gnds.tester.util.VbfParseUtils;
import com.geely.gnds.tester.util.VirtualVehicleUtils;
import java.io.BufferedInputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLClassLoader;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Random;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import org.apache.http.util.TextUtils;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/* loaded from: DxSequence.class */
public class DxSequence implements IQopUdsProcessor {
    private static final String ECU_ADDRESS = "ECU_address";
    private static final String ECU_NAME = "ECU_name";
    private static final String CALLAPI_READDID = "GNDS.API.ReadDID";
    private static final String DID_VALUE_LIST = "DID_value_list";
    private static final String PARAMETER_RESULT_LIST = "Parameter_result_list";
    private static final String VEHICLE_CONFIG = "Vehicle_Config";
    private static final String ERROR_CODE_INFO = "Error_code_info";
    private static final String FUNCTIONAL_ADDRESSING = "1FFF";
    private String userName;
    private String version;
    private List<String> maskCodes;
    private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(DxSequence.class);
    private static final TrustManager[] TRUST_MANAGERS = {new X509TrustManager() { // from class: com.geely.gnds.tester.seq.DxSequence.1
        @Override // javax.net.ssl.X509TrustManager
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }

        @Override // javax.net.ssl.X509TrustManager
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            try {
                chain[0].checkValidity();
            } catch (Exception e) {
                throw new CertificateException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00111));
            }
        }

        @Override // javax.net.ssl.X509TrustManager
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            try {
                chain[0].checkValidity();
            } catch (Exception e) {
                throw new CertificateException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00111));
            }
        }
    }};
    private static String[] VERIFY_HOST_NAME_ARRAY = new String[0];
    private static final HostnameVerifier DO_NOT_VERIFY = (hostname, session) -> {
        return (TextUtils.isEmpty(hostname) || Arrays.asList(VERIFY_HOST_NAME_ARRAY).contains(hostname)) ? false : true;
    };
    static Pattern p = Pattern.compile("(?<!\\w)-?\\w+(\\.\\w+)?");
    private static String hexStr = "0123456789ABCDEF";
    private static String[] binaryArray = {"0000", "0001", "0010", "0011", "0100", "0101", "0110", "0111", "1000", "1001", "1010", "1011", "1100", "1101", "1110", "1111"};
    private ConcurrentLinkedQueue<Object> setUiJson = new ConcurrentLinkedQueue<>();
    private JSONObject scriptJsonObject = null;
    private SingletonManager manager = SingletonManager.getInstance();
    private VbfParseUtils vbfParseUtils = VbfParseUtils.getInstance();
    private Cloud cloud = CloudFactory.getCloud();
    private FileCache fileCache = CloudFactory.getFileCache();
    protected String vin = "";
    protected String initializeArgs = "";
    private boolean uploadFlag = false;
    protected String initializeParams = "";
    protected String initializeUi = StrFormatter.EMPTY_JSON;
    private DoipUtil doipUtil = DoipUtil.getInstance();
    private XmlSeq xmlSeq = null;
    private FdTxtLogger txtLogger = null;
    protected boolean status = true;
    protected String testName = "";
    protected String testId = "";
    protected String testVersion = "";
    private volatile Object lock = new Object();
    private boolean supportQop = false;
    protected String seqCode = "";
    protected String dtcInfo = "DTC_info";
    protected String vehicleReadout = "vehicle_readout";
    private FdXmlLogger xmlLogger = null;
    protected String readoutDd00Globaltime = "Readout_DD00_globalTime";
    private AtomicInteger vbfswdlCount = new AtomicInteger();
    private Boolean isStatusReadout = false;
    protected Boolean closeWindowFlag = false;
    private AtomicInteger reset1082 = new AtomicInteger();
    private Map<String, Map> udsDataMap = new ConcurrentHashMap();
    private boolean mdp = false;
    private int timeout = 60;
    private boolean dro = false;
    private String edr = "";
    private boolean createEdr = false;
    private boolean isDsa = false;
    private Boolean droFather = null;
    private boolean moudleEndFlag = false;
    private boolean isVbfswdl = false;
    private FdDsaLogger fdDsaLogger = null;
    protected Boolean isException = false;
    private String seqCodeFather = "";
    private CopyOnWriteArrayList<FdDoipTcpReceiveListener> listeners = new CopyOnWriteArrayList<>();
    private boolean vbfswdlFail = false;
    private String requestId = "";
    protected String seqName = "";
    private boolean needThrowUserCancelException = true;

    public String getSeqCode() {
        return this.seqCode;
    }

    public int getTimeout() {
        return this.timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

    public boolean isMdp() {
        return this.mdp;
    }

    public void setMdp(boolean mdp) {
        this.mdp = mdp;
    }

    public boolean isCreateEdr() {
        return this.createEdr;
    }

    public void setCreateEdr() {
        this.createEdr = true;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public boolean isDro() {
        return this.dro;
    }

    public void setDro(boolean dro) {
        this.dro = dro;
    }

    public String getEdr() {
        return this.edr;
    }

    public void setEdr(String edr) {
        this.edr = edr;
    }

    public void setDsa(boolean dsa) {
        this.isDsa = dsa;
    }

    public void setMoudleEnd() {
        this.moudleEndFlag = true;
    }

    public Boolean getDroFather() {
        return this.droFather;
    }

    public void setDroFather(Boolean droFather) {
        this.droFather = droFather;
    }

    public Boolean getCloseWindowFlag() {
        return this.closeWindowFlag;
    }

    public void setCloseWindowFlag(Boolean closeWindowFlag) {
        this.closeWindowFlag = closeWindowFlag;
        cancelByUserException();
        if (this.moudleEndFlag) {
            uploadXml();
        }
    }

    public Boolean getStatusReadout() {
        return this.isStatusReadout;
    }

    public void setStatusReadout(Boolean statusReadout) {
        this.isStatusReadout = statusReadout;
    }

    public void setFdDsaLogger(FdDsaLogger fdDsaLogger) {
        this.fdDsaLogger = fdDsaLogger;
    }

    public Boolean getException() {
        return this.isException;
    }

    public void setException(Boolean exception) {
        this.isException = exception;
    }

    public String getSeqCodeFather() {
        return this.seqCodeFather;
    }

    public void setSeqCodeFather(String seqCodeFather) {
        this.seqCodeFather = seqCodeFather;
    }

    public XmlSeq getXmlSeq() {
        return this.xmlSeq;
    }

    public String getRequestId() {
        return this.requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getSeqName() {
        return this.seqName;
    }

    public void setSeqName(String seqName) {
        this.seqName = seqName;
    }

    public String getSeqResult() {
        return getSeqResultFinalString();
    }

    public String getDefaultResult() {
        return "\"Pass\"";
    }

    public void setSeqResult(Boolean seqResult) {
        LOG.info("诊断序列开始设置结果:{}", seqResult);
        if (this.xmlSeq != null) {
            this.xmlSeq.setResultSetBySeq(true);
        }
        if (seqResult.booleanValue()) {
            setSeqResultFinalString(XmlStatusEnum.STATUS_SUCCESS.getValue());
        } else {
            setSeqResultFinalString(XmlStatusEnum.STATUS_FAIL.getValue());
        }
    }

    public void setSeqResult2(Boolean seqResult) {
        if (seqResult.booleanValue()) {
            setSeqResultFinalString(XmlStatusEnum.STATUS_SUCCESS.getValue());
        } else {
            setSeqResultFinalString(XmlStatusEnum.STATUS_FAIL.getValue());
        }
    }

    public Object getGlobal(String name, String path) {
        return "";
    }

    public void setInitializeUi(String path, String value) {
        JSONObject json = JSONObject.parseObject(this.initializeUi);
        if (json == null) {
            json = new JSONObject();
        }
        boolean success = JSONPath.set(json, path, value);
        if (success) {
            this.initializeUi = json.toJSONString();
        }
    }

    public Object getUiAttribute(String param) {
        JSONObject jsonObject;
        LOG.info("诊断序列获取UI值入参param【{}】initializeUi【{}】", param, this.initializeUi);
        String value = "";
        try {
            JSONObject inputObj = JSONObject.parseObject(param);
            String uiName = inputObj.getString("UI_Name");
            String uiKey = inputObj.getString("getValue");
            JSONObject json = JSONObject.parseObject(this.initializeUi);
            if (json != null && json.containsKey(uiName) && (jsonObject = json.getJSONObject(uiName)) != null && jsonObject.containsKey(uiKey)) {
                value = jsonObject.get(uiKey).toString();
            }
        } catch (Exception e) {
            LOG.info("诊断序列获取UI值异常", e);
        }
        return value;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public List<String> getMaskCodes() {
        return this.maskCodes;
    }

    public void setMaskCodes(List<String> maskCodes) {
        this.maskCodes = maskCodes;
    }

    public boolean isNeedThrowUserCancelException() {
        return this.needThrowUserCancelException;
    }

    public void notThrowUserCancelException() {
        this.needThrowUserCancelException = false;
    }

    public void setClickUi(Map<String, Map> initUi) {
        if (!ObjectUtils.isEmpty(initUi)) {
            initUi.forEach((key, value) -> {
                if (!ObjectUtils.isEmpty(value)) {
                    value.forEach((key2, value2) -> {
                        String path = "$." + key + ConstantEnum.POINT + key2;
                        if (value2 instanceof String) {
                            setInitializeUi(path, value2.toString());
                        } else {
                            setInitializeUi(path, JSON.toJSONString(value2));
                        }
                    });
                }
            });
        }
    }

    public Object getGlobal(String name) {
        return this.manager.getGlobal(name, this.vin);
    }

    public String getInitializeParams(String name) {
        Map map = (Map) JSON.parseObject(this.initializeParams, Map.class);
        if (map != null && map.containsKey(name)) {
            return map.get(name).toString();
        }
        return "";
    }

    public void setGlobal(String name, Object value) {
        if (!this.isStatusReadout.booleanValue()) {
            LOG.info("设置全局变量信息------name：{};value:{},vin{}", new Object[]{name, value, this.vin});
        }
        this.manager.setGlobal(name, value, this.vin);
        if (this.vehicleReadout.equals(name)) {
            LOG.info("车辆读取vehicle_readout信息 ********************* 开始缓存");
            String userName = TesterLoginUtils.getLoginUserName();
            TesterReadoutCacheEntity cacheEntity = new TesterReadoutCacheEntity();
            cacheEntity.setVehicleReadout(value.toString());
            cacheEntity.setVin(this.vin);
            cacheEntity.setLang("zh_CN");
            cacheEntity.setCreateBy(userName);
            cacheEntity.setUpdateBy(userName);
            cacheEntity.setCreateTime(new Date());
            cacheEntity.setUpdateTime(new Date());
            try {
                ((ReadoutCacheServiceImpl) SpringUtils.getBean(ReadoutCacheServiceImpl.class)).save(cacheEntity);
                this.manager.setGlobal(userName, cacheEntity.getId(), this.vin);
                LOG.info("车辆读取vehicle_readout信息 ********************* 缓存结束");
            } catch (Exception e) {
                LOG.error("车辆读取vehicle_readout信息时保存缓存数据到本地数据库出错", e);
            }
        }
        if (this.readoutDd00Globaltime.equals(name)) {
            LOG.info("车辆读取DD00信息 ********************* 开始缓存");
            try {
                Object globalTimeObj = this.manager.getGlobal("Readout_DD00_globalTime", this.vin);
                ReadoutCacheServiceImpl bean = (ReadoutCacheServiceImpl) SpringUtils.getBean(ReadoutCacheServiceImpl.class);
                Object existCacheId = this.manager.getGlobal(this.userName, this.vin);
                if (!Objects.isNull(existCacheId)) {
                    long cacheEntityId = ((Long) existCacheId).longValue();
                    TesterReadoutCacheEntity exsit = bean.getById(Long.valueOf(cacheEntityId));
                    if (exsit != null) {
                        exsit.setGlobalTime(ObjectUtils.isEmpty(globalTimeObj) ? null : globalTimeObj.toString());
                        exsit.setCreateGlobalTime(new Date());
                        bean.updateReadoutCache(exsit);
                    }
                }
            } catch (Exception e2) {
                LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00062), e2);
            }
        }
        if (this.dtcInfo.equals(name)) {
            LOG.info("车辆读取DTC信息 ********************* 开始缓存");
            String userName2 = TesterLoginUtils.getLoginUserName();
            TesterReadoutCacheEntity cacheEntity2 = new TesterReadoutCacheEntity();
            cacheEntity2.setDtcInfo(value.toString());
            cacheEntity2.setVin(this.vin);
            cacheEntity2.setLang("zh_CN");
            cacheEntity2.setCreateBy(userName2);
            cacheEntity2.setUpdateBy(userName2);
            cacheEntity2.setCreateTime(new Date());
            cacheEntity2.setUpdateTime(new Date());
            try {
                ReadoutCacheServiceImpl bean2 = (ReadoutCacheServiceImpl) SpringUtils.getBean(ReadoutCacheServiceImpl.class);
                Object existCacheId2 = this.manager.getGlobal(userName2, this.vin);
                if (!Objects.isNull(existCacheId2)) {
                    long cacheEntityId2 = ((Long) existCacheId2).longValue();
                    TesterReadoutCacheEntity exsit2 = bean2.getById(Long.valueOf(cacheEntityId2));
                    if (exsit2 != null) {
                        cacheEntity2.setCacheTime(exsit2.getCacheTime());
                        cacheEntity2.setGlobalTime(exsit2.getGlobalTime());
                        cacheEntity2.setConfirmedDtcList(exsit2.getConfirmedDtcList());
                        cacheEntity2.setCreateGlobalTime(exsit2.getCreateGlobalTime());
                        cacheEntity2.setUnConfirmedDtcList(exsit2.getUnConfirmedDtcList());
                        cacheEntity2.setVehicleConfig(exsit2.getVehicleConfig());
                        cacheEntity2.setVehicleReadout(exsit2.getVehicleReadout());
                    }
                }
                bean2.save(cacheEntity2);
                LOG.info("车辆读取DTC信息 ********************* 缓存结束");
            } catch (Exception e3) {
                LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00062), e3);
            }
        }
        if (VEHICLE_CONFIG.equals(name)) {
            LOG.info("车辆读取Vehicle_Config信息 ********************* 开始缓存");
            String userName3 = TesterLoginUtils.getLoginUserName();
            TesterReadoutCacheEntity cacheEntity3 = new TesterReadoutCacheEntity();
            cacheEntity3.setVehicleConfig(value.toString());
            cacheEntity3.setVin(this.vin);
            cacheEntity3.setLang("zh_CN");
            cacheEntity3.setCreateBy(userName3);
            cacheEntity3.setUpdateBy(userName3);
            cacheEntity3.setCreateTime(new Date());
            cacheEntity3.setUpdateTime(new Date());
            try {
                ((ReadoutCacheServiceImpl) SpringUtils.getBean(ReadoutCacheServiceImpl.class)).saveOrUpdateVehicleConfig(cacheEntity3);
                LOG.info("车辆读取Vehicle_Config信息 ********************* 缓存结束,Vehicle_Config={}", cacheEntity3);
            } catch (Exception e4) {
                LOG.error("车辆读取Vehicle_Config信息时保存缓存数据到本地数据库出错", e4);
            }
        }
    }

    protected final void fail() {
        this.status = false;
        if (this.xmlSeq != null) {
            this.xmlSeq.fail();
        }
    }

    protected final void enableQop() {
        this.supportQop = true;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public String getVin() {
        return this.vin;
    }

    public void setInitializeArgs(String initializeArgs) {
        this.initializeArgs = initializeArgs;
    }

    public void setInitializeParams(String initializeParams, boolean dro) {
        this.initializeParams = initializeParams;
        try {
            setDro(dro);
            JSONObject jsonObject = JSONObject.parseObject(initializeParams);
            String seqCodeFather = jsonObject.getString("seqCodeFather");
            setSeqCodeFather(seqCodeFather);
            String droFather = jsonObject.getString("DRO");
            if (StringUtils.isBlank(droFather)) {
                this.droFather = null;
            } else if (ConstantEnum.TRUE.equalsIgnoreCase(droFather)) {
                setDro(true);
                this.droFather = true;
            } else {
                this.droFather = false;
            }
        } catch (Exception e) {
            LOG.error("DRO字段处理失败");
        }
    }

    public JSONObject getScriptJsonObject() {
        return this.scriptJsonObject;
    }

    public void setScriptJsonObject(JSONObject scriptJsonObject) {
        this.scriptJsonObject = scriptJsonObject;
        this.testName = scriptJsonObject.getString("Name");
        this.testId = scriptJsonObject.getString("GRINumber");
        this.testVersion = scriptJsonObject.getString("Version");
    }

    protected void finalize() throws Exception {
        super.finalize();
        finalModule();
    }

    public void addSetUi(Object obj) {
        LOG.info("线程【{}】->诊断序列UI数据SetUi数据：{}", Thread.currentThread().getName(), obj);
        if (obj instanceof String) {
            saveSetUi((String) obj);
        } else {
            saveSetUi(JSON.toJSONString(obj));
        }
        String json = obj.toString();
        if (json.contains("Auto_Close") && json.contains(ConstantEnum.TRUE)) {
            createEdr();
            notThrowUserCancelException();
            setCloseWindowFlag(true);
        }
        this.setUiJson.add(obj);
    }

    public void createEdr() {
        if (StringUtils.isNotBlank(getEdr())) {
            try {
                if (!isCreateEdr()) {
                    getFdTcpClient().getXmlLogger().a(getXmlSeq(), getEdr());
                    setCreateEdr();
                }
            } catch (Exception e) {
                LOG.error("生成EDR日志失败", e);
            }
        }
    }

    public void setUiException(String method, String msg) {
        Map<String, String> map = new HashMap<>();
        map.put("Exception", method);
        if (msg == null) {
            msg = "";
        }
        TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(msg);
        if (enumByCodeOrStr == null) {
            TesterErrorCodeEnum enumByCodeOrStr2 = TesterErrorCodeEnum.SG00255;
            msg = TesterErrorCodeEnum.formatMsg(enumByCodeOrStr2) + "：" + msg;
        }
        ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
        map.put("Message", this.cloud.aE(msg));
        map.put("SeqCode", this.seqCode);
        map.put("SeqName", this.seqName);
        map.put("Version", this.version);
        String json = JSON.toJSONString(map);
        LOG.info("setUiException报错信息{}", json);
        addSetUi(json);
    }

    public String pollSetUi() {
        int size = this.setUiJson.size();
        if (size > 0) {
            StringBuffer sb = new StringBuffer();
            sb.append("{\"Set_UI\":[");
            sb.append(this.setUiJson.poll());
            while (true) {
                size--;
                if (size > 0) {
                    sb.append(ConstantEnum.COMMA);
                    sb.append(this.setUiJson.poll());
                } else {
                    sb.append("]}");
                    return sb.toString();
                }
            }
        } else {
            return null;
        }
    }

    public String initializeUiModule() throws Exception {
        Object obj = JSONPath.eval(this.scriptJsonObject, "$.seqInfo[seqType = 'Initialize_UI'][0]");
        if (obj == null) {
            return "";
        }
        saveInitializeUi(obj.toString());
        return handleMediaDataForInit(obj.toString());
    }

    public boolean hasWindow() {
        boolean hasWindow = true;
        try {
            Object init = JSONPath.eval(this.scriptJsonObject, "$.seqInfo[seqType = 'Initialize_UI'][0]");
            if (init == null) {
                hasWindow = false;
            } else {
                JSONArray jsonArray = (JSONArray) JSONPath.eval(init.toString(), "$.seqInfo");
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    Iterator it = jsonArray.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            break;
                        }
                        Object obj = it.next();
                        JSONObject jsonObject = JSONObject.parseObject(obj.toString());
                        Object componentType = JSONPath.eval(jsonObject, "$.seqInfo.ComponentType");
                        if (!ObjectUtils.isEmpty(componentType) && "Canvas".equalsIgnoreCase(componentType.toString())) {
                            Object eval = JSONPath.eval(jsonObject, "$.seqInfo.Input.Visible");
                            if (!ObjectUtils.isEmpty(eval) && ConstantEnum.FALSE.equalsIgnoreCase(eval.toString())) {
                                hasWindow = false;
                                break;
                            }
                        }
                    }
                } else {
                    hasWindow = false;
                }
            }
        } catch (Exception e) {
            LOG.error("判断诊断序列是否有弹窗失败", e);
        }
        return hasWindow;
    }

    public void saveInitializeUi(String s) {
        try {
            JSONArray jsonArray = (JSONArray) JSONPath.eval(s, "$.seqInfo");
            if (!CollectionUtils.isEmpty(jsonArray)) {
                Iterator it = jsonArray.iterator();
                while (it.hasNext()) {
                    Object obj = it.next();
                    JSONObject jsonObject = JSONObject.parseObject(obj.toString());
                    String name = jsonObject.getString("Name");
                    Object eval = JSONPath.eval(jsonObject, "$.seqInfo.Input");
                    if (StringUtils.isNotBlank(name) && !ObjectUtils.isEmpty(eval)) {
                        JSONObject input = JSONObject.parseObject(JSON.toJSONString(eval));
                        input.forEach((key, value) -> {
                            String path = "$." + name + ConstantEnum.POINT + key;
                            setInitializeUi(path, value.toString());
                        });
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("保存初始化UI数据失败", e);
        }
    }

    public void saveSetUi(String s) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(s);
            if (!ObjectUtils.isEmpty(jsonObject)) {
                Object uiName = JSONPath.eval(jsonObject, "$.Input.UI_Name");
                Object setValue = JSONPath.eval(jsonObject, "$.Input.SetValue");
                if (!ObjectUtils.isEmpty(uiName) && !ObjectUtils.isEmpty(setValue)) {
                    String name = uiName.toString();
                    JSONObject input = JSONObject.parseObject(JSON.toJSONString(setValue));
                    input.forEach((key, value) -> {
                        String path = "$." + name + ConstantEnum.POINT + key;
                        setInitializeUi(path, value.toString());
                    });
                }
            }
        } catch (JSONException e) {
            LOG.error("JSON解析出错：" + s);
        }
    }

    public void setXmlLogger(FdXmlLogger xmlLogger) {
        this.xmlLogger = xmlLogger;
        if (xmlLogger != null && !this.isStatusReadout.booleanValue()) {
            this.xmlSeq = new XmlSeq(this.testName, new Date(), this.testId, StringUtils.defaultString(this.version));
            xmlLogger.a(this.xmlSeq);
        } else {
            this.xmlSeq = null;
        }
    }

    public void setTxtLogger(FdTxtLogger txtLogger) {
        this.txtLogger = txtLogger;
    }

    public String finalModule() throws Exception {
        Logger.info("Enter into FinalModule() ...");
        return "";
    }

    public String subSequenceNameTest(String str) throws Exception {
        Logger.info("Enter into Sub_Sequence_Name_test() ...");
        Logger.info("Hello, " + str + ", this is " + getClass().getName() + ConstantEnum.POINT);
        return "";
    }

    protected String setJsonProperty(String rootString, String path, String value) throws Exception {
        LOG.debug("setJsonProperty的path：{}", path);
        if (path.contains(ConstantEnum.EMPTY)) {
            path = path.replace(ConstantEnum.EMPTY, "");
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00063);
            LOG.error("{}：{}", formatMsg, path);
        }
        JSONObject json = JSONObject.parseObject(rootString);
        if (json == null) {
            json = new JSONObject();
        }
        if (path.contains(ConstantEnum.INFO_1904) || path.contains(ConstantEnum.INFO_1906)) {
            String temp = path.substring(0, path.lastIndexOf(ConstantEnum.POINT));
            JSONObject jo = new JSONObject();
            if (JSONPath.eval(json, temp) != null) {
                jo = (JSONObject) JSONPath.read(rootString, temp);
            }
            jo.put(path.substring(path.lastIndexOf(ConstantEnum.POINT) + 1), value);
            JSONPath.set(json, temp, jo);
        } else if (!JSONPath.set(json, path, value)) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00114));
        }
        return json.toJSONString();
    }

    protected String getJsonProperty(String rootString, String path) throws Exception {
        JSONObject json = JSONObject.parseObject(rootString);
        if (json == null) {
            String errorMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00064);
            LOG.error(errorMsg);
            throw new Exception(errorMsg);
        }
        Object result = JSONPath.eval(rootString, path);
        return result == null ? "" : result.toString();
    }

    protected String getJsonProperty(Object rootString, String path) throws Exception {
        JSONObject json = JSONObject.parseObject(JSON.toJSONString(rootString));
        if (json == null) {
            String errorMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00064);
            LOG.error(errorMsg);
            throw new Exception(errorMsg);
        }
        Object result = JSONPath.eval(rootString, path);
        return result == null ? "" : result.toString();
    }

    protected JSONObject toJson(String jsonString) throws Exception {
        return JSONObject.parseObject(jsonString);
    }

    public String udsData(String inputJsonStr) throws Exception {
        return udsData(inputJsonStr, null);
    }

    @Override // com.geely.gnds.tester.seq.IQopUdsProcessor
    public String udsData(String inputJsonStr, IQopUdsDataQueuer queuer) throws Exception {
        return udsData(inputJsonStr, queuer, true);
    }

    @Override // com.geely.gnds.tester.seq.IQopUdsProcessor
    public String udsData(String inputJsonStr, IQopUdsDataQueuer queuer, boolean logXml) throws Exception {
        String response;
        if (this.xmlSeq != null) {
            String status = this.xmlSeq.getStatus();
            if (XmlStatusEnum.NOT_STARTED.getValue().equalsIgnoreCase(status)) {
                setSeqResultFinalString(XmlStatusEnum.STATUS_SUCCESS.getValue());
            }
        }
        FdTcpClient client = this.manager.getFdTcpClient(this.vin);
        if (client == null) {
            throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115) + this.vin);
        }
        boolean is36 = false;
        JSONObject inputObj = JSONObject.parseObject(inputJsonStr);
        String doipInstructionCode = inputObj.getString("Data");
        String udsData = doipInstructionCode;
        String udsDataInfoLog = doipInstructionCode;
        if (udsDataInfoLog.startsWith("36")) {
            udsDataInfoLog = doipInstructionCode.substring(0, 4);
        }
        LOG.info("线程【{}-{}】{}发送udsData：【{}】", new Object[]{Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName(), this.vin, udsDataInfoLog});
        String targetAddress = inputObj.getString("Target_address");
        if (this.txtLogger != null) {
            String content = "VehComm  request: Ecu " + targetAddress + ",Message " + udsData;
            if (doipInstructionCode.startsWith("36")) {
                is36 = true;
                boolean sub = false;
                if (udsData.length() > 20) {
                    udsData = udsData.substring(0, 20);
                    sub = true;
                }
                content = "VehComm  request: Ecu " + targetAddress + ",Message" + (sub ? "(截取前20位字符)" : ConstantEnum.EMPTY) + udsData;
            }
            this.txtLogger.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
        }
        String finalCommand = DoipUtil.getDsaLogCommand(doipInstructionCode);
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
            logger.write(DsaLogTypeEnum.COMMENT.getValue(), "# Sending Request：Tester -> " + targetAddress + ConstantEnum.EMPTY + finalCommand);
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
        });
        long start = System.currentTimeMillis();
        int errorHandlingType = 2;
        String type = inputObj.getString("ErrorHandlingType");
        int retries = Integer.parseInt(inputObj.getString("Retries"));
        int delay = Integer.parseInt(inputObj.getString("Delay"));
        if (StringUtils.isNotBlank(type)) {
            errorHandlingType = Integer.parseInt(type);
        }
        int pClientVehicleMax = 8000;
        int p2ServerMax = 2000;
        int p4ServerMax = 1000000;
        int p2ClientMax = 6000;
        String pClient2 = inputObj.getString("P_Client2_VehicleMax");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(pClient2)) {
            pClientVehicleMax = Integer.parseInt(pClient2);
        }
        String p2Max = inputObj.getString("P2ServerMax");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(p2Max)) {
            p2ServerMax = Integer.parseInt(p2Max);
        }
        String p4Max = inputObj.getString("P4ServerMax");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(p4Max)) {
            p4ServerMax = Integer.parseInt(p4Max);
        }
        String p2Client = inputObj.getString("P2s_ClientMax");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(p2Client)) {
            p2ClientMax = Integer.parseInt(p2Client);
        }
        int targetAddressInt = this.doipUtil.hexString2Int(targetAddress);
        boolean needRetries = true;
        String send = doipInstructionCode.length() > 30 ? doipInstructionCode.substring(0, 31) : doipInstructionCode;
        if (client.isVirtualVehicle()) {
            String virtualRes = getVirtualRes(doipInstructionCode.substring(0, 2), targetAddress, doipInstructionCode);
            return virtualRes;
        }
        String send2 = doipInstructionCode.length() > 9 ? doipInstructionCode.substring(0, 10) : doipInstructionCode;
        while (true) {
            if (retries >= 0 || needRetries) {
                needRetries = false;
                DoipMessageContanier contanier = new DoipMessageContanier();
                contanier.setTxtLogger(this.txtLogger);
                try {
                    if ("1FFF".equalsIgnoreCase(targetAddress)) {
                        client.sendDoipMessage(targetAddress, doipInstructionCode, new FdFunctionalAddressingReceiveListener(doipInstructionCode, targetAddressInt, contanier, pClientVehicleMax, p2ServerMax, p4ServerMax, p2ClientMax), queuer, false);
                        if (org.apache.commons.lang.StringUtils.isBlank(contanier.getFunctionalAddressingMessages())) {
                            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                                logger2.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "No ECU Responding");
                                logger2.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
                            });
                        }
                        return contanier.getFunctionalAddressingResponse();
                    }
                    String udsType = doipInstructionCode.substring(0, 2);
                    FdDoipTcpReceiveListener listener = client.getIdleReceiveListener();
                    if (this.fdDsaLogger != null) {
                        listener.setFdDsaLogger(this.fdDsaLogger);
                        this.fdDsaLogger.write(DsaLogTypeEnum.SEND_UDS.getValue(), "Tester -> " + targetAddress + ConstantEnum.EMPTY + finalCommand);
                    }
                    this.listeners.add(listener);
                    FdTcpClient fdTcpClient = getFdTcpClient();
                    boolean isSwitch = fdTcpClient.isSwitch(targetAddress);
                    if ("36".equalsIgnoreCase(udsType)) {
                        listener.setDoipInfo(doipInstructionCode, targetAddressInt, contanier, send2, pClientVehicleMax, p2ServerMax, p4ServerMax, targetAddress, true, p2ClientMax, isSwitch);
                    } else {
                        listener.setDoipInfo(doipInstructionCode, targetAddressInt, contanier, send2, pClientVehicleMax, p2ServerMax, p4ServerMax, targetAddress, false, p2ClientMax, isSwitch);
                    }
                    client.sendDoipMessage(targetAddress, doipInstructionCode, listener, queuer, is36);
                    LOG.info("线程【{}-{}】{}等待udsData：【{}】结束", new Object[]{Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName(), this.vin, udsDataInfoLog});
                    this.listeners.remove(listener);
                    String received = contanier.getXmlMessageString();
                    DoipException e = listener.getLastDoipException();
                    response = contanier.getResponse(udsType);
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                        long end = System.currentTimeMillis();
                        if (e == null) {
                            String strByJsonNodeExpr = ObjectMapperUtils.findStrByJsonNodeExpr(response, "/ECU_Response_value/Data_Value");
                            logger3.write(DsaLogTypeEnum.SEND_UDS.getValue(), "Complete Response：" + targetAddress + ConstantEnum.EMPTY + DoipUtil.getDsaLogCommand(strByJsonNodeExpr));
                        }
                        logger3.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "Time stamp：" + end);
                        logger3.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "Time between request and response(P2 time)：" + (end - start) + " ms");
                        logger3.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
                    });
                    if (e != null) {
                        TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(e.getMessage());
                        if (enumByCodeOrStr == null) {
                            TesterErrorCodeEnum enumByCodeOrStr2 = TesterErrorCodeEnum.SG00152;
                            if (logXml) {
                                if (errorHandlingType == 0) {
                                    logMasked(false, enumByCodeOrStr2.code(), TesterErrorCodeEnum.formatMsg(enumByCodeOrStr2) + e.getMessage(), "", send, received, targetAddress);
                                } else {
                                    logFault(false, enumByCodeOrStr2.code(), TesterErrorCodeEnum.formatMsg(enumByCodeOrStr2) + e.getMessage(), "", send, received, targetAddress);
                                }
                            }
                        } else if (logXml) {
                            if (errorHandlingType == 0) {
                                logMasked(false, enumByCodeOrStr.code(), e.getMessage(), "", send, received, targetAddress);
                            } else {
                                logFault(false, enumByCodeOrStr.code(), e.getMessage(), "", send, received, targetAddress);
                            }
                        }
                        if (e instanceof CancelByUserException) {
                        }
                        if (e instanceof DoipRetryException) {
                        }
                        throw e;
                    }
                    if (contanier.isDiagnosticMessageNegAck()) {
                        throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00161));
                    }
                    retries = -1;
                    try {
                        if (!isDro()) {
                            break;
                        }
                        String resUds = JSONPath.eval(response, "$.ECU_Response_value.Data_Value").toString();
                        DroUdsDto droUdsDto = new DroUdsDto(doipInstructionCode, targetAddress, resUds, udsType);
                        client.addDroUds(droUdsDto);
                        break;
                    } catch (Exception e2) {
                        LOG.error("生成DRO方法continueRecordDro报错", e2);
                    }
                } catch (CancelByUserException e3) {
                    throw e3;
                } catch (DoipException e4) {
                    LOG.warn("发送uds指令异常{}", e4.getMessage());
                    if (0 != 0) {
                        Thread.sleep(150L);
                    } else if (retries == 0) {
                        if (errorHandlingType == ConstantEnum.TWO.intValue()) {
                            if (retries == 0) {
                                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger4 -> {
                                    logger4.write(DsaLogTypeEnum.ERROR.getValue(), e4.getMessage());
                                });
                                throw e4;
                            }
                        } else {
                            if (errorHandlingType == 1) {
                                ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
                                addSetUi("{\"Exception\":\"Main\",\"Message\":\"" + this.cloud.aE(e4.getMessage()) + "\"}");
                                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger5 -> {
                                    logger5.write(DsaLogTypeEnum.ERROR.getValue(), e4.getMessage());
                                });
                                return getNegRes(e4.getMessage());
                            }
                            return getNegRes(e4.getMessage());
                        }
                    } else {
                        Thread.sleep(delay);
                        retries--;
                    }
                }
            } else {
                return "";
            }
        }
        return response;
    }

    public void logSeqFaultResult(String msg, TesterErrorCodeEnum enumByCode) {
        TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(msg);
        if (enumByCodeOrStr == null) {
            logFault(false, enumByCode.code(), TesterErrorCodeEnum.formatMsg(enumByCode) + msg, "", "", "", null);
        } else {
            logFault(false, enumByCodeOrStr.code(), msg, "", "", "", null);
        }
    }

    public Ecu continueRecordDro(String diagnosisRequest, String ecuAddress, String unparsedResponse, String udsType, Map<String, JSONArray> map, FdTcpClient fdTcpClient) {
        BroadcastVehicleDataDTO broadcasts;
        int i;
        Ecu ecu = new Ecu();
        try {
            String data = this.manager.getGlobal(GlobalVariableEnum.lY, this.vin).toString();
            broadcasts = (BroadcastVehicleDataDTO) ObjectMapperUtils.jsonStr2Clazz(data, BroadcastVehicleDataDTO.class);
        } catch (Exception e) {
            LOG.error("dro生成continueRecordDro方法异常", e);
        }
        if (Objects.isNull(broadcasts)) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00118));
            return ecu;
        }
        List<EcuBroadcastDto> ecus = broadcasts.getEcus();
        if (CollectionUtils.isEmpty(ecus)) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00177));
            return ecu;
        }
        List<EcuBroadcastDto> ecuBroadcastDtoList = (List) ecus.stream().filter(ecuBroadcastDto -> {
            return Objects.equals(ecuBroadcastDto.getEcuAddress(), ecuAddress);
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ecuBroadcastDtoList)) {
            LOG.error("vin为" + this.vin + "的broadcasts信息中未查到" + ecuAddress + "对应的ecu信息");
            return ecu;
        }
        EcuBroadcastDto ecuBroadcastDto2 = ecuBroadcastDtoList.get(0);
        String ecuName = ecuBroadcastDto2.getEcuName();
        ecu.setShortName(ecuName);
        ecu.setLongName(ecuBroadcastDto2.getEcuFullName());
        ecu.setAddress(ecuAddress);
        PartInformation partInformation = new PartInformation();
        partInformation.setHardwarePartNumber(StringParseUtils.fillBlank(ecuBroadcastDto2.getHardwarePartNumber(), 3));
        String diagnosisPartNumber = ecuBroadcastDto2.getDiagnosticPartNumber() + ecuBroadcastDto2.getDiagnosticPartVersion();
        partInformation.setDiagnosticPartNumber(StringParseUtils.fillBlank(diagnosisPartNumber, 3));
        partInformation.setSerialNumber(ecuBroadcastDto2.getHardwareSerialNumber());
        SoftwarePartNumbers softwarePartNumbers = new SoftwarePartNumbers();
        List<String> softwarePartNumberList = new ArrayList<>();
        ecuBroadcastDto2.getSoftwarePartNumbers().forEach(numbers -> {
            softwarePartNumberList.add(StringParseUtils.fillBlank(numbers, 3));
        });
        softwarePartNumbers.setSoftwarePartNumber(softwarePartNumberList);
        partInformation.setSoftwarePartNumbers(softwarePartNumbers);
        Set<Request> requests = new LinkedHashSet<>();
        Request request = new Request();
        new AtomicBoolean(false);
        request.setUdsType(udsType);
        request.setDiagnosticRequest(diagnosisRequest);
        request.setUnparsedResponse(unparsedResponse);
        if (StrUtil.isNotBlank(unparsedResponse) && unparsedResponse.length() <= 6) {
            request.setHasParsedData(false);
        }
        handle19Mark(request, diagnosisRequest, Constants.S_1902);
        handle19Mark(request, diagnosisRequest, Constants.S_1903);
        handle19Mark(request, diagnosisRequest, Constants.S_1904);
        handle19Mark(request, diagnosisRequest, Constants.S_1906);
        if ("19".equals(udsType) && StrUtil.isNotBlank(unparsedResponse) && unparsedResponse.length() > 6) {
            if (Objects.equals(request.getCategory19(), Constants.S_1902) || Objects.equals(request.getCategory19(), Constants.S_1903)) {
                if (Objects.equals(request.getCategory19(), Constants.S_1902)) {
                    i = 6;
                } else {
                    i = 4;
                }
                String subResponse = unparsedResponse.substring(i);
                Set<String> responseSplit = StringParseUtils.getStrListLimitLength(subResponse, 8);
                request.setResponseSplitData(responseSplit);
            }
            if (Objects.equals(request.getCategory19(), Constants.S_1904) || Objects.equals(request.getCategory19(), Constants.S_1906)) {
                String subResponse2 = unparsedResponse.substring(4);
                HashSet<String> set = new HashSet<>(1);
                set.add(subResponse2);
                request.setResponseSplitData(set);
            }
        }
        List<Item> items = new ArrayList<>();
        if ("22".equals(udsType) && request.getHasParsedData().booleanValue()) {
            String did = diagnosisRequest.substring(2);
            request.setDid(did);
            ParamService paramService = (ParamService) SpringUtils.getBean(ParamService.class);
            JSONArray jsonArray = new JSONArray();
            if (map.containsKey(ecuAddress)) {
                jsonArray = map.get(ecuAddress);
            } else {
                jsonArray.add(did);
            }
            List<DiagResItemGroupDto> diagResItemGroupDtos = paramService.list2(this.vin, diagnosisPartNumber, ecuName, jsonArray);
            if (!CollectionUtils.isEmpty(diagResItemGroupDtos)) {
                List<DiagResItemGroupDto> resItemGroupDtos = (List) diagResItemGroupDtos.stream().filter(itemGroupDto -> {
                    return Objects.equals(itemGroupDto.getDataIdentifierId(), did);
                }).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(resItemGroupDtos)) {
                    Item item = new Item();
                    List<Parameter> parameters = new ArrayList<>();
                    for (DiagResItemGroupDto resItemGroupDto : resItemGroupDtos) {
                        Optional.of(resItemGroupDto).ifPresent(itemGroupDto2 -> {
                            item.setName(resItemGroupDto.getName());
                            item.setValue(resItemGroupDto.getDataIdentifierId());
                            item.setDataType("0");
                            List<DiagResItemDto> responseItemDtoList = itemGroupDto2.getResponseItemDtoList();
                            if (!CollectionUtils.isEmpty(responseItemDtoList)) {
                                List<DiagResItemParseResultDto> result = new ArrayList<>();
                                List<String> didiList = new ArrayList<>();
                                didiList.add(did);
                                List<DiagResItemGroupDto> resItemGroupDtos2 = new ArrayList<>();
                                resItemGroupDtos2.add(resItemGroupDto);
                                paramService.parseResponseByDid2(this.vin, result, resItemGroupDtos2, unparsedResponse, didiList);
                                result.forEach(res -> {
                                    Parameter parameter = new Parameter();
                                    String name = res.getOriginalName();
                                    if (null != res.getDmeName() && StringUtils.isNotBlank(res.getDmeName())) {
                                        parameter.setName(res.getDmeName());
                                    } else {
                                        parameter.setName(res.getOriginalName());
                                    }
                                    String vale = StrUtil.blankToDefault(res.getValue(), "");
                                    String valeTrunk = vale;
                                    if ("DD00".equalsIgnoreCase(did)) {
                                        if (StringUtils.isNotBlank(vale)) {
                                            valeTrunk = String.valueOf(Double.valueOf(vale).intValue());
                                        }
                                        DiagnosticReadoutEvent diagnosticReadoutEvent = fdTcpClient.getDiagnosticReadoutEvent();
                                        diagnosticReadoutEvent.setVehicleGlobalTime(valeTrunk);
                                        fdTcpClient.setDiagnosticReadoutEvent(diagnosticReadoutEvent);
                                    }
                                    if ("DD01".equalsIgnoreCase(did) && "Total Distance".equalsIgnoreCase(name)) {
                                        if (StringUtils.isNotBlank(vale)) {
                                            valeTrunk = String.valueOf(Double.valueOf(vale).intValue());
                                        }
                                        DiagnosticReadoutEvent diagnosticReadoutEvent2 = fdTcpClient.getDiagnosticReadoutEvent();
                                        diagnosticReadoutEvent2.setOdometerValue(valeTrunk);
                                        fdTcpClient.setDiagnosticReadoutEvent(diagnosticReadoutEvent2);
                                    }
                                    if ("DD0A".equalsIgnoreCase(did)) {
                                        if (StringUtils.isNotBlank(vale)) {
                                            valeTrunk = String.valueOf(Double.valueOf(vale).intValue());
                                        }
                                        DiagnosticReadoutEvent diagnosticReadoutEvent3 = fdTcpClient.getDiagnosticReadoutEvent();
                                        diagnosticReadoutEvent3.setPowerMode(valeTrunk);
                                        fdTcpClient.setDiagnosticReadoutEvent(diagnosticReadoutEvent3);
                                    }
                                    parameter.setValue(vale);
                                    parameter.setDataType(StringParseUtils.convert(res.getOutDataType()));
                                    parameter.setUnit(StrUtil.blankToDefault(res.getUnit(), ""));
                                    parameters.add(parameter);
                                });
                            }
                        });
                    }
                    item.setParameter(parameters);
                    items.add(item);
                }
            }
        }
        request.setItem(items);
        requests.add(request);
        ecu.setRequest(requests);
        ecu.setPartInformation(partInformation);
        return ecu;
    }

    public void handle19Mark(Request request, String req, String mark) {
        if (StrUtil.isNotBlank(req) && req.startsWith(mark)) {
            request.setCategory19(mark);
        }
    }

    private String getNegRes(String msg) {
        Map<String, Object> data = new HashMap<>(2);
        Map<String, Object> value = new HashMap<>(2);
        Map<String, Object> type = new HashMap<>(2);
        value.put("Data_Type", ConstantEnum.STRING);
        ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
        value.put("Data_Value", this.cloud.aE(msg));
        type.put("Data_Type", ConstantEnum.STRING);
        type.put("Data_Value", "Negative");
        data.put("ECU_Response_value", value);
        data.put("ECU_Response_Type", type);
        return ObjectMapperUtils.obj2JsonStr(data);
    }

    private String getVirtualRes(String doipInstructionCode, String ecuAddress, String request) throws DoipException {
        DoipInstructionType doipInstructionType = this.doipUtil.getDoipInstructionType(doipInstructionCode);
        String posAckCode = doipInstructionType.getPosAckCode();
        String response = DownloadManager.getInstance().i(this.vin, ecuAddress, request);
        LOG.info("虚拟车发送指令【{}-{}】,收到回复【{}】", new Object[]{ecuAddress, request, response});
        return VirtualVehicleUtils.getResponse(response, posAckCode);
    }

    public String udsQopData(String inputJsonStr, UdsQopManager qop, int transmissionIntervalTime) throws Exception {
        if (transmissionIntervalTime > 0) {
            Thread.sleep(transmissionIntervalTime);
        }
        if (qop == null || !qop.isSupportQop()) {
            return udsData(inputJsonStr);
        }
        UdsQopThread thread = qop.getQopThread();
        if (qop.isClosing() || thread == null) {
            DoipException doipException = qop.getLastDoipException();
            if (doipException == null) {
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00076));
            }
            throw doipException;
        }
        while (!thread.isWaiting()) {
            Thread.sleep(1L);
        }
        thread.setUsdData(inputJsonStr);
        return "";
    }

    public String callDll(String inputJsonStr) throws Exception {
        Logger.info("Enter into CallDLL() with input " + inputJsonStr);
        JSONObject inputObj = JSONObject.parseObject(inputJsonStr);
        String fixByte = "";
        String seed = "";
        JSONArray paraList = (JSONArray) JSONPath.eval(inputObj, "$.Arguments");
        Iterator it = paraList.iterator();
        while (it.hasNext()) {
            Object para = it.next();
            JSONObject object = JSONObject.parseObject(JSON.toJSONString(para));
            if (object.containsKey("InputValue1")) {
                fixByte = object.get("InputValue1").toString();
            }
            if (object.containsKey("InputValue2")) {
                seed = object.get("InputValue2").toString();
            }
        }
        String s = SecurityUtils.securityAccess(fixByte, seed);
        return "{\"Security_Key\": {\"Data_Type\": \"String\",\"Data_Value\": \"" + s + "\"}}";
    }

    public String callApi(String apiName, JSONObject data, String componentId) throws Exception {
        Logger.info(MessageFormat.format("Enter into CallAPI({0}, {1}) with input.", apiName, data.toJSONString()));
        try {
            if (CALLAPI_READDID.equals(apiName)) {
                return parseDid(data);
            }
            handleString(data);
            LOG.info("Dxsequence callApi handleString");
            String res = this.cloud.callApi(apiName, data, this.userName);
            JSONObject jsonObject = JSONObject.parseObject(res);
            String msg = jsonObject.getString(AjaxResult.gB);
            int code = Integer.parseInt(jsonObject.getString(AjaxResult.gA));
            String apiData = jsonObject.getString(AjaxResult.gC);
            if (0 != code) {
                throw new Exception(MessageUtils.getMessage("Call cloud interface") + apiName + MessageUtils.getMessage("fail") + ";" + MessageUtils.getMessage("Reason for failure") + msg);
            }
            LOG.info("Dxsequence callApi 返回数据");
            return apiData;
        } catch (Exception e) {
            TesterErrorCodeEnum sg00153 = TesterErrorCodeEnum.SG00153;
            logFault(false, sg00153.code(), TesterErrorCodeEnum.formatMsg(sg00153) + e.getMessage(), componentId, "", "", null);
            fail();
            throw e;
        }
    }

    private void handleString(JSONObject data) {
        Set<String> keys = data.keySet();
        for (String key : keys) {
            Object value = data.get(key);
            JSONArray arrayNew = new JSONArray();
            if (value instanceof JSONArray) {
                JSONArray array = (JSONArray) value;
                Iterator it = array.iterator();
                while (it.hasNext()) {
                    Object object = it.next();
                    try {
                        JSONObject jsonObject = JSONObject.parseObject(object.toString());
                        arrayNew.add(jsonObject);
                    } catch (Exception e) {
                        arrayNew.add(object);
                    }
                }
                data.put(key, arrayNew);
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:167:0x0b3c A[Catch: all -> 0x0bd9, Exception -> 0x0dca, all -> 0x0f93, TryCatch #0 {Exception -> 0x0dca, blocks: (B:54:0x02a1, B:55:0x02e9, B:57:0x02f3, B:60:0x0307, B:63:0x0314, B:66:0x0330, B:68:0x03f2, B:75:0x040b, B:77:0x043b, B:81:0x0460, B:82:0x0465, B:181:0x0c08, B:83:0x0488, B:85:0x0498, B:89:0x04b5, B:90:0x04ba, B:91:0x04dd, B:93:0x04e8, B:95:0x04ff, B:96:0x0504, B:97:0x0527, B:98:0x055c, B:100:0x0571, B:102:0x0588, B:103:0x058d, B:110:0x05f6, B:112:0x0612, B:114:0x062d, B:115:0x0636, B:117:0x0640, B:118:0x067f, B:120:0x0698, B:121:0x070a, B:122:0x070b, B:132:0x08d3, B:134:0x094a, B:137:0x095c, B:139:0x096b, B:143:0x09bc, B:146:0x09e1, B:149:0x0a04, B:156:0x0a8f, B:141:0x09a4, B:142:0x09bb, B:124:0x074d, B:125:0x07c8, B:127:0x07cb, B:128:0x0854, B:130:0x0857, B:131:0x08d2, B:151:0x0a14, B:153:0x0a1c, B:154:0x0a54, B:157:0x0ad0, B:159:0x0adb, B:164:0x0b03, B:165:0x0b30, B:167:0x0b3c, B:169:0x0b5c, B:170:0x0b64, B:171:0x0b6a, B:173:0x0bb1, B:174:0x0bb6, B:106:0x05b5, B:108:0x05ce, B:109:0x05d3, B:76:0x042e, B:178:0x0be0, B:179:0x0be5, B:180:0x0c07), top: B:268:0x02a1, outer: #7 }] */
    /* JADX WARN: Removed duplicated region for block: B:173:0x0bb1 A[Catch: Exception -> 0x0dca, all -> 0x0f93, TryCatch #0 {Exception -> 0x0dca, blocks: (B:54:0x02a1, B:55:0x02e9, B:57:0x02f3, B:60:0x0307, B:63:0x0314, B:66:0x0330, B:68:0x03f2, B:75:0x040b, B:77:0x043b, B:81:0x0460, B:82:0x0465, B:181:0x0c08, B:83:0x0488, B:85:0x0498, B:89:0x04b5, B:90:0x04ba, B:91:0x04dd, B:93:0x04e8, B:95:0x04ff, B:96:0x0504, B:97:0x0527, B:98:0x055c, B:100:0x0571, B:102:0x0588, B:103:0x058d, B:110:0x05f6, B:112:0x0612, B:114:0x062d, B:115:0x0636, B:117:0x0640, B:118:0x067f, B:120:0x0698, B:121:0x070a, B:122:0x070b, B:132:0x08d3, B:134:0x094a, B:137:0x095c, B:139:0x096b, B:143:0x09bc, B:146:0x09e1, B:149:0x0a04, B:156:0x0a8f, B:141:0x09a4, B:142:0x09bb, B:124:0x074d, B:125:0x07c8, B:127:0x07cb, B:128:0x0854, B:130:0x0857, B:131:0x08d2, B:151:0x0a14, B:153:0x0a1c, B:154:0x0a54, B:157:0x0ad0, B:159:0x0adb, B:164:0x0b03, B:165:0x0b30, B:167:0x0b3c, B:169:0x0b5c, B:170:0x0b64, B:171:0x0b6a, B:173:0x0bb1, B:174:0x0bb6, B:106:0x05b5, B:108:0x05ce, B:109:0x05d3, B:76:0x042e, B:178:0x0be0, B:179:0x0be5, B:180:0x0c07), top: B:268:0x02a1, outer: #7 }] */
    /* JADX WARN: Removed duplicated region for block: B:91:0x04dd A[Catch: all -> 0x0bd9, Exception -> 0x0dca, all -> 0x0f93, TRY_ENTER, TryCatch #0 {Exception -> 0x0dca, blocks: (B:54:0x02a1, B:55:0x02e9, B:57:0x02f3, B:60:0x0307, B:63:0x0314, B:66:0x0330, B:68:0x03f2, B:75:0x040b, B:77:0x043b, B:81:0x0460, B:82:0x0465, B:181:0x0c08, B:83:0x0488, B:85:0x0498, B:89:0x04b5, B:90:0x04ba, B:91:0x04dd, B:93:0x04e8, B:95:0x04ff, B:96:0x0504, B:97:0x0527, B:98:0x055c, B:100:0x0571, B:102:0x0588, B:103:0x058d, B:110:0x05f6, B:112:0x0612, B:114:0x062d, B:115:0x0636, B:117:0x0640, B:118:0x067f, B:120:0x0698, B:121:0x070a, B:122:0x070b, B:132:0x08d3, B:134:0x094a, B:137:0x095c, B:139:0x096b, B:143:0x09bc, B:146:0x09e1, B:149:0x0a04, B:156:0x0a8f, B:141:0x09a4, B:142:0x09bb, B:124:0x074d, B:125:0x07c8, B:127:0x07cb, B:128:0x0854, B:130:0x0857, B:131:0x08d2, B:151:0x0a14, B:153:0x0a1c, B:154:0x0a54, B:157:0x0ad0, B:159:0x0adb, B:164:0x0b03, B:165:0x0b30, B:167:0x0b3c, B:169:0x0b5c, B:170:0x0b64, B:171:0x0b6a, B:173:0x0bb1, B:174:0x0bb6, B:106:0x05b5, B:108:0x05ce, B:109:0x05d3, B:76:0x042e, B:178:0x0be0, B:179:0x0be5, B:180:0x0c07), top: B:268:0x02a1, outer: #7 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.String vbfswdl(java.lang.String r20) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 4483
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.seq.DxSequence.vbfswdl(java.lang.String):java.lang.String");
    }

    private boolean send1002(String ecuAddress, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String p2Client) throws InterruptedException, DoipException {
        try {
            udsData(getUdsString(ecuAddress, "1002", pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client));
            Thread.sleep(6000L);
            return true;
        } catch (DoipException e) {
            LOG.error("发送1002失败", e);
            throw e;
        } catch (Exception e2) {
            LOG.error("发送1002失败", e2);
            return false;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x0089, code lost:
    
        com.geely.gnds.tester.seq.DxSequence.LOG.error("刷写ecu之前CRC校验失败{}", r10);
        r8 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x009a, code lost:
    
        if (r0 == null) goto L23;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x009f, code lost:
    
        if (0 == 0) goto L22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x00a2, code lost:
    
        r0.close();
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00aa, code lost:
    
        r19 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00ac, code lost:
    
        r13.addSuppressed(r19);
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00b6, code lost:
    
        r0.close();
     */
    /* JADX WARN: Removed duplicated region for block: B:56:0x0138  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private boolean checkEcu(java.util.List<java.lang.String> r6, java.util.Map<java.lang.String, java.lang.String> r7) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 356
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.seq.DxSequence.checkEcu(java.util.List, java.util.Map):boolean");
    }

    private void encodeEcu(List<String> vbfList, String ecuAddress) {
        if (!CollectionUtils.isEmpty(vbfList)) {
            long start = System.currentTimeMillis();
            for (String s : vbfList) {
                if (!s.contains(ConstantEnum.VBF)) {
                    s = s + ConstantEnum.VBF;
                }
                String filePath = this.vbfParseUtils.getVbfPath(s);
                try {
                    AesUtils.encodeFileSelf(filePath);
                } catch (Exception e) {
                    LOG.error("刷写结束加密vbf失败", e);
                }
            }
            long end = System.currentTimeMillis();
            String content = "加密: Ecu " + ecuAddress + "时间：" + (end - start) + "ms";
            LOG.info(content);
            if (this.txtLogger != null) {
                this.txtLogger.write(new Date(), "Main", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
            }
        }
    }

    private void deleteEcu(List<String> vbfList) {
        if (!CollectionUtils.isEmpty(vbfList)) {
            Iterator<String> it = vbfList.iterator();
            while (it.hasNext()) {
                String s = it.next();
                if (!s.contains(ConstantEnum.VBF)) {
                    s = s + ConstantEnum.VBF;
                }
                try {
                    String filePath = this.vbfParseUtils.getVbfTempPath(s, this.vin);
                    LOG.info("刷写结束开始删除：{}", filePath);
                    File file = new File(filePath);
                    if (file.exists()) {
                        file.delete();
                    }
                    LOG.info("刷写开始删除：{}成功", filePath);
                } catch (Exception e) {
                    LOG.error("刷写结束加密vbf失败", e);
                }
            }
        }
    }

    private void decodeEcu(List<String> vbfList, String ecuAddress) {
        if (!CollectionUtils.isEmpty(vbfList)) {
            long start = System.currentTimeMillis();
            for (String s : vbfList) {
                if (!s.contains(ConstantEnum.VBF)) {
                    s = s + ConstantEnum.VBF;
                }
                String filePath = this.vbfParseUtils.getVbfPath(s);
                try {
                    AesUtils.decodeFileSelfForFlush(new File(filePath), this.vin);
                } catch (Exception e) {
                    LOG.error("刷写结束解密vbf失败", e);
                }
            }
            long end = System.currentTimeMillis();
            String content = "解密: Ecu " + ecuAddress + "时间：" + (end - start) + "ms";
            LOG.info(content);
            if (this.txtLogger != null) {
                this.txtLogger.write(new Date(), "Main", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
            }
        }
    }

    private boolean signature(Map<String, Object> vbfHeader, List<Object> dataValue, String ecuAddress, String vbf, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String ecuName, String signatureFlag, String p2Client) throws Exception {
        String swSignatureDev = "";
        String swSignature = "";
        if (vbfHeader.containsKey("sw_signature_dev") && vbfHeader.get("sw_signature_dev") != null) {
            swSignatureDev = vbfHeader.get("sw_signature_dev").toString();
        }
        if (vbfHeader.containsKey("sw_signature") && vbfHeader.get("sw_signature") != null) {
            swSignature = vbfHeader.get("sw_signature").toString();
        }
        if ("".equals(swSignature) && "".equals(swSignatureDev)) {
            return true;
        }
        if (ConstantEnum.TRUE.equalsIgnoreCase(signatureFlag)) {
            Boolean checkFlag = Boolean.valueOf(checkFlagOne(ecuAddress, swSignatureDev, pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client));
            if (!checkFlag.booleanValue() && !"".equals(swSignature)) {
                return checkFlag2(dataValue, ecuAddress, vbf, swSignature, pClientVehicleMax, p2ServerMax, p4ServerMax, ecuName, p2Client);
            }
            return true;
        }
        Boolean checkFlag2 = Boolean.valueOf(checkFlagOne(ecuAddress, swSignature, pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client));
        if (!checkFlag2.booleanValue() && !"".equals(swSignatureDev)) {
            return checkFlag2(dataValue, ecuAddress, vbf, swSignatureDev, pClientVehicleMax, p2ServerMax, p4ServerMax, ecuName, p2Client);
        }
        return true;
    }

    private boolean activate(Map<String, Object> vbfHeader, List<Object> dataValue, String ecuAddress, String vbf, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String ecuName, String p2Client) throws Exception {
        boolean successFlag = true;
        String call = "";
        if (vbfHeader.containsKey("call") && vbfHeader.get("call") != null) {
            call = vbfHeader.get("call").toString();
        }
        if (!"".equals(call)) {
            if (call.length() < ConstantEnum.EIGHT.intValue()) {
                while (call.length() < ConstantEnum.EIGHT.intValue()) {
                    call = "00" + call;
                }
            }
            String callUds = "31010301" + call;
            String callMap = getUdsString(ecuAddress, callUds, pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client);
            String s2 = udsData(callMap);
            String res = JSONPath.eval(s2, "$.ECU_Response_value.Data_Value").toString();
            String[] splits = res.split("71010301");
            if (splits.length > 1) {
                String split = splits[1].substring(1, 2);
                if (!ConstantEnum.ZERO.toString().equals(split)) {
                    successFlag = errorLog3(dataValue, ecuAddress, TesterErrorCodeEnum.SG00131, res, ecuName).booleanValue();
                    LOG.error("Request Download;ECU: {}; File: {}; UDS Send={}; UDS Response = {}", new Object[]{ecuAddress, vbf, callMap, s2});
                }
            } else {
                LOG.error("Request Download;ECU: {}; File: {}; UDS Send={}; UDS Response = {}", new Object[]{ecuAddress, vbf, callMap, s2});
            }
        }
        return successFlag;
    }

    private boolean checkFlag2(List<Object> dataValue, String ecuAddress, String vbf, String swSignature, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String ecuName, String p2Client) throws Exception {
        boolean successFlag = true;
        LOG.info("第二次验签开始");
        String checkMemeroyUds = "31010212" + swSignature;
        String eraseMap = getUdsString(ecuAddress, checkMemeroyUds, pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client);
        String checkMemeoryRespond = udsData(eraseMap);
        String res = JSONPath.eval(checkMemeoryRespond, "$.ECU_Response_value.Data_Value").toString();
        if (!res.contains("710102121000")) {
            successFlag = errorLog3(dataValue, ecuAddress, TesterErrorCodeEnum.SG00130, res, ecuName).booleanValue();
            LOG.error("Request Download;ECU: {}; File: {}; UDS Send={}; UDS Response = {}", new Object[]{ecuAddress, vbf, eraseMap, res});
        }
        return successFlag;
    }

    private boolean checkFlagOne(String ecuAddress, String swSignatureDev, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String p2Client) throws Exception {
        boolean checkFlag = false;
        try {
            if (!"".equals(swSignatureDev)) {
                LOG.info("第一次验签开始");
                String checkMemeroyuds = "31010212" + swSignatureDev;
                String checkMemeoryRespond = udsData(getUdsString(ecuAddress, checkMemeroyuds, pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client));
                String res = JSONPath.eval(checkMemeoryRespond, "$.ECU_Response_value.Data_Value").toString();
                if (res.contains("710102121000")) {
                    checkFlag = true;
                }
            }
        } catch (Exception e) {
            Logger.error("第一次验签失败", e);
        }
        return checkFlag;
    }

    private boolean flashEnd(String ecuAddress, boolean successFlag, boolean isLast, String progressBarName, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, List<Object> dataValue, String ecuName, String p2Client) {
        boolean success = true;
        String response = "";
        try {
            String udsData = udsData(getUdsString(ecuAddress, "31010205", pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client));
            String res = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
            response = res;
            if (res.length() >= ConstantEnum.EIGHT.intValue() && !"710102051000000000".equals(res.substring(0, 18))) {
                LOG.info("31010205收到负相应");
                success = false;
            }
            if (successFlag && isLast) {
                addSetUi("{\"Input\":{\"UI_Name\":\"" + progressBarName + "\",\"SetValue\":{\"Value\":\"100\"}},\"ComponentId\":\"Set_UI11\"}");
            }
        } catch (Exception e) {
            success = false;
            TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(e.getMessage());
            if (enumByCodeOrStr == null) {
                enumByCodeOrStr = TesterErrorCodeEnum.SG00152;
            }
            errorLog3(dataValue, ecuAddress, enumByCodeOrStr, response, ecuName);
        }
        if (!success) {
            errorLog3(dataValue, ecuAddress, TesterErrorCodeEnum.SG00156, response, ecuName);
        }
        return success;
    }

    private String getResult(Boolean successFlag, List<Object> dataValue) {
        String res = "Fail";
        if (successFlag.booleanValue()) {
            res = "Successful";
        }
        Map map = new HashMap(2);
        HashMap map2 = new HashMap(2);
        map2.put("Data_Type", ConstantEnum.STRING);
        map2.put("Data_Value", res);
        map.put("SWDL_result", map2);
        HashMap map3 = new HashMap(2);
        map3.put("Data_Type", ConstantEnum.LIST);
        setSolution(dataValue);
        map3.put("Data_Value", dataValue);
        map.put("Error_info", map3);
        String result = JSON.toJSONString(map);
        LOG.info("Software Download Complete: " + result);
        return result;
    }

    private void setSolution(List<Object> dataValue) {
        try {
            List<String> errorCodes = new ArrayList<>();
            if (dataValue.size() > 0) {
                for (Object o : dataValue) {
                    TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(((Map) o).get(ERROR_CODE_INFO).toString());
                    errorCodes.add(enumByCodeOrStr.code());
                }
                ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
                Map<String, String> solutionByErrorCode = this.cloud.j(errorCodes);
                for (Object o2 : dataValue) {
                    Map error = (Map) o2;
                    String info = error.get(ERROR_CODE_INFO).toString();
                    TesterErrorCodeEnum enumByCodeOrStr2 = TesterErrorCodeEnum.getEnumByCodeOrStr(info);
                    String code = enumByCodeOrStr2.code();
                    if (solutionByErrorCode.containsKey(code)) {
                        if ("zh-CN".equals(HttpUtils.getLanguage())) {
                            error.put(ERROR_CODE_INFO, info + ConstantEnum.ERROR_SOLUTION + solutionByErrorCode.get(code));
                        } else {
                            error.put(ERROR_CODE_INFO, info + ConstantEnum.ERROR_SOLUTION_EN + solutionByErrorCode.get(code));
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("获取解决方案失败", e);
        }
    }

    private Boolean errorLog(List<Object> dataValue, String ecuAddress, TesterErrorCodeEnum errorCode, String ecuName) {
        Map<String, String> error = new HashMap<>(2);
        error.put(ECU_ADDRESS, ecuAddress);
        error.put(ERROR_CODE_INFO, TesterErrorCodeEnum.formatMsg(errorCode));
        error.put(ECU_NAME, ecuName);
        dataValue.add(error);
        logFault(false, errorCode.code(), TesterErrorCodeEnum.formatMsg(errorCode) + "; " + MessageUtils.getMessage("ECU address") + ":" + ecuAddress, "", "", "", ecuAddress, ecuName);
        return false;
    }

    private Boolean errorLog2(List<Object> dataValue, String ecuAddress, String message, String ecuName) {
        Map<String, String> error = new HashMap<>(2);
        error.put(ECU_ADDRESS, ecuAddress);
        error.put(ERROR_CODE_INFO, message);
        error.put(ECU_NAME, ecuName);
        dataValue.add(error);
        TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(message);
        if (enumByCodeOrStr == null) {
            logFault(false, "", message + "; " + MessageUtils.getMessage("ECU address") + ":" + ecuAddress, "", "", "", ecuAddress, ecuName);
        } else {
            logFault(false, enumByCodeOrStr.code(), message + "; " + MessageUtils.getMessage("ECU address") + ":" + ecuAddress, "", "", "", ecuAddress, ecuName);
        }
        return false;
    }

    private Boolean errorLog3(List<Object> dataValue, String ecuAddress, TesterErrorCodeEnum errorCode, String response, String ecuName) {
        Map<String, String> error = new HashMap<>(2);
        error.put(ECU_ADDRESS, ecuAddress);
        error.put(ERROR_CODE_INFO, TesterErrorCodeEnum.formatMsg(errorCode) + "; " + MessageUtils.getMessage("Receive response") + ":" + response);
        error.put(ECU_NAME, ecuName);
        dataValue.add(error);
        logFault(false, errorCode.code(), TesterErrorCodeEnum.formatMsg(errorCode) + "; " + MessageUtils.getMessage("ECU address") + ":" + ecuAddress, "", "", "", ecuAddress, ecuName);
        return false;
    }

    private JSONArray getEcuList(JSONObject inputObj) {
        JSONArray ecuList;
        Object eval = JSONPath.eval(inputObj, "$.VBF_path_info");
        new JSONArray();
        if (eval.toString().contains("$<initializeArgs>")) {
            ecuList = JSONArray.parseArray(this.initializeArgs);
        } else {
            ecuList = JSONObject.parseArray(eval.toString());
        }
        String logger = "Enter into VBF_SWDL() with input " + ecuList.toString();
        LOG.info(logger);
        return ecuList;
    }

    private FdTcpClient getFdTcpClient() throws DoipException {
        FdTcpClient client = this.manager.getFdTcpClient(this.vin);
        if (client == null) {
            throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115) + this.vin);
        }
        return client;
    }

    private void erase(String ecuAddress, List<List<String>> erase, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String p2Client) throws Exception {
        if (!CollectionUtils.isEmpty(erase)) {
            for (List list : erase) {
                String eraseUds = "3101FF00" + ((Object) list.get(0)) + ((Object) list.get(1));
                udsData(getUdsString(ecuAddress, eraseUds, pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client));
            }
        }
    }

    private void setSoftwares(Set<String> delFiles, List<SoftwareDto> softwares, List<String> vbfList, Map<String, String> swkFile, Map<String, String> checksumMap, List<String> vbfListCheck, Map<String, SoftwareDto> swt1SoftWare) {
        for (SoftwareDto softwareDto : softwares) {
            String url = softwareDto.getUrl();
            String fileName = VbfParseUtils.getVbfName(url);
            if (StringUtils.isBlank(fileName)) {
                fileName = softwareDto.getVbfName();
            }
            if (TesterSoftwareTypeEnum.cq(softwareDto.getSoftwareType())) {
                fileName = this.vin + "_" + fileName;
            }
            if (TesterSoftwareTypeEnum.SWT1.getValue().equals(softwareDto.getSoftwareType())) {
                delFiles.add(fileName);
                swt1SoftWare.put(softwareDto.getSoftwareType(), softwareDto);
            } else {
                if (TesterSoftwareTypeEnum.SWLM.getValue().equals(softwareDto.getSoftwareType())) {
                    swt1SoftWare.put(softwareDto.getSoftwareType(), softwareDto);
                }
                if (StringUtils.isNotBlank(fileName)) {
                    checksumMap.put(fileName, softwareDto.getChecksum());
                    vbfList.add(fileName);
                }
                if (TesterSoftwareTypeEnum.SWK1.getValue().equals(softwareDto.getSoftwareType()) || TesterSoftwareTypeEnum.SWK2.getValue().equals(softwareDto.getSoftwareType())) {
                    delFiles.add(fileName);
                    swkFile.put(softwareDto.getSoftwareType(), fileName);
                } else {
                    vbfListCheck.add(fileName);
                }
            }
        }
    }

    private long getVbfTotalSize(JSONArray ecuList, long vbfTotalSize) {
        Iterator it = ecuList.iterator();
        while (it.hasNext()) {
            Object obj = it.next();
            long ecuSize = 0;
            EcuDto ecuDto = (EcuDto) ObjectMapperUtils.jsonStr2Clazz(obj.toString(), EcuDto.class);
            List<SoftwareDto> softwares = ecuDto.getSoftwares();
            Iterator<SoftwareDto> iterator = softwares.iterator();
            while (iterator.hasNext()) {
                SoftwareDto softwareDto = iterator.next();
                if (TesterSoftwareTypeEnum.SWK3.getValue().equals(softwareDto.getSoftwareType())) {
                    iterator.remove();
                } else {
                    vbfTotalSize += softwareDto.getVbfSize().longValue();
                    ecuSize += softwareDto.getVbfSize().longValue();
                }
            }
            ecuDto.setVbfSize(Long.valueOf(ecuSize));
        }
        return vbfTotalSize;
    }

    private void settlePinCode(List<EcuPinCodeDto> ecuPinCodes, List<String> fixByte) {
        for (EcuPinCodeDto ecuPinCodeDto : ecuPinCodes) {
            if (StringUtils.isNotBlank(ecuPinCodeDto.getActualValue())) {
                fixByte.add(ecuPinCodeDto.getActualValue());
            }
            if (StringUtils.isNotBlank(ecuPinCodeDto.getDefaultValue())) {
                fixByte.add(ecuPinCodeDto.getDefaultValue());
            }
            if (StringUtils.isNotBlank(ecuPinCodeDto.getDefaultValue())) {
                fixByte.add(ecuPinCodeDto.getDefaultValue());
            }
            if (StringUtils.isNotBlank(ecuPinCodeDto.getActualValue())) {
                fixByte.add(ecuPinCodeDto.getActualValue());
            }
        }
    }

    private boolean getSwkFiles(EcuDto ecuDto, Map<String, String> swkFile, List<Object> dataValue, String ecuAddress, String ecuName) {
        String swk1 = swkFile.get(TesterSoftwareTypeEnum.SWK1.getValue());
        String swk2 = swkFile.get(TesterSoftwareTypeEnum.SWK2.getValue());
        if (StringUtils.isBlank(swk1) && StringUtils.isBlank(swk2)) {
            return true;
        }
        if (StringUtils.isBlank(swk1) || StringUtils.isBlank(swk2)) {
            errorLog(dataValue, ecuAddress, TesterErrorCodeEnum.SG00133, ecuName);
            return false;
        }
        try {
            Object swk1Base64 = getCertificateTemplate(swk1);
            Object swk2Base64 = getCertificateTemplate(swk2);
            String global = (String) getGlobal(GlobalVariableEnum.lY);
            Object structureWeek = getJsonProperty(global, "structureWeek");
            Object vehicleType = getJsonProperty(global, "vehicleType");
            Map<String, Object> content = new HashMap<>(6);
            content.put("structureWeek", structureWeek);
            content.put("vehicleType", vehicleType);
            content.put("VIN", this.vin);
            content.put("ecuType", ecuDto.getEcuName());
            content.put("p12VbfTemplate", swk1Base64);
            content.put("pwVbfTemplate", swk2Base64);
            ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
            String certificate = this.cloud.ar(ObjectMapperUtils.obj2JsonStr(content));
            Map<String, Object> jsonStr2Map = ObjectMapperUtils.jsonStr2Map(certificate);
            String pkcs12 = (String) jsonStr2Map.get("pkcs12");
            saveCertificate(swk1, pkcs12);
            String secret = (String) jsonStr2Map.get("secret");
            saveCertificate(swk2, secret);
            return true;
        } catch (Exception e) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00066), e);
            errorLog2(dataValue, ecuAddress, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00133) + ":" + e.getMessage(), ecuName);
            return false;
        }
    }

    private boolean getSwt1Files(Map<String, SoftwareDto> swt1SoftWare, List<Object> dataValue, List<String> vbfList, String ecuAddress, String ecuName) {
        if (!swt1SoftWare.containsKey(TesterSoftwareTypeEnum.SWT1.getValue())) {
            return true;
        }
        Map<String, String> content = new HashMap<>(6);
        if (swt1SoftWare.containsKey(TesterSoftwareTypeEnum.SWT1.getValue())) {
            SoftwareDto swt1Dto = swt1SoftWare.get(TesterSoftwareTypeEnum.SWT1.getValue());
            content.put("swt1TemplateUrl", swt1Dto.getUrl());
            content.put("swt1SoftwareNumber", swt1Dto.getSoftwareNumber());
            content.put("swt1SoftwareVersion", swt1Dto.getSoftwareVersion());
        }
        if (swt1SoftWare.containsKey(TesterSoftwareTypeEnum.SWLM.getValue())) {
            SoftwareDto swlmDto = swt1SoftWare.get(TesterSoftwareTypeEnum.SWLM.getValue());
            content.put("swlmTargetUrl", swlmDto.getUrl());
            content.put("swlmSoftwareNumber", swlmDto.getSoftwareNumber());
            content.put("swlmSoftwareVersion", swlmDto.getSoftwareVersion());
        }
        try {
            content.put("vin", this.vin);
            ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
            String certificate = this.cloud.aP(ObjectMapperUtils.obj2JsonStr(content));
            Map<String, Object> jsonStr2Map = ObjectMapperUtils.jsonStr2Map(certificate);
            String swt1SoftwareUrl = (String) jsonStr2Map.get("swt1SoftwareUrl");
            saveSwt1Software(content.get("swt1TemplateUrl"), swt1SoftwareUrl, vbfList);
            return true;
        } catch (Exception e) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00066), e);
            errorLog2(dataValue, ecuAddress, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00133) + ":" + e.getMessage(), ecuName);
            return false;
        }
    }

    private void saveSwt1Software(String templateUrl, String downloadUrl, List<String> vbfList) {
        String fileName = null;
        if (StringUtils.isNotBlank(templateUrl) && templateUrl.contains("/")) {
            fileName = this.vin + "_" + VbfParseUtils.getVbfName(templateUrl);
            vbfList.add(fileName);
        }
        try {
            downloadTempVbfHttp(downloadUrl, fileName);
        } catch (Exception e) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00149);
            LOG.error("{}SWT1下载失败,e={}", fileName, e);
            throw new CustomException(formatMsg, e);
        }
    }

    private void saveCertificate(String tempName, String content) throws IOException {
        if (StringUtils.isBlank(content)) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00147);
            LOG.error("{}tempName:【{}】", formatMsg, tempName);
            throw new CustomException(formatMsg);
        }
        if (!tempName.contains(ConstantEnum.VBF)) {
            tempName = tempName + ConstantEnum.VBF;
        }
        String filePath = this.vbfParseUtils.getVbfTempPath(tempName, this.vin);
        File file = new File(filePath);
        if (!file.exists()) {
            String formatMsg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00148);
            LOG.error("{} tempName【{}】", formatMsg2, tempName);
            throw new CustomException(formatMsg2);
        }
        try {
            FileOutputStream out = new FileOutputStream(file);
            Throwable th = null;
            try {
                Base64.Decoder decoder = Base64.getDecoder();
                byte[] decode = decoder.decode(content);
                out.write(decode);
                if (out != null) {
                    if (0 != 0) {
                        try {
                            out.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        out.close();
                    }
                }
            } finally {
            }
        } catch (Exception e) {
            String formatMsg3 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00149);
            LOG.error("{}tempName【{}】", formatMsg3, tempName);
            throw new CustomException(formatMsg3, e);
        }
    }

    private String getCertificateTemplate(String tempName) throws IOException {
        if (!tempName.contains(ConstantEnum.VBF)) {
            tempName = tempName + ConstantEnum.VBF;
        }
        String filePath = this.vbfParseUtils.getVbfTempPath(tempName, this.vin);
        File file = new File(filePath);
        if (!file.exists()) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00148);
            LOG.error("{} tempName:【{}】", formatMsg, tempName);
            throw new CustomException(formatMsg);
        }
        try {
            FileInputStream in = new FileInputStream(file);
            Throwable th = null;
            try {
                try {
                    byte[] arr = new byte[in.available()];
                    in.read(arr);
                    Base64.Encoder encoder = Base64.getEncoder();
                    String strEncodeToString = encoder.encodeToString(arr);
                    if (in != null) {
                        if (0 != 0) {
                            try {
                                in.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            in.close();
                        }
                    }
                    return strEncodeToString;
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            String formatMsg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00150);
            LOG.error("{} tempName【{}】", formatMsg2, tempName);
            throw new CustomException(formatMsg2, e);
        }
    }

    private void deleteSecurityVbf(Set<String> delFiles) {
        if (!CollectionUtils.isEmpty(delFiles)) {
            for (String name : delFiles) {
                try {
                    this.fileCache.deleteVbf(name);
                } catch (Exception e) {
                    Logger.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00067), e);
                }
            }
        }
    }

    public String getSecurityKey(String inputJsonStr) throws Exception {
        Logger.info("Enter into getSecurityKey() with input " + inputJsonStr);
        JSONObject inputObj = JSONObject.parseObject(inputJsonStr);
        String fixByte = inputObj.getString("FixByte");
        String seed = inputObj.getString("Seed");
        String securityKey = "";
        String platform = ConstantEnum.GEEA2;
        if (seed.length() == 32) {
            platform = ConstantEnum.GEEA3;
        }
        boolean checkFixByte = checkFixByte(fixByte, platform);
        if (!checkFixByte) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00273));
        }
        if (ConstantEnum.GEEA2.equals(platform)) {
            securityKey = SecurityUtils.securityAccess(fixByte, seed);
        } else if (ConstantEnum.GEEA3.equals(platform)) {
            securityKey = SecurityUtils.cmac(fixByte, seed);
        }
        String result = "{\"Security_Key\": {\"Data_Type\": \"String\",\"Data_Value\": \"" + securityKey + "\"}}";
        return result;
    }

    private boolean checkFixByte(String fixByte, String platform) {
        if (ConstantEnum.GEEA2.equals(platform)) {
            if (fixByte.length() == 10) {
                return true;
            }
            return false;
        }
        if (ConstantEnum.GEEA3.equals(platform) && fixByte.length() == 32) {
            return true;
        }
        return false;
    }

    /* JADX WARN: Code restructure failed: missing block: B:22:0x0120, code lost:
    
        r18 = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public boolean getSecurityAccess(java.lang.String r10, java.util.List<java.lang.String> r11, java.util.List<java.lang.Object> r12, java.lang.String r13, java.lang.String r14, java.lang.String r15, java.lang.String r16, java.lang.String r17) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 809
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.seq.DxSequence.getSecurityAccess(java.lang.String, java.util.List, java.util.List, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String):boolean");
    }

    public String getNrcMessage(String message) {
        if (message.contains("S50053")) {
            return "NRC35";
        }
        if (message.contains("S50054")) {
            return "NRC36";
        }
        if (message.contains("S50055")) {
            return "NRC37";
        }
        return "";
    }

    public String getUdsString(String targetAddress, String data, String pClientVehicleMax, String p2ServerMax, String p4ServerMax) {
        String res = "{\"Source_address\": \"0E80\",\"Target_address\": \"" + targetAddress + "\",\"Data\": \"" + data + "\",\"Retries\": \"0\",\"Delay\": \"500\",\"ErrorHandlingType\": \"2\",\"P_Client2_VehicleMax\": \"" + pClientVehicleMax + "\",\"P2ServerMax\": \"" + p2ServerMax + "\",\"P4ServerMax\": \"" + p4ServerMax + "\"}";
        return res;
    }

    public String getUdsString(String targetAddress, String data, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String p2Client) {
        String res = "{\"Source_address\": \"0E80\",\"Target_address\": \"" + targetAddress + "\",\"Data\": \"" + data + "\",\"Retries\": \"0\",\"Delay\": \"500\",\"ErrorHandlingType\": \"2\",\"P_Client2_VehicleMax\": \"" + pClientVehicleMax + "\",\"P2ServerMax\": \"" + p2ServerMax + "\",\"P4ServerMax\": \"" + p4ServerMax + "\",\"P2s_ClientMax\":\"" + p2Client + "\"}";
        return res;
    }

    public String getUdsString(String targetAddress, String data) {
        String res = "{\"Source_address\": \"0E80\",\"Target_address\": \"" + targetAddress + "\",\"Data\": \"" + data + "\",\"Retries\": \"0\",\"Delay\": \"500\",\"ErrorHandlingType\": \"2\"}";
        return res;
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r40v2 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r40v2 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r41v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r41v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 40, insn: 0x030c: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r40 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('reader' java.io.FileInputStream)]) A[TRY_LEAVE], block:B:58:0x030c */
    /* JADX WARN: Not initialized variable reg: 41, insn: 0x0311: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r41 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:60:0x0311 */
    /* JADX WARN: Type inference failed for: r40v2, names: [reader], types: [java.io.FileInputStream] */
    /* JADX WARN: Type inference failed for: r41v0, types: [java.lang.Throwable] */
    public long parseBodyPlus(int asciiCount, String name, FdTcpClient client, String dataFormatIdentifier, String ecuAddress, long vbfTotalSize, long downloadSize, String progressBarName, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, Map size, UdsQopManager qop, String p2Client, int transmissionIntervalTime) throws Exception {
        ?? r40;
        ?? r41;
        if (!name.contains(ConstantEnum.VBF)) {
            name = name + ConstantEnum.VBF;
        }
        long updateSize = vbfTotalSize / 100;
        long updateDownloadSize = 0;
        String filePath = this.vbfParseUtils.getVbfTempPath(name, this.vin);
        File file = new File(filePath);
        try {
            try {
                FileInputStream fileInputStream = new FileInputStream(file);
                Throwable th = null;
                BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
                Throwable th2 = null;
                try {
                    try {
                        long j = asciiCount;
                        byte[] bArr = new byte[4];
                        byte[] bArr2 = new byte[4];
                        byte[] bArr3 = new byte[2];
                        bufferedInputStream.read(new byte[asciiCount], 0, asciiCount);
                        int i = 0;
                        while (j < fileInputStream.getChannel().size()) {
                            i++;
                            bufferedInputStream.read(bArr, 0, 4);
                            String strBytesToHexString2 = this.doipUtil.bytesToHexString2(bArr, bArr.length);
                            bufferedInputStream.read(bArr2, 0, 4);
                            long j2 = j + 4 + 4;
                            long j3 = Long.parseLong(this.doipUtil.bytesToHexString2(bArr2, bArr2.length), 16);
                            int maxLength = getMaxLength(name, dataFormatIdentifier, ecuAddress, bArr2, i, strBytesToHexString2, j3, pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client);
                            long j4 = 0;
                            byte[] bArr4 = new byte[maxLength];
                            int i2 = 1;
                            while (j3 > 0) {
                                String str = String.format("%02x", Integer.valueOf(i2));
                                if (j3 < maxLength) {
                                    int i3 = (int) j3;
                                    byte[] bArr5 = new byte[i3];
                                    bufferedInputStream.read(bArr5, 0, i3);
                                    udsQopData(getUdsString(ecuAddress, "36" + str + bytesToHexString2(bArr5, bArr5.length), pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client), qop, transmissionIntervalTime);
                                    downloadSize += j3;
                                    size.put("downloadSize", Long.valueOf(downloadSize));
                                    updateDownloadSize = getUpdateDownloadSize(vbfTotalSize, downloadSize, updateSize, updateDownloadSize, i3, progressBarName);
                                    j4 += j3;
                                    j3 = 0;
                                } else {
                                    bufferedInputStream.read(bArr4, 0, maxLength);
                                    udsQopData(getUdsString(ecuAddress, "36" + str + bytesToHexString2(bArr4, bArr4.length), pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client), qop, transmissionIntervalTime);
                                    downloadSize += maxLength;
                                    size.put("downloadSize", Long.valueOf(downloadSize));
                                    updateDownloadSize = getUpdateDownloadSize(vbfTotalSize, downloadSize, updateSize, updateDownloadSize, maxLength, progressBarName);
                                    j4 += maxLength;
                                    j3 -= maxLength;
                                }
                                i2 = i2 == 255 ? 0 : i2 + 1;
                            }
                            long index = getIndex(name, ecuAddress, bufferedInputStream, j2, bArr3, i, j3, pClientVehicleMax, p2ServerMax, p4ServerMax, qop, p2Client);
                            bufferedInputStream.read(bArr3, 0, 2);
                            j = index + 2;
                        }
                        if (bufferedInputStream != null) {
                            if (0 != 0) {
                                try {
                                    bufferedInputStream.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                bufferedInputStream.close();
                            }
                        }
                        if (fileInputStream != null) {
                            if (0 != 0) {
                                try {
                                    fileInputStream.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                fileInputStream.close();
                            }
                        }
                        return downloadSize;
                    } catch (Throwable th5) {
                        if (bufferedInputStream != null) {
                            if (th2 != null) {
                                try {
                                    bufferedInputStream.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                bufferedInputStream.close();
                            }
                        }
                        throw th5;
                    }
                } finally {
                }
            } catch (DoipException e) {
                throw e;
            } catch (IOException e2) {
                LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00071), e2);
                throw e2;
            }
        } catch (Throwable th7) {
            if (r40 != 0) {
                if (r41 != 0) {
                    try {
                        r40.close();
                    } catch (Throwable th8) {
                        r41.addSuppressed(th8);
                    }
                } else {
                    r40.close();
                }
            }
            throw th7;
        }
    }

    private String bytesToHexString2(byte[] bytes, int count) {
        char[] hexArray = "0123456789ABCDEF".toCharArray();
        if (bytes.length == 0 || count == 0) {
            return "";
        }
        if (bytes.length < count) {
            count = bytes.length;
        }
        char[] hexChars = new char[count * 2];
        for (int j = 0; j < count; j++) {
            int v = bytes[j] & 255;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[(j * 2) + 1] = hexArray[v & 15];
        }
        return new String(hexChars);
    }

    public void deleteVbf(String filePath) {
        String successFileName = filePath.replace(ConstantEnum.VBF, ".success");
        File file = new File(filePath);
        if (file.exists()) {
            file.delete();
        }
        File successFile = new File(successFileName);
        if (successFile.exists()) {
            successFile.delete();
        }
    }

    private Boolean equalsBytes(byte[] b1, byte[] b2) {
        if (b1.length != b2.length) {
            return false;
        }
        if (b1 == null || b2 == null) {
            return false;
        }
        for (int i = 0; i < b1.length; i++) {
            if (b1[i] != b2[i]) {
                return false;
            }
        }
        return true;
    }

    private int getMaxLength(String name, String dataFormatIdentifier, String ecuAddress, byte[] blockLengh, int j, String startAdd, long bl, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String p2Client) throws Exception {
        String logger = "Block_DL;File: " + name + ";block:" + j + ";Start_Add:" + startAdd + ";Length:" + bl;
        LOG.info(logger);
        String startTransUds = "34" + dataFormatIdentifier + "44" + startAdd + this.doipUtil.bytesToHexString2(blockLengh, blockLengh.length);
        LOG.info("发送34指令：" + startTransUds);
        String s = udsData(getUdsString(ecuAddress, startTransUds, pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client));
        String maxNumberOfBlockLength = JSONPath.eval(s, "$.ECU_Response_value.Data_Value").toString().substring(4);
        int maxLength = new BigInteger(maxNumberOfBlockLength, 16).intValue();
        return maxLength - 2;
    }

    private long getUpdateDownloadSize(float vbfTotalSize, long downloadSize, long updateSize, long updateDownloadSize, int bufLen, String progressBarName) {
        long updateDownloadSize2 = updateDownloadSize + bufLen;
        if (updateDownloadSize2 > updateSize) {
            updateDownloadSize2 = 0;
            float progress = (downloadSize / vbfTotalSize) * 100.0f;
            if (progress >= 99) {
                progress = 99.0f;
            }
            int progressInt = StrictMath.round(progress);
            addSetUi("{\"Input\":{\"UI_Name\":\"" + progressBarName + "\",\"SetValue\":{\"Value\":\"" + progressInt + "\"}},\"ComponentId\":\"Set_UI11\"}");
        }
        return updateDownloadSize2;
    }

    private long getIndex(String name, String ecuAddress, BufferedInputStream inBuff, long index, byte[] checksum, int j, long bl, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, UdsQopManager qop, String p2Client) throws Exception {
        LOG.info("发送37开始");
        if (qop != null && qop.isSupportQop()) {
            qop.waitQopComplete();
        }
        udsData(getUdsString(ecuAddress, "37", pClientVehicleMax, p2ServerMax, p4ServerMax, p2Client));
        long index2 = index + bl;
        String logger = "第" + j + "Block读取结束;File: " + name;
        LOG.info(logger);
        return index2;
    }

    public int byteArrayToInt(byte[] bytes) {
        int value = 0;
        for (int i = 0; i < ConstantEnum.FORE.intValue(); i++) {
            int shift = (3 - i) * 8;
            value += (bytes[i] & 255) << shift;
        }
        return value;
    }

    protected void logXml(String logName, String value, String componentId) {
        if (this.xmlSeq != null) {
            XmlBuriedPoint point = new XmlBuriedPoint(logName, value, componentId, (String) Optional.ofNullable(ActionContext.getRequest()).map((v0) -> {
                return v0.getRequestURI();
            }).orElse(""));
            this.xmlSeq.a(point);
        }
    }

    protected void logAttribute(String variableName, String value, String logicStatement, String componentId) {
        if (this.xmlSeq != null) {
            XmlAttribute attr = new XmlAttribute(variableName, value, logicStatement, componentId);
            this.xmlSeq.a(attr);
        }
    }

    protected void logFault(boolean success, String code, String shortDesc, String componentId, String sended, String received, String ecuAddress) {
        if (this.xmlSeq != null) {
            boolean mask = false;
            if (this.maskCodes.contains(code)) {
                mask = true;
            }
            XmlFault fault = new XmlFault(success, code, shortDesc, componentId, sended, received, mask, ecuAddress, getEcuName(ecuAddress));
            this.xmlSeq.b(fault);
        }
    }

    protected void logMasked(boolean success, String code, String shortDesc, String componentId, String sended, String received, String ecuAddress) {
        if (this.xmlSeq != null) {
            XmlFault fault = new XmlFault(success, code, shortDesc, componentId, sended, received, true, ecuAddress, getEcuName(ecuAddress));
            this.xmlSeq.b(fault);
        }
    }

    protected void logFault(boolean success, String code, String shortDesc, String componentId, String sended, String received, String ecuAddress, String ecuName) {
        if (this.xmlSeq != null) {
            boolean mask = false;
            if (this.maskCodes.contains(code)) {
                mask = true;
            }
            if (StringUtils.isBlank(ecuName)) {
                ecuName = getEcuName(ecuAddress);
            }
            XmlFault fault = new XmlFault(success, code, shortDesc, componentId, sended, received, mask, ecuAddress, ecuName);
            this.xmlSeq.b(fault);
        }
    }

    /* JADX WARN: Can't wrap try/catch for region: R(12:166|25|(1:27)(1:28)|29|(11:(2:183|31)(2:32|(5:177|34|35|185|184)(1:181))|155|42|(2:43|(3:45|(3:190|47|193)(1:192)|191)(1:189))|48|(2:50|(2:164|52)(1:55))|70|(2:72|(2:160|74)(1:77))|92|99|100)|36|(1:38)|39|169|40|170|41) */
    /* JADX WARN: Code restructure failed: missing block: B:93:0x0348, code lost:
    
        r38 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:94:0x034a, code lost:
    
        r0 = r38.getMessage();
     */
    /* JADX WARN: Code restructure failed: missing block: B:95:0x0359, code lost:
    
        if (r0.contains("Server returned HTTP response code: 416") == false) goto L178;
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x035e, code lost:
    
        throw r38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:98:0x035f, code lost:
    
        r24 = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void downloadVbfHttp(java.util.List<java.lang.String> r8, long r9, long r11, java.util.Map<java.lang.String, java.lang.String> r13, java.util.List<java.lang.String> r14) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 1243
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.seq.DxSequence.downloadVbfHttp(java.util.List, long, long, java.util.Map, java.util.List):void");
    }

    public void updateProgress(long downloadSize, long totleSize) {
        float progress = (downloadSize / totleSize) * 100.0f;
        if (progress >= 100.0f) {
            progress = 100.0f;
        }
        int progressInt = (int) progress;
        addSetUi("{\"Input\":{\"UI_Name\":\"ProgressBarForVbfdl\",\"SetValue\":{\"Value\":\"" + progressInt + "\"}},\"ComponentId\":\"Set_UI11\"}");
    }

    /* JADX WARN: Failed to calculate best type for var: r16v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r16v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 16, insn: 0x016d: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r16 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:48:0x016d */
    /* JADX WARN: Type inference failed for: r0v61, types: [java.io.DataInputStream, javax.net.ssl.HttpsURLConnection] */
    /* JADX WARN: Type inference failed for: r16v0, types: [java.lang.Throwable] */
    public void downloadTempVbfHttp(String urlStr, String fileName) throws Exception {
        File vbfBase = this.fileCache.getTempBase();
        LOG.info("DxSequence开始下载Temp目录vbf：{}", fileName);
        String savePath = vbfBase + File.separator + this.vin + "_" + fileName;
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        boolean useHttps = urlStr.startsWith("https");
        if (useHttps) {
            ?? r0 = (HttpsURLConnection) conn;
            trustAllHosts(r0);
            r0.setHostnameVerifier(DO_NOT_VERIFY);
        }
        conn.setRequestProperty("Accept", "*/*");
        conn.setConnectTimeout(15000);
        conn.setUseCaches(false);
        conn.setReadTimeout(15000);
        conn.connect();
        try {
            try {
                DataInputStream in = new DataInputStream(conn.getInputStream());
                Throwable th = null;
                DataOutputStream out = new DataOutputStream(new FileOutputStream(savePath, true));
                Throwable th2 = null;
                try {
                    try {
                        byte[] buffer = new byte[4096];
                        while (true) {
                            int count = in.read(buffer);
                            if (count <= 0) {
                                break;
                            } else {
                                out.write(buffer, 0, count);
                            }
                        }
                        if (out != null) {
                            if (0 != 0) {
                                try {
                                    out.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                out.close();
                            }
                        }
                        if (in != null) {
                            if (0 != 0) {
                                try {
                                    in.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                in.close();
                            }
                        }
                    } catch (Throwable th5) {
                        if (out != null) {
                            if (th2 != null) {
                                try {
                                    out.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                out.close();
                            }
                        }
                        throw th5;
                    }
                } finally {
                }
            } catch (Exception e) {
                throw e;
            }
        } finally {
        }
    }

    private static SSLSocketFactory trustAllHosts(HttpsURLConnection connection) throws NoSuchAlgorithmException, KeyManagementException {
        SSLSocketFactory oldFactory = connection.getSSLSocketFactory();
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, TRUST_MANAGERS, new SecureRandom());
            SSLSocketFactory newFactory = sc.getSocketFactory();
            connection.setSSLSocketFactory(newFactory);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return oldFactory;
    }

    public void download() throws Exception {
        int retry = 3;
        while (retry > 0) {
            try {
                downloadVbf();
                retry = 0;
            } catch (IOException e) {
                retry--;
                LOG.info("重载下载VBF失败，尝试第{}次", Integer.valueOf(3 - retry), e);
                if (retry == 0) {
                    throw new IOException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00052) + e.getMessage());
                }
            } catch (Exception e2) {
                throw e2;
            }
        }
    }

    public void downloadVbf() throws Exception {
        int i;
        DownloadManager instance = DownloadManager.getInstance();
        instance.H();
        LOG.info("重载需要下载的软件：" + this.initializeArgs);
        JSONArray vbfPath = JSONArray.parseArray(this.initializeArgs);
        List<String> urls = new ArrayList<>();
        long totleSize = 0;
        long downloadSize = 0;
        List<EcuDto> ecuDtoList = new ArrayList<>();
        Map<String, String> checksumMap = new HashMap<>();
        List<String> tempUrl = new ArrayList<>();
        Iterator it = vbfPath.iterator();
        while (it.hasNext()) {
            Object obj = it.next();
            EcuDto ecuDto = (EcuDto) ObjectMapperUtils.jsonStr2Clazz(obj.toString(), EcuDto.class);
            List<SoftwareDto> softwares = ecuDto.getSoftwares();
            if (!CollectionUtils.isEmpty(softwares)) {
                ecuDtoList.add(ecuDto);
                for (SoftwareDto softwareDto : softwares) {
                    String softwareType = softwareDto.getSoftwareType();
                    String url = softwareDto.getUrl();
                    if (StringUtils.isBlank(url)) {
                        throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00181));
                    }
                    String checksum = softwareDto.getChecksum();
                    if (TesterSoftwareTypeEnum.SWK1.getValue().equals(softwareType) || TesterSoftwareTypeEnum.SWK2.getValue().equals(softwareType)) {
                        totleSize += softwareDto.getVbfSize().longValue();
                        urls.add(url);
                        tempUrl.add(url);
                        checksumMap.put(url, checksum);
                        String fileName = this.vin + "_" + VbfParseUtils.getVbfName(url);
                        if (StringUtils.isBlank(fileName)) {
                            fileName = this.vin + "_" + softwareDto.getVbfName();
                        }
                        this.fileCache.deleteVbf(fileName);
                    } else {
                        Long vbfSize = softwareDto.getVbfSize();
                        totleSize += vbfSize.longValue();
                        if (TesterSoftwareTypeEnum.cq(softwareType)) {
                            i = this.fileCache.j(url, this.vin);
                        } else {
                            i = this.fileCache.ab(url);
                        }
                        if (i == 0) {
                            urls.add(url);
                            if (TesterSoftwareTypeEnum.cq(softwareType)) {
                                tempUrl.add(url);
                            }
                            checksumMap.put(url, checksum);
                        } else if (i == 2) {
                            downloadSize += this.fileCache.af(url);
                            if (TesterSoftwareTypeEnum.cq(softwareType)) {
                                tempUrl.add(url);
                            }
                            urls.add(url);
                            checksumMap.put(url, checksum);
                        } else {
                            downloadSize += vbfSize.longValue();
                        }
                    }
                }
            }
        }
        downloadVbfHttp(urls, totleSize, downloadSize, checksumMap, tempUrl);
        try {
            SoftwareServiceImpl bean = (SoftwareServiceImpl) SpringUtils.getBean(SoftwareServiceImpl.class);
            if (bean != null) {
                bean.checkSoftwareRecord(ecuDtoList);
            }
        } catch (Exception e) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00072), e);
        }
    }

    public Object runScript(String code, List init) throws Exception {
        Map initializeParamMap = new HashMap();
        String runScriptId = UUID.randomUUID().toString().replace("-", "");
        initializeParamMap.put("runScriptId", runScriptId);
        try {
            initializeParamMap.put("seqCodeFather", this.seqCode);
            if (this.dro) {
                initializeParamMap.put("DRO", ConstantEnum.TRUE);
            } else {
                initializeParamMap.put("DRO", ConstantEnum.FALSE);
            }
            String bizType = getInitializeParams("bizType");
            String type = initializeParamMap.get("bizType") == null ? "" : initializeParamMap.get("bizType").toString();
            if (StringUtils.isBlank(type)) {
                initializeParamMap.put("bizType", bizType);
            }
        } catch (Exception e) {
        }
        RunScriptDTO runScriptDTO = new RunScriptDTO();
        runScriptDTO.setArgument(init);
        this.manager.addScript(runScriptId, runScriptDTO);
        String initString = JSON.toJSONString(initializeParamMap);
        addSetUi("{\"Input\":{\"UI_Name\":\"NewSeq\",\"SetValue\":{\"seqCode\":\"" + code + "\",\"vin\":\"" + this.vin + "\",\"initializeParams\":" + initString + "}},\"ComponentId\":\"Set_UI11\"}");
        waitSeq();
        ScriptHandler handler = SeqManager.getInstance().getScriptHandler(this.vin + code);
        if (handler == null) {
            throw new Exception(MessageUtils.getMessage("Nested diagnostic sequence execution exception, process needs to be aborted, script number") + "：" + code);
        }
        String exceptionMsg = runScriptDTO.getExceptionMsg();
        if (StringUtils.isNotBlank(exceptionMsg)) {
            throw new Exception(MessageUtils.getMessage("Nested diagnostic sequence execution exception, process needs to be aborted, script number") + "：" + code + ConstantEnum.COMMA + MessageUtils.getMessage("Reason for failure") + "：" + exceptionMsg);
        }
        Object result = runScriptDTO.getResult();
        LOG.info("调用诊断序列{}结果：{}", code, result);
        return result;
    }

    public RunScriptDTO getRunScript() throws Exception {
        String runScriptId = getInitializeParams("runScriptId");
        RunScriptDTO runScript = this.manager.getRunScript(runScriptId);
        if (runScript == null) {
            return null;
        }
        return runScript;
    }

    public String checkConnectStatus() {
        String connectStatus = "1";
        FdTcpClient fdTcpClient = this.manager.getFdTcpClient(this.vin);
        try {
            boolean isClose = fdTcpClient.checkConnectStatus();
            if (isClose) {
                connectStatus = "0";
            }
        } catch (Exception e) {
            connectStatus = "0";
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00073), e);
        }
        return connectStatus;
    }

    public void addSeqProcessCount() {
        if (this.isStatusReadout.booleanValue()) {
            Object count = getGlobal("seqStatusReadoutCount");
            if (!ObjectUtils.isEmpty(count)) {
                AtomicInteger seqProcessCount = (AtomicInteger) count;
                int i = seqProcessCount.incrementAndGet();
                LOG.debug("执行002诊断序列：" + this.seqCode + ";数量加1当前002脚本计数器数量为：" + i);
                return;
            }
            LOG.error("seqStatusReadoutCount没有初始化");
            return;
        }
        Object count2 = getGlobal("seqProcessCount");
        if (!ObjectUtils.isEmpty(count2)) {
            AtomicInteger seqProcessCount2 = (AtomicInteger) count2;
            int i2 = seqProcessCount2.incrementAndGet();
            LOG.info("执行诊断序列：" + this.seqCode + ";数量加1当前普通脚本计数器数量为：" + i2);
            return;
        }
        LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00074));
    }

    public void reduceSeqProcessCount() {
        if (this.isStatusReadout.booleanValue()) {
            Object count = getGlobal("seqStatusReadoutCount");
            if (!ObjectUtils.isEmpty(count)) {
                AtomicInteger seqProcessCount = (AtomicInteger) count;
                int i = seqProcessCount.decrementAndGet();
                LOG.debug("执行002诊断序列：" + this.seqCode + ";数量减1;当前002脚本计数器数量为：" + i);
            } else {
                LOG.error("seqStatusReadoutCount没有初始化");
            }
        } else {
            Object count2 = getGlobal("seqProcessCount");
            if (!ObjectUtils.isEmpty(count2)) {
                AtomicInteger seqProcessCount2 = (AtomicInteger) count2;
                int i2 = seqProcessCount2.decrementAndGet();
                LOG.info("执行诊断序列：" + this.seqCode + ";数量减1当前普通脚本计数器数量为：" + i2);
            } else {
                LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00074));
            }
        }
        Object lock = this.manager.getLock(this.vin);
        synchronized (lock) {
            lock.notifyAll();
        }
    }

    public void waitSeq() {
        LOG.info("{}诊断序列等待！", this.seqCode);
        try {
            FdTcpClient fdTcpClient = this.manager.getFdTcpClient(this.vin);
            if (fdTcpClient != null) {
                fdTcpClient.setSeqCode(this.seqCode);
            }
            reduceSeqProcessCount();
            synchronized (this.lock) {
                this.lock.wait();
            }
            if (fdTcpClient != null) {
                fdTcpClient.cleanSeqCode();
            }
            addSeqProcessCount();
        } catch (InterruptedException e) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00075);
            LOG.error("{}{}", new Object[]{formatMsg, this.seqCode, e});
        }
    }

    public void notifySeq() {
        LOG.info("唤醒" + this.seqCode + "诊断序列！");
        synchronized (this.lock) {
            this.lock.notifyAll();
        }
    }

    protected String getStrList(String inputString, int length) {
        int size = inputString.length() / length;
        if (inputString.length() % length != 0) {
            size++;
        }
        List<String> list = new ArrayList<>();
        for (int index = 0; index < size; index++) {
            String childStr = subStr(inputString, index * length, (index + 1) * length);
            list.add(childStr);
        }
        return JSON.toJSONString(list);
    }

    protected List<String> subStrList(String inputString, int length) {
        int size = inputString.length() / length;
        if (inputString.length() % length != 0) {
            size++;
        }
        List<String> list = new ArrayList<>();
        for (int index = 0; index < size; index++) {
            String childStr = subStr(inputString, index * length, (index + 1) * length);
            list.add(childStr);
        }
        return list;
    }

    private String subStr(String str, int f, int t) {
        if (f > str.length()) {
            return null;
        }
        if (t > str.length()) {
            return str.substring(f, str.length());
        }
        return str.substring(f, t);
    }

    public String getPinCode(String ecuName, String pinCodeKey) {
        LOG.info("{}诊断序列pincode获取！", this.seqCode);
        List pinCode = new ArrayList();
        String diagnosticPartNumber = "";
        Object nums = this.manager.getGlobal(GlobalVariableEnum.mb, this.vin) == null ? new HashMap(1) : this.manager.getGlobal(GlobalVariableEnum.mb, this.vin);
        Map diagnosticNumbers = (Map) nums;
        if (diagnosticNumbers.containsKey(ecuName)) {
            diagnosticPartNumber = diagnosticNumbers.get(ecuName).toString();
        }
        ParamServiceImpl bean = (ParamServiceImpl) SpringUtils.getBean(ParamServiceImpl.class);
        if (bean != null) {
            pinCode = bean.getPinCode(this.vin, diagnosticPartNumber, pinCodeKey, ecuName);
        }
        return JSON.toJSONString(pinCode);
    }

    /* JADX WARN: Code restructure failed: missing block: B:27:0x00f4, code lost:
    
        r12 = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.Boolean getSecurityAccess(java.lang.String r8, java.lang.String r9, java.lang.String r10, java.lang.Object r11) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 429
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.seq.DxSequence.getSecurityAccess(java.lang.String, java.lang.String, java.lang.String, java.lang.Object):java.lang.Boolean");
    }

    public String checkPrecondition(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        String applics = jsonObject.getString("VDN");
        String ecuApplics = jsonObject.getString("ECU");
        Map res = new HashMap(2);
        HashMap map = new HashMap(2);
        map.put("Data_Type", ConstantEnum.STRING);
        map.put("Data_Value", "");
        HashMap map2 = new HashMap(2);
        map2.put("Data_Type", ConstantEnum.STRING);
        map2.put("Data_Value", "Failed");
        Object broadcastQuery = this.manager.getGlobal(GlobalVariableEnum.lX, this.vin);
        if (broadcastQuery != null) {
            try {
                new ArrayList();
                new ArrayList();
                String broadcast = broadcastQuery.toString();
                Object strByJsonNodeExpr = ObjectMapperUtils.findObjByJsonNodeExpr(broadcast, "/vehicleData/ecus");
                List<EcuBroadcastDto> ecusByBroadcast = ObjectMapperUtils.jsonStr2List(strByJsonNodeExpr.toString(), EcuBroadcastDto.class);
                List<String> ecuList = (List) ecusByBroadcast.stream().map((v0) -> {
                    return v0.getEcuName();
                }).collect(Collectors.toList());
                Map<String, Object> jsonStr2Map = ObjectMapperUtils.jsonStr2Map(broadcast);
                String vdns = ObjectMapperUtils.obj2JsonStr(jsonStr2Map.get("vdnData"));
                ObjectMapper mapper = ObjectMapperUtils.getInstance();
                List<VdnDataDto> vdnDataDtos = Arrays.asList((Object[]) mapper.readValue(vdns, VdnDataDto[].class));
                List<String> vdnList = (List) vdnDataDtos.stream().map((v0) -> {
                    return v0.getValue();
                }).collect(Collectors.toList());
                if (applicFilter(vdnList, ecuList, applics, ecuApplics)) {
                    map.put("Data_Value", "Successful");
                    map2.put("Data_Value", "Successful");
                } else {
                    map.put("Data_Value", TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00165));
                }
            } catch (Exception e) {
                map.put("Data_Value", TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00165));
            }
        } else {
            map.put("Data_Value", TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00166));
        }
        res.put("Respond_value", map);
        res.put("Respond_Type", map2);
        return JSONObject.toJSONString(res);
    }

    private boolean applicFilter(List<String> vdnList, List<String> ecuList, String applics, String ecuApplics) throws ScriptException {
        String strReplace;
        LOG.info("ecuList:" + ecuList.toString());
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine se = manager.getEngineByName("js");
        boolean applicFlag = true;
        LOG.info("applics==" + applics);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(applics)) {
            applicFlag = getResult(applics, vdnList).booleanValue();
        }
        boolean ecuApplicFlag = true;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(ecuApplics) && ecuList.size() > 0) {
            String newEcus = ecuApplics.replaceAll("[!()|&]", ConstantEnum.COMMA);
            String newEcus2 = newEcus.replaceAll(",,,,", ConstantEnum.COMMA).replaceAll(",,,", ConstantEnum.COMMA).replaceAll(",,", ConstantEnum.COMMA);
            if (!"".equals(ecuApplics) && ecuApplics.startsWith(ConstantEnum.COMMA)) {
                ecuApplics = ecuApplics.substring(1);
            }
            if (!"".equals(ecuApplics) && ecuApplics.endsWith(ConstantEnum.COMMA)) {
                ecuApplics = ecuApplics.substring(0, ecuApplics.length() - 1);
            }
            String[] ecuApplicsGroup = newEcus2.split(ConstantEnum.COMMA);
            for (int i = 0; i < ecuApplicsGroup.length; i++) {
                if (ecuList.contains(ecuApplicsGroup[i])) {
                    strReplace = ecuApplics.replace(ecuApplicsGroup[i], "1");
                } else {
                    strReplace = ecuApplics.replace(ecuApplicsGroup[i], "0");
                }
                ecuApplics = strReplace;
            }
            Object obj = se.eval(ecuApplics);
            ecuApplicFlag = Boolean.TRUE.equals(obj) || new Integer(1).equals(obj);
        }
        LOG.info("applicFlag:" + applicFlag + ";ecuApplicFlag:" + ecuApplicFlag);
        return applicFlag && ecuApplicFlag;
    }

    public static Boolean getResult(String expression, List<String> vdnList) throws ScriptException {
        Matcher m = p.matcher(expression);
        while (m.find()) {
            String temp = m.group();
            if (vdnList.contains(temp)) {
                expression = expression.replaceFirst(temp, "1");
            } else {
                expression = expression.replaceFirst(temp, "0");
            }
        }
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine se = manager.getEngineByName("js");
        Object obj = se.eval(expression);
        return Boolean.valueOf(Boolean.TRUE.equals(obj) || new Integer(1).equals(obj));
    }

    public static String swdlUrlInfoListRankRule(Object ecuNames, String ecuInfo) {
        try {
            LOG.info("排序入参ECU列表：{}", ecuInfo);
            LOG.info("排序入参ECU名称列表：{}", ecuNames);
            JSONArray names = new JSONArray();
            if (ecuNames instanceof JSONArray) {
                names = (JSONArray) ecuNames;
            } else if (ecuNames instanceof String) {
                names = JSONObject.parseArray((String) ecuNames);
            }
            List<EcuDto> ecuDtoList = ObjectMapperUtils.jsonStr2List(ecuInfo, EcuDto.class);
            List<EcuDto> sort = new ArrayList<>(ecuDtoList.size());
            for (EcuDto ecuDto : ecuDtoList) {
                String ecuName = ecuDto.getEcuName();
                if (!names.contains(ecuName)) {
                    sort.add(ecuDto);
                }
            }
            Iterator it = names.iterator();
            while (it.hasNext()) {
                Object object = it.next();
                String name = (String) object;
                for (EcuDto ecuDto2 : ecuDtoList) {
                    String ecuName2 = ecuDto2.getEcuName();
                    if (name.equals(ecuName2)) {
                        sort.add(ecuDto2);
                    }
                }
            }
            String ecuInfoSort = JSON.toJSONString(sort);
            LOG.info("排序后的ECU列表：{}", ecuInfoSort);
            return ecuInfoSort;
        } catch (Exception e) {
            LOG.error("排序失败", e);
            return ecuInfo;
        }
    }

    public static JSONArray swdlUrlInfoListRankRule(Object ecuNames, JSONArray ecuInfo) {
        try {
            LOG.info("排序入参ECU列表：{}", ecuInfo);
            LOG.info("排序入参ECU名称列表：{}", ecuNames);
            JSONArray names = new JSONArray();
            if (ecuNames instanceof JSONArray) {
                names = (JSONArray) ecuNames;
            } else if (ecuNames instanceof String) {
                names = JSONObject.parseArray((String) ecuNames);
            }
            JSONArray sort = new JSONArray(ecuInfo.size());
            Iterator it = ecuInfo.iterator();
            while (it.hasNext()) {
                Object object = it.next();
                EcuDto ecuDto = (EcuDto) JSONObject.parseObject(JSON.toJSONString(object), EcuDto.class);
                String ecuName = ecuDto.getEcuName();
                if (!names.contains(ecuName)) {
                    sort.add(ecuDto);
                }
            }
            Iterator it2 = names.iterator();
            while (it2.hasNext()) {
                Object name = it2.next();
                Iterator it3 = ecuInfo.iterator();
                while (it3.hasNext()) {
                    Object object2 = it3.next();
                    EcuDto ecuDto2 = (EcuDto) JSONObject.parseObject(JSON.toJSONString(object2), EcuDto.class);
                    String ecuName2 = ecuDto2.getEcuName();
                    if (name.equals(ecuName2)) {
                        sort.add(ecuDto2);
                    }
                }
            }
            String ecuInfoSort = JSON.toJSONString(sort);
            LOG.info("排序后的ECU列表：{}", ecuInfoSort);
            return sort;
        } catch (Exception e) {
            LOG.error("排序失败", e);
            return ecuInfo;
        }
    }

    public void cancelByUserException() {
        if (!this.isVbfswdl) {
            Iterator<FdDoipTcpReceiveListener> iter = this.listeners.iterator();
            while (iter.hasNext()) {
                FdDoipTcpReceiveListener listener = iter.next();
                listener.cancelByUserException();
            }
        }
    }

    public boolean isCloseNoLock() throws Exception {
        boolean isClose;
        FdTcpClient fdTcpClient = this.manager.getFdTcpClient(this.vin);
        if (fdTcpClient != null) {
            isClose = fdTcpClient.isCloseNoLock();
            if (isClose && fdTcpClient.isRecvFailed()) {
                throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00235));
            }
        } else {
            isClose = true;
        }
        return isClose;
    }

    public void checkConnect() throws Exception {
        if (isCloseNoLock()) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00170));
        }
    }

    public String swdlSort(Object ecuNames, String ecuInfo) {
        try {
            LOG.info("swdlSort排序入参ECU列表：{}", ecuInfo);
            LOG.info("swdlSort排序入参ECU名称列表：{}", ecuNames);
            JSONArray names = new JSONArray();
            if (ecuNames instanceof JSONArray) {
                names = (JSONArray) ecuNames;
            } else if (ecuNames instanceof String) {
                names = JSONObject.parseArray((String) ecuNames);
            }
            List<EcuDto> ecuDtoList = ObjectMapperUtils.jsonStr2List(ecuInfo, EcuDto.class);
            List<EcuDto> sort = new ArrayList<>(ecuDtoList.size());
            for (EcuDto ecuDto : ecuDtoList) {
                List<SoftwareDto> softwaresSort = new ArrayList<>();
                List<SoftwareDto> softwares = ecuDto.getSoftwares();
                Iterator it = names.iterator();
                while (it.hasNext()) {
                    Object obj = it.next();
                    String type = (String) obj;
                    Iterator<SoftwareDto> it2 = softwares.iterator();
                    while (true) {
                        if (it2.hasNext()) {
                            SoftwareDto softwareDto = it2.next();
                            String softwareType = softwareDto.getSoftwareType();
                            if (softwareType.startsWith(type)) {
                                softwaresSort.add(softwareDto);
                                break;
                            }
                        }
                    }
                }
                for (SoftwareDto softwareDto2 : softwares) {
                    if (!softwaresSort.contains(softwareDto2)) {
                        softwaresSort.add(softwareDto2);
                    }
                }
                ecuDto.setSoftwares(softwaresSort);
                sort.add(ecuDto);
            }
            String ecuInfoSort = JSON.toJSONString(sort);
            LOG.info("swdlSort排序后的ECU列表：{}", ecuInfoSort);
            return ecuInfoSort;
        } catch (Exception e) {
            LOG.error("排序失败", e);
            return ecuInfo;
        }
    }

    public JSONArray swdlSort(Object ecuNames, JSONArray ecuInfo) {
        EcuDto ecuDto;
        try {
            LOG.info("swdlSort排序入参ECU列表：{}", ecuInfo);
            LOG.info("swdlSort排序入参ECU名称列表：{}", ecuNames);
            JSONArray names = new JSONArray();
            if (ecuNames instanceof JSONArray) {
                names = (JSONArray) ecuNames;
            } else if (ecuNames instanceof String) {
                names = JSONObject.parseArray((String) ecuNames);
            }
            List<EcuDto> sort = new ArrayList<>(ecuInfo.size());
            Iterator it = ecuInfo.iterator();
            while (it.hasNext()) {
                Object object = it.next();
                if (object instanceof String) {
                    ecuDto = (EcuDto) JSONObject.parseObject((String) object, EcuDto.class);
                } else {
                    ecuDto = (EcuDto) JSONObject.parseObject(JSON.toJSONString(object), EcuDto.class);
                }
                List<SoftwareDto> softwaresSort = new ArrayList<>();
                List<SoftwareDto> softwares = ecuDto.getSoftwares();
                Iterator it2 = names.iterator();
                while (it2.hasNext()) {
                    Object obj = it2.next();
                    String type = (String) obj;
                    Iterator<SoftwareDto> it3 = softwares.iterator();
                    while (true) {
                        if (it3.hasNext()) {
                            SoftwareDto softwareDto = it3.next();
                            String softwareType = softwareDto.getSoftwareType();
                            if (softwareType.startsWith(type)) {
                                softwaresSort.add(softwareDto);
                                break;
                            }
                        }
                    }
                }
                for (SoftwareDto softwareDto2 : softwares) {
                    if (!softwaresSort.contains(softwareDto2)) {
                        softwaresSort.add(softwareDto2);
                    }
                }
                ecuDto.setSoftwares(softwaresSort);
                sort.add(ecuDto);
            }
            JSONArray ecuInfoSort = JSON.parseArray(JSON.toJSONString(sort));
            LOG.info("swdlSort排序后的ECU列表：{}", ecuInfoSort);
            return ecuInfoSort;
        } catch (Exception e) {
            LOG.error("排序失败", e);
            return ecuInfo;
        }
    }

    public void swdlSortEcu(Object ecuNames, EcuDto ecuDto) {
        try {
            LOG.info("swdlSortEcu排序入参ECU列表：{}", ecuDto);
            LOG.info("swdlSortEcu排序入参ECU名称列表：{}", ecuNames);
            JSONArray names = new JSONArray();
            if (ecuNames instanceof JSONArray) {
                names = (JSONArray) ecuNames;
            } else if (ecuNames instanceof String) {
                names = JSONObject.parseArray((String) ecuNames);
            }
            List<SoftwareDto> softwaresSort = new ArrayList<>();
            List<SoftwareDto> softwares = ecuDto.getSoftwares();
            Iterator it = names.iterator();
            while (it.hasNext()) {
                Object obj = it.next();
                String type = (String) obj;
                Iterator<SoftwareDto> it2 = softwares.iterator();
                while (true) {
                    if (it2.hasNext()) {
                        SoftwareDto softwareDto = it2.next();
                        String softwareType = softwareDto.getSoftwareType();
                        if (softwareType.startsWith(type)) {
                            softwaresSort.add(softwareDto);
                            break;
                        }
                    }
                }
            }
            for (SoftwareDto softwareDto2 : softwares) {
                if (!softwaresSort.contains(softwareDto2)) {
                    softwaresSort.add(softwareDto2);
                }
            }
            ecuDto.setSoftwares(softwaresSort);
            LOG.info("swdlSortEcu排序后的ECU列表：{}", ecuDto);
        } catch (Exception e) {
        }
    }

    public static String bytes2BinStr(byte[] bArray) {
        String outStr = "";
        for (byte b : bArray) {
            int pos = (b & 240) >> 4;
            String outStr2 = outStr + binaryArray[pos];
            int pos2 = b & 15;
            outStr = outStr2 + binaryArray[pos2];
        }
        return outStr;
    }

    public static byte[] hexStr2BinArr(String hexString) {
        int len = hexString.length() / 2;
        byte[] bytes = new byte[len];
        for (int i = 0; i < len; i++) {
            byte high = (byte) (hexStr.indexOf(hexString.charAt(2 * i)) << 4);
            byte low = (byte) hexStr.indexOf(hexString.charAt((2 * i) + 1));
            bytes[i] = (byte) (high | low);
        }
        return bytes;
    }

    public static int hexStr2SignedInt(String hexString) {
        int symbol = 1;
        String binStr = bytes2BinStr(hexStr2BinArr(hexString));
        if (binStr != null && binStr.length() > 1) {
            if (binStr.startsWith("1")) {
                symbol = -1;
            }
            return Integer.parseInt(binStr.substring(1), 2) * symbol;
        }
        return 0;
    }

    public String parseDid(JSONObject ecuInfo) {
        JSONObject output = new JSONObject();
        JSONArray resultList = new JSONArray();
        List<DiagResItemParseResultDto> itemParseResults = new ArrayList<>();
        try {
            String ecuName = ecuInfo.getString(ECU_NAME);
            JSONObject didList = ecuInfo.getJSONObject(DID_VALUE_LIST);
            LOG.info("parseDid入参ECU名称：{}", ecuName);
            LOG.info("parseDid入参DID列表：{}", didList);
            TesterVehicleResponseUtils.handleNegativeRes(String.join("", (CharSequence[]) didList.values().toArray(new String[0])), "22");
            ParamServiceImpl bean = (ParamServiceImpl) SpringUtils.getBean(ParamServiceImpl.class);
            List<DiagResItemGroupDto> didListResponseItem = new ArrayList();
            try {
                didListResponseItem = bean.list(this.vin, "", ecuName);
            } catch (Exception e) {
                String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00086);
                LOG.error("{},diagnosticNumber={},serviceId={},e=", new Object[]{formatMsg, "", "22", e});
            }
            for (DiagResItemGroupDto resItemGroupDto : (List) didListResponseItem.stream().filter(s -> {
                return didList.containsKey(s.getDataIdentifierId());
            }).collect(Collectors.toList())) {
                String result = didList.getString(resItemGroupDto.getDataIdentifierId());
                bean.parseResponseByResItem(itemParseResults, resItemGroupDto, resItemGroupDto.getResponseItemDtoList(), result);
            }
            itemParseResults.forEach(o -> {
                JSONObject js = new JSONObject();
                js.put("dataIdentifierId", o.getDataIdentifierId());
                js.put(o.getName(), o.getValue() + ConstantEnum.EMPTY + o.getUnit());
                resultList.add(js);
            });
            LOG.info("parseDid解析后的ECU列表：{}", itemParseResults);
            output.put(ECU_NAME, ecuName);
            output.put(PARAMETER_RESULT_LIST, resultList);
            return output.toJSONString();
        } catch (Exception e2) {
            LOG.error("parseDid解析失败", e2);
            return "";
        }
    }

    protected Class<?> callJar(String jarPath, String className) throws Exception {
        loadJar(jarPath);
        return Class.forName(className);
    }

    public static void loadJar(String jarPath) throws NoSuchMethodException, SecurityException {
        File jarFile = new File(jarPath);
        Method method = null;
        try {
            method = URLClassLoader.class.getDeclaredMethod("addURL", URL.class);
        } catch (NoSuchMethodException | SecurityException e1) {
            e1.printStackTrace();
        }
        boolean accessible = method.isAccessible();
        try {
            try {
                method.setAccessible(true);
                URLClassLoader classLoader = (URLClassLoader) ClassLoader.getSystemClassLoader();
                URL url = jarFile.toURI().toURL();
                method.invoke(classLoader, url);
                method.setAccessible(accessible);
            } catch (Exception e) {
                e.printStackTrace();
                method.setAccessible(accessible);
            }
        } catch (Throwable th) {
            method.setAccessible(accessible);
            throw th;
        }
    }

    public static <T> T jsonStr2Clazz(String str, Class<T> cls) {
        return (T) ObjectMapperUtils.jsonStr2Clazz(str, cls);
    }

    public void setError(String code, String shortDesc, String componentId, String ecuAddress) {
        if (this.xmlSeq != null) {
            boolean mask = false;
            if (this.maskCodes.contains(code)) {
                mask = true;
            }
            if (StringUtils.isNotBlank(ecuAddress)) {
                shortDesc = shortDesc + "; " + MessageUtils.getMessage("ECU address") + ":" + ecuAddress;
            }
            XmlFault fault = new XmlFault(false, code, shortDesc, componentId, "", "", mask, ecuAddress, "");
            this.xmlSeq.b(fault);
        }
    }

    public static int stringToUnsignedInt(String hex, int system) throws NumberFormatException {
        int result;
        if (hex.contains("0x") || hex.contains("0X")) {
            result = Integer.parseInt(hex.substring(2), 16);
        } else {
            result = Integer.parseInt(hex, system);
        }
        return result;
    }

    public static int bcdToUnsignedInt(String bcd) {
        byte[] b2 = str2Bcd(bcd);
        return bcd2Str(b2);
    }

    public static String binaryToUnsignedInt(String binarySting) throws NumberFormatException {
        int data = Integer.parseInt(binarySting, 2);
        return Integer.toHexString(data);
    }

    public static int binaryStingToHex(String binarySting) {
        return Integer.parseInt(binarySting, 2);
    }

    public static String hexToAscii(String hexStr2) {
        StringBuilder output = new StringBuilder("");
        for (int i = 0; i < hexStr2.length(); i += 2) {
            String str = hexStr2.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }
        return output.toString();
    }

    public static float stringToFloat(String str) {
        return Float.parseFloat(str);
    }

    public static int stringToInt(String str) {
        return Integer.parseInt(str);
    }

    public static long stringToLong(String str) {
        return Long.parseLong(str);
    }

    public static int stringToUnsignedInt(String str) {
        return Integer.parseInt(str);
    }

    public static String reserveFloatDecimal(String format, float value) {
        DecimalFormat fnum = new DecimalFormat(format);
        String formatString = fnum.format(value);
        return formatString;
    }

    public static float hexStringToFloat(String str) {
        Long l = Long.valueOf(Long.parseLong(str, 16));
        return Float.intBitsToFloat(l.intValue());
    }

    public static String floatToHexString(float str) {
        int i = Float.floatToIntBits(str);
        return Integer.toHexString(i);
    }

    public static String asciiToHexString(String asciiStr) {
        char[] chars = asciiStr.toCharArray();
        StringBuilder hex = new StringBuilder();
        for (char ch : chars) {
            hex.append(Integer.toHexString(ch));
        }
        return hex.toString();
    }

    public static String intToHexString(int value, int length) {
        String hexString = Integer.toHexString(value);
        String stringCompletion = stringCompletion(hexString, length * 2);
        return stringCompletion;
    }

    public static String stringCompletion(String str, int length) {
        while (str.length() < length) {
            str = "0" + str;
        }
        return str;
    }

    public static int trunc(double value) {
        int trunc = (int) Math.floor(value);
        return trunc;
    }

    public static int hexStringToInt(String hex, int system) throws NumberFormatException {
        int value;
        if (hex.contains("0x") || hex.contains("0X")) {
            value = Integer.parseInt(hex.substring(2), 16);
        } else {
            value = Integer.parseInt(hex, system);
        }
        String hexString = Integer.toHexString(value);
        int symbol = 1;
        String binStr = bytes2BinStr(hexToByteArray(hexString));
        if (binStr != null && binStr.length() > 1) {
            if (binStr.startsWith("1")) {
                symbol = -1;
            }
            return Integer.parseInt(binStr.substring(1), 2) * symbol;
        }
        return 0;
    }

    public static int binaryStringToInt(String binaryString) {
        int symbol = 1;
        if (binaryString.startsWith("1")) {
            symbol = -1;
        }
        return Integer.parseInt(binaryString.substring(1), 2) * symbol;
    }

    public static boolean judgeByBit(String hex, int byteLocation, int bitLocation) {
        String byteString = hex.substring(byteLocation * 2, (byteLocation * 2) + 2);
        byte[] statusBits = ParseUtils.byteToBitOfArray((byte) Integer.parseInt(byteString, 16));
        if (statusBits[bitLocation] == 1) {
            return true;
        }
        return false;
    }

    public static byte[] hexToByteArray(String inHex) {
        byte[] result;
        int hexlen = inHex.length();
        if (hexlen % 2 == 1) {
            hexlen++;
            result = new byte[hexlen / 2];
            inHex = "0" + inHex;
        } else {
            result = new byte[hexlen / 2];
        }
        int j = 0;
        for (int i = 0; i < hexlen; i += 2) {
            result[j] = hexToByte(inHex.substring(i, i + 2));
            j++;
        }
        return result;
    }

    public static byte hexToByte(String inHex) {
        return (byte) Integer.parseInt(inHex, 16);
    }

    public static String hexString2binaryString(String hexString) {
        StringBuilder res = new StringBuilder();
        for (String s : hexString.split("")) {
            res.append(org.apache.commons.lang3.StringUtils.leftPad(new BigInteger(s, 16).toString(2), 4, '0'));
        }
        return res.toString();
    }

    public static int bcd2Str(byte[] bytes) {
        StringBuffer temp = new StringBuffer(bytes.length * 2);
        for (int i = 0; i < bytes.length; i++) {
            temp.append((int) ((byte) ((bytes[i] & 240) >>> 4)));
            temp.append((int) ((byte) (bytes[i] & 15)));
        }
        String intStr = "0".equalsIgnoreCase(temp.toString().substring(0, 1)) ? temp.toString().substring(1) : temp.toString();
        return Integer.parseInt(intStr);
    }

    public static byte[] str2Bcd(String asc) {
        int j;
        int i;
        int len = asc.length();
        int mod = len % 2;
        if (mod != 0) {
            asc = "0" + asc;
            len = asc.length();
        }
        byte[] bArr = new byte[len];
        if (len >= 2) {
            len /= 2;
        }
        byte[] bbt = new byte[len];
        byte[] abt = asc.getBytes();
        for (int p2 = 0; p2 < asc.length() / 2; p2++) {
            if (abt[2 * p2] >= 48 && abt[2 * p2] <= 57) {
                j = abt[2 * p2] - 48;
            } else if (abt[2 * p2] >= 97 && abt[2 * p2] <= 122) {
                j = (abt[2 * p2] - 97) + 10;
            } else {
                j = (abt[2 * p2] - 65) + 10;
            }
            if (abt[(2 * p2) + 1] >= 48 && abt[(2 * p2) + 1] <= 57) {
                i = abt[(2 * p2) + 1] - 48;
            } else if (abt[(2 * p2) + 1] >= 97 && abt[(2 * p2) + 1] <= 122) {
                i = (abt[(2 * p2) + 1] - 97) + 10;
            } else {
                i = (abt[(2 * p2) + 1] - 65) + 10;
            }
            int k = i;
            int a = (j << 4) + k;
            byte b = (byte) a;
            bbt[p2] = b;
        }
        return bbt;
    }

    public static String stringTransformAscii(String value) {
        StringBuffer sbu = new StringBuffer();
        char[] chars = value.toCharArray();
        for (char c : chars) {
            sbu.append(Integer.toHexString(c));
        }
        return sbu.toString();
    }

    public static String asciiTransHexString(String value) {
        StringBuffer sbu = new StringBuffer();
        String[] chars = value.split(ConstantEnum.COMMA);
        for (String str : chars) {
            sbu.append((char) Integer.parseInt(str));
        }
        return sbu.toString();
    }

    public String getValveBodyAssemblyData(String pCode, String tCode, String vCode) throws Exception {
        LOG.info("更换阀体总成数据入参：{}-{}-{}", new Object[]{pCode, tCode, vCode});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
        String res = this.cloud.v(pCode, tCode, vCode);
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        if ("0".equals(code)) {
            String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
            LOG.info("更换阀体总成数据：{}", data);
            return data;
        }
        String msg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00207) + ":" + msg;
        LOG.error(msg2);
        throw new Exception(msg2);
    }

    public String getValveBodyAssemblyData(String pCode, String tCode, String vCode, String pCodePcm, String tCodePcm, String vCodePcm) throws Exception {
        LOG.info("更换阀体总成数据入参：{}-{}-{}---{}-{}-{}", new Object[]{pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
        String res = this.cloud.b(pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm);
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        if ("0".equals(code)) {
            String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
            LOG.info("更换阀体总成数据：{}", data);
            return data;
        }
        String msg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00207) + ":" + msg;
        LOG.error(msg2);
        throw new Exception(msg2);
    }

    public String updatePcmBind(String pCode, String tCode, String vCode, String pCodePcm, String tCodePcm, String vCodePcm) throws Exception {
        LOG.info("阀体号更新绑定对应的PCM变速器总成号入参：{}-{}-{}---{}-{}-{}", new Object[]{pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
        String res = this.cloud.a(pCode, tCode, vCode, pCodePcm, tCodePcm, vCodePcm, this.userName);
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        if (!"0".equals(code)) {
            String msg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00261);
            LOG.error(msg);
            throw new Exception(msg);
        }
        return "成功";
    }

    public String getReplacePcmAssemblyData(String pCode, String tCode, String vCode) throws Exception {
        LOG.info("更换PCM总成入参：{}-{}-{}", new Object[]{pCode, tCode, vCode});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
        String res = this.cloud.w(pCode, tCode, vCode);
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        if ("0".equals(code)) {
            String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
            LOG.info("更换PCM总成数据：{}", data);
            return data;
        }
        String msg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00208) + ":" + msg;
        LOG.error(msg2);
        throw new Exception(msg2);
    }

    public void resetSocket() {
        try {
            LOG.info("脚本控制重连");
            FdTcpClient fdTcpClient = this.manager.getFdTcpClient(this.vin);
            if (fdTcpClient != null) {
                fdTcpClient.resetSocket2(false);
            }
        } catch (Exception e) {
            LOG.error("重连socket失败", e);
        }
    }

    public void doNotSleep(boolean doNotSleep) {
        SleepUtils sleepUtils = (SleepUtils) SpringUtils.getBean(SleepUtils.class);
        if (doNotSleep) {
            sleepUtils.doNotSleep();
        } else {
            sleepUtils.cancel();
        }
    }

    public void handleMediaData(String s) {
        try {
            JSONArray jsonArray = (JSONArray) JSONPath.eval(s, "$.seqInfo");
            if (!CollectionUtils.isEmpty(jsonArray)) {
                Iterator it = jsonArray.iterator();
                while (it.hasNext()) {
                    Object obj = it.next();
                    JSONObject jsonObject = JSONObject.parseObject(obj.toString());
                    String componentType = jsonObject.getString("ComponentType");
                    Object eval = JSONPath.eval(jsonObject, "$.seqInfo.Input.Src");
                    if (StringUtils.isNotBlank(componentType) && !ObjectUtils.isEmpty(eval)) {
                        String src = eval.toString();
                        this.cloud.E(src, this.userName);
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("诊断序列获取媒体数据失败", e);
        }
    }

    public String handleMediaDataForInit(String s) {
        try {
            JSONObject res = JSONObject.parseObject(s);
            JSONArray jsonArrays = new JSONArray();
            JSONArray jsonArray = (JSONArray) JSONPath.eval(s, "$.seqInfo");
            if (!CollectionUtils.isEmpty(jsonArray)) {
                Iterator it = jsonArray.iterator();
                while (it.hasNext()) {
                    Object obj = it.next();
                    JSONObject jsonObject = (JSONObject) obj;
                    Object componentType = JSONPath.eval(jsonObject, "$.seqInfo.ComponentType");
                    Object eval = JSONPath.eval(jsonObject, "$.seqInfo.Input.Src");
                    if (!ObjectUtils.isEmpty(componentType) && !ObjectUtils.isEmpty(eval) && ("Image".equals(componentType.toString()) || "Video".equals(componentType.toString()) || "Audio".equals(componentType.toString()))) {
                        try {
                            String src = eval.toString();
                            String mediaData = this.cloud.E(src, this.userName);
                            JSONPath.set(jsonObject, "$.seqInfo.Input.Src", mediaData);
                        } catch (Exception e) {
                            LOG.error("诊断序列获取媒体数据失败1", e);
                            JSONPath.set(jsonObject, "$.seqInfo.Input.Src", "");
                        }
                    }
                    jsonArrays.add(jsonObject);
                }
            }
            res.put("seqInfo", jsonArrays);
            return JSON.toJSONString(res);
        } catch (Exception e2) {
            LOG.error("诊断序列获取媒体数据失败", e2);
            return s;
        }
    }

    public static String createNfcKey() {
        StringBuilder sb = new StringBuilder("");
        sb.append("D").append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom());
        sb.append("01");
        sb.append("00");
        sb.append("FFFF");
        sb.append("D").append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom());
        sb.append(getUtcTime());
        sb.append("30");
        sb.append("7").append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom());
        sb.append("D").append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom()).append(getRandom());
        return sb.toString();
    }

    private static String getUtcTime() {
        Date date = new Date();
        long time = date.getTime();
        Date utcDate = new Date(time - TimeZone.getDefault().getRawOffset());
        long timeUtc = utcDate.getTime();
        String res = Long.toHexString(timeUtc / 1000).toUpperCase();
        return res;
    }

    public static String getRandom() {
        Random random = new Random();
        if (random.nextInt(2) == 1) {
            return UserConstants.TYPE_BUTTON;
        }
        return "0";
    }

    public String udsDataParse(String inputJsonStr, String udsId) throws Exception {
        String json;
        String udsData = "";
        try {
            try {
                udsData = udsData(inputJsonStr, null);
                try {
                    Map map = new HashMap();
                    JSONObject inputObj = JSONObject.parseObject(inputJsonStr);
                    String doipInstructionCode = inputObj.getString("Data");
                    String targetAddress = inputObj.getString("Target_address");
                    String respondInfo = inputObj.getString("Respond_info_Result_Data");
                    String did = getDid(doipInstructionCode);
                    List<String> variableList = ObjectMapperUtils.jsonStr2List(respondInfo, String.class);
                    String response = getJsonProperty(udsData, "$.ECU_Response_value.Data_Value");
                    String type = getJsonProperty(udsData, "$.ECU_Response_Type.Data_Value");
                    boolean positive = true;
                    if (!"Positive".equalsIgnoreCase(type)) {
                        positive = false;
                    }
                    if (response.startsWith("5901FF")) {
                        String substring = response.substring(8, 12);
                        map.put("Variable_DID_respond_Result_Parameter_0_Value", Integer.valueOf(Integer.parseInt(substring, 16)));
                    } else if (response.startsWith("5902")) {
                        String substring2 = response.substring(6);
                        List<String> dtcIds = new ArrayList<>();
                        List<String> strList = subStrList(substring2, 8);
                        for (int i = 0; i < strList.size(); i++) {
                            String s = strList.get(i);
                            String dtc = s.substring(0, s.length() - 2);
                            dtcIds.add(dtc);
                            map.put("Variable_DID_respond_Result_Parameter_" + i + "_Value", dtc);
                        }
                        map.put("Variable_DID_respond_Result_Parameter_Len", Integer.valueOf(strList.size()));
                        DtcService bean = (DtcService) SpringUtils.getBean(DtcService.class);
                        String ecuName = getEcuName(targetAddress);
                        String diagnosisPartNumber = "";
                        Object nums = this.manager.getGlobal(GlobalVariableEnum.mb, this.vin) == null ? new HashMap(1) : this.manager.getGlobal(GlobalVariableEnum.mb, this.vin);
                        Map diagnosticNumbers = (Map) nums;
                        if (diagnosticNumbers.containsKey(ecuName)) {
                            diagnosisPartNumber = diagnosticNumbers.get(ecuName).toString();
                        }
                        List<List<String>> dtcListByUds = bean.getDtcListByUds(diagnosisPartNumber, this.vin, ecuName, dtcIds);
                        map.put("Variable_DID_respond_Result_DTC_List", JSON.toJSONString(dtcListByUds));
                    }
                    Map<Integer, DiagResItemParseResultDto> resItemCopy = null;
                    int size = 0;
                    boolean flag = response.startsWith("62") || response.startsWith("71") || response.startsWith("6F") || response.startsWith("6E");
                    if (flag) {
                        JSONArray jsonArray = new JSONArray();
                        jsonArray.add(did);
                        ParamServiceImpl paramService = (ParamServiceImpl) SpringUtils.getBean(ParamServiceImpl.class);
                        String ecuName2 = getEcuName(targetAddress);
                        String diagnosisPartNumber2 = "";
                        Object nums2 = this.manager.getGlobal(GlobalVariableEnum.mb, this.vin) == null ? new HashMap(1) : this.manager.getGlobal(GlobalVariableEnum.mb, this.vin);
                        Map diagnosticNumbers2 = (Map) nums2;
                        if (diagnosticNumbers2.containsKey(ecuName2)) {
                            diagnosisPartNumber2 = diagnosticNumbers2.get(ecuName2).toString();
                        }
                        List<DiagResItemGroupDto> cloudGroups = new ArrayList<>();
                        String serviceId = doipInstructionCode.substring(0, 2);
                        if ("31".equals(serviceId)) {
                            String subfunction = doipInstructionCode.substring(2, 4);
                            json = this.cloud.g(this.vin, diagnosisPartNumber2, subfunction, ecuName2, HttpUtils.getLanguage());
                        } else {
                            json = this.cloud.a(this.vin, diagnosisPartNumber2, serviceId, ecuName2, jsonArray);
                        }
                        cloudGroups.addAll(ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class));
                        List<DiagResItemGroupDto> diagResItemGroupDtos = paramService.getResItemGroupDtos(cloudGroups);
                        List<DiagResItemParseResultDto> resItem = new ArrayList<>();
                        size = diagResItemGroupDtos.size();
                        resItemCopy = new HashMap<>(size);
                        if (!CollectionUtils.isEmpty(diagResItemGroupDtos)) {
                            List<DiagResItemGroupDto> resItemGroupDtos = (List) diagResItemGroupDtos.stream().filter(itemGroupDto -> {
                                return Objects.equals(itemGroupDto.getDataIdentifierId(), did);
                            }).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(resItemGroupDtos)) {
                                for (DiagResItemGroupDto resItemGroupDto : resItemGroupDtos) {
                                    if (resItemGroupDto != null) {
                                        List<DiagResItemDto> responseItemDtoList = resItemGroupDto.getResponseItemDtoList();
                                        if (!CollectionUtils.isEmpty(responseItemDtoList)) {
                                            List<DiagResItemParseResultDto> result = new ArrayList<>();
                                            List<String> didiList = new ArrayList<>();
                                            didiList.add(did);
                                            List<DiagResItemGroupDto> resItemGroupDtos2 = new ArrayList<>();
                                            resItemGroupDtos2.add(resItemGroupDto);
                                            paramService.parseResponseByDid3(this.vin, result, resItemGroupDtos2, response, didiList);
                                            resItem.addAll(result);
                                        }
                                    }
                                }
                            }
                            for (int i2 = 0; i2 < resItem.size(); i2++) {
                                DiagResItemParseResultDto diagResItemParseResultDto = resItem.get(i2);
                                Integer sort = diagResItemParseResultDto.getSort();
                                if (sort != null) {
                                    resItemCopy.put(Integer.valueOf(sort.intValue() - 1), diagResItemParseResultDto);
                                }
                            }
                        }
                    }
                    for (String variable : variableList) {
                        try {
                        } catch (Exception e) {
                            LOG.error("uds{{}处理失败", variable, e);
                        }
                        if (variable.contains("Result_Response")) {
                            if (positive) {
                                map.put(variable, response);
                            } else {
                                TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(response);
                                String code = TesterNegativeResEnum.a(enumByCodeOrStr);
                                if (StringUtils.isNotBlank(code)) {
                                    map.put(variable, "7F" + doipInstructionCode.substring(0, 2) + code);
                                } else {
                                    map.put(variable, "");
                                }
                            }
                        } else if (variable.contains("Result_Identifier")) {
                            map.put(variable, did);
                        } else if (variable.contains("Result_DataRecord")) {
                            if (positive) {
                                String data = getDataRecord(response);
                                map.put(variable, data);
                            } else {
                                map.put(variable, "");
                            }
                        } else if (variable.contains("Result_EcuName")) {
                            map.put(variable, getEcuName(targetAddress));
                        } else if (variable.contains("Result_EcuAddress")) {
                            map.put(variable, targetAddress);
                        } else if (variable.contains("Result_Successful")) {
                            if (positive) {
                                map.put(variable, ConstantEnum.TRUE);
                            } else {
                                map.put(variable, ConstantEnum.FALSE);
                            }
                        } else if (variable.contains("Result_Parameter") && flag) {
                            if (variable.contains("Variable_DID_respond_Result_Parameter_Len")) {
                                map.put(variable, String.valueOf(size));
                            } else {
                                String defaultData = "";
                                String index = variable.substring(variable.indexOf("Parameter_") + 10, variable.lastIndexOf("_"));
                                int parseInt = Integer.parseInt(index);
                                DiagResItemParseResultDto diagResItemParseResultDto2 = resItemCopy.get(Integer.valueOf(parseInt));
                                if (diagResItemParseResultDto2 == null) {
                                    diagResItemParseResultDto2 = new DiagResItemParseResultDto();
                                }
                                if (variable.contains("Title")) {
                                    if (resItemCopy.containsKey(Integer.valueOf(parseInt))) {
                                        DiagResItemParseResultDto itemGroupDto2 = diagResItemParseResultDto2;
                                        defaultData = itemGroupDto2.getName();
                                    }
                                    map.put(variable, defaultData);
                                } else if (variable.contains("Raw")) {
                                    if (resItemCopy.containsKey(Integer.valueOf(parseInt))) {
                                        DiagResItemParseResultDto itemGroupDto3 = diagResItemParseResultDto2;
                                        defaultData = itemGroupDto3.getUnParseValue();
                                    }
                                    map.put(variable, defaultData);
                                } else if (variable.contains("Text")) {
                                    if (resItemCopy.containsKey(Integer.valueOf(parseInt))) {
                                        DiagResItemParseResultDto itemGroupDto4 = diagResItemParseResultDto2;
                                        defaultData = itemGroupDto4.getValue() + itemGroupDto4.getUnit();
                                    }
                                    map.put(variable, defaultData);
                                } else if (variable.contains("Value")) {
                                    if (resItemCopy.containsKey(Integer.valueOf(parseInt))) {
                                        DiagResItemParseResultDto itemGroupDto5 = diagResItemParseResultDto2;
                                        defaultData = itemGroupDto5.getValue();
                                    }
                                    map.put(variable, defaultData);
                                } else if (variable.contains("Unit")) {
                                    if (resItemCopy.containsKey(Integer.valueOf(parseInt))) {
                                        DiagResItemParseResultDto itemGroupDto6 = diagResItemParseResultDto2;
                                        defaultData = itemGroupDto6.getUnit();
                                    }
                                    map.put(variable, defaultData);
                                }
                            }
                        }
                    }
                    this.udsDataMap.put(udsId, map);
                    LOG.error("uds增强解析数据:udsId--{}，data---{}", udsId, map);
                    return udsData;
                } catch (Exception e2) {
                    LOG.error("uds增强数据处理失败", e2);
                    return udsData;
                }
            } catch (Throwable th) {
                return udsData;
            }
        } catch (Exception e3) {
            throw e3;
        }
    }

    private String getEcuName(String targetAddress) {
        Object ecuNameMap;
        String name = "";
        if (StringUtils.isNotBlank(targetAddress) && (ecuNameMap = this.manager.getGlobal("ecuNameMap", this.vin)) != null) {
            Map names = (Map) ecuNameMap;
            if (names.containsKey(this.vin + "address" + targetAddress)) {
                name = names.get(this.vin + "address" + targetAddress).toString();
            }
        }
        return name;
    }

    public String getDid(String request) {
        String did = "";
        if (request.startsWith("22") && (request.length() > 5)) {
            did = request.substring(2, 6);
        } else {
            if (request.startsWith("31") & (request.length() > 7)) {
                did = request.substring(4, 8);
            } else if ((request.startsWith("2F") || request.startsWith("2f")) && request.length() > 5) {
                did = request.substring(2, 6);
            }
        }
        if ((request.startsWith("2E") || request.startsWith("2e")) && request.length() > 5) {
            did = request.substring(2, 6);
        }
        return did;
    }

    public String getDataRecord(String request) {
        String dataRecord = "";
        if (request.startsWith("62") && (request.length() > 6)) {
            dataRecord = request.substring(6);
        } else {
            if (request.startsWith("71") & (request.length() > 8)) {
                dataRecord = request.substring(8);
            } else if ((request.startsWith("6F") || request.startsWith("6f") || request.startsWith("6E") || request.startsWith("6e")) && request.length() > 6) {
                dataRecord = request.substring(6);
            }
        }
        return dataRecord;
    }

    public String getUdsData(String udsId, String variable) {
        Object data;
        try {
            if (this.udsDataMap.containsKey(udsId)) {
                Map map = this.udsDataMap.get(udsId);
                if (map.containsKey(variable) && (data = map.get(variable)) != null) {
                    return data.toString();
                }
                return "";
            }
            return "";
        } catch (Exception e) {
            LOG.error("获取uds{}增强数据{}处理失败", new Object[]{udsId, variable, e});
            return "";
        }
    }

    public String getReplacePcmDataByType(String pCode, String tCode, String vCode, String type) throws Exception {
        LOG.info("更换PCM总成入参：{}-{}-{}", new Object[]{pCode, tCode, vCode});
        if (StringUtils.isBlank(tCode) || tCode.length() != 12) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00212));
        }
        ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
        String res = this.cloud.getReplacePcmDataByType(pCode, tCode, vCode, type);
        String code = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/code");
        String msg = ObjectMapperUtils.findStrByJsonNodeExpr(res, "/msg");
        if ("0".equals(code)) {
            String data = ObjectMapperUtils.findObjByJsonNodeExpr(res, "/data").toString();
            LOG.info("更换PCM总成数据：{}", data);
            return data;
        }
        String msg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00208) + ":" + msg;
        LOG.error(msg2);
        throw new Exception(msg2);
    }

    public String getSwt1PublicKey() throws Exception {
        LOG.info("请求获取SWT1公钥信息，vin={}", this.vin);
        ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
        String certificate = this.cloud.aQ(this.vin);
        Map<String, Object> jsonStr2Map = ObjectMapperUtils.jsonStr2Map(certificate);
        String pkcs12 = (String) jsonStr2Map.get("pkcs12");
        if (!StringUtils.isNotEmpty(pkcs12)) {
            String msg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00266) + ":";
            LOG.error(msg);
            throw new Exception(msg);
        }
        byte[] bytes = Base64Utils.base64DecodeBytes(pkcs12);
        String data = CommonFunctionsUtils.bytesToHex(bytes);
        return data;
    }

    public void closeVehicleTab() throws Exception {
        LOG.info("开始关闭车的标签页，vin={}", this.vin);
        Map map = new HashMap();
        map.put("Close_Vehicle_Tab", ConstantEnum.TRUE);
        map.put("vin", this.vin);
        addSetUi(JSON.toJSONString(map));
    }

    public void pcapFragment() {
        try {
            LOG.info("进入pcapFragment方法vbfswdlFail--【】", Boolean.valueOf(this.vbfswdlFail));
            if (this.vbfswdlFail) {
                FdTcpClient fdTcpClient = getFdTcpClient();
                if (fdTcpClient != null) {
                    fdTcpClient.pcapFragment();
                }
                this.vbfswdlFail = false;
            }
        } catch (Exception e) {
            LOG.info("pcap日志分片异常", e);
        }
    }

    public synchronized void uploadXml() {
        LOG.info("xml日志实时上传ID【{}】", this.requestId);
        if (!this.uploadFlag && this.closeWindowFlag.booleanValue() && StringUtils.isNotBlank(this.requestId)) {
            this.uploadFlag = true;
            TokenManager.getPool().execute(() -> {
                try {
                    SysRequestDTO sysRequestDTO = new SysRequestDTO();
                    sysRequestDTO.setRequestId(this.requestId);
                    sysRequestDTO.setXmlResult(this.xmlSeq.getStatus());
                    SysLogXmlImmediateDTO sysLogXml = new SysLogXmlImmediateDTO();
                    if (this.xmlLogger != null) {
                        sysLogXml.setClientVersion("1.36.0.2");
                        sysLogXml.setAccount(this.xmlLogger.getAccount());
                        sysLogXml.setMac(this.xmlLogger.getTesterMac());
                        sysLogXml.setModel(this.xmlLogger.getTesterModel());
                        sysLogXml.setFileName(this.xmlLogger.getFileName());
                        sysLogXml.setFileTime(this.xmlLogger.getFileTime());
                    }
                    sysLogXml.setSeqRequestId(this.requestId);
                    sysLogXml.setTestName(this.xmlSeq.getName());
                    sysLogXml.setTestTime(this.xmlSeq.getTime());
                    sysLogXml.setTestId(this.xmlSeq.getId());
                    sysLogXml.setTestVer(this.xmlSeq.getVersion());
                    sysLogXml.setTestStatus(this.xmlSeq.getStatus());
                    sysLogXml.setVin(this.vin);
                    List<SysLogXmlFaultCodesImmediateDTO> faultList = new ArrayList<>();
                    Iterator<XmlFault> it = this.xmlSeq.getFaults().iterator();
                    while (it.hasNext()) {
                        XmlFault fault = it.next();
                        SysLogXmlFaultCodesImmediateDTO faultLog = new SysLogXmlFaultCodesImmediateDTO();
                        faultLog.setFaultCode(fault.getCode());
                        faultLog.setFaultDesc(fault.getShortDesc());
                        faultLog.setFaultStatus(fault.getStatus());
                        faultLog.setFaultTime(fault.getFaultTime());
                        faultLog.setLogComponentId(fault.getComponentId());
                        faultLog.setEcuAddress(fault.getEcuAddress());
                        faultLog.setEcuName(fault.getEcuName());
                        faultLog.setReceiveUds(fault.getReceived());
                        faultLog.setSendUds(fault.getSended());
                        faultLog.setUri(fault.getUri());
                        faultList.add(faultLog);
                    }
                    List<SysLogXmlLoggersImmediateDTO> loggerList = new ArrayList<>();
                    Iterator<XmlBuriedPoint> it2 = this.xmlSeq.getPoints().iterator();
                    while (it2.hasNext()) {
                        XmlBuriedPoint point = it2.next();
                        SysLogXmlLoggersImmediateDTO logger = new SysLogXmlLoggersImmediateDTO();
                        logger.setLogComponentId(point.getComponentId());
                        logger.setLogName(point.getLogName());
                        logger.setUri(point.getUri());
                        logger.setLogVal(point.getValue());
                        loggerList.add(logger);
                    }
                    sysRequestDTO.setSysLogXmlImmediateDTO(sysLogXml);
                    sysRequestDTO.setSysLogXmlFaultCodesImmediateDTO(faultList);
                    sysRequestDTO.setSysLogXmlLoggersImmediateDTO(loggerList);
                    this.cloud.a(this.userName, sysRequestDTO);
                } catch (Exception e) {
                    LOG.error("xml日志实时上传失败【{}】", this.requestId, e);
                }
            });
        }
    }

    public String getTenantName() throws Exception {
        String jarPath = AppConfig.getAppDataDir().getAbsolutePath();
        String configFilePath = jarPath + File.separator + "config" + File.separator + "application.properties";
        Properties properties = new Properties();
        try {
            FileInputStream fileInputStream = new FileInputStream(configFilePath);
            Throwable th = null;
            try {
                try {
                    properties.load(fileInputStream);
                    String property = properties.getProperty("tester.tenantName");
                    if (fileInputStream != null) {
                        if (0 != 0) {
                            try {
                                fileInputStream.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            fileInputStream.close();
                        }
                    }
                    return property;
                } finally {
                }
            } finally {
            }
        } catch (IOException e) {
            LOG.error("获取租户数据失败，e={}", e);
            return null;
        }
    }

    public String getSeqResultFinalString() {
        if (this.xmlSeq != null) {
            LOG.info("xml结果获取getSeqResultFinalString={}", this.xmlSeq.getStatus());
            return this.xmlSeq.getStatus();
        }
        return "";
    }

    public void setSeqResultFinalString(String seqResultFinalString) {
        LOG.info("xml结果设置seqResultFinalString={}", seqResultFinalString);
        if (this.xmlSeq != null) {
            this.xmlSeq.setStatus(seqResultFinalString);
        }
    }

    public static String HexStringToAscii(String hexStr2) throws NumberFormatException {
        StringBuilder asciiBuilder = new StringBuilder();
        for (int i = 0; i < hexStr2.length(); i += 2) {
            int decimalValue = Integer.parseInt(hexStr2.substring(i, i + 2), 16);
            char character = (char) decimalValue;
            asciiBuilder.append(character);
        }
        return asciiBuilder.toString();
    }

    public boolean authenticationConfiguration(String address) throws Exception {
        String udsData = udsData(getUdsString(address, "2908"));
        String res = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
        if (!res.startsWith("6908")) {
            throw new Exception("2908" + MessageUtils.getMessage("Failed, received response") + "：{}" + res);
        }
        String cerHexString = getCerHexString();
        String random = getRandom(32);
        String req = "290100" + intToHexString(cerHexString.length(), 2) + cerHexString + "0020" + random;
        String udsData2 = udsData(getUdsString(address, req));
        String res2 = JSONPath.eval(udsData2, "$.ECU_Response_value.Data_Value").toString();
        String challengeServerLength = res2.substring(6, 10);
        String challengeServer = res2.substring(10, Integer.parseInt(challengeServerLength, 16));
        Signature signature = Signature.getInstance("SHA256withECDSA");
        signature.initSign(null);
        String proofOfOwnershipClient = challengeServer + random;
        signature.update(hexStringToByteArray(proofOfOwnershipClient));
        String proofOfOwnershipClientSign = bytesToHex(signature.sign());
        String req2 = "2903" + intToHexString(proofOfOwnershipClientSign.length(), 2) + proofOfOwnershipClientSign + "0000";
        String udsData3 = udsData(getUdsString(address, req2));
        String res3 = JSONPath.eval(udsData3, "$.ECU_Response_value.Data_Value").toString();
        if (!res3.startsWith("6903120000")) {
            throw new Exception("2903" + MessageUtils.getMessage("Failed, received response") + "：{}" + res3);
        }
        String udsData4 = udsData(getUdsString(address, "2900"));
        String res4 = JSONPath.eval(udsData4, "$.ECU_Response_value.Data_Value").toString();
        if (!res4.startsWith("690010")) {
            throw new Exception("2900" + MessageUtils.getMessage("Failed, received response") + "：{}" + res4);
        }
        return true;
    }

    public static String getRandom(int length) {
        SecureRandom random = new SecureRandom();
        byte[] randomBytes = new byte[length];
        random.nextBytes(randomBytes);
        StringBuilder sb = new StringBuilder();
        for (byte b : randomBytes) {
            sb.append(String.format("%02X", Byte.valueOf(b)));
        }
        String randomHex = sb.toString();
        System.out.println(randomHex);
        return randomHex;
    }

    private static String getCerHexString() throws IOException {
        byte[] fileBytes = readFileToByteArray(new File("D:\\xmls\\Supplier.cer"));
        String hexString = bytesToHex(fileBytes);
        return hexString;
    }

    private static byte[] readFileToByteArray(File file) throws IOException {
        FileInputStream fis = new FileInputStream(file);
        byte[] data = new byte[(int) file.length()];
        fis.read(data);
        fis.close();
        return data;
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", Byte.valueOf(b)));
        }
        return sb.toString();
    }

    private static byte[] hexStringToByteArray(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4) + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }

    public static String getCertificateData() throws Exception {
        return TlsUtils.getCertificateData();
    }

    public static String ecdsaWithSha256(String challengeServer) throws Exception {
        return TlsUtils.ecdsaWithSha256(challengeServer);
    }

    public String ocspRequest() throws Exception {
        return ocspRequest("");
    }

    public String ocspRequest(String region) throws Exception {
        LOG.info("诊断序列调用ocspRequest");
        String verifyCert = this.cloud.aS(region);
        byte[] bytes = org.springframework.util.Base64Utils.decodeFromUrlSafeString(verifyCert);
        String ocsp = bytesToHex(SingletonManager.decryptOcsp(bytes));
        String res = TlsUtils.intToPaddedHex(ocsp.length() / 2, 2) + ocsp;
        return res;
    }

    public String checkNetwork(int count) {
        String url = GndsWebSocket.getCloud();
        LOG.info("诊断序列获取网络状态入参count：{},url", Integer.valueOf(count), url);
        NetQualityLevel netQualityLevel = NetQualityUtils.checkNetworkLevel(url, count);
        String jsonString = netQualityLevel.toString();
        LOG.info("诊断序列获取网络状态出参：{}", jsonString);
        return jsonString;
    }

    public String checkNetwork() {
        return checkNetwork(3);
    }

    public void throwException() throws Exception {
        throw new Exception(MessageUtils.getMessage("SeqException"));
    }

    public void logHtmlChapter(String title) {
        LOG.info("诊断序列创建logHtmlChapter章入参：{}", title);
        TestOrderDTO testOrder = this.manager.getTestOrder(this.vin);
        if (testOrder != null) {
            HtmlChapterDTO htmlChapterDTO = getHtmlChapter(testOrder, title);
            List<HtmlChapterDTO> chapters = testOrder.getChapters();
            htmlChapterDTO.setTitle(title);
            chapters.add(htmlChapterDTO);
            testOrder.setChapters(chapters);
        }
    }

    public void logHtmlTable(String chapterTitle, String tableTitle, JSONArray headers) {
        LOG.info("诊断序列创建logHtmlTable表入参：{},{},{}", new Object[]{chapterTitle, tableTitle, headers});
        TestOrderDTO testOrder = this.manager.getTestOrder(this.vin);
        if (testOrder != null) {
            HtmlTableDTO htmlTable = getHtmlTable(testOrder, chapterTitle, tableTitle);
            htmlTable.setTitle(tableTitle);
            List<String> headList = new ArrayList<>();
            Iterator it = headers.iterator();
            while (it.hasNext()) {
                Object object = it.next();
                headList.add(object.toString());
            }
            htmlTable.setHeaders(headList);
            getHtmlChapter(testOrder, chapterTitle).getTables().add(htmlTable);
        }
    }

    public void logHtmlLine(String chapterTitle, String tableTitle, String line) {
        LOG.info("诊断序列创建logHtmlLine行入参：{},{},{}", new Object[]{chapterTitle, tableTitle, line});
        TestOrderDTO testOrder = this.manager.getTestOrder(this.vin);
        if (testOrder != null) {
            HtmlTableDTO htmlTable = getHtmlTable(testOrder, chapterTitle, tableTitle);
            List<String> lines = htmlTable.getLines();
            if (lines == null) {
                lines = new ArrayList();
            }
            lines.add(line);
            htmlTable.setLines(lines);
        }
    }

    public void logHtmlRow(String chapterTitle, String tableTitle, JSONArray contents, String result) {
        LOG.info("诊断序列创建logHtmlRow行入参：{},{},{},{}", new Object[]{chapterTitle, tableTitle, contents, result});
        TestOrderDTO testOrder = this.manager.getTestOrder(this.vin);
        if (testOrder != null) {
            HtmlTableDTO htmlTable = getHtmlTable(testOrder, chapterTitle, tableTitle);
            List<HtmlRowDTO> rows = htmlTable.getRows();
            if (rows == null) {
                rows = new ArrayList();
            }
            HtmlRowDTO htmlRowDTO = new HtmlRowDTO();
            List<String> contentList = new ArrayList<>();
            Iterator it = contents.iterator();
            while (it.hasNext()) {
                Object object = it.next();
                contentList.add(object.toString());
            }
            htmlRowDTO.setContents(contentList);
            htmlRowDTO.setResult(result);
            rows.add(htmlRowDTO);
            htmlTable.setRows(rows);
        }
    }

    public HtmlChapterDTO getHtmlChapter(TestOrderDTO testOrder, String title) {
        LOG.info("诊断序列获取html章节入参：{}", title);
        List<HtmlChapterDTO> chapters = testOrder.getChapters();
        if (chapters == null) {
            testOrder.setChapters(new ArrayList<>());
        } else {
            for (HtmlChapterDTO htmlChapter : chapters) {
                if (title.equals(htmlChapter.getTitle())) {
                    return htmlChapter;
                }
            }
        }
        return new HtmlChapterDTO();
    }

    public HtmlTableDTO getHtmlTable(TestOrderDTO testOrder, String chapterTitle, String tableTitle) {
        LOG.info("诊断序列获取getHtmlTable入参：{},{}", chapterTitle, tableTitle);
        List<HtmlChapterDTO> chapters = testOrder.getChapters();
        if (chapters == null) {
            testOrder.setChapters(new ArrayList());
        } else {
            for (HtmlChapterDTO htmlChapter : chapters) {
                if (chapterTitle.equals(htmlChapter.getTitle())) {
                    List<HtmlTableDTO> tables = htmlChapter.getTables();
                    if (CollectionUtils.isEmpty(tables)) {
                        htmlChapter.setTables(new ArrayList());
                    } else {
                        for (HtmlTableDTO htmlTable : tables) {
                            if (tableTitle.equals(htmlTable.getTitle())) {
                                return htmlTable;
                            }
                        }
                    }
                }
            }
        }
        return new HtmlTableDTO();
    }

    public String getEcuAddressList() {
        TesterOrderParamDTO testerOrderParam;
        TestOrderDTO testOrder = this.manager.getTestOrder(this.vin);
        List<String> ecuAddressList = new ArrayList<>();
        if (testOrder != null && (testerOrderParam = testOrder.getTesterOrderParam()) != null) {
            List<CalculationEcuDTO> ecus = testerOrderParam.getEcus();
            if (!CollectionUtils.isEmpty(ecus)) {
                for (CalculationEcuDTO ecuDTO : ecus) {
                    String ecuAddress = ecuDTO.getEcuAddress();
                    ecuAddressList.add(ecuAddress);
                }
            }
        }
        String ecus2 = JSON.toJSONString(ecuAddressList);
        LOG.info("诊断序列获取EcuAddressList结果：{}", ecus2);
        return ecus2;
    }

    public String getSddbDtc(String diagnosisPartNum) throws Exception {
        List<DiaDtcDTO> dtcs = new ArrayList<>();
        LOG.info("诊断序列获取Sddb的Dtc列表参数：{}", diagnosisPartNum);
        try {
            ThreadLocalUtils.CURRENT_USER_NAME.set(this.userName);
            SddbService bean = (SddbService) SpringUtils.getBean(SddbService.class);
            dtcs = bean.getDtcList(diagnosisPartNum);
        } catch (Exception e) {
            LOG.error("获取Sddb的Dtc列表异常", e);
        }
        String jsonString = JSON.toJSONString(dtcs);
        LOG.info("诊断序列获取Sddb的Dtc列表参数：{}，结果：{}", diagnosisPartNum, jsonString);
        return jsonString;
    }

    public String getDtcReadValue(String dtc) {
        DtcService bean = (DtcService) SpringUtils.getBean(DtcService.class);
        String dtcReadValue = bean.getDtcReadValue(dtc);
        LOG.info("诊断序列获取DtcReadValue参数：{}，结果：{}", dtc, dtcReadValue);
        return dtcReadValue;
    }

    public synchronized String createServeSocket(JSONArray addressList, String uds) throws Exception {
        LOG.info("诊断序列创建服务端socket入参addressList-{},uds:{}:", addressList, uds);
        String res = ConstantEnum.FALSE;
        if (!CollectionUtils.isEmpty(addressList)) {
            Iterator it = addressList.iterator();
            while (it.hasNext()) {
                Object address = it.next();
                res = createServeSocket(address.toString(), uds);
            }
        }
        return res;
    }

    public synchronized String createServeSocket(String address, String uds) throws Exception {
        LOG.info("诊断序列创建服务端socket");
        String res = ConstantEnum.FALSE;
        FdTcpClient fdTcpClient = this.manager.getFdTcpClient(this.vin);
        if ("3101091A".equals(uds)) {
            try {
                String localIp = fdTcpClient.getLocalIp();
                LOG.info("诊断序列创建服务端socket:localIP:{}", localIp);
                udsData(getUdsString(address, "3101091A" + fdTcpClient.getLocalIp()));
                FdTcpClientServer fdTcpClientServer = new FdTcpClientServer(localIp, 3712, this.userName);
                fdTcpClientServer.setAddress(address);
                fdTcpClientServer.setFdTcpClient(fdTcpClient);
                fdTcpClientServer.connectDoip(this.vin, true);
                fdTcpClient.setSocketByAddress(address, fdTcpClientServer);
                res = ConstantEnum.TRUE;
            } catch (Exception e) {
                LOG.error("创建FdTcpClientServer失败", e);
            }
        } else {
            try {
                if ("31010249".equals(uds)) {
                    try {
                        udsData(getUdsString(address, uds));
                        Thread.sleep(2000L);
                        fdTcpClient.removeSocketByAddress(address);
                    } catch (Exception e2) {
                        LOG.error("关闭FdTcpClientServer失败", e2);
                        Thread.sleep(2000L);
                        fdTcpClient.removeSocketByAddress(address);
                    }
                }
            } catch (Throwable th) {
                Thread.sleep(2000L);
                fdTcpClient.removeSocketByAddress(address);
                throw th;
            }
        }
        return res;
    }

    public static String getNowDate() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss.SSS");
        return LocalDateTime.now().format(formatter);
    }

    public static String dsaLogCommand(String command) {
        return DoipUtil.getDsaLogCommand(command);
    }

    public String vehiclePortrait(String vin) throws Exception {
        LOG.info("诊断序列请求ORT数据入参vin:{},", vin);
        return this.cloud.vehiclePortrait(vin, "");
    }

    public String vehiclePortrait(String vin, String remark) throws Exception {
        LOG.info("诊断序列请求ORT数据入参vin:{},remark:{}", vin, remark);
        return this.cloud.vehiclePortrait(vin, remark);
    }

    public boolean isResultSetBySeq() {
        if (this.xmlSeq == null) {
            return false;
        }
        return this.xmlSeq.isResultSetBySeq();
    }
}
