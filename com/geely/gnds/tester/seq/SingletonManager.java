package com.geely.gnds.tester.seq;

import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSON;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.xml.XmlSeq;
import com.geely.gnds.dsa.component.SoftwareDownload;
import com.geely.gnds.dsa.dto.RunScriptDTO;
import com.geely.gnds.dsa.dto.TestOrderDTO;
import com.geely.gnds.tester.dto.CertResultDTO;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.security.CloudApiRsaUtils;
import java.io.File;
import java.io.IOException;
import java.net.Socket;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;

/* loaded from: SingletonManager.class */
public class SingletonManager {
    private volatile Object lockForLockMap = new Object();
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(SingletonManager.class);
    private static final SingletonManager INSTANCE = new SingletonManager();
    private static Map<String, FdTcpClient> fdTcpClientMap = new ConcurrentHashMap();
    private static Map<String, VehicleDto> vehicleMap = new ConcurrentHashMap();
    private static Map<String, Map<String, Object>> globalParamMap = new ConcurrentHashMap();
    private static Map<String, XmlSeq> readParamXmlSeqMap = new ConcurrentHashMap();
    private static Map<String, XmlSeq> activeXmlSeqMap = new ConcurrentHashMap();
    private static Map<String, RunScriptDTO> runScripts = new ConcurrentHashMap();
    private static Map<String, Long> vehicleConnectedTimeMap = new ConcurrentHashMap();
    private static Vector<String> logUse = new Vector<>();
    private static Long queryTime = null;
    private static Map<String, Object> lockMap = new ConcurrentHashMap();
    private static Map<String, Set<String>> vehiclesBindUser = new ConcurrentHashMap();
    private static Map<String, Boolean> tryConnectMap = new ConcurrentHashMap();
    private static Map<String, Class<?>> seqCompileMap = new ConcurrentHashMap();
    private static Map<String, String> vinContrastMap = new ConcurrentHashMap();
    private static Map<String, SoftwareDownload> softwareDownloadMap = new ConcurrentHashMap();
    private static CertResultDTO cert = null;
    private static Map<String, TestOrderDTO> testOrderMap = new ConcurrentHashMap();

    private SingletonManager() {
    }

    public static SingletonManager getInstance() {
        return INSTANCE;
    }

    public static Long getQueryTime() {
        return queryTime;
    }

    public static void setQueryTime(Long queryTime2) {
        queryTime = queryTime2;
    }

    public static void addVehicleConnectedTime(String vin, Long time) {
        vehicleConnectedTimeMap.put(vin, time);
    }

    public static void removeVehicleConnectedTime(String vin) {
        if (vehicleConnectedTimeMap.containsKey(vin)) {
            vehicleConnectedTimeMap.remove(vin);
        }
    }

    public static Map<String, Long> vehicleConnectedTimeMap() {
        return vehicleConnectedTimeMap;
    }

    public void addLogUse(String path) {
        if (!logUse.contains(path)) {
            logUse.add(path);
        }
    }

    public void removeLogUse(String path) {
        if (logUse.contains(path)) {
            logUse.remove(path);
        }
    }

    public boolean isLogUse(String path) {
        Iterator<String> it = logUse.iterator();
        while (it.hasNext()) {
            String name = it.next();
            if (path.contains(name)) {
                return true;
            }
        }
        return false;
    }

    public void addLock(String vin) {
        synchronized (this.lockForLockMap) {
            if (!lockMap.containsKey(vin)) {
                lockMap.put(vin, new Object());
            }
        }
    }

    public void tryConnect(String vin) {
        if (tryConnectMap.containsKey(vin)) {
            tryConnectMap.remove(vin);
        }
        tryConnectMap.put(vin, true);
        logger.info("tryConnect为true");
    }

    public void tryConnectNot(String vin) {
        if (tryConnectMap.containsKey(vin)) {
            tryConnectMap.remove(vin);
        }
        tryConnectMap.put(vin, false);
        logger.info("tryConnect为false");
    }

    public boolean getTryConnect(String vin) {
        if (tryConnectMap.containsKey(vin)) {
            return tryConnectMap.get(vin).booleanValue();
        }
        return true;
    }

    public Object getLock(String vin) {
        synchronized (this.lockForLockMap) {
            if (lockMap.containsKey(vin)) {
                return lockMap.get(vin);
            }
            Object lock = new Object();
            lockMap.put(vin, lock);
            return lock;
        }
    }

    public void removeLock(String vin) {
        synchronized (this.lockForLockMap) {
            if (!lockMap.containsKey(vin)) {
                lockMap.remove(vin);
            }
        }
    }

    public void addVehiclesBindUser(String username, String vin) {
        Set<String> vehicles = vehiclesBindUser.get(username);
        if (CollectionUtils.isEmpty(vehicles)) {
            vehicles = new LinkedHashSet();
        }
        vehicles.add(vin);
        vehiclesBindUser.put(username, vehicles);
    }

    public void removeVehiclesByUser(String username, String vin) {
        Set<String> vehicles = vehiclesBindUser.get(username);
        if (CollectionUtils.isEmpty(vehicles) || !vehicles.contains(vin)) {
            return;
        }
        vehicles.remove(vin);
        vehiclesBindUser.put(username, vehicles);
    }

    public Set<String> getVehicles(String username) {
        Optional optionalOfNullable = Optional.ofNullable(username);
        Map<String, Set<String>> map = vehiclesBindUser;
        map.getClass();
        return (Set) optionalOfNullable.map((v1) -> {
            return r1.get(v1);
        }).orElse(new HashSet());
    }

    public void addFdTcpClient(String vin, FdTcpClient client) {
        fdTcpClientMap.put(vin, client);
    }

    public FdTcpClient getFdTcpClient(String vin) {
        if (StringUtils.isBlank(vin)) {
            return null;
        }
        return fdTcpClientMap.get(vin);
    }

    public FdTcpClient getFdTcpClientAndDsa(String vin) {
        return fdTcpClientMap.get(vin);
    }

    public void addReadParamXmlSeq(String vin, XmlSeq xmlSeq) {
        readParamXmlSeqMap.put(vin, xmlSeq);
    }

    public XmlSeq getReadParamXmlSeq(String vin) {
        return readParamXmlSeqMap.get(vin);
    }

    public void addActiveXmlSeqMap(String vin, XmlSeq xmlSeq) {
        activeXmlSeqMap.put(vin, xmlSeq);
    }

    public XmlSeq getActiveXmlSeqMap(String vin) {
        return activeXmlSeqMap.get(vin);
    }

    public void removeFdTcpClient(String vin) throws IOException {
        if (fdTcpClientMap.containsKey(vin)) {
            FdTcpClient client = fdTcpClientMap.remove(vin);
            Socket socket = client.getSocket();
            if (socket != null) {
                try {
                    socket.close();
                } catch (IOException e) {
                    logger.error(e.getMessage());
                }
            }
        }
    }

    public void addVehicle(String vin, VehicleDto vehicle) {
        vehicleMap.put(vin, vehicle);
    }

    public Map<String, Object> getGlobalParam(String key) {
        Map<String, Object> map = globalParamMap.get(key);
        if (map == null) {
            map = new ConcurrentHashMap(16);
            addGlobalParam(key, map);
        }
        return map;
    }

    public void addGlobalParam(String key, Map<String, Object> map) {
        globalParamMap.put(key, map);
    }

    public VehicleDto getVehicle(String vin) {
        return vehicleMap.get(vin);
    }

    public void removeVehicle(String vin) {
        vehicleMap.remove(vin);
    }

    public void exitApp() {
        for (FdTcpClient client : fdTcpClientMap.values()) {
            client.closeDoip();
        }
    }

    public Object getGlobal(String name, String vin) {
        Map<String, Object> globalParam = getGlobalParam(vin);
        Object value = globalParam.get(name);
        return value;
    }

    public Object getGlobalStringDefaultNull(String name, String vin) {
        Map<String, Object> globalParam = getGlobalParam(vin);
        if (globalParam.containsKey(name)) {
            return globalParam.get(name);
        }
        return "";
    }

    public String getGlobalString(String name, String vin) {
        Map<String, Object> globalParam = getGlobalParam(vin);
        Object value = globalParam.get(name);
        if (value instanceof String) {
            return (String) value;
        }
        return JSON.toJSONString(value);
    }

    public void setGlobal(String name, Object value, String vin) {
        Map<String, Object> globalParam = getGlobalParam(vin);
        if (value != null) {
            globalParam.put(name, value);
        }
    }

    public void cleanGlobal(String vin) {
        globalParamMap.remove(vin);
    }

    public void addSeqProcessCount(String vin) {
        Object count = getGlobal("seqProcessCount", vin);
        if (!ObjectUtils.isEmpty(count)) {
            AtomicInteger seqProcessCount = (AtomicInteger) count;
            int i = seqProcessCount.incrementAndGet();
            logger.info("执行参数或激活接口;数量加1当前普通脚本计数器数量为：" + i);
            return;
        }
        logger.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00074));
    }

    public void reduceSeqProcessCount(String vin) {
        Object count = getGlobal("seqProcessCount", vin);
        if (!ObjectUtils.isEmpty(count)) {
            AtomicInteger seqProcessCount = (AtomicInteger) count;
            int i = seqProcessCount.decrementAndGet();
            logger.info("执行参数或激活接口;数量减1当前普通脚本计数器数量为：" + i);
            return;
        }
        logger.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00074));
    }

    public static Map<String, VehicleDto> getVehicleMap() {
        return vehicleMap;
    }

    public static Map<String, FdTcpClient> getFdTcpClientMap() {
        return fdTcpClientMap;
    }

    public static Boolean hasVehicleConnected() {
        Set<String> strings = fdTcpClientMap.keySet();
        if (CollectionUtils.isEmpty(strings)) {
            return false;
        }
        return true;
    }

    public static boolean isCompile(String name) {
        if (seqCompileMap.containsKey(name)) {
            return true;
        }
        return false;
    }

    public static void setSeqCompileClass(String name, Class<?> aClass) {
        seqCompileMap.put(name, aClass);
    }

    public static Class<?> getSeqCompileClass(String name) {
        return seqCompileMap.get(name);
    }

    public static void addVinContrast(String oldVin, String vin) {
        if (StringUtils.isNotBlank(vin) && StringUtils.isNotBlank(oldVin)) {
            vinContrastMap.put(vin, oldVin);
        }
    }

    public static void removeVinContrast(String vin) {
        if (vinContrastMap.containsKey(vin)) {
            vinContrastMap.remove(vin);
        }
    }

    public static String getContrastVin(String oldVin) {
        if (vinContrastMap.containsValue(oldVin)) {
            for (Map.Entry<String, String> entry : vinContrastMap.entrySet()) {
                if (Objects.equals(entry.getValue(), oldVin)) {
                    return entry.getKey();
                }
            }
        }
        return oldVin;
    }

    public Map<String, FdTcpClient> getFdTcpClients() {
        return fdTcpClientMap;
    }

    public void addSoftwareDownload(String vin, SoftwareDownload softwareDownload) {
        softwareDownloadMap.put(vin, softwareDownload);
    }

    public SoftwareDownload getSoftwareDownload(String vin) {
        if (softwareDownloadMap.containsKey(vin)) {
            return softwareDownloadMap.get(vin);
        }
        return null;
    }

    public void removeSoftwareDownload(String vin) {
        if (softwareDownloadMap.containsKey(vin)) {
            softwareDownloadMap.remove(vin);
        }
    }

    public void addScript(String key, RunScriptDTO runScriptDTO) {
        if (StringUtils.isNotBlank(key)) {
            runScripts.put(key, runScriptDTO);
        }
    }

    public RunScriptDTO getRunScript(String key) {
        if (runScripts.containsKey(key)) {
            return runScripts.get(key);
        }
        return null;
    }

    public static String getCertContent() {
        return cert.getCertContent();
    }

    public static byte[] getCert() throws Exception {
        byte[] certByte = Base64Utils.decodeFromUrlSafeString(cert.getCertContent());
        byte[] keyBytes = Base64Utils.decodeFromUrlSafeString(cert.getAesEncryptionKey());
        byte[] aesKey = CloudApiRsaUtils.oU.decrypt(keyBytes, KeyType.PrivateKey);
        AES aes = new AES(aesKey);
        byte[] decrypt = aes.decrypt(certByte);
        return Base64Utils.decode(decrypt);
    }

    public static byte[] getPrivateKey() throws Exception {
        byte[] certByte = Base64Utils.decodeFromUrlSafeString(cert.getPrivateKey());
        byte[] keyBytes = Base64Utils.decodeFromUrlSafeString(cert.getAesEncryptionKey());
        byte[] aesKey = CloudApiRsaUtils.oU.decrypt(keyBytes, KeyType.PrivateKey);
        AES aes = new AES(aesKey);
        byte[] decrypt = aes.decrypt(certByte);
        return decrypt;
    }

    public static byte[] decryptOcsp(byte[] ocsp) {
        byte[] keyBytes = Base64Utils.decodeFromUrlSafeString(cert.getAesEncryptionKey());
        byte[] aesKey = CloudApiRsaUtils.oU.decrypt(keyBytes, KeyType.PrivateKey);
        AES aes = new AES(aesKey);
        return aes.decrypt(ocsp);
    }

    public static void setCert(CertResultDTO cert2) {
        cert = cert2;
    }

    public void addTestOrder(String vin, TestOrderDTO testOrderDTO) {
        testOrderMap.put(vin, testOrderDTO);
    }

    public TestOrderDTO getTestOrder(String vin) {
        if (StringUtils.isBlank(vin)) {
            return null;
        }
        return testOrderMap.get(vin);
    }

    public void removeTestOrder(String vin) {
        if (testOrderMap.containsKey(vin)) {
            testOrderMap.remove(vin);
        }
    }

    public static byte[] getTlsCert() throws Exception {
        return FileUtil.readBytes(new File("./config/PROD.pem"));
    }
}
