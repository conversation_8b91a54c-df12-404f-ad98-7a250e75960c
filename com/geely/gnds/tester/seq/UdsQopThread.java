package com.geely.gnds.tester.seq;

import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.text.MessageFormat;
import java.util.concurrent.CountDownLatch;
import org.slf4j.LoggerFactory;

/* loaded from: UdsQopThread.class */
public class UdsQopThread extends Thread {
    private volatile Object lock;
    private static final long TIME_SLEEP = 10;
    private static final String NAME_FORMAT = "QOP_{0}_{1}";
    private volatile boolean closing;
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(UdsQopThread.class);
    private final CountDownLatch latch;
    protected volatile String udsData;
    private boolean isWaiting;

    public UdsQopThread(CountDownLatch latch, String vin, int num) {
        super(MessageFormat.format(NAME_FORMAT, vin, Integer.valueOf(num)));
        this.lock = new Object();
        this.closing = false;
        this.isWaiting = false;
        this.latch = latch;
    }

    protected void countDown() {
        this.latch.countDown();
    }

    public void close() {
        this.closing = true;
        notify4Data();
    }

    public void setUsdData(String inputJsonStr) {
        this.udsData = inputJsonStr;
        notify4Data();
    }

    public boolean isWaiting() {
        return this.isWaiting;
    }

    private void debug(String msg) {
        if (logger.isDebugEnabled()) {
            logger.debug(msg);
        }
    }

    protected final void wait4Data() {
        debug(getName() + "等待新Q操作指令！");
        try {
            debug(getName() + "Q操作lock.wait()前" + this.udsData);
            synchronized (this.lock) {
                this.isWaiting = true;
                this.lock.wait();
                this.isWaiting = false;
            }
            debug(getName() + "Q操作lock.wait()后" + this.udsData);
        } catch (InterruptedException e) {
            logger.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00077), e);
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    protected final void notify4Data() {
        if (this.closing) {
            debug(getName() + "正在关闭，最后的Q操作指令：" + this.udsData);
        } else {
            debug(getName() + "接收到新的Q操作指令" + this.udsData);
        }
        synchronized (this.lock) {
            debug(getName() + "Q操作lock.notify()前" + this.udsData);
            this.lock.notify();
            debug(getName() + "Q操作lock.notify()后" + this.udsData);
        }
    }
}
