package com.geely.gnds.tester.cache;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.common.LogFileReslove;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.servlet.ServletContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.web.context.WebApplicationContext;

@Component
/* loaded from: TokenManager.class */
public class TokenManager {

    @Autowired
    private Cloud cloud;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private LogFileReslove logFileReslove;
    private static Long testerTenantCode;
    private static final Logger log = LoggerFactory.getLogger(TokenManager.class);
    private static String testerId = "";
    private static String secret = "";
    private static Map<String, String> secretMap = new ConcurrentHashMap();
    private static Map<String, String> nameTokenMap = new ConcurrentHashMap();

    public void init(String mac) {
        try {
            log.info("执行init操作---------------------");
            ServletContext servletContext = this.webApplicationContext.getServletContext();
            log.info("获取testerId:" + mac);
            servletContext.setAttribute("testerID", mac);
            setTesterId(mac);
            servletContext.setAttribute("testerToken", "");
            servletContext.setAttribute(Constants.TOKEN, "");
            String token = this.cloud.getToken();
            servletContext.setAttribute("testerToken", token);
            log.info("获取testerToken成功");
            this.logFileReslove.reslove();
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00008), e);
        } finally {
            TesterLoginUtils.start();
        }
    }

    public static ThreadPoolTaskExecutor getPool() {
        return (ThreadPoolTaskExecutor) SpringUtils.getBean(ThreadPoolTaskExecutor.class);
    }

    private static String getMacAddress(InetAddress inetAddress) throws Exception {
        byte[] mac = NetworkInterface.getByInetAddress(inetAddress).getHardwareAddress();
        StringBuffer sb = new StringBuffer();
        for (byte b : mac) {
            String s = Integer.toHexString(b & 255);
            sb.append(s.length() == 1 ? 0 + s : s);
        }
        return sb.toString().toUpperCase();
    }

    public static String getSecret() {
        return secret;
    }

    public static void setSecret(String s) {
        secret = s;
    }

    public static String getTesterId() {
        return testerId;
    }

    public static void setTesterId(String testerId2) {
        testerId = testerId2;
    }

    public static String getSecretMap(String username) {
        return secretMap.get(username);
    }

    public static void setSecretMap(String username, String secret2) {
        secretMap.put(username, secret2);
    }

    public static String getNameTokenMap(String token) {
        return nameTokenMap.get(token);
    }

    public static void setNameTokenMap(String token, String username) {
        nameTokenMap.put(token, username);
    }

    @Value("${tester.tenantCode}")
    public void setTesterTenantCode(Long testerTenantCode2) {
        testerTenantCode = testerTenantCode2;
    }

    public static Long getTesterTenantCode() {
        return testerTenantCode;
    }
}
