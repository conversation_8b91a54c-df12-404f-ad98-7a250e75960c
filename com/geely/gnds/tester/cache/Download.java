package com.geely.gnds.tester.cache;

import com.geely.gnds.tester.enums.TesterErrorCodeEnum;

/* loaded from: Download.class */
public class Download {
    private Float downloadTime;
    private Float progress;
    private Boolean hH;
    private boolean executeSql;
    private boolean hI;
    private boolean checkMd5;
    private boolean hJ;
    private boolean hK;
    private String speed;
    private TesterErrorCodeEnum hL;

    public Download(Float downloadTime, Float progress, Boolean supportDownload) {
        this.downloadTime = downloadTime;
        this.progress = progress;
        this.hH = supportDownload;
        this.hI = false;
        this.executeSql = false;
        this.checkMd5 = true;
        this.hJ = true;
        this.hK = false;
    }

    public Download() {
        this.executeSql = false;
        this.checkMd5 = true;
        this.hJ = true;
        this.hI = false;
        this.hK = false;
    }

    public Float getDownloadTime() {
        return this.downloadTime;
    }

    public void setDownloadTime(Float downloadTime) {
        if (downloadTime.floatValue() == Float.POSITIVE_INFINITY || downloadTime.floatValue() == Float.NEGATIVE_INFINITY) {
            downloadTime = Float.valueOf(0.0f);
        }
        this.downloadTime = downloadTime;
    }

    public Float getProgress() {
        return this.progress;
    }

    public void setProgress(Float progress) {
        this.progress = progress;
    }

    public Boolean getSupportDownload() {
        return this.hH;
    }

    public void setSupportDownload(Boolean supportDownload) {
        this.hH = supportDownload;
    }

    public boolean isDisconnection() {
        return this.hI;
    }

    public void setDisconnection(boolean disconnection) {
        this.hI = disconnection;
    }

    public boolean isExecuteSql() {
        return this.executeSql;
    }

    public void setExecuteSql(boolean executeSql) {
        this.executeSql = executeSql;
    }

    public boolean isCheckMd5() {
        return this.checkMd5;
    }

    public void setCheckMd5(boolean checkMd5) {
        this.checkMd5 = checkMd5;
    }

    public boolean isIntegrity() {
        return this.hJ;
    }

    public void setIntegrity(boolean integrity) {
        this.hJ = integrity;
    }

    public boolean isNoSpace() {
        return this.hK;
    }

    public void setNoSpace(boolean noSpace) {
        this.hK = noSpace;
    }

    public TesterErrorCodeEnum getErrorCodeEnum() {
        return this.hL;
    }

    public void setErrorCodeEnum(TesterErrorCodeEnum errorCodeEnum) {
        this.hL = errorCodeEnum;
    }

    public String toString() {
        return "Download{downloadTime=" + this.downloadTime + ", progress=" + this.progress + ", supportDownload=" + this.hH + ", executeSql=" + this.executeSql + ", disconnection=" + this.hI + ", checkMd5=" + this.checkMd5 + ", integrity=" + this.hJ + '}';
    }

    public String getSpeed() {
        return this.speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }
}
