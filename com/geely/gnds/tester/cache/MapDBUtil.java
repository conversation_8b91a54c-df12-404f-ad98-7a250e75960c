package com.geely.gnds.tester.cache;

import cn.hutool.core.io.FileUtil;
import com.geely.gnds.tester.common.AppConfig;
import java.util.EnumMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.mapdb.DB;
import org.mapdb.DBMaker;
import org.mapdb.HTreeMap;
import org.mapdb.Serializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
/* loaded from: MapDBUtil.class */
public class MapDBUtil implements InitializingBean {
    private static final Logger log = LoggerFactory.getLogger(MapDBUtil.class);
    private static final EnumMap<MapDBCache, Lock> ij = new EnumMap<>(MapDBCache.class);

    @Autowired
    private MapDBService ik;
    private static DB il;

    public void afterPropertiesSet() throws Exception {
        FileUtil.del(AppConfig.getCacheFile());
        il = DBMaker.fileDB(AppConfig.getCacheFile()).fileMmapEnableIfSupported().fileMmapPreclearDisable().transactionEnable().closeOnJvmShutdown().make();
        for (MapDBCache key : MapDBCache.values()) {
            il.hashMap(key.getKey(), Serializer.STRING, Serializer.STRING).expireAfterCreate(key.getExpireTime(), TimeUnit.SECONDS).createOrOpen();
            ij.put((EnumMap<MapDBCache, Lock>) key, (MapDBCache) new ReentrantLock());
            log.info("初始化本地缓存：{}", key.getKey());
        }
    }

    public String a(MapDBCache key) {
        Lock lock = ij.get(key);
        HTreeMap<String, String> cacheMap = il.hashMap(key.getKey(), Serializer.STRING, Serializer.STRING).createOrOpen();
        String data = (String) cacheMap.get(key.getKey());
        if (data == null) {
            lock.lock();
            try {
                try {
                    data = this.ik.a(key);
                    cacheMap.put(key.getKey(), data);
                    il.commit();
                    lock.unlock();
                } catch (Exception e) {
                    log.error("getData error ：{}", e.getMessage(), e);
                    il.rollback();
                    lock.unlock();
                }
            } catch (Throwable th) {
                lock.unlock();
                throw th;
            }
        }
        return data;
    }

    public void init() {
        a(MapDBCache.UI_LANGUAGE, this.ik.a(MapDBCache.UI_LANGUAGE));
        a(MapDBCache.DATA_LANGUAGE, this.ik.a(MapDBCache.DATA_LANGUAGE));
    }

    public void a(MapDBCache key, String data) {
        Lock lock = ij.get(key);
        lock.lock();
        try {
            try {
                HTreeMap<String, String> cacheMap = il.hashMap(key.getKey(), Serializer.STRING, Serializer.STRING).createOrOpen();
                cacheMap.put(key.getKey(), data);
                il.commit();
                lock.unlock();
            } catch (Exception e) {
                log.error("putData error ：{}", e.getMessage(), e);
                il.rollback();
                lock.unlock();
            }
        } catch (Throwable th) {
            lock.unlock();
            throw th;
        }
    }

    public void close() {
        if (null != il && !il.isClosed()) {
            il.close();
        }
    }
}
