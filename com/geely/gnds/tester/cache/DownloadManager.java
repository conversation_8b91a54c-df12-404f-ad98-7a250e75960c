package com.geely.gnds.tester.cache;

import com.geely.gnds.tester.controller.UpgradeController;
import com.geely.gnds.tester.dto.LogCollectionProgressDto;
import com.geely.gnds.tester.dto.xml.Command;
import com.geely.gnds.tester.dto.xml.EcuCommand;
import com.geely.gnds.tester.dto.xml.ImitateVehDto;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

/* loaded from: DownloadManager.class */
public class DownloadManager {
    public static final ConcurrentHashMap<String, Download> hM = new ConcurrentHashMap<>();
    private static final DownloadManager hN = new DownloadManager();
    private static final Logger log = LoggerFactory.getLogger(UpgradeController.class);
    private static Map<String, Download> hO = new HashMap();
    private static Map<String, ImitateVehDto> hP = new HashMap();
    private static Map<String, LogCollectionProgressDto> hQ = new HashMap();

    private DownloadManager() {
    }

    public static DownloadManager getInstance() {
        return hN;
    }

    public void a(String key, Download download) {
        hO.put(key, download);
    }

    public Download Y(String key) {
        return hO.get(key);
    }

    public void H() {
        if (hO.size() > 0) {
            for (String id : hO.keySet()) {
                log.info("关闭下载任务{}", hO.get(id));
                Download download = hO.get(id);
                download.setSupportDownload(false);
            }
        }
    }

    public String i(String vin, String ecuAddress, String request) {
        AtomicReference<String> response = new AtomicReference<>("");
        ImitateVehDto imitateVehDto = hP.get(vin);
        Optional.ofNullable(imitateVehDto).ifPresent(dto -> {
            List<EcuCommand> ecus = dto.getEcus();
            if (!CollectionUtils.isEmpty(ecus)) {
                ecus.forEach(ecu -> {
                    if (Objects.equals(ecu.getAddress(), ecuAddress)) {
                        List<Command> commands = ecu.getCommands();
                        if (!CollectionUtils.isEmpty(commands)) {
                            commands.forEach(command -> {
                                if (Objects.equals(command.getRequestMessage(), request)) {
                                    response.set(command.getResponse());
                                }
                            });
                        }
                    }
                });
            }
        });
        return response.get();
    }

    public void a(String vin, ImitateVehDto imitateVehDto) {
        hP.put(vin, imitateVehDto);
    }

    public ImitateVehDto Z(String vin) {
        return hP.get(vin);
    }

    public static LogCollectionProgressDto aa(String id) {
        if (hQ.containsKey(id)) {
            return hQ.get(id);
        }
        return null;
    }

    public static void a(String id, LogCollectionProgressDto logCollectionProgressDto) {
        hQ.put(id, logCollectionProgressDto);
    }
}
