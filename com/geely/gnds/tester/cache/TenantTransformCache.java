package com.geely.gnds.tester.cache;

import java.util.HashMap;
import java.util.Map;

/* loaded from: TenantTransformCache.class */
public class TenantTransformCache {
    private static Map<String, String> im = new HashMap();

    static {
        im.put("g", "GLDS2.0");
        im.put("l", "MADS");
        im.put("gri", "GNDS");
        im.put("og", "GLDS2.0-I");
        im.put("rkm", "ADS");
        im.put("levc", "LVDS");
        im.put("yf", "NDS");
        im.put("renault", "ADS");
        im.put("peds", "PEDS");
    }

    public static String al(String shortName) {
        if (im.containsKey(shortName)) {
            return im.get(shortName);
        }
        return shortName;
    }
}
