package com.geely.gnds.tester.cache;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dao.TesterSoftwareDao;
import com.geely.gnds.tester.dto.EcuDto;
import com.geely.gnds.tester.dto.ReleaseNoteDTO;
import com.geely.gnds.tester.dto.SoftwareDto;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.entity.TesterSoftwareEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.AesUtils;
import com.geely.gnds.tester.util.VbfParseUtils;
import java.io.BufferedWriter;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.PostConstruct;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.TextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
/* loaded from: FileCache.class */
public class FileCache {

    @Autowired
    private TesterConfigDao eC;

    @Autowired
    private TesterSoftwareDao hR;

    @Value("${fd.resource.vbfPath}")
    private String vbfPath;

    @Value("${fd.resource.seqPath}")
    private String seqPath;

    @Value("${fd.resource.txtPath}")
    private String txtPath;

    @Value("${fd.resource.pcapPath}")
    private String pcapPath;

    @Value("${fd.resource.xmlPath}")
    private String xmlPath;

    @Value("${fd.resource.vbfErrorPath}")
    private String vbfErrorPath;

    @Value("${fd.resource.dsaSeqPath}")
    private String dsaSeqPath;
    private File hS = AppConfig.getAppDataDir();
    private File hT = AppConfig.getAppHomeDir();
    private File hU = null;
    private File hV = null;
    private File hW = null;
    private File hX = null;
    private File hY = null;
    private File hZ = null;
    private File ia = null;
    private File ib = null;
    private static final Logger log = LoggerFactory.getLogger(FileCache.class);
    private static final TrustManager[] TRUST_MANAGERS = {new X509TrustManager() { // from class: com.geely.gnds.tester.cache.FileCache.2
        @Override // javax.net.ssl.X509TrustManager
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }

        @Override // javax.net.ssl.X509TrustManager
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            try {
                chain[0].checkValidity();
            } catch (Exception e) {
                throw new CertificateException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00111));
            }
        }

        @Override // javax.net.ssl.X509TrustManager
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            try {
                chain[0].checkValidity();
            } catch (Exception e) {
                throw new CertificateException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00111));
            }
        }
    }};
    private static String[] VERIFY_HOST_NAME_ARRAY = new String[0];
    private static final HostnameVerifier DO_NOT_VERIFY = (hostname, session) -> {
        return (TextUtils.isEmpty(hostname) || Arrays.asList(VERIFY_HOST_NAME_ARRAY).contains(hostname)) ? false : true;
    };

    public void setVbfBase(File vbfBase) {
        this.hU = vbfBase;
        if (!vbfBase.exists()) {
            vbfBase.mkdirs();
        }
    }

    public void setDsaSeqBase(String filePath) {
        this.ib = new File(filePath);
        if (!this.ib.exists()) {
            this.ib.mkdirs();
        }
    }

    public File getDsaSeqBase() {
        return this.ib;
    }

    @PostConstruct
    public void init() {
        this.hU = new File(this.hT, this.vbfPath);
        if (!this.hU.exists()) {
            this.hU.mkdirs();
        }
        File tempBase = new File(this.hT, "Temp");
        if (!tempBase.exists()) {
            tempBase.mkdirs();
        }
        this.hX = new File(this.hS, this.txtPath);
        if (!this.hX.exists()) {
            this.hX.mkdirs();
        }
        this.hV = new File(this.hS, this.seqPath);
        if (!this.hV.exists()) {
            this.hV.mkdirs();
        }
        this.hW = new File(this.hS, this.pcapPath);
        if (!this.hW.exists()) {
            this.hW.mkdirs();
        }
        this.hY = new File(this.hS, this.xmlPath);
        if (!this.hY.exists()) {
            this.hY.mkdirs();
        }
        this.hZ = new File(this.hT, "Temp");
        if (!this.hZ.exists()) {
            this.hZ.mkdirs();
        }
        this.ia = new File(this.hT, this.vbfErrorPath);
        if (!this.ia.exists()) {
            this.ia.mkdirs();
        }
        this.ib = new File(AppConfig.getAppDataDir(), this.dsaSeqPath);
        if (!this.ib.exists()) {
            this.ib.mkdirs();
        }
        TesterConfigDto config = this.eC.getConfig();
        if (ObjectUtils.isNotEmpty(config)) {
            Integer vbfCleanFlag = config.getVbfCleanFlag();
            if (ObjectUtils.isNotEmpty(vbfCleanFlag) && vbfCleanFlag.intValue() == 1) {
                try {
                    setVbfBase(new File(config.getVbfPath()));
                    J();
                    config.setVbfCleanFlag(0);
                    this.eC.update(config);
                } catch (IOException e) {
                    log.error("清空vbf失败,配置{}", config.toString(), e);
                }
            }
        }
    }

    public File getTempBase() {
        return new File(this.hU, "Temp");
    }

    public void I() throws Exception {
        String canonicalBasePath = this.hS.getCanonicalPath();
        String canonicalVbfBasePath = this.hU.getCanonicalPath();
        log.info("程序安装路径：{}", canonicalBasePath);
        log.info("VBF软件路径：{}", canonicalVbfBasePath);
        File[] disks = File.listRoots();
        for (File file : disks) {
            String canonicalPath = file.getCanonicalPath();
            if (canonicalBasePath.contains(canonicalPath) || canonicalVbfBasePath.contains(canonicalPath)) {
                long usableSpace = file.getUsableSpace();
                log.info("{}的剩余大小{}B", canonicalPath, Long.valueOf(usableSpace));
                if (usableSpace < 5368709120L) {
                    throw new CustomException(TesterErrorCodeEnum.SG00190.code() + ":" + canonicalPath.substring(0, 1) + TesterErrorCodeEnum.SG00190.valueByLanguage());
                }
            }
        }
    }

    public int ab(String downloadUrl) {
        int res = 0;
        if ("".equals(downloadUrl)) {
            return 0;
        }
        String fileName = VbfParseUtils.getVbfName(downloadUrl);
        File file = new File(this.hU, fileName);
        String successPath = fileName.split("\\.")[0] + ".success";
        File successfile = new File(this.hU, successPath);
        if (file.exists()) {
            res = 2;
            if (successfile.exists()) {
                res = 1;
            }
        }
        log.info("vbf文件是否存在checkVbfExist：{},res：{}", fileName, Integer.valueOf(res));
        return res;
    }

    public int j(String downloadUrl, String vin) {
        int res = 0;
        if ("".equals(downloadUrl)) {
            return 0;
        }
        String fileName = vin + "_" + VbfParseUtils.getVbfName(downloadUrl);
        File file = new File(this.hU, fileName);
        String successPath = fileName.split("\\.")[0] + ".success";
        File successfile = new File(this.hU, successPath);
        if (file.exists()) {
            res = 2;
            if (successfile.exists()) {
                res = 1;
            }
        }
        log.info("vbf文件是否存在checkVbfExistByVin：{},res：{},vin：{}", new Object[]{fileName, Integer.valueOf(res), vin});
        return res;
    }

    public long getVbfTotalSize() {
        long size = FileUtil.size(this.hU);
        return size;
    }

    public void b(long vbfDefaultSize) throws IOException {
        long vbfTotalSize = getVbfTotalSize();
        for (File file : c(this.hU)) {
            if (vbfTotalSize > vbfDefaultSize) {
                String canonicalPath = file.getCanonicalPath();
                if (canonicalPath.contains(ConstantEnum.VBF)) {
                    long length = file.length();
                    String fileName = canonicalPath.substring(canonicalPath.lastIndexOf(File.separator) + 1);
                    deleteVbf(fileName);
                    vbfTotalSize -= length;
                }
            } else {
                return;
            }
        }
    }

    public void J() throws IOException {
        for (File file : c(this.hU)) {
            String canonicalPath = file.getCanonicalPath();
            if (canonicalPath.contains(ConstantEnum.VBF)) {
                String fileName = canonicalPath.substring(canonicalPath.lastIndexOf(File.separator) + 1);
                deleteVbf(fileName);
            }
        }
    }

    public File[] c(File file) {
        File[] files = file.listFiles();
        Arrays.sort(files, new Comparator<File>() { // from class: com.geely.gnds.tester.cache.FileCache.1
            @Override // java.util.Comparator
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public int compare(File f1, File f2) {
                long diff = f1.lastModified() - f2.lastModified();
                if (diff > 0) {
                    return 1;
                }
                if (diff == 0) {
                    return 0;
                }
                return -1;
            }

            @Override // java.util.Comparator
            public boolean equals(Object obj) {
                return true;
            }
        });
        for (int i = 0; i < files.length; i++) {
            System.out.println(files[i].getName());
            System.out.println(new Date(files[i].lastModified()));
        }
        return files;
    }

    public int ac(String fileName) {
        if (!fileName.contains(ConstantEnum.VBF)) {
            fileName = fileName + ConstantEnum.VBF;
        }
        int res = 0;
        File file = new File(this.hU, fileName);
        String successPath = fileName.split("\\.")[0] + ".success";
        File successfile = new File(this.hU, successPath);
        if (file.exists()) {
            log.info("VBF【{}】文件大小【{}】", fileName, Long.valueOf(file.length()));
            res = 2;
            if (successfile.exists()) {
                res = 1;
            }
        }
        return res;
    }

    public int ad(String fileName) {
        int res = 0;
        File file = new File(this.hZ, fileName);
        File successfile = new File(this.hZ, "downloadApp.success");
        if (file.exists()) {
            res = 2;
            if (successfile.exists()) {
                res = 1;
            }
        }
        return res;
    }

    public File ae(String fileName) {
        if (StringUtils.isNotBlank(fileName)) {
            String fileName2 = VbfParseUtils.getVbfName(fileName);
            File file = new File(this.hU, fileName2);
            String successPath = fileName2.split("\\.")[0] + ".success";
            File successfile = new File(this.hU, successPath);
            if (file.exists() && successfile.exists()) {
                return file;
            }
            return null;
        }
        return null;
    }

    public long af(String downloadUrl) {
        long res = 0;
        if (!"".equals(downloadUrl) && downloadUrl.contains("/")) {
            String fileName = VbfParseUtils.getVbfName(downloadUrl);
            File file = new File(this.hU, fileName);
            res = file.length();
        }
        return res;
    }

    public long getDownloadJarSize() {
        File file = new File(this.hZ, "tester.jar");
        long res = file.length();
        return res;
    }

    public File getVbfBase() {
        return this.hU;
    }

    public String ag(String seqCode) {
        String fileName = org.springframework.util.StringUtils.cleanPath(seqCode + ConstantEnum.JSON);
        String basePath = this.hS.getAbsolutePath();
        String filePath = basePath.substring(0, basePath.length() - 1) + this.seqPath + File.separator + fileName;
        File file = new File(this.hV, fileName);
        if (file.exists()) {
            return filePath;
        }
        return "";
    }

    public boolean k(String seqCode, String seq) throws IOException {
        String fileName = org.springframework.util.StringUtils.cleanPath(seqCode + ConstantEnum.JSON);
        String basePath = this.hS.getAbsolutePath();
        String filePath = basePath.substring(0, basePath.length() - 1) + this.seqPath + File.separator + fileName;
        try {
            OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(filePath), StandardCharsets.UTF_8);
            Throwable th = null;
            try {
                try {
                    osw.write(JSON.toJSONString(JSONObject.parseObject(seq), new SerializerFeature[]{SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat}));
                    osw.flush();
                    if (osw != null) {
                        if (0 != 0) {
                            try {
                                osw.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            osw.close();
                        }
                    }
                    AesUtils.encodeFileSelf(filePath);
                    return true;
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00001), e);
            return false;
        }
    }

    public boolean b(MultipartFile multipartFile) throws IOException {
        String fileName = org.springframework.util.StringUtils.cleanPath(multipartFile.getOriginalFilename());
        File localFile = new File(this.hU + File.separator + fileName);
        try {
            localFile.createNewFile();
            FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), localFile);
            String successPath = this.hU + File.separator + fileName.replace("vbf", "success");
            File successFile = new File(successPath);
            successFile.createNewFile();
            return true;
        } catch (IOException e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00002), e);
            return false;
        }
    }

    public void deleteVbf(String vbfName) {
        log.info("开始deleteVbf：{}", vbfName);
        String vbfName2 = vbfName.replace(ConstantEnum.VBF, "");
        String fileName = vbfName2 + ConstantEnum.VBF;
        String successFileName = vbfName2 + ".success";
        File file = new File(this.hU, fileName);
        if (file.exists()) {
            file.delete();
        }
        File successFile = new File(this.hU, successFileName);
        if (successFile.exists()) {
            successFile.delete();
        }
        Set<String> keySet = new HashSet<>(2);
        keySet.add(fileName);
        a(keySet);
    }

    public void ah(String vbfName) {
        try {
            log.info("开始removeErrorVbf：{}", vbfName);
            vbfName = vbfName.replace(ConstantEnum.VBF, "");
            String fileName = vbfName + ConstantEnum.VBF;
            String successFileName = vbfName + ".success";
            File file = new File(this.hU, fileName);
            long currentTimeMillis = System.currentTimeMillis();
            if (file.exists()) {
                File vbfErrorFile = new File(this.ia, currentTimeMillis + "_" + fileName);
                file.renameTo(vbfErrorFile);
            }
            File successFile = new File(this.hU, successFileName);
            if (successFile.exists()) {
                File vbfErrorFile2 = new File(this.ia, currentTimeMillis + "_" + successFileName);
                successFile.renameTo(vbfErrorFile2);
            }
            deleteVbf(fileName);
        } catch (Exception e) {
            log.error("删除vbf失败{}", vbfName);
        }
    }

    private void a(Set<String> keySet) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("fileNames", keySet);
        List<TesterSoftwareEntity> testerSoftwareEntitys = this.hR.queryList(params);
        if (testerSoftwareEntitys.size() > 0) {
            for (TesterSoftwareEntity testerSoftwareEntity : testerSoftwareEntitys) {
                this.hR.delete(testerSoftwareEntity.getId());
            }
        }
    }

    public void ai(String seqCode) {
        String fileName = org.springframework.util.StringUtils.cleanPath(seqCode + ConstantEnum.JSON);
        File file = new File(this.hV, fileName);
        file.delete();
    }

    public void aj(String fileName) {
        File file = new File(this.hZ, fileName);
        if (file.exists()) {
            file.delete();
        }
    }

    /* JADX WARN: Can't wrap try/catch for region: R(10:201|17|(1:19)(1:20)|21|(8:(2:221|23)(2:24|(5:212|26|27|224|223)(1:222))|194|33|202|34|(2:35|(1:231)(1:39))|40|(8:213|42|(2:44|(2:185|46)(1:49))|50|(2:52|(2:189|54)(1:57))|58|59|60)(7:61|(2:63|(2:180|65)(1:68))|83|(2:85|(2:204|87)(1:90))|105|119|120))|28|(1:30)|31|207|32) */
    /* JADX WARN: Code restructure failed: missing block: B:106:0x0381, code lost:
    
        r45 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:107:0x0383, code lost:
    
        r0 = r45.getMessage();
     */
    /* JADX WARN: Code restructure failed: missing block: B:108:0x0391, code lost:
    
        if (r0.contains("Server returned HTTP response code: 416") != false) goto L109;
     */
    /* JADX WARN: Code restructure failed: missing block: B:110:0x0396, code lost:
    
        r0.close();
        com.geely.gnds.tester.cache.DownloadManager.hM.remove(r0);
        com.geely.gnds.tester.util.LockUtil.unlock(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:111:0x03aa, code lost:
    
        com.geely.gnds.tester.cache.FileCache.log.error(com.geely.gnds.tester.enums.TesterErrorCodeEnum.formatMsg(com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00003), r45);
     */
    /* JADX WARN: Code restructure failed: missing block: B:112:0x03bc, code lost:
    
        throw r45;
     */
    /* JADX WARN: Code restructure failed: missing block: B:113:0x03bd, code lost:
    
        r45 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:114:0x03bf, code lost:
    
        r0 = r45.getMessage();
     */
    /* JADX WARN: Code restructure failed: missing block: B:115:0x03cd, code lost:
    
        if (r0.contains("Server returned HTTP response code: 416") == false) goto L214;
     */
    /* JADX WARN: Code restructure failed: missing block: B:116:0x03d0, code lost:
    
        com.geely.gnds.tester.cache.FileCache.log.error(com.geely.gnds.tester.enums.TesterErrorCodeEnum.formatMsg(com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00003), r45);
     */
    /* JADX WARN: Code restructure failed: missing block: B:117:0x03e2, code lost:
    
        throw r45;
     */
    /* JADX WARN: Code restructure failed: missing block: B:118:0x03e3, code lost:
    
        r33 = true;
     */
    /* JADX WARN: Finally extract failed */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void a(java.util.List<java.lang.String> r12, com.geely.gnds.tester.cache.Download r13, long r14, long r16, java.util.List<com.geely.gnds.tester.dto.EcuDto> r18, java.util.Map<java.lang.String, java.lang.String> r19, java.util.List<java.lang.String> r20, java.lang.String r21) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 1531
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.cache.FileCache.a(java.util.List, com.geely.gnds.tester.cache.Download, long, long, java.util.List, java.util.Map, java.util.List, java.lang.String):void");
    }

    private boolean a(List<EcuDto> ecuDtoLis, List<String> tempUrl, String vin) {
        int i;
        try {
            for (EcuDto ecuDto : ecuDtoLis) {
                for (SoftwareDto softwareDto : ecuDto.getSoftwares()) {
                    String urlStr = softwareDto.getUrl();
                    if (tempUrl.contains(urlStr)) {
                        i = j(urlStr, vin);
                    } else {
                        i = ab(urlStr);
                    }
                    if (i != 1) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("校验VBF完整性异常", e);
            return false;
        }
    }

    private void a(Download download, long totleSize, long downloadSize, long updateSizeInit, long startTime) {
        float progress;
        float downloadTime;
        String speedS = "0KB/s";
        if (downloadSize > totleSize) {
            progress = 99.0f;
            downloadTime = 0.0f;
        } else {
            progress = (downloadSize / totleSize) * 100.0f;
            long time = System.currentTimeMillis() - startTime;
            float speed = ((downloadSize - updateSizeInit) / time) * 1000.0f;
            float speedF = Math.round((speed / 1024.0f) * 10.0f) / 10.0f;
            if (speedF > 1024.0f) {
                speedS = (Math.round((speedF / 1024.0f) * 10.0f) / 10.0f) + "MB/s";
            } else {
                speedS = speedF + "KB/s";
            }
            downloadTime = (totleSize - downloadSize) / speed;
            if (progress >= 99.0f) {
                progress = 99.0f;
            }
            if (downloadTime <= 0.0f) {
                downloadTime = 0.0f;
                progress = 99.0f;
            }
        }
        download.setProgress(Float.valueOf(progress));
        download.setDownloadTime(Float.valueOf(downloadTime));
        download.setSpeed(speedS);
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r33v2 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r33v2 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r34v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r34v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r35v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r35v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 33, insn: 0x0459: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r33 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:125:0x0459 */
    /* JADX WARN: Not initialized variable reg: 34, insn: 0x03fd: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r34 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('out' java.io.DataOutputStream)]) A[TRY_LEAVE], block:B:101:0x03fd */
    /* JADX WARN: Not initialized variable reg: 35, insn: 0x0402: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r35 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:103:0x0402 */
    /* JADX WARN: Type inference failed for: r0v193, names: [https], types: [java.io.DataInputStream, javax.net.ssl.HttpsURLConnection] */
    /* JADX WARN: Type inference failed for: r33v2, types: [java.lang.Throwable] */
    /* JADX WARN: Type inference failed for: r34v1, names: [out], types: [java.io.DataOutputStream] */
    /* JADX WARN: Type inference failed for: r35v1, types: [java.lang.Throwable] */
    public void a(String urlStr, Download download, long totalSize, long downloadSize, ReleaseNoteDTO releaseNoteDTO) throws Exception {
        BufferedWriter writer;
        Throwable th;
        ?? r33;
        ?? r34;
        ?? r35;
        ?? r0;
        long updateSize = totalSize / 100;
        long updateDownloadSize = 0;
        long startTime = System.currentTimeMillis();
        long startByte = 0;
        releaseNoteDTO.getAppType();
        d(this.hZ);
        String fileName = VbfParseUtils.getVbfName(urlStr);
        int i = ad(fileName);
        if (i == ConstantEnum.TWO.intValue()) {
            startByte = getDownloadJarSize();
        } else if (i == ConstantEnum.ONE.intValue()) {
            String baseDir = AppConfig.getAppHomeDir().getAbsolutePath();
            String flgFileName = baseDir + "\\restart.flg";
            Path path = Paths.get(flgFileName, new String[0]);
            try {
                writer = Files.newBufferedWriter(path, StandardCharsets.UTF_8, new OpenOption[0]);
                th = null;
            } catch (Exception e) {
                System.out.println("TesterControllerService restart Exception,e=" + e);
            }
            try {
                try {
                    writer.write("1");
                    if (writer != null) {
                        if (0 != 0) {
                            try {
                                writer.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            writer.close();
                        }
                    }
                    download.setProgress(Float.valueOf(100.0f));
                    download.setDownloadTime(Float.valueOf(0.0f));
                    return;
                } finally {
                }
            } finally {
            }
        }
        String savePath = this.hZ + File.separator + fileName;
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        boolean useHttps = urlStr.startsWith("https");
        if (useHttps) {
            r0 = (HttpsURLConnection) conn;
            trustAllHosts(r0);
            r0.setHostnameVerifier(DO_NOT_VERIFY);
        }
        conn.setRequestProperty("range", "bytes=" + startByte + "-");
        conn.setRequestProperty("Accept", "*/*");
        conn.setConnectTimeout(5000);
        conn.setUseCaches(false);
        conn.setReadTimeout(15000);
        conn.connect();
        try {
            try {
                DataInputStream in = new DataInputStream(conn.getInputStream());
                Throwable th3 = null;
                try {
                    DataOutputStream dataOutputStream = new DataOutputStream(new FileOutputStream(savePath, true));
                    Throwable th4 = null;
                    byte[] bArr = new byte[4096];
                    while (true) {
                        int i2 = in.read(bArr);
                        if (i2 <= 0 || !download.getSupportDownload().booleanValue()) {
                            break;
                        }
                        dataOutputStream.write(bArr, 0, i2);
                        downloadSize += i2;
                        updateDownloadSize += i2;
                        if (updateDownloadSize > updateSize) {
                            updateDownloadSize = 0;
                            float f = (downloadSize / totalSize) * 100.0f;
                            float fCurrentTimeMillis = (totalSize - downloadSize) / ((downloadSize / (System.currentTimeMillis() - startTime)) * 1000.0f);
                            if (f >= 99.0f) {
                                f = 99.0f;
                            }
                            if (fCurrentTimeMillis <= 0.0f) {
                                f = 0.0f;
                            }
                            download.setProgress(Float.valueOf(f));
                            download.setDownloadTime(Float.valueOf(fCurrentTimeMillis));
                        }
                    }
                    if (download.getSupportDownload().booleanValue()) {
                        String absolutePath = AppConfig.getAppHomeDir().getAbsolutePath();
                        Integer num = 1;
                        if (num.intValue() == 1) {
                            new File(absolutePath, "Temp\\exe.success").createNewFile();
                        } else {
                            new File(this.hZ + File.separator + fileName.replace("jar", "success")).createNewFile();
                            try {
                                BufferedWriter bufferedWriterNewBufferedWriter = Files.newBufferedWriter(Paths.get(absolutePath + "\\restart.flg", new String[0]), StandardCharsets.UTF_8, new OpenOption[0]);
                                Throwable th5 = null;
                                try {
                                    bufferedWriterNewBufferedWriter.write("1");
                                    if (bufferedWriterNewBufferedWriter != null) {
                                        if (0 != 0) {
                                            try {
                                                bufferedWriterNewBufferedWriter.close();
                                            } catch (Throwable th6) {
                                                th5.addSuppressed(th6);
                                            }
                                        } else {
                                            bufferedWriterNewBufferedWriter.close();
                                        }
                                    }
                                } catch (Throwable th7) {
                                    if (bufferedWriterNewBufferedWriter != null) {
                                        if (0 != 0) {
                                            try {
                                                bufferedWriterNewBufferedWriter.close();
                                            } catch (Throwable th8) {
                                                th5.addSuppressed(th8);
                                            }
                                        } else {
                                            bufferedWriterNewBufferedWriter.close();
                                        }
                                    }
                                    throw th7;
                                }
                            } catch (Exception e2) {
                                System.out.println("TesterControllerService restart Exception,e=" + e2);
                            }
                        }
                        new File(absolutePath, "Temp\\downloadApp.success").createNewFile();
                        download.setProgress(Float.valueOf(100.0f));
                        download.setDownloadTime(Float.valueOf(0.0f));
                    }
                    if (dataOutputStream != null) {
                        if (0 != 0) {
                            try {
                                dataOutputStream.close();
                            } catch (Throwable th9) {
                                th4.addSuppressed(th9);
                            }
                        } else {
                            dataOutputStream.close();
                        }
                    }
                    if (in != null) {
                        if (0 != 0) {
                            try {
                                in.close();
                            } catch (Throwable th10) {
                                th3.addSuppressed(th10);
                            }
                        } else {
                            in.close();
                        }
                    }
                } catch (Throwable th11) {
                    if (r34 != 0) {
                        if (r35 != 0) {
                            try {
                                r34.close();
                            } catch (Throwable th12) {
                                r35.addSuppressed(th12);
                            }
                        } else {
                            r34.close();
                        }
                    }
                    throw th11;
                }
            } catch (FileNotFoundException e3) {
                log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00260), e3);
                download.setDisconnection(true);
                download.setErrorCodeEnum(TesterErrorCodeEnum.SG00260);
                throw e3;
            } catch (UnknownHostException e4) {
                log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00259), e4);
                download.setDisconnection(true);
                download.setErrorCodeEnum(TesterErrorCodeEnum.SG00259);
                throw e4;
            } catch (IOException e5) {
                log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00259), e5);
                download.setDisconnection(true);
                download.setErrorCodeEnum(TesterErrorCodeEnum.SG00259);
                throw e5;
            }
        } catch (Throwable th13) {
            if (r0 != 0) {
                if (r33 != 0) {
                    try {
                        r0.close();
                    } catch (Throwable th14) {
                        r33.addSuppressed(th14);
                    }
                } else {
                    r0.close();
                }
            }
            throw th13;
        }
    }

    private void d(File jarBase) {
        File[] files;
        if (jarBase.exists() && (files = jarBase.listFiles()) != null) {
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile()) {
                    files[i].delete();
                } else if (files[i].isDirectory()) {
                    d(files[i]);
                }
                files[i].delete();
            }
        }
    }

    public String ak(String urlStr) throws Exception {
        int i = ad("dq.sql");
        if (i != 0) {
            aj("dq.sql");
        }
        int i2 = ad("dq.success");
        if (i2 != 0) {
            aj("dq.success");
        }
        String savePath = this.hZ + File.separator + "dq.sql";
        Path path = Paths.get(savePath, new String[0]);
        if (!path.toFile().exists()) {
            path.toFile().createNewFile();
        }
        String[] split = urlStr.split(";");
        for (String s : split) {
            if (StringUtils.isNotBlank(s)) {
                URL url = new URL(s);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                boolean useHttps = s.startsWith("https");
                if (useHttps) {
                    HttpsURLConnection https = (HttpsURLConnection) conn;
                    trustAllHosts(https);
                    https.setHostnameVerifier(DO_NOT_VERIFY);
                }
                conn.setRequestProperty("Accept", "*/*");
                conn.setConnectTimeout(HttpStatus.SETTLEMENT_FAIL);
                conn.setUseCaches(false);
                conn.setReadTimeout(3000);
                conn.connect();
                try {
                    OutputStream writer = Files.newOutputStream(path, StandardOpenOption.APPEND);
                    Throwable th = null;
                    try {
                        try {
                            DataInputStream in = new DataInputStream(conn.getInputStream());
                            Throwable th2 = null;
                            try {
                                try {
                                    byte[] buffer = new byte[8192];
                                    while (true) {
                                        int count = in.read(buffer);
                                        if (count <= 0) {
                                            break;
                                        }
                                        writer.write(buffer, 0, count);
                                    }
                                    if (in != null) {
                                        if (0 != 0) {
                                            try {
                                                in.close();
                                            } catch (Throwable th3) {
                                                th2.addSuppressed(th3);
                                            }
                                        } else {
                                            in.close();
                                        }
                                    }
                                    if (writer != null) {
                                        if (0 != 0) {
                                            try {
                                                writer.close();
                                            } catch (Throwable th4) {
                                                th.addSuppressed(th4);
                                            }
                                        } else {
                                            writer.close();
                                        }
                                    }
                                } finally {
                                }
                            } finally {
                            }
                        } finally {
                        }
                    } finally {
                    }
                } catch (IOException e) {
                    log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00006), e);
                    throw e;
                }
            }
        }
        return FileUtil.readString(new File(savePath), StandardCharsets.UTF_8);
    }

    private static SSLSocketFactory trustAllHosts(HttpsURLConnection connection) throws NoSuchAlgorithmException, KeyManagementException {
        SSLSocketFactory oldFactory = connection.getSSLSocketFactory();
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, TRUST_MANAGERS, new SecureRandom());
            SSLSocketFactory newFactory = sc.getSocketFactory();
            connection.setSSLSocketFactory(newFactory);
        } catch (Exception e) {
            log.error("trustAllHosts失败", e);
            e.printStackTrace();
        }
        return oldFactory;
    }
}
