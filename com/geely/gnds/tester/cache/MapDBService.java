package com.geely.gnds.tester.cache;

import com.geely.gnds.tester.component.Cloud;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: MapDBService.class */
public class MapDBService {
    private static final Logger log = LoggerFactory.getLogger(MapDBService.class);

    @Autowired
    private Cloud cloud;

    public String a(MapDBCache key) {
        switch (key) {
            case DATA_LANGUAGE:
                return getDataLanguage();
            case UI_LANGUAGE:
                return getUiLanguage();
            default:
                return null;
        }
    }

    private String getUiLanguage() {
        try {
            log.info("更新UI多语言缓存数据");
            return this.cloud.getUiLanguageData();
        } catch (Exception e) {
            log.error("无法缓存UI多语言到本地：{}", e.getMessage(), e);
            return null;
        }
    }

    private String getDataLanguage() {
        try {
            log.info("更新数据多语言缓存数据");
            return this.cloud.getDataLanguageData();
        } catch (Exception e) {
            log.error("无法缓存数据多语言到本地：{}", e.getMessage(), e);
            return null;
        }
    }
}
