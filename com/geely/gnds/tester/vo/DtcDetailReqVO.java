package com.geely.gnds.tester.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: DtcDetailReqVO.class */
public class DtcDetailReqVO {
    private String title;
    private String diagnosticNumber;
    private String dtcId;
    private String vin;
    private String ecuAddress;
    private String ecuName;
    private String globalTime;
    private String qr;
    private String createTime;

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDiagnosticNumber() {
        return this.diagnosticNumber;
    }

    public void setDiagnosticNumber(String diagnosticNumber) {
        this.diagnosticNumber = diagnosticNumber;
    }

    public String getDtcId() {
        return this.dtcId;
    }

    public void setDtcId(String dtcId) {
        this.dtcId = dtcId;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getGlobalTime() {
        return this.globalTime;
    }

    public void setGlobalTime(String globalTime) {
        this.globalTime = globalTime;
    }

    public String getCreateGlobalTime() {
        return this.qr;
    }

    public void setCreateGlobalTime(String createGlobalTime) {
        this.qr = createGlobalTime;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String toString() {
        return new ToStringBuilder(this).append("title", this.title).append("diagnosticNumber", this.diagnosticNumber).append("dtcId", this.dtcId).append("vin", this.vin).append("ecuAddress", this.ecuAddress).append("ecuName", this.ecuName).append("globalTime", this.globalTime).append("createGlobalTime", this.qr).append("createTime", this.createTime).toString();
    }
}
