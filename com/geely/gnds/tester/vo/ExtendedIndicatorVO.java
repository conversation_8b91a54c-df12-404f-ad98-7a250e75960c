package com.geely.gnds.tester.vo;

import java.util.StringJoiner;

/* loaded from: ExtendedIndicatorVO.class */
public class ExtendedIndicatorVO {
    private DtcStatusMask qy;
    private DtcExtendedDataRecord qz;

    public ExtendedIndicatorVO() {
        setStatusMask(new DtcStatusMask());
        setExtendedDataRecord(new DtcExtendedDataRecord());
    }

    public DtcStatusMask getStatusMask() {
        return this.qy;
    }

    public void setStatusMask(DtcStatusMask statusMask) {
        this.qy = statusMask;
    }

    public DtcExtendedDataRecord getExtendedDataRecord() {
        return this.qz;
    }

    public void setExtendedDataRecord(DtcExtendedDataRecord extendedDataRecord) {
        this.qz = extendedDataRecord;
    }

    public String toString() {
        return new StringJoiner(", ", ExtendedIndicatorVO.class.getSimpleName() + "[", "]").add("statusMask=" + this.qy).add("extendedDataRecord=" + this.qz).toString();
    }

    /* loaded from: ExtendedIndicatorVO$DtcStatusMask.class */
    public static class DtcStatusMask {
        private Boolean qI;
        private Boolean qJ;
        private Boolean qK;
        private Boolean qL;
        private Boolean qM;
        private Boolean qN;
        private Boolean qO;
        private Boolean qP;

        public Boolean getFailedNow() {
            return this.qI;
        }

        public void setFailedNow(Boolean failedNow) {
            this.qI = failedNow;
        }

        public Boolean getFailedThisCycle() {
            return this.qJ;
        }

        public void setFailedThisCycle(Boolean failedThisCycle) {
            this.qJ = failedThisCycle;
        }

        public Boolean getPending() {
            return this.qK;
        }

        public void setPending(Boolean pending) {
            this.qK = pending;
        }

        public Boolean getConfirmed() {
            return this.qL;
        }

        public void setConfirmed(Boolean confirmed) {
            this.qL = confirmed;
        }

        public Boolean getNotCompletedSinceErase() {
            return this.qM;
        }

        public void setNotCompletedSinceErase(Boolean notCompletedSinceErase) {
            this.qM = notCompletedSinceErase;
        }

        public Boolean getFailedSinceErase() {
            return this.qN;
        }

        public void setFailedSinceErase(Boolean failedSinceErase) {
            this.qN = failedSinceErase;
        }

        public Boolean getNotCompletedThisCycle() {
            return this.qO;
        }

        public void setNotCompletedThisCycle(Boolean notCompletedThisCycle) {
            this.qO = notCompletedThisCycle;
        }

        public Boolean getWarningIndicatorNow() {
            return this.qP;
        }

        public void setWarningIndicatorNow(Boolean warningIndicatorNow) {
            this.qP = warningIndicatorNow;
        }

        public String toString() {
            return new StringJoiner(", ", DtcStatusMask.class.getSimpleName() + "[", "]").add("failedNow=" + this.qI).add("failedThisCycle=" + this.qJ).add("pending=" + this.qK).add("confirmed=" + this.qL).add("notCompletedSinceErase=" + this.qM).add("failedSinceErase=" + this.qN).add("notCompletedThisCycle=" + this.qO).add("warningIndicatorNow=" + this.qP).toString();
        }
    }

    /* loaded from: ExtendedIndicatorVO$DtcExtendedDataRecord.class */
    public static class DtcExtendedDataRecord {
        private Boolean qA;
        private Boolean qB;
        private Boolean qC;
        private Boolean qD;
        private Boolean qE;
        private Boolean qF;
        private Boolean qG;
        private Boolean qH;

        public Boolean getUnConfirmedNow() {
            return this.qA;
        }

        public void setUnConfirmedNow(Boolean unConfirmedNow) {
            this.qA = unConfirmedNow;
        }

        public Boolean getUnConfirmedThisCycle() {
            return this.qB;
        }

        public void setUnConfirmedThisCycle(Boolean unConfirmedThisCycle) {
            this.qB = unConfirmedThisCycle;
        }

        public Boolean getUnConfirmedSinceErase() {
            return this.qC;
        }

        public void setUnConfirmedSinceErase(Boolean unConfirmedSinceErase) {
            this.qC = unConfirmedSinceErase;
        }

        public Boolean getAgedDtc() {
            return this.qD;
        }

        public void setAgedDtc(Boolean agedDtc) {
            this.qD = agedDtc;
        }

        public Boolean getSymptomSinceErase() {
            return this.qE;
        }

        public void setSymptomSinceErase(Boolean symptomSinceErase) {
            this.qE = symptomSinceErase;
        }

        public Boolean getWarningIndicatorSinceErase() {
            return this.qF;
        }

        public void setWarningIndicatorSinceErase(Boolean warningIndicatorSinceErase) {
            this.qF = warningIndicatorSinceErase;
        }

        public Boolean getEmissionRelatedDtc() {
            return this.qG;
        }

        public void setEmissionRelatedDtc(Boolean emissionRelatedDtc) {
            this.qG = emissionRelatedDtc;
        }

        public Boolean getFailedSinceAgingOrErase() {
            return this.qH;
        }

        public void setFailedSinceAgingOrErase(Boolean failedSinceAgingOrErase) {
            this.qH = failedSinceAgingOrErase;
        }

        public String toString() {
            return new StringJoiner(", ", DtcExtendedDataRecord.class.getSimpleName() + "[", "]").add("unConfirmedNow=" + this.qA).add("unConfirmedThisCycle=" + this.qB).add("unConfirmedSinceErase=" + this.qC).add("agedDtc=" + this.qD).add("symptomSinceErase=" + this.qE).add("warningIndicatorSinceErase=" + this.qF).add("emissionRelatedDtc=" + this.qG).add("failedSinceAgingOrErase=" + this.qH).toString();
        }
    }
}
