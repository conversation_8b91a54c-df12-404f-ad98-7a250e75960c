package com.geely.gnds.tester.vo.language;

import java.util.List;
import java.util.Map;

/* loaded from: DtcResultVo.class */
public class DtcResultVo extends LanguageBaseVo {
    private List<String> rA;
    private Map<String, String> rB;

    public List<String> getShieldDtcList() {
        return this.rA;
    }

    public void setShieldDtcList(List<String> shieldDtcList) {
        this.rA = shieldDtcList;
    }

    public Map<String, String> getDmeMap() {
        return this.rB;
    }

    public void setDmeMap(Map<String, String> dmeMap) {
        this.rB = dmeMap;
    }
}
