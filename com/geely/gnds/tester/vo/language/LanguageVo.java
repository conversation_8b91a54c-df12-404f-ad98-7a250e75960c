package com.geely.gnds.tester.vo.language;

import java.io.Serializable;

/* loaded from: LanguageVo.class */
public class LanguageVo implements Serializable {
    private Long id;
    private String rE;
    private String rF;
    private String rG;
    private String rH;
    private String tenantCode;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLanguageKey() {
        return this.rE;
    }

    public void setLanguageKey(String languageKey) {
        this.rE = languageKey;
    }

    public String getLanguageContent() {
        return this.rF;
    }

    public void setLanguageContent(String languageContent) {
        this.rF = languageContent;
    }

    public String getLanguageType() {
        return this.rG;
    }

    public void setLanguageType(String languageType) {
        this.rG = languageType;
    }

    public String getSystemCode() {
        return this.rH;
    }

    public void setSystemCode(String systemCode) {
        this.rH = systemCode;
    }

    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
}
