package com.geely.gnds.tester.vo;

import java.util.List;
import java.util.StringJoiner;

/* loaded from: SnapshotRecordVO.class */
public class SnapshotRecordVO {
    private TimeAxisVO qS;
    private TimeAxisVO qT;
    private Long qU;
    private Integer qV;
    private List<FrozenValuesVO> qW;
    private Long qX;

    public TimeAxisVO getFirstToLast() {
        return this.qS;
    }

    public void setFirstToLast(TimeAxisVO firstToLast) {
        this.qS = firstToLast;
    }

    public TimeAxisVO getLastToNow() {
        return this.qT;
    }

    public void setLastToNow(TimeAxisVO lastToNow) {
        this.qT = lastToNow;
    }

    public Long getFrozenTime() {
        return this.qU;
    }

    public void setFrozenTime(Long frozenTime) {
        this.qU = frozenTime;
    }

    public Integer getLightStatus() {
        return this.qV;
    }

    public void setLightStatus(Integer lightStatus) {
        this.qV = lightStatus;
    }

    public List<FrozenValuesVO> getFrozenValues() {
        return this.qW;
    }

    public void setFrozenValues(List<FrozenValuesVO> frozenValues) {
        this.qW = frozenValues;
    }

    public Long getNow() {
        return this.qX;
    }

    public void setNow(Long now) {
        this.qX = now;
    }

    public String toString() {
        return new StringJoiner(", ", SnapshotRecordVO.class.getSimpleName() + "[", "]").add("firstToLast=" + this.qS).add("lastToNow=" + this.qT).add("frozenTime=" + this.qU).add("lightStatus=" + this.qV).add("frozenValues=" + this.qW).add("now=" + this.qX).toString();
    }

    /* loaded from: SnapshotRecordVO$TimeAxisVO.class */
    public static class TimeAxisVO {
        private Integer qZ;
        private Integer ra;
        private Integer passed;
        private Integer rb;
        private Long rc;

        public Integer getAll() {
            return this.qZ;
        }

        public void setAll(Integer all) {
            this.qZ = all;
        }

        public Integer getUnconfirmed() {
            return this.ra;
        }

        public void setUnconfirmed(Integer unconfirmed) {
            this.ra = unconfirmed;
        }

        public Integer getPassed() {
            return this.passed;
        }

        public void setPassed(Integer passed) {
            this.passed = passed;
        }

        public Integer getNoResult() {
            return this.rb;
        }

        public void setNoResult(Integer noResult) {
            this.rb = noResult;
        }

        public Long getTimeStamp() {
            return this.rc;
        }

        public void setTimeStamp(Long timeStamp) {
            this.rc = timeStamp;
        }

        public String toString() {
            return new StringJoiner(", ", TimeAxisVO.class.getSimpleName() + "[", "]").add("all=" + this.qZ).add("unconfirmed=" + this.ra).add("passed=" + this.passed).add("noResult=" + this.rb).add("timeStamp=" + this.rc).toString();
        }
    }

    /* loaded from: SnapshotRecordVO$FrozenValuesVO.class */
    public static class FrozenValuesVO {
        private String name;
        private List<SnapshotResponseItemVO> qY;

        public String getName() {
            return this.name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<SnapshotResponseItemVO> getItems() {
            return this.qY;
        }

        public void setItems(List<SnapshotResponseItemVO> items) {
            this.qY = items;
        }

        public String toString() {
            return new StringJoiner(", ", FrozenValuesVO.class.getSimpleName() + "[", "]").add("name='" + this.name + "'").add("items=" + this.qY).toString();
        }

        /* loaded from: SnapshotRecordVO$FrozenValuesVO$SnapshotResponseItemVO.class */
        public static class SnapshotResponseItemVO {
            private String dmeName;
            private String name;
            private String originalName;
            private String originalUnit;
            private String dataIdentifierId;
            private String value;
            private String unit;
            private String outDataType;

            public String getOutDataType() {
                return this.outDataType;
            }

            public void setOutDataType(String outDataType) {
                this.outDataType = outDataType;
            }

            public String getOriginalUnit() {
                return this.originalUnit;
            }

            public void setOriginalUnit(String originalUnit) {
                this.originalUnit = originalUnit;
            }

            public String getName() {
                return this.name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getOriginalName() {
                return this.originalName;
            }

            public void setOriginalName(String originalName) {
                this.originalName = originalName;
            }

            public String getValue() {
                return this.value;
            }

            public void setValue(String value) {
                this.value = value;
            }

            public String getUnit() {
                return this.unit;
            }

            public void setUnit(String unit) {
                this.unit = unit;
            }

            public String getDataIdentifierId() {
                return this.dataIdentifierId;
            }

            public void setDataIdentifierId(String dataIdentifierId) {
                this.dataIdentifierId = dataIdentifierId;
            }

            public String getDmeName() {
                return this.dmeName;
            }

            public void setDmeName(String dmeName) {
                this.dmeName = dmeName;
            }

            public String toString() {
                return new StringJoiner(", ", SnapshotResponseItemVO.class.getSimpleName() + "[", "]").add("dmeName='" + this.dmeName + "'").add("name='" + this.name + "'").add("originalName='" + this.originalName + "'").add("dataIdentifierId='" + this.dataIdentifierId + "'").add("value='" + this.value + "'").add("unit='" + this.unit + "'").toString();
            }
        }
    }
}
