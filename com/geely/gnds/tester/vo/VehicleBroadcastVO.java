package com.geely.gnds.tester.vo;

import com.geely.gnds.tester.dto.BroadcastDto;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.VdnDataDto;
import java.util.List;

/* loaded from: VehicleBroadcastVO.class */
public class VehicleBroadcastVO {
    private List<EcuBroadcastDto> rd;
    private List<VehicleVdnData> re;
    private BroadcastDto rf;
    private VehicleConfigVO rg;

    public List<EcuBroadcastDto> getEcuBroadcast() {
        return this.rd;
    }

    public void setEcuBroadcast(List<EcuBroadcastDto> ecuBroadcast) {
        this.rd = ecuBroadcast;
    }

    public List<VehicleVdnData> getVdnDatas() {
        return this.re;
    }

    public void setVdnDatas(List<VehicleVdnData> vdnDatas) {
        this.re = vdnDatas;
    }

    public BroadcastDto getGeneralAttribute() {
        return this.rf;
    }

    public void setGeneralAttribute(BroadcastDto generalAttribute) {
        this.rf = generalAttribute;
    }

    public VehicleConfigVO getConfigVO() {
        return this.rg;
    }

    public void setConfigVO(VehicleConfigVO configVO) {
        this.rg = configVO;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("VehicleBroadcastVO{");
        sb.append("ecuBroadcast=").append(this.rd);
        sb.append(", vdnDatas=").append(this.re);
        sb.append(", generalAttribute=").append(this.rf);
        sb.append(", configVO=").append(this.rg);
        sb.append('}');
        return sb.toString();
    }

    /* loaded from: VehicleBroadcastVO$VehicleVdnData.class */
    public static class VehicleVdnData extends VdnDataDto {
        private String rh;
        private String ri;
        private String rj;
        private String rk;
        private String rl;

        public String getGroupsName() {
            return this.rh;
        }

        public void setGroupsName(String groupsName) {
            this.rh = groupsName;
        }

        public String getGroupValue() {
            return this.ri;
        }

        public void setGroupValue(String groupValue) {
            this.ri = groupValue;
        }

        public String getGroupAttribute() {
            return this.rj;
        }

        public void setGroupAttribute(String groupAttribute) {
            this.rj = groupAttribute;
        }

        public String getGroupLd() {
            return this.rk;
        }

        public void setGroupLd(String groupLd) {
            this.rk = groupLd;
        }

        public String getGroupSd() {
            return this.rl;
        }

        public void setGroupSd(String groupSd) {
            this.rl = groupSd;
        }

        public String toString() {
            StringBuilder sb = new StringBuilder("VehicleVdnData{");
            sb.append("groupsName='").append(this.rh).append('\'');
            sb.append(", groupValue='").append(this.ri).append('\'');
            sb.append(", groupAttribute='").append(this.rj).append('\'');
            sb.append(", groupLd='").append(this.rk).append('\'');
            sb.append(", groupSd='").append(this.rl).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }
}
