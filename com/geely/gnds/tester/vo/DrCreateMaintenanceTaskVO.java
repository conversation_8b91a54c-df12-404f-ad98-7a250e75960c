package com.geely.gnds.tester.vo;

import com.alibaba.fastjson.JSONObject;
import java.util.List;

/* loaded from: DrCreateMaintenanceTaskVO.class */
public class DrCreateMaintenanceTaskVO {
    private String qk;
    private String ql;
    private String qm;
    private String vin;
    private String model;
    private String qn;
    private List<String> qo;
    private List<JSONObject> qp;

    public String getExternalUsername() {
        return this.qk;
    }

    public void setExternalUsername(String externalUsername) {
        this.qk = externalUsername;
    }

    public String getServiceStationId() {
        return this.ql;
    }

    public void setServiceStationId(String serviceStationId) {
        this.ql = serviceStationId;
    }

    public String getServiceStation() {
        return this.qm;
    }

    public void setServiceStation(String serviceStation) {
        this.qm = serviceStation;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getElectronicControlSystem() {
        return this.qn;
    }

    public void setElectronicControlSystem(String electronicControlSystem) {
        this.qn = electronicControlSystem;
    }

    public List<String> getVdsSymptoms() {
        return this.qo;
    }

    public void setVdsSymptoms(List<String> vdsSymptoms) {
        this.qo = vdsSymptoms;
    }

    public List<JSONObject> getEcuInfoList() {
        return this.qp;
    }

    public void setEcuInfoList(List<JSONObject> ecuInfoList) {
        this.qp = ecuInfoList;
    }
}
