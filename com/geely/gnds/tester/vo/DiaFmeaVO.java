package com.geely.gnds.tester.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/* loaded from: DiaFmeaVO.class */
public class DiaFmeaVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String pZ;
    private String qa;
    private String variant;
    private String qb;
    private String structureWeekFrom;
    private String structureWeekTo;
    private String qc;
    private Integer qd;
    private Boolean qe;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFmeaTranslationName() {
        return this.qa;
    }

    public void setFmeaTranslationName(String fmeaTranslationName) {
        this.qa = fmeaTranslationName;
    }

    public String getCustomerFunction() {
        return this.pZ;
    }

    public void setCustomerFunction(String customerFunction) {
        this.pZ = customerFunction;
    }

    public String getVariant() {
        return this.variant;
    }

    public void setVariant(String variant) {
        this.variant = variant;
    }

    public String getActive() {
        return this.qb;
    }

    public void setActive(String active) {
        this.qb = active;
    }

    public String getStructureWeekFrom() {
        return this.structureWeekFrom;
    }

    public void setStructureWeekFrom(String structureWeekFrom) {
        this.structureWeekFrom = structureWeekFrom;
    }

    public String getStructureWeekTo() {
        return this.structureWeekTo;
    }

    public void setStructureWeekTo(String structureWeekTo) {
        this.structureWeekTo = structureWeekTo;
    }

    public String getCustomerFunctionType() {
        return this.qc;
    }

    public void setCustomerFunctionType(String customerFunctionType) {
        this.qc = customerFunctionType;
    }

    public Integer getScore() {
        return this.qd;
    }

    public void setScore(Integer score) {
        this.qd = score;
    }

    public Boolean getHasFault() {
        return this.qe;
    }

    public void setHasFault(Boolean hasFault) {
        this.qe = hasFault;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("DiaFmeaVO{");
        sb.append("id=").append(this.id);
        sb.append(", customerFunction='").append(this.pZ).append('\'');
        sb.append(", variant='").append(this.variant).append('\'');
        sb.append(", active='").append(this.qb).append('\'');
        sb.append(", structureWeekFrom='").append(this.structureWeekFrom).append('\'');
        sb.append(", structureWeekTo='").append(this.structureWeekTo).append('\'');
        sb.append(", customerFunctionType='").append(this.qc).append('\'');
        sb.append(", score=").append(this.qd);
        sb.append(", hasFault=").append(this.qe);
        sb.append('}');
        return sb.toString();
    }
}
