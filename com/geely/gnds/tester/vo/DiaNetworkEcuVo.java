package com.geely.gnds.tester.vo;

import com.geely.gnds.tester.dto.DiaNetworkEcuWdidDto;
import java.util.List;
import java.util.Objects;

/* loaded from: DiaNetworkEcuVo.class */
public class DiaNetworkEcuVo {
    String qi;
    String fileName;
    List<DiaNetworkEcuWdidDto> qj;

    public String getFileId() {
        return this.qi;
    }

    public void setFileId(String fileId) {
        this.qi = fileId;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public List<DiaNetworkEcuWdidDto> getEcuWdid() {
        return this.qj;
    }

    public void setEcuWdid(List<DiaNetworkEcuWdidDto> ecuWdid) {
        this.qj = ecuWdid;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DiaNetworkEcuVo that = (DiaNetworkEcuVo) o;
        return Objects.equals(this.qi, that.qi) && Objects.equals(this.fileName, that.fileName) && Objects.equals(this.qj, that.qj);
    }

    public String toString() {
        return "DiaNetworkEcuVo{fileId='" + this.qi + "', fileName='" + this.fileName + "', ecuWdid=" + this.qj + '}';
    }

    public int hashCode() {
        return Objects.hash(this.qi, this.fileName, this.qj);
    }

    public DiaNetworkEcuVo() {
    }

    public DiaNetworkEcuVo(String fileId, String fileName, List<DiaNetworkEcuWdidDto> ecuWdid) {
        this.qi = fileId;
        this.fileName = fileName;
        this.qj = ecuWdid;
    }
}
