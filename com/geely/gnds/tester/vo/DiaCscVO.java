package com.geely.gnds.tester.vo;

/* loaded from: DiaCscVO.class */
public class DiaCscVO {
    private Long id;
    private String pK;
    private String status;
    private String pL;
    private String pM;
    private String pN;
    private String pO;
    private String component;
    private String pP;
    private String pQ;
    private String pR;
    private String pS;
    private String pT;
    private String pU;
    private String pV;
    private String pW;
    private String remark;
    private String listName;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSymptomId() {
        return this.pK;
    }

    public void setSymptomId(String symptomId) {
        this.pK = symptomId;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCsc() {
        return this.pL;
    }

    public String getTranComponent() {
        return this.pP;
    }

    public void setTranComponent(String tranComponent) {
        this.pP = tranComponent;
    }

    public String getTranDeviation() {
        return this.pR;
    }

    public void setTranDeviation(String tranDeviation) {
        this.pR = tranDeviation;
    }

    public String getTranCondition1() {
        return this.pT;
    }

    public void setTranCondition1(String tranCondition1) {
        this.pT = tranCondition1;
    }

    public void setCsc(String csc) {
        this.pL = csc;
    }

    public String getSymptomType() {
        return this.pM;
    }

    public void setSymptomType(String symptomType) {
        this.pM = symptomType;
    }

    public String getFunctionGroup1() {
        return this.pN;
    }

    public void setFunctionGroup1(String functionGroup1) {
        this.pN = functionGroup1;
    }

    public String getFunctionGroup2() {
        return this.pO;
    }

    public void setFunctionGroup2(String functionGroup2) {
        this.pO = functionGroup2;
    }

    public String getComponent() {
        return this.component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getDeviation() {
        return this.pQ;
    }

    public void setDeviation(String deviation) {
        this.pQ = deviation;
    }

    public String getCondition1() {
        return this.pS;
    }

    public void setCondition1(String condition1) {
        this.pS = condition1;
    }

    public String getCondition2() {
        return this.pU;
    }

    public void setCondition2(String condition2) {
        this.pU = condition2;
    }

    public String getValidFromDate() {
        return this.pV;
    }

    public void setValidFromDate(String validFromDate) {
        this.pV = validFromDate;
    }

    public String getProtocol() {
        return this.pW;
    }

    public void setProtocol(String protocol) {
        this.pW = protocol;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getListName() {
        return this.listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("DiaCscVO{");
        sb.append("id=").append(this.id);
        sb.append(", symptomId='").append(this.pK).append('\'');
        sb.append(", status='").append(this.status).append('\'');
        sb.append(", csc='").append(this.pL).append('\'');
        sb.append(", symptomType='").append(this.pM).append('\'');
        sb.append(", functionGroup1='").append(this.pN).append('\'');
        sb.append(", functionGroup2='").append(this.pO).append('\'');
        sb.append(", component='").append(this.component).append('\'');
        sb.append(", deviation='").append(this.pQ).append('\'');
        sb.append(", condition1='").append(this.pS).append('\'');
        sb.append(", condition2='").append(this.pU).append('\'');
        sb.append(", validFromDate='").append(this.pV).append('\'');
        sb.append(", protocol='").append(this.pW).append('\'');
        sb.append(", remark='").append(this.remark).append('\'');
        sb.append(", listName='").append(this.listName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
