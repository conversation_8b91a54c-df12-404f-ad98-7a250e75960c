package com.geely.gnds.tester.vo;

import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import java.util.Date;
import java.util.List;

/* loaded from: VehicleConfigVO.class */
public class VehicleConfigVO {
    private List<DiagResItemParseResultDto> rm;
    private List<DiagResItemParseResultDto> rn;
    protected Date updateTime;

    public List<DiagResItemParseResultDto> getMileage() {
        return this.rm;
    }

    public void setMileage(List<DiagResItemParseResultDto> mileage) {
        this.rm = mileage;
    }

    public List<DiagResItemParseResultDto> getConfig() {
        return this.rn;
    }

    public void setConfig(List<DiagResItemParseResultDto> config) {
        this.rn = config;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("VehicleConfigVO{");
        sb.append("mileage=").append(this.rm);
        sb.append(", config=").append(this.rn);
        sb.append(", updateTime=").append(this.updateTime);
        sb.append('}');
        return sb.toString();
    }
}
