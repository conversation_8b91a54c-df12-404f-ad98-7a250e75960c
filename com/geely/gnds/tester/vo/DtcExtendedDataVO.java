package com.geely.gnds.tester.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/* loaded from: DtcExtendedDataVO.class */
public class DtcExtendedDataVO implements Serializable {
    private static final long serialVersionUID = -8984570916460220458L;
    private List<ExtendedOccDataVO> qu;
    private List<ExtendedTimestampVO> qv;
    private List<ExtendedFdcVO> qw;
    private ExtendedIndicatorVO qx;

    public DtcExtendedDataVO() {
        setOccData(new ArrayList());
        setFdcData(new ArrayList());
        setTimestampData(new ArrayList());
        setIndicatorData(new ExtendedIndicatorVO());
    }

    public List<ExtendedOccDataVO> getOccData() {
        return this.qu;
    }

    public void setOccData(List<ExtendedOccDataVO> occData) {
        this.qu = occData;
    }

    public List<ExtendedTimestampVO> getTimestampData() {
        return this.qv;
    }

    public void setTimestampData(List<ExtendedTimestampVO> timestampData) {
        this.qv = timestampData;
    }

    public List<ExtendedFdcVO> getFdcData() {
        return this.qw;
    }

    public void setFdcData(List<ExtendedFdcVO> fdcData) {
        this.qw = fdcData;
    }

    public ExtendedIndicatorVO getIndicatorData() {
        return this.qx;
    }

    public void setIndicatorData(ExtendedIndicatorVO indicatorData) {
        this.qx = indicatorData;
    }

    public String toString() {
        return new StringJoiner(", ", DtcExtendedDataVO.class.getSimpleName() + "[", "]").add("occData=" + this.qu).add("timestampData=" + this.qv).add("fdcData=" + this.qw).add("indicatorData=" + this.qx).toString();
    }

    /* loaded from: DtcExtendedDataVO$BaseExtendedVO.class */
    public static class BaseExtendedVO<R> {
        private String name;
        private R value;

        public String getName() {
            return this.name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public R getValue() {
            return this.value;
        }

        public void setValue(R value) {
            this.value = value;
        }

        public String toString() {
            return new StringJoiner(", ", BaseExtendedVO.class.getSimpleName() + "[", "]").add("name='" + this.name + "'").add("value=" + this.value).toString();
        }
    }

    /* loaded from: DtcExtendedDataVO$ExtendedOccDataVO.class */
    public static class ExtendedOccDataVO extends BaseExtendedVO {
        @Override // com.geely.gnds.tester.vo.DtcExtendedDataVO.BaseExtendedVO
        public String toString() {
            return new StringJoiner(", ", ExtendedOccDataVO.class.getSimpleName() + "[", "]").add("name='" + super.getName() + "'").add("value=" + super.getValue()).toString();
        }
    }

    /* loaded from: DtcExtendedDataVO$ExtendedTimestampVO.class */
    public static class ExtendedTimestampVO extends BaseExtendedVO {
        @Override // com.geely.gnds.tester.vo.DtcExtendedDataVO.BaseExtendedVO
        public String toString() {
            return new StringJoiner(", ", ExtendedTimestampVO.class.getSimpleName() + "[", "]").add("name='" + super.getName() + "'").add("value=" + super.getValue()).toString();
        }
    }

    /* loaded from: DtcExtendedDataVO$ExtendedFdcVO.class */
    public static class ExtendedFdcVO extends BaseExtendedVO {
        @Override // com.geely.gnds.tester.vo.DtcExtendedDataVO.BaseExtendedVO
        public String toString() {
            return new StringJoiner(", ", ExtendedFdcVO.class.getSimpleName() + "[", "]").add("name='" + super.getName() + "'").add("value=" + super.getValue()).toString();
        }
    }
}
