package com.geely.gnds.tester.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/* loaded from: DiaGcidVo.class */
public class DiaGcidVo {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String gcid;
    private String gcidName;
    private String gcidTranName;
    private String locationName;
    private String locationTranName;
    private String listName;
    private String wdid;
    private String type;
    private String ecu;
    private String structureWeekFrom;
    private String structureWeekTo;
    private String variant;
    private String qh;
    private String pL;
    private String score;
    private String dtcId;
    private String historyFailureRate;

    public String getHistoryFailureRate() {
        return this.historyFailureRate;
    }

    public void setHistoryFailureRate(String historyFailureRate) {
        this.historyFailureRate = historyFailureRate;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGcid() {
        return this.gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public String getGcidName() {
        return this.gcidName;
    }

    public void setGcidName(String gcidName) {
        this.gcidName = gcidName;
    }

    public String getLocationName() {
        return this.locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getListName() {
        return this.listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public String getWdid() {
        return this.wdid;
    }

    public void setWdid(String wdid) {
        this.wdid = wdid;
    }

    public String getEcu() {
        return this.ecu;
    }

    public String getGcidTranName() {
        return this.gcidTranName;
    }

    public void setGcidTranName(String gcidTranName) {
        this.gcidTranName = gcidTranName;
    }

    public String getLocationTranName() {
        return this.locationTranName;
    }

    public void setLocationTranName(String locationTranName) {
        this.locationTranName = locationTranName;
    }

    public void setEcu(String ecu) {
        this.ecu = ecu;
    }

    public String getStructureWeekFrom() {
        return this.structureWeekFrom;
    }

    public void setStructureWeekFrom(String structureWeekFrom) {
        this.structureWeekFrom = structureWeekFrom;
    }

    public String getStructureWeekTo() {
        return this.structureWeekTo;
    }

    public void setStructureWeekTo(String structureWeekTo) {
        this.structureWeekTo = structureWeekTo;
    }

    public String getVariant() {
        return this.variant;
    }

    public void setVariant(String variant) {
        this.variant = variant;
    }

    public String getWindowMessage() {
        return this.qh;
    }

    public void setWindowMessage(String windowMessage) {
        this.qh = windowMessage;
    }

    public String getCsc() {
        return this.pL;
    }

    public void setCsc(String csc) {
        this.pL = csc;
    }

    public String getScore() {
        return this.score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getDtcId() {
        return this.dtcId;
    }

    public void setDtcId(String dtcId) {
        this.dtcId = dtcId;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("DiaGcidVo{");
        sb.append("id=").append(this.id);
        sb.append(", gcid='").append(this.gcid).append('\'');
        sb.append(", gcidName='").append(this.gcidName).append('\'');
        sb.append(", gcidTranName='").append(this.gcidTranName).append('\'');
        sb.append(", locationName='").append(this.locationName).append('\'');
        sb.append(", locationTranName='").append(this.locationTranName).append('\'');
        sb.append(", listName='").append(this.listName).append('\'');
        sb.append(", wdid='").append(this.wdid).append('\'');
        sb.append(", ecu='").append(this.ecu).append('\'');
        sb.append(", structureWeekFrom='").append(this.structureWeekFrom).append('\'');
        sb.append(", structureWeekTo='").append(this.structureWeekTo).append('\'');
        sb.append(", variant='").append(this.variant).append('\'');
        sb.append(", windowMessage='").append(this.qh).append('\'');
        sb.append(", csc='").append(this.pL).append('\'');
        sb.append(", score='").append(this.score).append('\'');
        sb.append(", dtcId='").append(this.dtcId).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
