package com.geely.gnds.tester.aspect;

import com.geely.gnds.tester.common.EncryptContext;
import com.geely.gnds.tester.common.EncryptRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

@Aspect
@Component
/* loaded from: EncryptRequestAspect.class */
public class EncryptRequestAspect implements InitializingBean {
    public void afterPropertiesSet() throws Exception {
    }

    @Around("@annotation(encryptRequest)")
    public Object handleEncryption(ProceedingJoinPoint joinPoint, EncryptRequest encryptRequest) throws Throwable {
        try {
            if (encryptRequest.encryptRequest()) {
                EncryptContext.enableEncryptRequest();
            }
            if (encryptRequest.encryptResponse()) {
                EncryptContext.enableEncryptResponse();
            }
            Object objProceed = joinPoint.proceed();
            EncryptContext.clear();
            return objProceed;
        } catch (Throwable th) {
            EncryptContext.clear();
            throw th;
        }
    }
}
