package com.geely.gnds.tester.security;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import org.springframework.util.Base64Utils;

/* loaded from: CloudApiRsaUtils.class */
public class CloudApiRsaUtils {
    public static final RSA oU = new RSA();

    public static String getPublicKey() {
        return Base64Utils.encodeToUrlSafeString(oU.getPublicKey().getEncoded());
    }

    public static String cr(String data) {
        return oU.decryptStr(data, KeyType.PrivateKey);
    }
}
