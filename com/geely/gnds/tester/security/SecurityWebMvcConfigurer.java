package com.geely.gnds.tester.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
/* loaded from: SecurityWebMvcConfigurer.class */
public class SecurityWebMvcConfigurer implements WebMvcConfigurer {

    @Autowired
    private CloudTokenHandlerInterceptor cloudTokenHandlerInterceptor;

    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(this.cloudTokenHandlerInterceptor).excludePathPatterns(new String[]{"/api/v1/login/getRequestKey", "/sendMobileCode", "/ds/**", "/api/v1/log/timeoutLog", "/api/v1/circuitDiagram/getCircuitDiagram", "/api/v1/circuitDiagram/getImage", "/api/v1/circuitDiagram/getImageByWdid", "/api/v1/circuitDiagram/getHarnessImage", "/api/v1/networkTopology/getNetworkTopology", "/login", "/api/v1/login/**", "/api/v1/version/**", "/api/v1/upgrade/**", "/actuator/health", "/api/v1/testerService/**", "/api/v1/testerConfig/color", "/api/v1/testerConfig/getConfig", "/getTenantInfo", "/api/v1/socket/**", "/api/v1/seq/refreshUi", "/api/v1/doc/getFile/**"});
    }
}
