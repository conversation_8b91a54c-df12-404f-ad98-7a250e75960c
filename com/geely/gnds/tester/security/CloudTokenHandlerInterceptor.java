package com.geely.gnds.tester.security;

import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.exception.UnAuthException;
import com.google.common.util.concurrent.RateLimiter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
/* loaded from: CloudTokenHandlerInterceptor.class */
public class CloudTokenHandlerInterceptor implements HandlerInterceptor {
    private static final Logger LOG = LoggerFactory.getLogger(CloudTokenHandlerInterceptor.class);

    @Autowired
    private Cloud cloud;
    RateLimiter limiter = RateLimiter.create(0.0033333333333333335d);

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (StringUtils.isNotEmpty(request.getHeader("Authorization"))) {
            String url = request.getRequestURI();
            try {
                if (this.limiter.tryAcquire()) {
                    this.cloud.K();
                }
            } catch (UnAuthException e) {
                LOG.error("CloudTokenHandlerInterceptor--------云端token校验------已失效,{}", url, e);
                throw e;
            } catch (Exception e2) {
                LOG.error("CloudTokenHandlerInterceptor--------请求云端失败,{}", url, e2);
            }
        }
        return super.preHandle(request, response, handler);
    }
}
