package com.geely.gnds.tester.entity;

/* loaded from: TesterSoftwareEntity.class */
public class TesterSoftwareEntity extends TesterBaseEntity {
    private static final long serialVersionUID = -2536689803667375390L;
    private Long id;
    private String address;
    private String name;
    private String fileName;
    private String softwareType;
    private String softwareFormat;
    private String softwareNumber;
    private String softwareVersion;
    private String status;
    private String description;
    private Long vbfSize;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getSoftwareType() {
        return this.softwareType;
    }

    public void setSoftwareType(String softwareType) {
        this.softwareType = softwareType;
    }

    public String getSoftwareFormat() {
        return this.softwareFormat;
    }

    public void setSoftwareFormat(String softwareFormat) {
        this.softwareFormat = softwareFormat;
    }

    public String getSoftwareNumber() {
        return this.softwareNumber;
    }

    public void setSoftwareNumber(String softwareNumber) {
        this.softwareNumber = softwareNumber;
    }

    public String getSoftwareVersion() {
        return this.softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getVbfSize() {
        return this.vbfSize;
    }

    public void setVbfSize(Long vbfSize) {
        this.vbfSize = vbfSize;
    }
}
