package com.geely.gnds.tester.entity;

/* loaded from: TesterRecentEntity.class */
public class TesterRecentEntity extends TesterBaseEntity {
    private Long id;
    private String vin;
    private String vehicle;
    private String manufacturingYear;
    private String brandName;
    private Long brandId;
    private String operator;
    private String broadcast;

    public String getVehicle() {
        return this.vehicle;
    }

    public Long getBrandId() {
        return this.brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public void setVehicle(String vehicle) {
        this.vehicle = vehicle;
    }

    public String getManufacturingYear() {
        return this.manufacturingYear;
    }

    public void setManufacturingYear(String manufacturingYear) {
        this.manufacturingYear = manufacturingYear;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getOperator() {
        return this.operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getBroadcast() {
        return this.broadcast;
    }

    public void setBroadcast(String broadcast) {
        this.broadcast = broadcast;
    }

    public String getBrandName() {
        return this.brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }
}
