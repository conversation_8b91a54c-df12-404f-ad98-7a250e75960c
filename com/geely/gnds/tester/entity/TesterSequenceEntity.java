package com.geely.gnds.tester.entity;

/* loaded from: TesterSequenceEntity.class */
public class TesterSequenceEntity extends TesterBaseEntity {
    private static final long serialVersionUID = 1279725765113115131L;
    private Long id;
    private String sequenceId;
    private String name;
    private String applicability;
    private String version;
    private String status;
    private String description;
    private String seqContent;

    public String getSeqContent() {
        return this.seqContent;
    }

    public void setSeqContent(String seqContent) {
        this.seqContent = seqContent;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSequenceId() {
        return this.sequenceId;
    }

    public void setSequenceId(String sequenceId) {
        this.sequenceId = sequenceId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getApplicability() {
        return this.applicability;
    }

    public void setApplicability(String applicability) {
        this.applicability = applicability;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
