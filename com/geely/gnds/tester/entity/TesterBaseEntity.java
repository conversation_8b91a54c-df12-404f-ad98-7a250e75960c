package com.geely.gnds.tester.entity;

import java.io.Serializable;
import java.util.Date;

/* loaded from: TesterBaseEntity.class */
public class TesterBaseEntity implements Serializable {
    private static final long serialVersionUID = 1;
    protected String createBy;
    protected Date createTime;
    protected String updateBy;
    protected Date updateTime;

    public static long getSerialVersionUid() {
        return serialVersionUID;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
