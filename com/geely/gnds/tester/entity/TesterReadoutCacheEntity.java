package com.geely.gnds.tester.entity;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: TesterReadoutCacheEntity.class */
public class TesterReadoutCacheEntity extends TesterBaseEntity {
    private Long id;
    private String vehicleReadout;
    private String dtcInfo;
    private String lang;
    private String cacheTime;
    private String vin;
    private String confirmedDtcList;
    private String unConfirmedDtcList;
    private String vehicleConfig;
    private String globalTime;
    private Date createGlobalTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDtcInfo() {
        return this.dtcInfo;
    }

    public void setDtcInfo(String dtcInfo) {
        this.dtcInfo = dtcInfo;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getLang() {
        return this.lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getCacheTime() {
        return this.cacheTime;
    }

    public void setCacheTime(String cacheTime) {
        this.cacheTime = cacheTime;
    }

    public String getConfirmedDtcList() {
        return this.confirmedDtcList;
    }

    public void setConfirmedDtcList(String confirmedDtcList) {
        this.confirmedDtcList = confirmedDtcList;
    }

    public String getUnConfirmedDtcList() {
        return this.unConfirmedDtcList;
    }

    public void setUnConfirmedDtcList(String unConfirmedDtcList) {
        this.unConfirmedDtcList = unConfirmedDtcList;
    }

    public String getVehicleConfig() {
        return this.vehicleConfig;
    }

    public void setVehicleConfig(String vehicleConfig) {
        this.vehicleConfig = vehicleConfig;
    }

    public String getVehicleReadout() {
        return this.vehicleReadout;
    }

    public void setVehicleReadout(String vehicleReadout) {
        this.vehicleReadout = vehicleReadout;
    }

    public String getGlobalTime() {
        return this.globalTime;
    }

    public void setGlobalTime(String globalTime) {
        this.globalTime = globalTime;
    }

    public Date getCreateGlobalTime() {
        return this.createGlobalTime;
    }

    public void setCreateGlobalTime(Date createGlobalTime) {
        this.createGlobalTime = createGlobalTime;
    }

    public String toString() {
        return new ToStringBuilder(this).append("id", this.id).append("vehicleReadout", this.vehicleReadout).append("dtcInfo", this.dtcInfo).append("lang", this.lang).append("cacheTime", this.cacheTime).append("vin", this.vin).append("confirmedDtcList", this.confirmedDtcList).append("unConfirmedDtcList", this.unConfirmedDtcList).append("vehicleConfig", this.vehicleConfig).append("globalTime", this.globalTime).append("createGlobalTime", this.createGlobalTime).toString();
    }
}
