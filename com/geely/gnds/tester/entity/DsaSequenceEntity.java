package com.geely.gnds.tester.entity;

/* loaded from: DsaSequenceEntity.class */
public class DsaSequenceEntity extends TesterBaseEntity {
    private static final long serialVersionUID = 1279725765113115991L;
    private Long id;
    private String sequenceCode;
    private String buttonName;
    private String version;
    private String seqcontentCn;
    private String seqcontentEn;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSequenceCode() {
        return this.sequenceCode;
    }

    public void setSequenceCode(String sequenceCode) {
        this.sequenceCode = sequenceCode;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getButtonName() {
        return this.buttonName;
    }

    public void setButtonName(String buttonName) {
        this.buttonName = buttonName;
    }

    public String getSeqcontentEn() {
        return this.seqcontentEn;
    }

    public void setSeqcontentEn(String seqcontentEn) {
        this.seqcontentEn = seqcontentEn;
    }

    public String cl(String lang) {
        if (!"zh_CN".equals(lang)) {
            return this.seqcontentEn;
        }
        return this.seqcontentCn;
    }

    public String getSeqcontentCn() {
        return this.seqcontentCn;
    }

    public void setSeqcontentCn(String seqcontentCn) {
        this.seqcontentCn = seqcontentCn;
    }
}
