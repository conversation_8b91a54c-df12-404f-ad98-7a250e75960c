package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.vo.VehicleBroadcastVO;
import com.geely.gnds.tester.vo.VehicleConfigVO;
import com.geely.gnds.tester.vo.VehicleMaintainVO;
import java.util.List;

/* loaded from: VehicleDetailsService.class */
public interface VehicleDetailsService {
    VehicleConfigVO vehicleConfig(VehicleBroadcastVO vehicleBroadcastVO, String str) throws Exception;

    List<DiagResItemParseResultDto> analysisVehicleConfig(String str, String str2, String str3, String str4, String str5) throws Exception;

    VehicleBroadcastVO getVehicleBroadcast(String str) throws Exception;

    void vehicleRealtimeCheck(String str, String str2) throws Exception;

    List<VehicleMaintainVO> vehicleHealthInspection(String str, String str2) throws Exception;

    void vehicleMaintainReset(String str, String str2, List<String> list) throws Exception;
}
