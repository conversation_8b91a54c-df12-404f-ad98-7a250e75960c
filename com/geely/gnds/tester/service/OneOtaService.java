package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.OneOtaDTO;
import com.geely.gnds.tester.dto.SimpleResultDTO;
import java.util.List;

/* loaded from: OneOtaService.class */
public interface OneOtaService {
    List<OneOtaDTO> getOneOtaList(String str) throws Exception;

    String installation(Long l) throws Exception;

    String withdrawn(String str) throws Exception;

    SimpleResultDTO remoteUpgrade(String str) throws Exception;
}
