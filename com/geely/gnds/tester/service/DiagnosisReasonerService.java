package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.DrCscNode;
import com.geely.gnds.tester.vo.DrCreateMaintenanceTaskVO;
import java.util.List;

/* loaded from: DiagnosisReasonerService.class */
public interface DiagnosisReasonerService {
    List<DrCscNode> getCscList() throws Exception;

    Object createMaintenanceTask(DrCreateMaintenanceTaskVO drCreateMaintenanceTaskVO) throws Exception;

    Object getExistingMaintenanceTask(String str, String str2, String str3) throws Exception;

    Object getMaintenanceTask(String str) throws Exception;

    Object faultTest(String str, String str2, String str3) throws Exception;

    Object revokeFaultTest(String str, String str2, String str3) throws Exception;

    Object executeMaintenancePlanFixed(String str, int i, int i2, String str2) throws Exception;

    Object executeMaintenancePlanNotFixed(String str, int i) throws Exception;

    Object getMaintenanceTaskCloseOptions() throws Exception;

    Object closeMaintenanceTask(String str, Integer num, String str2) throws Exception;

    Object deleteTask(String str) throws Exception;

    Object revokeMaintenancePlan(String str, Integer num) throws Exception;
}
