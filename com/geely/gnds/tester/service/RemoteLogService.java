package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.RemoteLogCollectionDTO;
import com.geely.gnds.tester.dto.RemoteLogConfigDTO;
import com.geely.gnds.tester.dto.RemoteLogUploadProgressDTO;

/* loaded from: RemoteLogService.class */
public interface RemoteLogService {
    RemoteLogConfigDTO query(String str) throws Exception;

    RemoteLogCollectionDTO remoteLogDetail(String str) throws Exception;

    RemoteLogCollectionDTO remoteLogSetLevel(String str, Integer num, Integer num2, Boolean bool, String str2) throws Exception;

    RemoteLogCollectionDTO remoteLogSetLogConfig(String str, Integer num, Integer num2, Boolean bool, String str2, String str3, String str4) throws Exception;

    RemoteLogCollectionDTO remoteLogSetTime(String str, String str2, String str3) throws Exception;

    RemoteLogCollectionDTO remoteLogGetOssPath(String str) throws Exception;

    Boolean remoteLogCancel(String str) throws Exception;

    RemoteLogUploadProgressDTO getVehicleUploadProgress(String str, String str2, String str3) throws Exception;
}
