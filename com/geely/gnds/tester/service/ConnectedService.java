package com.geely.gnds.tester.service;

import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.VehicleDto;
import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

/* loaded from: ConnectedService.class */
public interface ConnectedService {
    List<VehicleDto> queryByTask(String str) throws Exception;

    Map getVehicleInfo(String str) throws Exception;

    VehicleDto getVehicle(String str) throws Exception;

    void disconnect(String str, String str2) throws Exception;

    void disconnect(String str, String str2, String str3) throws Exception;

    String connectCreate(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;

    void connectTcp(VehicleDto vehicleDto) throws Exception;

    List<VehicleDto> query(String str) throws Exception;

    VehicleDto queryVirtualVehicle(MultipartFile multipartFile) throws Exception;

    void connected(String str, String str2) throws Exception;

    Boolean connectSuccessUploadServer(String str) throws Exception;
}
