package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.OtherSeqDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.entity.TesterSequenceEntity;
import java.util.List;

/* loaded from: SequenceService.class */
public interface SequenceService {
    void save(ReloadDto reloadDto);

    List<TesterSequenceEntity> query();

    List<OtherSeqDto> getSeqList(String str, String str2, String str3, String str4, String str5) throws Exception;

    List<OtherSeqDto> getDebugSeqList(String str) throws Exception;
}
