package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.LogCollectionDto;
import com.geely.gnds.tester.dto.LogCollectionProgressDto;
import com.geely.gnds.tester.dto.PageWorkOrderDto;
import com.geely.gnds.tester.dto.SupportWorkOrderDto;
import javax.servlet.http.HttpServletResponse;

/* loaded from: SupportService.class */
public interface SupportService {
    void logCollection(LogCollectionDto logCollectionDto, String str) throws Throwable;

    LogCollectionProgressDto getLogCollectionProgress(String str);

    String submitSupportWorkOrder(SupportWorkOrderDto supportWorkOrderDto) throws Exception;

    void saveLog(LogCollectionDto logCollectionDto) throws Exception;

    void deleteLog(String str) throws Exception;

    String getLogUrl(String str) throws Exception;

    PageWorkOrderDto queryByPageForClient(int i, int i2, Long l, String str, String str2) throws Exception;

    Boolean queryNotice(Long l) throws Exception;

    void attachmentDownload(String str, HttpServletResponse httpServletResponse) throws Exception;
}
