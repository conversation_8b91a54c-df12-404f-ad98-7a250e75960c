package com.geely.gnds.tester.service;

import com.geely.gnds.dsa.dto.SeqUiDto;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.VehicleStatusDto;
import org.springframework.web.multipart.MultipartFile;

/* loaded from: SeqService.class */
public interface SeqService {
    String init(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;

    String excute(InitializeUiDto initializeUiDto) throws Exception;

    String refreshUi(InitializeUiDto initializeUiDto) throws Exception;

    String handleClickEvent(InitializeUiDto initializeUiDto) throws Exception;

    String getSeqId(InitializeUiDto initializeUiDto) throws Exception;

    String stop(InitializeUiDto initializeUiDto) throws Exception;

    void notifySeq(InitializeUiDto initializeUiDto) throws Exception;

    VehicleStatusDto statusReadout(InitializeUiDto initializeUiDto);

    VehicleStatusDto statusDsa(InitializeUiDto initializeUiDto);

    VehicleStatusDto statusReadoutDsa(InitializeUiDto initializeUiDto);

    String initBtnSeq(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;

    String initStatusReadout(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;

    Integer getSeqStatus(String str) throws Exception;

    Integer getSeqStatus(String str, String str2) throws Exception;

    void handleUiData(InitializeUiDto initializeUiDto) throws Exception;

    ScriptHandler getScriptHandlerFun(InitializeUiDto initializeUiDto) throws Exception;

    SeqUiDto initDsaSeq(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;

    SeqUiDto initDsaSeqBrowser(String str, MultipartFile multipartFile, LoginUser loginUser) throws Exception;

    String initDsaCloudSeq(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;
}
