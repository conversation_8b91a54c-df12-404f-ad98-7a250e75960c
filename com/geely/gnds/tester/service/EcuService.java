package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.EcuReadoutDto;
import com.geely.gnds.tester.dto.ReloadDto;
import java.util.List;

/* loaded from: EcuService.class */
public interface EcuService {
    List<ReloadDto> getSeqByEcu(String str, String str2) throws Exception;

    List<EcuBroadcastDto> getEcuList(String str) throws Exception;

    List<EcuReadoutDto> getEcuReadout(String str, String str2) throws Exception;
}
