package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.CircuitDiagramOverviewDTO;
import com.geely.gnds.tester.dto.DiaImgDTO;
import com.geely.gnds.tester.dto.TranWithOrigin;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

/* loaded from: CircuitDiagramService.class */
public interface CircuitDiagramService {
    List<TranWithOrigin> getCircuitDiagramList(String str, String str2) throws Exception;

    void getCircuitDiagram(String str, String str2, HttpServletResponse httpServletResponse);

    Map<String, Object> getCircuitInfo(String str, String str2, String str3) throws Exception;

    List<Object> getCircuitExtend(String str, String str2, String str3, String str4) throws Exception;

    List<DiaImgDTO> getImageTypeList(String str, String str2, String str3) throws Exception;

    void getImage(String str, HttpServletResponse httpServletResponse, String str2);

    void getImageByWdid(String str, String str2, String str3, HttpServletResponse httpServletResponse);

    List<DiaImgDTO> getEcuImageTypeList(String str, String str2, String str3, String str4) throws Exception;

    void getHarnessImage(String str, String str2, String str3, HttpServletResponse httpServletResponse);

    List<String> filterCircuitDiagramNode(String str, String str2) throws Exception;

    List<TranWithOrigin> getSystemCircuitDiagramList(String str) throws Exception;

    List<CircuitDiagramOverviewDTO> getSystemCircuitDiagramOverviewList(List<String> list, String str) throws Exception;
}
