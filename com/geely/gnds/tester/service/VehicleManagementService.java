package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.VehicleManagementDto;
import java.util.List;
import java.util.Map;

/* loaded from: VehicleManagementService.class */
public interface VehicleManagementService {
    List<VehicleManagementDto> getVehicleList() throws Exception;

    Map disconnectVehicle(VehicleManagementDto vehicleManagementDto, String str) throws Exception;

    void confirmDisconnect(VehicleManagementDto vehicleManagementDto, String str) throws Exception;
}
