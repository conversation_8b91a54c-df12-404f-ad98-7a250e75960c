package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.QuickLinksDTO;
import java.util.List;

/* loaded from: QuickLinksService.class */
public interface QuickLinksService {
    QuickLinksDTO queryById(Integer num);

    List<QuickLinksDTO> getList();

    QuickLinksDTO insert(QuickLinksDTO quickLinksDTO);

    QuickLinksDTO update(QuickLinksDTO quickLinksDTO);

    QuickLinksDTO updateSort(QuickLinksDTO quickLinksDTO);

    boolean deleteById(Long l);
}
