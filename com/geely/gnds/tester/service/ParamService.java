package com.geely.gnds.tester.service;

import com.alibaba.fastjson.JSONArray;
import com.geely.gnds.tester.dto.DiagDmeDidDto;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Pageable;

/* loaded from: ParamService.class */
public interface ParamService {
    List<DiagResItemGroupDto> list(String str, String str2, String str3);

    Map<String, Object> list(String str, String str2, String str3, String str4, String str5, Pageable pageable);

    List<DiagResItemGroupDto> getFmeaParamList(String str, String str2, String str3, String str4, Pageable pageable);

    List<DiagResItemParseResultDto> readParam(List<DiagResItemGroupDto> list, String str);

    void parseResponseByDid(String str, List<DiagResItemParseResultDto> list, List<DiagResItemGroupDto> list2, String str2, List<String> list3);

    void parseResponseByDid2(String str, List<DiagResItemParseResultDto> list, List<DiagResItemGroupDto> list2, String str2, List<String> list3);

    void parseResponseByResItem(List<DiagResItemParseResultDto> list, DiagResItemGroupDto diagResItemGroupDto, List<DiagResItemDto> list2, String str);

    void parseRemoteResponseByResItem(List<DiagResItemParseResultDto> list, DiagResItemGroupDto diagResItemGroupDto, List<DiagResItemDto> list2, String str);

    DiagDmeDidDto getDidDme(String str, String str2, String str3, String str4, String str5, String str6) throws Exception;

    void initPinCode(String str, String str2, String str3);

    List<DiagResItemGroupDto> list2(String str, String str2, String str3, JSONArray jSONArray);

    void exportReadParamResult(HttpServletResponse httpServletResponse);
}
