package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.IndexSearchDto;
import com.geely.gnds.tester.dto.TechnicalReqDTO;
import com.geely.gnds.tester.dto.TechnicalResDTO;

/* loaded from: DocIndexService.class */
public interface DocIndexService {
    Object getIndexCondition(IndexSearchDto indexSearchDto) throws Exception;

    TechnicalResDTO getTechnologyList(TechnicalReqDTO technicalReqDTO) throws Exception;
}
