package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.vo.DiaGcidVo;
import java.util.List;

/* loaded from: GuidedDiagnosisService.class */
public interface GuidedDiagnosisService {
    List<DiaGcidVo> getComponentOtherList(String str) throws Exception;

    List<DiagResItemGroupDto> getComActivationList(String str, String str2, String str3) throws Exception;

    List<DiagResItemGroupDto> getComParamList(String str, String str2, String str3) throws Exception;

    List<DiagResItemGroupDto> getFmeaActivationList(String str, String str2, String str3, String str4) throws Exception;
}
