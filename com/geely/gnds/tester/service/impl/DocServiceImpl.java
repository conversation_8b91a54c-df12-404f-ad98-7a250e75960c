package com.geely.gnds.tester.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.service.DocService;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
/* loaded from: DocServiceImpl.class */
public class DocServiceImpl implements DocService {

    @Autowired
    private Cloud cloud;

    @Value("${fd.resource.docPath}")
    private String docPath;
    private File hS = AppConfig.getAppDataDir();
    private static final Logger log = LoggerFactory.getLogger(DocServiceImpl.class);

    @Override // com.geely.gnds.tester.service.DocService
    public String getDocUrl(String vin, String gcid, String location, String fmea, Integer docType) throws Exception {
        log.info("======> 进入getDocUrl方法 <======");
        log.info("======> 开始请求云端接口：cloud.getDocUrl <======");
        String docUrl = this.cloud.getDocUrl(vin, gcid, location, fmea, docType);
        if (StringUtils.isEmpty(docUrl)) {
            return docUrl;
        }
        log.info("======> 开始处理云端返回的docUrl <======");
        String fileName = docUrl.substring(docUrl.lastIndexOf("/") + 1);
        int temp = docUrl.indexOf("Doc/") + 4;
        String code = docUrl.substring(temp, docUrl.indexOf("/", temp));
        File dir = new File(this.hS, this.docPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String savePath = dir.getAbsolutePath() + File.separator + code;
        FileUtil.clean(savePath);
        log.info("======> 开始调用downLoadFromUrl方法 <======");
        String downFile = U(docUrl, fileName, savePath);
        log.info("======> 调用downLoadFromUrl方法结束 <======");
        ZipUtil.unzip(downFile, savePath);
        File file = new File(savePath);
        String url = "/api/v1/doc/getFile/" + code + "/";
        for (File f : (File[]) Objects.requireNonNull(file.listFiles())) {
            if (f.getName().endsWith("html") || f.getName().endsWith("htm") || f.getName().endsWith("HTM") || f.getName().endsWith("HTML")) {
                url = url + f.getName();
                break;
            }
        }
        log.info("======> 处理云端返回的docUrl结束 <======");
        return url;
    }

    private String U(String urlStr, String fileName, String savePath) throws IOException {
        FileOutputStream fos = null;
        InputStream inputStream = null;
        try {
            try {
                URL url = new URL(urlStr);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setConnectTimeout(5000);
                conn.setRequestProperty("Accept", "*/*");
                conn.setUseCaches(false);
                conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
                inputStream = conn.getInputStream();
                byte[] getData = b(inputStream);
                File saveDir = new File(savePath);
                if (!saveDir.exists()) {
                    saveDir.mkdir();
                }
                File file = new File(saveDir + File.separator + fileName);
                fos = new FileOutputStream(file);
                fos.write(getData);
                fos.flush();
                String str = saveDir + File.separator + fileName;
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e2) {
                        e2.printStackTrace();
                    }
                }
                return str;
            } catch (Exception e3) {
                e3.printStackTrace();
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (IOException e4) {
                        e4.printStackTrace();
                    }
                }
                if (inputStream == null) {
                    return "";
                }
                try {
                    inputStream.close();
                    return "";
                } catch (IOException e5) {
                    e5.printStackTrace();
                    return "";
                }
            }
        } catch (Throwable th) {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e6) {
                    e6.printStackTrace();
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e7) {
                    e7.printStackTrace();
                }
            }
            throw th;
        }
    }

    private byte[] b(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[DoipUtil.MAX_BYTE_ARRAY_SIZE];
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while (true) {
            int len = inputStream.read(buffer);
            if (len != -1) {
                bos.write(buffer, 0, len);
            } else {
                bos.close();
                return bos.toByteArray();
            }
        }
    }
}
