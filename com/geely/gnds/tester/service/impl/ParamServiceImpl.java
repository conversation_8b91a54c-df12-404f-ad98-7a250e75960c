package com.geely.gnds.tester.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.geely.gnds.doip.client.exception.DoipNegativeException;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.xml.XmlFault;
import com.geely.gnds.doip.client.xml.XmlSeq;
import com.geely.gnds.dsa.enums.LanguageEnum;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DiagDmeDidDto;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.seq.UdsSend;
import com.geely.gnds.tester.service.ParamService;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.StringParseUtils;
import com.geely.gnds.tester.util.TesterReadParamUtil;
import com.geely.gnds.tester.util.TesterVehicleResponseUtils;
import com.google.common.collect.Lists;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: ParamServiceImpl.class */
public class ParamServiceImpl implements ParamService {
    private static final Logger log = LoggerFactory.getLogger(ParamServiceImpl.class);

    @Autowired
    private Cloud cloud;

    @Autowired
    private TokenService tokenService;
    private SingletonManager manager = SingletonManager.getInstance();
    private UdsSend udsSend = null;

    private synchronized void initSend() {
        if (this.udsSend == null) {
            this.udsSend = new UdsSend();
        }
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public Map<String, Object> list(String vin, String diagnosticPartNumber, String ecuName, String p, String did, Pageable pageable) {
        Map<String, Object> map = new HashMap<>();
        log.info("/api/v1/param/list 进入service");
        List<DiagResItemGroupDto> list = list(vin, diagnosticPartNumber, ecuName);
        if (StringUtils.isNotBlank(did)) {
            list = (List) list.stream().filter(s -> {
                return StringUtils.containsIgnoreCase(s.getDataIdentifierId(), did);
            }).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(p)) {
            list = (List) list.stream().filter(s2 -> {
                return !CollectionUtil.isEmpty(s2.getResponseItemDtoList()) && StringUtils.containsIgnoreCase(s2.getResponseItemDtoList().get(0).getDmeName(), p);
            }).collect(Collectors.toList());
        }
        log.info("/api/v1/param/list 分页完成");
        map.put("total", Integer.valueOf(list.size()));
        map.put(AjaxResult.gC, ListUtil.page(pageable.getPageNumber(), pageable.getPageSize(), list));
        return map;
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public List<DiagResItemGroupDto> getFmeaParamList(String vin, String fmeaName, String p, String did, Pageable pageable) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        Optional.ofNullable(tcpClient).ifPresent(client -> {
            client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, "Get Param list Data".getBytes());
        });
        try {
            String json = this.cloud.s(vin, fmeaName);
            if (StringUtils.isBlank(json)) {
                return new ArrayList();
            }
            List<DiagResItemGroupDto> cloudGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
            List<DiagResItemGroupDto> result = getResItemGroupDtos(cloudGroups);
            return result;
        } catch (Exception e) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00087);
            log.error("{}VIN【{}】fmeaName【{}】", new Object[]{formatMsg, vin, fmeaName, e});
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                String content = String.format("%sVIN【%s】fmeaName【%s】", formatMsg, vin, fmeaName);
                client2.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
            });
            throw new CustomException(formatMsg);
        }
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public List<DiagResItemGroupDto> list(String vin, String diagnosticPartNumber, String ecuName) {
        log.info("/api/v1/param/list client.geTxtLogger().write开始");
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        Optional.ofNullable(tcpClient).ifPresent(client -> {
            client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, "Get Param list Data".getBytes());
        });
        log.info("/api/v1/param/list client.geTxtLogger().write结束");
        try {
            log.info("/api/v1/param/list cloud.getDidByDiaPartNumber开始");
            String json = this.cloud.c(vin, diagnosticPartNumber, "22", ecuName, HttpUtils.getLanguage());
            if (StringUtils.isBlank(json)) {
                return new ArrayList();
            }
            log.info("/api/v1/param/list cloud.getDidByDiaPartNumber结束");
            log.info("/api/v1/param/list cloud.getDidByDiaPartNumber入参:{}语言:{}返回数据:{}", new Object[]{diagnosticPartNumber, HttpUtils.getLanguage(), json});
            List<DiagResItemGroupDto> cloudGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
            log.info("/api/v1/param/list ObjectMapperUtils.jsonStr2List结束");
            List<DiagResItemGroupDto> result = getResItemGroupDtos(cloudGroups);
            return result;
        } catch (Exception e) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00087);
            log.error("{}VIN【{}】diagnosticPartNumber【{}】", new Object[]{formatMsg, vin, diagnosticPartNumber, e});
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                String content = String.format("%sVIN【%s】diagnosticPartNumber【%s】", formatMsg, vin, diagnosticPartNumber);
                client2.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
            });
            throw new CustomException(formatMsg);
        }
    }

    public List<DiagResItemGroupDto> getResItemGroupDtos(List<DiagResItemGroupDto> cloudGroups) {
        log.info("/api/v1/param/list getResItemGroupDtos开始");
        List<DiagResItemGroupDto> result = new ArrayList<>();
        for (DiagResItemGroupDto diagResItemGroupDto : cloudGroups) {
            List<DiagResItemDto> items = diagResItemGroupDto.getResponseItemDtoList();
            if (!CollectionUtils.isEmpty(items)) {
                Map<String, List<DiagResItemDto>> collect = (Map) items.stream().collect(Collectors.groupingBy(dto -> {
                    return dto.getOffset() + Constants.GROUP_SPLIT + dto.getSize() + Constants.GROUP_SPLIT + dto.getName();
                }, LinkedHashMap::new, Collectors.toList()));
                Set<String> keySet = collect.keySet();
                if (keySet.size() > 1) {
                    diagResItemGroupDto.setResponseItemDtoList(null);
                    for (String key : keySet) {
                        DiagResItemGroupDto group = new DiagResItemGroupDto();
                        BeanUtils.copyProperties(diagResItemGroupDto, group);
                        group.setResponseItemDtoList(collect.get(key));
                        result.add(group);
                    }
                } else {
                    result.add(diagResItemGroupDto);
                }
            }
        }
        log.info("/api/v1/param/list getResItemGroupDtos结束");
        return result;
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public List<DiagResItemParseResultDto> readParam(List<DiagResItemGroupDto> parmas, String vin) {
        log.info("进入paramServicec readParam");
        List<DiagResItemParseResultDto> result = new ArrayList<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        if (CollectionUtils.isEmpty(parmas)) {
            return result;
        }
        log.info("进入paramServicec 根据ECU地址、session、refs分组开始");
        Map<String, List<DiagResItemGroupDto>> group = (Map) parmas.stream().collect(Collectors.groupingBy(dto -> {
            return dto.getAddress() + Constants.GROUP_SPLIT + dto.getSessionId() + Constants.GROUP_SPLIT + dto.getSecurityAccessRefs();
        }));
        log.info("进入paramServicec 根据ECU地址、session、refs分组结束");
        for (Map.Entry<String, List<DiagResItemGroupDto>> entry : group.entrySet()) {
            String ecuAddress = "";
            String sessionId = "";
            String securityAccessRefs = "";
            List<String> didList = new ArrayList<>();
            String maxNumberOfDidsForReadDid = "";
            List<DiagResItemGroupDto> list = entry.getValue();
            list.sort(Comparator.comparing((v0) -> {
                return v0.getDataIdentifierId();
            }));
            for (DiagResItemGroupDto diagParamDto : list) {
                String dataIdentifierId = diagParamDto.getDataIdentifierId();
                maxNumberOfDidsForReadDid = diagParamDto.getMaxNumberOfDidsForReadDid();
                if (!didList.contains(dataIdentifierId)) {
                    didList.add(dataIdentifierId);
                    ecuAddress = diagParamDto.getAddress();
                    sessionId = diagParamDto.getSessionId();
                    securityAccessRefs = diagParamDto.getSecurityAccessRefs();
                }
            }
            log.info("进入paramServicec 根据maxNumberOfDidsForReadDid分组");
            try {
                if (StringUtils.isNotBlank(sessionId)) {
                    sendSession(vin, ecuAddress, sessionId);
                    log.info("进入paramServicec 发送UDS10结束");
                }
                if (StringUtils.isNotBlank(securityAccessRefs)) {
                    sendSecurityAccessRefs(vin, ecuAddress, securityAccessRefs);
                    log.info("进入paramServicec 发送UDS27结束");
                }
                sendDidByMaxReadSize(vin, result, ecuAddress, didList, maxNumberOfDidsForReadDid, list);
                log.info("进入paramServicec 发送DID组结束initFlush");
            } catch (Exception e) {
                addErrorMsg(result, list, e, false);
                String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00088);
                log.error("{}VIN【{}】DID【{}】ecuAddress【{}】securityAccessRefs【{}】", new Object[]{formatMsg, vin, didList, ecuAddress, securityAccessRefs, e});
                Optional.ofNullable(tcpClient).ifPresent(client -> {
                    client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, formatMsg.getBytes());
                });
            }
        }
        return result;
    }

    private void addErrorMsg(List<DiagResItemParseResultDto> result, List<DiagResItemGroupDto> list, Exception e, boolean hasNegativeRes) {
        for (DiagResItemGroupDto diagParamDto : list) {
            List<DiagResItemDto> responseItemDtoList = diagParamDto.getResponseItemDtoList();
            for (DiagResItemDto diagResItemDto : responseItemDtoList) {
                List<DiagResItemParseResultDto> hasThisName = (List) result.stream().filter(dto -> {
                    return dto.getName().equals(diagResItemDto.getName());
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(hasThisName) || !Boolean.TRUE.equals(hasThisName.get(0).getReadFlag())) {
                    DiagResItemParseResultDto diagParamResultDto = new DiagResItemParseResultDto();
                    diagParamResultDto.setDataIdentifierId(diagParamDto.getDataIdentifierId());
                    diagParamResultDto.setEcuName(diagParamDto.getEcuName());
                    diagParamResultDto.setName(diagResItemDto.getName());
                    diagParamResultDto.setReadFlag(false);
                    diagParamResultDto.setErrorMsg(e.getMessage());
                    diagParamResultDto.setHasNegativeRes(Boolean.valueOf(hasNegativeRes));
                    diagParamResultDto.setPosition(diagParamDto.getPosition());
                    diagParamResultDto.setOriginalResponse(e.getMessage());
                    result.add(diagParamResultDto);
                }
            }
        }
    }

    private void sendDidByMaxReadSize(String vin, List<DiagResItemParseResultDto> result, String ecuAddress, List<String> didList, String maxNumberOfDidsForReadDid, List<DiagResItemGroupDto> list) throws Exception {
        List<String> subList;
        int maxSize = didList.size();
        if (StringUtils.isNotBlank(maxNumberOfDidsForReadDid)) {
            Integer max = Integer.valueOf(maxNumberOfDidsForReadDid);
            if (max.intValue() < maxSize) {
                maxSize = max.intValue();
            }
        }
        int readGroup = (int) Math.ceil(didList.size() / maxSize);
        for (int i = 1; i <= readGroup; i++) {
            if (i == 1) {
                subList = didList.subList(0, maxSize * i);
            } else if (i == readGroup) {
                subList = didList.subList(maxSize * (i - 1), didList.size());
            } else {
                subList = didList.subList(maxSize * (i - 1), maxSize * i);
            }
            List<String> readList = new ArrayList<>(subList);
            StringBuilder didSb = new StringBuilder();
            didSb.getClass();
            readList.forEach(didSb::append);
            try {
                String response = sendDid(vin, ecuAddress, didSb.toString());
                parseResponseByDid(vin, result, list, response, readList);
            } catch (DoipNegativeException ex) {
                if (readList.size() == 1) {
                    String did = readList.get(0);
                    addErrorMsg(result, (List) list.stream().filter(obj -> {
                        return did.equals(obj.getDataIdentifierId());
                    }).collect(Collectors.toList()), ex, true);
                } else {
                    List<String> retry = new ArrayList<>();
                    for (String did2 : readList) {
                        retry.clear();
                        retry.add(did2);
                        try {
                            String res = sendDid(vin, ecuAddress, did2);
                            parseResponseByDid(vin, result, list, res, retry);
                        } catch (DoipNegativeException last) {
                            addErrorMsg(result, (List) list.stream().filter(obj2 -> {
                                return did2.equals(obj2.getDataIdentifierId());
                            }).collect(Collectors.toList()), last, true);
                        }
                    }
                }
            }
        }
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public void parseResponseByDid(String vin, List<DiagResItemParseResultDto> result, List<DiagResItemGroupDto> list, String response, List<String> readList) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        for (int i = 0; i < list.size(); i++) {
            DiagResItemGroupDto diagParamDto = list.get(i);
            String address = diagParamDto.getAddress();
            String ecuName = diagParamDto.getEcuName();
            String did = diagParamDto.getDataIdentifierId();
            if (readList.contains(did)) {
                List<DiagResItemDto> items = diagParamDto.getResponseItemDtoList();
                String dmeName = items.stream().findFirst().orElse(new DiagResItemDto()).getDmeName();
                items.sort((a, b) -> {
                    if (StringUtils.isNotBlank(a.getCompareValue())) {
                        return -1;
                    }
                    return 1;
                });
                boolean flag = false;
                String nameTemp = "";
                String originalName = "";
                String originalUnit = "";
                String outDataType = "";
                try {
                    String didSize = diagParamDto.getSize();
                    Iterator<DiagResItemDto> it = items.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            break;
                        }
                        DiagResItemDto diagParamItemDto = it.next();
                        nameTemp = diagParamItemDto.getName();
                        originalName = diagParamItemDto.getOriginalName();
                        outDataType = diagParamItemDto.getOutDataType();
                        originalUnit = diagParamItemDto.getOriginalUnit();
                        String parseParam = TesterVehicleResponseUtils.parseParam(response, did, didSize, diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
                        if (StringUtils.isNotBlank(parseParam)) {
                            DiagResItemParseResultDto diagParamResultDto = new DiagResItemParseResultDto();
                            diagParamResultDto.setDataIdentifierId(did);
                            diagParamResultDto.setDmeName(diagParamItemDto.getDmeName());
                            diagParamResultDto.setName(diagParamItemDto.getName());
                            diagParamResultDto.setOriginalName(originalName);
                            diagParamResultDto.setEcuName(ecuName);
                            diagParamResultDto.setOriginalUnit(originalUnit);
                            diagParamResultDto.setOutDataType(outDataType);
                            diagParamResultDto.setUnit(diagParamItemDto.getUnit());
                            diagParamResultDto.setValue(parseParam);
                            diagParamResultDto.setReadFlag(true);
                            diagParamResultDto.setHasCompareValue(Boolean.valueOf(StringUtils.isNotBlank(diagParamItemDto.getCompareValue())));
                            diagParamResultDto.setPosition(diagParamDto.getPosition());
                            diagParamResultDto.setOriginalResponse(response);
                            result.add(diagParamResultDto);
                            flag = true;
                            break;
                        }
                    }
                    response = subNextDidRes(list, response, i, did, didSize);
                    if (!flag) {
                        DiagResItemParseResultDto diagParamResultDto2 = new DiagResItemParseResultDto();
                        diagParamResultDto2.setDataIdentifierId(did);
                        diagParamResultDto2.setName(nameTemp);
                        diagParamResultDto2.setDmeName(dmeName);
                        diagParamResultDto2.setEcuName(ecuName);
                        diagParamResultDto2.setOriginalName(originalName);
                        diagParamResultDto2.setOriginalUnit(originalUnit);
                        diagParamResultDto2.setOutDataType(outDataType);
                        diagParamResultDto2.setReadFlag(false);
                        diagParamResultDto2.setPosition(diagParamDto.getPosition());
                        diagParamResultDto2.setErrorMsg(LanguageEnum.ERROR7.valueByLanguage());
                        diagParamResultDto2.setOriginalResponse(response);
                        result.add(diagParamResultDto2);
                    }
                } catch (Exception e) {
                    DiagResItemParseResultDto diagParamResultDto3 = new DiagResItemParseResultDto();
                    diagParamResultDto3.setDataIdentifierId(did);
                    diagParamResultDto3.setName(nameTemp);
                    diagParamResultDto3.setDmeName(dmeName);
                    diagParamResultDto3.setEcuName(ecuName);
                    diagParamResultDto3.setOriginalName(originalName);
                    diagParamResultDto3.setOriginalUnit(originalUnit);
                    diagParamResultDto3.setOutDataType(outDataType);
                    diagParamResultDto3.setReadFlag(false);
                    diagParamResultDto3.setErrorMsg(e.getMessage());
                    diagParamResultDto3.setPosition(diagParamDto.getPosition());
                    diagParamResultDto3.setOriginalResponse(response);
                    result.add(diagParamResultDto3);
                    String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00089);
                    XmlSeq xmlSeq = this.manager.getReadParamXmlSeq(vin);
                    String shortDesc = formatMsg + ";  " + MessageUtils.getMessage("ECU address") + ":" + diagParamDto.getAddress() + "; " + MessageUtils.getMessage("instruct") + ":22" + did + "; DID:" + did + "; " + MessageUtils.getMessage("response value") + ":" + response + "; " + MessageUtils.getMessage("ECU Name") + diagParamDto.getEcuName() + "; " + MessageUtils.getMessage("Diagnostic part number") + ":" + diagParamDto.getDiagnosticPartNumber();
                    List<String> collect = (List) xmlSeq.getXmldids().stream().map((v0) -> {
                        return v0.getShortDesc();
                    }).collect(Collectors.toList());
                    if (!collect.contains(shortDesc)) {
                        xmlSeq.c(new XmlFault(false, TesterErrorCodeEnum.SG00088.code(), shortDesc, "", "", "", true, address, ""));
                    }
                    log.error("{}DID【{}】", new Object[]{formatMsg, diagParamDto.getDataIdentifierId(), e});
                    Optional.ofNullable(tcpClient).ifPresent(client -> {
                        client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, formatMsg.getBytes());
                    });
                }
                if (flag) {
                    Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                        String content = "ReadDIDOperation: " + ((DiagResItemParseResultDto) result.get(0)).getName() + " – " + diagParamDto.getAddress() + ", DID: " + ((DiagResItemParseResultDto) result.get(0)).getDataIdentifierId() + ",Value: " + ((DiagResItemParseResultDto) result.get(0)).getValue() + ",Unit:" + ((DiagResItemParseResultDto) result.get(0)).getUnit();
                        client2.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
                    });
                } else {
                    String finalNameTemp = nameTemp;
                    Optional.ofNullable(tcpClient).ifPresent(client3 -> {
                        String content = "ReadDIDOperation:" + finalNameTemp + " – " + diagParamDto.getAddress() + ", DID: " + diagParamDto.getDataIdentifierId() + ",Value: ,Unit:";
                        client3.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
                    });
                }
            }
        }
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public void parseResponseByDid2(String vin, List<DiagResItemParseResultDto> result, List<DiagResItemGroupDto> list, String response, List<String> readList) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        for (int i = 0; i < list.size(); i++) {
            DiagResItemGroupDto diagParamDto = list.get(i);
            String did = diagParamDto.getDataIdentifierId();
            if (readList.contains(did)) {
                List<DiagResItemDto> items = diagParamDto.getResponseItemDtoList();
                Collections.sort(items, (a, b) -> {
                    if (StringUtils.isNotBlank(a.getCompareValue())) {
                        return -1;
                    }
                    return 1;
                });
                boolean flag = false;
                String nameTemp = "";
                String originalName = "";
                String originalUnit = "";
                String outDataType = "";
                String dmeName = "";
                String subRes = "";
                try {
                    String didSize = diagParamDto.getSize();
                    Iterator<DiagResItemDto> it = items.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            break;
                        }
                        DiagResItemDto diagParamItemDto = it.next();
                        nameTemp = diagParamItemDto.getName();
                        originalName = diagParamItemDto.getOriginalName();
                        outDataType = diagParamItemDto.getOutDataType();
                        originalUnit = diagParamItemDto.getOriginalUnit();
                        dmeName = diagParamItemDto.getDmeName();
                        String parseParam = TesterVehicleResponseUtils.parseParam(response, did, didSize, diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
                        if (StringUtils.isNotBlank(parseParam)) {
                            DiagResItemParseResultDto diagParamResultDto = new DiagResItemParseResultDto();
                            diagParamResultDto.setDataIdentifierId(did);
                            diagParamResultDto.setName(diagParamItemDto.getName());
                            diagParamResultDto.setOriginalName(originalName);
                            diagParamResultDto.setOriginalUnit(originalUnit);
                            diagParamResultDto.setOutDataType(outDataType);
                            diagParamResultDto.setUnit(diagParamItemDto.getUnit());
                            diagParamResultDto.setValue(parseParam);
                            diagParamResultDto.setReadFlag(true);
                            result.add(diagParamResultDto);
                            flag = true;
                            break;
                        }
                        if (StringUtils.isBlank(subRes)) {
                            subRes = TesterVehicleResponseUtils.getSubRes(response, did, didSize, diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
                        }
                    }
                    response = subNextDidRes(list, response, i, did, didSize);
                    if (!flag) {
                        DiagResItemParseResultDto diagParamResultDto2 = new DiagResItemParseResultDto();
                        diagParamResultDto2.setDataIdentifierId(did);
                        diagParamResultDto2.setName(nameTemp);
                        diagParamResultDto2.setOriginalName(originalName);
                        diagParamResultDto2.setOriginalUnit(originalUnit);
                        diagParamResultDto2.setOutDataType(outDataType);
                        diagParamResultDto2.setReadFlag(false);
                        diagParamResultDto2.setErrorMsg(LanguageEnum.ERROR7.valueByLanguage());
                        diagParamResultDto2.setDmeName(dmeName);
                        diagParamResultDto2.setValue(subRes);
                        result.add(diagParamResultDto2);
                    }
                } catch (Exception e) {
                    DiagResItemParseResultDto diagParamResultDto3 = new DiagResItemParseResultDto();
                    diagParamResultDto3.setDataIdentifierId(did);
                    diagParamResultDto3.setName(nameTemp);
                    diagParamResultDto3.setOriginalName(originalName);
                    diagParamResultDto3.setOriginalUnit(originalUnit);
                    diagParamResultDto3.setOutDataType(outDataType);
                    diagParamResultDto3.setReadFlag(false);
                    diagParamResultDto3.setErrorMsg(e.getMessage());
                    diagParamResultDto3.setDmeName(dmeName);
                    diagParamResultDto3.setValue(subRes);
                    result.add(diagParamResultDto3);
                    String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00089);
                    log.error("{}DID【{}】", new Object[]{formatMsg, diagParamDto.getDataIdentifierId(), e});
                    Optional.ofNullable(tcpClient).ifPresent(client -> {
                        client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, formatMsg.getBytes());
                    });
                }
            }
        }
    }

    public void parseResponseByDid3(String vin, List<DiagResItemParseResultDto> result, List<DiagResItemGroupDto> list, String response, List<String> readList) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        for (int i = 0; i < list.size(); i++) {
            DiagResItemGroupDto diagParamDto = list.get(i);
            String did = diagParamDto.getDataIdentifierId();
            if (readList.contains(did)) {
                List<DiagResItemDto> items = diagParamDto.getResponseItemDtoList();
                Collections.sort(items, (a, b) -> {
                    if (StringUtils.isNotBlank(a.getCompareValue())) {
                        return -1;
                    }
                    return 1;
                });
                String subRes = "";
                try {
                    String didSize = diagParamDto.getSize();
                    Iterator<DiagResItemDto> it = items.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            break;
                        }
                        DiagResItemDto diagParamItemDto = it.next();
                        String originalName = diagParamItemDto.getOriginalName();
                        String outDataType = diagParamItemDto.getOutDataType();
                        String originalUnit = diagParamItemDto.getOriginalUnit();
                        String parseParam = TesterVehicleResponseUtils.parseParam(response, did, didSize, diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
                        if (StringUtils.isNotBlank(parseParam)) {
                            DiagResItemParseResultDto diagParamResultDto = new DiagResItemParseResultDto();
                            diagParamResultDto.setDataIdentifierId(did);
                            diagParamResultDto.setName(diagParamItemDto.getName());
                            diagParamResultDto.setOriginalName(originalName);
                            diagParamResultDto.setOriginalUnit(originalUnit);
                            diagParamResultDto.setOutDataType(outDataType);
                            diagParamResultDto.setUnit(diagParamItemDto.getUnit());
                            diagParamResultDto.setValue(parseParam);
                            String subRes2 = TesterVehicleResponseUtils.getSubRes(response, did, didSize, diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
                            diagParamResultDto.setReadFlag(true);
                            diagParamResultDto.setUnParseValue(subRes2);
                            diagParamResultDto.setSort(diagParamItemDto.getSort());
                            result.add(diagParamResultDto);
                            break;
                        }
                        if (StringUtils.isBlank(subRes)) {
                            subRes = TesterVehicleResponseUtils.getSubRes(response, did, didSize, diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
                        }
                    }
                    response = subNextDidRes(list, response, i, did, didSize);
                } catch (Exception e) {
                    log.error("DID【{}】", diagParamDto.getDataIdentifierId(), e);
                    Optional.ofNullable(tcpClient).ifPresent(client -> {
                        client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, e.getMessage().getBytes());
                    });
                }
            }
        }
    }

    private String subNextDidRes(List<DiagResItemGroupDto> list, String response, int i, String did, String didSize) throws NumberFormatException {
        if (i + 1 < list.size()) {
            String nextDid = list.get(i + 1).getDataIdentifierId();
            if (!did.equals(nextDid)) {
                int didLength = Integer.parseInt(didSize);
                int didBegin = response.indexOf(did);
                int resBegin = didBegin + did.length();
                String substring = response.substring(didBegin, resBegin + (didLength * 2));
                response = response.replaceFirst(substring, "");
            }
        }
        return response;
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public void parseResponseByResItem(List<DiagResItemParseResultDto> result, DiagResItemGroupDto groupDto, List<DiagResItemDto> list, String response) {
        Collections.sort(list, (a, b) -> {
            if (StringUtils.isNotBlank(a.getCompareValue())) {
                return -1;
            }
            return 1;
        });
        for (DiagResItemDto diagParamItemDto : list) {
            String parseParam = TesterVehicleResponseUtils.parseParam(response, groupDto.getDataIdentifierId(), groupDto.getSize(), diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
            if (StringUtils.isNotBlank(parseParam)) {
                DiagResItemParseResultDto diagParamResultDto = new DiagResItemParseResultDto();
                diagParamResultDto.setDataIdentifierId(groupDto.getDataIdentifierId());
                diagParamResultDto.setName(diagParamItemDto.getName());
                diagParamResultDto.setUnit(diagParamItemDto.getUnit());
                diagParamResultDto.setOutDataType(diagParamItemDto.getOutDataType());
                diagParamResultDto.setValue(parseParam);
                diagParamResultDto.setOriginalName(diagParamItemDto.getOriginalName());
                diagParamResultDto.setOriginalUnit(diagParamItemDto.getOriginalUnit());
                diagParamResultDto.setDmeName(diagParamItemDto.getDmeName());
                diagParamResultDto.setDiagnosticPartNumber(groupDto.getDiagnosticPartNumber());
                diagParamResultDto.setEcuName(groupDto.getEcuName());
                result.add(diagParamResultDto);
                return;
            }
        }
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public void parseRemoteResponseByResItem(List<DiagResItemParseResultDto> result, DiagResItemGroupDto groupDto, List<DiagResItemDto> list, String response) {
        String parseParam;
        Collections.sort(list, (a, b) -> {
            if (StringUtils.isNotBlank(a.getCompareValue())) {
                return -1;
            }
            return 1;
        });
        for (DiagResItemDto diagParamItemDto : list) {
            try {
                parseParam = TesterVehicleResponseUtils.parseParam(response, groupDto.getDataIdentifierId(), groupDto.getSize(), diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
            } catch (Exception e) {
                DiagResItemParseResultDto diagParamResultDto = new DiagResItemParseResultDto();
                diagParamResultDto.setDataIdentifierId(groupDto.getDataIdentifierId());
                diagParamResultDto.setName(diagParamItemDto.getName());
                diagParamResultDto.setOriginalName(diagParamItemDto.getOriginalName());
                diagParamResultDto.setOriginalUnit(diagParamItemDto.getOriginalUnit());
                diagParamResultDto.setOutDataType(diagParamItemDto.getOutDataType());
                diagParamResultDto.setReadFlag(false);
                diagParamResultDto.setErrorMsg(e.getMessage());
                result.add(diagParamResultDto);
            }
            if (StringUtils.isNotBlank(parseParam)) {
                DiagResItemParseResultDto diagParamResultDto2 = new DiagResItemParseResultDto();
                diagParamResultDto2.setDataIdentifierId(groupDto.getDataIdentifierId());
                diagParamResultDto2.setName(diagParamItemDto.getName());
                diagParamResultDto2.setUnit(diagParamItemDto.getUnit());
                diagParamResultDto2.setOutDataType(diagParamItemDto.getOutDataType());
                diagParamResultDto2.setValue(parseParam);
                diagParamResultDto2.setOriginalName(diagParamItemDto.getOriginalName());
                diagParamResultDto2.setOriginalUnit(diagParamItemDto.getOriginalUnit());
                diagParamResultDto2.setDmeName(diagParamItemDto.getDmeName());
                diagParamResultDto2.setDiagnosticPartNumber(groupDto.getDiagnosticPartNumber());
                diagParamResultDto2.setEcuName(groupDto.getEcuName());
                result.add(diagParamResultDto2);
                return;
            }
            continue;
        }
    }

    private void sendSession(String vin, String ecuAddress, String sessionId) throws Exception {
        initSend();
        String udsData = this.udsSend.udsData(ecuAddress, "10" + sessionId, vin);
        log.info("读取参数发送UDS10，VIN【{}】ecuAddress【{}】sessionId【{}】响应【{}】", new Object[]{vin, ecuAddress, sessionId, udsData});
    }

    private void sendSecurityAccessRefs(String vin, String ecuAddress, String securityAccessRefs) throws Exception {
        initSend();
        String[] split = securityAccessRefs.split("/");
        Map<String, List<String>> map = TesterVehicleResponseUtils.PIN_CODE.get(ecuAddress);
        if (map == null) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00121));
        }
        String value = split[0];
        String link = TesterVehicleResponseUtils.PIN_CODE_LINK.get(value);
        List<String> list = null;
        Iterator<Map.Entry<String, List<String>>> it = map.entrySet().iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Map.Entry<String, List<String>> entry = it.next();
            String key = entry.getKey();
            if (key.contains(link)) {
                list = entry.getValue();
                break;
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00121));
        }
        Boolean securityAccess = this.udsSend.getSecurityAccess(ecuAddress, split[0], split[1].substring(0, 2), vin, list);
        log.info("读取参数发送UDS27，VIN【{}】ecuAddress【{}】securityAccessRefs【{}】响应【{}】", new Object[]{vin, ecuAddress, securityAccessRefs, securityAccess});
        if (Boolean.FALSE.equals(securityAccess)) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00125));
        }
    }

    private String sendDid(String vin, String ecuAddress, String did) throws Exception {
        initSend();
        String udsData = this.udsSend.udsData(ecuAddress, "22" + did, vin);
        log.info("读取参数发送UDS22，VIN【{}】ecuAddress【{}】did【{}】响应【{}】", new Object[]{vin, ecuAddress, did, udsData});
        return udsData;
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public DiagDmeDidDto getDidDme(String vin, String identifier, String name, String diagnosisPartNumber, String ecuName, String type) throws Exception {
        log.info("======> 开始执行getDidDme方法 <======");
        if (StringUtils.isBlank(identifier) || StringUtils.isBlank(name)) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00126));
        }
        log.info("======> 开始调用云端接口cloud.queryDidDme <======");
        String queryDidDme = this.cloud.a(vin, identifier, name, diagnosisPartNumber, ecuName, type);
        log.info("/api/v1/param/getDidDme cloud.queryDidDme入参:{}语言:{}返回数据:{}", new Object[]{diagnosisPartNumber, HttpUtils.getLanguage(), queryDidDme});
        log.info("======> 调用云端接口cloud.queryDidDme结束 <======");
        if (StringUtils.isNotBlank(queryDidDme)) {
            List<DiagDmeDidDto> jsonStr2List = ObjectMapperUtils.jsonStr2List(queryDidDme, DiagDmeDidDto.class);
            if (!CollectionUtils.isEmpty(jsonStr2List)) {
                return jsonStr2List.get(0);
            }
        }
        log.info("======> 执行getDidDme方法结束 <======");
        return null;
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public void initPinCode(String vin, String diagnosticPartNumber, String ecuName) {
        String json;
        log.info("/api/v1/param/initPinCode 进入service");
        if (StringUtils.isNotBlank(diagnosticPartNumber)) {
            try {
                log.info("/api/v1/param/initPinCode 调用云端接口");
                json = this.cloud.n(vin, diagnosticPartNumber, ecuName);
                log.info("/api/v1/param/initPinCode cloud.getEcuByDiagPartNum入参:{}返回数据:{}", diagnosticPartNumber, json);
                log.info("/api/v1/param/initPinCode 调用云端接口结束");
            } catch (Exception e) {
                String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00090);
                log.error("{},诊断零件号【{}】", new Object[]{formatMsg, diagnosticPartNumber, e});
            }
            if (StringUtils.isBlank(json)) {
                return;
            }
            EcuSwDto ecuSwDto = (EcuSwDto) ObjectMapperUtils.jsonStr2Clazz(json, EcuSwDto.class);
            log.info("/api/v1/param/initPinCode ObjectMapperUtils.jsonStr2Clazz");
            if (ecuSwDto != null) {
                TesterVehicleResponseUtils.setPinCodeByEcu(ecuSwDto.getAddress(), ecuSwDto.getPincode());
            }
            log.info("/api/v1/param/initPinCode TesterVehicleResponseUtils.setPinCodeByEcu");
            log.info("/api/v1/param/initPinCode service结束");
        }
    }

    public List getPinCode(String vin, String diagnosticPartNumber, String pinCodeKey, String ecuName) {
        List<String> valueList = new ArrayList<>();
        if (StringUtils.isNotBlank(diagnosticPartNumber)) {
            try {
                String json = this.cloud.n(vin, diagnosticPartNumber, ecuName);
                log.info("/api/v1/param/getPinCode cloud.getEcuByDiagPartNum入参:{}返回数据:{}", diagnosticPartNumber, json);
                if (StringUtils.isBlank(json)) {
                    return valueList;
                }
                EcuSwDto ecuSwDto = (EcuSwDto) ObjectMapperUtils.jsonStr2Clazz(json, EcuSwDto.class);
                if (ecuSwDto != null) {
                    return TesterVehicleResponseUtils.getPinCodeByPinCodeKey(pinCodeKey, ecuSwDto.getPincode());
                }
            } catch (Exception e) {
                String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00090);
                log.error("{},诊断零件号【{}】", new Object[]{formatMsg, diagnosticPartNumber, e});
            }
        }
        return valueList;
    }

    /* loaded from: ParamServiceImpl$EcuSwDto.class */
    static class EcuSwDto {
        private Object pj;
        private String address;
        private String diagnosticPartNumber;

        EcuSwDto() {
        }

        public Object getPincode() {
            return this.pj;
        }

        public void setPincode(Object pincode) {
            this.pj = pincode;
        }

        public String getAddress() {
            return this.address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getDiagnosticPartNumber() {
            return this.diagnosticPartNumber;
        }

        public void setDiagnosticPartNumber(String diagnosticPartNumber) {
            this.diagnosticPartNumber = diagnosticPartNumber;
        }
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public List<DiagResItemGroupDto> list2(String vin, String diagnosticPartNumber, String ecuName, JSONArray ids) {
        List<DiagResItemGroupDto> cloudGroups = new ArrayList<>();
        try {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < ids.size(); i++) {
                list.add(ids.get(i).toString());
            }
            for (List<String> strings : StringParseUtils.splitList(list, 10)) {
                JSONArray jsonArray = new JSONArray();
                for (int i2 = 0; i2 < strings.size(); i2++) {
                    jsonArray.add(strings.get(i2));
                }
                String json = this.cloud.a(vin, diagnosticPartNumber, "22", ecuName, jsonArray);
                if (StringUtils.isBlank(json)) {
                    return new ArrayList();
                }
                cloudGroups.addAll(ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class));
            }
        } catch (Exception e) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00087);
            log.error("{}VIN【{}】diagnosticPartNumber【{}】", new Object[]{formatMsg, vin, diagnosticPartNumber, e});
        }
        List<DiagResItemGroupDto> result = getResItemGroupDtos(cloudGroups);
        return result;
    }

    @Override // com.geely.gnds.tester.service.ParamService
    public void exportReadParamResult(HttpServletResponse response) {
        List<List<TesterReadParamUtil.ParameterValue>> readValues = TesterReadParamUtil.getReadValues();
        SysUser user = this.tokenService.c(ServletUtils.getRequest()).getUser();
        int didInfo = JSON.parseObject(user.getPermission()).getIntValue("didInfo");
        List<Map<String, String>> data = CollectionUtil.isEmpty(readValues) ? Lists.newArrayList() : parseData(readValues);
        List<Map<String, String>> rowData = CollectionUtil.isEmpty(readValues) ? Lists.newArrayList() : parseRowData(readValues);
        String fileName = "read_param_result_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtils.YYYYMMDDHHMMSS)) + ConstantEnum.EXT_EXCEL;
        ExcelWriter writer = ExcelUtil.getWriter(FileUtil.file(fileName));
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            Throwable th = null;
            try {
                try {
                    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                    response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
                    writer.renameSheet("Data");
                    if (didInfo == 0 && data.size() > 1) {
                        data.get(0).replaceAll((key, value) -> {
                            return maskDid(value);
                        });
                    }
                    writer.write(data, true);
                    writer.setSheet("RawData");
                    if (didInfo == 0 && rowData.size() > 1) {
                        rowData.get(0).replaceAll((key2, value2) -> {
                            return maskDid(value2);
                        });
                    }
                    writer.write(rowData, false);
                    writer.flush(outputStream);
                    if (outputStream != null) {
                        if (0 != 0) {
                            try {
                                outputStream.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            outputStream.close();
                        }
                    }
                } finally {
                }
            } catch (Throwable th3) {
                th = th3;
                throw th3;
            }
        } catch (IOException e) {
            log.error("======> ParamServiceImpl#exportReadParamResult 导出参数列表读取结果失败 <======", e);
            GlobalException.c(e);
        }
    }

    @NotNull
    private static List<Map<String, String>> parseData(List<List<TesterReadParamUtil.ParameterValue>> readValues) {
        List<Map<String, String>> data = new ArrayList<>();
        for (int i = 0; i < readValues.size(); i++) {
            List<TesterReadParamUtil.ParameterValue> values = readValues.get(i);
            Map<String, String> row = new HashMap<>();
            if (i == 0) {
                values.forEach(parameterValue -> {
                });
                data.add(row);
            } else {
                values.forEach(parameterValue2 -> {
                    if (Objects.isNull(parameterValue2.getValue())) {
                        row.put(parameterValue2.getName(), "");
                    } else {
                        String unit = StringUtils.isBlank(parameterValue2.getUnit()) ? "" : parameterValue2.getUnit();
                        row.put(parameterValue2.getName(), parameterValue2.getValue() + ConstantEnum.EMPTY + unit);
                    }
                });
                data.add(row);
            }
        }
        return data;
    }

    @NotNull
    private static List<Map<String, String>> parseRowData(List<List<TesterReadParamUtil.ParameterValue>> readValues) {
        List<Map<String, String>> data = new ArrayList<>();
        for (int i = 0; i < readValues.size(); i++) {
            List<TesterReadParamUtil.ParameterValue> values = readValues.get(i);
            Map<String, String> row = new HashMap<>();
            if (i == 0) {
                values.forEach(parameterValue -> {
                });
                data.add(row);
            } else {
                values.forEach(parameterValue2 -> {
                });
                data.add(row);
            }
        }
        return data;
    }

    public static String maskDid(String input) {
        if (input == null || input.length() <= 2) {
            return input;
        }
        char firstChar = input.charAt(0);
        char lastChar = input.charAt(input.length() - 1);
        int maskLength = input.length() - 2;
        StringBuilder maskedString = new StringBuilder();
        maskedString.append(firstChar);
        for (int i = 0; i < maskLength; i++) {
            maskedString.append('*');
        }
        maskedString.append(lastChar);
        return maskedString.toString();
    }
}
