package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.dao.TesterSoftwareDao;
import com.geely.gnds.tester.dto.EcuDto;
import com.geely.gnds.tester.dto.SoftwareDto;
import com.geely.gnds.tester.entity.TesterSoftwareEntity;
import com.geely.gnds.tester.enums.TesterSoftwareTypeEnum;
import com.geely.gnds.tester.service.SoftwareService;
import com.geely.gnds.tester.util.VbfParseUtils;
import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: SoftwareServiceImpl.class */
public class SoftwareServiceImpl implements SoftwareService {

    @Autowired
    private TesterSoftwareDao hR;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private FileCache fileCache;

    @Autowired
    private VbfParseUtils vbfParseUtils;

    @Override // com.geely.gnds.tester.service.SoftwareService
    public void checkSoftwareRecord(List<EcuDto> ecuDtoList) {
    }

    private void a(List<EcuDto> ecuDtoList, Map<String, TesterSoftwareEntity> checkMap) {
        for (EcuDto ecuDto : ecuDtoList) {
            List<SoftwareDto> softwares = ecuDto.getSoftwares();
            if (!CollectionUtils.isEmpty(softwares)) {
                for (SoftwareDto software : softwares) {
                    String url = software.getUrl();
                    String softwareType = software.getSoftwareType();
                    File vbfFile = this.fileCache.ae(url);
                    if (vbfFile != null && !TesterSoftwareTypeEnum.SWK1.getValue().equals(softwareType) && !TesterSoftwareTypeEnum.SWK2.getValue().equals(softwareType) && !TesterSoftwareTypeEnum.SWK3.getValue().equals(softwareType)) {
                        TesterSoftwareEntity testerSoftwareEntity = new TesterSoftwareEntity();
                        testerSoftwareEntity.setName(ecuDto.getEcuName());
                        checkMap.put(vbfFile.getName(), testerSoftwareEntity);
                    }
                }
            }
        }
    }

    private void a(Map<String, TesterSoftwareEntity> checkMap, Set<String> keySet) throws NumberFormatException {
        String userName;
        LoginUser loginUser = this.tokenService.getLoginUser();
        Date now = new Date();
        if (loginUser != null) {
            userName = loginUser.getUsername();
        } else {
            userName = "admin";
        }
        for (String key : keySet) {
            TesterSoftwareEntity testerSoftwareEntity = checkMap.get(key);
            if (testerSoftwareEntity != null) {
                try {
                    Map parsePlus = this.vbfParseUtils.parsePlus(key);
                    Object header = parsePlus.get("VBF_Header");
                    if (header != null) {
                        Map<String, Object> vbfHeader = (Map) header;
                        Object value = vbfHeader.get("Data_Value");
                        if (value != null) {
                            Map<String, Object> dataValue = (Map) value;
                            String softwareVersion = String.valueOf(dataValue.get("sw_version"));
                            String address = String.valueOf(dataValue.get("ecu_address"));
                            String softwareFormat = String.valueOf(dataValue.get("data_format_identifier"));
                            String softwareNumber = String.valueOf(dataValue.get("sw_part_number"));
                            String softwareType = String.valueOf(dataValue.get("sw_part_type"));
                            Long vbfSize = null;
                            Object object = dataValue.get("sw_size");
                            if (object != null && StringUtils.isNoneBlank(new CharSequence[]{object.toString()})) {
                                vbfSize = Long.valueOf(object.toString());
                            }
                            testerSoftwareEntity.setAddress(address);
                            testerSoftwareEntity.setFileName(key);
                            testerSoftwareEntity.setSoftwareFormat(softwareFormat);
                            testerSoftwareEntity.setSoftwareNumber(softwareNumber);
                            testerSoftwareEntity.setSoftwareType(softwareType);
                            testerSoftwareEntity.setSoftwareVersion(softwareVersion);
                            testerSoftwareEntity.setVbfSize(vbfSize);
                            testerSoftwareEntity.setUpdateBy(userName);
                            testerSoftwareEntity.setUpdateTime(now);
                            if (testerSoftwareEntity.getId() == null) {
                                testerSoftwareEntity.setCreateBy(userName);
                                testerSoftwareEntity.setCreateTime(now);
                                this.hR.create(testerSoftwareEntity);
                            } else {
                                this.hR.update(testerSoftwareEntity);
                            }
                        }
                    }
                } catch (Exception e) {
                    return;
                }
            }
        }
    }

    @Override // com.geely.gnds.tester.service.SoftwareService
    public List<TesterSoftwareEntity> query() {
        return this.hR.queryList(new HashMap(2));
    }
}
