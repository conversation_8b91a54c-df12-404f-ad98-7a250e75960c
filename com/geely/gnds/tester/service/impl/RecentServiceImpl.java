package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.dao.RecentDao;
import com.geely.gnds.tester.entity.TesterRecentEntity;
import com.geely.gnds.tester.service.RecentService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: RecentServiceImpl.class */
public class RecentServiceImpl implements RecentService {

    @Autowired
    private RecentDao oW;

    @Autowired
    private TokenService tokenService;

    @Override // com.geely.gnds.tester.service.RecentService
    public List<TesterRecentEntity> query() {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String username = loginUser.getUsername();
        List<TesterRecentEntity> recentVehicleDtos = this.oW.getVehicles(username);
        return recentVehicleDtos;
    }
}
