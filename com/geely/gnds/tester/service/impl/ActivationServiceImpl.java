package com.geely.gnds.tester.service.impl;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.xml.XmlFault;
import com.geely.gnds.doip.client.xml.XmlSeq;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.seq.UdsSend;
import com.geely.gnds.tester.service.ActivationService;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterVehicleResponseUtils;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: ActivationServiceImpl.class */
public class ActivationServiceImpl implements ActivationService {

    @Autowired
    private Cloud cloud;
    private static final Logger log = LoggerFactory.getLogger(ActivationServiceImpl.class);
    private static final Pattern oV = Pattern.compile("[=]((0[xX][0-9a-fA-F]+)|-?[0-9]+)");
    private static final Pattern ADN_PATTERN = Pattern.compile("^(X&|x&)[\\w]*[/][\\w]+");
    private SingletonManager manager = SingletonManager.getInstance();
    private UdsSend udsSend = null;

    private synchronized void initSend() {
        if (this.udsSend == null) {
            this.udsSend = new UdsSend();
        }
    }

    @Override // com.geely.gnds.tester.service.ActivationService
    public List<DiagResItemGroupDto> list(String vin, String diagnosticPartNumber, String ecuName) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            String json = this.cloud.c(vin, diagnosticPartNumber, "2F", ecuName, HttpUtils.getLanguage());
            if (StringUtils.isBlank(json)) {
                return new ArrayList();
            }
            log.info("/api/v1/activation/list cloud.getDidByDiaPartNumber入参:{}语言:{}返回数据:{}", new Object[]{diagnosticPartNumber, HttpUtils.getLanguage(), json});
            List<DiagResItemGroupDto> cloudGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
            List<DiagResItemGroupDto> result = new ArrayList<>();
            for (DiagResItemGroupDto diagResItemGroupDto : cloudGroups) {
                List<DiagResItemDto> items = diagResItemGroupDto.getResponseItemDtoList();
                if (!CollectionUtils.isEmpty(items)) {
                    Map<String, List<DiagResItemDto>> collect = (Map) items.stream().collect(Collectors.groupingBy(dto -> {
                        return dto.getOffset() + Constants.GROUP_SPLIT + dto.getSize() + Constants.GROUP_SPLIT + dto.getName();
                    }));
                    Set<String> keySet = collect.keySet();
                    if (keySet.size() > 1) {
                        diagResItemGroupDto.setResponseItemDtoList(null);
                        for (String key : keySet) {
                            DiagResItemGroupDto group = new DiagResItemGroupDto();
                            BeanUtils.copyProperties(diagResItemGroupDto, group);
                            group.setResponseItemDtoList((List) collect.get(key).stream().distinct().collect(Collectors.toList()));
                            a(group, result);
                        }
                    } else {
                        diagResItemGroupDto.setResponseItemDtoList((List) diagResItemGroupDto.getResponseItemDtoList().stream().distinct().collect(Collectors.toList()));
                        a(diagResItemGroupDto, result);
                    }
                }
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00081);
            log.error("{}VIN【{}】diagnosticPartNumber【{}】", new Object[]{formatMsg, vin, diagnosticPartNumber, e});
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, formatMsg.getBytes());
            });
            throw new CustomException(formatMsg);
        }
    }

    private void a(DiagResItemGroupDto group, List<DiagResItemGroupDto> result) {
        group.setElType("select");
        List<DiagResItemDto> responseItemDtoList = group.getResponseItemDtoList();
        if (!CollectionUtils.isEmpty(responseItemDtoList)) {
            Iterator<DiagResItemDto> iterator = responseItemDtoList.iterator();
            while (iterator.hasNext()) {
                DiagResItemDto diagResItemDto = iterator.next();
                if (StringUtils.isBlank(diagResItemDto.getUnit()) || StringUtils.isBlank(diagResItemDto.getCompareValue()) || StringUtils.isBlank(diagResItemDto.getFormula())) {
                    iterator.remove();
                } else {
                    String compareValue = diagResItemDto.getCompareValue().trim();
                    int eqIndex = compareValue.indexOf("=");
                    if (eqIndex != -1) {
                        int lastStrLength = compareValue.length() - eqIndex;
                        Matcher m = oV.matcher(compareValue);
                        if (m.find()) {
                            String matchStr = m.group(0);
                            if (lastStrLength == matchStr.length()) {
                            }
                        }
                    }
                    iterator.remove();
                }
            }
            if (!CollectionUtils.isEmpty(responseItemDtoList)) {
                result.add(group);
            }
        }
    }

    private static String n(String compareValue, String formula, String offset, String size, String maxSize) {
        BigInteger compareValueInt;
        String compareHexStr;
        log.info("激活组装DID指令compareValue【{}】formula【{}】offset【{}】size【{}】maxSize【{}】", new Object[]{compareValue, formula, offset, size, maxSize});
        Integer offsetValue = Integer.valueOf(Integer.valueOf(offset, 16).intValue() * 2);
        Integer sizeValue = Integer.valueOf(Integer.valueOf(size, 16).intValue() * 2);
        Integer maxSizeValue = Integer.valueOf(Integer.valueOf(maxSize).intValue() * 2);
        int eqIndex = compareValue.indexOf("=");
        if (eqIndex != -1) {
            String valueStr = compareValue.substring(eqIndex + 1, compareValue.length());
            if (valueStr.contains("0X") || valueStr.contains("0x")) {
                compareValueInt = new BigInteger(valueStr.replace("0X", "").replace("0x", ""), 16);
            } else {
                compareValueInt = new BigInteger(valueStr);
            }
            if (formula.contains("&")) {
                Matcher m = ADN_PATTERN.matcher(formula);
                if (m.find()) {
                    String andStr = m.group(0);
                    String[] split = andStr.split("/");
                    String beforeExpr = "X*" + split[1];
                    Object obj = TesterVehicleResponseUtils.executeExpr(beforeExpr, compareValueInt);
                    Object sendValue = TesterVehicleResponseUtils.executeExpr(split[0], obj);
                    compareHexStr = a(sizeValue, sendValue);
                } else {
                    compareHexStr = a(sizeValue, compareValueInt);
                }
            } else {
                compareHexStr = a(sizeValue, compareValueInt);
            }
            StringBuilder resSb = new StringBuilder();
            for (int i = 0; i < maxSizeValue.intValue(); i++) {
                resSb.append(0);
            }
            return resSb.replace(offsetValue.intValue(), offsetValue.intValue() + sizeValue.intValue(), compareHexStr).toString();
        }
        log.error("激活组装发送指令--compareValue表达式有误【{}】", compareValue);
        throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00120));
    }

    private static String a(Integer sizeValue, Object sendValue) {
        String sendHex = Long.toHexString(Long.valueOf(sendValue.toString()).longValue());
        if (sendHex.length() == sizeValue.intValue()) {
            return sendHex;
        }
        if (sendHex.length() < sizeValue.intValue()) {
            StringBuilder resSb = new StringBuilder();
            for (int i = 0; i < sizeValue.intValue() - sendHex.length(); i++) {
                resSb.append(0);
            }
            return resSb.append(sendHex).toString();
        }
        return sendHex.substring(sendHex.length() - sizeValue.intValue());
    }

    @Override // com.geely.gnds.tester.service.ActivationService
    public void send(DiagResItemGroupDto activation, String vin) {
        log.info("======> 开始进入send方法 <======");
        String dataIdentifierId = activation.getDataIdentifierId();
        String ecuAddress = activation.getAddress();
        String sessionId = activation.getSessionId();
        String securityAccessRefs = activation.getSecurityAccessRefs();
        Integer responseItemNum = activation.getResponseItemNum();
        List<DiagResItemDto> items = activation.getResponseItemDtoList();
        String maxSize = activation.getSize();
        String ecuName = activation.getEcuName();
        String diagnosticPartNumber = activation.getDiagnosticPartNumber();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        XmlSeq xmlSeq = this.manager.getActiveXmlSeqMap(vin);
        if (CollectionUtils.isEmpty(items) || responseItemNum == null || responseItemNum.intValue() < 1) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DiD", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00120).getBytes());
            });
            log.info("======> send方法中出现异常:{}<======", TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00120));
            String shortDesc = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00120) + ";  " + MessageUtils.getMessage("ECU address") + ":" + ecuAddress + "; " + MessageUtils.getMessage("instruct") + ":; DID:" + dataIdentifierId + "; " + MessageUtils.getMessage("response value") + ":; " + MessageUtils.getMessage("ECU Name") + ecuName + "; " + MessageUtils.getMessage("Diagnostic part number") + ":" + diagnosticPartNumber;
            List<String> collect = (List) xmlSeq.getXmldids().stream().map(x -> {
                return x.getShortDesc();
            }).collect(Collectors.toList());
            if (!collect.contains(shortDesc)) {
                xmlSeq.c(new XmlFault(false, TesterErrorCodeEnum.SG00082.code(), shortDesc, "", "", "", true, ecuAddress, ""));
            }
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00120));
        }
        try {
            if (StringUtils.isNotBlank(sessionId)) {
                sendSession(vin, ecuAddress, sessionId);
                log.info("======> 判断session，发送UDS10 <======");
            }
            if (StringUtils.isNotBlank(securityAccessRefs)) {
                sendSecurityAccessRefs(vin, ecuAddress, securityAccessRefs);
                log.info("======> 判断securityAccessRefs，发送UDS27 <======");
            }
            String compareValue = "";
            String offset = "";
            String formula = "";
            String size = "";
            int sort = 0;
            Iterator<DiagResItemDto> it = items.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                DiagResItemDto diagParamItemDto = it.next();
                if (Boolean.TRUE.equals(diagParamItemDto.getSelected()) && StringUtils.isNotBlank(diagParamItemDto.getCompareValue())) {
                    compareValue = diagParamItemDto.getCompareValue();
                    sort = diagParamItemDto.getSort().intValue();
                    offset = diagParamItemDto.getOffset();
                    formula = diagParamItemDto.getFormula();
                    size = diagParamItemDto.getSize();
                    break;
                }
            }
            log.info("======> 发送DID <======");
            String sendValue = n(compareValue, formula, offset, size, maxSize);
            if (StringUtils.isBlank(sendValue)) {
                Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                    client2.geTxtLogger().write(new Date(), "DiD", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00120).getBytes());
                });
                log.info("======> send方法中出现异常:{}<======", TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00120));
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00120));
            }
            StringBuilder maskStr = new StringBuilder();
            if (responseItemNum.intValue() > 1 && sort > 0) {
                double maskSize = Math.ceil(responseItemNum.intValue() / 8.0f);
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < maskSize; i++) {
                    sb.append("00000000");
                }
                sb.replace(sort - 1, sort, "1");
                for (int i2 = 0; i2 < sb.length(); i2 += 4) {
                    String s = Integer.toHexString(Integer.valueOf(sb.substring(i2, i2 + 4), 2).intValue());
                    maskStr.append(s);
                }
                log.info("======> 补全mask <======");
            }
            f(vin, ecuAddress, dataIdentifierId, sendValue, maskStr.toString(), "");
        } catch (Exception e) {
            Optional.ofNullable(tcpClient).ifPresent(client3 -> {
                client3.geTxtLogger().write(new Date(), "DiD", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00082).getBytes());
            });
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00082);
            log.error("{}，需要发送UDS1001复位DID【{}】", new Object[]{formatMsg, dataIdentifierId, e});
            String shortDesc2 = formatMsg + ";  " + MessageUtils.getMessage("ECU address") + ":" + ecuAddress + "; " + MessageUtils.getMessage("instruct") + ":; DID:" + dataIdentifierId + "; " + MessageUtils.getMessage("response value") + ":; " + MessageUtils.getMessage("ECU Name") + ecuName + "; " + MessageUtils.getMessage("Diagnostic part number") + ":" + diagnosticPartNumber;
            List<String> collect2 = (List) xmlSeq.getXmldids().stream().map(x2 -> {
                return x2.getShortDesc();
            }).collect(Collectors.toList());
            if (!collect2.contains(shortDesc2)) {
                xmlSeq.c(new XmlFault(false, TesterErrorCodeEnum.SG00082.code(), shortDesc2, "", "", "", true, ecuAddress, ""));
            }
            try {
                sendRevert(vin, ecuAddress, dataIdentifierId);
            } catch (Exception ex) {
                Optional.ofNullable(tcpClient).ifPresent(client4 -> {
                    client4.geTxtLogger().write(new Date(), "DiD", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00013).getBytes());
                });
                log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00013), ex);
            }
            throw new CustomException(e.getMessage());
        }
    }

    private void sendSession(String vin, String ecuAddress, String sessionId) throws Exception {
        initSend();
        String udsData = this.udsSend.udsData(ecuAddress, "10" + sessionId, vin);
        log.info("激活发送UDS10，VIN【{}】ecuAddress【{}】sessionId【{}】响应【{}】", new Object[]{vin, ecuAddress, sessionId, udsData});
    }

    private void sendSecurityAccessRefs(String vin, String ecuAddress, String securityAccessRefs) throws Exception {
        initSend();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        String[] split = securityAccessRefs.split("/");
        Map<String, List<String>> map = TesterVehicleResponseUtils.PIN_CODE.get(ecuAddress);
        if (map == null) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00121).getBytes());
            });
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00121));
        }
        String value = split[0];
        String link = TesterVehicleResponseUtils.PIN_CODE_LINK.get(value);
        List<String> list = null;
        Iterator<Map.Entry<String, List<String>>> it = map.entrySet().iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Map.Entry<String, List<String>> entry = it.next();
            String key = entry.getKey();
            if (key.contains(link)) {
                list = entry.getValue();
                break;
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                client2.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00121).getBytes());
            });
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00121));
        }
        Boolean securityAccess = this.udsSend.getSecurityAccess(ecuAddress, split[0], split[1].substring(0, 2), vin, list);
        log.info("激活发送UDS27，VIN【{}】ecuAddress【{}】securityAccessRefs【{}】响应【{}】", new Object[]{vin, ecuAddress, securityAccessRefs, securityAccess});
        if (Boolean.FALSE.equals(securityAccess)) {
            Optional.ofNullable(tcpClient).ifPresent(client3 -> {
                client3.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00122).getBytes());
            });
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00122));
        }
    }

    private void f(String vin, String ecuAddress, String did, String sendValue, String mask, String result) throws Exception {
        log.info("======> 进入sendDid方法，方法参数如下：vin:{},ecuAddress:{},dataIdentifierId:{},sendValue:{},maskStr:{} <======", new Object[]{vin, ecuAddress, did, sendValue, mask});
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        log.info("======> initSend开始 <======");
        initSend();
        log.info("======> initSend结束 <======");
        StringBuilder sb = new StringBuilder();
        sb.append("2F").append(did).append("03").append(sendValue).append(mask);
        String udsData = this.udsSend.udsData(ecuAddress, sb.toString(), vin);
        log.info("激活发送UDS2F，VIN【{}】ecuAddress【{}】DID【{}】sendValue【{}】mask【{}】响应【{}】", new Object[]{vin, ecuAddress, did, sendValue, mask, udsData});
        if (StringUtils.isBlank(udsData)) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00123).getBytes());
            });
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00123));
        }
        TesterVehicleResponseUtils.handleNegativeRes(udsData, "2F");
    }

    @Override // com.geely.gnds.tester.service.ActivationService
    public void sendRevert(String vin, String ecuAddress, String dataIdentifierId) throws Exception {
        initSend();
        StringBuilder sb = new StringBuilder();
        sb.append("2F").append(dataIdentifierId).append("00");
        String revertStrRes = this.udsSend.udsData(ecuAddress, sb.toString(), vin);
        log.info("激活发送UDS1001复位前置指令，VIN【{}】ecuAddress【{}】dataIdentifierId【{}】响应【{}】", new Object[]{vin, ecuAddress, dataIdentifierId, revertStrRes});
        String udsData = this.udsSend.udsData(ecuAddress, "1001", vin);
        log.info("激活发送UDS1001复位，VIN【{}】ecuAddress【{}】响应【{}】", new Object[]{vin, ecuAddress, udsData});
    }
}
