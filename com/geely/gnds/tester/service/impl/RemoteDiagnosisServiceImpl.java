package com.geely.gnds.tester.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.bean.BeanUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.BroadcastVehicleDataDTO;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.dto.RemoteDtcDTO;
import com.geely.gnds.tester.dto.RemoteParamDTO;
import com.geely.gnds.tester.dto.RemoteReadoutParamDto;
import com.geely.gnds.tester.dto.RemoteSeqExecuteDTO;
import com.geely.gnds.tester.dto.RemoteStatusDTO;
import com.geely.gnds.tester.dto.RemoteTaskDTO;
import com.geely.gnds.tester.dto.ResponseDidDTO;
import com.geely.gnds.tester.dto.ResponseValueDTO;
import com.geely.gnds.tester.dto.dtc.DtcExtendedDataRecordDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.dto.dtc.ExtendedDataDtcIndicatorDTO;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.LightStatusEnum;
import com.geely.gnds.tester.enums.PowerModeRemoteEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.DtcDataAnalysisService;
import com.geely.gnds.tester.service.RemoteDiagnosisService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterVehicleResponseUtils;
import com.geely.gnds.tester.vo.DtcDetailedVO;
import com.geely.gnds.tester.vo.DtcExtendedDataVO;
import com.geely.gnds.tester.vo.SnapshotRecordVO;
import com.geely.gnds.tester.vo.language.DtcListVo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: RemoteDiagnosisServiceImpl.class */
public class RemoteDiagnosisServiceImpl implements RemoteDiagnosisService {
    private static final Logger log = LoggerFactory.getLogger(RemoteDiagnosisServiceImpl.class);

    @Autowired
    private Cloud cloud;

    @Autowired
    private DtcServiceImpl pn;

    @Value("#{${dtcDisDict}}")
    public Map<String, String> dtcDisDict;

    @Autowired
    private ParamServiceImpl po;

    @Autowired
    private DtcDataAnalysisService pd;

    @Autowired
    private TokenService tokenService;
    private SingletonManager manager = SingletonManager.getInstance();
    JSONObject pp = (JSONObject) JSON.parse("{\"00\":\"休眠\",\"01\":\"不工作\",\"02\":\"便利\",\"0B\":\"工作\",\"0D\":\"驾驶\"}");

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<RemoteTaskDTO> remoteTask(String vin) throws Exception {
        List<RemoteTaskDTO> list = new ArrayList<>();
        String remoteTask = this.cloud.aw(vin);
        JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(remoteTask, JsonNode.class);
        if (readValue.isArray()) {
            readValue.forEach(obj -> {
                RemoteTaskDTO remoteTaskDTO = (RemoteTaskDTO) ObjectMapperUtils.getInstance().convertValue(obj, RemoteTaskDTO.class);
                list.add(remoteTaskDTO);
            });
        }
        return list;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<DtcInfoDTO> queryDtc(String vin, String id, Integer type) throws Exception {
        List<DtcInfoDTO> list = new ArrayList();
        String dtcList = this.cloud.ax(id);
        if (StringUtils.isNotEmpty(dtcList)) {
            JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(dtcList, JsonNode.class);
            if (readValue.isArray()) {
                list = a(vin, type, readValue);
            }
        }
        return list;
    }

    private List<DtcInfoDTO> a(String vin, Integer type, JsonNode readValue) {
        List<DtcInfoDTO> list = new ArrayList<>();
        readValue.forEach(obj -> {
            RemoteDtcDTO remoteDtcDTO = (RemoteDtcDTO) ObjectMapperUtils.getInstance().convertValue(obj, RemoteDtcDTO.class);
            String ecuAddress = remoteDtcDTO.getEcuAddress();
            String dtcInfo = remoteDtcDTO.getDtcInfo();
            List<String> dtcCodes = ObjectMapperUtils.jsonStr2List(dtcInfo, String.class);
            DtcListVo vo = new DtcListVo();
            if (!CollectionUtils.isEmpty(dtcCodes)) {
                dtcCodes = (List) dtcCodes.stream().filter(d -> {
                    return !d.startsWith("EF");
                }).collect(Collectors.toList());
            }
            List<String> dtcIds = (List) dtcCodes.stream().map(d2 -> {
                return d2.substring(0, 6);
            }).collect(Collectors.toList());
            vo.setDtcIds((String[]) dtcIds.toArray(new String[0]));
            vo.setLanguage(HttpUtils.getLanguage());
            String dtcListByIds = null;
            try {
                dtcListByIds = this.cloud.a(vo);
            } catch (Exception e) {
                e.printStackTrace();
            }
            Map<String, Object> dtcMap = StringUtils.isEmpty(dtcListByIds) ? new HashMap<>() : ObjectMapperUtils.jsonStr2Map(dtcListByIds);
            Object broadcast = this.manager.getGlobal(GlobalVariableEnum.lY, vin);
            BroadcastVehicleDataDTO broadcasts = broadcast == null ? null : (BroadcastVehicleDataDTO) ObjectMapperUtils.jsonStr2Clazz((String) broadcast, BroadcastVehicleDataDTO.class);
            dtcCodes.forEach(dtcId -> {
                DtcInfoDTO dtcInfoDTO = new DtcInfoDTO();
                Optional.ofNullable(broadcasts).flatMap(broad -> {
                    return Optional.ofNullable(broad.getEcus());
                }).flatMap(ecus -> {
                    return Optional.of(ecus.stream().filter(ecuBroadcastDto -> {
                        return Objects.equals(ecuBroadcastDto.getEcuAddress(), ecuAddress);
                    }).collect(Collectors.toList()));
                }).ifPresent(filter -> {
                    if (!CollectionUtils.isEmpty(filter)) {
                        dtcInfoDTO.setDiagnosticNumber(((EcuBroadcastDto) filter.get(0)).getDiagnosticPartNumber() + ((EcuBroadcastDto) filter.get(0)).getDiagnosticPartVersion());
                        dtcInfoDTO.setEcuName(((EcuBroadcastDto) filter.get(0)).getEcuName());
                    }
                });
                dtcInfoDTO.setId(dtcId.substring(0, 6));
                dtcInfoDTO.setDtcId(dtcId.substring(0, 6));
                dtcInfoDTO.setDtcInfoId(dtcId);
                dtcInfoDTO.setEcuAddress(ecuAddress);
                String key = dtcId.substring(0, 1);
                if (this.dtcDisDict.containsKey(key)) {
                    dtcInfoDTO.setDtcValue(this.dtcDisDict.get(key) + dtcId.substring(0, 6).substring(1));
                }
                Object ob = dtcMap.get(dtcInfoDTO.getId());
                String value = ob == null ? "" : ObjectMapperUtils.obj2JsonStr(ob);
                String name = "";
                if (StringUtils.isNotBlank(value)) {
                    Map<String, Object> info = ObjectMapperUtils.jsonStr2Map(value);
                    if (ObjectUtils.isNotEmpty(info.get("name"))) {
                        name = info.get("name").toString();
                    }
                }
                dtcInfoDTO.setRawDtc(name);
                dtcInfoDTO.setDtc(dtcInfoDTO.getEcuName() + "-" + dtcInfoDTO.getDtcValue() + ConstantEnum.EMPTY + name);
                int lightStatus = this.pn.V(dtcId.substring(6), "");
                dtcInfoDTO.setStatus(Integer.valueOf(lightStatus));
                boolean unConfirmed = (lightStatus == 1 || lightStatus == 2 || lightStatus == 0) && type.intValue() == 2;
                if (unConfirmed) {
                    list.add(dtcInfoDTO);
                }
                boolean confirmed = (lightStatus == 2 || lightStatus == 1 || lightStatus == 0 || type.intValue() != 1) ? false : true;
                if (confirmed) {
                    list.add(dtcInfoDTO);
                }
            });
        });
        list.sort(Comparator.comparing((v0) -> {
            return v0.getIsBold();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).thenComparing((v0) -> {
            return v0.getStatus();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).reversed().thenComparing((v0) -> {
            return v0.getEcuName();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).thenComparing((v0) -> {
            return v0.getDtcValue();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })));
        return list;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<DiagResItemParseResultDto> queryParam(String id) throws Exception {
        List<DiagResItemParseResultDto> diagResItemParseResultDtoList = new ArrayList<>();
        String dtcList = this.cloud.ax(id);
        if (StringUtils.isNotEmpty(dtcList)) {
            RemoteParamDTO remoteParamDTO = (RemoteParamDTO) ObjectMapperUtils.jsonStr2Clazz(dtcList, RemoteParamDTO.class);
            List<DiagResItemGroupDto> diagResItemGroupDtoList = remoteParamDTO.getResItemGroupDtoList();
            List<DiagResItemGroupDto> groupList = this.po.getResItemGroupDtos(diagResItemGroupDtoList);
            String result = remoteParamDTO.getParamData();
            TesterVehicleResponseUtils.handleNegativeRes(result, "22");
            for (DiagResItemGroupDto resItemGroupDto : groupList) {
                try {
                    this.po.parseRemoteResponseByResItem(diagResItemParseResultDtoList, resItemGroupDto, resItemGroupDto.getResponseItemDtoList(), result);
                } catch (Exception e) {
                }
            }
        }
        return diagResItemParseResultDtoList;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public DtcDetailedVO queryDtcDetail(String title, String taskId, String diagnosticNumber, String dtcId, String vin, String ecuAddress, String ecuName) throws Exception {
        String detail = this.cloud.r(taskId, vin, ecuAddress);
        if (StringUtils.isNotEmpty(detail)) {
            JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(detail, JsonNode.class);
            RemoteDtcDTO remoteDtcDTO = (RemoteDtcDTO) ObjectMapperUtils.getInstance().convertValue(readValue, RemoteDtcDTO.class);
            List<String> dtcList = ObjectMapperUtils.jsonStr2List(remoteDtcDTO.getDtcInfo(), String.class);
            Map<String, Object> map1906Data = StringUtils.isEmpty(remoteDtcDTO.getDtcInfo6()) ? new HashMap<>() : ObjectMapperUtils.jsonStr2Map(remoteDtcDTO.getDtcInfo6());
            Map<String, Object> dtcInfo = new HashMap<>();
            dtcInfo.put(ConstantEnum.INFO_1904, StringUtils.isEmpty(remoteDtcDTO.getDtcInfo4()) ? "" : ObjectMapperUtils.jsonStr2Map(remoteDtcDTO.getDtcInfo4()));
            String data1906 = null;
            StringBuffer dtcStatus = new StringBuffer();
            FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
            DtcDetailedVO vo = new DtcDetailedVO();
            if (!ObjectUtils.isEmpty(dtcList)) {
                dtcList.stream().filter(d -> {
                    return d.startsWith(dtcId);
                }).findFirst().ifPresent(x -> {
                    dtcStatus.append(x.substring(6, 8));
                });
            }
            if (StringUtils.isEmpty(dtcStatus.toString())) {
                return vo;
            }
            String dtcInfoId = dtcId + dtcStatus.toString();
            DtcExtendedDataRecordDTO dataRecordDTO = null;
            if (!ObjectUtils.isEmpty(map1906Data)) {
                Object dtc1906 = map1906Data.get(dtcInfoId);
                if (!ObjectUtils.isEmpty(dtc1906)) {
                    data1906 = dtc1906.toString();
                    dataRecordDTO = this.pn.getDtcExtendedDetailInfo(diagnosticNumber, dtcId, vin, data1906, ecuAddress);
                    vo.setExtendedData(this.pn.covertExtendedDataVO(dataRecordDTO, dtcStatus.toString(), new DtcExtendedDataVO()));
                }
            }
            String globalTimeObj = remoteDtcDTO.getDd00();
            Long globalTime = 0L;
            try {
                if (!org.springframework.util.ObjectUtils.isEmpty(globalTimeObj)) {
                    globalTime = Long.valueOf(Long.parseLong(globalTimeObj, 16) * 100);
                    log.info("{}转换globalTime成功:{}", globalTimeObj, globalTime);
                }
            } catch (Exception e) {
                Optional.ofNullable(tcpClient).ifPresent(client -> {
                    String content = "转换globalTime异常，globalTime=" + globalTimeObj + "，e=" + e;
                    client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
                });
                log.error("转换globalTime异常，globalTime={}，e={}", globalTimeObj, e);
            }
            Object platform = this.manager.getGlobal("platformCode", vin);
            int platformCode = 2;
            if (platform != null) {
                platformCode = ((Integer) platform).intValue();
            }
            SnapshotRecordVO snapshotRecordVO = this.pd.fillTimeAxis(dataRecordDTO, new SnapshotRecordVO(), globalTime, DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, remoteDtcDTO.getUpdateDate()), platformCode);
            if (!ObjectUtils.isEmpty(dtcStatus) && !ObjectUtils.isEmpty(dataRecordDTO)) {
                ExtendedDataDtcIndicatorDTO indicatorDTO = dataRecordDTO.getIndicatorDTO();
                if (!ObjectUtils.isEmpty(indicatorDTO)) {
                    snapshotRecordVO.setLightStatus(Integer.valueOf(this.pn.V(dtcStatus.toString(), indicatorDTO.getValue().toString())));
                }
            }
            vo.setSnapshotRecord(snapshotRecordVO);
            String finalData190 = data1906;
            Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                SnapshotRecordVO snapshotRecord = vo.getSnapshotRecord();
                String defaultColor = "";
                if (!Objects.isNull(snapshotRecord) && !Objects.isNull(snapshotRecord.getLightStatus())) {
                    defaultColor = LightStatusEnum.getValue(snapshotRecord.getLightStatus().intValue());
                }
                String content = "GetDTCDetailOperation: " + title + ",Color: " + defaultColor;
                client2.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
                client2.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", ("1906: " + finalData190).getBytes());
            });
            if (this.pn.a(diagnosticNumber, dtcId, vin, dtcStatus, vo, dtcInfo, dtcInfoId, dataRecordDTO, globalTime, ecuName, platformCode)) {
                return vo;
            }
            return vo;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<RemoteTaskDTO> remoteTask3(String vin) throws Exception {
        List<RemoteTaskDTO> list = new ArrayList<>();
        String remoteTask = this.cloud.az(vin);
        JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(remoteTask, JsonNode.class);
        if (readValue.isArray()) {
            readValue.forEach(obj -> {
                RemoteTaskDTO remoteTaskDTO = (RemoteTaskDTO) ObjectMapperUtils.getInstance().convertValue(obj, RemoteTaskDTO.class);
                list.add(remoteTaskDTO);
            });
        }
        return list;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<DtcInfoDTO> queryDtc3(String vin, String id, Integer type) throws Exception {
        List<DtcInfoDTO> list = new ArrayList();
        String dtcList = this.cloud.aA(id);
        if (StringUtils.isNotEmpty(dtcList)) {
            JSONArray readValue = JSONObject.parseArray(dtcList);
            if (!readValue.isEmpty()) {
                List<RemoteDtcDTO> dList = new ArrayList<>();
                List<Object> js = (List) readValue.stream().filter(o -> {
                    return "19 02".equals(JSON.parseObject(o.toString()).getString("service"));
                }).collect(Collectors.toList());
                js.forEach(j -> {
                    JSONObject item = (JSONObject) j;
                    String ecuAddress = item.getString("ecuAddress");
                    List<String> dtc = (List) item.getJSONArray("valueList").stream().map(o2 -> {
                        return JSON.parseObject(o2.toString()).getString(AjaxResult.gA) + JSON.parseObject(o2.toString()).getString("value");
                    }).collect(Collectors.toList());
                    RemoteDtcDTO dto = new RemoteDtcDTO();
                    dto.setEcuAddress(ecuAddress);
                    dto.setDtcInfo(ObjectMapperUtils.obj2JsonStr(dtc));
                    dList.add(dto);
                });
                if (StringUtils.isNotEmpty(dList)) {
                    JsonNode jn = new ObjectMapper().valueToTree(dList);
                    list = a(vin, type, jn);
                }
            }
        }
        return list;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<DiagResItemParseResultDto> queryParam3(String id) throws Exception {
        String dtcList = this.cloud.aA(id);
        return cu(dtcList);
    }

    private List<DiagResItemParseResultDto> cu(String str) {
        List<DiagResItemParseResultDto> diagResItemParseResultDtoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(str)) {
            List<ResponseDidDTO> responseDids = ObjectMapperUtils.jsonStr2List(str, ResponseDidDTO.class);
            if (!CollectionUtils.isEmpty(responseDids)) {
                for (ResponseDidDTO responseDidDTO : responseDids) {
                    if ("22".equals(responseDidDTO.getService())) {
                        List<ResponseValueDTO> valueList = responseDidDTO.getValueList();
                        if (!CollectionUtils.isEmpty(valueList)) {
                            for (ResponseValueDTO responseValueDTO : valueList) {
                                try {
                                    DiagResItemParseResultDto dto = new DiagResItemParseResultDto();
                                    dto.setName(responseValueDTO.getValueDescription());
                                    dto.setDiagnosticPartNumber(responseDidDTO.getDiagnosticPartNum());
                                    dto.setValue(responseValueDTO.getValueDisplay());
                                    dto.setUnit(responseValueDTO.getValueDisplay());
                                    dto.setDataIdentifierId(responseValueDTO.getCode());
                                    diagResItemParseResultDtoList.add(dto);
                                } catch (Exception e) {
                                }
                            }
                        }
                    }
                }
            }
        }
        return diagResItemParseResultDtoList;
    }

    public DtcDetailedVO f(String title, String taskId, String diagnosticNumber, String dtcId, String vin, String ecuAddress, String ecuName) throws Exception {
        String detail = this.cloud.s(taskId, vin, ecuAddress);
        if (StringUtils.isNotEmpty(detail)) {
            List<Object> js4 = new JSONArray<>();
            JSONObject js6 = new JSONObject();
            List<String> dtcList = new ArrayList<>();
            JSONArray readValue = JSONObject.parseArray(detail);
            if (!readValue.isEmpty()) {
                JSONObject js2 = (JSONObject) ((List) readValue.stream().filter(o -> {
                    return "19 02".equals(JSON.parseObject(o.toString()).getString("service"));
                }).collect(Collectors.toList())).get(0);
                js4 = (List) readValue.stream().filter(o2 -> {
                    return "19 04".equals(JSON.parseObject(o2.toString()).getString("service"));
                }).collect(Collectors.toList());
                js6 = (JSONObject) ((List) readValue.stream().filter(o3 -> {
                    return "19 06".equals(JSON.parseObject(o3.toString()).getString("service"));
                }).collect(Collectors.toList())).get(0);
                dtcList = (List) js2.getJSONArray("valueList").stream().map(o4 -> {
                    return JSON.parseObject(o4.toString()).getString(AjaxResult.gA) + JSON.parseObject(o4.toString()).getString("value");
                }).collect(Collectors.toList());
            }
            Map<String, Object> map1906Data = new HashMap<>();
            if (!js6.isEmpty() && !StringUtils.isEmpty(js6.getString("rawValue"))) {
                map1906Data.put(js6.getString("input"), js6.getString("rawValue").replaceAll(ConstantEnum.EMPTY, ""));
            }
            Map<String, Object> dtcInfo = new HashMap<>();
            Map<String, String> map1904 = new HashMap<>();
            StringBuffer sb = new StringBuffer();
            js4.forEach(i -> {
                JSONObject js = (JSONObject) i;
                sb.append(js.getString("type1904")).append(js.getString("rawValue").replaceAll(ConstantEnum.EMPTY, ""));
            });
            map1904.put(js6.getString("input"), sb.toString());
            dtcInfo.put(ConstantEnum.INFO_1904, map1904);
            String data1906 = null;
            StringBuffer dtcStatus = new StringBuffer();
            FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
            DtcDetailedVO vo = new DtcDetailedVO();
            if (!ObjectUtils.isEmpty(dtcList)) {
                dtcList.stream().filter(d -> {
                    return d.startsWith(dtcId);
                }).findFirst().ifPresent(x -> {
                    dtcStatus.append(x.substring(6, 8));
                });
            }
            if (StringUtils.isEmpty(dtcStatus.toString())) {
                return vo;
            }
            String dtcInfoId = dtcId + dtcStatus.toString();
            DtcExtendedDataRecordDTO dataRecordDTO = null;
            if (!ObjectUtils.isEmpty(map1906Data)) {
                Object dtc1906 = map1906Data.get(dtcInfoId);
                if (!ObjectUtils.isEmpty(dtc1906)) {
                    data1906 = dtc1906.toString();
                    dataRecordDTO = this.pn.getDtcExtendedDetailInfo(diagnosticNumber, dtcId, vin, data1906, ecuAddress);
                    vo.setExtendedData(this.pn.covertExtendedDataVO(dataRecordDTO, dtcStatus.toString(), new DtcExtendedDataVO()));
                }
            }
            JSONObject dtc1904 = (JSONObject) ((List) js4.stream().filter(o5 -> {
                return "21".equals(JSON.parseObject(o5.toString()).getString("type1904"));
            }).collect(Collectors.toList())).get(0);
            Long globalTime = 0L;
            if (!dtc1904.isEmpty()) {
                JSONObject time = (JSONObject) ((List) dtc1904.getJSONArray("valueList").stream().filter(o6 -> {
                    return "DD00".equals(JSON.parseObject(o6.toString()).getString(AjaxResult.gA));
                }).collect(Collectors.toList())).get(0);
                globalTime = Long.valueOf(time.isEmpty() ? 0L : ((long) Double.parseDouble(time.getString("valueDisplay").replace("min\n", "").trim())) * 60000);
                log.info("{}转换globalTime成功:{}", time, globalTime);
            }
            Object platform = this.manager.getGlobal("platformCode", vin);
            int platformCode = 2;
            if (platform != null) {
                platformCode = ((Integer) platform).intValue();
            }
            SnapshotRecordVO snapshotRecordVO = this.pd.fillTimeAxis(dataRecordDTO, new SnapshotRecordVO(), globalTime, new Date(), platformCode);
            if (!ObjectUtils.isEmpty(dtcStatus) && !ObjectUtils.isEmpty(dataRecordDTO)) {
                ExtendedDataDtcIndicatorDTO indicatorDTO = dataRecordDTO.getIndicatorDTO();
                if (!ObjectUtils.isEmpty(indicatorDTO)) {
                    snapshotRecordVO.setLightStatus(Integer.valueOf(this.pn.V(dtcStatus.toString(), indicatorDTO.getValue().toString())));
                }
            }
            vo.setSnapshotRecord(snapshotRecordVO);
            String finalData190 = data1906;
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                SnapshotRecordVO snapshotRecord = vo.getSnapshotRecord();
                String defaultColor = "";
                if (!Objects.isNull(snapshotRecord) && !Objects.isNull(snapshotRecord.getLightStatus())) {
                    defaultColor = LightStatusEnum.getValue(snapshotRecord.getLightStatus().intValue());
                }
                String content = "GetDTCDetailOperation: " + title + ",Color: " + defaultColor;
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", ("1906: " + finalData190).getBytes());
            });
            if (this.pn.a(diagnosticNumber, dtcId, vin, dtcStatus, vo, dtcInfo, dtcInfoId, dataRecordDTO, globalTime, ecuName, platformCode)) {
                return vo;
            }
            return vo;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public DtcDetailedVO queryDtcDetail3(String title, String taskId, String diagnosticNumber, String dtcId, String vin, String ecuAddress, String ecuName) throws Exception {
        String detail = this.cloud.s(taskId, vin, ecuAddress);
        if (StringUtils.isNotEmpty(detail)) {
            List<ResponseDidDTO> js6 = new ArrayList<>();
            List<String> dtcList = new ArrayList<>();
            List<ResponseDidDTO> js4 = new ArrayList<>();
            List<ResponseDidDTO> responseDids = ObjectMapperUtils.jsonStr2List(detail, ResponseDidDTO.class);
            if (!CollectionUtils.isEmpty(responseDids)) {
                boolean flag1902 = true;
                for (ResponseDidDTO responseDidDTO : responseDids) {
                    String address = responseDidDTO.getEcuAddress();
                    if (StringUtils.isNotBlank(ecuAddress) && ecuAddress.equals(address)) {
                        String service = responseDidDTO.getService();
                        if (flag1902 && "19 02".equals(service)) {
                            List<ResponseValueDTO> valueList = responseDidDTO.getValueList();
                            if (!CollectionUtils.isEmpty(valueList)) {
                                for (ResponseValueDTO responseValueDTO : valueList) {
                                    dtcList.add(responseValueDTO.getCode() + responseValueDTO.getValue());
                                }
                            }
                            flag1902 = false;
                        }
                        if ("19 06".equals(service)) {
                            js6.add(responseDidDTO);
                        }
                        if ("19 04".equals(service)) {
                            js4.add(responseDidDTO);
                        }
                    }
                }
            }
            Map<String, Object> map1906Data = new HashMap<>();
            if (!CollectionUtils.isEmpty(js6)) {
                js6.forEach(i -> {
                    if (StringUtils.isNotBlank(i.getInput())) {
                        map1906Data.put(i.getInput(), i.getRawValue().replaceAll(ConstantEnum.EMPTY, ""));
                    }
                });
            }
            Map<String, Object> dtcInfo = new HashMap<>();
            Map<String, String> map1904 = new HashMap<>();
            if (!CollectionUtils.isEmpty(js4)) {
                js4.forEach(i2 -> {
                    if (StringUtils.isNotBlank(i2.getInput())) {
                        map1904.put(i2.getInput(), i2.getRawValue().replaceAll(ConstantEnum.EMPTY, ""));
                    }
                });
            }
            dtcInfo.put(ConstantEnum.INFO_1904, map1904);
            String data1906 = null;
            StringBuffer dtcStatus = new StringBuffer();
            FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
            DtcDetailedVO vo = new DtcDetailedVO();
            if (!ObjectUtils.isEmpty(dtcList)) {
                dtcList.stream().filter(d -> {
                    return d.startsWith(dtcId);
                }).findFirst().ifPresent(x -> {
                    dtcStatus.append(x.substring(6, 8));
                });
            }
            if (StringUtils.isEmpty(dtcStatus.toString())) {
                return vo;
            }
            String dtcInfoId = dtcId + dtcStatus.toString();
            DtcExtendedDataRecordDTO dataRecordDTO = null;
            if (!ObjectUtils.isEmpty(map1906Data)) {
                Object dtc1906 = map1906Data.get(dtcInfoId);
                if (!ObjectUtils.isEmpty(dtc1906)) {
                    data1906 = dtc1906.toString();
                    dataRecordDTO = this.pn.getDtcExtendedDetailInfo(diagnosticNumber, dtcId, vin, data1906, ecuAddress);
                    vo.setExtendedData(this.pn.covertExtendedDataVO(dataRecordDTO, dtcStatus.toString(), new DtcExtendedDataVO()));
                }
            }
            Long globalTime = 0L;
            Iterator<ResponseDidDTO> it = js4.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                ResponseDidDTO responseDidDTO2 = it.next();
                if ("21".equals(responseDidDTO2.getType1904())) {
                    if (!ObjectUtils.isEmpty(responseDidDTO2)) {
                        Iterator<ResponseValueDTO> it2 = responseDidDTO2.getValueList().iterator();
                        while (true) {
                            if (!it2.hasNext()) {
                                break;
                            }
                            ResponseValueDTO responseValueDTO2 = it2.next();
                            if ("DD00".equals(responseValueDTO2.getCode())) {
                                globalTime = Long.valueOf(ObjectUtils.isEmpty(responseValueDTO2) ? 0L : ((long) Double.parseDouble(responseValueDTO2.getValueDisplay().replace("min\n", "").trim())) * 60000);
                                log.info("{}转换globalTime成功:{}", responseValueDTO2, globalTime);
                            }
                        }
                    }
                }
            }
            Object platform = this.manager.getGlobal("platformCode", vin);
            int platformCode = 2;
            if (platform != null) {
                platformCode = ((Integer) platform).intValue();
            }
            SnapshotRecordVO snapshotRecordVO = this.pd.fillTimeAxis(dataRecordDTO, new SnapshotRecordVO(), globalTime, new Date(), platformCode);
            if (!ObjectUtils.isEmpty(dtcStatus) && !ObjectUtils.isEmpty(dataRecordDTO)) {
                ExtendedDataDtcIndicatorDTO indicatorDTO = dataRecordDTO.getIndicatorDTO();
                if (!ObjectUtils.isEmpty(indicatorDTO)) {
                    snapshotRecordVO.setLightStatus(Integer.valueOf(this.pn.V(dtcStatus.toString(), indicatorDTO.getValue().toString())));
                }
            }
            vo.setSnapshotRecord(snapshotRecordVO);
            String finalData190 = data1906;
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                SnapshotRecordVO snapshotRecord = vo.getSnapshotRecord();
                String defaultColor = "";
                if (!Objects.isNull(snapshotRecord) && !Objects.isNull(snapshotRecord.getLightStatus())) {
                    defaultColor = LightStatusEnum.getValue(snapshotRecord.getLightStatus().intValue());
                }
                String content = "GetDTCDetailOperation: " + title + ",Color: " + defaultColor;
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", ("1906: " + finalData190).getBytes());
            });
            if (this.pn.a(diagnosticNumber, dtcId, vin, dtcStatus, vo, dtcInfo, dtcInfoId, dataRecordDTO, globalTime, ecuName, platformCode)) {
                return vo;
            }
            return vo;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public RemoteStatusDTO connect(String vin, String vehicle, String first, String second, String model, List<ReloadDto> seqList, Integer remoteTaskType) throws Exception {
        boolean has;
        RemoteStatusDTO remoteStatusDTO = new RemoteStatusDTO();
        String result = this.cloud.a(vin, vehicle, first, second, model, seqList, remoteTaskType);
        if (StringUtils.isNotEmpty(result)) {
            List<DtcInfoDTO> dtcList = new ArrayList();
            remoteStatusDTO = (RemoteStatusDTO) ObjectMapperUtils.jsonStr2Clazz(result, RemoteStatusDTO.class);
            String remoteDtc = remoteStatusDTO.getRemoteDtc();
            try {
                JSONObject.parseArray(remoteDtc);
                has = true;
            } catch (Exception e) {
                has = false;
            }
            if (has && remoteDtc != null) {
                JSONArray v = JSONObject.parseArray(remoteDtc);
                if (!v.isEmpty()) {
                    List<RemoteDtcDTO> dList = new ArrayList<>();
                    List<Object> js = (List) v.stream().filter(o -> {
                        return "19 02".equals(JSON.parseObject(o.toString()).getString("service"));
                    }).collect(Collectors.toList());
                    js.forEach(j -> {
                        JSONObject item = (JSONObject) j;
                        String ecuAddress = item.getString("ecuAddress");
                        List<String> dtc = (List) item.getJSONArray("valueList").stream().map(o2 -> {
                            return JSON.parseObject(o2.toString()).getString(AjaxResult.gA) + JSON.parseObject(o2.toString()).getString("value");
                        }).collect(Collectors.toList());
                        RemoteDtcDTO dto = new RemoteDtcDTO();
                        dto.setEcuAddress(ecuAddress);
                        dto.setDtcInfo(ObjectMapperUtils.obj2JsonStr(dtc));
                        dList.add(dto);
                    });
                    if (StringUtils.isNotEmpty(dList)) {
                        JsonNode jn = new ObjectMapper().valueToTree(dList);
                        dtcList = a(vin, (Integer) 1, jn);
                    }
                    Iterator it = v.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            break;
                        }
                        Object o2 = it.next();
                        JSONObject jsonObject = JSON.parseObject(o2.toString());
                        if ("22".equals(jsonObject.getString("service")) && "DD02".equals(jsonObject.getString("input"))) {
                            JSONArray dids = jsonObject.getJSONArray("valueList");
                            if (!CollectionUtils.isEmpty(dids)) {
                                remoteStatusDTO.setVehicleVoltage(PowerModeRemoteEnum.co(JSON.parseObject(dids.get(0).toString()).getString("valueDisplay")));
                            }
                        }
                    }
                    Iterator it2 = v.iterator();
                    while (true) {
                        if (!it2.hasNext()) {
                            break;
                        }
                        Object o3 = it2.next();
                        JSONObject jsonObject2 = JSON.parseObject(o3.toString());
                        if ("22".equals(jsonObject2.getString("service")) && "DD0A".equals(jsonObject2.getString("input"))) {
                            JSONArray dids2 = jsonObject2.getJSONArray("valueList");
                            if (!CollectionUtils.isEmpty(dids2)) {
                                remoteStatusDTO.setVehicleUsage(PowerModeRemoteEnum.co(JSON.parseObject(dids2.get(0).toString()).getString("valueDisplay")));
                            }
                        }
                    }
                    remoteStatusDTO.setSuccess(true);
                }
            }
            remoteStatusDTO.setList(dtcList);
        }
        return remoteStatusDTO;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public RemoteStatusDTO remoteControl(String vin) throws Exception {
        RemoteStatusDTO remoteStatusDTO = new RemoteStatusDTO();
        remoteStatusDTO.setSuccess(false);
        String result = this.cloud.ay(vin);
        if ("true".equals(result)) {
            remoteStatusDTO.setSuccess(true);
        }
        return remoteStatusDTO;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<EcuBroadcastDto> getEcuList(String vin) throws Exception {
        List<String> vins = new ArrayList<>();
        vins.add(vin);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String data = this.cloud.c(vins, loginUser.getUsername());
        ObjectMapper mapper = new ObjectMapper();
        List<Object> broadcasts = Arrays.asList((Object[]) mapper.readValue(data, Object[].class));
        if (CollectionUtils.isEmpty(broadcasts)) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00118));
        }
        String broadcast = ObjectMapperUtils.obj2JsonStr(broadcasts.get(0));
        System.out.println(broadcast);
        Object strByJsonNodeExpr = ObjectMapperUtils.findObjByJsonNodeExpr(broadcast, "/vehicleData/ecus");
        List<EcuBroadcastDto> ecusByBroadcast = ObjectMapperUtils.jsonStr2List(strByJsonNodeExpr.toString(), EcuBroadcastDto.class);
        return ecusByBroadcast;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public RemoteStatusDTO readoutDtc(String vin, String vehicle, String first, String second, String model) throws Exception {
        RemoteStatusDTO remoteStatusDTO = new RemoteStatusDTO();
        String result = this.cloud.e(vin, vehicle, first, second, model);
        if (StringUtils.isNotEmpty(result)) {
            List<DtcInfoDTO> dtcList = new ArrayList();
            remoteStatusDTO = (RemoteStatusDTO) ObjectMapperUtils.jsonStr2Clazz(result, RemoteStatusDTO.class);
            String remoteDtc = remoteStatusDTO.getRemoteDtc();
            if (StringUtils.isNotEmpty(remoteDtc)) {
                JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(remoteDtc, JsonNode.class);
                if (readValue.isArray()) {
                    dtcList = a(vin, (Integer) 1, readValue);
                    remoteStatusDTO.setSuccess(true);
                }
                remoteStatusDTO.setList(dtcList);
            }
        }
        return remoteStatusDTO;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public RemoteStatusDTO clearDtc(String vin, String vehicle, String first, String second, String model) throws Exception {
        RemoteStatusDTO remoteStatusDTO = new RemoteStatusDTO();
        String result = this.cloud.f(vin, vehicle, first, second, model);
        if (StringUtils.isNotEmpty(result)) {
            remoteStatusDTO = (RemoteStatusDTO) ObjectMapperUtils.jsonStr2Clazz(result, RemoteStatusDTO.class);
        }
        return remoteStatusDTO;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public RemoteStatusDTO readoutParam(List<DiagResItemGroupDto> parmas, String vin, String vehicle, String first, String second, String model) throws Exception {
        RemoteStatusDTO remoteStatusDTO = new RemoteStatusDTO();
        List<DiagResItemParseResultDto> list = new ArrayList<>();
        List<RemoteReadoutParamDto> readoutParamDtoList = BeanUtils.copyListProperties(parmas, RemoteReadoutParamDto::new);
        String result = this.cloud.a(readoutParamDtoList, vin, vehicle, first, second, model);
        if (StringUtils.isNotEmpty(result)) {
            remoteStatusDTO = (RemoteStatusDTO) ObjectMapperUtils.jsonStr2Clazz(result, RemoteStatusDTO.class);
            String msg = remoteStatusDTO.getMsg();
            if (msg.startsWith("00") || msg.startsWith("FF")) {
                TesterVehicleResponseUtils.handleNegativeRes(msg, "22");
                for (DiagResItemGroupDto resItemGroupDto : parmas) {
                    this.po.parseRemoteResponseByResItem(list, resItemGroupDto, resItemGroupDto.getResponseItemDtoList(), msg);
                }
                remoteStatusDTO.setParamList(list);
            }
        }
        return remoteStatusDTO;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public RemoteStatusDTO readoutParam3(List<DiagResItemGroupDto> parmas, String vin, String vehicle, String first, String second, String model) throws Exception {
        RemoteStatusDTO remoteStatusDTO = new RemoteStatusDTO();
        new ArrayList();
        List<RemoteReadoutParamDto> readoutParamDtoList = BeanUtils.copyListProperties(parmas, RemoteReadoutParamDto::new);
        String result = this.cloud.b(readoutParamDtoList, vin, vehicle, first, second, model);
        if (StringUtils.isNotEmpty(result)) {
            remoteStatusDTO = (RemoteStatusDTO) ObjectMapperUtils.jsonStr2Clazz(result, RemoteStatusDTO.class);
            String msg = remoteStatusDTO.getRemoteDtc();
            if (StringUtils.isNotBlank(msg)) {
                remoteStatusDTO.setComplete(true);
                List<DiagResItemParseResultDto> list = cu(msg);
                remoteStatusDTO.setParamList(list);
            }
        }
        return remoteStatusDTO;
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public RemoteStatusDTO ecuIdentify(String vin, String vehicle, String ecuAddress, String ecuName, String diagnosticPartNumber, String first, String second, String model) throws Exception {
        RemoteStatusDTO remoteStatusDTO = new RemoteStatusDTO();
        List<DiagResItemParseResultDto> itemParseResults = new ArrayList<>();
        String result = this.cloud.a(vin, vehicle, ecuAddress, ecuName, diagnosticPartNumber, first, second, model);
        if (StringUtils.isNotEmpty(result)) {
            remoteStatusDTO = (RemoteStatusDTO) ObjectMapperUtils.jsonStr2Clazz(result, RemoteStatusDTO.class);
            String msg = remoteStatusDTO.getMsg();
            if (msg.startsWith("00") || msg.startsWith("FF")) {
                TesterVehicleResponseUtils.handleNegativeRes(msg, "22");
                List<String> dataIdentifierIds = Arrays.asList("F1AA", "F1AE", "F1A0", "F18C");
                List<DiagResItemGroupDto> didListResponseItem = new ArrayList<>();
                try {
                    didListResponseItem = this.po.list(vin, diagnosticPartNumber, ecuName);
                } catch (Exception e) {
                    String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00086);
                    log.error("{},diagnosticNumber={},serviceId={},e=", new Object[]{formatMsg, diagnosticPartNumber, "22", e});
                }
                for (DiagResItemGroupDto resItemGroupDto : (List) didListResponseItem.stream().filter(s -> {
                    return dataIdentifierIds.contains(s.getDataIdentifierId());
                }).collect(Collectors.toList())) {
                    this.po.parseRemoteResponseByResItem(itemParseResults, resItemGroupDto, resItemGroupDto.getResponseItemDtoList(), msg);
                }
                remoteStatusDTO.setParamList(itemParseResults);
            }
        }
        return remoteStatusDTO;
    }

    public String cv(String value) {
        for (String key : this.dtcDisDict.keySet()) {
            if (this.dtcDisDict.get(key).equals(value)) {
                return key;
            }
        }
        return "";
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<ReloadDto> getRemoteSeqByEcu(String ecuName, String vin, String type) throws Exception {
        log.info("======> 开始调用云端 cloud.getSeqByEcu接口 <======");
        String seqByEcu = this.cloud.i(ecuName, vin, this.cloud.an(vin), type);
        log.info("======> 调用云端 cloud.getSeqByEcu接口结束 <======");
        return ObjectMapperUtils.jsonStr2List(seqByEcu, ReloadDto.class);
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public List<RemoteSeqExecuteDTO> querySeqDetail(String taskId) throws Exception {
        String detail = this.cloud.aV(taskId);
        return ObjectMapperUtils.jsonStr2List(detail, RemoteSeqExecuteDTO.class);
    }

    @Override // com.geely.gnds.tester.service.RemoteDiagnosisService
    public void hideApp(String vin, boolean hide) throws Exception {
        this.cloud.b(vin, hide);
    }
}
