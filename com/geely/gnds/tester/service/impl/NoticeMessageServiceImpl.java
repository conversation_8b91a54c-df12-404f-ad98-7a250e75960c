package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.NoticeMessageDTO;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.service.NoticeMessageService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
/* loaded from: NoticeMessageServiceImpl.class */
public class NoticeMessageServiceImpl implements NoticeMessageService {
    private static final Logger log = LoggerFactory.getLogger(NoticeMessageServiceImpl.class);

    @Resource
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.NoticeMessageService
    public List<NoticeMessageDTO> queryMessages() throws Exception {
        return ObjectMapperUtils.jsonStr2List(this.cloud.L(), NoticeMessageDTO.class);
    }

    @Override // com.geely.gnds.tester.service.NoticeMessageService
    public void downLoadFile(String id, HttpServletResponse response) throws Exception {
        log.info("======> 开始请求云端接口：cloud.getNoticeMessagesFileUrl,id:{} <======", id);
        String fileUrl = this.cloud.e(Long.valueOf(Long.parseLong(id)));
        if (StringUtils.isEmpty(fileUrl)) {
            return;
        }
        log.info("======> 开始处理云端返回的docUrl <======");
        c(fileUrl, response);
    }

    private void c(String urlStr, HttpServletResponse response) throws IOException {
        try {
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(300000);
            conn.setRequestProperty("Accept", "*/*");
            conn.setUseCaches(false);
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            try {
                InputStream inputStream = conn.getInputStream();
                Throwable th = null;
                try {
                    try {
                        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
                        int available = inputStream.available();
                        byte[] buff = new byte[available];
                        while (true) {
                            int rc = inputStream.read(buff, 0, available);
                            if (rc <= 0) {
                                break;
                            } else {
                                swapStream.write(buff, 0, rc);
                            }
                        }
                        String fileName = StringUtils.substringAfterLast(urlStr, "/");
                        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
                        response.setContentType("application/octet-stream");
                        response.getOutputStream().write(swapStream.toByteArray());
                        if (inputStream != null) {
                            if (0 != 0) {
                                try {
                                    inputStream.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                inputStream.close();
                            }
                        }
                    } finally {
                    }
                } catch (Throwable th3) {
                    th = th3;
                    throw th3;
                }
            } catch (IOException e) {
                log.error("======> NoticeMessageServiceImpl#downLoadFromUrl 文件下载失败 <======", e);
                GlobalException.c(e);
            }
        } catch (IOException e2) {
            log.error("======> NoticeMessageServiceImpl#downLoadFromUrl 链接cdn服务器异常 <======", e2);
            throw new RuntimeException(e2);
        }
    }
}
