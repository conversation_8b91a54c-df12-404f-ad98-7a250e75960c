package com.geely.gnds.tester.service.impl;

import com.alibaba.fastjson.JSON;
import com.geely.gnds.ruoyi.common.constant.UserConstants;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.bean.BeanUtils;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagnosisEcuDto;
import com.geely.gnds.tester.dto.dtc.DtcExtendedDataRecordDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcSnapshotRecordDTO;
import com.geely.gnds.tester.dto.dtc.ExtendedDataDtcIndicatorDTO;
import com.geely.gnds.tester.dto.dtc.ExtendedDataFdcDTO;
import com.geely.gnds.tester.dto.dtc.ExtendedDataOccDTO;
import com.geely.gnds.tester.dto.dtc.ExtendedDataTimestampDTO;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.service.DtcDataAnalysisService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.ParseUtils;
import com.geely.gnds.tester.vo.DtcExtendedDataVO;
import com.geely.gnds.tester.vo.ExtendedIndicatorVO;
import com.geely.gnds.tester.vo.SnapshotRecordVO;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
/* loaded from: DtcDataAnalysisServiceImpl.class */
public class DtcDataAnalysisServiceImpl implements DtcDataAnalysisService {
    private static final String INT_MAX = "FFFFFFFF";
    private static final Logger log = LoggerFactory.getLogger(DtcDataAnalysisServiceImpl.class);
    private static final Pattern pc = Pattern.compile("[0][1-7]\\w{2}|[1][0-2]\\w{2}|[2][0-1]\\w{8}|2[2-3]\\w{12}|[3][0]\\w{2}");

    public static void main(String[] args) {
        Matcher matcher = pc.matcher("01000200030004010600107F127F2218050F0C04032318050F0C04033000");
        while (matcher.find()) {
            System.out.println(matcher.group());
        }
    }

    @Override // com.geely.gnds.tester.service.DtcDataAnalysisService
    public List<DtcInfoDTO> analysisDtcList(String data) {
        List<DtcInfoDTO> dtcs = new ArrayList<>();
        List<DiagnosisEcuDto> ecusByVehicle = ParseUtils.parseDtcInfo(StringUtils.isEmpty(data) ? "" : data);
        if (!CollectionUtils.isEmpty(ecusByVehicle)) {
            for (DiagnosisEcuDto diagnosisEcuDto : ecusByVehicle) {
                Map dtcInfo = diagnosisEcuDto.getDtcInfo();
                List<String> dtcList = ObjectMapperUtils.jsonStr2List(dtcInfo.get("1902_info").toString(), String.class);
                ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(dtcInfo.get(ConstantEnum.INFO_1906)));
                if (!CollectionUtils.isEmpty(dtcList)) {
                    for (String dtc : dtcList) {
                        DtcInfoDTO dtcInfoDTO = new DtcInfoDTO();
                        dtcInfoDTO.setEcuAddress(diagnosisEcuDto.getEcuAddress());
                        dtcInfoDTO.setDtcInfoId(dtc);
                        String dtcReadValue = dtc.substring(0, 6);
                        dtcInfoDTO.setId(dtcReadValue);
                        String ecuName = diagnosisEcuDto.getEcuName();
                        dtcInfoDTO.setEcuName(ecuName);
                        String ecuAddress = diagnosisEcuDto.getEcuAddress();
                        dtcInfoDTO.setEcuAddress(ecuAddress);
                        dtcs.add(dtcInfoDTO);
                    }
                }
            }
        }
        return dtcs;
    }

    @Override // com.geely.gnds.tester.service.DtcDataAnalysisService
    public DtcExtendedDataRecordDTO analysisDtcExtendedDataRecord(String data) {
        if (ObjectUtils.isEmpty(data)) {
            return null;
        }
        DtcExtendedDataRecordDTO record = new DtcExtendedDataRecordDTO();
        Matcher matcher = pc.matcher(data);
        record.setRowData(data);
        while (matcher.find()) {
            a(record, matcher.group());
        }
        return record;
    }

    @Override // com.geely.gnds.tester.service.DtcDataAnalysisService
    public List<DtcSnapshotRecordDTO> analysisDtcDtcSnapshotRecord(List<DiagResItemGroupDto> diagParamDtos, String data) throws NumberFormatException {
        if (ObjectUtils.isEmpty(data) || CollectionUtils.isEmpty(diagParamDtos)) {
            return null;
        }
        DiagResItemGroupDto groupDto = diagParamDtos.get(0);
        List<DtcSnapshotRecordDTO> list = new ArrayList<>();
        TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00083);
        while (StringUtils.isNotEmpty(data)) {
            DtcSnapshotRecordDTO snapshot = new DtcSnapshotRecordDTO();
            snapshot.setName(data.substring(0, 2));
            snapshot.setParametersNumber(Integer.valueOf(data.substring(2, 4), 16));
            List<DtcSnapshotRecordDTO.DidDTO> didList = new ArrayList<>();
            int index = 4;
            int i = 0;
            while (true) {
                if (i < snapshot.getParametersNumber().intValue()) {
                    DtcSnapshotRecordDTO.DidDTO did = new DtcSnapshotRecordDTO.DidDTO();
                    int i2 = index;
                    int index2 = index + 4;
                    did.setDidName(data.substring(i2, index2));
                    DiagResItemGroupDto diagResItemGroupDto = diagParamDtos.stream().filter(d -> {
                        return did.getDidName().equalsIgnoreCase(d.getDataIdentifierId());
                    }).findFirst().orElse(null);
                    if (ObjectUtils.isEmpty(diagResItemGroupDto)) {
                        log.error("解析DTC1904数据失败 解析规则为null,diagnosticPartNumber={},did={},rowData={},DTC={}", new Object[]{groupDto.getDiagnosticPartNumber(), did.getDidName(), data});
                        index = data.length();
                        break;
                    }
                    try {
                        int size = Integer.parseInt(diagResItemGroupDto.getSize());
                        did.setId(diagResItemGroupDto.getId());
                        int i3 = index2 + (size * 2);
                        index = i3;
                        did.setDidValue(data.substring(index2, i3));
                        didList.add(did);
                        i++;
                    } catch (Exception e) {
                        log.error("解析DTC1904数据失败,diagnosticPartNumber={},did={},rowData={},e={}", new Object[]{groupDto.getDiagnosticPartNumber(), did.getDidName(), data, e});
                        index = data.length();
                    }
                }
            }
            snapshot.setDidList(didList);
            snapshot.setRawData(data.substring(0, index));
            data = data.substring(index);
            list.add(snapshot);
        }
        if (!CollectionUtils.isEmpty(list)) {
            DtcSnapshotRecordDTO y20 = list.stream().filter(x -> {
                return "20".equals(x.getName());
            }).findFirst().orElseGet(() -> {
                DtcSnapshotRecordDTO dto = new DtcSnapshotRecordDTO();
                dto.setName("20");
                dto.setDidList(new ArrayList());
                list.add(dto);
                return dto;
            });
            DtcSnapshotRecordDTO y21 = list.stream().filter(x2 -> {
                return "21".equals(x2.getName());
            }).findFirst().orElseGet(() -> {
                DtcSnapshotRecordDTO dto = new DtcSnapshotRecordDTO();
                dto.setName("21");
                dto.setDidList(new ArrayList());
                list.add(dto);
                return dto;
            });
            List<DtcSnapshotRecordDTO> otherSnapshotRecord = (List) list.stream().filter(x3 -> {
                return ("20".equals(x3.getName()) || "21".equals(x3.getName())) ? false : true;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(otherSnapshotRecord)) {
                log.info("---analysisDtcDtcSnapshotRecord---解析到其他name={},diagnosticPartNumber={},ecu={},address={}", new Object[]{otherSnapshotRecord.stream().map((v0) -> {
                    return v0.getName();
                }).collect(Collectors.toList()), groupDto.getDiagnosticPartNumber(), groupDto.getEcuName(), groupDto.getAddress()});
                List<DtcSnapshotRecordDTO.DidDTO> otherDidList = (List) otherSnapshotRecord.stream().map((v0) -> {
                    return v0.getDidList();
                }).flatMap((v0) -> {
                    return v0.stream();
                }).collect(Collectors.toList());
                y20.getDidList().addAll(otherDidList);
                y20.setParametersNumber(Integer.valueOf(y20.getDidList().size()));
                y21.getDidList().addAll(otherDidList);
                y21.setParametersNumber(Integer.valueOf(y21.getDidList().size()));
            }
        }
        return list;
    }

    @Override // com.geely.gnds.tester.service.DtcDataAnalysisService
    public DtcExtendedDataVO convertExtendDto2Vo(DtcExtendedDataRecordDTO dto, String dtcStatus, DtcExtendedDataVO vo) {
        if (ObjectUtils.isEmpty(vo)) {
            vo = new DtcExtendedDataVO();
        }
        if (!ObjectUtils.isEmpty(dto)) {
            vo.setOccData(BeanUtils.copyListProperties(dto.getOccDTO(), DtcExtendedDataVO.ExtendedOccDataVO::new));
            vo.setFdcData(BeanUtils.copyListProperties(dto.getFdcDTO(), DtcExtendedDataVO.ExtendedFdcVO::new));
            vo.setTimestampData(BeanUtils.copyListProperties(dto.getTimestampDTO(), DtcExtendedDataVO.ExtendedTimestampVO::new));
            ExtendedDataDtcIndicatorDTO indicatorDTO = dto.getIndicatorDTO();
            ExtendedIndicatorVO extendedIndicatorVO = new ExtendedIndicatorVO();
            if (!ObjectUtils.isEmpty(indicatorDTO)) {
                byte[] indicatorBits = ParseUtils.byteToBitOfArray((byte) Integer.parseInt(String.valueOf(indicatorDTO.getValue()), 16));
                analysisDtcExtendedIndicator(indicatorBits, extendedIndicatorVO);
            }
            if (!ObjectUtils.isEmpty(dtcStatus)) {
                byte[] statusBits = ParseUtils.byteToBitOfArray((byte) Integer.parseInt(dtcStatus, 16));
                analysisDtcStatusMask(statusBits, extendedIndicatorVO);
            }
            vo.setIndicatorData(extendedIndicatorVO);
        }
        return vo;
    }

    @Override // com.geely.gnds.tester.service.DtcDataAnalysisService
    public SnapshotRecordVO fillTimeAxis(DtcExtendedDataRecordDTO extendedData, SnapshotRecordVO vo, Long globalTime, Date cacheTime, int platformCode) {
        if (ObjectUtils.isEmpty(vo)) {
            vo = new SnapshotRecordVO();
        }
        if (ObjectUtils.isEmpty(extendedData) || ObjectUtils.isEmpty(extendedData.getOccDTO())) {
            return vo;
        }
        if (!ObjectUtils.isEmpty(cacheTime)) {
            vo.setNow(Long.valueOf(cacheTime.getTime()));
        } else {
            vo.setNow(Long.valueOf(System.currentTimeMillis()));
        }
        Map<String, ExtendedDataOccDTO> occMap = (Map) extendedData.getOccDTO().stream().collect(Collectors.toMap((v0) -> {
            return v0.getName();
        }, Function.identity(), (k1, k2) -> {
            return k1;
        }));
        Integer occ01 = 0;
        Integer occ02 = 0;
        Integer occ03 = 0;
        Integer occ04 = 0;
        Integer occ07 = 0;
        if (occMap.containsKey("01")) {
            occ01 = Integer.valueOf(Integer.parseInt(occMap.get("01").getValue().toString()));
        }
        if (occMap.containsKey("02")) {
            occ02 = Integer.valueOf(Integer.parseInt(occMap.get("02").getValue().toString()));
        }
        if (occMap.containsKey("03")) {
            occ03 = Integer.valueOf(Integer.parseInt(occMap.get("03").getValue().toString()));
        }
        if (occMap.containsKey("04")) {
            occ04 = Integer.valueOf(Integer.parseInt(occMap.get("04").getValue().toString()));
        }
        if (occMap.containsKey("07")) {
            occ07 = Integer.valueOf(Integer.parseInt(occMap.get("07").getValue().toString()));
        }
        Long now = vo.getNow();
        List<ExtendedDataTimestampDTO> timestampDTO = extendedData.getTimestampDTO() == null ? new ArrayList<>() : extendedData.getTimestampDTO();
        SnapshotRecordVO.TimeAxisVO firstToLast = new SnapshotRecordVO.TimeAxisVO();
        firstToLast.setAll(Integer.valueOf((occ03.intValue() - occ01.intValue()) + 1));
        firstToLast.setPassed(Integer.valueOf(((occ07.intValue() - occ04.intValue()) - occ02.intValue()) + 1));
        firstToLast.setUnconfirmed(occ04);
        firstToLast.setNoResult(Integer.valueOf((firstToLast.getAll().intValue() - firstToLast.getUnconfirmed().intValue()) - firstToLast.getPassed().intValue()));
        SnapshotRecordVO.TimeAxisVO lastToNow = new SnapshotRecordVO.TimeAxisVO();
        lastToNow.setAll(occ01);
        lastToNow.setPassed(occ02);
        lastToNow.setUnconfirmed(0);
        lastToNow.setNoResult(Integer.valueOf((lastToNow.getAll().intValue() - lastToNow.getUnconfirmed().intValue()) - lastToNow.getPassed().intValue()));
        if (platformCode == 3) {
            AtomicBoolean flag = new AtomicBoolean(true);
            log.info("dtc时间轴参数platformCode:{}", JSON.toJSONString(timestampDTO));
            timestampDTO.stream().filter(t -> {
                return "22".equals(t.getName());
            }).findFirst().ifPresent(x -> {
                try {
                    flag.set(false);
                    log.info("dtc时间轴参数1：{}", JSON.toJSONString(x));
                    firstToLast.setTimeStamp(Long.valueOf(ParseUtils.parseDtcDate(x.getValue().toString())));
                } catch (Exception e) {
                    log.error("设置first To Last时间异常,now={},globalTime={},value={},e={}", new Object[]{now, globalTime, x.getValue(), e});
                }
            });
            timestampDTO.stream().filter(t2 -> {
                return "23".equals(t2.getName());
            }).findFirst().ifPresent(x2 -> {
                try {
                    flag.set(false);
                    log.info("dtc时间轴参数2：{}", JSON.toJSONString(x2));
                    lastToNow.setTimeStamp(Long.valueOf(ParseUtils.parseDtcDate(x2.getValue().toString())));
                } catch (Exception e) {
                    log.error("设置last To Now 时间异常,now={},globalTime={},value={},e={}", new Object[]{now, globalTime, x2.getValue(), e});
                }
            });
            if (flag.get()) {
                a(globalTime, timestampDTO, firstToLast, now, lastToNow);
            }
        } else {
            a(globalTime, timestampDTO, firstToLast, now, lastToNow);
        }
        vo.setFirstToLast(firstToLast);
        vo.setLastToNow(lastToNow);
        return vo;
    }

    private static void a(Long globalTime, List<ExtendedDataTimestampDTO> timestampDTO, SnapshotRecordVO.TimeAxisVO firstToLast, Long now, SnapshotRecordVO.TimeAxisVO lastToNow) {
        timestampDTO.stream().filter(t -> {
            return "20".equals(t.getName());
        }).findFirst().ifPresent(x -> {
            try {
                firstToLast.setTimeStamp(Long.valueOf(now.longValue() - (globalTime.longValue() - (Long.parseLong(x.getValue().toString()) * 100))));
            } catch (Exception e) {
                log.error("设置first To Last时间异常,now={},globalTime={},value={},e={}", new Object[]{now, globalTime, x.getValue(), e});
            }
        });
        timestampDTO.stream().filter(t2 -> {
            return "21".equals(t2.getName());
        }).findFirst().ifPresent(x2 -> {
            try {
                lastToNow.setTimeStamp(Long.valueOf(now.longValue() - (globalTime.longValue() - (Long.parseLong(x2.getValue().toString()) * 100))));
            } catch (Exception e) {
                log.error("设置last To Now 时间异常,now={},globalTime={},value={},e={}", new Object[]{now, globalTime, x2.getValue(), e});
            }
        });
    }

    @Override // com.geely.gnds.tester.service.DtcDataAnalysisService
    public ExtendedIndicatorVO analysisDtcExtendedIndicator(byte[] indicatorBits, ExtendedIndicatorVO vo) {
        if (ObjectUtils.isEmpty(vo)) {
            vo = new ExtendedIndicatorVO();
        }
        ExtendedIndicatorVO.DtcExtendedDataRecord extendedDataRecord = vo.getExtendedDataRecord();
        extendedDataRecord.setUnConfirmedNow(Boolean.valueOf(indicatorBits[7] == 1));
        extendedDataRecord.setUnConfirmedThisCycle(Boolean.valueOf(indicatorBits[6] == 1));
        extendedDataRecord.setUnConfirmedSinceErase(Boolean.valueOf(indicatorBits[5] == 1));
        extendedDataRecord.setAgedDtc(Boolean.valueOf(indicatorBits[4] == 1));
        extendedDataRecord.setSymptomSinceErase(Boolean.valueOf(indicatorBits[3] == 1));
        extendedDataRecord.setWarningIndicatorSinceErase(Boolean.valueOf(indicatorBits[2] == 1));
        extendedDataRecord.setEmissionRelatedDtc(Boolean.valueOf(indicatorBits[1] == 1));
        extendedDataRecord.setFailedSinceAgingOrErase(Boolean.valueOf(indicatorBits[0] == 1));
        return vo;
    }

    @Override // com.geely.gnds.tester.service.DtcDataAnalysisService
    public ExtendedIndicatorVO analysisDtcStatusMask(byte[] statusMaskBits, ExtendedIndicatorVO vo) {
        if (ObjectUtils.isEmpty(vo)) {
            vo = new ExtendedIndicatorVO();
        }
        ExtendedIndicatorVO.DtcStatusMask statusMask = vo.getStatusMask();
        statusMask.setFailedNow(Boolean.valueOf(statusMaskBits[7] == 1));
        statusMask.setFailedThisCycle(Boolean.valueOf(statusMaskBits[6] == 1));
        statusMask.setPending(Boolean.valueOf(statusMaskBits[5] == 1));
        statusMask.setConfirmed(Boolean.valueOf(statusMaskBits[4] == 1));
        statusMask.setNotCompletedSinceErase(Boolean.valueOf(statusMaskBits[3] == 1));
        statusMask.setFailedSinceErase(Boolean.valueOf(statusMaskBits[2] == 1));
        statusMask.setNotCompletedThisCycle(Boolean.valueOf(statusMaskBits[1] == 1));
        statusMask.setWarningIndicatorNow(Boolean.valueOf(statusMaskBits[0] == 1));
        return vo;
    }

    private void a(DtcExtendedDataRecordDTO record, String matcherData) {
        if (!ObjectUtils.isEmpty(record)) {
            char[] chars = matcherData.toCharArray();
            char headMark = chars[0];
            switch (headMark) {
                case '0':
                    List<ExtendedDataOccDTO> occs = record.getOccDTO();
                    if (ObjectUtils.isEmpty(occs)) {
                        occs = new ArrayList();
                        record.setOccDTO(occs);
                    }
                    ExtendedDataOccDTO occ = new ExtendedDataOccDTO();
                    occ.setName(matcherData.substring(0, 2));
                    occ.setValue(Long.valueOf(matcherData.substring(2, 4), 16));
                    occs.add(occ);
                    break;
                case '1':
                    List<ExtendedDataFdcDTO> fdcs = record.getFdcDTO();
                    if (ObjectUtils.isEmpty(fdcs)) {
                        fdcs = new ArrayList();
                        record.setFdcDTO(fdcs);
                    }
                    ExtendedDataFdcDTO<Integer> fdc = new ExtendedDataFdcDTO<>();
                    fdc.setName(matcherData.substring(0, 2));
                    fdc.setValue(cs(matcherData.substring(2, 4)));
                    fdcs.add(fdc);
                    break;
                case '2':
                    List<ExtendedDataTimestampDTO> timestamps = record.getTimestampDTO();
                    if (ObjectUtils.isEmpty(timestamps)) {
                        timestamps = new ArrayList();
                        record.setTimestampDTO(timestamps);
                    }
                    ExtendedDataTimestampDTO<Long> timestamp = new ExtendedDataTimestampDTO<>();
                    String substring = matcherData.substring(0, 2);
                    timestamp.setName(substring);
                    if ("22".equals(substring) || "23".equals(substring)) {
                        timestamp.setValue(matcherData.substring(2));
                    } else {
                        timestamp.setValue(Long.valueOf(matcherData.substring(2, 10), 16));
                    }
                    timestamps.add(timestamp);
                    break;
                case '3':
                    ExtendedDataDtcIndicatorDTO indicator = record.getIndicatorDTO();
                    if (ObjectUtils.isEmpty(indicator)) {
                        indicator = new ExtendedDataDtcIndicatorDTO();
                        record.setIndicatorDTO(indicator);
                    }
                    indicator.setName(matcherData.substring(0, 2));
                    indicator.setValue(matcherData.substring(2, 4));
                    break;
            }
        }
    }

    private static String cs(String outValue) {
        String obj = "";
        String firstHex = outValue.substring(0, 1);
        if (Integer.parseInt(firstHex, 16) >= 8) {
            StringBuilder sb = new StringBuilder();
            if (outValue.length() < 8) {
                int appendSize = 8 - outValue.length();
                for (int i = 0; i < appendSize; i++) {
                    sb.append(UserConstants.TYPE_BUTTON);
                }
            } else if (outValue.length() < 16) {
                int appendSize2 = 16 - outValue.length();
                for (int i2 = 0; i2 < appendSize2; i2++) {
                    sb.append(UserConstants.TYPE_BUTTON);
                }
            }
            sb.append(outValue);
            BigInteger hexToIntValue = new BigInteger(sb.toString(), 16);
            try {
                if (hexToIntValue.compareTo(new BigInteger(INT_MAX, 16)) == 1) {
                    obj = Double.valueOf(hexToIntValue.longValue()).toString();
                } else {
                    obj = Double.valueOf(hexToIntValue.intValue()).toString();
                }
            } catch (Exception e) {
                log.error("十六进制转int带符号数字出错【{}】", outValue, e);
            }
        } else {
            BigInteger hexToIntValue2 = new BigInteger(outValue, 16);
            try {
                if (hexToIntValue2.compareTo(new BigInteger(INT_MAX, 16)) == 1) {
                    obj = Double.valueOf(hexToIntValue2.longValue()).toString();
                } else {
                    obj = Double.valueOf(hexToIntValue2.intValue()).toString();
                }
            } catch (Exception e2) {
                log.error("十六进制转int带符号数字出错【{}】", outValue, e2);
            }
        }
        if (obj.contains(ConstantEnum.POINT)) {
            obj = obj.substring(0, obj.indexOf(ConstantEnum.POINT));
        }
        return obj;
    }
}
