package com.geely.gnds.tester.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.OtherSeqDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.service.ReloadOrOtherService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
/* loaded from: ReloadOrOtherServiceImpl.class */
public class ReloadOrOtherServiceImpl implements ReloadOrOtherService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private FileCache fileCache;

    @Value("${tester.tenantName}")
    private String tenantName;

    @Autowired
    private TokenService tokenService;
    private static final Logger log = LoggerFactory.getLogger(ReloadOrOtherServiceImpl.class);

    @Override // com.geely.gnds.tester.service.ReloadOrOtherService
    public List queryReloadSeqList(String vin) throws Exception {
        List<ReloadDto> list = new ArrayList<>();
        String seqType = this.cloud.an(vin);
        log.info("reloadOrOtherService cloud.getSeqType");
        LoginUser loginUser = this.tokenService.getLoginUser();
        String reload = this.cloud.k(vin, seqType, loginUser.getUsername());
        JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(reload, JsonNode.class);
        log.info("reloadOrOtherService 整车版本号开始");
        if (readValue.isArray()) {
            readValue.forEach(obj -> {
                ReloadDto reloadDto = (ReloadDto) ObjectMapperUtils.getInstance().convertValue(obj, ReloadDto.class);
                list.add(reloadDto);
            });
        }
        log.info("reloadOrOtherService 整车版本号结束");
        return list;
    }

    @Override // com.geely.gnds.tester.service.ReloadOrOtherService
    public List queryOtherSeqList(String vin) throws Exception {
        List<OtherSeqDto> list = new ArrayList<>();
        String other = this.cloud.n(vin, this.cloud.an(vin));
        JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(other, JsonNode.class);
        if (readValue.isArray()) {
            readValue.forEach(obj -> {
                OtherSeqDto otherSeqDto = (OtherSeqDto) ObjectMapperUtils.getInstance().convertValue(obj, OtherSeqDto.class);
                list.add(otherSeqDto);
            });
        }
        return list;
    }

    @Override // com.geely.gnds.tester.service.ReloadOrOtherService
    public List queryDevelopSeqList(String vin) throws Exception {
        new ArrayList();
        String other = this.cloud.o(vin, this.cloud.an(vin));
        return ObjectMapperUtils.jsonStr2List(other, OtherSeqDto.class);
    }

    @Override // com.geely.gnds.tester.service.ReloadOrOtherService
    public String initFlush(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        ScriptHandler handler;
        String vin = initializeUiDto.getVin();
        String seqCode = initializeUiDto.getSeqCode();
        String args = initializeUiDto.getArgs();
        String type = initializeUiDto.getType();
        String ver = initializeUiDto.getVersion();
        boolean nested = initializeUiDto.isNested();
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        if (TesterLoginUtils.isOnLine()) {
            if (StringUtils.isBlank(seqCode)) {
                seqCode = this.cloud.l(type, vin);
            }
            ReloadDto seqBycode = this.cloud.a(seqCode, vin, nested, false, ver, "");
            String seq = seqBycode.getSeqContentString();
            String version = seqBycode.getVersion();
            if (StringUtils.isBlank(seq)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
            }
            if (StringUtils.isBlank(version)) {
                version = SeqManager.getInstance().getSeqVersionCache(seqCode);
            }
            handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, version, user.getUsername(), this.tenantName, user.getUser().getServiceStationCode(), seqBycode.getRequestId());
        } else {
            String filePath = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
            }
            handler = new ScriptHandler(new File(filePath), vin, args, seqCode, "1.0", "");
        }
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        return handler.getInitializeUi();
    }

    @Override // com.geely.gnds.tester.service.ReloadOrOtherService
    public String initFlush2(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        ScriptHandler handler;
        String vin = initializeUiDto.getVin();
        String seqCode = initializeUiDto.getSeqCode();
        String args = initializeUiDto.getArgs();
        String type = initializeUiDto.getType();
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        if (TesterLoginUtils.isOnLine()) {
            if (StringUtils.isBlank(seqCode)) {
                seqCode = this.cloud.l(type, vin);
            }
            handler = new ScriptHandler(new File(ConstantEnum.POINT, "pcaps" + File.separator + seqCode + ConstantEnum.JSON), vin, args, seqCode, initializeParams, "1.0", user.getUsername(), this.tenantName, user.getUser().getServiceStationCode(), "");
        } else {
            String filePath = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
            }
            handler = new ScriptHandler(new File(filePath), vin, args, seqCode, "1.0", "");
        }
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        return handler.getInitializeUi();
    }
}
