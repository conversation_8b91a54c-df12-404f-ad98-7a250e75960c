package com.geely.gnds.tester.service.impl;

import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.service.TesterConfigService;
import com.geely.gnds.tester.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: TesterConfigServiceImpl.class */
public class TesterConfigServiceImpl implements TesterConfigService {

    @Autowired
    private TesterConfigDao eC;

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.TesterConfigService
    public int insert(TesterConfigDto testerConfigDto) {
        int i = this.eC.insert(testerConfigDto);
        return i;
    }

    @Override // com.geely.gnds.tester.service.TesterConfigService
    public void update(TesterConfigDto testerConfigDto) {
        this.eC.update(testerConfigDto);
    }

    @Override // com.geely.gnds.tester.service.TesterConfigService
    public TesterConfigDto getConfig() {
        TesterConfigDto dto = this.eC.getConfig();
        return dto;
    }

    @Override // com.geely.gnds.tester.service.TesterConfigService
    public Result<String> getVdnImage(String vin) throws Exception {
        Result<String> result = new Result<>();
        String res = this.cloud.aB(vin);
        return result.ok(res);
    }
}
