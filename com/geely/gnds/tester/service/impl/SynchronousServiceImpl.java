package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.mapper.SysUserMapper;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.DsaSequenceDao;
import com.geely.gnds.tester.dao.TesterSequenceDao;
import com.geely.gnds.tester.dto.PermissionDTO;
import com.geely.gnds.tester.entity.DsaSequenceEntity;
import com.geely.gnds.tester.entity.TesterSequenceEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.SeqButtonTypeEnum;
import com.geely.gnds.tester.service.SynchronousService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: SynchronousServiceImpl.class */
public class SynchronousServiceImpl implements SynchronousService {
    private static final Logger LOG = LoggerFactory.getLogger(SynchronousServiceImpl.class);

    @Autowired
    private SysUserMapper hA;

    @Autowired
    private Cloud cloud;

    @Autowired
    private TesterSequenceDao pq;

    @Autowired
    private FileCache fileCache;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private DsaSequenceDao eK;

    @Override // com.geely.gnds.tester.service.SynchronousService
    public SysUser synchronousUsers(String userName) throws Exception {
        String testerInfo = this.cloud.ap(userName);
        if (StringUtils.isNotBlank(testerInfo)) {
            Object object = ObjectMapperUtils.getInstance().readValue(testerInfo, Object.class);
            Map<String, Object> map = ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(object));
            String username = map.get(ConstantEnum.USERNAME_STR).toString();
            boolean flag = false;
            SysUser sysUser = this.hA.selectUserByUserName(username);
            if (sysUser == null) {
                sysUser = new SysUser();
                Object id = map.get("id");
                sysUser.setUserId(id == null ? null : Long.valueOf(id.toString()));
                flag = true;
            }
            sysUser.setUserName(username);
            a(map, sysUser);
            b(map, sysUser);
            SimpleDateFormat format = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
            sysUser.setLastOnlineLoginTime(format.format(new Date()));
            if (flag) {
                this.hA.insertUser(sysUser);
            } else {
                this.hA.updateUser(sysUser);
            }
            Optional.ofNullable(map.get("pcapLogUploadFlag")).ifPresent(v -> {
                this.ossUtils.setUploadPcapLog(Integer.parseInt(v.toString()) == 1);
            });
            Optional.ofNullable(map.get("sysLogUploadFlag")).ifPresent(v2 -> {
                this.ossUtils.setUploadSysLog(Integer.parseInt(v2.toString()) == 1);
            });
            return sysUser;
        }
        return null;
    }

    private void a(Map<String, Object> map, SysUser sysUser) {
        String now = String.valueOf(System.currentTimeMillis());
        Object realName = map.get("realName");
        Object password = map.get("password");
        Object gender = map.get("gender");
        Object mobile = map.get("mobile");
        Object email = map.get("email");
        Object seqStatus = map.get("seqStatus");
        Object softwareStatus = map.get("softwareStatus");
        Object brandSelect = map.get("brandSelect");
        map.put("seqStatus", ObjectUtils.isNotEmpty(sysUser.getSeqStatus()) ? sysUser.getSeqStatus() : seqStatus);
        map.put("softwareStatus", ObjectUtils.isNotEmpty(sysUser.getSoftwareStatus()) ? sysUser.getSoftwareStatus() : softwareStatus);
        map.put("brandSelect", ObjectUtils.isNotEmpty(sysUser.getBrandSelect()) ? sysUser.getBrandSelect() : brandSelect);
        Object updateDate = map.get("updateDate");
        Object createDate = map.get("createDate");
        sysUser.setNickName(realName == null ? null : realName.toString());
        sysUser.setPassword(password == null ? null : password.toString());
        sysUser.setSex(gender == null ? null : gender.toString());
        sysUser.setPhonenumber(mobile == null ? null : mobile.toString());
        sysUser.setEmail(email == null ? null : email.toString());
        if (updateDate == null) {
            sysUser.setUpdateTime(now);
        } else {
            Date parseDate = DateUtils.parseDate(createDate);
            if (parseDate != null) {
                sysUser.setUpdateTime(String.valueOf(parseDate.getTime()));
            }
        }
        if (createDate == null) {
            sysUser.setCreateTime(now);
            return;
        }
        Date parseDate2 = DateUtils.parseDate(createDate);
        if (parseDate2 != null) {
            sysUser.setCreateTime(String.valueOf(parseDate2.getTime()));
        }
    }

    private void b(Map<String, Object> map, SysUser sysUser) {
        Date parseDate;
        Object seqStatus = map.get("seqStatus");
        Object softwareStatus = map.get("softwareStatus");
        Object passwordExpired = map.get("passwordExpired");
        Object accountExpired = map.get("accountExpired");
        Object brandSelect = map.get("brandSelect");
        Object timeSelect = map.get("timeSelect");
        Object readNum = map.get("readNum");
        Object vinDecode = map.get("vinDecode");
        Object recentVehicles = map.get("recentVehicles");
        Object startTab = map.get("startTab");
        Object lang = map.get("lang");
        Object clientPermissionStr = map.get("clientPermissionStr");
        Object serviceStationCode = map.get("serviceStationCode");
        Object serviceStationName = map.get("serviceStationName");
        Object serviceStationLargeArea = map.get("serviceStationLargeArea");
        Object serviceStationArea = map.get("serviceStationArea");
        Object serviceStationProvince = map.get("serviceStationProvince");
        Object serviceStationCity = map.get("serviceStationCity");
        Object offlineLoginValidity = map.get("offlineLoginValidity");
        Object paramShowType = map.get("paramShowType");
        sysUser.setPasswordExpired(passwordExpired == null ? null : Integer.valueOf(passwordExpired.toString()));
        if (accountExpired != null && (parseDate = DateUtils.parseDate(accountExpired)) != null) {
            sysUser.setAccountExpired(String.valueOf(parseDate.getTime()));
        }
        String permission = clientPermissionStr == null ? "" : clientPermissionStr.toString();
        sysUser.setVinDecode(vinDecode == null ? null : Integer.valueOf(vinDecode.toString()));
        sysUser.setStartTab(startTab == null ? null : startTab.toString());
        sysUser.setRecentVehicles(recentVehicles == null ? null : Integer.valueOf(recentVehicles.toString()));
        sysUser.setReadNum(readNum == null ? null : Integer.valueOf(readNum.toString()));
        sysUser.setTimeSelect(timeSelect == null ? null : Integer.valueOf(timeSelect.toString()));
        sysUser.setPermission(permission);
        sysUser.setServiceStationCode(serviceStationCode == null ? "" : serviceStationCode.toString());
        sysUser.setServiceStationName(serviceStationName == null ? "" : serviceStationName.toString());
        sysUser.setServiceStationLargeArea(serviceStationLargeArea == null ? "" : serviceStationLargeArea.toString());
        sysUser.setServiceStationArea(serviceStationArea == null ? "" : serviceStationArea.toString());
        sysUser.setServiceStationProvince(serviceStationProvince == null ? "" : serviceStationProvince.toString());
        sysUser.setServiceStationCity(serviceStationCity == null ? "" : serviceStationCity.toString());
        sysUser.setOfflineLoginValidity(Integer.valueOf(offlineLoginValidity == null ? 0 : Integer.valueOf(offlineLoginValidity.toString()).intValue()));
        sysUser.setParamShowType(Integer.valueOf(paramShowType == null ? 1 : Integer.valueOf(paramShowType.toString()).intValue()));
        sysUser.setLang(lang == null ? "" : lang.toString());
        PermissionDTO permissionDTO = (PermissionDTO) ObjectMapperUtils.jsonStr2Clazz(permission, PermissionDTO.class);
        String bssVersion = permissionDTO.getBssVersion();
        String diagnosticVersion = permissionDTO.getDiagnosticVersion();
        String brands = permissionDTO.getBrands();
        Integer seqStatusInt = seqStatus == null ? null : Integer.valueOf(seqStatus.toString());
        sysUser.setSeqStatus(c(diagnosticVersion, seqStatusInt));
        Integer softwareStatusInt = softwareStatus == null ? null : Integer.valueOf(softwareStatus.toString());
        sysUser.setSoftwareStatus(c(bssVersion, softwareStatusInt));
        if (brandSelect != null) {
            sysUser.setBrandSelect(Long.valueOf(c(brands, Integer.valueOf(Long.valueOf(brandSelect.toString()).intValue())).intValue()));
        }
    }

    private Integer c(String permission, Integer select) throws NumberFormatException {
        LOG.info("getDefault入参--{}---{}", permission, select);
        if (select == null) {
            return select;
        }
        try {
            if (StringUtils.isNotBlank(permission)) {
                String[] split = permission.split(ConstantEnum.COMMA);
                List<String> list = Arrays.asList(split);
                if (!list.contains(String.valueOf(select))) {
                    LOG.info("getDefault不包含");
                    select = 0;
                    for (String str : split) {
                        int parseInt = Integer.parseInt(str);
                        if (parseInt > select.intValue()) {
                            select = Integer.valueOf(parseInt);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("getDefault异常", e);
        }
        LOG.info("getDefault出参---{}", select);
        return select;
    }

    @Override // com.geely.gnds.tester.service.SynchronousService
    public void synchronousSeqs(String username) throws Exception {
        String userName;
        String offlineSeqInfo = this.cloud.aq(username);
        List<Object> list = Arrays.asList((Object[]) ObjectMapperUtils.getInstance().readValue(offlineSeqInfo, Object[].class));
        if (!CollectionUtils.isEmpty(list)) {
            for (Object object : list) {
                Map<String, Object> map = ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(object));
                String seqCode = map.get("seqCode").toString();
                String version = map.get("version").toString();
                String seqContent = ObjectMapperUtils.obj2JsonStr(map.get("seqContent"));
                Map<String, Object> params = new HashMap<>(2);
                params.put("sequenceId", seqCode);
                LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
                if (loginUser != null) {
                    userName = loginUser.getUsername();
                } else {
                    userName = "admin";
                }
                Date now = new Date();
                List<TesterSequenceEntity> seqIdList = this.pq.queryList(params);
                TesterSequenceEntity testerSequenceEntity = new TesterSequenceEntity();
                testerSequenceEntity.setApplicability(map.get("applic").toString());
                testerSequenceEntity.setSequenceId(seqCode);
                testerSequenceEntity.setVersion(version);
                testerSequenceEntity.setName(map.get("name").toString());
                testerSequenceEntity.setStatus(map.get("status").toString());
                if (CollectionUtils.isEmpty(seqIdList)) {
                    testerSequenceEntity.setCreateBy(userName);
                    testerSequenceEntity.setUpdateBy(userName);
                    testerSequenceEntity.setCreateTime(now);
                    testerSequenceEntity.setUpdateTime(now);
                    this.fileCache.k(seqCode, seqContent);
                    this.pq.create(testerSequenceEntity);
                } else {
                    testerSequenceEntity.setId(seqIdList.get(0).getId());
                    testerSequenceEntity.setUpdateBy(userName);
                    testerSequenceEntity.setUpdateTime(now);
                    params.put("version", version);
                    List<TesterSequenceEntity> seqs = this.pq.queryList(params);
                    if (CollectionUtils.isEmpty(seqs)) {
                        this.fileCache.ai(seqCode);
                        this.fileCache.k(seqCode, seqContent);
                    }
                    this.pq.update(testerSequenceEntity);
                }
            }
        }
    }

    @Override // com.geely.gnds.tester.service.SynchronousService
    public void synchronousDsaSeqs(String username) throws Exception {
        List btnCodes = new ArrayList();
        btnCodes.add(SeqButtonTypeEnum.DSA_SWITCH_MODE.getValue());
        btnCodes.add(SeqButtonTypeEnum.DSA_STATUS_READOUT.getValue());
        List<DsaSequenceEntity> dsaSequenceEntities = new ArrayList<>();
        DsaSequenceEntity dsaSequenceEntity = this.eK.selectByBtnCode(SeqButtonTypeEnum.DSA_SWITCH_MODE.getValue());
        if (dsaSequenceEntity == null) {
            dsaSequenceEntity = new DsaSequenceEntity();
            dsaSequenceEntity.setButtonName(SeqButtonTypeEnum.DSA_SWITCH_MODE.getValue());
        }
        dsaSequenceEntities.add(dsaSequenceEntity);
        DsaSequenceEntity dsaSequenceEntity2 = this.eK.selectByBtnCode(SeqButtonTypeEnum.DSA_STATUS_READOUT.getValue());
        if (dsaSequenceEntity2 == null) {
            dsaSequenceEntity2 = new DsaSequenceEntity();
            dsaSequenceEntity2.setButtonName(SeqButtonTypeEnum.DSA_STATUS_READOUT.getValue());
        }
        dsaSequenceEntities.add(dsaSequenceEntity2);
        DsaSequenceEntity dsaSequenceEntity3 = this.eK.selectByBtnCode(SeqButtonTypeEnum.DSA_SOFTWARE_DOWNLOAD.getValue());
        if (dsaSequenceEntity3 == null) {
            dsaSequenceEntity3 = new DsaSequenceEntity();
            dsaSequenceEntity3.setButtonName(SeqButtonTypeEnum.DSA_SOFTWARE_DOWNLOAD.getValue());
        }
        dsaSequenceEntities.add(dsaSequenceEntity3);
        String offlineSeqInfo = this.cloud.e(dsaSequenceEntities, username);
        List<DsaSequenceEntity> dsaSequences = ObjectMapperUtils.jsonStr2List(offlineSeqInfo, DsaSequenceEntity.class);
        for (DsaSequenceEntity entity : dsaSequences) {
            entity.setUpdateBy(username);
            entity.setUpdateTime(new Date());
            if (entity.getId() == null) {
                entity.setCreateBy(username);
                entity.setCreateTime(new Date());
                this.eK.create(entity);
            } else {
                this.eK.update(entity);
            }
        }
    }
}
