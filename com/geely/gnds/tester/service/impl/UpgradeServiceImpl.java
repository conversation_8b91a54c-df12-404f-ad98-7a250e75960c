package com.geely.gnds.tester.service.impl;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.component.SoftwareDownloadSeq_4;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.ScheduleConstants;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.cache.Download;
import com.geely.gnds.tester.cache.DownloadManager;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.cache.TenantTransformCache;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DownloadDto;
import com.geely.gnds.tester.dto.DownloadProgressDto;
import com.geely.gnds.tester.dto.EcuDto;
import com.geely.gnds.tester.dto.EcuPinCodeDto;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.ReleaseNoteDTO;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.dto.SoftwareDto;
import com.geely.gnds.tester.dto.SysTenantDto;
import com.geely.gnds.tester.dto.UpgradeDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.SoftwareTypeEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.enums.TesterSeqCodeEnum;
import com.geely.gnds.tester.enums.TesterSoftwareTypeEnum;
import com.geely.gnds.tester.security.CloudApiRsaUtils;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.LoginService;
import com.geely.gnds.tester.service.SoftwareService;
import com.geely.gnds.tester.service.UpgradeService;
import com.geely.gnds.tester.service.VersionService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: UpgradeServiceImpl.class */
public class UpgradeServiceImpl implements UpgradeService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private FileCache fileCache;

    @Autowired
    private SoftwareService jr;

    @Autowired
    private VersionService jQ;

    @Autowired
    private LoginService hs;

    @Value("${spring.profiles.active}")
    private String environment;

    @Value("${tester.tenantName}")
    private String tenantName;
    private static final Logger log = LoggerFactory.getLogger(UpgradeServiceImpl.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @Override // com.geely.gnds.tester.service.UpgradeService
    public UpgradeDto query(String vin, SoftwareTypeEnum bizType, String bssId) throws Exception {
        List<EcuDto> list = new ArrayList<>();
        String upgradeSoftwareList = this.cloud.j(vin, bizType.getValue(), bssId);
        log.info("upgradeService query cloud.getUpgradeSoftwareList");
        UpgradeDto upgradeDto = (UpgradeDto) ObjectMapperUtils.jsonStr2Clazz(upgradeSoftwareList, UpgradeDto.class);
        if (upgradeDto != null) {
            String displayVersion = upgradeDto.getDisplayVersion();
            if (StringUtils.isNotBlank(displayVersion)) {
                this.manager.setGlobal(GlobalVariableEnum.lZ, displayVersion, vin);
            }
            String cloudBssId = upgradeDto.getBssId();
            if (StringUtils.isNotBlank(cloudBssId)) {
                this.manager.setGlobal(GlobalVariableEnum.ma, cloudBssId, vin);
            }
        }
        log.info("upgradeService query cloud.getUpgradeSoftwareList");
        List<EcuDto> ecuDtoList = upgradeDto.getEcuInstallationInstructions();
        if (!CollectionUtils.isEmpty(ecuDtoList)) {
            if (ScheduleConstants.MISFIRE_FIRE_AND_PROCEED.equals(upgradeDto.getVehicleStatus())) {
            }
            log.info("upgradeService query for(int i=0;i<ecuDtoList.size();i++)开始");
            for (int i = 0; i < ecuDtoList.size(); i++) {
                float time = 0.0f;
                EcuDto ecuDto = ecuDtoList.get(i);
                List<SoftwareDto> softwares = ecuDto.getSoftwares();
                ecuDto.getHardwareStatus();
                long vbfSize = 0;
                if (!CollectionUtils.isEmpty(softwares)) {
                    log.info("upgradeService query for(SoftwareDto softwareDto:softwares)开始");
                    for (SoftwareDto softwareDto : softwares) {
                        Long size = softwareDto.getVbfSize();
                        if (!ObjectUtils.isEmpty(size)) {
                            softwareDto.setVbfSize(size);
                            vbfSize += size.longValue();
                            float downloadTime = c(size.longValue());
                            time += downloadTime;
                            softwareDto.setDownloadTime(downloadTime);
                            softwareDto.setVbfName(softwareDto.getSoftwareNumber() + softwareDto.getSoftwareVersion() + ConstantEnum.VBF);
                        }
                    }
                    log.info("upgradeService query for(SoftwareDto softwareDto:softwares)结束");
                }
                ecuDto.setVbfSize(Long.valueOf(vbfSize));
                ecuDto.setDownloadTime(time);
                list.add(ecuDto);
            }
            log.info("upgradeService query for(int i=0;i<ecuDtoList.size();i++)结束");
        }
        upgradeDto.setEcuInstallationInstructions(list);
        return upgradeDto;
    }

    private float c(long size) {
        float downloadTimeFloat = (size / 1024) / 30000.0f;
        float downloadTime = Math.round(downloadTimeFloat * 10.0f) / 10.0f;
        return downloadTime;
    }

    private float d(long size) {
        float downloadTimeFloat = (size / 1024) / 180000.0f;
        float downloadTime = Math.round(downloadTimeFloat * 10.0f) / 10.0f;
        return downloadTime;
    }

    @Override // com.geely.gnds.tester.service.UpgradeService
    public void download(DownloadDto downloadDto) throws Exception {
        int i;
        List<String> urls = new ArrayList<>();
        long totleSize = 0;
        long downloadSize = 0;
        String id = downloadDto.getId();
        List<EcuDto> ecuDtoList = downloadDto.getEcuDtoList();
        Map<String, String> checksumMap = new HashMap<>();
        log.info("vbf加解密--软件列表【{}】", ecuDtoList);
        Boolean encrypted = downloadDto.getEncrypted();
        List<String> tempUrl = new ArrayList<>();
        for (EcuDto ecuDto : ecuDtoList) {
            for (SoftwareDto softwareDto : ecuDto.getSoftwares()) {
                String url = softwareDto.getUrl();
                String softwareType = softwareDto.getSoftwareType();
                if (StringUtils.isNotBlank(url)) {
                    if (ObjectUtils.isNotEmpty(encrypted) && encrypted.booleanValue()) {
                        url = CloudApiRsaUtils.cr(url);
                        softwareDto.setUrl(url);
                    }
                    String checksum = softwareDto.getChecksum();
                    if (TesterSoftwareTypeEnum.cq(softwareType)) {
                        i = this.fileCache.j(url, downloadDto.getVin());
                    } else {
                        i = this.fileCache.ab(url);
                    }
                    totleSize += softwareDto.getVbfSize().longValue();
                    if (i == 0) {
                        urls.add(url);
                        if (TesterSoftwareTypeEnum.cq(softwareType)) {
                            tempUrl.add(url);
                        }
                        checksumMap.put(url, checksum);
                    } else if (i == 2) {
                        downloadSize += this.fileCache.af(url);
                        urls.add(url);
                        checksumMap.put(url, checksum);
                        if (TesterSoftwareTypeEnum.cq(softwareType)) {
                            tempUrl.add(url);
                        }
                    } else {
                        downloadSize += softwareDto.getVbfSize().longValue();
                    }
                }
            }
        }
        DownloadManager instance = DownloadManager.getInstance();
        Download download = instance.Y(id);
        download.setSupportDownload(true);
        if (totleSize <= downloadSize && CollectionUtils.isEmpty(urls)) {
            download.setDownloadTime(Float.valueOf(0.0f));
            download.setProgress(Float.valueOf(100.0f));
        } else {
            download.setDownloadTime(Float.valueOf(d(totleSize - downloadSize) * 60.0f));
            float process = (downloadSize / totleSize) * 100.0f;
            if (process >= 99.0f) {
                process = 99.0f;
            }
            download.setProgress(Float.valueOf(process));
            download.setSupportDownload(true);
            this.fileCache.a(urls, download, totleSize, downloadSize, ecuDtoList, checksumMap, tempUrl, downloadDto.getVin());
        }
        long start = System.currentTimeMillis();
        try {
            this.jr.checkSoftwareRecord(downloadDto.getEcuDtoList());
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00091), e);
        }
        long end = System.currentTimeMillis();
        log.info("解析VBF入库时间：" + (end - start) + "ms");
    }

    @Override // com.geely.gnds.tester.service.UpgradeService
    public void upgradeTester(DownloadDto downloadDto) throws Exception {
        String id = downloadDto.getId();
        DownloadManager instance = DownloadManager.getInstance();
        Download download = instance.Y(id);
        ReleaseNoteDTO releaseNoteDTO = downloadDto.getReleaseNoteDTO();
        String jarUrl = releaseNoteDTO.getJarUrl();
        String sqlUrl = releaseNoteDTO.getSqlUrl();
        long totalSize = releaseNoteDTO.getJarSize().longValue() * 1024;
        long downloadSize = this.fileCache.getDownloadJarSize();
        if (totalSize == 0) {
            download.setDownloadTime(Float.valueOf(0.0f));
            download.setDownloadTime(Float.valueOf(0.0f));
            download.setProgress(Float.valueOf(100.0f));
        } else {
            download.setDownloadTime(Float.valueOf(0.0f * 60.0f));
            float process = downloadSize / totalSize;
            download.setProgress(Float.valueOf(process));
            download.setSupportDownload(true);
            SysTenantDto tenantCodeByCode = this.hs.getTenantCodeByCode();
            if (ObjectUtils.isNotEmpty(tenantCodeByCode)) {
                String transformName = TenantTransformCache.al(tenantCodeByCode.getShortName().toLowerCase());
                if (null != this.environment && !"".equals(this.environment)) {
                    transformName = transformName + "-" + StringUtils.capitalize(this.environment);
                }
                log.info("transformName={}", transformName);
                jarUrl = jarUrl.replace("{short_name}", transformName);
                log.info("jarUrl={}", jarUrl);
            }
            this.fileCache.a(jarUrl, download, totalSize, downloadSize, releaseNoteDTO);
        }
        if (com.geely.gnds.ruoyi.common.utils.StringUtils.isNotBlank(sqlUrl)) {
            String sql = this.fileCache.ak(sqlUrl);
            File base = new File(new File(ConstantEnum.POINT), "Temp");
            File successFile = new File(base, "downloadApp.success");
            try {
                if (successFile.exists()) {
                    try {
                        this.jQ.doExecuteSql(sql);
                        successFile.delete();
                        download.setExecuteSql(Boolean.TRUE.booleanValue());
                        return;
                    } catch (Exception e) {
                        throw e;
                    }
                }
                return;
            } catch (Throwable th) {
                download.setExecuteSql(Boolean.TRUE.booleanValue());
                throw th;
            }
        }
        download.setExecuteSql(Boolean.TRUE.booleanValue());
    }

    @Override // com.geely.gnds.tester.service.UpgradeService
    public String getDownloadId() {
        double d = ((Math.random() * 9.0d) + 1.0d) * 100000.0d;
        String id = String.valueOf(System.currentTimeMillis()) + ((int) d);
        Download download = new Download();
        DownloadManager instance = DownloadManager.getInstance();
        instance.H();
        instance.a(id, download);
        return id;
    }

    @Override // com.geely.gnds.tester.service.UpgradeService
    public DownloadProgressDto getDownloadProgress(String id) throws Exception {
        DownloadManager instance = DownloadManager.getInstance();
        Download download = instance.Y(id);
        log.info("upgradeService getDownloadProgress CacheManager.getInstance");
        if (download.isNoSpace()) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00191));
        }
        if (download.isDisconnection()) {
            if (ObjectUtils.isNotEmpty(download.getErrorCodeEnum())) {
                throw new Exception(TesterErrorCodeEnum.formatMsg(download.getErrorCodeEnum()));
            }
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00175));
        }
        if (!download.isIntegrity()) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00174));
        }
        if (download.getErrorCodeEnum() != null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(download.getErrorCodeEnum()));
        }
        log.info(download.toString());
        DownloadProgressDto dto = new DownloadProgressDto(download.getDownloadTime(), download.getProgress(), download.isCheckMd5(), download.isExecuteSql(), download.getSpeed());
        log.info(dto.toString());
        return dto;
    }

    @Override // com.geely.gnds.tester.service.UpgradeService
    public Download stopDownload(String id) {
        DownloadManager instance = DownloadManager.getInstance();
        Download download = instance.Y(id);
        download.setSupportDownload(false);
        return download;
    }

    @Override // com.geely.gnds.tester.service.UpgradeService
    public String initFlush(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        String seqCode;
        ScriptHandler handler;
        String vin = initializeUiDto.getVin();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        String args = initializeUiDto.getArgs();
        Boolean encrypted = initializeUiDto.getEncrypted();
        if (ObjectUtils.isNotEmpty(encrypted) && encrypted.booleanValue()) {
            List<EcuDto> ecuDtos = ObjectMapperUtils.jsonStr2List(args, EcuDto.class);
            for (int i = 0; i < ecuDtos.size(); i++) {
                EcuDto ecuDto = ecuDtos.get(i);
                List<EcuPinCodeDto> ecuPinCodes = ecuDto.getEcuPinCodes();
                if (!CollectionUtils.isEmpty(ecuPinCodes)) {
                    for (EcuPinCodeDto ecuPinCode : ecuPinCodes) {
                        String actualValue = ecuPinCode.getActualValue();
                        String defaultValue = ecuPinCode.getDefaultValue();
                        if (StringUtils.isNotEmpty(actualValue)) {
                            ecuPinCode.setActualValue(CloudApiRsaUtils.cr(actualValue));
                        }
                        if (StringUtils.isNotEmpty(defaultValue)) {
                            ecuPinCode.setDefaultValue(CloudApiRsaUtils.cr(defaultValue));
                        }
                    }
                }
                List<SoftwareDto> softwares = ecuDto.getSoftwares();
                if (!CollectionUtils.isEmpty(softwares)) {
                    for (SoftwareDto software : softwares) {
                        software.setUrl(CloudApiRsaUtils.cr(software.getUrl()));
                    }
                }
            }
            args = ObjectMapperUtils.obj2JsonStr(ecuDtos);
        }
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        if (TesterLoginUtils.isOnLine()) {
            seqCode = this.cloud.l(ConstantEnum.SOFT_UPDATE, vin);
            ReloadDto seqBycode = this.cloud.B(seqCode, vin);
            String seq = seqBycode.getSeqContentString();
            String version = seqBycode.getVersion();
            if (StringUtils.isBlank(seq)) {
                Optional.ofNullable(tcpClient).ifPresent(client -> {
                    client.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117).getBytes());
                });
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
            }
            handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, version, user.getUsername(), this.tenantName, user.getUser().getServiceStationCode(), seqBycode.getRequestId());
        } else {
            seqCode = TesterSeqCodeEnum.OFF_LINE_SOFTWARE.getValue();
            String filePath = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath)) {
                Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                    client2.geTxtLogger().write(new Date(), SoftwareDownloadSeq_4.dY, Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119).getBytes());
                });
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
            }
            handler = new ScriptHandler(new File(filePath), vin, args, seqCode, "1.0", "");
        }
        this.manager.setGlobal(ConstantEnum.SOFT_UPDATE, seqCode, vin);
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        log.info("upgradeService initFlush 结束");
        return handler.getInitializeUi();
    }

    @Override // com.geely.gnds.tester.service.UpgradeService
    public String initFlush2(InitializeUiDto initializeUiDto) throws Exception {
        String seqCode;
        ScriptHandler handler;
        String vin = initializeUiDto.getVin();
        String args = initializeUiDto.getArgs();
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        if (TesterLoginUtils.isOnLine()) {
            seqCode = this.cloud.l(ConstantEnum.SOFT_UPDATE, vin);
            ReloadDto seqBycode = this.cloud.B(seqCode, vin);
            String seq = seqBycode.getSeqContentString();
            String version = seqBycode.getVersion();
            String filePath = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath)) {
                handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, version, "test", this.tenantName, seqBycode.getRequestId());
            } else {
                handler = new ScriptHandler(new File(filePath), vin, args, seqCode, initializeParams, version, "", "", "", "");
            }
        } else {
            seqCode = TesterSeqCodeEnum.OFF_LINE_SOFTWARE.getValue();
            String filePath2 = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath2)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
            }
            handler = new ScriptHandler(new File(filePath2), vin, args, seqCode, "1.0", "");
        }
        this.manager.setGlobal(ConstantEnum.SOFT_UPDATE, seqCode, vin);
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        return handler.getInitializeUi();
    }

    private void d(File jarBase) {
        File[] files;
        if (jarBase.exists() && (files = jarBase.listFiles()) != null) {
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile()) {
                    files[i].delete();
                } else if (files[i].isDirectory()) {
                    d(files[i]);
                }
                files[i].delete();
            }
        }
    }
}
