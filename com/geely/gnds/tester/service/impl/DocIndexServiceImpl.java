package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.constant.ScheduleConstants;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.IndexSearchDto;
import com.geely.gnds.tester.dto.TechnicalReqDTO;
import com.geely.gnds.tester.dto.TechnicalResDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.service.DocIndexService;
import com.geely.gnds.tester.service.DtcService;
import com.geely.gnds.tester.service.EcuService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: DocIndexServiceImpl.class */
public class DocIndexServiceImpl implements DocIndexService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private EcuService iU;

    @Autowired
    private DtcService eA;
    private static final Logger log = LoggerFactory.getLogger(DocIndexServiceImpl.class);

    @Override // com.geely.gnds.tester.service.DocIndexService
    public Object getIndexCondition(IndexSearchDto indexSearchDto) throws Exception {
        String type = indexSearchDto.getType();
        String selectOne = indexSearchDto.getSelectOne();
        if ("ecu".equals(type)) {
            List<EcuBroadcastDto> ecuList = this.iU.getEcuList(indexSearchDto.getVin());
            List<Map> ecuMap = new ArrayList<>();
            for (EcuBroadcastDto ecu : ecuList) {
                Map map = new HashMap();
                map.put("ecuName", ecu.getEcuName());
                map.put("diagnosticNumber", ecu.getDiagnosticNumber());
                ecuMap.add(map);
            }
            return ecuMap;
        }
        if ("dtc".equals(type)) {
            String diagnosticNumber = indexSearchDto.getDiagnosticNumber();
            List<DtcInfoDTO> dtcList = this.eA.getDtcList(diagnosticNumber, ScheduleConstants.MISFIRE_DO_NOTHING, indexSearchDto.getVin(), selectOne);
            List<Map> ecuMap2 = new ArrayList<>();
            for (DtcInfoDTO dtc : dtcList) {
                Map map2 = new HashMap();
                map2.put("dtcCode", dtc.getDtcValue());
                map2.put("name", dtc.getName());
                ecuMap2.add(map2);
            }
            return ecuMap2;
        }
        if ("cscType".equals(type) || "cscCom".equals(type)) {
            String selectTwo = indexSearchDto.getSelectTwo();
            String cscList = this.cloud.C(type, selectOne, selectTwo);
            List<String> list = ObjectMapperUtils.jsonStr2List(cscList, String.class);
            return list;
        }
        if ("infoCsc".equals(type)) {
            String selectTwo2 = indexSearchDto.getSelectTwo();
            String cscList2 = this.cloud.C(type, selectOne, selectTwo2);
            List<Map> list2 = ObjectMapperUtils.jsonStr2List(cscList2, Map.class);
            return list2;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.DocIndexService
    public TechnicalResDTO getTechnologyList(TechnicalReqDTO technicalReqDTO) throws Exception {
        String technologyList = this.cloud.a(technicalReqDTO);
        TechnicalResDTO technicalResDTO = (TechnicalResDTO) ObjectMapperUtils.jsonStr2Clazz(technologyList, TechnicalResDTO.class);
        return technicalResDTO;
    }
}
