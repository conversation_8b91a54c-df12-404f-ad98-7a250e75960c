package com.geely.gnds.tester.service.impl;

import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.service.SoftwarePackageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: SoftwarePackageServiceImpl.class */
public class SoftwarePackageServiceImpl implements SoftwarePackageService {

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.SoftwarePackageService
    public String preview(String ossFileName) throws Exception {
        String softwarePackageMediaData = this.cloud.aJ(ossFileName);
        return softwarePackageMediaData;
    }
}
