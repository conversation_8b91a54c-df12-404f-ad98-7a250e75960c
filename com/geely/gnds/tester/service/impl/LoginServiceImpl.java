package com.geely.gnds.tester.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.constant.ScheduleConstants;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.mapper.SysUserMapper;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.cache.MapDBCache;
import com.geely.gnds.tester.cache.MapDBUtil;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.AppSsoConfigDTO;
import com.geely.gnds.tester.dto.CepUserDTO;
import com.geely.gnds.tester.dto.LoginDto;
import com.geely.gnds.tester.dto.LoginInfoDTO;
import com.geely.gnds.tester.dto.SysTenantDto;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.security.CloudApiRsaUtils;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ConnectedService;
import com.geely.gnds.tester.service.LoginService;
import com.geely.gnds.tester.util.AesUtils;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.RsaUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import com.geely.gnds.tester.vo.language.LanguageVo;
import java.io.File;
import java.security.interfaces.RSAPrivateKey;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.WebApplicationContext;

@Service
/* loaded from: LoginServiceImpl.class */
public class LoginServiceImpl implements LoginService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysUserMapper hA;

    @Autowired
    private ConnectedService hr;

    @Autowired
    private TesterConfigDao eC;

    @Autowired
    private FileCache fileCache;

    @Autowired
    private MapDBUtil mapDbUtil;

    @Autowired
    private TesterThread testerThread;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginServiceImpl.class);

    @Override // com.geely.gnds.tester.service.LoginService
    public Boolean loginCloud() throws Exception {
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        String token = this.cloud.getToken();
        if (StringUtils.isNotBlank(token)) {
            servletContext.setAttribute("testerToken", token);
            return true;
        }
        return false;
    }

    private String c(LoginDto loginDto) throws Exception {
        TesterConfigDto config = this.eC.getConfig();
        if (config != null) {
            loginDto.setTesterCode(config.getTesterCode());
        }
        Map<String, Object> initKey = RsaUtils.initKey();
        String publicKey = RsaUtils.getPublicKey(initKey);
        String res = this.cloud.a(loginDto, publicKey);
        return res;
    }

    private String d(LoginDto loginDto) throws Exception {
        TesterConfigDto config = this.eC.getConfig();
        if (config != null) {
            loginDto.setTesterCode(config.getTesterCode());
        }
        Map<String, Object> initKey = RsaUtils.initKey();
        String publicKey = RsaUtils.getPublicKey(initKey);
        String res = this.cloud.b(loginDto, publicKey);
        return res;
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public LoginInfoDTO onlineOauth2Login(LoginDto loginDto) throws Exception {
        return onlineLogin(loginDto, d(loginDto));
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public LoginInfoDTO onlineLogin(LoginDto loginDto) throws Exception {
        return onlineLogin(loginDto, c(loginDto));
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public LoginInfoDTO onlineLogin(LoginDto loginDto, String res) throws Exception {
        Map<String, Object> object;
        String randomKey;
        String randomKeyTimeString;
        TesterConfigDto config = this.eC.getConfig();
        if (config != null) {
            loginDto.setTesterCode(config.getTesterCode());
        }
        RSAPrivateKey privateKey = RsaUtils.getPrivateKey();
        Map<String, Object> jsonStr2Map = ObjectMapperUtils.jsonStr2Map(res);
        String msg = jsonStr2Map.get(AjaxResult.gB) == null ? null : jsonStr2Map.get(AjaxResult.gB).toString();
        String code = jsonStr2Map.get(AjaxResult.gA) == null ? null : jsonStr2Map.get(AjaxResult.gA).toString();
        Map<String, Object> data = jsonStr2Map.get(AjaxResult.gC) == null ? null : (Map) jsonStr2Map.get(AjaxResult.gC);
        LoginInfoDTO result = new LoginInfoDTO();
        String dataMsg = "";
        if (!"0".equals(code)) {
            if ("1".equals(code)) {
                dataMsg = "mobileAuth";
            } else if (ScheduleConstants.MISFIRE_FIRE_AND_PROCEED.equals(code)) {
                dataMsg = "emailAuth";
            } else if ("10010".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102);
            } else if ("10005".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00142);
            } else if ("10004".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102);
            } else if ("10024".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00155);
            } else if ("10027".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00157);
            } else if ("10028".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00158);
            } else if ("10029".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00159);
            } else if ("10039".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00194);
            } else if ("10040".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00192);
            } else if ("10046".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00217);
            } else if ("10055".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00246);
            } else if ("10059".equals(code)) {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00278);
            } else {
                dataMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200) + msg;
            }
        } else {
            ServletContext servletContext = this.webApplicationContext.getServletContext();
            String testerSessionId = "";
            if (servletContext != null && (object = (Map) jsonStr2Map.get(AjaxResult.gC)) != null) {
                if (StringUtils.isEmpty(loginDto.getUsername())) {
                    loginDto.setUsername((String) Optional.ofNullable(object.get("userName")).map(StrUtil::toString).orElse(null));
                }
                Boolean geelySsoLogin = loginDto.getGeelySsoLogin();
                if (ObjectUtils.isNotEmpty(geelySsoLogin) && geelySsoLogin.booleanValue()) {
                    String userName = (String) object.get("userName");
                    if (StringUtils.isNotEmpty(userName)) {
                        loginDto.setUsername(userName);
                    }
                }
                String encryptNewSecret = (String) object.get("s");
                if (StringUtils.isEmpty(encryptNewSecret)) {
                    logout(loginDto.getUsername());
                    throw new Exception(MessageUtils.getMessage("Data abnormality login failure"));
                }
                Object token = object.get(Constants.TOKEN);
                String newSecret = CloudApiRsaUtils.cr(encryptNewSecret);
                TokenManager.setSecretMap(loginDto.getUsername(), newSecret);
                TokenManager.setNameTokenMap(token.toString(), loginDto.getUsername());
                servletContext.setAttribute(loginDto.getUsername().toLowerCase() + "-token", object.get(Constants.TOKEN));
                String testerCode = (String) object.get("testerCode");
                testerSessionId = object.get("testerSessionId") == null ? "" : String.valueOf(object.get("testerSessionId"));
                TesterConfigDto configDto = new TesterConfigDto();
                configDto.setTesterCode(testerCode);
                File vbfBase = new File(new File(ConstantEnum.POINT), "vbfs");
                if (!vbfBase.exists()) {
                    vbfBase.mkdirs();
                }
                String canonicalPath = vbfBase.getCanonicalPath();
                if (config == null) {
                    configDto.setCreator(loginDto.getUsername());
                    configDto.setCreateTime(new Date());
                    configDto.setUpdater(loginDto.getUsername());
                    configDto.setUpdateTime(new Date());
                    configDto.setVbfPath(canonicalPath);
                    this.fileCache.setVbfBase(new File(canonicalPath));
                    configDto.setVbfDefaultSize(40);
                    this.eC.insert(configDto);
                } else {
                    String vbfPath = config.getVbfPath();
                    if (StringUtils.isBlank(vbfPath)) {
                        configDto.setVbfPath(canonicalPath);
                        this.fileCache.setVbfBase(new File(canonicalPath));
                    } else {
                        this.fileCache.setVbfBase(new File(vbfPath));
                    }
                    configDto.setVbfDefaultSize(config.getVbfDefaultSize());
                    configDto.setId(config.getId());
                    configDto.setUpdater(loginDto.getUsername());
                    configDto.setUpdateTime(new Date());
                    this.eC.update(configDto);
                }
                AppConfig.setTesterCode(configDto.getTesterCode());
                try {
                    randomKey = (String) object.get("randomKey");
                    randomKeyTimeString = (String) object.get("randomKeyTime");
                } catch (Exception e) {
                    LOGGER.error("密钥获取失败", e);
                }
                if (StringUtils.isBlank(randomKey) || StringUtils.isBlank(randomKeyTimeString)) {
                    throw new Exception(MessageUtils.getMessage("Key acquisition failed, please log in again"));
                }
                Long.valueOf(randomKeyTimeString).longValue();
                String key = RsaUtils.decryptData(privateKey, randomKey);
                AesUtils.setRandomKey(key);
                try {
                    Object uploadRate = object.get("uploadRate");
                    if (uploadRate != null) {
                        OssUtils.setUserLimit(Integer.parseInt(uploadRate.toString()));
                    }
                } catch (Exception e2) {
                    LOGGER.error("密钥获取失败", e2);
                }
            }
            if (StringUtils.isNotBlank(testerSessionId)) {
                servletContext.setAttribute("testerSessionId", testerSessionId);
                dataMsg = testerSessionId;
            }
        }
        result.setMsg(dataMsg);
        if (ObjectUtils.isNotEmpty(data)) {
            if (data.containsKey("userName")) {
                result.setUserName((String) data.get("userName"));
            }
            if (data.containsKey("thirdPartyCode")) {
                result.setThirdPartyCode((String) data.get("thirdPartyCode"));
            }
        }
        return result;
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public String getPublicKey(HttpServletRequest request) throws Exception {
        Map<String, Object> initKey = RsaUtils.initKey();
        String publicKey = RsaUtils.getPublicKey(initKey);
        HttpSession session = request.getSession();
        session.setAttribute(ConstantEnum.PRIVATEKEY_STR, RsaUtils.getPrivateKey());
        return publicKey;
    }

    @Override // com.geely.gnds.tester.service.LoginService
    @Transactional(rollbackFor = {Exception.class})
    public void updateUserStatus(SysUser sysUser) throws Exception {
        SysUser user;
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        if (loginUser == null || (user = loginUser.getUser()) == null) {
            return;
        }
        TesterConfigDto config = this.eC.getConfig();
        SysUser update = this.hA.selectUserById(user.getUserId());
        String vbfPath = sysUser.getVbfPath();
        if (StringUtils.isNotBlank(vbfPath)) {
            File file = new File(vbfPath);
            if (!file.exists()) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00193));
            }
            config.setVbfPath(sysUser.getVbfPath());
            this.fileCache.setVbfBase(file);
        }
        int vbfDefaultSize = sysUser.getVbfDefaultSize();
        if (vbfDefaultSize < 40) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00186));
        }
        config.setVbfDefaultSize(sysUser.getVbfDefaultSize());
        this.eC.update(config);
        update.setUpdateTime(System.currentTimeMillis() + "");
        update.setSeqStatus(sysUser.getSeqStatus());
        update.setSoftwareStatus(sysUser.getSoftwareStatus());
        update.setBrandSelect(sysUser.getBrandSelect());
        update.setVinDecode(sysUser.getVinDecode());
        update.setStartTab(sysUser.getStartTab());
        update.setRecentVehicles(sysUser.getRecentVehicles());
        update.setReadNum(sysUser.getReadNum());
        update.setTimeSelect(sysUser.getTimeSelect());
        this.hA.updateUser(update);
        loginUser.setUser(update);
        this.tokenService.setLoginUser(loginUser);
        if (TesterLoginUtils.isOnLine()) {
            try {
                this.cloud.k(update);
            } catch (UnAuthException e) {
                throw e;
            } catch (Exception e2) {
                LOGGER.error("客户端用户配置同步到服务端请求失败", e2);
            }
        }
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public void logout() {
        String username = "";
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        if (Objects.isNull(loginUser)) {
            loginUser = ThreadLocalUtils.CURRENT_USER.get();
            if (Objects.isNull(loginUser)) {
                loginUser = ThreadLocalUtils.CURRENT_USER.get();
            }
        }
        if (ObjectUtils.isNotEmpty(loginUser)) {
            username = loginUser.getUsername();
        }
        logout(username);
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public void logout(String username) {
        LOGGER.info("logout方法,用户名={}", username);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        TesterConfigDto config = this.eC.getConfig();
        try {
            Set<String> vinList = this.manager.getVehicles(username);
            if (!CollectionUtils.isEmpty(vinList)) {
                Iterator<String> iterator = vinList.iterator();
                if (iterator.hasNext()) {
                    this.hr.disconnect(username, iterator.next());
                }
            }
        } catch (Exception e) {
            LOGGER.error("logout方法断开车辆异常,用户名{},e{}", username, e);
        }
        try {
            if (TesterLoginUtils.isOnLine()) {
                try {
                    this.cloud.r(config.getTesterCode(), username);
                    ServletContext servletContext = this.webApplicationContext.getServletContext();
                    servletContext.removeAttribute(username.toLowerCase() + "-token");
                } catch (Exception e2) {
                    LOGGER.error("logout方法onlineLogout异常,e={}", username, e2);
                    ServletContext servletContext2 = this.webApplicationContext.getServletContext();
                    servletContext2.removeAttribute(username.toLowerCase() + "-token");
                }
            }
            TesterLoginUtils.removeLoginUsers(username);
            if (ObjectUtils.isNotEmpty(loginUser)) {
                this.tokenService.I(loginUser.getToken());
            }
        } catch (Throwable th) {
            ServletContext servletContext3 = this.webApplicationContext.getServletContext();
            servletContext3.removeAttribute(username.toLowerCase() + "-token");
            throw th;
        }
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public String sendMobileCode(String username, String password, int sendCodeType, Boolean cepLogin, Boolean geelySsoLogin, String thirdPartyCode) throws Exception {
        String res = this.cloud.sendMobileCode(username, password, sendCodeType, cepLogin, geelySsoLogin, thirdPartyCode);
        Map<String, Object> jsonStr2Map = ObjectMapperUtils.jsonStr2Map(res);
        String msg = jsonStr2Map.get(AjaxResult.gB) == null ? null : jsonStr2Map.get(AjaxResult.gB).toString();
        String code = jsonStr2Map.get(AjaxResult.gA) == null ? null : jsonStr2Map.get(AjaxResult.gA).toString();
        if ("0".equals(code) || "10023".equals(code)) {
            return "";
        }
        if ("10010".equals(code)) {
            return TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102);
        }
        if ("10005".equals(code)) {
            return TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00142);
        }
        if ("10004".equals(code)) {
            return TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102);
        }
        if ("10022".equals(code)) {
            return TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00154);
        }
        return TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200) + msg;
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public SysTenantDto getTenantCodeByCode() throws Exception {
        String resultStr = this.cloud.getTenantByCode();
        Map<String, Object> jsonStr2Map = ObjectMapperUtils.jsonStr2Map(resultStr);
        String string = jsonStr2Map.get(AjaxResult.gB) == null ? null : jsonStr2Map.get(AjaxResult.gB).toString();
        String code = jsonStr2Map.get(AjaxResult.gA) == null ? null : jsonStr2Map.get(AjaxResult.gA).toString();
        Object data = jsonStr2Map.get(AjaxResult.gC) == null ? null : jsonStr2Map.get(AjaxResult.gC);
        Optional.ofNullable(data).orElseThrow(() -> {
            return new CustomException("获取租户信息异常", Integer.valueOf(HttpStatus.UNAUTHORIZED));
        });
        if (!"0".equals(code)) {
            LOGGER.error("调用云端根据租户编码获取租户信息接口失败");
            return null;
        }
        Map<String, Object> tenantMap = (Map) data;
        SysTenantDto dto = new SysTenantDto();
        dto.setId(Long.valueOf(String.valueOf(tenantMap.get("id"))));
        dto.setTenantCode(Long.valueOf(String.valueOf(tenantMap.get(ConstantEnum.TENANTCODE_STR))));
        dto.setTenantName((String) tenantMap.get("tenantName"));
        dto.setShortName((String) tenantMap.get("shortName"));
        if (tenantMap.containsKey("securitySoftwarePath")) {
            dto.setSecuritySoftwarePath((String) tenantMap.get("securitySoftwarePath"));
        }
        return dto;
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public void heartBeatStop() {
        String username = TesterLoginUtils.getLoginUserName();
        LOGGER.info("心跳停止：vinList：{}", username);
        Set<String> vinList = this.manager.getVehicles(username);
        try {
            TesterConfigDto config = this.eC.getConfig();
            if (null != config) {
                this.cloud.r(config.getTesterCode(), username);
            }
            ServletContext servletContext = this.webApplicationContext.getServletContext();
            servletContext.removeAttribute(username.toLowerCase() + "-token");
        } catch (Exception e) {
            GlobalException.a(e, MessageUtils.getMessage("Online logout and login exception"));
        }
        LOGGER.info("心跳停止：vinList：{}", vinList);
        if (CollectionUtils.isEmpty(vinList)) {
            return;
        }
        SingletonManager instance = SingletonManager.getInstance();
        try {
            Iterator<String> iterator = vinList.iterator();
            if (iterator.hasNext()) {
                try {
                    String vin = iterator.next();
                    VehicleDto vehicle = instance.getVehicle(vin);
                    LOGGER.info("心跳停止：vehicle：{}", vehicle);
                    if (vehicle != null && !vehicle.isVbfswdl()) {
                        LOGGER.info("心跳停止：没有在刷写");
                        this.hr.disconnect(username, vin);
                    }
                } catch (Exception e2) {
                    LOGGER.error("断开连接异常", e2);
                }
            }
        } catch (Exception e3) {
            LOGGER.error("心跳停止t方法异常,用户名{},e{}", username, e3);
        }
        TesterLoginUtils.removeLoginUsers(username);
        LoginUser loginUser = this.tokenService.getLoginUser();
        if (loginUser != null) {
            this.tokenService.I(loginUser.getToken());
        }
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public AppSsoConfigDTO getAppSsoConfig(HttpServletRequest request) throws Exception {
        String ssoConfig = this.cloud.getAppSsoConfig();
        AppSsoConfigDTO appSsoConfigDTO = (AppSsoConfigDTO) ObjectMapperUtils.jsonStr2Clazz(ssoConfig, AppSsoConfigDTO.class);
        double d = ((Math.random() * 9.0d) + 1.0d) * 100000.0d;
        String state = String.valueOf(System.currentTimeMillis()) + ((int) d);
        appSsoConfigDTO.setState(state);
        TesterLoginUtils.setStateFinal(state);
        return appSsoConfigDTO;
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public CepUserDTO getCepLoginName(String code, String redirectUri) throws Exception {
        String login = this.cloud.G(code, redirectUri);
        CepUserDTO cepUserDTO = (CepUserDTO) ObjectMapperUtils.jsonStr2Clazz(login, CepUserDTO.class);
        return cepUserDTO;
    }

    @Override // com.geely.gnds.tester.service.LoginService
    public List<LanguageVo> getUiLanguageResource(String systemCode) throws Exception {
        String jsonStr = this.mapDbUtil.a(MapDBCache.UI_LANGUAGE);
        List<LanguageVo> list = (List) JSON.parseObject(jsonStr, new TypeReference<List<LanguageVo>>() { // from class: com.geely.gnds.tester.service.impl.LoginServiceImpl.1
        }, new Feature[0]);
        return (List) list.stream().filter(languageVo -> {
            return StrUtil.equalsIgnoreCase(languageVo.getSystemCode(), systemCode);
        }).collect(Collectors.toList());
    }
}
