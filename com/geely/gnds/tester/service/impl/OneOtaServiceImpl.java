package com.geely.gnds.tester.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.OneOtaDTO;
import com.geely.gnds.tester.dto.SimpleResultDTO;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.OneOtaService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: OneOtaServiceImpl.class */
public class OneOtaServiceImpl implements OneOtaService {
    private static final Logger log = LoggerFactory.getLogger(EcuServiceImpl.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.OneOtaService
    public List<OneOtaDTO> getOneOtaList(String vin) throws Exception {
        List<OneOtaDTO> list = new ArrayList<>();
        String oneOtaList = this.cloud.aD(vin);
        JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(oneOtaList, JsonNode.class);
        if (readValue.isArray()) {
            readValue.forEach(obj -> {
                OneOtaDTO remoteTaskDTO = (OneOtaDTO) ObjectMapperUtils.getInstance().convertValue(obj, OneOtaDTO.class);
                list.add(remoteTaskDTO);
            });
        }
        return list;
    }

    @Override // com.geely.gnds.tester.service.OneOtaService
    public String installation(Long id) throws Exception {
        return this.cloud.installation(id);
    }

    @Override // com.geely.gnds.tester.service.OneOtaService
    public String withdrawn(String vin) throws Exception {
        return this.cloud.withdrawn(vin);
    }

    @Override // com.geely.gnds.tester.service.OneOtaService
    public SimpleResultDTO remoteUpgrade(String vin) throws Exception {
        return this.cloud.remoteUpgrade(vin);
    }
}
