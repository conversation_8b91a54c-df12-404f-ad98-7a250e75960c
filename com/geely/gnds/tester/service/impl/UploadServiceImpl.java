package com.geely.gnds.tester.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.dao.TesterSequenceDao;
import com.geely.gnds.tester.dao.TesterSoftwareDao;
import com.geely.gnds.tester.entity.TesterSequenceEntity;
import com.geely.gnds.tester.entity.TesterSoftwareEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.SequenceStatusEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.service.UploadService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.util.VbfParseUtils;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
/* loaded from: UploadServiceImpl.class */
public class UploadServiceImpl implements UploadService {

    @Autowired
    private TesterSequenceDao pq;

    @Autowired
    private TesterSoftwareDao pt;

    @Autowired
    private FileCache fileCache;

    @Autowired
    private VbfParseUtils vbfParseUtils;

    @Autowired
    private TokenService tokenService;
    private static final Logger log = LoggerFactory.getLogger(UploadServiceImpl.class);

    @Override // com.geely.gnds.tester.service.UploadService
    public Result uploadSeq(MultipartFile file) {
        Result<String> result = new Result<>();
        try {
            String fileName = file.getOriginalFilename();
            String extName = fileName.substring(fileName.lastIndexOf(ConstantEnum.POINT));
            if (!ConstantEnum.JSON.equals(extName)) {
                result.error(HttpStatus.ERROR, "离线模式--上传诊断序列文件格式错误");
                return result;
            }
            Result<TesterSequenceEntity> analyseJsonResult = c(file.getInputStream());
            if (analyseJsonResult.getCode() == 200) {
                TesterSequenceEntity entity = analyseJsonResult.getData();
                LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
                entity.setCreateBy(loginUser.getUsername());
                entity.setCreateTime(new Date());
                entity.setUpdateBy(loginUser.getUsername());
                entity.setUpdateTime(new Date());
                TesterSequenceEntity sequenceEntity = this.pq.selectBySeqCode(entity.getSequenceId());
                if (null == sequenceEntity) {
                    this.pq.create(entity);
                } else {
                    entity.setId(sequenceEntity.getId());
                    this.pq.update(entity);
                }
                String checkSeqExist = this.fileCache.ag(entity.getSequenceId());
                if (StringUtils.isEmpty(checkSeqExist)) {
                    boolean flag = this.fileCache.k(entity.getSequenceId(), entity.getSeqContent());
                    if (!flag) {
                        result.error(HttpStatus.ERROR, "离线模式--上传诊断序列保存失败");
                        return result;
                    }
                }
                return result;
            }
            return analyseJsonResult;
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00092), e);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00092));
            return result;
        }
    }

    @Override // com.geely.gnds.tester.service.UploadService
    public Result uploadVbf(MultipartFile file) {
        String fileName;
        String extName;
        Result<String> result = new Result<>();
        try {
            fileName = org.springframework.util.StringUtils.cleanPath(file.getOriginalFilename());
            extName = fileName.substring(fileName.lastIndexOf(ConstantEnum.POINT));
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00093), e);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00093));
        }
        if (!ConstantEnum.VBF.equals(extName)) {
            result.error(HttpStatus.ERROR, "文件格式错误");
            return result;
        }
        if (this.fileCache.ab(fileName) == 1) {
            this.fileCache.deleteVbf(fileName);
        }
        boolean flag = this.fileCache.b(file);
        if (!flag) {
            result.error(HttpStatus.ERROR, "vbf文件上传保存失败！");
            return result;
        }
        TesterSoftwareEntity softwareEntity = cA(fileName);
        if (null == softwareEntity) {
            result.error(HttpStatus.ERROR, "vbf文件上传失败，vbf解析失败！");
            return result;
        }
        TesterSoftwareEntity entity = this.pt.selectByFileName(softwareEntity.getFileName());
        if (null == entity) {
            this.pt.create(softwareEntity);
        } else {
            softwareEntity.setId(entity.getId());
            this.pt.update(softwareEntity);
        }
        return result;
    }

    private TesterSoftwareEntity cA(String vbfFileName) throws NumberFormatException {
        String userName;
        try {
            LoginUser loginUser = this.tokenService.getLoginUser();
            if (loginUser != null) {
                userName = loginUser.getUsername();
            } else {
                userName = "admin";
            }
            TesterSoftwareEntity testerSoftwareEntity = new TesterSoftwareEntity();
            Map parsePlus = this.vbfParseUtils.parsePlus(vbfFileName);
            Object header = parsePlus.get("VBF_Header");
            if (header != null) {
                Map<String, Object> vbfHeader = (Map) header;
                Object value = vbfHeader.get("Data_Value");
                if (value != null) {
                    Map<String, Object> dataValue = (Map) value;
                    String softwareVersion = String.valueOf(dataValue.get("sw_version"));
                    String address = String.valueOf(dataValue.get("ecu_address"));
                    String softwareFormat = String.valueOf(dataValue.get("data_format_identifier"));
                    String softwareNumber = String.valueOf(dataValue.get("sw_part_number"));
                    String softwareType = String.valueOf(dataValue.get("sw_part_type"));
                    Long vbfSize = null;
                    Object object = dataValue.get("sw_size");
                    if (object != null && org.apache.commons.lang3.StringUtils.isNoneBlank(new CharSequence[]{object.toString()})) {
                        vbfSize = Long.valueOf(object.toString());
                    }
                    testerSoftwareEntity.setVbfSize(vbfSize);
                    testerSoftwareEntity.setAddress(address);
                    testerSoftwareEntity.setFileName(vbfFileName);
                    testerSoftwareEntity.setSoftwareFormat(softwareFormat);
                    testerSoftwareEntity.setSoftwareNumber(softwareNumber);
                    testerSoftwareEntity.setSoftwareType(softwareType);
                    testerSoftwareEntity.setSoftwareVersion(softwareVersion);
                    testerSoftwareEntity.setCreateBy(userName);
                    testerSoftwareEntity.setCreateTime(new Date());
                    testerSoftwareEntity.setUpdateBy(userName);
                    testerSoftwareEntity.setUpdateTime(new Date());
                }
            }
            return testerSoftwareEntity;
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00094), e);
            return null;
        }
    }

    private Result<TesterSequenceEntity> c(InputStream inputStream) throws IOException {
        Result<TesterSequenceEntity> result = new Result<>();
        try {
            byte[] b = new byte[51200];
            StringBuilder stringBuilder = new StringBuilder();
            while (true) {
                int len = inputStream.read(b);
                if (len != -1) {
                    stringBuilder.append(new String(b, 0, len));
                } else {
                    inputStream.close();
                    JSONObject jsonObject = JSONObject.parseObject(stringBuilder.toString());
                    TesterSequenceEntity entity = new TesterSequenceEntity();
                    String name = jsonObject.getString("Name");
                    String griNumber = jsonObject.getString("GRINumber");
                    String precondition = jsonObject.getString("Precondition");
                    entity.setName(name);
                    entity.setSequenceId(griNumber);
                    entity.setApplicability(precondition);
                    entity.setSeqContent(stringBuilder.toString());
                    entity.setStatus(SequenceStatusEnum.DRAFT.ap());
                    log.info("脚本文件解析成功！");
                    return result.ok(entity);
                }
            }
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00095), e);
            result.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00095));
            return result;
        }
    }

    private String d(List<String> formatErrorList, List<String> cacheErrorList) {
        StringBuilder stringBuilder = new StringBuilder();
        if (formatErrorList.size() > 0) {
            stringBuilder.append("文件名称为：");
            for (int i = 0; i < formatErrorList.size(); i++) {
                if (i == 0) {
                    stringBuilder.append("'" + formatErrorList.get(i) + "'");
                } else {
                    stringBuilder.append("，'" + formatErrorList.get(i) + "'");
                }
            }
            stringBuilder.append("的文件格式错误。");
        }
        if (cacheErrorList.size() > 0) {
            stringBuilder.append("文件名称为：");
            for (int i2 = 0; i2 < cacheErrorList.size(); i2++) {
                if (i2 == 0) {
                    stringBuilder.append("'" + cacheErrorList.get(i2) + "'");
                } else {
                    stringBuilder.append("，'" + cacheErrorList.get(i2) + "'");
                }
            }
            stringBuilder.append("的文件上传错误。");
        }
        return stringBuilder.toString();
    }
}
