package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.RemoteLogCollectionDTO;
import com.geely.gnds.tester.dto.RemoteLogConfigDTO;
import com.geely.gnds.tester.dto.RemoteLogUploadProgressDTO;
import com.geely.gnds.tester.service.RemoteLogService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: RemoteLogServiceImpl.class */
public class RemoteLogServiceImpl implements RemoteLogService {

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.RemoteLogService
    public RemoteLogConfigDTO query(String vin) throws Exception {
        String content = this.cloud.aL(vin);
        if (StringUtils.isNotBlank(content)) {
            RemoteLogConfigDTO dto = (RemoteLogConfigDTO) ObjectMapperUtils.jsonStr2Clazz(content, RemoteLogConfigDTO.class);
            return dto;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteLogService
    public RemoteLogCollectionDTO remoteLogDetail(String taskId) throws Exception {
        String content = this.cloud.aM(taskId);
        if (StringUtils.isNotBlank(content)) {
            RemoteLogCollectionDTO dto = (RemoteLogCollectionDTO) ObjectMapperUtils.jsonStr2Clazz(content, RemoteLogCollectionDTO.class);
            return dto;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteLogService
    public RemoteLogCollectionDTO remoteLogSetLevel(String vin, Integer updateIhuLogLevel, Integer oldUpdateIhuLogLevel, Boolean isIhuOperate, String model) throws Exception {
        String content = this.cloud.a(vin, updateIhuLogLevel, oldUpdateIhuLogLevel, isIhuOperate, model);
        if (StringUtils.isNotBlank(content)) {
            RemoteLogCollectionDTO dto = (RemoteLogCollectionDTO) ObjectMapperUtils.jsonStr2Clazz(content, RemoteLogCollectionDTO.class);
            return dto;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteLogService
    public RemoteLogCollectionDTO remoteLogSetLogConfig(String vin, Integer updateIhuLogLevel, Integer oldUpdateIhuLogLevel, Boolean isIhuOperate, String model, String startTimeIhu, String endTimeIhu) throws Exception {
        String content = this.cloud.a(vin, updateIhuLogLevel, oldUpdateIhuLogLevel, isIhuOperate, model, startTimeIhu, endTimeIhu);
        if (StringUtils.isNotBlank(content)) {
            RemoteLogCollectionDTO dto = (RemoteLogCollectionDTO) ObjectMapperUtils.jsonStr2Clazz(content, RemoteLogCollectionDTO.class);
            return dto;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteLogService
    public RemoteLogCollectionDTO remoteLogSetTime(String taskId, String startTimeIhu, String endTimeIhu) throws Exception {
        String content = this.cloud.B(taskId, startTimeIhu, endTimeIhu);
        if (StringUtils.isNotBlank(content)) {
            RemoteLogCollectionDTO dto = (RemoteLogCollectionDTO) ObjectMapperUtils.jsonStr2Clazz(content, RemoteLogCollectionDTO.class);
            return dto;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteLogService
    public RemoteLogCollectionDTO remoteLogGetOssPath(String taskId) throws Exception {
        String content = this.cloud.aN(taskId);
        if (StringUtils.isNotBlank(content)) {
            RemoteLogCollectionDTO dto = (RemoteLogCollectionDTO) ObjectMapperUtils.jsonStr2Clazz(content, RemoteLogCollectionDTO.class);
            return dto;
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.RemoteLogService
    public Boolean remoteLogCancel(String taskId) throws Exception {
        String content = this.cloud.aO(taskId);
        if (StringUtils.isNotBlank(content)) {
            return (Boolean) ObjectMapperUtils.jsonStr2Clazz(content, Boolean.class);
        }
        return false;
    }

    @Override // com.geely.gnds.tester.service.RemoteLogService
    public RemoteLogUploadProgressDTO getVehicleUploadProgress(String vin, String ecuName, String taskId) throws Exception {
        String content = this.cloud.D(vin, ecuName, taskId);
        if (StringUtils.isNotBlank(content)) {
            return (RemoteLogUploadProgressDTO) ObjectMapperUtils.jsonStr2Clazz(content, RemoteLogUploadProgressDTO.class);
        }
        return null;
    }
}
