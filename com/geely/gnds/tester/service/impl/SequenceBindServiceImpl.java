package com.geely.gnds.tester.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.SequenceBindDTO;
import com.geely.gnds.tester.service.SequenceBindService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: SequenceBindServiceImpl.class */
public class SequenceBindServiceImpl implements SequenceBindService {
    private static final Logger log = LoggerFactory.getLogger(FixSeqServiceImpl.class);

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.SequenceBindService
    public List<SequenceBindDTO> querySeqList(String vin, String type) throws Exception {
        log.info("======> 开始执行queryList方法 <======");
        List<SequenceBindDTO> list = new ArrayList<>();
        log.info("======> 开始调用云端接口 cloud.queryList <======");
        String fix = this.cloud.F(vin, this.cloud.an(vin), type);
        log.info("======> 调用云端接口 cloud.queryList结束 <======");
        JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(fix, JsonNode.class);
        if (readValue.isArray()) {
            readValue.forEach(obj -> {
                SequenceBindDTO sequenceBindDTO = (SequenceBindDTO) ObjectMapperUtils.getInstance().convertValue(obj, SequenceBindDTO.class);
                list.add(sequenceBindDTO);
            });
        }
        log.info("======> 执行queryFixSeqList方法结束 <======");
        return list;
    }
}
