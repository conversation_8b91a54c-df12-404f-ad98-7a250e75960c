package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.CircuitDiagramOverviewDTO;
import com.geely.gnds.tester.dto.DiaImgDTO;
import com.geely.gnds.tester.dto.TranWithOrigin;
import com.geely.gnds.tester.service.CircuitDiagramService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: CircuitDiagramServiceImpl.class */
public class CircuitDiagramServiceImpl implements CircuitDiagramService {

    @Autowired
    private Cloud cloud;
    private static final Logger LOG = LoggerFactory.getLogger(CircuitDiagramServiceImpl.class);

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public List<TranWithOrigin> getCircuitDiagramList(String wdid, String vin) throws Exception {
        if (StringUtils.isNotBlank(wdid)) {
            LOG.info("======> 开始请求云端接口 cloud.getCircuitDiagramList <======");
            String circuitDiagramList = this.cloud.t(wdid, vin);
            LOG.info("======> 请求云端接口  cloud.getCircuitDiagramList结束 <======");
            return ObjectMapperUtils.jsonStr2List(circuitDiagramList, TranWithOrigin.class);
        }
        return new ArrayList();
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public void getCircuitDiagram(String svgName, String vin, HttpServletResponse response) {
        try {
            String url = this.cloud.u(svgName, vin);
            if (StringUtils.isNotBlank(url)) {
                response.setContentType("image/svg+xml");
                response.getOutputStream().write(HttpUtils.sendGet(url));
            }
        } catch (Exception e) {
            LOG.error("获取电路图异常！", e);
        }
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public Map<String, Object> getCircuitInfo(String vin, String type, String ref) throws Exception {
        LOG.info("======> 开始请求云端接口 cloud.getCircuitInfo <======");
        String circuitInfo = this.cloud.o(vin, type, ref);
        LOG.info("======> 请求云端接口 cloud.getCircuitInfo结束 <======");
        return ObjectMapperUtils.jsonStr2Map(circuitInfo);
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public List<Object> getCircuitExtend(String type, String ref, String vin, String id) throws Exception {
        LOG.info("======> 开始请求云端接口 cloud.getCircuitExtend <======");
        String circuitInfo = this.cloud.f(type, ref, vin, id);
        LOG.info("======> 请求云端接口 cloud.getCircuitExtend结束 <======");
        return ObjectMapperUtils.jsonStr2List(circuitInfo, Object.class);
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public void getHarnessImage(String type, String ref, String vin, HttpServletResponse response) {
        try {
            String circuitExtend = this.cloud.f(type, ref, vin, "");
            List<Object> list = ObjectMapperUtils.jsonStr2List(circuitExtend, Object.class);
            if (!CollectionUtils.isEmpty(list)) {
                response.setContentType("image/svg+xml");
                response.getOutputStream().write(HttpUtils.sendGet((String) list.get(0)));
            }
        } catch (Exception e) {
            LOG.error("获取线束图片异常！", e);
        }
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public List<String> filterCircuitDiagramNode(String id, String vin) throws Exception {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(vin)) {
            return new ArrayList();
        }
        return ObjectMapperUtils.jsonStr2List(this.cloud.z(id, vin), String.class);
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public List<TranWithOrigin> getSystemCircuitDiagramList(String vin) throws Exception {
        if (StringUtils.isNotBlank(vin)) {
            LOG.info("======> 开始请求云端接口 cloud.getCircuitDiagramList <======");
            String circuitDiagramList = this.cloud.aU(vin);
            LOG.info("======> 请求云端接口  cloud.getCircuitDiagramList结束 <======");
            return ObjectMapperUtils.jsonStr2List(circuitDiagramList, TranWithOrigin.class);
        }
        return new ArrayList();
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public List<CircuitDiagramOverviewDTO> getSystemCircuitDiagramOverviewList(List<String> wdids, String vin) throws Exception {
        if (StringUtils.isNotBlank(vin) && !CollectionUtils.isEmpty(wdids)) {
            LOG.info("======> 开始请求云端接口 cloud.getSystemCircuitDiagramOverviewList <======");
            String circuitDiagramOverviewList = this.cloud.c(vin, wdids);
            LOG.info("======> 请求云端接口  cloud.getSystemCircuitDiagramOverviewList结束 <======");
            return ObjectMapperUtils.jsonStr2List(circuitDiagramOverviewList, CircuitDiagramOverviewDTO.class);
        }
        return new ArrayList();
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public List<DiaImgDTO> getImageTypeList(String gcid, String location, String vin) throws Exception {
        if (StringUtils.isNotBlank(gcid)) {
            String imageTypeList = this.cloud.p(gcid, location, vin);
            return ObjectMapperUtils.jsonStr2List(imageTypeList, DiaImgDTO.class);
        }
        return new ArrayList();
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public void getImage(String nevisImage, HttpServletResponse response, String vin) {
        try {
            String url = this.cloud.v(nevisImage, vin);
            if (StringUtils.isNotBlank(url)) {
                response.setContentType("image/svg+xml");
                response.getOutputStream().write(HttpUtils.sendGet(url));
            }
        } catch (Exception e) {
            LOG.error("获取图片异常！", e);
        }
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public void getImageByWdid(String type, String wdid, String vin, HttpServletResponse response) {
        try {
            if (StringUtils.isNotBlank(wdid)) {
                String url = this.cloud.q(type, wdid, vin);
                if (StringUtils.isNotBlank(url)) {
                    response.setContentType("image/svg+xml");
                    response.getOutputStream().write(HttpUtils.sendGet(url));
                }
            }
        } catch (Exception e) {
            LOG.error("获取图片异常！", e);
        }
    }

    @Override // com.geely.gnds.tester.service.CircuitDiagramService
    public List<DiaImgDTO> getEcuImageTypeList(String wdid, String vin, String gcid, String location) throws Exception {
        String imageTypeList = this.cloud.g(wdid, vin, gcid, location);
        return ObjectMapperUtils.jsonStr2List(imageTypeList, DiaImgDTO.class);
    }
}
