package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.ReleaseNoteDTO;
import com.geely.gnds.tester.service.ReleaseNoteService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: ReleaseNoteServiceImpl.class */
public class ReleaseNoteServiceImpl implements ReleaseNoteService {

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.ReleaseNoteService
    public List<ReleaseNoteDTO> getList() throws Exception {
        String releaseNote = this.cloud.ao(HttpUtils.getLanguage());
        List<ReleaseNoteDTO> list = ObjectMapperUtils.jsonStr2List(releaseNote, ReleaseNoteDTO.class);
        List<ReleaseNoteDTO> thisReleaseNote = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            List<ReleaseNoteDTO> thisReleaseNote2 = (List) list.stream().filter(l -> {
                return "1.36.0.2".equals(l.getVersion());
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(thisReleaseNote2)) {
                return thisReleaseNote2;
            }
            thisReleaseNote = (List) list.stream().filter(l2 -> {
                return 2 == l2.getStatus().intValue();
            }).collect(Collectors.toList());
        }
        return thisReleaseNote;
    }
}
