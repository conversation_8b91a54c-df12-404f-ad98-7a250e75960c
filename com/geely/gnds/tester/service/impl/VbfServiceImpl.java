package com.geely.gnds.tester.service.impl;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.dto.VbfDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.VbfService;
import java.io.File;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: VbfServiceImpl.class */
public class VbfServiceImpl implements VbfService {

    @Autowired
    private FileCache fileCache;

    @Autowired
    private TesterConfigDao eC;
    private static final Logger LOG = LoggerFactory.getLogger(VbfServiceImpl.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @Override // com.geely.gnds.tester.service.VbfService
    public void update(int size, String path) throws Exception {
        if (SingletonManager.hasVehicleConnected().booleanValue()) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00188));
        }
        if (size < 40) {
            size = 40;
        }
        File file = new File(path);
        if (!file.exists()) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00193));
        }
        TesterConfigDto config = this.eC.getConfig();
        String vbfPath = config.getVbfPath();
        if (!path.equals(vbfPath)) {
            this.fileCache.J();
        }
        config.setVbfDefaultSize(size);
        config.setVbfPath(path);
        this.eC.update(config);
        this.fileCache.setVbfBase(file);
    }

    @Override // com.geely.gnds.tester.service.VbfService
    public boolean queryDefaultSize() throws Exception {
        TesterConfigDto config = this.eC.getConfig();
        int vbfDefaultSizeInt = config.getVbfDefaultSize();
        long vbfDefaultSize = vbfDefaultSizeInt * DoipUtil.MAX_BYTE_ARRAY_SIZE * DoipUtil.MAX_BYTE_ARRAY_SIZE * 1024;
        long vbfTotalSize = this.fileCache.getVbfTotalSize();
        LOG.info("vbfDefaultSize:{}", Long.valueOf(vbfDefaultSize));
        LOG.info("vbfTotalSize:{}", Long.valueOf(vbfTotalSize));
        float scale = (vbfTotalSize / vbfDefaultSize) * 100.0f;
        if (scale >= 90.0f) {
            return true;
        }
        return false;
    }

    @Override // com.geely.gnds.tester.service.VbfService
    public void clean() throws Exception {
        if (SingletonManager.hasVehicleConnected().booleanValue()) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00189));
        }
        TesterConfigDto config = this.eC.getConfig();
        int vbfDefaultSizeInt = config.getVbfDefaultSize();
        long vbfDefaultSize = vbfDefaultSizeInt * DoipUtil.MAX_BYTE_ARRAY_SIZE * DoipUtil.MAX_BYTE_ARRAY_SIZE * 1024;
        this.fileCache.b(vbfDefaultSize / 2);
    }

    @Override // com.geely.gnds.tester.service.VbfService
    public void recoveryDefault() throws Exception {
        if (SingletonManager.hasVehicleConnected().booleanValue()) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00188));
        }
        File vbfBase = new File(AppConfig.getAppHomeDir(), "vbfs");
        if (!vbfBase.exists()) {
            vbfBase.mkdirs();
        }
        String canonicalPath = vbfBase.getCanonicalPath();
        TesterConfigDto config = this.eC.getConfig();
        config.setVbfPath(canonicalPath);
        this.eC.update(config);
        this.fileCache.setVbfBase(new File(canonicalPath));
    }

    @Override // com.geely.gnds.tester.service.VbfService
    public VbfDto getDefault() throws Exception {
        if (SingletonManager.hasVehicleConnected().booleanValue()) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00188));
        }
        File vbfBase = new File(AppConfig.getAppHomeDir(), "vbfs");
        if (!vbfBase.exists()) {
            vbfBase.mkdirs();
        }
        return new VbfDto(40, vbfBase.getCanonicalPath());
    }
}
