package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.ReleaseNoteDTO;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.service.VersionService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: VersionServiceImpl.class */
public class VersionServiceImpl implements VersionService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private TesterConfigDao eC;
    private static final Logger log = LoggerFactory.getLogger(VersionServiceImpl.class);

    @Override // com.geely.gnds.tester.service.VersionService
    public ReleaseNoteDTO getVersionInfo() throws Exception {
        TesterConfigDto config = this.eC.getConfig();
        ReleaseNoteDTO releaseNoteDTO = new ReleaseNoteDTO();
        String versionInfo = this.cloud.b(config);
        List<ReleaseNoteDTO> list = ObjectMapperUtils.jsonStr2List(versionInfo, ReleaseNoteDTO.class);
        if (!CollectionUtils.isEmpty(list)) {
            releaseNoteDTO = list.stream().filter(l -> {
                return 1 == l.getAppType().intValue();
            }).reduce((first, second) -> {
                return second;
            }).orElse(new ReleaseNoteDTO());
            releaseNoteDTO.setSqlUrl((String) list.stream().filter(l2 -> {
                return StringUtils.isNotEmpty(l2.getSqlUrl());
            }).map((v0) -> {
                return v0.getSqlUrl();
            }).collect(Collectors.joining(";")));
            releaseNoteDTO.setNewVersion(true);
        } else {
            releaseNoteDTO.setNewVersion(false);
        }
        return releaseNoteDTO;
    }

    @Override // com.geely.gnds.tester.service.VersionService
    public void doExecuteSql(String sql) throws Exception {
    }
}
