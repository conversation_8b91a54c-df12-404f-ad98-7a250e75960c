package com.geely.gnds.tester.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.RecentDao;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.entity.TesterRecentEntity;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.SearchService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.VehicleUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: SearchServiceImpl.class */
public class SearchServiceImpl implements SearchService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private VehicleUtils io;

    @Autowired
    private RecentDao oW;

    @Autowired
    private TokenService tokenService;
    private static final Logger log = LoggerFactory.getLogger(SearchServiceImpl.class);

    @Override // com.geely.gnds.tester.service.SearchService
    public VehicleDto search(String vin) throws Exception {
        VehicleDto vehicleDto = new VehicleDto();
        List<String> vins = new ArrayList<>();
        vins.add(vin);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String data = this.cloud.c(vins, loginUser.getUsername());
        ObjectMapper mapper = new ObjectMapper();
        List<Object> broadcasts = Arrays.asList((Object[]) mapper.readValue(data, Object[].class));
        if (CollectionUtils.isEmpty(broadcasts)) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00118));
        }
        String broadcast = ObjectMapperUtils.obj2JsonStr(broadcasts.get(0));
        Map res = this.io.settleVehicleInfo(broadcast);
        vehicleDto.setBroadcastMap(res);
        SingletonManager instance = SingletonManager.getInstance();
        vehicleDto.setConnect(Boolean.valueOf(instance.getFdTcpClient(vin) != null));
        vehicleDto.setVin(vin);
        return vehicleDto;
    }

    @Override // com.geely.gnds.tester.service.SearchService
    public void saveVehicle(String vin) throws Exception {
        if ("00000000000000000".equals(vin)) {
            return;
        }
        List<String> vins = new ArrayList<>();
        vins.add(vin);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String data = this.cloud.c(vins, loginUser.getUsername());
        ObjectMapper mapper = new ObjectMapper();
        List<Object> broadcasts = Arrays.asList((Object[]) mapper.readValue(data, Object[].class));
        if (CollectionUtils.isEmpty(broadcasts)) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00118));
        }
        String broadcast = ObjectMapperUtils.obj2JsonStr(broadcasts.get(0));
        Map res = this.io.settleVehicleInfo(broadcast);
        log.info("searchService tokenService.getLoginUser");
        String username = loginUser.getUsername();
        List<TesterRecentEntity> testerRecentEntitys = this.oW.getVehicle(username, vin);
        TesterRecentEntity testerRecentEntity = null;
        if (testerRecentEntitys.size() > 0) {
            testerRecentEntity = testerRecentEntitys.get(0);
        }
        log.info("searchService recentDao.getVehicle");
        if (testerRecentEntity == null) {
            TesterRecentEntity testerRecentEntity2 = new TesterRecentEntity();
            testerRecentEntity2.setBroadcast(broadcast);
            testerRecentEntity2.setOperator(username);
            testerRecentEntity2.setCreateBy(username);
            testerRecentEntity2.setUpdateBy(username);
            testerRecentEntity2.setVin(vin);
            testerRecentEntity2.setVehicle(res.get("vehicle") == null ? "" : res.get("vehicle").toString());
            testerRecentEntity2.setManufacturingYear(res.get("manufacturingYear") == null ? "" : res.get("manufacturingYear").toString());
            testerRecentEntity2.setBrandId(Long.valueOf((String) res.get("brandId")));
            testerRecentEntity2.setCreateTime(new Date());
            testerRecentEntity2.setUpdateTime(new Date());
            this.oW.addVehicle(testerRecentEntity2);
            log.info("searchService recentDao.addVehicle");
            return;
        }
        BeanUtils.copyProperties(testerRecentEntity, testerRecentEntity);
        testerRecentEntity.setBrandId(Long.valueOf((String) res.get("brandId")));
        testerRecentEntity.setUpdateTime(new Date());
        this.oW.updateVehicle(testerRecentEntity);
        log.info("searchService recentDao.updateVehicle");
    }
}
