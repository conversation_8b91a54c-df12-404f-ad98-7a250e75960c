package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.dao.ReadoutCacheDao;
import com.geely.gnds.tester.dto.TesterReadoutCacheDTO;
import com.geely.gnds.tester.entity.TesterReadoutCacheEntity;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ReadoutCacheService;
import com.geely.gnds.tester.util.StringParseUtils;
import com.geely.gnds.tester.vo.DtcCacheQueryVo;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: ReadoutCacheServiceImpl.class */
public class ReadoutCacheServiceImpl implements ReadoutCacheService {
    private static final Logger log = LoggerFactory.getLogger(ReadoutCacheServiceImpl.class);

    @Autowired
    private ReadoutCacheDao pl;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final String pm = "DTC_info";
    private static final String pg = "vehicle_readout";

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public void getInfo(DtcCacheQueryVo cacheQueryVo) throws ParseException {
        String vin = cacheQueryVo.getVin().trim();
        String cacheDate = cacheQueryVo.getCacheDate();
        TesterReadoutCacheEntity cacheEntity = this.pl.getInfo(vin, String.valueOf(DateUtils.parseDate(cacheDate, new String[]{DateUtils.YYYY_MM_DD_HH_MM_SS_SSS}).getTime()));
        if (cacheEntity != null) {
            this.manager.setGlobal(pm, cacheEntity.getDtcInfo(), vin);
            this.manager.setGlobal(pg, cacheEntity.getVehicleReadout() == null ? "" : cacheEntity.getVehicleReadout(), vin);
            this.manager.setGlobal("Readout_DD00_globalTime", cacheEntity.getGlobalTime(), vin);
        }
    }

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public TesterReadoutCacheDTO checkDtcInfo(String vin) {
        log.info("======> 进入checkDtcInfo方法,参数vin:{} <======", vin);
        TesterReadoutCacheDTO entity = new TesterReadoutCacheDTO();
        List<TesterReadoutCacheDTO> cacheEntities = this.pl.getLasted(vin);
        log.info("======> 访问数据库readoutCacheDao.getLasted，获取最新缓存数据 <======");
        if (!CollectionUtils.isEmpty(cacheEntities)) {
            entity = cacheEntities.get(0);
            entity.setCacheTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS, entity.getCreateTime()));
        }
        log.info("======> 进入checkDtcInfo方法结束 <======");
        return entity;
    }

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public void saveOrUpdateVehicleConfig(TesterReadoutCacheEntity entity) {
        TesterReadoutCacheEntity old = this.pl.getLastVehicleConfig(entity.getVin());
        if (ObjectUtils.isNotEmpty(old)) {
            entity.setId(old.getId());
            this.pl.updateReadoutCache(entity);
        } else {
            this.pl.save(entity);
        }
    }

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public TesterReadoutCacheEntity getVehicleConfig(String vin) {
        return this.pl.getLastVehicleConfig(vin);
    }

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public void save(TesterReadoutCacheEntity entity) {
        this.pl.save(entity);
    }

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public TesterReadoutCacheEntity getById(Long id) {
        return this.pl.getById(id);
    }

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public List<TesterReadoutCacheDTO> getLasted(String vin) {
        log.info("======> 进入getLasted方法,参数vin:{} <======", vin);
        List<TesterReadoutCacheDTO> cacheEntities = this.pl.getLasted(vin);
        log.info("======> 进入getLasted方法结束 <======");
        return cacheEntities;
    }

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public void updateReadoutCache(TesterReadoutCacheEntity entity) {
        this.pl.updateReadoutCache(entity);
    }

    @Override // com.geely.gnds.tester.service.ReadoutCacheService
    public void deleteReadoutCache() {
        long time = System.currentTimeMillis() - 604800000;
        List<TesterReadoutCacheEntity> allReadoutCache = this.pl.getAllReadoutCache(String.valueOf(time));
        log.warn("开始删除：{}", Integer.valueOf(allReadoutCache.size()));
        if (!CollectionUtils.isEmpty(allReadoutCache)) {
            List<Long> ids = new ArrayList<>();
            for (TesterReadoutCacheEntity readout : allReadoutCache) {
                ids.add(readout.getId());
            }
            for (List<Long> strings : StringParseUtils.splitList(ids, 1000)) {
                this.pl.deleteByIds(strings);
            }
        }
        log.warn("结束删除");
    }
}
