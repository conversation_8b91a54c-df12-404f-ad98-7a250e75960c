package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DrCscNode;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpActivityDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisOrderDetailOrderDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpSubDtcDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTjDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTokenDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTrDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpUpdateDiagnosisOrderDTO;
import com.geely.gnds.tester.entity.TesterReadoutCacheEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.DtcService;
import com.geely.gnds.tester.service.GidpService;
import com.geely.gnds.tester.service.ReadoutCacheService;
import com.geely.gnds.tester.util.CommonFunctionsUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.github.pagehelper.PageInfo;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: GidpServiceImpl.class */
public class GidpServiceImpl implements GidpService {

    @Autowired
    private DtcService eA;

    @Autowired
    private Cloud cloud;

    @Autowired
    private ReadoutCacheService ji;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(GidpServiceImpl.class);

    @Override // com.geely.gnds.tester.service.GidpService
    public String getGidpToken(String username) throws Exception {
        GidpTokenDTO gidpToken = this.cloud.aR(username);
        return gidpToken.getToken();
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public GidpDiagnosisDTO saveGidpDiagnosis(GidpDiagnosisOrderDetailOrderDTO detailOrder) throws Exception {
        String vin = detailOrder.getVin();
        if (StringUtils.isNotBlank(vin) && 17 == vin.length()) {
            GidpDiagnosisDTO dto = this.cloud.saveGidpDiagnosis(detailOrder);
            return dto;
        }
        throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00276));
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public GidpDiagnosisDTO updateGidpDiagnosis(GidpUpdateDiagnosisOrderDTO updateOrder) throws Exception {
        if ("dtcs".equals(updateOrder.getUpdateType())) {
            String vin = updateOrder.getVin();
            List<DtcInfoDTO> dtcList = this.eA.getDtcList(null, String.valueOf(ConstantEnum.ONE), vin, null);
            if (CollectionUtils.isEmpty(dtcList)) {
                return new GidpDiagnosisDTO();
            }
            String userName = TesterLoginUtils.getLoginUserName();
            Object existCacheId = this.manager.getGlobal(userName, vin);
            String readTime = null;
            if (!Objects.isNull(existCacheId)) {
                long cacheEntityId = ((Long) existCacheId).longValue();
                TesterReadoutCacheEntity cacheEntity = this.ji.getById(Long.valueOf(cacheEntityId));
                if (cacheEntity.getCreateGlobalTime() != null) {
                    readTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, cacheEntity.getCreateGlobalTime());
                }
            }
            String finalReadTime = readTime;
            List<GidpSubDtcDTO> subDtcs = (List) dtcList.stream().map(d -> {
                GidpSubDtcDTO subDtcDTO = new GidpSubDtcDTO();
                subDtcDTO.setDtcEcu(d.getEcuName());
                subDtcDTO.setDtcDtc(d.getDtcValue());
                subDtcDTO.setDtcDescription(d.getRawDtc());
                subDtcDTO.setDtcType(String.valueOf(d.getStatus()));
                subDtcDTO.setReadTime(finalReadTime);
                return subDtcDTO;
            }).collect(Collectors.toList());
            updateOrder.setSubdtcList(subDtcs);
            try {
                String bss = this.manager.getGlobalStringDefaultNull(GlobalVariableEnum.md, vin).toString();
                if (StringUtils.isNotBlank(bss)) {
                    updateOrder.setCurrentBssid(CommonFunctionsUtils.hexStringToAscii(CommonFunctionsUtils.hexStringToAscii(bss)));
                }
                String version = this.manager.getGlobalStringDefaultNull(GlobalVariableEnum.me, vin).toString();
                if (StringUtils.isNotBlank(version)) {
                    updateOrder.setCurrentVehicleVersionNumber(CommonFunctionsUtils.hexStringToAscii(CommonFunctionsUtils.hexStringToAscii(version)));
                }
                String driving = this.manager.getGlobalStringDefaultNull(GlobalVariableEnum.mc, vin).toString();
                if (StringUtils.isNotBlank(driving)) {
                    updateOrder.setVehicleKm(Integer.toString(CommonFunctionsUtils.stringToUnsignedInt(driving, 16)));
                }
            } catch (Exception e) {
                log.error("转换失败", e);
            }
        }
        GidpDiagnosisDTO dto = this.cloud.updateGidpDiagnosis(updateOrder);
        return dto;
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public PageInfo<GidpDiagnosisDTO> getPageDiagnosisList(String serviceStationCode, Integer orderStatus, Integer pageNum, Integer pageSize) throws Exception {
        return this.cloud.getPageDiagnosisList(serviceStationCode, orderStatus, pageNum, pageSize);
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public PageInfo<GidpDiagnosisDTO> getPageDiagnosisListByVin(String serviceStationCode, String vin, Integer pageNum, Integer pageSize, String diagnosisMode) throws Exception {
        return this.cloud.getPageDiagnosisListByVin(serviceStationCode, vin, pageNum, pageSize, diagnosisMode);
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public List<DrCscNode> getGidpIssueFunctions() throws Exception {
        return this.cloud.getGidpIssueFunctions();
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public PageInfo<GidpActivityDTO> getGidpActivity(String diagnosticNumber, String vin, Integer pageNum, Integer pageSize) throws Exception {
        return this.cloud.getGidpActivity(diagnosticNumber, vin, pageNum, pageSize);
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public Map responseActivityAction(String diagnosticNumber, String activityNumber, String executionStatus, String feedBack) throws Exception {
        return this.cloud.responseActivityAction(diagnosticNumber, activityNumber, executionStatus, feedBack);
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public PageInfo<GidpTjDTO> searchTjDone(String diagnosticNumber, String vin, String vehicleType, String vehicleStructureWeek, String vdnList, Integer pageNum, Integer pageSize) throws Exception {
        return this.cloud.searchTjDone(diagnosticNumber, vin, vehicleType, vehicleStructureWeek, vdnList, pageNum, pageSize);
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public Map responseTjAction(String diagnosticNumber, String tjNumber, String pushExecutionStatus, String pushFeedBack) throws Exception {
        return this.cloud.responseTjAction(diagnosticNumber, tjNumber, pushExecutionStatus, pushFeedBack);
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public PageInfo<GidpTrDTO> getTrList(String diagnosticNumber, String vin, Integer pageNum, Integer pageSize) throws Exception {
        return this.cloud.getTrList(diagnosticNumber, vin, pageNum, pageSize);
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public GidpTrDTO getCreateTrUrl() throws Exception {
        return this.cloud.getCreateTrUrl();
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public GidpDiagnosisDTO remoteUpdateGidpDiagnosis(String diagnosticNumber, List<DtcInfoDTO> dtcList) {
        try {
            GidpUpdateDiagnosisOrderDTO updateOrder = new GidpUpdateDiagnosisOrderDTO();
            updateOrder.setDiagnosticNumber(diagnosticNumber);
            updateOrder.setUpdateType("dtcs");
            List<GidpSubDtcDTO> subDtcs = (List) dtcList.stream().map(d -> {
                GidpSubDtcDTO subDtcDTO = new GidpSubDtcDTO();
                subDtcDTO.setDtcEcu(d.getEcuName());
                subDtcDTO.setDtcDtc(d.getDtcValue());
                subDtcDTO.setDtcDescription(d.getRawDtc());
                subDtcDTO.setDtcType(String.valueOf(d.getStatus()));
                subDtcDTO.setReadTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
                return subDtcDTO;
            }).collect(Collectors.toList());
            updateOrder.setSubdtcList(subDtcs);
            GidpDiagnosisDTO dto = this.cloud.updateGidpDiagnosis(updateOrder);
            return dto;
        } catch (Exception e) {
            log.error("远程诊断更新工单失败，diagnosticNumber={},e={}", diagnosticNumber, e);
            return null;
        }
    }

    @Override // com.geely.gnds.tester.service.GidpService
    public String getWorkOrderMenuList(String workerOrderType) throws Exception {
        return this.cloud.getWorkOrderMenuList(workerOrderType);
    }
}
