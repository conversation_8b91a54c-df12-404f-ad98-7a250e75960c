package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.service.NetworkTopologyService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.vo.DiaNetworkEcuVo;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: NetworkTopologyServiceImpl.class */
public class NetworkTopologyServiceImpl implements NetworkTopologyService {
    private static final Logger log = LoggerFactory.getLogger(NetworkTopologyServiceImpl.class);

    @Autowired
    Cloud cloud;

    @Override // com.geely.gnds.tester.service.NetworkTopologyService
    public void getNetworkTopology(Long fileId, HttpServletResponse response, String vin) {
        try {
            String url = this.cloud.b(fileId, vin);
            if (StringUtils.isNotBlank(url)) {
                response.setContentType("image/svg+xml");
                response.getOutputStream().write(HttpUtils.sendGet(url));
            }
        } catch (Exception e) {
            log.error("获取网络拓扑图异常！", e);
        }
    }

    @Override // com.geely.gnds.tester.service.NetworkTopologyService
    public Result<DiaNetworkEcuVo> getEcuRelation(String vin) throws Exception {
        String string = this.cloud.aC(vin);
        return new Result().ok(ObjectMapperUtils.jsonStr2Clazz(string, DiaNetworkEcuVo.class));
    }
}
