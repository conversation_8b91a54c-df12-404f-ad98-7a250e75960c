package com.geely.gnds.tester.service.impl;

import com.geely.gnds.tester.dto.SecurityCheckResultDTO;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.service.SecurityCheckService;
import com.geely.gnds.tester.util.RegistryUtil;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
/* loaded from: SecurityCheckServiceImpl.class */
public class SecurityCheckServiceImpl implements SecurityCheckService {
    private static final Logger log = LoggerFactory.getLogger(SecurityCheckServiceImpl.class);

    @Override // com.geely.gnds.tester.service.SecurityCheckService
    public List<SecurityCheckResultDTO> checkSecurityInstallKey(String securitySoftwarePath) {
        if (StringUtils.isEmpty(securitySoftwarePath)) {
            return new ArrayList();
        }
        List<SecurityCheckResultDTO> result = new ArrayList<>();
        String[] pathList = securitySoftwarePath.split(";");
        for (String path : pathList) {
            log.info("安全软件注册表检测,path={}", path);
            String[] regedit = path.split(ConstantEnum.COMMA);
            if (regedit.length > 1) {
                try {
                    String temp = regedit[0];
                    SecurityCheckResultDTO dto = new SecurityCheckResultDTO();
                    dto.setHkey(temp.substring(0, temp.indexOf("\\")));
                    dto.setKey(temp.substring(temp.indexOf("\\") + 1));
                    dto.setSubKey(regedit[1]);
                    dto.setResult(Boolean.valueOf(StringUtils.isNotEmpty(RegistryUtil.readKeyFromRegistry(RegistryUtil.getHkeyByName(dto.getHkey()), dto.getKey(), dto.getSubKey()))));
                    log.info("安全软件注册表检测,path={},result ={}", path, dto.getResult());
                    result.add(dto);
                } catch (Exception e) {
                    log.info("安全软件检测异常，e={}", e);
                }
            }
        }
        return result;
    }

    public static void main(String[] args) {
        String s = null;
        if (StringUtils.isEmpty((CharSequence) null)) {
            return;
        }
        String[] pathList = s.split(";");
        for (String path : pathList) {
            String[] regedit = path.split(ConstantEnum.COMMA);
            if (regedit.length > 1) {
                String temp = regedit[0];
                SecurityCheckResultDTO dto = new SecurityCheckResultDTO();
                dto.setHkey(temp.substring(0, temp.indexOf("\\")) + 1);
                dto.setKey(temp.substring(temp.indexOf("\\") + 1) + 1);
                dto.setSubKey(regedit[1]);
                String s1 = RegistryUtil.readKeyFromRegistry(RegistryUtil.getHkeyByName(dto.getHkey()), dto.getKey(), dto.getSubKey());
                if (StringUtils.isEmpty(s1)) {
                    System.out.println(false);
                }
            }
        }
    }
}
