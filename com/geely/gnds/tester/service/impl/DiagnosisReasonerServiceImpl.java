package com.geely.gnds.tester.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DrCscNode;
import com.geely.gnds.tester.dto.DrMaintenanceTaskDTO;
import com.geely.gnds.tester.service.DiagnosisReasonerService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.vo.DrCreateMaintenanceTaskVO;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: DiagnosisReasonerServiceImpl.class */
public class DiagnosisReasonerServiceImpl implements DiagnosisReasonerService {
    private static final Logger log = LoggerFactory.getLogger(DiagnosisReasonerServiceImpl.class);

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public List<DrCscNode> getCscList() throws Exception {
        String listStr = this.cloud.getCscList();
        List<DrCscNode> list = ObjectMapperUtils.jsonStr2List(listStr, DrCscNode.class);
        return list;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object createMaintenanceTask(DrCreateMaintenanceTaskVO vo) throws Exception {
        String task = this.cloud.a(vo);
        DrMaintenanceTaskDTO dto = (DrMaintenanceTaskDTO) ObjectMapperUtils.jsonStr2Clazz(task, DrMaintenanceTaskDTO.class);
        return dto;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object getExistingMaintenanceTask(String vin, String engineNumber, String caseNo) throws Exception {
        String task = this.cloud.y(vin, engineNumber, caseNo);
        return JSONObject.parseObject(task);
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object getMaintenanceTask(String taskUid) throws Exception {
        String task = this.cloud.aH(taskUid);
        return JSONObject.parseObject(task);
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object faultTest(String faultUid, String testId, String value) throws Exception {
        String res = this.cloud.z(faultUid, testId, value);
        return res;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object revokeFaultTest(String faultUid, String testId, String value) throws Exception {
        String res = this.cloud.A(faultUid, testId, value);
        return res;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object executeMaintenancePlanFixed(String faultUid, int caId, int rating, String comment) throws Exception {
        String res = this.cloud.a(faultUid, caId, rating, comment);
        return res;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object executeMaintenancePlanNotFixed(String faultUid, int caId) throws Exception {
        String res = this.cloud.b(faultUid, caId);
        return res;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object getMaintenanceTaskCloseOptions() throws Exception {
        String res = this.cloud.getMaintenanceTaskCloseOptions();
        return res;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object closeMaintenanceTask(String taskUid, Integer closeOption, String solution) throws Exception {
        String res = this.cloud.a(taskUid, closeOption, solution);
        return res;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object deleteTask(String taskId) throws Exception {
        String res = this.cloud.aI(taskId);
        return res;
    }

    @Override // com.geely.gnds.tester.service.DiagnosisReasonerService
    public Object revokeMaintenancePlan(String faultUid, Integer caId) throws Exception {
        String res = this.cloud.a(faultUid, caId);
        return res;
    }
}
