package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.VehicleTodoDTO;
import com.geely.gnds.tester.service.VehicleTodoService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: VehicleTodoServiceImpl.class */
public class VehicleTodoServiceImpl implements VehicleTodoService {
    private static final Logger LOG = LoggerFactory.getLogger(VehicleTodoServiceImpl.class);

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.VehicleTodoService
    public List<VehicleTodoDTO> getVehicleTodoList(String vin) throws Exception {
        String vehicleTodoList = this.cloud.aF(vin);
        if (StringUtils.isEmpty(vehicleTodoList)) {
            return new ArrayList();
        }
        return ObjectMapperUtils.jsonStr2List(vehicleTodoList, VehicleTodoDTO.class);
    }

    @Override // com.geely.gnds.tester.service.VehicleTodoService
    public Boolean vehicleTodoFeedback(String vin, Long vehicleTodoId, Integer state) throws Exception {
        String result = this.cloud.a(vin, vehicleTodoId, state);
        if (StringUtils.isEmpty(result)) {
            return false;
        }
        return (Boolean) ObjectMapperUtils.jsonStr2Clazz(result, Boolean.class);
    }
}
