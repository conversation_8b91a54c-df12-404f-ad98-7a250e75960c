package com.geely.gnds.tester.service.impl;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.EcuService;
import com.geely.gnds.tester.service.GuidedDiagnosisService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.vo.DiaGcidVo;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: GuidedDiagnosisServiceImpl.class */
public class GuidedDiagnosisServiceImpl implements GuidedDiagnosisService {
    private static final Logger log = LoggerFactory.getLogger(GuidedDiagnosisService.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @Autowired
    private Cloud cloud;

    @Autowired
    private EcuService iU;

    @Override // com.geely.gnds.tester.service.GuidedDiagnosisService
    public List<DiaGcidVo> getComponentOtherList(String vin) throws Exception {
        return ObjectMapperUtils.jsonStr2List(this.cloud.au(vin), DiaGcidVo.class);
    }

    @Override // com.geely.gnds.tester.service.GuidedDiagnosisService
    public List<DiagResItemGroupDto> getFmeaActivationList(String vin, String gcidName, String location, String fmeaName) throws Exception {
        List<EcuBroadcastDto> ecuList = this.iU.getEcuList(vin);
        Map<String, String> ecuMap = (Map) ecuList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getEcuName();
        }, (v0) -> {
            return v0.getDiagnosticNumber();
        }, (k, v) -> {
            return v;
        }));
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            String json = this.cloud.a(vin, gcidName, location, fmeaName, ecuMap);
            if (StringUtils.isBlank(json)) {
                return new ArrayList();
            }
            List<DiagResItemGroupDto> cloudGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
            List<DiagResItemGroupDto> result = new ArrayList<>();
            for (DiagResItemGroupDto diagResItemGroupDto : cloudGroups) {
                List<DiagResItemDto> items = diagResItemGroupDto.getResponseItemDtoList();
                if (!CollectionUtils.isEmpty(items)) {
                    Map<String, List<DiagResItemDto>> collect = (Map) items.stream().collect(Collectors.groupingBy(dto -> {
                        return dto.getOffset() + Constants.GROUP_SPLIT + dto.getSize();
                    }));
                    Set<String> keySet = collect.keySet();
                    if (keySet.size() > 1) {
                        diagResItemGroupDto.setResponseItemDtoList(null);
                        for (String key : keySet) {
                            DiagResItemGroupDto group = new DiagResItemGroupDto();
                            BeanUtils.copyProperties(diagResItemGroupDto, group);
                            group.setResponseItemDtoList((List) collect.get(key).stream().distinct().collect(Collectors.toList()));
                            a(group, result);
                        }
                    } else {
                        diagResItemGroupDto.setResponseItemDtoList((List) diagResItemGroupDto.getResponseItemDtoList().stream().distinct().collect(Collectors.toList()));
                        a(diagResItemGroupDto, result);
                    }
                }
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00081);
            log.error("{}VIN【{}】", new Object[]{formatMsg, vin, e});
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, formatMsg.getBytes());
            });
            throw new CustomException(formatMsg);
        }
    }

    @Override // com.geely.gnds.tester.service.GuidedDiagnosisService
    public List<DiagResItemGroupDto> getComParamList(String vin, String gcidName, String location) throws Exception {
        log.info("======> 开始执行getComParamList接口 <======");
        log.info("======> 开始调用ecuService的getEcuList接口 <======");
        List<EcuBroadcastDto> ecuList = this.iU.getEcuList(vin);
        log.info("======> 调用ecuService的getEcuList接口结束 <======");
        Map<String, String> ecuMap = (Map) ecuList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getEcuName();
        }, (v0) -> {
            return v0.getDiagnosticNumber();
        }, (k, v) -> {
            return v;
        }));
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            log.info("======> 开始调用云端接口 cloud.getComParamList接口 <======");
            String json = this.cloud.b(vin, gcidName, location, ecuMap);
            log.info("======> 调用云端接口 cloud.getComParamList接口结束 <======");
            if (StringUtils.isBlank(json)) {
                return new ArrayList();
            }
            List<DiagResItemGroupDto> cloudGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
            log.info("======> 开始处理云端返回的cloudGroups数据 <======");
            List<DiagResItemGroupDto> result = new ArrayList<>();
            for (DiagResItemGroupDto diagResItemGroupDto : cloudGroups) {
                List<DiagResItemDto> items = diagResItemGroupDto.getResponseItemDtoList();
                if (!CollectionUtils.isEmpty(items)) {
                    Map<String, List<DiagResItemDto>> collect = (Map) items.stream().collect(Collectors.groupingBy(dto -> {
                        return dto.getOffset() + Constants.GROUP_SPLIT + dto.getSize() + Constants.GROUP_SPLIT + dto.getName();
                    }));
                    Set<String> keySet = collect.keySet();
                    if (keySet.size() > 1) {
                        diagResItemGroupDto.setResponseItemDtoList(null);
                        for (String key : keySet) {
                            DiagResItemGroupDto group = new DiagResItemGroupDto();
                            BeanUtils.copyProperties(diagResItemGroupDto, group);
                            group.setResponseItemDtoList(collect.get(key));
                            result.add(group);
                        }
                    } else {
                        result.add(diagResItemGroupDto);
                    }
                }
            }
            log.info("======> 处理云端返回的cloudGroups数据结束 <======");
            log.info("======> 执行getComParamList接口结束 <======");
            return result;
        } catch (Exception e) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00087);
            log.error("{}VIN【{}】", new Object[]{formatMsg, vin, e});
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                String content = String.format("%sVIN【%s】", formatMsg, vin);
                client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
            });
            throw new CustomException(formatMsg);
        }
    }

    @Override // com.geely.gnds.tester.service.GuidedDiagnosisService
    public List<DiagResItemGroupDto> getComActivationList(String vin, String gcidName, String location) throws Exception {
        log.info("======> 开始执行getComActivationList接口 <======");
        log.info("======> 开始调用ecuService的getEcuList接口 <======");
        List<EcuBroadcastDto> ecuList = this.iU.getEcuList(vin);
        log.info("======> 调用ecuService的getEcuList接口结束 <======");
        Map<String, String> ecuMap = (Map) ecuList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getEcuName();
        }, (v0) -> {
            return v0.getDiagnosticNumber();
        }, (k, v) -> {
            return v;
        }));
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            log.info("======> 开始调用云端接口 cloud.getComActivationList接口 <======");
            String json = this.cloud.a(vin, gcidName, location, ecuMap);
            log.info("======> 调用云端接口 cloud.getComActivationList接口结束 <======");
            if (StringUtils.isBlank(json)) {
                return new ArrayList();
            }
            List<DiagResItemGroupDto> cloudGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
            log.info("======> 开始处理云端返回的cloudGroups数据 <======");
            List<DiagResItemGroupDto> result = new ArrayList<>();
            for (DiagResItemGroupDto diagResItemGroupDto : cloudGroups) {
                List<DiagResItemDto> items = diagResItemGroupDto.getResponseItemDtoList();
                if (!CollectionUtils.isEmpty(items)) {
                    Map<String, List<DiagResItemDto>> collect = (Map) items.stream().collect(Collectors.groupingBy(dto -> {
                        return dto.getOffset() + Constants.GROUP_SPLIT + dto.getSize();
                    }));
                    Set<String> keySet = collect.keySet();
                    if (keySet.size() > 1) {
                        diagResItemGroupDto.setResponseItemDtoList(null);
                        for (String key : keySet) {
                            DiagResItemGroupDto group = new DiagResItemGroupDto();
                            BeanUtils.copyProperties(diagResItemGroupDto, group);
                            group.setResponseItemDtoList((List) collect.get(key).stream().distinct().collect(Collectors.toList()));
                            a(group, result);
                        }
                    } else {
                        diagResItemGroupDto.setResponseItemDtoList((List) diagResItemGroupDto.getResponseItemDtoList().stream().distinct().collect(Collectors.toList()));
                        a(diagResItemGroupDto, result);
                    }
                }
            }
            log.info("======> 处理云端返回的cloudGroups数据结束 <======");
            log.info("======> 执行getComActivationList接口结束 <======");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00081);
            log.error("{}VIN【{}】", new Object[]{formatMsg, vin, e});
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DID", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, formatMsg.getBytes());
            });
            throw new CustomException(formatMsg);
        }
    }

    private void a(DiagResItemGroupDto group, List<DiagResItemGroupDto> result) {
        group.setElType("select");
        List<DiagResItemDto> responseItemDtoList = group.getResponseItemDtoList();
        if (!CollectionUtils.isEmpty(responseItemDtoList)) {
            Iterator<DiagResItemDto> iterator = responseItemDtoList.iterator();
            while (iterator.hasNext()) {
                DiagResItemDto diagResItemDto = iterator.next();
                if (StringUtils.isBlank(diagResItemDto.getUnit()) || StringUtils.isBlank(diagResItemDto.getCompareValue())) {
                    iterator.remove();
                }
            }
            if (!CollectionUtils.isEmpty(responseItemDtoList)) {
                result.add(group);
            }
        }
    }
}
