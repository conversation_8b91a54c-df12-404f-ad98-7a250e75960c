package com.geely.gnds.tester.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.xml.FdXmlLogger;
import com.geely.gnds.doip.client.xml.XmlDtc;
import com.geely.gnds.doip.client.xml.XmlSeq;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.bean.BeanUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.dto.DiagnosisEcuDto;
import com.geely.gnds.tester.dto.TechnicalDTO;
import com.geely.gnds.tester.dto.TechnicalReqDTO;
import com.geely.gnds.tester.dto.dtc.DtcCalibrationInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcDmeGeneralInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcExtendedDataRecordDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcSnapshotRecordDTO;
import com.geely.gnds.tester.dto.dtc.ExtendedDataDtcIndicatorDTO;
import com.geely.gnds.tester.entity.TesterReadoutCacheEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.LightStatusEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.seq.UdsSend;
import com.geely.gnds.tester.service.DtcDataAnalysisService;
import com.geely.gnds.tester.service.DtcService;
import com.geely.gnds.tester.service.ParamService;
import com.geely.gnds.tester.service.ReadoutCacheService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.ParseUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.vo.DtcDetailedVO;
import com.geely.gnds.tester.vo.DtcExtendedDataVO;
import com.geely.gnds.tester.vo.SnapshotRecordVO;
import com.geely.gnds.tester.vo.language.DtcListVo;
import com.geely.gnds.tester.vo.language.DtcQueryListVo;
import com.geely.gnds.tester.vo.language.DtcResultVo;
import com.geely.gnds.tester.vo.language.DtcVo;
import com.geely.gnds.tester.vo.language.GetDataIdentifierParam;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: DtcServiceImpl.class */
public class DtcServiceImpl implements DtcService {

    @Autowired
    private Cloud cloud;

    @Value("#{${dtcDisDict}}")
    public Map<String, String> dtcDisDict;

    @Value("#{${dtcLightRule}}")
    public Map<String, String> dtcLightRule;

    @Value("${tester.tenantCode}")
    private String testerTenantCode;

    @Autowired
    private DtcDataAnalysisService pd;

    @Autowired
    private ReadoutCacheService ji;

    @Autowired
    private ParamService jf;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(DtcServiceImpl.class);
    public static final String pe = "数据库数据缺失，请联系售后技术管理人员";

    @Override // com.geely.gnds.tester.service.DtcService
    public DtcDmeGeneralInfoDTO getGeneralInfo(String diagnosticNumber, String dtcId, String vin, String ecuName) throws Exception {
        log.info("======> 开始执行入getDtcGeneralInfo方法 <======");
        log.info("======> 开始调用云端接口 cloud.getDtcDmeGeneralInfo <======");
        String dtcDmeInfo = this.cloud.d(vin, diagnosticNumber, dtcId, ecuName);
        log.info("/api/v1/dtc/data/general cloud.getDtcDmeGeneralInfo入参:{}语言:{}返回数据:{}", new Object[]{diagnosticNumber, HttpUtils.getLanguage(), dtcDmeInfo});
        log.info("======> 调用云端接口 cloud.getDtcDmeGeneralInfo结束 <======");
        DtcDmeGeneralInfoDTO dtcDmeGeneralInfoDTO = null;
        if (StringUtils.isNotBlank(dtcDmeInfo)) {
            dtcDmeGeneralInfoDTO = (DtcDmeGeneralInfoDTO) ObjectMapperUtils.jsonStr2Clazz(dtcDmeInfo, DtcDmeGeneralInfoDTO.class);
        }
        if (ObjectUtils.isEmpty(dtcDmeGeneralInfoDTO)) {
            String errorValue = TesterErrorCodeEnum.SG00028.valueByLanguage();
            log.error("SG00028,message={},diagnosticNumber={},dtcId={}", new Object[]{errorValue, diagnosticNumber, dtcId});
            FdTcpClient client = this.manager.getFdTcpClient(vin);
            Optional.ofNullable(client).ifPresent(cli -> {
                cli.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, ("SG00028,message=" + errorValue + ",diagnosticNumber=" + diagnosticNumber + ",dtcId=" + dtcId).getBytes());
            });
            if (ObjectUtils.isNotEmpty(client)) {
                FdXmlLogger xmlLogger = client.getXmlLogger();
                XmlSeq xmlSeq = new XmlSeq("DME", new Date(), "", "1.36.0.2");
                xmlSeq.fail();
                xmlSeq.a(new XmlDtc(TesterErrorCodeEnum.SG00028.code(), Boolean.FALSE.booleanValue(), diagnosticNumber, dtcId, errorValue));
                xmlLogger.a(xmlSeq);
                return null;
            }
            return null;
        }
        log.info("======> 执行getDtcGeneralInfo方法结束 <======");
        return dtcDmeGeneralInfoDTO;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public DtcCalibrationInfoDTO getCalibrationGeneralInfo(String diagnosticNumber, String dtcId) throws Exception {
        if (ObjectUtils.isEmpty(dtcId)) {
            return null;
        }
        DtcListVo vo = new DtcListVo();
        vo.setDtcIds((String[]) Collections.singletonList(dtcId).toArray(new String[0]));
        vo.setLanguage(HttpUtils.getLanguage());
        vo.setDiagPartNumber(diagnosticNumber);
        String dtcGeneralInfo = this.cloud.a(vo);
        Map<String, DtcCalibrationInfoDTO> calibrationInfoMap = (Map) ObjectMapperUtils.getInstance().readValue(dtcGeneralInfo, new TypeReference<Map<String, DtcCalibrationInfoDTO>>() { // from class: com.geely.gnds.tester.service.impl.DtcServiceImpl.1
        });
        if (!ObjectUtils.isEmpty(calibrationInfoMap)) {
            return calibrationInfoMap.get(dtcId);
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public DtcDetailedVO getDetailedInfo(String title, String diagnosticNumber, String dtcId, String vin, String ecuAddress, String ecuName, Date cacheTime, String globalTimeStr) {
        log.info("======> 开始进入getDetailedInfo方法 <======");
        String data1906 = null;
        StringBuffer dtcStatus = new StringBuffer();
        log.info("======> 开始从全局变量中获取DTC_INFO，tcpClient <======");
        Object globalDtcInfo = this.manager.getGlobal("DTC_info", vin);
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        log.info("======> 从全局变量中获取DTC_INFO，tcpClient结束 <======");
        Object platform = this.manager.getGlobal("platformCode", vin);
        log.info("dtc时间平台属性：{}", platform);
        int platformCode = 2;
        if (platform != null) {
            platformCode = ((Integer) platform).intValue();
        }
        if (ObjectUtils.isEmpty(globalDtcInfo)) {
            return null;
        }
        DtcDetailedVO vo = new DtcDetailedVO();
        String ecuDataStr = globalDtcInfo.toString();
        log.info("======> 开始调用ParseUtils.parseDtcInfo方法 <======");
        List<DiagnosisEcuDto> ecuDataList = ParseUtils.parseDtcInfo(ecuDataStr);
        log.info("======> 调用ParseUtils.parseDtcInfo方法结束 <======");
        if (ObjectUtils.isEmpty(ecuDataList)) {
            return vo;
        }
        Map<String, DiagnosisEcuDto> mapEcuDto = (Map) ecuDataList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getEcuAddress();
        }, Function.identity(), (k1, k2) -> {
            return k1;
        }));
        DiagnosisEcuDto diagnosisEcuDto = mapEcuDto.get(ecuAddress);
        if (ObjectUtils.isEmpty(diagnosisEcuDto)) {
            return vo;
        }
        Map<String, Object> dtcInfo = diagnosisEcuDto.getDtcInfo();
        if (ObjectUtils.isEmpty(dtcInfo)) {
            return vo;
        }
        log.info("======> 开始处理1902数据 <======");
        List<String> dtcList = ObjectMapperUtils.jsonStr2List((String) dtcInfo.get("1902_info"), String.class);
        if (!ObjectUtils.isEmpty(dtcList)) {
            dtcList.stream().filter(d -> {
                return d.startsWith(dtcId);
            }).findFirst().ifPresent(x -> {
                dtcStatus.append(x.substring(6, 8));
            });
        }
        if (StringUtils.isEmpty(dtcStatus.toString())) {
            return vo;
        }
        String dtcInfoId = dtcId + dtcStatus.toString();
        DtcExtendedDataRecordDTO dataRecordDTO = null;
        log.info("======> 处理1902数据结束 <======");
        Map<String, Object> map1906Data = ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(dtcInfo.get(ConstantEnum.INFO_1906)));
        if (!ObjectUtils.isEmpty(map1906Data)) {
            Object dtc1906 = map1906Data.get(dtcInfoId);
            if (!ObjectUtils.isEmpty(dtc1906)) {
                data1906 = dtc1906.toString();
                log.info("======> 开始处理1906数据 <======");
                dataRecordDTO = getDtcExtendedDetailInfo(diagnosticNumber, dtcId, vin, data1906, ecuAddress);
                log.info("======> 处理1906数据结束 <======");
                vo.setExtendedData(covertExtendedDataVO(dataRecordDTO, dtcStatus.toString(), new DtcExtendedDataVO()));
            }
        }
        Object globalTimeObj = this.manager.getGlobal("Readout_DD00_globalTime", vin);
        if (StringUtils.isNotEmpty(globalTimeStr)) {
            globalTimeObj = globalTimeStr.toString();
        }
        Long globalTime = 0L;
        try {
            if (!org.springframework.util.ObjectUtils.isEmpty(globalTimeObj)) {
                globalTime = Long.valueOf(Long.parseLong(globalTimeObj.toString(), 16) * 100);
            }
        } catch (Exception e) {
            Object finalGlobalTimeObj = globalTimeObj;
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                String content = "转换globalTime异常，globalTime=" + finalGlobalTimeObj + "，e=" + e;
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
            });
            log.error("转换globalTime异常，globalTime={}，e={}", globalTimeObj, e);
        }
        log.info("======> 开始计算设置时间轴 <======");
        SnapshotRecordVO snapshotRecordVO = this.pd.fillTimeAxis(dataRecordDTO, new SnapshotRecordVO(), globalTime, cacheTime, platformCode);
        log.info("======> 计算设置时间轴结束 <======");
        if (!ObjectUtils.isEmpty(dtcStatus) && !ObjectUtils.isEmpty(dataRecordDTO)) {
            ExtendedDataDtcIndicatorDTO indicatorDTO = dataRecordDTO.getIndicatorDTO();
            if (!ObjectUtils.isEmpty(indicatorDTO)) {
                log.info("======> 开始设置菱形指示灯 <======");
                snapshotRecordVO.setLightStatus(Integer.valueOf(V(dtcStatus.toString(), indicatorDTO.getValue().toString())));
                log.info("======> 设置菱形指示灯结束 <======");
            }
        }
        vo.setSnapshotRecord(snapshotRecordVO);
        String finalData190 = data1906;
        Optional.ofNullable(tcpClient).ifPresent(client2 -> {
            SnapshotRecordVO snapshotRecord = vo.getSnapshotRecord();
            String defaultColor = "";
            if (!Objects.isNull(snapshotRecord) && !Objects.isNull(snapshotRecord.getLightStatus())) {
                defaultColor = LightStatusEnum.getValue(snapshotRecord.getLightStatus().intValue());
            }
            String content = "GetDTCDetailOperation: " + title + ",Color: " + defaultColor;
            client2.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
            client2.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", ("1906: " + finalData190).getBytes());
        });
        log.info("======> 开始处理1904数据 <======");
        if (a(diagnosticNumber, dtcId, vin, dtcStatus, vo, dtcInfo, dtcInfoId, dataRecordDTO, globalTime, ecuName, platformCode)) {
            log.info("======> 处理1904数据结束，getDetailedInfo方法结束 <======");
            return vo;
        }
        log.info("======> 处理1904数据结束，getDetailedInfo方法结束 <======");
        return vo;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public DtcDetailedVO getDetailedInfo2(String diagnosticNumber, String dtcId, String dtcStatus, String vin, String ecuAddress, String ecuName) {
        log.info("dro getDetailedInfo2,diagnosticNumber={},dtcId={},dtcStatus={},vin={},ecuAddress={},ecuName={}", new Object[]{diagnosticNumber, dtcId, dtcStatus, vin, ecuAddress, ecuName});
        Object globalDtcInfo = this.manager.getGlobal("DTC_info", vin);
        if (ObjectUtils.isEmpty(globalDtcInfo)) {
            log.info("----dro----解析DTC_info为null,vin={}", vin);
            return null;
        }
        DtcDetailedVO vo = new DtcDetailedVO();
        String ecuDataStr = globalDtcInfo.toString();
        List<DiagnosisEcuDto> ecuDataList = ParseUtils.parseDtcInfo(ecuDataStr);
        if (ObjectUtils.isEmpty(ecuDataList)) {
            log.info("----dro----解析globalDtcInfo为null,vin={}", vin);
            return vo;
        }
        Map<String, DiagnosisEcuDto> mapEcuDto = (Map) ecuDataList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getEcuAddress();
        }, Function.identity(), (k1, k2) -> {
            return k1;
        }));
        DiagnosisEcuDto diagnosisEcuDto = mapEcuDto.get(ecuAddress);
        if (ObjectUtils.isEmpty(diagnosisEcuDto)) {
            log.info("----dro----解析diagnosisEcuDto为null,vin={},ecuAddress={}", vin, ecuAddress);
            return vo;
        }
        Map<String, String> dtcInfo = diagnosisEcuDto.getDtcInfo();
        if (ObjectUtils.isEmpty(dtcInfo)) {
            log.info("----dro----解析dtcInfo为null,vin={},ecuAddress={}", vin, ecuAddress);
            return vo;
        }
        String dtcInfoId = dtcId + dtcStatus;
        DtcExtendedDataRecordDTO dataRecordDTO = null;
        Map<String, Object> map1906Data = ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(dtcInfo.get(ConstantEnum.INFO_1906)));
        if (!ObjectUtils.isEmpty(map1906Data)) {
            log.info("----dro----解析map1906Data开始,vin={},ecuAddress={},map1906Data={}", new Object[]{vin, ecuAddress, map1906Data});
            Object dtc1906 = map1906Data.get(dtcInfoId);
            if (!ObjectUtils.isEmpty(dtc1906)) {
                String data1906 = dtc1906.toString();
                log.info("dro 处理1906数据中,diagnosticNumber={},dtcId={},vin={},data1906={},ecuAddress={}", new Object[]{diagnosticNumber, dtcId, vin, data1906, ecuAddress});
                dataRecordDTO = getDtcExtendedDetailInfo(diagnosticNumber, dtcId, vin, data1906, ecuAddress);
                vo.setExtendedData(covertExtendedDataVO(dataRecordDTO, dtcStatus.toString(), new DtcExtendedDataVO()));
            }
        }
        SnapshotRecordVO snapshotRecordVO = new SnapshotRecordVO();
        if (!ObjectUtils.isEmpty(dtcStatus) && !ObjectUtils.isEmpty(dataRecordDTO)) {
            ExtendedDataDtcIndicatorDTO indicatorDTO = dataRecordDTO.getIndicatorDTO();
            if (!ObjectUtils.isEmpty(indicatorDTO)) {
                snapshotRecordVO.setLightStatus(Integer.valueOf(V(dtcStatus.toString(), indicatorDTO.getValue().toString())));
            }
        }
        vo.setSnapshotRecord(snapshotRecordVO);
        log.info("dro 开始处理1904数据,diagnosticNumber={},dtcId={},vin={},dtcInfo={},dtcInfoId={},ecuName={}", new Object[]{diagnosticNumber, dtcId, vin, dtcInfo, dtcInfoId, ecuName});
        a(diagnosticNumber, dtcId, vin, vo, dtcInfo, dtcInfoId, ecuName);
        log.info("dro 结束处理1904数据,diagnosticNumber={},dtcId={},vin={},dtcInfo={},dtcInfoId={},ecuName={}", new Object[]{diagnosticNumber, dtcId, vin, dtcInfo, dtcInfoId, ecuName});
        return vo;
    }

    public boolean a(String diagnosticNumber, String dtcId, String vin, DtcDetailedVO vo, Map<String, String> dtcInfo, String dtcInfoId, String ecuName) {
        Map<String, Object> map1904Data = ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(dtcInfo.get(ConstantEnum.INFO_1904)));
        if (!ObjectUtils.isEmpty(map1904Data)) {
            log.info("----dro----解析map1904Data开始,vin={},ecuName={},map1906Data={}", new Object[]{vin, ecuName, map1904Data});
            Object dtc1904 = map1904Data.get(dtcInfoId);
            if (!ObjectUtils.isEmpty(dtc1904)) {
                String data1904 = dtc1904.toString();
                try {
                    String json = this.cloud.e(vin, diagnosticNumber, "22", ecuName);
                    List<DiagResItemGroupDto> didSizeGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
                    if (CollectionUtils.isEmpty(didSizeGroups)) {
                        return true;
                    }
                    didSizeGroups.forEach(x -> {
                        x.setDiagnosticPartNumber(diagnosticNumber);
                    });
                    List<DtcSnapshotRecordDTO> snapshotRecordDtos = getDtcSnapshotRecordDetailInfo(didSizeGroups, dtcId, vin, data1904);
                    List<Long> didList = new ArrayList<>();
                    snapshotRecordDtos.forEach(s -> {
                        s.getDidList().forEach(d -> {
                            didList.add(d.getId());
                        });
                    });
                    if (CollectionUtils.isEmpty(didList)) {
                        log.info("----dro----解析map1904Data中，didList为null,vin={},ecuName={},map1906Data={}", new Object[]{vin, ecuName, map1904Data});
                        return false;
                    }
                    List<DiagResItemGroupDto> didListResponseItem = null;
                    try {
                        GetDataIdentifierParam param = new GetDataIdentifierParam();
                        param.setDiagnosisPartNum(diagnosticNumber);
                        param.setLanguage(HttpUtils.getLanguage());
                        param.setDidIds((Long[]) didList.toArray(new Long[0]));
                        String didResponseItemJson = this.cloud.a(param);
                        didListResponseItem = ObjectMapperUtils.jsonStr2List(didResponseItemJson, DiagResItemGroupDto.class);
                    } catch (Exception e) {
                        String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00086);
                        log.error("{},diagnosticNumber={},serviceId={},e=", new Object[]{formatMsg, diagnosticNumber, "22", e});
                    }
                    vo.setSnapshotRecord(a(didListResponseItem, snapshotRecordDtos, vo.getSnapshotRecord()));
                    return false;
                } catch (Exception e2) {
                    String formatMsg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00084);
                    log.error("{},diagnosticNumber={},serviceId={},e=", new Object[]{formatMsg2, diagnosticNumber, "22", e2});
                    return true;
                }
            }
            return false;
        }
        return false;
    }

    public boolean a(String diagnosticNumber, String dtcId, String vin, StringBuffer dtcStatus, DtcDetailedVO vo, Map<String, Object> dtcInfo, String dtcInfoId, DtcExtendedDataRecordDTO dataRecordDTO, Long globalTime, String ecuName, int platformCode) {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        if (!ObjectUtils.isEmpty(dtcInfo.get(ConstantEnum.INFO_1904))) {
            Map<String, Object> map1904Data = ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(dtcInfo.get(ConstantEnum.INFO_1904)));
            Object dtc1904 = map1904Data.get(dtcInfoId);
            if (!ObjectUtils.isEmpty(dtc1904)) {
                String data1904 = dtc1904.toString();
                Optional.ofNullable(tcpClient).ifPresent(client -> {
                    client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), "Info", ("1904: " + data1904).getBytes());
                });
                try {
                    String json = this.cloud.e(vin, diagnosticNumber, "22", ecuName);
                    log.info("handle1904Data cloud.getOnlyDidByDiaPartNumber入参:{}返回数据:{}", diagnosticNumber, json);
                    List<DiagResItemGroupDto> didSizeGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
                    if (CollectionUtils.isEmpty(didSizeGroups)) {
                        String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00085);
                        log.error("{},diagnosticNumber={},serviceId={}", new Object[]{formatMsg, diagnosticNumber, "22"});
                        Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                            String content = formatMsg + ",diagnosticNumber=" + diagnosticNumber + ",serviceId=22";
                            client2.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
                        });
                        return true;
                    }
                    didSizeGroups.stream().forEach(x -> {
                        x.setDiagnosticPartNumber(diagnosticNumber);
                    });
                    List<DtcSnapshotRecordDTO> snapshotRecordDtos = getDtcSnapshotRecordDetailInfo(didSizeGroups, dtcId, vin, data1904);
                    List<Long> didList = new ArrayList<>();
                    snapshotRecordDtos.forEach(s -> {
                        s.getDidList().forEach(d -> {
                            didList.add(d.getId());
                        });
                    });
                    List<DiagResItemGroupDto> didListResponseItem = null;
                    try {
                        GetDataIdentifierParam param = new GetDataIdentifierParam();
                        param.setDiagnosisPartNum(diagnosticNumber);
                        param.setLanguage(HttpUtils.getLanguage());
                        param.setDidIds((Long[]) didList.toArray(new Long[0]));
                        String didResponseItemJson = this.cloud.a(param);
                        didListResponseItem = ObjectMapperUtils.jsonStr2List(didResponseItemJson, DiagResItemGroupDto.class);
                    } catch (Exception e) {
                        String formatMsg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00086);
                        log.error("{},diagnosticNumber={},serviceId={},e=", new Object[]{formatMsg2, diagnosticNumber, "22", e});
                        Optional.ofNullable(tcpClient).ifPresent(client3 -> {
                            String content = formatMsg2 + ",diagnosticNumber=" + diagnosticNumber + ",serviceId=22,e=" + e;
                            client3.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
                        });
                    }
                    vo.setSnapshotRecord(covertSnapshotRecordVO(didListResponseItem, dataRecordDTO, snapshotRecordDtos, diagnosticNumber, dtcStatus.toString(), vo.getSnapshotRecord(), vin, globalTime, platformCode));
                    return false;
                } catch (Exception e2) {
                    String formatMsg3 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00084);
                    log.error("{},diagnosticNumber={},serviceId={},e=", new Object[]{formatMsg3, diagnosticNumber, "22", e2});
                    Optional.ofNullable(tcpClient).ifPresent(client4 -> {
                        String content = formatMsg3 + ",diagnosticNumber=" + diagnosticNumber + ",serviceId=22,e=" + e2;
                        client4.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
                    });
                    return true;
                }
            }
            return false;
        }
        return false;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public String getDtcStatus(String vin, String dtcId, String ecuAddress) {
        StringBuffer dtcStatus = new StringBuffer();
        Object globalDtcInfo = this.manager.getGlobal("DTC_info", vin);
        if (ObjectUtils.isEmpty(globalDtcInfo)) {
            return "";
        }
        new DtcDetailedVO();
        String ecuDataStr = globalDtcInfo.toString();
        List<DiagnosisEcuDto> ecuDataList = ParseUtils.parseDtcInfo(ecuDataStr);
        if (ObjectUtils.isEmpty(ecuDataList)) {
            return "";
        }
        Map<String, DiagnosisEcuDto> mapEcuDto = (Map) ecuDataList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getEcuAddress();
        }, Function.identity(), (k1, k2) -> {
            return k1;
        }));
        DiagnosisEcuDto diagnosisEcuDto = mapEcuDto.get(ecuAddress);
        if (ObjectUtils.isEmpty(diagnosisEcuDto)) {
            return "";
        }
        Map<String, String> dtcInfo = diagnosisEcuDto.getDtcInfo();
        if (ObjectUtils.isEmpty(dtcInfo)) {
            return "";
        }
        List<String> dtcList = ObjectMapperUtils.jsonStr2List(dtcInfo.get("1902_info"), String.class);
        if (!ObjectUtils.isEmpty(dtcList)) {
            dtcList.stream().filter(d -> {
                return d.startsWith(dtcId);
            }).findFirst().ifPresent(x -> {
                dtcStatus.append(x.substring(6, 8));
            });
        }
        return dtcStatus.toString();
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public DtcExtendedDataRecordDTO getDtcExtendedDetailInfo(String diagnosticNumber, String dtcId, String vin, String data, String ecuAddress) {
        new DtcExtendedDataRecordDTO();
        if (ObjectUtils.isEmpty(data)) {
            data = "";
        }
        DtcExtendedDataRecordDTO dto = this.pd.analysisDtcExtendedDataRecord(data);
        return dto;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public List<DtcSnapshotRecordDTO> getDtcSnapshotRecordDetailInfo(List<DiagResItemGroupDto> didResItemGroups, String dtcId, String vin, String data) {
        if (ObjectUtils.isEmpty(data)) {
            data = "";
        }
        return this.pd.analysisDtcDtcSnapshotRecord(didResItemGroups, data);
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public DtcExtendedDataRecordDTO getRefreshDtcExtendedDetailInfo(String diagnosticNumber, String dtcId, String vin, String ecuAddress) {
        StringBuffer sb = new StringBuffer();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        String udsCommand = sb.append(Constants.S_1906).append(dtcId).append("FF").toString();
        try {
            String udsData = new UdsSend().udsData(ecuAddress, udsCommand, vin);
            log.info("获取DTC1906数据成功,返回值data={}", udsData);
            if (udsData.startsWith("5906")) {
                String udsData2 = udsData.substring(12);
                log.info("获取DTC1906数据成功后截取数据,截取后data={}", udsData2);
                return getDtcExtendedDetailInfo(diagnosticNumber, dtcId, vin, udsData2, ecuAddress);
            }
            return null;
        } catch (Exception e) {
            log.info("获取DTC1906数据失败,e={}", e);
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                String content = "获取DTC1906数据失败,e=" + e;
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
            });
            return null;
        }
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public DtcExtendedDataVO covertExtendedDataVO(DtcExtendedDataRecordDTO dto, String dtcStatus, DtcExtendedDataVO vo) {
        return this.pd.convertExtendDto2Vo(dto, dtcStatus, vo);
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public SnapshotRecordVO covertSnapshotRecordVO(List<DiagResItemGroupDto> didResItemGroups, DtcExtendedDataRecordDTO extendedDataRecordDTO, List<DtcSnapshotRecordDTO> snapshots, String diagnosticNumber, String dtcStatus, SnapshotRecordVO vo, String vin, Long globalTime, int platformCode) {
        if (ObjectUtils.isEmpty(vo)) {
            vo = new SnapshotRecordVO();
        }
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        if (!ObjectUtils.isEmpty(snapshots)) {
            SnapshotRecordVO finalVo = vo;
            AtomicBoolean flag = new AtomicBoolean(true);
            snapshots.stream().filter(s -> {
                return "20".equals(s.getName());
            }).findFirst().ifPresent(s2 -> {
                if (platformCode == 3) {
                    DtcSnapshotRecordDTO.DidDTO didDTO = s2.getDidList().stream().filter(x -> {
                        return "DD07".equals(x.getDidName());
                    }).findFirst().orElse(null);
                    if (!ObjectUtils.isEmpty(didDTO)) {
                        try {
                            flag.set(false);
                            String didValue = didDTO.getDidValue();
                            finalVo.setFrozenTime(Long.valueOf(ParseUtils.parseDtcDate(didValue)));
                        } catch (Exception e) {
                            log.error("设置冻结时间异常,now={},globalTime ={},value ={},e={}", new Object[]{finalVo.getNow(), globalTime, didDTO.getDidValue(), e});
                            Optional.ofNullable(tcpClient).ifPresent(client -> {
                                String content = String.format("设置冻结时间异常,now=%s,globalTime =%s,value =%s,e=%s", finalVo.getNow(), globalTime, didDTO.getDidValue(), e);
                                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
                            });
                        }
                    }
                    if (flag.get()) {
                        a(globalTime, s2, finalVo, tcpClient);
                        return;
                    }
                    return;
                }
                a(globalTime, s2, finalVo, tcpClient);
            });
        }
        if (CollectionUtils.isEmpty(didResItemGroups)) {
            return vo;
        }
        vo.setFrozenValues(getDidListParseValue(didResItemGroups, snapshots));
        return vo;
    }

    private static void a(Long globalTime, DtcSnapshotRecordDTO s, SnapshotRecordVO finalVo, FdTcpClient tcpClient) {
        DtcSnapshotRecordDTO.DidDTO didDTO = s.getDidList().stream().filter(x -> {
            return "DD00".equals(x.getDidName());
        }).findFirst().orElse(null);
        if (!ObjectUtils.isEmpty(didDTO)) {
            try {
                finalVo.setFrozenTime(Long.valueOf(finalVo.getNow().longValue() - (globalTime.longValue() - (Long.parseLong(didDTO.getDidValue(), 16) * 100))));
            } catch (Exception e) {
                log.error("设置冻结时间异常,now={},globalTime ={},value ={},e={}", new Object[]{finalVo.getNow(), globalTime, didDTO.getDidValue(), e});
                Optional.ofNullable(tcpClient).ifPresent(client -> {
                    String content = String.format("设置冻结时间异常,now=%s,globalTime =%s,value =%s,e=%s", finalVo.getNow(), globalTime, didDTO.getDidValue(), e);
                    client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, content.getBytes());
                });
            }
        }
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public List<SnapshotRecordVO.FrozenValuesVO> getDidListParseValue(List<DiagResItemGroupDto> didResItemGroups, List<DtcSnapshotRecordDTO> snapshots) {
        List<SnapshotRecordVO.FrozenValuesVO> frozenValues = new ArrayList<>();
        Set<String> didSets = new HashSet<>();
        snapshots.forEach(snapshotRecordDTO -> {
            snapshotRecordDTO.getDidList().forEach(s -> {
                didSets.add(s.getDidName());
            });
        });
        List<DiagResItemGroupDto> collect = (List) didResItemGroups.stream().filter(s -> {
            return didSets.contains(s.getDataIdentifierId());
        }).collect(Collectors.toList());
        for (int i = 0; i < snapshots.size(); i++) {
            DtcSnapshotRecordDTO snapshotRecordDTO2 = snapshots.get(i);
            SnapshotRecordVO.FrozenValuesVO frozen = new SnapshotRecordVO.FrozenValuesVO();
            frozen.setName(snapshotRecordDTO2.getName());
            List<DiagResItemParseResultDto> itemParseResults = new ArrayList<>();
            for (DtcSnapshotRecordDTO.DidDTO dto : snapshotRecordDTO2.getDidList()) {
                Optional<DiagResItemGroupDto> thisDid = collect.stream().filter(c -> {
                    return c.getDataIdentifierId().equals(dto.getDidName());
                }).findFirst();
                thisDid.ifPresent(t -> {
                    Map<String, List<DiagResItemDto>> offsetMap = (Map) t.getResponseItemDtoList().stream().sorted(Comparator.comparing((v0) -> {
                        return v0.getOffset();
                    })).collect(Collectors.groupingBy((v0) -> {
                        return v0.getOffset();
                    }));
                    for (String s2 : offsetMap.keySet()) {
                        this.jf.parseResponseByResItem(itemParseResults, t, offsetMap.get(s2), dto.getDidName() + dto.getDidValue());
                        List<SnapshotRecordVO.FrozenValuesVO.SnapshotResponseItemVO> copyListProperties = BeanUtils.copyListProperties(itemParseResults, SnapshotRecordVO.FrozenValuesVO.SnapshotResponseItemVO::new);
                        frozen.setItems(copyListProperties);
                    }
                });
            }
            frozenValues.add(frozen);
        }
        return frozenValues;
    }

    public SnapshotRecordVO a(List<DiagResItemGroupDto> didResItemGroups, List<DtcSnapshotRecordDTO> snapshots, SnapshotRecordVO vo) {
        if (ObjectUtils.isEmpty(vo)) {
            vo = new SnapshotRecordVO();
        }
        List<SnapshotRecordVO.FrozenValuesVO> frozenValues = new ArrayList<>();
        Set<String> didSets = new HashSet<>();
        snapshots.forEach(snapshotRecordDTO -> {
            snapshotRecordDTO.getDidList().forEach(s -> {
                didSets.add(s.getDidName());
            });
        });
        if (CollectionUtils.isEmpty(didResItemGroups)) {
            log.info("----dro----解析frozenValues中，didResItemGroups为null");
            return vo;
        }
        log.info("----dro----解析frozenValues中，didSets={},", didSets);
        List<DiagResItemGroupDto> collect = (List) didResItemGroups.stream().filter(s -> {
            return didSets.contains(s.getDataIdentifierId());
        }).collect(Collectors.toList());
        for (DtcSnapshotRecordDTO snapshotRecordDTO2 : snapshots) {
            SnapshotRecordVO.FrozenValuesVO frozen = new SnapshotRecordVO.FrozenValuesVO();
            frozen.setName(snapshotRecordDTO2.getName());
            List<DiagResItemParseResultDto> itemParseResults = new ArrayList<>();
            log.info("----dro----解析frozenValues中，当前处理的frozenName={},", snapshotRecordDTO2.getName());
            for (DtcSnapshotRecordDTO.DidDTO dto : snapshotRecordDTO2.getDidList()) {
                Optional<DiagResItemGroupDto> thisDid = collect.stream().filter(c -> {
                    return c.getDataIdentifierId().equals(dto.getDidName());
                }).findFirst();
                thisDid.ifPresent(t -> {
                    Map<String, List<DiagResItemDto>> offsetMap = (Map) t.getResponseItemDtoList().stream().sorted(Comparator.comparing((v0) -> {
                        return v0.getOffset();
                    })).collect(Collectors.groupingBy((v0) -> {
                        return v0.getOffset();
                    }));
                    for (String s2 : offsetMap.keySet()) {
                        log.info("----dro----解析frozenValues中，parseResponseByResItem当前处理的车辆的应答数据={},diagnosticPartNumber={},did={}", new Object[]{dto.getDidName() + dto.getDidValue(), t.getDiagnosticPartNumber(), dto.getDidName()});
                        this.jf.parseResponseByResItem(itemParseResults, t, offsetMap.get(s2), dto.getDidName() + dto.getDidValue());
                        List<SnapshotRecordVO.FrozenValuesVO.SnapshotResponseItemVO> copyListProperties = BeanUtils.copyListProperties(itemParseResults, SnapshotRecordVO.FrozenValuesVO.SnapshotResponseItemVO::new);
                        frozen.setItems(copyListProperties);
                    }
                });
            }
            frozenValues.add(frozen);
            log.info("----dro----解析frozenValues后，frozenValues.size={}", Integer.valueOf(frozenValues.size()));
        }
        vo.setFrozenValues(frozenValues);
        return vo;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public List<List<String>> getDtcListByUds(String diagnosticNumber, String vin, String ecuName, List<String> dtcIds) throws Exception {
        log.info("getDtcListByUds入参:diagnosticNumber----{},vin:{},ecuName:{},dtcIds:{}", new Object[]{diagnosticNumber, vin, ecuName, JSON.toJSONString(dtcIds)});
        List<DtcVo> dtcList = new ArrayList<>();
        List<List<String>> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(dtcIds)) {
            return list;
        }
        for (String dtc : dtcIds) {
            DtcVo dtcInfoDTO = new DtcVo();
            dtcInfoDTO.setDtcId(dtc);
            dtcInfoDTO.setEcu(ecuName);
            dtcInfoDTO.setDiagPartNumber(diagnosticNumber);
            String key = dtc.substring(0, 1);
            if (this.dtcDisDict.containsKey(key)) {
                dtcInfoDTO.setDtcValue(this.dtcDisDict.get(key) + dtc.substring(1));
            }
            dtcList.add(dtcInfoDTO);
        }
        DtcQueryListVo dtcQueryListVo = new DtcQueryListVo();
        dtcQueryListVo.setVin(vin);
        dtcQueryListVo.setLanguage(HttpUtils.getLanguage());
        dtcQueryListVo.setTenantCode(Long.valueOf(Long.parseLong(this.testerTenantCode)));
        dtcQueryListVo.setDtcList(dtcList);
        String dtcListByIds = this.cloud.a(dtcQueryListVo);
        DtcResultVo result = (DtcResultVo) ObjectMapperUtils.jsonStr2Clazz(dtcListByIds, DtcResultVo.class);
        if (result == null) {
            result = new DtcResultVo();
        }
        Map<String, String> dtcMap = result.getDmeMap();
        List<String> shieldDtcList = result.getShieldDtcList();
        Iterator<DtcVo> iterator = dtcList.iterator();
        while (iterator.hasNext()) {
            DtcVo dtcInfoDTO2 = iterator.next();
            String key2 = dtcInfoDTO2.getDiagPartNumber() + "_" + dtcInfoDTO2.getDtcId();
            if (shieldDtcList.contains(key2)) {
                iterator.remove();
            } else {
                String name = "数据库数据缺失，请联系售后技术管理人员";
                if (dtcMap.containsKey(key2)) {
                    name = dtcMap.get(key2);
                }
                List<String> dtc2 = new ArrayList<>();
                dtc2.add(ecuName + "-" + dtcInfoDTO2.getDtcValue() + ConstantEnum.EMPTY + name);
                list.add(dtc2);
            }
        }
        log.info("getDtcListByUds 出参：{}", JSON.toJSONString(list));
        return list;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public List<DtcInfoDTO> getDtcList(String diagnosticNumber, String listType, String vin, String ecuName) throws Exception {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        List<DtcInfoDTO> dtcInfoList = new ArrayList<>();
        int type = Integer.parseInt(listType);
        if (type == ConstantEnum.THREE.intValue()) {
            String json = this.cloud.l(vin, diagnosticNumber, ecuName);
            log.info("/api/v1/dtc/data/getDtcList cloud.getDtcList入参:{}语言:{}返回数据:{}", new Object[]{diagnosticNumber, HttpUtils.getLanguage(), json});
            List<DtcInfoDTO> allDtcList = ObjectMapperUtils.jsonStr2List(json, DtcInfoDTO.class);
            for (DtcInfoDTO dtcInfoDTO : allDtcList) {
                dtcInfoDTO.setEcuName(ecuName);
                String name = dtcInfoDTO.getName();
                if (StringUtils.isBlank(name)) {
                    name = "数据库数据缺失，请联系售后技术管理人员";
                }
                dtcInfoDTO.setDiagnosticNumber(diagnosticNumber);
                String dtcId = dtcInfoDTO.getDtcId();
                dtcInfoDTO.setId(dtcId);
                String key = dtcId.substring(0, 1);
                if (this.dtcDisDict.containsKey(key)) {
                    dtcInfoDTO.setDtcValue(this.dtcDisDict.get(key) + dtcId.substring(1));
                }
                dtcInfoDTO.setDtc(ecuName + "-" + dtcInfoDTO.getDtcValue() + ConstantEnum.EMPTY + name);
            }
            allDtcList.sort(Comparator.comparing((v0) -> {
                return v0.getIsBold();
            }, Comparator.nullsLast((v0, v1) -> {
                return v0.compareTo(v1);
            })).reversed());
            return excludeDevelopDtc(allDtcList);
        }
        List<DtcVo> dtcList = new ArrayList<>();
        DtcQueryListVo dtcQueryListVo = new DtcQueryListVo();
        dtcQueryListVo.setVin(vin);
        dtcQueryListVo.setLanguage(HttpUtils.getLanguage());
        dtcQueryListVo.setTenantCode(Long.valueOf(Long.parseLong(this.testerTenantCode)));
        a(dtcInfoList, vin, type, dtcList);
        if (CollectionUtils.isEmpty(dtcList)) {
            log.warn("未匹配到车辆上的DTC");
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "DTC", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, "未匹配到车辆上的DTC".getBytes());
            });
            return dtcInfoList;
        }
        dtcQueryListVo.setDtcList(dtcList);
        String dtcListByIds = this.cloud.a(dtcQueryListVo);
        DtcResultVo result = (DtcResultVo) ObjectMapperUtils.jsonStr2Clazz(dtcListByIds, DtcResultVo.class);
        if (result == null) {
            result = new DtcResultVo();
        }
        Map<String, String> dtcMap = result.getDmeMap();
        List<String> shieldDtcList = result.getShieldDtcList();
        Iterator<DtcInfoDTO> iterator = dtcInfoList.iterator();
        while (iterator.hasNext()) {
            DtcInfoDTO dtcInfoDTO2 = iterator.next();
            String key2 = dtcInfoDTO2.getDiagnosticNumber() + "_" + dtcInfoDTO2.getId();
            if (shieldDtcList.contains(key2)) {
                iterator.remove();
            } else {
                String name2 = "数据库数据缺失，请联系售后技术管理人员";
                if (dtcMap.containsKey(key2)) {
                    name2 = dtcMap.get(key2);
                }
                dtcInfoDTO2.setRawDtc(name2);
                dtcInfoDTO2.setDtc(dtcInfoDTO2.getEcuName() + "-" + dtcInfoDTO2.getDtcValue() + ConstantEnum.EMPTY + name2);
                if (ObjectUtils.isNotEmpty(ecuName) && ecuName.equals(dtcInfoDTO2.getEcuName())) {
                    dtcInfoDTO2.setIsBold(true);
                } else {
                    dtcInfoDTO2.setIsBold(false);
                }
            }
        }
        dtcInfoList.sort(Comparator.comparing((v0) -> {
            return v0.getIsBold();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).thenComparing((v0) -> {
            return v0.getStatus();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).reversed().thenComparing((v0) -> {
            return v0.getEcuName();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).thenComparing((v0) -> {
            return v0.getDtcValue();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })));
        a(vin, dtcInfoList, type);
        return excludeDevelopDtc(dtcInfoList);
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public List<DtcInfoDTO> excludeDevelopDtc(List<DtcInfoDTO> data) {
        return (List) data.stream().filter(d -> {
            return StringUtils.isEmpty(d.getDtcValue()) || !d.getDtcValue().startsWith("U2F");
        }).collect(Collectors.toList());
    }

    private void a(String vin, List<DtcInfoDTO> dtcInfoList, int type) {
        String userName = TesterLoginUtils.getLoginUserName();
        Object existCacheId = this.manager.getGlobal(userName, vin);
        if (!Objects.isNull(existCacheId)) {
            long cacheEntityId = ((Long) existCacheId).longValue();
            TesterReadoutCacheEntity cacheEntity = this.ji.getById(Long.valueOf(cacheEntityId));
            if (cacheEntity != null) {
                if (type == 1) {
                    cacheEntity.setConfirmedDtcList(ObjectMapperUtils.obj2JsonStr(dtcInfoList));
                } else if (type == 2) {
                    cacheEntity.setUnConfirmedDtcList(ObjectMapperUtils.obj2JsonStr(dtcInfoList));
                }
                this.ji.updateReadoutCache(cacheEntity);
            }
        }
    }

    private void a(List<DtcInfoDTO> dtcInfoList, String vin, int type, List<DtcVo> ids) {
        Object ecuList = this.manager.getGlobal("DTC_info", vin);
        Object nums = this.manager.getGlobal(GlobalVariableEnum.mb, vin) == null ? new HashMap(1) : this.manager.getGlobal(GlobalVariableEnum.mb, vin);
        Map diagnosticNumbers = (Map) nums;
        List<DiagnosisEcuDto> ecusByVehicle = ParseUtils.parseDtcInfo(ecuList == null ? "" : ecuList.toString());
        if (!CollectionUtils.isEmpty(ecusByVehicle)) {
            for (DiagnosisEcuDto diagnosisEcuDto : ecusByVehicle) {
                Map dtcInfo = diagnosisEcuDto.getDtcInfo();
                List<String> dtcList = ObjectMapperUtils.jsonStr2List(dtcInfo.get("1902_info").toString(), String.class);
                Map<String, Object> map = ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(dtcInfo.get(ConstantEnum.INFO_1906)));
                if (!CollectionUtils.isEmpty(dtcList)) {
                    for (String dtc : dtcList) {
                        DtcInfoDTO dtcInfoDTO = new DtcInfoDTO();
                        dtcInfoDTO.setEcuAddress(diagnosisEcuDto.getEcuAddress());
                        dtcInfoDTO.setDtcInfoId(dtc);
                        String dtcReadValue = dtc.substring(0, 6);
                        dtcInfoDTO.setId(dtcReadValue);
                        String ecuName = diagnosisEcuDto.getEcuName();
                        dtcInfoDTO.setEcuName(ecuName);
                        String ecuAddress = diagnosisEcuDto.getEcuAddress();
                        String key = dtcReadValue.substring(0, 1);
                        if (this.dtcDisDict.containsKey(key)) {
                            dtcInfoDTO.setDtcValue(this.dtcDisDict.get(key) + dtcReadValue.substring(1));
                        }
                        dtcInfoDTO.setEcuAddress(ecuAddress);
                        if (diagnosticNumbers.containsKey(ecuName)) {
                            dtcInfoDTO.setDiagnosticNumber(diagnosticNumbers.get(ecuName).toString());
                            DtcVo dtcVo = new DtcVo();
                            dtcVo.setDiagPartNumber(diagnosticNumbers.get(ecuName).toString());
                            dtcVo.setDtcId(dtcReadValue);
                            dtcVo.setEcu(ecuName);
                            dtcVo.setDtcValue(dtcInfoDTO.getDtcValue());
                            ids.add(dtcVo);
                        }
                        String status = dtc.substring(6);
                        String indicator = "";
                        if (ObjectUtils.isNotEmpty(map) && map.containsKey(dtc)) {
                            String value = map.get(dtc).toString();
                            if (StringUtils.isNotBlank(value)) {
                                DtcExtendedDataRecordDTO dtcExtendedDataRecordDTO = this.pd.analysisDtcExtendedDataRecord(value);
                                ExtendedDataDtcIndicatorDTO indicatorDTO = dtcExtendedDataRecordDTO.getIndicatorDTO();
                                if (indicatorDTO != null) {
                                    indicator = indicatorDTO.getValue().toString();
                                }
                            }
                        }
                        int lightStatus = V(status, indicator);
                        dtcInfoDTO.setStatus(Integer.valueOf(lightStatus));
                        Boolean unConfirmed = Boolean.valueOf((lightStatus == 1 || lightStatus == 2 || lightStatus == 0 || lightStatus == 3) && type == 2);
                        if (unConfirmed.booleanValue()) {
                            dtcInfoList.add(dtcInfoDTO);
                        }
                        Boolean confirmed = Boolean.valueOf((lightStatus == 2 || lightStatus == 1 || lightStatus == 0 || lightStatus == 3 || type != 1) ? false : true);
                        if (confirmed.booleanValue()) {
                            dtcInfoList.add(dtcInfoDTO);
                        }
                        if (type == 4) {
                            dtcInfoList.add(dtcInfoDTO);
                        }
                    }
                }
            }
        }
    }

    public static void main(String[] args) {
    }

    public int V(String status, String indicator) {
        int value;
        int value2;
        log.info("亮灯规则入参：status-" + status + ";indicator-" + indicator);
        int lightStatus = 0;
        if (StringUtils.isNotBlank(status)) {
            byte[] statusBits = ParseUtils.byteToBitOfArray((byte) Integer.parseInt(status, 16));
            for (int i = 0; i < statusBits.length; i++) {
                int j = 7 - i;
                if (statusBits[j] == 1 && (value2 = Integer.parseInt(this.dtcLightRule.get("status" + i))) > lightStatus) {
                    lightStatus = value2;
                }
            }
        }
        if (StringUtils.isNotBlank(indicator)) {
            byte[] indicatorBits = ParseUtils.byteToBitOfArray((byte) Integer.parseInt(indicator, 16));
            for (int i2 = 0; i2 < indicatorBits.length; i2++) {
                int j2 = 7 - i2;
                if (indicatorBits[j2] == 1 && (value = Integer.parseInt(this.dtcLightRule.get("indicator" + i2))) > lightStatus) {
                    lightStatus = value;
                }
            }
        }
        log.info("亮灯规则出参：lightStatus-" + lightStatus);
        return lightStatus;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public List<TechnicalDTO> getTechnologyList(TechnicalReqDTO technicalReqDTO) throws Exception {
        String technologyList = this.cloud.a(technicalReqDTO);
        List<TechnicalDTO> technicalList = ObjectMapperUtils.jsonStr2List(technologyList, TechnicalDTO.class);
        return technicalList;
    }

    @Override // com.geely.gnds.tester.service.DtcService
    public String getDtcReadValue(String dtc) {
        String key = dtc.substring(0, 1);
        if (this.dtcDisDict.containsKey(key)) {
            return this.dtcDisDict.get(key) + dtc.substring(1);
        }
        return dtc;
    }
}
