package com.geely.gnds.tester.service.impl;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.dto.VehicleManagementDto;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ConnectedService;
import com.geely.gnds.tester.service.VehicleManagementService;
import com.geely.gnds.tester.socket.GndsWebSocket;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: VehicleManagementServiceImpl.class */
public class VehicleManagementServiceImpl implements VehicleManagementService {
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger LOG = LoggerFactory.getLogger(VehicleManagementServiceImpl.class);

    @Autowired
    ConnectedService hr;

    @Override // com.geely.gnds.tester.service.VehicleManagementService
    public List<VehicleManagementDto> getVehicleList() {
        List<VehicleManagementDto> list = new ArrayList<>();
        Map<String, FdTcpClient> fdTcpClients = this.manager.getFdTcpClients();
        for (Map.Entry<String, FdTcpClient> entry : fdTcpClients.entrySet()) {
            String vin = entry.getKey();
            FdTcpClient fdTcpClient = entry.getValue();
            if (fdTcpClient != null || !fdTcpClient.isVirtualVehicle()) {
                VehicleManagementDto vehicleManagementDto = new VehicleManagementDto();
                vehicleManagementDto.setVin(vin);
                vehicleManagementDto.setUsername(fdTcpClient.getUsername());
                vehicleManagementDto.setStatus(fdTcpClient.isDisConnectedByAdmin());
                int connectStatus = 1;
                AtomicInteger seqProcessCount = (AtomicInteger) this.manager.getGlobal("seqProcessCount", vin);
                if (seqProcessCount != null && seqProcessCount.get() != 0) {
                    connectStatus = 2;
                }
                VehicleDto vehicle = this.manager.getVehicle(vin);
                if (vehicle != null) {
                    vehicleManagementDto.setBroadcastMap(vehicle.getBroadcastMap());
                    if (vehicle.isVbfswdl()) {
                        connectStatus = 3;
                    }
                }
                vehicleManagementDto.setConnectStatus(connectStatus);
                list.add(vehicleManagementDto);
            }
        }
        LOG.info("管理员车辆列表出参：{}", list);
        return list;
    }

    @Override // com.geely.gnds.tester.service.VehicleManagementService
    public Map disconnectVehicle(VehicleManagementDto vehicleManagementDto, String username) throws Exception {
        LOG.info("管理员断开车辆入参：{}", vehicleManagementDto.toString());
        Map res = new HashMap();
        String vin = vehicleManagementDto.getVin();
        VehicleDto vehicle = this.manager.getVehicle(vin);
        int code = 200;
        String msg = "";
        AtomicInteger seqProcessCount = (AtomicInteger) this.manager.getGlobal("seqProcessCount", vin);
        if (seqProcessCount != null && seqProcessCount.get() != 0) {
            code = 201;
            msg = "正在对车辆进行诊断，是否断开连接";
            if (vehicle != null && vehicle.isVbfswdl()) {
                code = 202;
                msg = "正在对车辆进行软件刷写，是否断开连接";
            }
        } else {
            LOG.info("管理员断开车辆没有脚本运行，开始关车：{}", res);
            FdTcpClient fdTcpClient = this.manager.getFdTcpClient(vin);
            this.hr.disconnect(vehicleManagementDto.getUsername(), vin, username);
            if (fdTcpClient != null) {
                GndsWebSocket.af(fdTcpClient.getHeartBeatId(), vin);
            }
        }
        res.put(AjaxResult.gA, Integer.valueOf(code));
        res.put(AjaxResult.gB, msg);
        LOG.info("管理员断开车辆出参：{}", res);
        return res;
    }

    /* JADX WARN: Removed duplicated region for block: B:40:0x010e  */
    /* JADX WARN: Removed duplicated region for block: B:52:? A[RETURN, SYNTHETIC] */
    @Override // com.geely.gnds.tester.service.VehicleManagementService
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void confirmDisconnect(com.geely.gnds.tester.dto.VehicleManagementDto r6, java.lang.String r7) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 322
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.service.impl.VehicleManagementServiceImpl.confirmDisconnect(com.geely.gnds.tester.dto.VehicleManagementDto, java.lang.String):void");
    }
}
