package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.dao.QuickLinksDao;
import com.geely.gnds.tester.dto.QuickLinksDTO;
import com.geely.gnds.tester.service.QuickLinksService;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service("sysQuickLinksService")
/* loaded from: QuickLinksServiceImpl.class */
public class QuickLinksServiceImpl implements QuickLinksService {

    @Autowired
    private QuickLinksDao pk;

    @Autowired
    private TokenService tokenService;

    @Override // com.geely.gnds.tester.service.QuickLinksService
    public QuickLinksDTO queryById(Integer id) {
        return this.pk.queryById(id);
    }

    @Override // com.geely.gnds.tester.service.QuickLinksService
    public List<QuickLinksDTO> getList() {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        return this.pk.getList(loginUser.getUsername());
    }

    @Override // com.geely.gnds.tester.service.QuickLinksService
    @Transactional
    public QuickLinksDTO insert(QuickLinksDTO quickLinks) {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        List<QuickLinksDTO> list = this.pk.getList(loginUser.getUsername());
        if (list.size() >= 10) {
            QuickLinksDTO earliestDto = list.stream().min(Comparator.comparing((v0) -> {
                return v0.getCreateTime();
            })).get();
            deleteById(earliestDto.getId());
        }
        if (CollectionUtils.isEmpty(list)) {
            quickLinks.setSort(1);
        } else {
            int maxSort = list.stream().max(Comparator.comparing((v0) -> {
                return v0.getSort();
            })).get().getSort().intValue();
            quickLinks.setSort(Integer.valueOf(maxSort + 1));
        }
        quickLinks.setUserName(loginUser.getUsername());
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
        Date date = new Date();
        quickLinks.setCreateTime(sdf.format(date));
        quickLinks.setUpdateTime(sdf.format(date));
        this.pk.insert(quickLinks);
        return quickLinks;
    }

    @Override // com.geely.gnds.tester.service.QuickLinksService
    public QuickLinksDTO update(QuickLinksDTO quickLinks) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
        quickLinks.setUpdateTime(sdf.format(new Date()));
        this.pk.update(quickLinks);
        return quickLinks;
    }

    @Override // com.geely.gnds.tester.service.QuickLinksService
    @Transactional
    public QuickLinksDTO updateSort(QuickLinksDTO quickLinksDto) {
        List<QuickLinksDTO> list = getList();
        for (int i = 0; i < list.size(); i++) {
            QuickLinksDTO dto = list.get(i);
            if (dto.getId().equals(quickLinksDto.getId())) {
                QuickLinksDTO targetDto = null;
                if ("up".equals(quickLinksDto.getMoveFlag())) {
                    if (i != 0) {
                        targetDto = list.get(i - 1);
                    }
                } else if ("down".equals(quickLinksDto.getMoveFlag()) && i != list.size() - 1) {
                    targetDto = list.get(i + 1);
                }
                int targetSort = targetDto.getSort().intValue();
                targetDto.setSort(dto.getSort());
                SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
                targetDto.setUpdateTime(sdf.format(new Date()));
                this.pk.updateSort(targetDto);
                dto.setSort(Integer.valueOf(targetSort));
                dto.setUpdateTime(sdf.format(new Date()));
                this.pk.updateSort(dto);
                return dto;
            }
        }
        return null;
    }

    @Override // com.geely.gnds.tester.service.QuickLinksService
    public boolean deleteById(Long id) {
        return this.pk.deleteById(id) > 0;
    }
}
