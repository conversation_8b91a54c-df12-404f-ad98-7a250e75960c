package com.geely.gnds.tester.service.impl;

import cn.hutool.core.util.ZipUtil;
import com.aliyun.oss.ClientException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.doip.client.pcap.PcapThreadGlobal;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.xml.FdXmlLogger;
import com.geely.gnds.dsa.enums.LanguageEnum;
import com.geely.gnds.dsa.service.impl.DsaLogServiceImpl;
import com.geely.gnds.ruoyi.common.exception.file.Base64Utils;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.cache.DownloadManager;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.common.SupportUploadService;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.LogCollectionDto;
import com.geely.gnds.tester.dto.LogCollectionProgressDto;
import com.geely.gnds.tester.dto.PageWorkOrderDto;
import com.geely.gnds.tester.dto.SupportWorkOrderDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.LogCollectionStatus;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.SupportService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.VehicleUtils;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: SupportServiceImpl.class */
public class SupportServiceImpl implements SupportService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private VehicleUtils io;

    @Autowired
    private TokenService tokenService;

    @Value("${tester.tenantName}")
    private String tenantName;

    @Autowired
    private SupportUploadService pr;

    @Value("${tester.tenantCode}")
    private String tenantCode;
    private static final Logger log = LoggerFactory.getLogger(SupportServiceImpl.class);
    private static final Map<String, String> ps = new HashMap();

    public static void ae(String key, String value) {
        ps.put(key, value);
    }

    public static String cz(String key) {
        if (ps.containsKey(key)) {
            return ps.get(key);
        }
        return "";
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public void logCollection(LogCollectionDto logCollectionDto, String username) throws Throwable {
        String ossPath;
        String vin = logCollectionDto.getVin();
        String imgEncode = logCollectionDto.getImgEncode();
        String id = logCollectionDto.getId();
        File base = new File(ConstantEnum.POINT, "logCollection");
        LogCollectionProgressDto progressDto = DownloadManager.aa(id);
        if (progressDto == null) {
            progressDto = new LogCollectionProgressDto(0, id, LogCollectionStatus.COMPRESS_LOG.getValue(), "", "", "");
            DownloadManager.a(id, progressDto);
        } else {
            progressDto.setStatusInt(1);
            progressDto.setStatus(LogCollectionStatus.UPLOAD.getValue());
            progressDto.setMsg("");
            DownloadManager.a(id, progressDto);
        }
        try {
            if (!base.exists()) {
                base.mkdirs();
            }
            File upload = new File(base, id);
            if (!upload.exists()) {
                upload.mkdirs();
            }
            String absolutePath = upload.getAbsolutePath();
            String zipPath = absolutePath.concat(ConstantEnum.EXT_ZIP);
            File zip = new File(zipPath);
            String fileTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM, new Date());
            String ossImagePath = "";
            if (StringUtils.isNotBlank(imgEncode)) {
                String imagePath = a(imgEncode, upload, id);
                ossImagePath = "logCollection/" + this.tenantName + "/" + fileTime + "/" + username + "/" + id + ".png";
                this.pr.uploadFileToAliyunForLogCollection(imagePath, ossImagePath, false, id, progressDto, username);
            }
            if (zip.exists()) {
                ossPath = cz(id);
                this.pr.uploadFileToAliyunForLogCollection(zip.getAbsolutePath(), ossPath, true, id, progressDto, username);
            } else {
                File pcap = new File(upload, DsaLogServiceImpl.eG);
                if (!pcap.exists()) {
                    pcap.mkdirs();
                }
                FdTcpClient fdTcpClient = SingletonManager.getInstance().getFdTcpClient(vin);
                log.info("日志收集开始关闭全局日志");
                PcapThreadGlobal.a(pcap.getAbsolutePath() + File.separator, vin, username);
                log.info("日志收集开始关闭全局日志成功");
                if (fdTcpClient != null) {
                    a(upload, fdTcpClient);
                }
                if (StringUtils.isNotBlank(vin)) {
                    a(pcap, vin);
                }
                f(upload);
                File logCollectionZip = ZipUtil.zip(absolutePath, zipPath);
                g(upload);
                ossPath = "logCollection/" + this.tenantName + "/" + fileTime + "/" + username + "/" + logCollectionZip.getName();
                ae(id, ossPath);
                this.pr.uploadFileToAliyunForLogCollection(logCollectionZip.getAbsolutePath(), ossPath, true, id, progressDto, username);
            }
            progressDto.setImageUrl(ossImagePath);
            progressDto.setLogUrl(ossPath);
            progressDto.setStatusInt(2);
            progressDto.setStatus(LogCollectionStatus.SUCCESS.getValue());
        } catch (Throwable e) {
            log.error("日志收集失败", e);
            String msg = e.getMessage();
            if (e instanceof ClientException) {
                msg = LanguageEnum.NETWORK_ERROR.valueByLanguage();
            }
            progressDto.setStatus(LogCollectionStatus.Fail.getValue());
            progressDto.setStatusInt(3);
            progressDto.setMsg(msg);
        }
    }

    private static void f(File upload) throws IOException {
        File info = new File(upload, "info");
        if (!info.exists()) {
            info.mkdirs();
        }
        File infoFile = new File(AppConfig.getAppDataDir(), "applog" + File.separator + "sys-info.log");
        Files.copy(infoFile.toPath(), new File(info, infoFile.getName()).toPath(), new CopyOption[0]);
        File backupFile = new File(AppConfig.getAppDataDir(), "applog" + File.separator + "backup" + File.separator + "info");
        if (backupFile.exists()) {
            for (File file : backupFile.listFiles()) {
                String time = DateUtils.parseDateToStr("yyyy-MM-dd", new Date());
                if (file.getName().contains(time)) {
                    Files.copy(file.toPath(), new File(info, file.getName()).toPath(), new CopyOption[0]);
                }
            }
        }
    }

    private static void a(File pcap, String vin) throws IOException {
        File pcapFile = new File(AppConfig.getAppDataDir(), "applog" + File.separator + "pcap_logs");
        if (pcapFile.exists()) {
            for (File file : pcapFile.listFiles()) {
                log.info("日志收集全局pcap：{}", file.getName());
                if (file.getName().contains("global") && file.getName().contains("vin")) {
                    Files.copy(file.toPath(), new File(pcap, file.getName()).toPath(), new CopyOption[0]);
                }
            }
        }
    }

    private static void a(File upload, FdTcpClient fdTcpClient) throws IOException {
        FdXmlLogger xmlLogger = fdTcpClient.getXmlLogger();
        File xmlFile = xmlLogger.o();
        File xml = new File(upload, "xml");
        if (!xml.exists()) {
            xml.mkdirs();
        }
        xmlFile.renameTo(new File(xml, xmlFile.getName()));
        File txt = new File(upload, "txt");
        if (!txt.exists()) {
            txt.mkdirs();
        }
        File txtFile = fdTcpClient.getTxtFile();
        if (txtFile != null && txtFile.exists()) {
            Files.copy(txtFile.toPath(), new File(txt, txtFile.getName()).toPath(), new CopyOption[0]);
        }
        File pcap = new File(upload, DsaLogServiceImpl.eG);
        SingletonManager instance = SingletonManager.getInstance();
        File pcapFile = new File(AppConfig.getAppDataDir(), "applog" + File.separator + LogTypeEnum.PCAP_PATH.getDirName());
        if (pcapFile.exists()) {
            for (File file : pcapFile.listFiles()) {
                if (instance.isLogUse(file.getName()) && file.length() < 1073741824) {
                    Files.copy(file.toPath(), new File(pcap, file.getName()).toPath(), new CopyOption[0]);
                }
            }
        }
    }

    public static String a(String base64, File upload, String id) throws IOException {
        try {
            File img = new File(upload.getAbsolutePath() + File.separator + "img");
            if (!img.exists()) {
                img.mkdirs();
            }
            String savePath = img.getAbsolutePath() + File.separator + id + ".png";
            File file = new File(savePath);
            if (file.exists()) {
                return savePath;
            }
            byte[] imgbytes = Base64Utils.base64DecodeBytes(base64.replace("data:image/png;base64,", ""));
            OutputStream out = new FileOutputStream(savePath);
            out.write(imgbytes);
            out.flush();
            out.close();
            return savePath;
        } catch (Exception e) {
            log.error("生成图片失败", e);
            return "";
        }
    }

    public static boolean g(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (String str : children) {
                boolean success = g(new File(dir, str));
                if (!success) {
                    return false;
                }
            }
        }
        return dir.delete();
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public LogCollectionProgressDto getLogCollectionProgress(String id) {
        return DownloadManager.aa(id);
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public String submitSupportWorkOrder(SupportWorkOrderDto supportWorkOrderDto) throws Exception {
        String vin = supportWorkOrderDto.getVin();
        if (StringUtils.isNotBlank(vin)) {
            Object broadcastQuery = SingletonManager.getInstance().getGlobal(GlobalVariableEnum.lX, vin);
            String bc = "";
            if (broadcastQuery != null) {
                bc = broadcastQuery.toString();
            } else {
                List<String> vins = new ArrayList<>();
                vins.add(vin);
                LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
                String data = this.cloud.c(vins, loginUser.getUsername());
                ObjectMapper mapper = new ObjectMapper();
                List<Object> broadcasts = Arrays.asList((Object[]) mapper.readValue(data, Object[].class));
                if (!CollectionUtils.isEmpty(broadcasts)) {
                    bc = ObjectMapperUtils.obj2JsonStr(broadcasts.get(0));
                }
            }
            if (!StringUtils.isBlank(bc)) {
                Map map = this.io.settleVehicleInfo(bc);
                supportWorkOrderDto.setEngine(map.getOrDefault("engine", "").toString());
                supportWorkOrderDto.setModel(map.getOrDefault("vehicle", "").toString());
                supportWorkOrderDto.setVehicleYear(map.getOrDefault("manufacturingYear", "").toString());
                supportWorkOrderDto.setTransmission(map.getOrDefault("gearbox", "").toString());
                supportWorkOrderDto.setVehicleWeek(map.getOrDefault("structureWeek", "").toString());
            }
        }
        if (!CollectionUtils.isEmpty(supportWorkOrderDto.getFiles())) {
            LoginUser loginUser2 = this.tokenService.c(ServletUtils.getRequest());
            List<String> attachUrls = new ArrayList<>();
            supportWorkOrderDto.getFiles().forEach(file -> {
                String fileTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM, new Date());
                String ossFilePath = "logCollection/" + this.tenantName + "/" + fileTime + "/" + file.getOriginalFilename();
                try {
                    this.pr.uploadFileToAliyun(file.getInputStream(), ossFilePath, loginUser2.getUsername());
                    attachUrls.add(ossFilePath);
                } catch (IOException e) {
                    throw new RuntimeException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00253));
                }
            });
            supportWorkOrderDto.setAttachUrl(attachUrls);
        }
        return this.cloud.submitSupportWorkOrder(supportWorkOrderDto);
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public void saveLog(LogCollectionDto logCollectionDto) throws Exception {
        String id = logCollectionDto.getId();
        String path = logCollectionDto.getLogPath();
        if (StringUtils.isBlank(path)) {
            path = AppConfig.getAppDataDir().getAbsolutePath() + File.separator + "Downloads";
        }
        File upload = new File(new File(ConstantEnum.POINT, "logCollection"), id);
        String absolutePath = upload.getAbsolutePath();
        String zipPath = absolutePath.concat(ConstantEnum.EXT_ZIP);
        File zip = new File(zipPath);
        if (zip.exists()) {
            String logPath = path + File.separator + zip.getName();
            Files.copy(zip.toPath(), new File(logPath).toPath(), new CopyOption[0]);
        } else {
            log.error("日志不存在");
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00256));
        }
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public void deleteLog(String id) throws Exception {
        File upload = new File(new File(ConstantEnum.POINT, "logCollection"), id);
        String absolutePath = upload.getAbsolutePath();
        String zipPath = absolutePath.concat(ConstantEnum.EXT_ZIP);
        File zip = new File(zipPath);
        if (zip.exists()) {
            zip.delete();
        } else {
            log.error("日志不存在{}", id);
        }
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public String getLogUrl(String id) throws Exception {
        String url = cz(id);
        log.info("日志url：{}", url);
        String workOrderLogUrl = this.cloud.aK(url);
        log.info("日志workOrderLogUrl：{}", workOrderLogUrl);
        return workOrderLogUrl;
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public PageWorkOrderDto queryByPageForClient(int page, int limit, Long submitUserId, String problemDesc, String consultContent) throws Exception {
        String queryByPageForClient = this.cloud.a(page, limit, submitUserId, problemDesc, consultContent);
        PageWorkOrderDto pageWorkOrderDto = (PageWorkOrderDto) ObjectMapperUtils.jsonStr2Clazz(queryByPageForClient, PageWorkOrderDto.class);
        return pageWorkOrderDto;
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public Boolean queryNotice(Long submitUserId) throws Exception {
        String s = this.cloud.d(submitUserId);
        if (StringUtils.isNotBlank(s) && "true".equals(s)) {
            return true;
        }
        return false;
    }

    @Override // com.geely.gnds.tester.service.SupportService
    public void attachmentDownload(String attachedFilePath, HttpServletResponse response) throws Exception {
        String fileName = attachedFilePath.substring(attachedFilePath.lastIndexOf("/") + 1);
        String fileName2 = fileName.contains("?") ? fileName.substring(0, fileName.indexOf("?")) : fileName;
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName2, "UTF-8"));
        response.getOutputStream().write(this.cloud.J(attachedFilePath, this.tenantCode));
    }
}
