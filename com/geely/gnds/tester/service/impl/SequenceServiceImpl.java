package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.TesterSequenceDao;
import com.geely.gnds.tester.dto.OtherSeqDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.entity.TesterSequenceEntity;
import com.geely.gnds.tester.service.SequenceService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: SequenceServiceImpl.class */
public class SequenceServiceImpl implements SequenceService {

    @Autowired
    private TesterSequenceDao pq;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.SequenceService
    public void save(ReloadDto reloadDto) {
        String userName;
        String seqCode = reloadDto.getSeqCode();
        if (StringUtils.isBlank(seqCode)) {
            return;
        }
        Date now = new Date();
        Map<String, Object> params = new HashMap<>(1);
        params.put("sequenceId", seqCode);
        List<TesterSequenceEntity> list = this.pq.queryList(params);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        if (loginUser != null) {
            userName = loginUser.getUsername();
        } else {
            userName = "admin";
        }
        TesterSequenceEntity testerSequenceEntity = new TesterSequenceEntity();
        testerSequenceEntity.setApplicability(reloadDto.getApplic());
        testerSequenceEntity.setName(reloadDto.getName());
        testerSequenceEntity.setSequenceId(seqCode);
        testerSequenceEntity.setVersion(reloadDto.getVersion());
        if (CollectionUtils.isEmpty(list)) {
            testerSequenceEntity.setCreateBy(userName);
            testerSequenceEntity.setCreateTime(now);
            testerSequenceEntity.setUpdateBy(userName);
            testerSequenceEntity.setUpdateTime(now);
            this.pq.create(testerSequenceEntity);
            return;
        }
        testerSequenceEntity.setId(list.get(0).getId());
        testerSequenceEntity.setUpdateBy(userName);
        testerSequenceEntity.setUpdateTime(now);
        this.pq.update(testerSequenceEntity);
    }

    @Override // com.geely.gnds.tester.service.SequenceService
    public List<TesterSequenceEntity> query() {
        return this.pq.queryList(new HashMap(1));
    }

    @Override // com.geely.gnds.tester.service.SequenceService
    public List<OtherSeqDto> getSeqList(String vin, String gcid, String gcidName, String location, String wdid) throws Exception {
        String compontOtherSeqList = this.cloud.d(vin, gcid, gcidName, location, wdid);
        if (StringUtils.isEmpty(compontOtherSeqList)) {
            return new ArrayList();
        }
        return ObjectMapperUtils.jsonStr2List(compontOtherSeqList, OtherSeqDto.class);
    }

    @Override // com.geely.gnds.tester.service.SequenceService
    public List<OtherSeqDto> getDebugSeqList(String vin) throws Exception {
        String compontOtherSeqList = this.cloud.av(vin);
        if (StringUtils.isEmpty(compontOtherSeqList)) {
            return new ArrayList();
        }
        return ObjectMapperUtils.jsonStr2List(compontOtherSeqList, OtherSeqDto.class);
    }
}
