package com.geely.gnds.tester.service.impl;

import cn.hutool.core.date.DateUtil;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.common.UploadCloudBean;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.service.LogUpLoadService;
import com.geely.gnds.tester.service.TesterConfigService;
import java.io.File;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.WebApplicationContext;

@Service
/* loaded from: LogUpLoadServiceImpl.class */
public class LogUpLoadServiceImpl implements LogUpLoadService {
    private static final Logger log = LoggerFactory.getLogger(LogUpLoadServiceImpl.class);

    @Autowired
    private Cloud cloud;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Value("${tester.tenantName}")
    private String tenantName;

    @Autowired
    private TesterConfigService cd;

    @Autowired
    private OssUtils ossUtils;

    @Override // com.geely.gnds.tester.service.LogUpLoadService
    public void upload(String vin, String username, String logPath, LogTypeEnum logType) throws Exception {
        String testerId = (String) Optional.ofNullable(this.cd.getConfig()).map(x -> {
            return x.getTesterCode();
        }).orElse("unknow_client");
        String keyPrefix = "log/" + this.tenantName + "/" + DateUtil.today() + "/" + username + "/" + testerId + "/" + logType.getDirName() + "/";
        File f = new File(logPath);
        if (null != f && f.isFile() && f.length() > 0) {
            String ossName = f.getName();
            this.ossUtils.uploadLogToCloud(new UploadCloudBean(vin, username, this.tenantName, logPath, keyPrefix + ossName, testerId, logType.convert()));
        }
    }
}
