package com.geely.gnds.tester.service.impl;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.dro.document.root.DroResult;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.message.DoipUdpVehicleAnnouncementMessage;
import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.doip.client.pcap.PcapParam;
import com.geely.gnds.doip.client.pcap.PcapThread;
import com.geely.gnds.doip.client.pcap.PcapThreadGlobal;
import com.geely.gnds.doip.client.pcap.PcapWrite;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.txt.FdTxtLogger;
import com.geely.gnds.doip.client.udp.FdDoipUdpReceiver;
import com.geely.gnds.doip.client.udp.UdpClient;
import com.geely.gnds.doip.client.xml.FdXmlLogger;
import com.geely.gnds.doip.client.xml.XmlSeq;
import com.geely.gnds.dsa.service.VehicleService;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.cache.DownloadManager;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.common.TesterContext;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.RecentDao;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.dto.xml.Command;
import com.geely.gnds.tester.dto.xml.EcuCommand;
import com.geely.gnds.tester.dto.xml.ImitateVehDto;
import com.geely.gnds.tester.entity.TesterRecentEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.enums.TesterSeqCodeEnum;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ConnectedService;
import com.geely.gnds.tester.service.LogUpLoadService;
import com.geely.gnds.tester.service.SeqService;
import com.geely.gnds.tester.service.TesterConfigService;
import com.geely.gnds.tester.task.FileUploadTask;
import com.geely.gnds.tester.util.IpUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.VehicleUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.Socket;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import javax.servlet.ServletContext;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

@Service
/* loaded from: ConnectedServiceImpl.class */
public class ConnectedServiceImpl implements ConnectedService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private VehicleUtils io;

    @Autowired
    private RecentDao oW;

    @Autowired
    private FileCache fileCache;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private LogUpLoadService oX;

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private SeqService ek;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private EhcacheClient ehcacheClient;

    @Autowired
    private FileUploadTask fileUploadTask;

    @Autowired
    private PcapWrite eV;

    @Autowired
    private TesterConfigService cd;

    @Autowired
    private VehicleService ev;

    @Autowired
    private SeqServiceImpl ew;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(ConnectedServiceImpl.class);
    private static final String eW = "Vehicle({0}:{1}) => VIN:{2}; EID:{3}; GID:{4}; TA:{5}";
    private static XmlSeq oY;
    private static XmlSeq oZ;

    @Value("${doip.broadcast.address}")
    private String broadcast;

    @Value("${tester.tenantName}")
    private String tenantName;

    @Override // com.geely.gnds.tester.service.ConnectedService
    public List<VehicleDto> queryByTask(String username) throws Exception {
        final List<VehicleDto> vehicles = new LinkedList<>();
        log.debug("进入搜车");
        System.currentTimeMillis();
        final DoipUtil doipUtil = DoipUtil.getInstance();
        UdpClient client = new UdpClient(this.broadcast, 13400, null);
        final SingletonManager instance = SingletonManager.getInstance();
        final List<String> vins = new LinkedList<>();
        final List<String> vinsCopy = new LinkedList<>();
        FdDoipUdpReceiver receiver = new FdDoipUdpReceiver() { // from class: com.geely.gnds.tester.service.impl.ConnectedServiceImpl.1
            @Override // com.geely.gnds.doip.client.udp.FdDoipUdpReceiver, com.geely.gnds.doip.client.udp.FdDoipUdpReceiveListener
            public void onDoipUdpVehicleAnnouncementMessage(DoipUdpVehicleAnnouncementMessage message, String udpServerIp, int udpServerPort) throws Exception {
                ConnectedServiceImpl.log.info("搜索到的车辆信息---->{}", MessageFormat.format(ConnectedServiceImpl.eW, udpServerIp, "" + udpServerPort, new String(message.getVin()), doipUtil.toHexString(message.getEid()), doipUtil.toHexString(message.getGid()), "" + message.getLogicalAddress()));
                m();
                String vinSearch = new String(message.getVin());
                String vin = SingletonManager.getContrastVin(vinSearch);
                VehicleDto vehicle = new VehicleDto();
                vehicle.setIp(udpServerIp);
                vehicle.setConnectType(IpUtils.getConnectType(udpServerIp));
                vehicle.setNeedTls(message.isNeedTls());
                vehicle.setPort(udpServerPort);
                vehicle.setSourceAddress(3712);
                vehicle.setGatewayAddress(message.getLogicalAddress());
                vehicle.setVin(vin);
                vehicle.setEid(doipUtil.toHexString(message.getEid()));
                vehicle.setGid(doipUtil.toHexString(message.getGid()));
                vehicle.setConnect(Boolean.valueOf(instance.getFdTcpClient(vin) != null));
                VehicleDto vehicle2 = instance.getVehicle(vin);
                if (vehicle2 != null) {
                    vehicle.setVbfswdl(vehicle2.isVbfswdl());
                }
                if (!vins.contains(vin)) {
                    if (ConnectedServiceImpl.this.manager.getGlobal(GlobalVariableEnum.lX, vin) == null) {
                        vinsCopy.add(vin);
                    }
                    vins.add(vin);
                    vehicles.add(vehicle);
                }
                synchronized (ConnectedServiceImpl.class) {
                    long now = System.currentTimeMillis() / 1000;
                    String key = String.valueOf(now);
                    String keyVehicle = "vehicle" + key;
                    Set<String> vins2 = (Set) ConnectedServiceImpl.this.ehcacheClient.w(key);
                    List<VehicleDto> vehicleList = (List) ConnectedServiceImpl.this.ehcacheClient.w(keyVehicle);
                    if (vins2 != null) {
                        vins2.add(vin);
                    } else {
                        vins2 = new HashSet<>();
                        vins2.add(vin);
                    }
                    if (vehicleList != null) {
                        vehicleList.add(vehicle);
                    } else {
                        vehicleList = new LinkedList<>();
                        vehicleList.add(vehicle);
                    }
                    vehicleList.add(vehicle);
                    ConnectedServiceImpl.log.info("query:{},vehicleList:{}", keyVehicle, vehicleList);
                    ConnectedServiceImpl.log.info("query:{},vin:{}", key, vin);
                    ConnectedServiceImpl.this.ehcacheClient.a(key, 6, vins2);
                    ConnectedServiceImpl.this.ehcacheClient.a(keyVehicle, 8, vehicleList);
                }
            }
        };
        client.a(receiver);
        if (a(vehicles, vins, vinsCopy, username)) {
            return vehicles;
        }
        return vehicles;
    }

    private boolean a(List<VehicleDto> vehicles, List<String> vins, List<String> vinsCopy, String username) throws Exception {
        log.info("后台定时任务搜车setBroadcast入参：vehicles【{}】,vins【{}】,vinsCopy【{}】", new Object[]{vehicles, vins, vinsCopy});
        Map<String, VehicleDto> vehicleMap = SingletonManager.getVehicleMap();
        if (!vehicleMap.isEmpty()) {
            for (String key : vehicleMap.keySet()) {
                VehicleDto vehicle = vehicleMap.get(key);
                String vin = vehicle.getVin();
                if (vehicle != null && (CollectionUtils.isEmpty(vins) || !vins.contains(vin))) {
                    vins.add(vin);
                    vehicles.add(vehicle);
                    if (this.manager.getGlobal(GlobalVariableEnum.lX, vin) == null) {
                        vinsCopy.add(vin);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(vehicles)) {
            Iterator<VehicleDto> iterator = vehicles.iterator();
            while (iterator.hasNext()) {
                VehicleDto vehicleDto = iterator.next();
                String vin2 = vehicleDto.getVin();
                vehicleDto.setConnect(Boolean.valueOf(this.manager.getFdTcpClientAndDsa(vin2) != null));
                FdTcpClient fdTcpClient = this.manager.getFdTcpClientAndDsa(vin2);
                if (fdTcpClient != null && fdTcpClient.isDsaVehicle()) {
                    iterator.remove();
                }
            }
        }
        if (!TesterLoginUtils.isOnLine()) {
            return true;
        }
        try {
            if (!CollectionUtils.isEmpty(vins)) {
                if (!CollectionUtils.isEmpty(vinsCopy)) {
                    String data = this.cloud.a(vins, username, false);
                    List<Object> broadcasts = Arrays.asList((Object[]) ObjectMapperUtils.getInstance().readValue(data, Object[].class));
                    if (!CollectionUtils.isEmpty(broadcasts)) {
                        Map<String, String> broadcastMap = new HashMap<>(broadcasts.size());
                        for (int i = 0; i < broadcasts.size(); i++) {
                            String bc = ObjectMapperUtils.obj2JsonStr(broadcasts.get(i));
                            String vin3 = ObjectMapperUtils.findStrByJsonNodeExpr(bc, "/vehicleData/vin");
                            this.manager.setGlobal(GlobalVariableEnum.lX, bc, vin3);
                            broadcastMap.put(vin3, bc);
                        }
                        for (VehicleDto vehicleDto2 : vehicles) {
                            String bc2 = broadcastMap.get(vehicleDto2.getVin());
                            vehicleDto2.setBroadcast(bc2);
                            vehicleDto2.setIsFromCloud(1);
                            vehicleDto2.setBroadcastMap(this.io.settleVehicleInfo(bc2));
                        }
                    } else {
                        Iterator<VehicleDto> it = vehicles.iterator();
                        while (it.hasNext()) {
                            it.next().setIsFromCloud(1);
                        }
                    }
                } else {
                    for (VehicleDto vehicleDto3 : vehicles) {
                        Object broadcastQuery = this.manager.getGlobal(GlobalVariableEnum.lX, vehicleDto3.getVin());
                        if (broadcastQuery != null) {
                            String bc3 = broadcastQuery.toString();
                            vehicleDto3.setBroadcast(bc3);
                            vehicleDto3.setIsFromCloud(1);
                            vehicleDto3.setBroadcastMap(this.io.settleVehicleInfo(bc3));
                        }
                    }
                }
            }
            return false;
        } catch (UnAuthException e) {
            log.error("获取车辆详情失败！", e);
            throw new UnAuthException("云端token失效", Integer.valueOf(HttpStatus.UNAUTHORIZED));
        } catch (Exception e2) {
            log.error("获取车辆详情失败！", e2);
            return false;
        }
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public List<VehicleDto> query(String username) throws Exception {
        IpUtils.clean();
        DoipAddressManager.getInstance().getInetAddress();
        List<VehicleDto> vehicles = new LinkedList<>();
        List<String> vins = new LinkedList<>();
        List<String> vinsCopy = new LinkedList<>();
        long now = System.currentTimeMillis();
        long start = now / 1000;
        log.info("用户点击主动搜车【{}】", Long.valueOf(now));
        SingletonManager.setQueryTime(Long.valueOf(now));
        for (int i = 0; i <= ConstantEnum.FIVE.intValue(); i++) {
            String key = String.valueOf(start - i);
            String keyVehicle = "vehicle" + key;
            List<VehicleDto> vehicleList = (List) this.ehcacheClient.w(keyVehicle);
            log.info("缓存key：{},vehicleList列表：{}", key, vehicleList);
            if (!CollectionUtils.isEmpty(vehicleList)) {
                for (VehicleDto vehicleDto : vehicleList) {
                    String vin = vehicleDto.getVin();
                    if (!vins.contains(vin)) {
                        vins.add(vin);
                        if (this.manager.getGlobal(GlobalVariableEnum.lX, vin) == null) {
                            vinsCopy.add(vin);
                        }
                        vehicles.add(vehicleDto);
                    }
                }
            }
        }
        if (a(vehicles, vins, vinsCopy, username)) {
            return vehicles;
        }
        return vehicles;
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public VehicleDto queryVirtualVehicle(MultipartFile file) throws Exception {
        VehicleDto vehicle = new VehicleDto();
        InputStream inputStream = file.getInputStream();
        String vin = a(inputStream);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String data = this.cloud.c(Collections.singletonList(vin), loginUser.getUsername());
        List<Object> broadcasts = Arrays.asList((Object[]) ObjectMapperUtils.getInstance().readValue(data, Object[].class));
        if (CollectionUtils.isEmpty(broadcasts)) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00118));
        }
        Map<String, String> broadcastMap = new HashMap<>(broadcasts.size());
        for (int i = 0; i < broadcasts.size(); i++) {
            String bc = ObjectMapperUtils.obj2JsonStr(broadcasts.get(i));
            log.info("===vin:{}====", ObjectMapperUtils.findStrByJsonNodeExpr(bc, "/vehicleData/vin"));
            this.manager.setGlobal(GlobalVariableEnum.lX, bc, vin);
            broadcastMap.put(vin, bc);
        }
        String bc2 = broadcastMap.get(vin);
        vehicle.setVin(vin);
        vehicle.setConnect(false);
        vehicle.setBroadcast(bc2);
        vehicle.setReadFromXml(true);
        vehicle.setBroadcastMap(this.io.settleVehicleInfo(bc2));
        vehicle.setIsFromCloud(1);
        return vehicle;
    }

    private String a(InputStream inputStream) throws DocumentException, SAXException {
        DownloadManager downloadManager = DownloadManager.getInstance();
        SAXReader reader = new SAXReader();
        reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        Document document = reader.read(inputStream);
        ImitateVehDto imitateVehDto = new ImitateVehDto();
        Element rootElement = document.getRootElement();
        String vin = rootElement.element("vin").getStringValue();
        imitateVehDto.setVin(vin);
        log.info("解析xml中模拟车的vin:{}", vin);
        Element ecus = rootElement.element("ecus");
        Iterator<Element> ecuNodes = ecus.elementIterator("ecu");
        List<EcuCommand> ecuCommands = new LinkedList<>();
        while (ecuNodes.hasNext()) {
            EcuCommand ecuCommand = new EcuCommand();
            Element ecu = ecuNodes.next();
            String address = ecu.attributeValue("address");
            ecuCommand.setAddress(address);
            Iterator<Element> requestNodes = ecu.elementIterator("request");
            List<Command> commands = new LinkedList<>();
            while (requestNodes.hasNext()) {
                Command command = new Command();
                Optional.ofNullable(requestNodes.next()).ifPresent(element -> {
                    command.setRequestMessage(element.attributeValue("message"));
                    command.setResponse(element.element("response").getStringValue());
                });
                commands.add(command);
            }
            ecuCommand.setCommands(commands);
            ecuCommands.add(ecuCommand);
        }
        imitateVehDto.setEcus(ecuCommands);
        downloadManager.a(vin, imitateVehDto);
        log.info("------------解析完成------------");
        return vin;
    }

    public void U(String vin, String broadcast) throws Exception {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String username = loginUser.getUsername();
        List<TesterRecentEntity> testerRecentEntitys = this.oW.getVehicle(username, vin);
        TesterRecentEntity testerRecentEntity = null;
        if (testerRecentEntitys.size() > 0) {
            testerRecentEntity = testerRecentEntitys.get(0);
        }
        Map res = this.io.settleVehicleInfo(broadcast);
        if (testerRecentEntity == null) {
            TesterRecentEntity testerRecentEntity2 = new TesterRecentEntity();
            testerRecentEntity2.setBroadcast(broadcast);
            testerRecentEntity2.setOperator(username);
            testerRecentEntity2.setCreateBy(username);
            testerRecentEntity2.setUpdateBy(username);
            testerRecentEntity2.setVin(vin);
            testerRecentEntity2.setVehicle(res.get("vehicle") == null ? "" : res.get("vehicle").toString());
            testerRecentEntity2.setManufacturingYear(res.get("manufacturingYear") == null ? "" : res.get("manufacturingYear").toString());
            testerRecentEntity2.setBrandId(Long.valueOf((String) res.get("brandId")));
            testerRecentEntity2.setCreateTime(new Date());
            testerRecentEntity2.setUpdateTime(new Date());
            this.oW.addVehicle(testerRecentEntity2);
            return;
        }
        BeanUtils.copyProperties(testerRecentEntity, testerRecentEntity);
        testerRecentEntity.setUpdateTime(new Date());
        testerRecentEntity.setBrandId(Long.valueOf((String) res.get("brandId")));
        this.oW.updateVehicle(testerRecentEntity);
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public Map getVehicleInfo(String vin) throws Exception {
        List<String> vins = new ArrayList<>();
        vins.add(vin);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String data = this.cloud.c(vins, loginUser.getUsername());
        ObjectMapper mapper = new ObjectMapper();
        List<Object> broadcasts = Arrays.asList((Object[]) mapper.readValue(data, Object[].class));
        String temp = ObjectMapperUtils.obj2JsonStr(broadcasts.get(0));
        return this.io.settleVehicleInfo(temp);
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public VehicleDto getVehicle(String vin) throws Exception {
        List<String> vins = new ArrayList<>();
        vins.add(vin);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String data = this.cloud.c(vins, loginUser.getUsername());
        ObjectMapper mapper = new ObjectMapper();
        List<Object> broadcasts = Arrays.asList((Object[]) mapper.readValue(data, Object[].class));
        if (CollectionUtils.isEmpty(broadcasts)) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00118));
        }
        String temp = ObjectMapperUtils.obj2JsonStr(broadcasts.get(0));
        Object strByJsonNodeExpr = ObjectMapperUtils.findObjByJsonNodeExpr(temp, "/vehicleData/ecus");
        List<EcuBroadcastDto> ecusByBroadcast = ObjectMapperUtils.jsonStr2List(strByJsonNodeExpr.toString(), EcuBroadcastDto.class);
        Map<String, String> diagnosticNumbers = new HashMap<>(ecusByBroadcast.size());
        for (EcuBroadcastDto ecuDto : ecusByBroadcast) {
            String number = ecuDto.getDiagnosticPartNumber();
            String version = ecuDto.getDiagnosticPartVersion();
            diagnosticNumbers.put(ecuDto.getEcuAddress(), number + version);
        }
        this.manager.setGlobal(GlobalVariableEnum.mb, diagnosticNumbers, vin);
        VehicleDto vehicleDto = new VehicleDto();
        vehicleDto.setBroadcast(temp);
        vehicleDto.setVin(vin);
        Map res = this.io.settleVehicleInfo(temp);
        vehicleDto.setBroadcastMap(res);
        return vehicleDto;
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public void disconnect(String username, String vin) throws Exception {
        disconnect(username, vin, "");
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public void disconnect(String username, String vin, String adminUsername) throws Exception {
        log.info("======> 进入disconnect断开车辆连接方法，入参username：{}，vin：{}，adminUsername：{} <======", new Object[]{username, vin, adminUsername});
        FdTcpClient fdTcpClient = this.manager.getFdTcpClientAndDsa(vin);
        if (fdTcpClient == null) {
            return;
        }
        if (fdTcpClient.isDsaVehicle()) {
            this.ev.disconnect(username, vin);
            return;
        }
        if (fdTcpClient != null) {
            String seqCode = fdTcpClient.getSeqCode();
            if (StringUtils.isNotBlank(seqCode)) {
                SeqManager.getInstance().handleException(vin, seqCode);
            }
        }
        log.info("======> 进入disconnect断开车辆连接方法,开始等待脚本结束 <======");
        long start = System.currentTimeMillis() + 3000;
        while (true) {
            Object lock = this.manager.getLock(vin);
            synchronized (lock) {
                AtomicInteger seqProcessCount = (AtomicInteger) this.manager.getGlobal("seqProcessCount", vin);
                if (seqProcessCount == null) {
                    seqProcessCount = new AtomicInteger();
                }
                AtomicInteger seqStatusReadoutCount = (AtomicInteger) this.manager.getGlobal("seqStatusReadoutCount", vin);
                if (seqStatusReadoutCount == null) {
                    seqStatusReadoutCount = new AtomicInteger();
                }
                log.info("断开车辆连接时，普通脚本计数器数量为{}，002脚本计数器数量为{}", seqProcessCount, seqStatusReadoutCount);
                if (seqProcessCount.get() != 0 || seqStatusReadoutCount.get() != 0) {
                    long now = start - System.currentTimeMillis();
                    if (now > 0) {
                        lock.wait(now);
                    }
                }
            }
        }
        log.info("======> 进入disconnect断开车辆连接方法,等待脚本结束 <======");
        log.info("======> 开始断开socket <======");
        this.manager.removeVehicle(vin);
        SingletonManager.removeVehicleConnectedTime(vin);
        if (fdTcpClient != null) {
            this.manager.removeLogUse(fdTcpClient.getChildPath());
            FdTxtLogger txtLogger = fdTcpClient.geTxtLogger();
            String content = "Log end: " + vin + " ,user: " + username + ",version:********";
            if (StringUtils.isNotBlank(adminUsername)) {
                content = "Log end: " + vin + " ,user: " + adminUsername + ",version:********";
            }
            if (txtLogger != null) {
                txtLogger.write(new Date(), "Main", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
            }
            log.info("======> 获取pcapThread <======");
            PcapThread pcapThread = fdTcpClient.getPcapThread();
            if (pcapThread != null) {
                this.eV.a(pcapThread);
                log.info("======> 关闭pcapThread线程 <======");
            }
            log.info("======> 开始执行fdTcpClient.closeDoip方法 <======");
            fdTcpClient.closeDoip();
            log.info("======> 执行fdTcpClient.closeDoip方法结束 <======");
        }
        SingletonManager.removeVinContrast(vin);
        aq();
        this.manager.removeVehiclesByUser(username, vin);
        this.manager.removeFdTcpClient(vin);
        this.manager.cleanGlobal(vin);
        this.manager.removeLock(vin);
        log.info("======> 断开车辆连接成功 <======");
        SeqManager.cleanSeqMap(vin);
        TesterLoginUtils.clearLoginUserByVin(vin);
        log.info("======> 断开成功，清除用户信息 <======");
        String testerCode = this.cd.getConfig().getTesterCode();
        this.fileUploadTask.cH(testerCode);
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public String connectCreate(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        String seqCode;
        ScriptHandler handler;
        log.info("======> 客户端开始进入connectedService.connectCreate方法 <======");
        String args = initializeUiDto.getArgs();
        String type = initializeUiDto.getType();
        String username = user.getUsername();
        String vin = initializeUiDto.getVin();
        if (TesterLoginUtils.isOnLine()) {
            log.info("======> 客户端开始调用云端接口：cloud.getSeqCodeByBtnCode，参数 type:{} <======", type);
            seqCode = this.cloud.l(type, vin);
            log.info("======> 客户端调用云端接口：cloud.getSeqCodeByBtnCode结束 <======");
            log.info("======> 客户端开始调用云端接口：cloud.getSeqBycode，参数 seqCode:{},vin:{} <======", seqCode, type);
            ReloadDto seqBycode = this.cloud.B(seqCode, vin);
            log.info("======> 客户端调用云端接口：cloud.getSeqBycode结束 <======");
            String seq = seqBycode.getSeqContentString();
            String version = seqBycode.getVersion();
            if (StringUtils.isBlank(seq)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
            }
            handler = new ScriptHandler(seq, vin, args, seqCode, version, username, this.tenantName, user.getUser().getServiceStationCode(), seqBycode.getRequestId());
        } else {
            seqCode = TesterSeqCodeEnum.OFF_LINE_CONNECT.getValue();
            String filePath = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
            }
            handler = new ScriptHandler(new File(filePath), vin, args, seqCode, "1.0", "");
        }
        this.manager.setGlobal(ConstantEnum.READ_OUT, seqCode, vin);
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        log.info("======> 客户端进入connectedService.connectCreate方法结束 <======");
        return handler.getInitializeUi();
    }

    private boolean b(String vin, String oldVin, Boolean readFromXml) {
        if (StringUtils.isNotBlank(oldVin)) {
            return true;
        }
        if (readFromXml != null && readFromXml.booleanValue()) {
            return true;
        }
        EhcacheClient ehcacheClient = (EhcacheClient) SpringUtils.getBean(EhcacheClient.class);
        long start = System.currentTimeMillis() / 1000;
        boolean success = false;
        for (int i = ConstantEnum.EIGHT.intValue(); i >= 0; i--) {
            String key = String.valueOf(start - i);
            Set<String> vins = (Set) ehcacheClient.w(key);
            log.info("缓存key：{},vins列表：{}", key, vins);
            if (vins != null && (vins.contains(vin) || vins.contains("00000000000000000"))) {
                success = true;
                break;
            }
        }
        return success;
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public void connectTcp(VehicleDto vehicleDto) throws Exception {
        FdTcpClient client;
        log.info("connectTcp------开始");
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        String testerId = servletContext.getAttribute("testerID").toString();
        String heartBeatId = vehicleDto.getHeartBeatId();
        String vin = vehicleDto.getVin();
        this.cloud.bb(vin);
        if (this.manager.getFdTcpClient(vin) != null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00179));
        }
        VehicleDto vehicle = this.manager.getVehicle(vin);
        if (vehicle != null && vehicle.getStatus() == 2) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00180));
        }
        if (!b(vin, vehicleDto.getOldVin(), vehicleDto.getReadFromXml())) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00239));
        }
        this.manager.addVehicle(vin, vehicleDto);
        this.manager.setGlobal("seqProcessCount", new AtomicInteger(), vin);
        this.manager.setGlobal("seqStatusReadoutCount", new AtomicInteger(), vin);
        this.manager.tryConnect(vin);
        String temp = vehicleDto.getBroadcast();
        if (StringUtils.isNotBlank(temp)) {
            U(vehicleDto.getVin(), vehicleDto.getBroadcast());
        }
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String username = loginUser.getUsername();
        this.manager.setGlobal("clientInfo", testerId + "_" + username, vin);
        String start = DateUtil.format(new Date(), DateUtils.YYYYMMDDHHMMSSSSS);
        PcapParam pcapParam = new PcapParam(OssUtils.getFullLogPath(LogTypeEnum.PCAP_PATH), vin, username, start);
        String testerModelName = "";
        try {
            testerModelName = Optional.ofNullable(vehicleDto.getBroadcastMap().getOrDefault("vehicle", "")).orElse("").toString();
        } catch (Exception e) {
            log.warn("获取testerModelName失败");
        }
        FdXmlLogger xmlLogger = new FdXmlLogger(this.cloud.getTesterTenantCode(), testerModelName, username, testerId, OssUtils.getFullLogPath(LogTypeEnum.XML_PATH), vin, start, this.tenantName);
        FdTxtLogger txtLogger = new FdTxtLogger(OssUtils.getFullLogPath(LogTypeEnum.TXT_PATH), vin, username, start);
        String content = "Log start: " + vin + " ,user: " + username + ",version:********";
        txtLogger.write(new Date(), "Main", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
        Boolean readFromXml = vehicleDto.getReadFromXml();
        if (readFromXml == null || !readFromXml.booleanValue()) {
            vehicleDto.setStatus(2);
            client = new FdTcpClient(vehicleDto.getIp(), vehicleDto.getSourceAddress(), vehicleDto.getGatewayAddress(), vehicleDto.getPort(), xmlLogger, pcapParam, txtLogger, username, heartBeatId, vehicleDto.isNeedTls());
            log.info("connectTcp------TesterContext.init开始");
            TesterContext.init(vin, client);
            log.info("connectTcp------TesterContext.init结束");
            SeqManager.getInstance().initSeqMap(vin);
            try {
                log.info("connectTcp------client.connectDoip开始");
                client.connectDoip(vehicleDto.getVin(), true);
                log.info("connectTcp------client.connectDoip结束");
                vehicleDto.setStatus(1);
                this.manager.addVehicle(vin, vehicleDto);
                this.manager.addLock(vin);
                client.setVehicleDto(vehicleDto);
                this.manager.addFdTcpClient(vin, client);
                client.setChildPath(username + "-" + vin + "-" + start);
                this.manager.addLogUse(username + "-" + vin + "-" + start);
                this.manager.addVehiclesBindUser(username, vin);
                SingletonManager.addVinContrast(vehicleDto.getOldVin(), vin);
                SingletonManager.addVehicleConnectedTime(vin, Long.valueOf(System.currentTimeMillis()));
                TesterLoginUtils.setLoginUserByVin(vin, loginUser);
                client.setDroResult(new DroResult());
            } catch (Throwable e2) {
                this.manager.removeVehicle(vin);
                boolean closeGlobal = true;
                if (e2 instanceof DoipException) {
                    log.error("FdTcpClient connectDoip异常---->{}", e2.getMessage());
                    txtLogger.write(new Date(), "Main", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, ("FdTcpClient connectDoip异常---->" + e2.getMessage()).getBytes());
                    Socket socket = client.getSocket();
                    if (socket != null) {
                        try {
                            socket.close();
                            PcapThread pcapThread = client.getPcapThread();
                            if (pcapThread != null) {
                                this.eV.a(pcapThread);
                                log.info("======> 关闭pcapThread线程 <======");
                                closeGlobal = false;
                            }
                        } catch (IOException e3) {
                            log.error("FdTcpClient socket关闭异常---->{}", e2.getMessage());
                            txtLogger.write(new Date(), "Main", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, ("FdTcpClient socket关闭异常---->" + e2.getMessage()).getBytes());
                        }
                    }
                }
                if (closeGlobal) {
                    log.info("开始生成连车全局pcap");
                    String childPath = org.springframework.util.StringUtils.cleanPath(username + "-" + vin + "-" + start);
                    String fullPath = OssUtils.getFullLogPath(LogTypeEnum.PCAP_PATH) + childPath;
                    PcapThreadGlobal.a(fullPath, vin, username);
                }
                TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(e2.getMessage());
                if (enumByCodeOrStr == null) {
                    throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015) + ":" + e2.getMessage());
                }
                throw e2;
            }
        } else {
            client = new FdTcpClient(xmlLogger, txtLogger, vin, username, heartBeatId);
            SingletonManager.addVinContrast(vehicleDto.getOldVin(), vin);
            this.manager.addVehicle(vin, vehicleDto);
            this.manager.addLock(vin);
            this.manager.addFdTcpClient(vin, client);
            this.manager.addVehiclesBindUser(username, vin);
            TesterLoginUtils.setLoginUserByVin(vin, loginUser);
        }
        oY = new XmlSeq("读取参数日志", new Date(), "", "********");
        xmlLogger.a(oY);
        this.manager.addReadParamXmlSeq(vin, oY);
        oZ = new XmlSeq("激活日志", new Date(), "", "********");
        xmlLogger.a(oZ);
        this.manager.addActiveXmlSeqMap(vin, oZ);
        log.info("connectTcp------client.connectDoip结束");
        Map broadcastMap = vehicleDto.getBroadcastMap();
        if (broadcastMap != null && broadcastMap.containsKey("platformCode")) {
            Integer platformCode = (Integer) broadcastMap.get("platformCode");
            client.setPlatformCode(platformCode);
            this.ew.a(vehicleDto.getVin(), username, platformCode, false);
        }
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public void connected(String username, String vin) throws Exception {
        log.info("======> 进入connected方法，入参username：{}，vin：{} <======", username, vin);
        FdTcpClient fdTcpClient = this.manager.getFdTcpClient(vin);
        if (fdTcpClient != null) {
            String fdTcpClientUsername = fdTcpClient.getUsername();
            log.info("======> 进入connected方法，fdTcpClientUsername：{}", fdTcpClientUsername);
            if (StringUtils.isBlank(fdTcpClientUsername) || !fdTcpClientUsername.equals(username)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00198));
            }
        }
    }

    @Override // com.geely.gnds.tester.service.ConnectedService
    public Boolean connectSuccessUploadServer(String vin) throws Exception {
        String result = this.cloud.aG(vin);
        if (com.geely.gnds.ruoyi.common.utils.StringUtils.isEmpty(result)) {
            return false;
        }
        return (Boolean) ObjectMapperUtils.jsonStr2Clazz(result, Boolean.class);
    }

    private void aq() {
        long start = System.currentTimeMillis() / 1000;
        for (int i = 0; i <= ConstantEnum.FIVE.intValue(); i++) {
            String key = String.valueOf(start - i);
            String keyVehicle = "vehicle" + key;
            this.ehcacheClient.y(keyVehicle);
            this.ehcacheClient.y(key);
        }
    }
}
