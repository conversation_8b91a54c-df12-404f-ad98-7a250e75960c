package com.geely.gnds.tester.service.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.xml.FdXmlLogger;
import com.geely.gnds.dsa.dto.SeqUiDto;
import com.geely.gnds.dsa.enums.LanguageEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.DsaSequenceDao;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.dto.VehicleStatusDto;
import com.geely.gnds.tester.entity.DsaSequenceEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.SeqButtonTypeEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.enums.TesterSeqCodeEnum;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.SeqService;
import com.geely.gnds.tester.util.CommonFunctionsUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.io.File;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
/* loaded from: SeqServiceImpl.class */
public class SeqServiceImpl implements SeqService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private FileCache fileCache;

    @Autowired
    private DsaSequenceDao eK;

    @Value("${tester.tenantName}")
    private String tenantName;
    private static final Logger log = LoggerFactory.getLogger(SeqServiceImpl.class);

    @Autowired
    private TesterThread testerThread;
    SeqManager seqManager = SeqManager.getInstance();
    private SingletonManager manager = SingletonManager.getInstance();

    @Override // com.geely.gnds.tester.service.SeqService
    public String init(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        ScriptHandler handler;
        String vin = initializeUiDto.getVin();
        String type = initializeUiDto.getType();
        String args = initializeUiDto.getArgs();
        String seqCode = initializeUiDto.getSeqCode();
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        if (TesterLoginUtils.isOnLine()) {
            if (StringUtils.isBlank(seqCode)) {
                seqCode = this.cloud.l(type, vin);
            }
            ReloadDto seqBycode = this.cloud.B(seqCode, vin);
            String seq = seqBycode.getSeqContentString();
            String version = seqBycode.getVersion();
            if (StringUtils.isBlank(seq)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
            }
            handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, version, user.getUsername(), this.tenantName, user.getUser().getServiceStationCode(), seqBycode.getRequestId());
        } else {
            if (ConstantEnum.STATUS_READOUT.equals(type)) {
                seqCode = TesterSeqCodeEnum.OFF_LINE_VEHICLE_STATUS.getValue();
            }
            String filePath = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
            }
            handler = new ScriptHandler(new File(filePath), vin, args, seqCode, initializeParams, "");
        }
        if (StringUtils.isNotBlank(type)) {
            this.manager.setGlobal(type, seqCode, vin);
        }
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        return handler.getInitializeUi();
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public String excute(InitializeUiDto initializeUiDto) throws Exception {
        log.info("seqService excute开始");
        ScriptHandler handler = o(initializeUiDto);
        log.info("seqService getScriptHandler");
        FdTcpClient client = this.manager.getFdTcpClient(initializeUiDto.getVin());
        if (client == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115));
        }
        FdXmlLogger xmlLogger = client.getXmlLogger();
        log.info("seqService excute结束");
        return handler.callMainModule(xmlLogger);
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public String refreshUi(InitializeUiDto initializeUiDto) throws Exception {
        ScriptHandler handler = o(initializeUiDto);
        String refreshUi = handler.pollSetUi();
        log.info("线程【{}】->诊断序列UI数据pollSetUi数据：{}", Thread.currentThread().getName(), refreshUi);
        return refreshUi;
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public String handleClickEvent(InitializeUiDto initializeUiDto) throws Exception {
        ScriptHandler handler = o(initializeUiDto);
        return handler.handleClickEvent(initializeUiDto);
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public String getSeqId(InitializeUiDto initializeUiDto) throws Exception {
        String seqCode = initializeUiDto.getSeqCode();
        if (StringUtils.isBlank(seqCode)) {
            seqCode = this.cloud.l(initializeUiDto.getType(), initializeUiDto.getVin());
        }
        return seqCode;
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public String stop(InitializeUiDto initializeUiDto) throws Exception {
        String seqCode = initializeUiDto.getSeqCode();
        if (StringUtils.isBlank(seqCode)) {
            seqCode = this.cloud.l(initializeUiDto.getType(), initializeUiDto.getVin());
        }
        SeqManager.getInstance().getScriptHandler(initializeUiDto.getVin() + seqCode);
        return "";
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public void notifySeq(InitializeUiDto initializeUiDto) throws Exception {
        String vin = initializeUiDto.getVin();
        try {
            String type = initializeUiDto.getType();
            String seqCode = initializeUiDto.getSeqCode();
            if (StringUtils.isBlank(seqCode)) {
                Object global = this.manager.getGlobal(type, vin);
                seqCode = global == null ? "" : global.toString();
                if (StringUtils.isBlank(seqCode)) {
                    seqCode = this.cloud.l(type, vin);
                }
            }
            SeqManager.getInstance().getScriptHandler(initializeUiDto.getVin() + seqCode).notifySeq(initializeUiDto);
            log.info("seqService notifySeq getSeqCodeByBtnCode");
        } catch (Exception e) {
            String type2 = initializeUiDto.getTypeChild();
            String seqCode2 = initializeUiDto.getSeqCodeChild();
            if (StringUtils.isBlank(seqCode2)) {
                Object global2 = this.manager.getGlobal(type2, vin);
                seqCode2 = global2 == null ? "" : global2.toString();
                if (StringUtils.isBlank(seqCode2)) {
                    seqCode2 = this.cloud.l(type2, vin);
                }
            }
            ScriptHandler handler = SeqManager.getInstance().getScriptHandler(initializeUiDto.getVin() + seqCode2);
            if (handler != null) {
                handler.logSeqFaultResult(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00045), TesterErrorCodeEnum.SG00045);
            }
            throw e;
        }
    }

    private ScriptHandler o(InitializeUiDto initializeUiDto) throws Exception {
        String seqCode = initializeUiDto.getSeqCode();
        String vin = initializeUiDto.getVin();
        String type = initializeUiDto.getType();
        if (StringUtils.isBlank(seqCode)) {
            Object global = this.manager.getGlobal(type, vin);
            seqCode = global == null ? "" : global.toString();
            if (StringUtils.isBlank(seqCode)) {
                if (TesterLoginUtils.isOnLine()) {
                    seqCode = this.cloud.l(type, vin);
                } else if (ConstantEnum.READ_OUT.equals(type)) {
                    seqCode = TesterSeqCodeEnum.OFF_LINE_CONNECT.getValue();
                } else if (ConstantEnum.SOFT_UPDATE.equals(type)) {
                    seqCode = TesterSeqCodeEnum.OFF_LINE_SOFTWARE.getValue();
                } else if (ConstantEnum.STATUS_READOUT.equals(type)) {
                    seqCode = TesterSeqCodeEnum.OFF_LINE_VEHICLE_STATUS.getValue();
                } else if (SeqButtonTypeEnum.DSA_SWITCH_MODE.equals(type)) {
                    DsaSequenceEntity dsaSequenceEntity = this.eK.selectByBtnCode(String.valueOf(SeqButtonTypeEnum.DSA_SWITCH_MODE));
                    if (dsaSequenceEntity == null) {
                        throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00263));
                    }
                    seqCode = dsaSequenceEntity.getSequenceCode();
                } else if (SeqButtonTypeEnum.DSA_STATUS_READOUT.equals(type)) {
                    DsaSequenceEntity dsaSequenceEntity2 = this.eK.selectByBtnCode(String.valueOf(SeqButtonTypeEnum.DSA_STATUS_READOUT));
                    if (dsaSequenceEntity2 == null) {
                        throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00263));
                    }
                    seqCode = dsaSequenceEntity2.getSequenceCode();
                }
                this.manager.setGlobal(type, seqCode, vin);
            }
        }
        ScriptHandler handler = SeqManager.getInstance().getScriptHandler(vin + seqCode);
        return handler;
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public VehicleStatusDto statusReadout(InitializeUiDto initializeUiDto) {
        log.info("进入statusReadout");
        long start = System.currentTimeMillis();
        String vin = initializeUiDto.getVin();
        VehicleStatusDto vehicleStatusDto = new VehicleStatusDto();
        boolean tryConnect = this.manager.getTryConnect(vin);
        vehicleStatusDto.setTryConnect(tryConnect);
        if (!tryConnect) {
            log.info("statusReadout的tryConnect为false");
            this.manager.tryConnect(vin);
            vehicleStatusDto.setConnectStatus("0");
            log.info("执行002脚本, 行号180耗时{}毫秒", Long.valueOf(System.currentTimeMillis() - start));
            return vehicleStatusDto;
        }
        FdTcpClient client = this.manager.getFdTcpClient(vin);
        if (client == null) {
            return null;
        }
        vehicleStatusDto.setConnectStatus("1");
        if (client.isVirtualVehicle()) {
            try {
                vehicleStatusDto.setConnectStatus("1");
                ScriptHandler handler = o(initializeUiDto);
                FdXmlLogger xmlLogger = client.getXmlLogger();
                handler.callStatusMainModule(xmlLogger);
            } catch (Exception e) {
            }
        } else {
            if (client == null || client.isInitSocket()) {
                vehicleStatusDto.setConnectStatus("0");
                return vehicleStatusDto;
            }
            try {
                if (client.isClose()) {
                    vehicleStatusDto.setConnectStatus("0");
                    return vehicleStatusDto;
                }
                ScriptHandler handler2 = o(initializeUiDto);
                FdXmlLogger xmlLogger2 = client.getXmlLogger();
                handler2.callStatusMainModule(xmlLogger2);
            } catch (DoipException e2) {
                vehicleStatusDto.setConnectStatus("0");
                return vehicleStatusDto;
            } catch (Exception e3) {
                if (client == null || client.isInitSocket()) {
                    vehicleStatusDto.setConnectStatus("0");
                    return vehicleStatusDto;
                }
                try {
                    if (client.isClose()) {
                        vehicleStatusDto.setConnectStatus("0");
                        return vehicleStatusDto;
                    }
                } catch (DoipException e4) {
                }
            }
        }
        Object vehicleVoltage = this.manager.getGlobal("vehicleVoltage", vin);
        Object vehicleUsage = this.manager.getGlobal("vehicleUsage", vin);
        Object hvStatus = this.manager.getGlobal("HV_Status", vin);
        Object drivingDistance = this.manager.getGlobal(GlobalVariableEnum.mc, vin);
        try {
            if (vehicleVoltage == null) {
                vehicleStatusDto.setVehicleVoltage("");
            } else {
                BigDecimal decimal = new BigDecimal(vehicleVoltage.toString()).setScale(1, 1);
                vehicleStatusDto.setVehicleVoltage(decimal.toString());
            }
            String usage = vehicleUsage == null ? "" : vehicleUsage.toString();
            if (StringUtils.isNotBlank(usage) && !"zh-CN".equals(HttpUtils.getLanguage())) {
                usage = LanguageEnum.replaceUsage(usage);
            }
            vehicleStatusDto.setVehicleUsage(usage);
            if (drivingDistance == null) {
                vehicleStatusDto.setDrivingDistance("");
            } else {
                vehicleStatusDto.setDrivingDistance(Integer.toString(CommonFunctionsUtils.stringToUnsignedInt(drivingDistance.toString(), 16)));
            }
        } catch (Exception e5) {
            log.info("读状态出错", e5);
        }
        if (hvStatus != null) {
            try {
                String hvStatusString = hvStatus.toString();
                if (StringUtils.isNotBlank(hvStatusString) && !"zh-CN".equals(HttpUtils.getLanguage())) {
                    hvStatusString = LanguageEnum.replaceUsage(hvStatusString);
                }
                vehicleStatusDto.setHvStatus(hvStatusString);
            } catch (Exception e6) {
                log.info("读高压状态出错", e6);
            }
        }
        log.info("执行002脚本, 行号244耗时{}毫秒", Long.valueOf(System.currentTimeMillis() - start));
        return vehicleStatusDto;
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public VehicleStatusDto statusDsa(InitializeUiDto initializeUiDto) {
        log.info("进入statusReadout");
        String vin = initializeUiDto.getVin();
        VehicleStatusDto vehicleStatusDto = new VehicleStatusDto();
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        if (client == null) {
            return null;
        }
        vehicleStatusDto.setConnectStatus("1");
        if (client == null || client.isInitSocket()) {
            vehicleStatusDto.setConnectStatus("0");
            return vehicleStatusDto;
        }
        try {
            if (client.isClose()) {
                vehicleStatusDto.setConnectStatus("0");
                return vehicleStatusDto;
            }
        } catch (DoipException e) {
            vehicleStatusDto.setConnectStatus("0");
        } catch (Exception e2) {
            if (client == null || client.isInitSocket()) {
                vehicleStatusDto.setConnectStatus("0");
                return vehicleStatusDto;
            }
            try {
                if (client.isClose()) {
                    vehicleStatusDto.setConnectStatus("0");
                    return vehicleStatusDto;
                }
            } catch (DoipException e3) {
            }
        }
        return vehicleStatusDto;
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public VehicleStatusDto statusReadoutDsa(InitializeUiDto initializeUiDto) {
        String vin = initializeUiDto.getVin();
        VehicleStatusDto vehicleStatusDto = new VehicleStatusDto();
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        if (client == null) {
            return null;
        }
        vehicleStatusDto.setConnectStatus("1");
        if (client == null || client.isInitSocket()) {
            vehicleStatusDto.setConnectStatus("0");
            return vehicleStatusDto;
        }
        try {
        } catch (DoipException e) {
            vehicleStatusDto.setConnectStatus("0");
            return vehicleStatusDto;
        } catch (Exception e2) {
            if (client == null || client.isInitSocket()) {
                vehicleStatusDto.setConnectStatus("0");
                return vehicleStatusDto;
            }
            try {
                if (client.isClose()) {
                    vehicleStatusDto.setConnectStatus("0");
                    return vehicleStatusDto;
                }
            } catch (DoipException e3) {
            }
        }
        if (client.isClose()) {
            vehicleStatusDto.setConnectStatus("0");
            return vehicleStatusDto;
        }
        ScriptHandler handler = o(initializeUiDto);
        FdXmlLogger xmlLogger = client.getXmlLogger();
        handler.callStatusMainModule(xmlLogger);
        Object vehicleVoltage = this.manager.getGlobal("vehicleVoltage", vin);
        Object vehicleUsage = this.manager.getGlobal("vehicleUsage", vin);
        Object hvStatus = this.manager.getGlobal("HV_Status", vin);
        try {
            if (vehicleVoltage == null) {
                vehicleStatusDto.setVehicleVoltage("");
            } else {
                BigDecimal decimal = new BigDecimal(vehicleVoltage.toString()).setScale(1, 1);
                vehicleStatusDto.setVehicleVoltage(decimal.toString());
            }
            String usage = vehicleUsage == null ? "" : vehicleUsage.toString();
            if (StringUtils.isNotBlank(usage) && !"zh-CN".equals(HttpUtils.getLanguage())) {
                usage = LanguageEnum.replaceUsage(usage);
            }
            vehicleStatusDto.setVehicleUsage(usage);
        } catch (Exception e4) {
            log.info("读状态出错", e4);
        }
        if (hvStatus != null) {
            try {
                String hvStatusString = hvStatus.toString();
                if (StringUtils.isNotBlank(hvStatusString) && !"zh-CN".equals(HttpUtils.getLanguage())) {
                    hvStatusString = LanguageEnum.replaceUsage(hvStatusString);
                }
                vehicleStatusDto.setHvStatus(hvStatusString);
            } catch (Exception e5) {
                log.info("读高压状态出错", e5);
            }
        }
        return vehicleStatusDto;
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public String initBtnSeq(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        String vin = initializeUiDto.getVin();
        String type = initializeUiDto.getType();
        String username = user.getUsername();
        String seqCode = this.cloud.l(type, vin);
        log.info("seqService initBtnSeq getSeqCodeByBtnCode");
        String args = initializeUiDto.getArgs();
        this.manager.setGlobal(type, seqCode, vin);
        ReloadDto seqBycode = this.cloud.B(seqCode, vin);
        log.info("seqService initBtnSeq getSeqBycode");
        String seq = seqBycode.getSeqContentString();
        String version = seqBycode.getVersion();
        log.info("seqService initBtnSeq client.geTxtLogger().write");
        if (StringUtils.isBlank(seq)) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
        }
        ScriptHandler handler = new ScriptHandler(seq, vin, args, seqCode, version, username, this.tenantName, user.getUser().getServiceStationCode(), seqBycode.getRequestId());
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        return handler.getInitializeUi();
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public String initStatusReadout(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        String seqCode;
        ScriptHandler handler;
        String vin = initializeUiDto.getVin();
        String type = initializeUiDto.getType();
        String args = initializeUiDto.getArgs();
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        if (TesterLoginUtils.isOnLine()) {
            seqCode = this.cloud.l(type, vin);
            ReloadDto seqBycode = this.cloud.B(seqCode, vin);
            String seq = seqBycode.getSeqContentString();
            String version = seqBycode.getVersion();
            if (StringUtils.isBlank(seq)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
            }
            handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, true, version, user.getUsername(), this.tenantName, user.getUser().getServiceStationCode(), seqBycode.getRequestId());
        } else {
            seqCode = TesterSeqCodeEnum.OFF_LINE_VEHICLE_STATUS.getValue();
            String filePath = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
            }
            handler = new ScriptHandler(new File(filePath), vin, args, seqCode, initializeParams, (Boolean) true, "1.0", user.getUsername(), "");
        }
        if (StringUtils.isNotBlank(type)) {
            this.manager.setGlobal(type, seqCode, vin);
        }
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        return handler.getInitializeUi();
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public Integer getSeqStatus(String username) throws Exception {
        int res = 0;
        Set<String> vinList = this.manager.getVehicles(username);
        if (CollectionUtils.isEmpty(vinList)) {
            return 0;
        }
        log.info("seqService getSeqStatus manager.getVehicles");
        Iterator<String> it = vinList.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            String vin = it.next();
            if (!com.geely.gnds.ruoyi.common.utils.StringUtils.isEmpty(vin)) {
                AtomicInteger seqProcessCount = (AtomicInteger) this.manager.getGlobal("seqProcessCount", vin);
                if (seqProcessCount == null) {
                    seqProcessCount = new AtomicInteger();
                }
                if (seqProcessCount.get() != 0) {
                    res = 1;
                    break;
                }
            }
        }
        log.info("seqService getSeqStatus for(String vin:vinList)");
        return Integer.valueOf(res);
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public Integer getSeqStatus(String username, String vin) throws Exception {
        int res = 0;
        if (StringUtils.isNotBlank(vin)) {
            AtomicInteger seqProcessCount = (AtomicInteger) this.manager.getGlobal("seqProcessCount", vin);
            if (seqProcessCount == null) {
                seqProcessCount = new AtomicInteger();
            }
            if (seqProcessCount.get() != 0) {
                res = 1;
            }
            return Integer.valueOf(res);
        }
        Set<String> vinList = this.manager.getVehicles(username);
        if (CollectionUtils.isEmpty(vinList)) {
            return 0;
        }
        log.info("seqService getSeqStatus manager.getVehicles");
        Iterator<String> it = vinList.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            String vins = it.next();
            if (!com.geely.gnds.ruoyi.common.utils.StringUtils.isEmpty(vins)) {
                AtomicInteger seqProcessCount2 = (AtomicInteger) this.manager.getGlobal("seqProcessCount", vins);
                if (seqProcessCount2 == null) {
                    seqProcessCount2 = new AtomicInteger();
                }
                if (seqProcessCount2.get() != 0) {
                    res = 1;
                    break;
                }
            }
        }
        log.info("seqService getSeqStatus for(String vin:vinList)");
        return Integer.valueOf(res);
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public void handleUiData(InitializeUiDto initializeUiDto) throws Exception {
        ScriptHandler handler = o(initializeUiDto);
        handler.setClickUi(initializeUiDto);
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public ScriptHandler getScriptHandlerFun(InitializeUiDto initializeUiDto) throws Exception {
        ScriptHandler handler = o(initializeUiDto);
        return handler;
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public SeqUiDto initDsaSeq(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        String vin = initializeUiDto.getVin();
        File file = new File(initializeUiDto.getFilePath());
        if (file == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00112));
        }
        String seq = FileUtil.readString(file, StandardCharsets.UTF_8);
        String args = initializeUiDto.getArgs();
        String seqCode = cy(seq);
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        ScriptHandler handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, "1.0", user.getUsername(), this.tenantName, user.getUser().getServiceStationCode(), "");
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        SeqUiDto seqUiDto = new SeqUiDto();
        seqUiDto.setInitUi(handler.getInitializeUi());
        seqUiDto.setSeqCode(seqCode);
        return seqUiDto;
    }

    private String cy(String seq) {
        JSONObject jsonObject = JSONObject.parseObject(seq);
        return jsonObject.getString("GRINumber");
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public SeqUiDto initDsaSeqBrowser(String vin, MultipartFile file, LoginUser user) throws Exception {
        if (file == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00112));
        }
        String seq = new String(file.getBytes());
        String seqCode = cy(seq);
        ScriptHandler handler = new ScriptHandler(seq, vin, "", seqCode, "", "1.0", user.getUsername(), this.tenantName, user.getUser().getServiceStationCode(), "");
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        SeqUiDto seqUiDto = new SeqUiDto();
        seqUiDto.setInitUi(handler.getInitializeUi());
        seqUiDto.setSeqCode(seqCode);
        return seqUiDto;
    }

    @Override // com.geely.gnds.tester.service.SeqService
    public String initDsaCloudSeq(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        String seq;
        String version;
        ScriptHandler handler;
        String vin = initializeUiDto.getVin();
        String type = initializeUiDto.getType();
        String args = initializeUiDto.getArgs();
        String seqCode = initializeUiDto.getSeqCode();
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        DsaSequenceEntity dsaSequenceEntity = this.eK.selectByBtnCode(type);
        String username = user.getUsername();
        if (TesterLoginUtils.isOnLine()) {
            if (StringUtils.isBlank(seqCode)) {
                seqCode = this.cloud.a(type, vin, (Boolean) true);
            }
            ReloadDto seqBycode = this.cloud.C(seqCode, vin);
            seq = seqBycode.getSeqContentString();
            version = seqBycode.getVersion();
            if (StringUtils.isBlank(seq)) {
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
            }
            if (StringUtils.isNotBlank(type)) {
                if (dsaSequenceEntity == null) {
                    DsaSequenceEntity dsaSequenceEntity2 = new DsaSequenceEntity();
                    dsaSequenceEntity2.setButtonName(type);
                    dsaSequenceEntity2.setSequenceCode(seqCode);
                    if ("zh-CN".equalsIgnoreCase(HttpUtils.getLanguage())) {
                        dsaSequenceEntity2.setSeqcontentCn(seq);
                    } else {
                        dsaSequenceEntity2.setSeqcontentEn(seq);
                    }
                    dsaSequenceEntity2.setVersion(version);
                    dsaSequenceEntity2.setUpdateTime(new Date());
                    dsaSequenceEntity2.setUpdateBy(username);
                    dsaSequenceEntity2.setCreateTime(new Date());
                    dsaSequenceEntity2.setCreateBy(username);
                    this.eK.create(dsaSequenceEntity2);
                } else if (!version.equalsIgnoreCase(dsaSequenceEntity.getVersion()) || !seqCode.equalsIgnoreCase(dsaSequenceEntity.getSequenceCode())) {
                    dsaSequenceEntity.setSequenceCode(seqCode);
                    if ("zh-CN".equalsIgnoreCase(HttpUtils.getLanguage())) {
                        dsaSequenceEntity.setSeqcontentCn(seq);
                    } else {
                        dsaSequenceEntity.setSeqcontentEn(seq);
                    }
                    dsaSequenceEntity.setVersion(version);
                    dsaSequenceEntity.setUpdateTime(new Date());
                    dsaSequenceEntity.setUpdateBy(username);
                    this.eK.update(dsaSequenceEntity);
                }
            }
        } else if (dsaSequenceEntity != null) {
            seqCode = dsaSequenceEntity.getSequenceCode();
            if ("zh-CN".equalsIgnoreCase(HttpUtils.getLanguage())) {
                seq = dsaSequenceEntity.getSeqcontentCn();
            } else {
                seq = dsaSequenceEntity.getSeqcontentEn();
            }
            version = dsaSequenceEntity.getVersion();
        } else {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
        }
        if (StringUtils.isNotBlank(type)) {
            this.manager.setGlobal(type, seqCode, vin);
        }
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        if (client == null) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115);
            throw new DoipException(formatMsg);
        }
        FdDsaLogger fdDsaLogger = client.getFdDsaLogger();
        if (SeqButtonTypeEnum.DSA_STATUS_READOUT.equals(initializeUiDto.getType())) {
            handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, version, username, this.tenantName, user.getUser().getServiceStationCode(), true, true, null, "");
        } else {
            handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, version, username, this.tenantName, user.getUser().getServiceStationCode(), true, false, fdDsaLogger, "");
        }
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        return handler.getInitializeUi();
    }

    public void a(String vin, String userName, Integer platformCode, boolean dsa) {
        log.info("initSocketSeq入参：vin：{}，userName：{}，platformCode：{}", new Object[]{vin, userName, platformCode});
        this.testerThread.getPool().execute(() -> {
            try {
                String type = SeqButtonTypeEnum.CONNECT_AUTHENTICATION.getValue();
                if (dsa) {
                    type = SeqButtonTypeEnum.DSA_CONNECT_AUTHENTICATION.getValue();
                } else if (platformCode.intValue() != 2 && platformCode.intValue() != 3) {
                    return;
                }
                FdTcpClient fdTcpClient = this.manager.getFdTcpClient(vin);
                if (fdTcpClient != null) {
                    ThreadLocalUtils.CURRENT_USER_NAME.set(userName);
                    InitializeUiDto initializeUiDto = new InitializeUiDto();
                    initializeUiDto.setType(type);
                    initializeUiDto.setVin(vin);
                    Object global = this.manager.getGlobal(type, vin);
                    String seqCode = global == null ? "" : global.toString();
                    if (StringUtils.isBlank(seqCode)) {
                        seqCode = this.cloud.l(type, vin);
                        this.manager.setGlobal(type, seqCode, vin);
                    }
                    ReloadDto seqBycode = this.cloud.B(seqCode, vin);
                    String seq = seqBycode.getSeqContentString();
                    String version = seqBycode.getVersion();
                    if (StringUtils.isBlank(seq)) {
                        throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
                    }
                    ScriptHandler handler = new ScriptHandler(seq, vin, "", seqCode, StrFormatter.EMPTY_JSON, false, version, userName, this.tenantName, "", seqBycode.getRequestId());
                    if (StringUtils.isNotBlank(type)) {
                        this.manager.setGlobal(type, seqCode, vin);
                    }
                    SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
                    FdXmlLogger xmlLogger = fdTcpClient.getXmlLogger();
                    handler.callMainModule(xmlLogger);
                }
            } catch (Exception e) {
                log.error("执行连车脚本失败", e);
            }
        });
    }
}
