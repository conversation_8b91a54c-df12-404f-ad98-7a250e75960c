package com.geely.gnds.tester.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DiaEcuWdidDTO;
import com.geely.gnds.tester.dto.DiagnosisEcuDto;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.EcuReadoutDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.EcuReadoutEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.EcuService;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.ParseUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: EcuServiceImpl.class */
public class EcuServiceImpl implements EcuService {

    @Autowired
    private Cloud cloud;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(EcuServiceImpl.class);
    private static final String pg = "vehicle_readout";

    @Autowired
    private TokenService tokenService;

    @Override // com.geely.gnds.tester.service.EcuService
    public List<ReloadDto> getSeqByEcu(String ecuName, String vin) throws Exception {
        log.info("======> 开始调用云端 cloud.getSeqByEcu接口 <======");
        String seqByEcu = this.cloud.m(ecuName, vin, this.cloud.an(vin));
        log.info("======> 调用云端 cloud.getSeqByEcu接口结束 <======");
        return ObjectMapperUtils.jsonStr2List(seqByEcu, ReloadDto.class);
    }

    @Override // com.geely.gnds.tester.service.EcuService
    public List<EcuBroadcastDto> getEcuList(String vin) throws Exception {
        log.info("======> 开始执行getEcuList方法 <======");
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        List<String> vins = new ArrayList<>();
        vins.add(vin);
        log.info("======> 开始调用云端 cloud.getVehicleInfo接口 <======");
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String data = this.cloud.c(vins, loginUser.getUsername());
        log.info("======> 调用云端 cloud.getVehicleInfo接口结束 <======");
        ObjectMapper mapper = new ObjectMapper();
        List<Object> broadcasts = Arrays.asList((Object[]) mapper.readValue(data, Object[].class));
        if (CollectionUtils.isEmpty(broadcasts)) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00118));
        }
        String broadcast = ObjectMapperUtils.obj2JsonStr(broadcasts.get(0));
        Object strByJsonNodeExpr = ObjectMapperUtils.findObjByJsonNodeExpr(broadcast, "/vehicleData/ecus");
        List<EcuBroadcastDto> ecusByBroadcast = ObjectMapperUtils.jsonStr2List(strByJsonNodeExpr.toString(), EcuBroadcastDto.class);
        Object ecuList = this.manager.getGlobal("DTC_info", vin);
        List<DiagnosisEcuDto> ecusByVehicle = ParseUtils.parseDtcInfo(ecuList == null ? "" : ecuList.toString());
        Object ecuListByReadout = this.manager.getGlobal(pg, vin);
        String s = StringUtils.isBlank((String) ecuListByReadout) ? StrFormatter.EMPTY_JSON : (String) ecuListByReadout;
        Map<String, Object> map = ObjectMapperUtils.jsonStr2Map(s);
        Set<String> ecuNames = map.keySet();
        Map<String, String> diagnosticNumbers = new HashMap<>(ecusByBroadcast.size());
        Map<String, String> ecuNameMap = new HashMap<>(ecusByBroadcast.size());
        log.info("======> 开始调用云端 cloud.getEcuWdidListByVdnMatch接口 <======");
        String ecuWdidListByVdnMatchString = this.cloud.at(vin);
        log.info("======> 调用云端 cloud.getEcuWdidListByVdnMatch接口结束 <======");
        List<DiaEcuWdidDTO> diaEcuWdidList = new ArrayList<>();
        if (com.geely.gnds.ruoyi.common.utils.StringUtils.isNotEmpty(ecuWdidListByVdnMatchString)) {
            diaEcuWdidList = ObjectMapperUtils.jsonStr2List(ecuWdidListByVdnMatchString, DiaEcuWdidDTO.class);
        }
        List<String> ecusByBroadcastList = new ArrayList<>(ecusByBroadcast.size());
        Iterator<EcuBroadcastDto> it = ecusByBroadcast.iterator();
        while (it.hasNext()) {
            ecusByBroadcastList.add(it.next().getEcuName());
        }
        for (String ecuName : ecuNames) {
            if (!ecusByBroadcastList.contains(ecuName)) {
                log.info("ota没有的ecu：{}", ecuName);
                EcuBroadcastDto ecuBroadcastDto = new EcuBroadcastDto();
                ecuBroadcastDto.setEcuName(ecuName);
                ecuBroadcastDto.setEcuFullName(ecuName);
                ecusByBroadcast.add(ecuBroadcastDto);
            }
        }
        log.info("======> 开始处理ecusByBroadcast数据 <======");
        for (EcuBroadcastDto ecuDto : ecusByBroadcast) {
            ecuDto.setReadByVehicle(false);
            ecuDto.setDtcCount(0);
            String number = ecuDto.getDiagnosticPartNumber() == null ? "" : ecuDto.getDiagnosticPartNumber();
            String version = ecuDto.getDiagnosticPartVersion() == null ? "" : ecuDto.getDiagnosticPartVersion();
            ecuDto.setDiagnosticNumber(number + version);
            diaEcuWdidList.stream().filter(d -> {
                return d.getEcuName().equals(ecuDto.getEcuName());
            }).sorted(Comparator.comparing((v0) -> {
                return v0.getId();
            }).reversed()).findFirst().ifPresent(x -> {
                ecuDto.setWdid(x.getWdid());
                ecuDto.setGcid(x.getGcid());
                ecuDto.setLocationName(x.getLocationName());
                ecuDto.setLocationTranName(x.getLocationTranName());
                ecuDto.setGcidName(x.getGcidName());
                ecuDto.setGcidTranName(x.getGcidTranName());
            });
            if (!CollectionUtils.isEmpty(ecusByVehicle)) {
                Iterator<DiagnosisEcuDto> it2 = ecusByVehicle.iterator();
                while (true) {
                    if (!it2.hasNext()) {
                        break;
                    }
                    DiagnosisEcuDto diagnosisEcuDto = it2.next();
                    if (ecuDto.getEcuName().equals(diagnosisEcuDto.getEcuName())) {
                        ecuDto.setEcuAddress(diagnosisEcuDto.getEcuAddress());
                        Map dtcInfo = diagnosisEcuDto.getDtcInfo();
                        List<String> dtcList = ObjectMapperUtils.jsonStr2List(dtcInfo.get("1902_info").toString(), String.class);
                        if (CollectionUtils.isEmpty(dtcList)) {
                            ecuDto.setDtcCount(0);
                        } else {
                            ecuDto.setDtcCount(((List) dtcList.stream().filter(d2 -> {
                                return !d2.startsWith("EF");
                            }).collect(Collectors.toList())).size());
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(ecuNames) && ecuNames.contains(ecuDto.getEcuName())) {
                ecuDto.setReadByVehicle(true);
            }
            diagnosticNumbers.put(ecuDto.getEcuName(), ecuDto.getDiagnosticNumber());
            ecuNameMap.put(vin + "address" + ecuDto.getEcuAddress(), ecuDto.getEcuName());
        }
        log.info("======> 处理ecusByBroadcast数据结束 <======");
        this.manager.setGlobal(GlobalVariableEnum.mb, diagnosticNumbers, vin);
        this.manager.setGlobal("ecuNameMap", ecuNameMap, vin);
        log.info("ECU列表数据：");
        List<String> readFromVehicle = new ArrayList<>();
        List<String> readNotFromVehicle = new ArrayList<>();
        for (EcuBroadcastDto ecuBroadcastDto2 : ecusByBroadcast) {
            log.info("ECU名称：{};诊断零件号：{}", ecuBroadcastDto2.getEcuName(), ecuBroadcastDto2.getDiagnosticNumber());
            if (ecuBroadcastDto2.isReadByVehicle()) {
                readFromVehicle.add(ecuBroadcastDto2.getEcuName() + "(" + ecuBroadcastDto2.getEcuAddress() + ") DiagNo:" + ecuBroadcastDto2.getDiagnosticNumber());
            } else {
                readNotFromVehicle.add("ECU " + ecuBroadcastDto2.getEcuName() + " is present from GNDS but not present in the vehicle");
            }
        }
        Optional.ofNullable(tcpClient).ifPresent(client -> {
            String content = "Current ECUs: " + ecusByBroadcast.size();
            client.geTxtLogger().write(new Date(), "ECUManager", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
            if (!CollectionUtils.isEmpty(readFromVehicle)) {
                readFromVehicle.forEach(t -> {
                    client.geTxtLogger().write(new Date(), "ECUManager", Long.valueOf(System.currentTimeMillis()), "Info", t.getBytes());
                });
            }
            if (!CollectionUtils.isEmpty(readNotFromVehicle)) {
                readNotFromVehicle.forEach(t2 -> {
                    client.geTxtLogger().write(new Date(), "ECUManager", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, t2.getBytes());
                });
            }
        });
        log.info("======> 执行getEcuList方法结束 <======");
        return ecusByBroadcast;
    }

    @Override // com.geely.gnds.tester.service.EcuService
    public List<EcuReadoutDto> getEcuReadout(String vin, String ecuName) throws Exception {
        List<EcuReadoutDto> list = new ArrayList<>();
        Object globalVehicleReadout = this.manager.getGlobal(pg, vin);
        if (ObjectUtils.isEmpty(globalVehicleReadout)) {
            return list;
        }
        Map<String, Object> vehicleReadout = ObjectMapperUtils.jsonStr2Map(globalVehicleReadout.toString());
        if (vehicleReadout.containsKey(ecuName)) {
            Map<String, Object> map = ObjectMapperUtils.jsonStr2Map(ObjectMapperUtils.obj2JsonStr(vehicleReadout.get(ecuName)));
            if (map.containsKey(EcuReadoutEnum.SERIAL_NUMBER.am())) {
                EcuReadoutDto dto = new EcuReadoutDto();
                dto.setName(MessageUtils.getMessage(EcuReadoutEnum.SERIAL_NUMBER.am()));
                dto.setValue(map.get(EcuReadoutEnum.SERIAL_NUMBER.am()).toString());
                list.add(dto);
            }
            if (map.containsKey(EcuReadoutEnum.ASSEMBLY_NUMBER.am())) {
                EcuReadoutDto dto2 = new EcuReadoutDto();
                dto2.setName(MessageUtils.getMessage(EcuReadoutEnum.ASSEMBLY_NUMBER.am()));
                String value = map.get(EcuReadoutEnum.ASSEMBLY_NUMBER.am()) == null ? "" : map.get(EcuReadoutEnum.ASSEMBLY_NUMBER.am()).toString();
                if (com.geely.gnds.ruoyi.common.utils.StringUtils.isNotBlank(value) && value.length() > ConstantEnum.THIRTEEN.intValue()) {
                    dto2.setValue(getValue(value));
                    list.add(dto2);
                }
            }
            if (map.containsKey(EcuReadoutEnum.HARDWARE_NUMBER.am())) {
                EcuReadoutDto dto3 = new EcuReadoutDto();
                dto3.setName(MessageUtils.getMessage(EcuReadoutEnum.HARDWARE_NUMBER.am()));
                String value2 = map.get(EcuReadoutEnum.HARDWARE_NUMBER.am()) == null ? "" : map.get(EcuReadoutEnum.HARDWARE_NUMBER.am()).toString();
                if (com.geely.gnds.ruoyi.common.utils.StringUtils.isNotBlank(value2) && value2.length() > ConstantEnum.THIRTEEN.intValue()) {
                    dto3.setValue(getValue(value2));
                    list.add(dto3);
                }
            }
            if (map.containsKey(EcuReadoutEnum.DIAGNOSIS_NUMBER.am())) {
                EcuReadoutDto dto4 = new EcuReadoutDto();
                dto4.setName(MessageUtils.getMessage(EcuReadoutEnum.DIAGNOSIS_NUMBER.am()));
                String value3 = map.get(EcuReadoutEnum.DIAGNOSIS_NUMBER.am()) == null ? "" : map.get(EcuReadoutEnum.DIAGNOSIS_NUMBER.am()).toString();
                if (com.geely.gnds.ruoyi.common.utils.StringUtils.isNotBlank(value3) && value3.length() > ConstantEnum.THIRTEEN.intValue()) {
                    dto4.setValue(getValue(value3));
                    list.add(dto4);
                }
            }
            if (map.containsKey(EcuReadoutEnum.SOFTWARE_NUMBER.am())) {
                String value4 = map.get(EcuReadoutEnum.SOFTWARE_NUMBER.am()) == null ? "" : map.get(EcuReadoutEnum.SOFTWARE_NUMBER.am()).toString();
                if (com.geely.gnds.ruoyi.common.utils.StringUtils.isNotBlank(value4) && value4.length() > ConstantEnum.FIFTEEN.intValue()) {
                    String str = value4.substring(2);
                    List<String> array = splitString(str, 16);
                    for (String s : array) {
                        EcuReadoutDto dto5 = new EcuReadoutDto();
                        dto5.setName(MessageUtils.getMessage(EcuReadoutEnum.SOFTWARE_NUMBER.am()));
                        dto5.setValue(getValue(s));
                        list.add(dto5);
                    }
                }
            }
        }
        return list;
    }

    public String getValue(String input) {
        String number = input.substring(0, 10);
        String version = input.substring(14);
        char ascii = (char) Integer.parseInt(version, 16);
        String version2 = String.valueOf(ascii);
        return number + "  " + version2;
    }

    public List<String> splitString(String input, int length) {
        List<String> result = new ArrayList<>();
        int k = 0;
        for (int i = 0; i < input.length() / length; i++) {
            String str = input.substring(k, k + length);
            result.add(str);
            k += length;
        }
        return result;
    }
}
