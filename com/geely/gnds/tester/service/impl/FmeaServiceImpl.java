package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DiaGcidCheckDTO;
import com.geely.gnds.tester.dto.DiaGcidCheckDataDTO;
import com.geely.gnds.tester.dto.DiaGcidDtcDTO;
import com.geely.gnds.tester.dto.EcuDto;
import com.geely.gnds.tester.dto.UpgradeDto;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GcidCheckStatusEnum;
import com.geely.gnds.tester.enums.SoftwareTypeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.DtcDataAnalysisService;
import com.geely.gnds.tester.service.DtcService;
import com.geely.gnds.tester.service.FmeaService;
import com.geely.gnds.tester.service.UpgradeService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.vo.DiaCscVO;
import com.geely.gnds.tester.vo.DiaFmeaAndCscVo;
import com.geely.gnds.tester.vo.DiaFmeaVO;
import com.geely.gnds.tester.vo.DiaGcidCheckDataVo;
import com.geely.gnds.tester.vo.DiaGcidVo;
import com.geely.gnds.tester.vo.language.DtcListVo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
/* loaded from: FmeaServiceImpl.class */
public class FmeaServiceImpl implements FmeaService {

    @Autowired
    private DtcDataAnalysisService pd;

    @Autowired
    private DtcService eA;

    @Autowired
    private Cloud cloud;

    @Value("#{${dtcDisDict}}")
    public Map<String, String> dtcDisDict;

    @Autowired
    private UpgradeService jK;

    @Value("${tester.tenantName}")
    private String tenantName;
    private SingletonManager manager = SingletonManager.getInstance();
    public static final String pe = "数据库数据缺失，请联系售后技术管理人员";

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaFmeaVO> getNetworkList(String vin, String customerFunctionType) throws Exception {
        String fmeaStr = this.cloud.w(vin, customerFunctionType);
        if (StringUtils.isEmpty(fmeaStr)) {
            return null;
        }
        List<DiaFmeaVO> networkList = ObjectMapperUtils.jsonStr2List(fmeaStr, DiaFmeaVO.class);
        g(networkList, vin);
        return networkList;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaFmeaVO> getStandardList(String vin, String customerFunctionType) throws Exception {
        String fmeaStr = this.cloud.x(vin, customerFunctionType);
        if (StringUtils.isEmpty(fmeaStr)) {
            return null;
        }
        List<DiaFmeaVO> standardList = ObjectMapperUtils.jsonStr2List(fmeaStr, DiaFmeaVO.class);
        g(standardList, vin);
        return standardList;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaCscVO> getStandardCscList(String vin, Long fmeaId) throws Exception {
        String cscStr = this.cloud.b(vin, fmeaId);
        if (StringUtils.isEmpty(cscStr)) {
            return null;
        }
        List<DiaCscVO> diaCscList = ObjectMapperUtils.jsonStr2List(cscStr, DiaCscVO.class);
        return diaCscList;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaGcidVo> getGcidList(String vin, Long fmeaId) throws Exception {
        String fmeaStr = this.cloud.a(vin, fmeaId);
        if (StringUtils.isEmpty(fmeaStr)) {
            return null;
        }
        List<DiaGcidVo> gcidVoList = ObjectMapperUtils.jsonStr2List(fmeaStr, DiaGcidVo.class);
        if (CollectionUtils.isEmpty(gcidVoList)) {
            return null;
        }
        a(gcidVoList, vin, fmeaId);
        return gcidVoList;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaGcidVo> getStandardGcidList(String vin, Long fmeaId, String csc) throws Exception {
        String standardGcidStr = this.cloud.a(vin, fmeaId, csc);
        if (StringUtils.isEmpty(standardGcidStr)) {
            return null;
        }
        List<DiaGcidVo> gcidVoList = ObjectMapperUtils.jsonStr2List(standardGcidStr, DiaGcidVo.class);
        if (CollectionUtils.isEmpty(gcidVoList)) {
            return null;
        }
        a(gcidVoList, vin, fmeaId);
        return gcidVoList;
    }

    private List<DiaFmeaVO> g(List<DiaFmeaVO> diaFmeaList, String vin) throws Exception {
        Object ecuList = this.manager.getGlobal("DTC_info", vin);
        if (!CollectionUtils.isEmpty(diaFmeaList)) {
            diaFmeaList.stream().forEach(x -> {
                x.setHasFault(Boolean.FALSE);
            });
        }
        if (ObjectUtils.isEmpty(ecuList) || CollectionUtils.isEmpty(diaFmeaList)) {
            return diaFmeaList;
        }
        List<Long> fmeaIds = (List) diaFmeaList.stream().map((v0) -> {
            return v0.getId();
        }).collect(Collectors.toList());
        String fmeaDtcList = this.cloud.c(fmeaIds, new ArrayList());
        if (StringUtils.isNotEmpty(fmeaDtcList)) {
            List<DiaGcidDtcDTO> diaGcidDtcList = ObjectMapperUtils.jsonStr2List(fmeaDtcList, DiaGcidDtcDTO.class);
            Map<Long, List<DiaGcidDtcDTO>> fmeaDtcMap = (Map) diaGcidDtcList.stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getFmeaId();
            }, Collectors.toList()));
            List<DtcInfoDTO> dtcInfoList = this.pd.analysisDtcList(ecuList.toString());
            diaFmeaList.forEach(d -> {
                List<DiaGcidDtcDTO> tempDiaGcidDtcDTO = (List) fmeaDtcMap.get(d.getId());
                if (!CollectionUtils.isEmpty(tempDiaGcidDtcDTO)) {
                    dtcInfoList.forEach(dtc -> {
                        tempDiaGcidDtcDTO.forEach(t -> {
                            if (t.getEcuName().equals(dtc.getEcuName()) && t.getDtcId().equals(dtc.getId())) {
                                d.setHasFault(Boolean.TRUE);
                            }
                        });
                    });
                }
            });
        }
        return diaFmeaList;
    }

    private List<DiaGcidVo> a(List<DiaGcidVo> gcidList, String vin, Long fmeaId) throws Exception {
        Object ecuList = this.manager.getGlobal("DTC_info", vin);
        if (CollectionUtils.isEmpty(gcidList) || ObjectUtils.isEmpty(ecuList)) {
            return gcidList;
        }
        List<Long> gcidIds = (List) gcidList.stream().map((v0) -> {
            return v0.getId();
        }).collect(Collectors.toList());
        String gcidDtcList = this.cloud.c(Arrays.asList(fmeaId), gcidIds);
        if (StringUtils.isNotEmpty(gcidDtcList)) {
            List<DiaGcidDtcDTO> diaGcidDtcList = ObjectMapperUtils.jsonStr2List(gcidDtcList, DiaGcidDtcDTO.class);
            List<DtcInfoDTO> dtcInfoList = this.pd.analysisDtcList(ecuList.toString());
            Set<String> dtcInfoSet = (Set) dtcInfoList.stream().map((v0) -> {
                return v0.getId();
            }).collect(Collectors.toSet());
            List<DiaGcidDtcDTO> intersectionList = (List) diaGcidDtcList.stream().filter(gcidDtc -> {
                return dtcInfoSet.contains(gcidDtc.getDtcId());
            }).collect(Collectors.toList());
            List<String> dtcIds = (List) intersectionList.stream().map((v0) -> {
                return v0.getDtcId();
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dtcIds)) {
                return gcidList;
            }
            DtcListVo vo = new DtcListVo();
            vo.setDtcIds((String[]) dtcIds.toArray(new String[0]));
            vo.setLanguage(HttpUtils.getLanguage());
            String dtcListByIds = this.cloud.a(vo);
            Map<String, Object> dtcMap = ObjectMapperUtils.jsonStr2Map(dtcListByIds);
            intersectionList.forEach(i -> {
                gcidList.forEach(g -> {
                    if (g.getId().equals(i.getGcidId()) && dtcMap.containsKey(i.getDtcId())) {
                        String name = "";
                        Object v = dtcMap.get(i.getDtcId());
                        if (!ObjectUtils.isEmpty(v)) {
                            String value = v == null ? "" : ObjectMapperUtils.obj2JsonStr(v);
                            Map<String, Object> info = ObjectMapperUtils.jsonStr2Map(value);
                            if (StringUtils.isNotEmpty(info.get("name").toString())) {
                                name = info.get("name").toString();
                            }
                        }
                        if (StringUtils.isBlank(name)) {
                            name = "数据库数据缺失，请联系售后技术管理人员";
                        }
                        String dtcId = i.getDtcId();
                        g.setDtcId(dtcId);
                        String key = dtcId.substring(0, 1);
                        if (this.dtcDisDict.containsKey(key)) {
                            String vaule = this.dtcDisDict.get(key) + dtcId.substring(1);
                            g.setWindowMessage(i.getEcuName() + "-" + vaule + ConstantEnum.EMPTY + name);
                        }
                    }
                });
            });
        }
        return gcidList;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public void updateScore(int type, Long fmeaId, Long gcId, String csc) throws Exception {
        this.cloud.a(type, fmeaId, gcId, csc);
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaFmeaVO> getDtcFmeaList(String vin, String dtcId, String ecuName) throws Exception {
        String fmeaStr = this.cloud.t(vin, dtcId, ecuName);
        if (StringUtils.isEmpty(fmeaStr)) {
            return null;
        }
        List<DiaFmeaVO> networkList = ObjectMapperUtils.jsonStr2List(fmeaStr, DiaFmeaVO.class);
        return networkList;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaFmeaAndCscVo> getDtcFemaFunctionList(String vin, String dtcId, String diagnosticNumber) throws Exception {
        String fmeaStr = this.cloud.u(vin, dtcId, diagnosticNumber);
        if (StringUtils.isEmpty(fmeaStr)) {
            return null;
        }
        List<DiaFmeaAndCscVo> networkList = ObjectMapperUtils.jsonStr2List(fmeaStr, DiaFmeaAndCscVo.class);
        return networkList;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DtcInfoDTO> getDtcList(String listType, String vin, Long fmeaId, String dtcId) throws Exception {
        List<DtcInfoDTO> dtcList = this.eA.getDtcList(null, listType, vin, null);
        if (CollectionUtils.isEmpty(dtcList)) {
            return dtcList;
        }
        String fmeaDtcList = this.cloud.c(Arrays.asList(fmeaId), new ArrayList());
        if (StringUtils.isNotEmpty(fmeaDtcList)) {
            List<DiaGcidDtcDTO> diaGcidDtcList = ObjectMapperUtils.jsonStr2List(fmeaDtcList, DiaGcidDtcDTO.class);
            Map<Long, List<DiaGcidDtcDTO>> fmeaDtcMap = (Map) diaGcidDtcList.stream().collect(Collectors.groupingBy((v0) -> {
                return v0.getFmeaId();
            }, Collectors.toList()));
            List<DiaGcidDtcDTO> diaGcidDtcDtos = fmeaDtcMap.get(fmeaId);
            if (!CollectionUtils.isEmpty(diaGcidDtcDtos)) {
                Set<String> dtcIds = (Set) diaGcidDtcDtos.stream().map((v0) -> {
                    return v0.getDtcId();
                }).collect(Collectors.toSet());
                dtcList.stream().forEach(x -> {
                    if (x.getId().equals(dtcId) && dtcIds.contains(x.getId())) {
                        x.setIsBold(true);
                    }
                });
            }
        }
        dtcList.sort(Comparator.comparing((v0) -> {
            return v0.getIsBold();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).thenComparing((v0) -> {
            return v0.getStatus();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).reversed().thenComparing((v0) -> {
            return v0.getId();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })).thenComparing((v0) -> {
            return v0.getDtcValue();
        }, Comparator.nullsLast((v0, v1) -> {
            return v0.compareTo(v1);
        })));
        return dtcList;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaGcidCheckDataVo> getGcidCheckdata(DiaGcidCheckDTO dto) throws Exception {
        List<DiaGcidCheckDataDTO> diaGcidCheckDatas = dto.getDiaGcidCheckDatas();
        ArrayList arrayList = new ArrayList();
        List<DiaGcidCheckDataVo> diaGcidCheckExceptionDataVos = new ArrayList<>();
        Map<Long, Integer> scoreMaps = new HashMap<>();
        Map<Long, Boolean> booleanMap = new HashMap<>();
        UpgradeDto query = this.jK.query(dto.getVin(), SoftwareTypeEnum.TARGET_BSS, null);
        if (!ObjectUtils.isEmpty(diaGcidCheckDatas)) {
            diaGcidCheckDatas.stream().forEach(it -> {
                DiaGcidCheckDataVo diaGcidCheckDataVo = new DiaGcidCheckDataVo();
                diaGcidCheckDataVo.setId(it.getId());
                diaGcidCheckDataVo.setHistoryFailureRate(it.getHistoryFailureRate());
                diaGcidCheckDataVo.setWdid(it.getWdid());
                List<DtcInfoDTO> dtcList = null;
                try {
                    dtcList = getDtcList(String.valueOf(ConstantEnum.ONE), dto.getVin(), it.getFmeaId(), it.getDtcId());
                } catch (Exception e) {
                }
                if (!CollectionUtils.isEmpty(dtcList)) {
                    List<DtcInfoDTO> dtcInfoList = (List) dtcList.stream().filter(it2 -> {
                        return ObjectUtils.nullSafeEquals(it2.getEcuName(), it.getEcu());
                    }).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(dtcInfoList)) {
                        diaGcidCheckDataVo.setDtcStatus(GcidCheckStatusEnum.EXCEPTION.ao());
                        scoreMaps.put(it.getId(), Integer.valueOf(dtcInfoList.size()));
                    } else {
                        diaGcidCheckDataVo.setDtcStatus(GcidCheckStatusEnum.NORMAL.ao());
                    }
                } else {
                    diaGcidCheckDataVo.setDtcStatus(GcidCheckStatusEnum.NORMAL.ao());
                }
                if (!ObjectUtils.isEmpty(query)) {
                    List<EcuDto> ecuInstallationInstructions = query.getEcuInstallationInstructions();
                    if (!CollectionUtils.isEmpty(ecuInstallationInstructions)) {
                        ecuInstallationInstructions.stream().forEach(it22 -> {
                            if (!ObjectUtils.nullSafeEquals(it22.getEcuName(), it.getEcu()) || !ObjectUtils.nullSafeEquals(it22.getHardwareStatus(), "1") || it22.getVbfSize().longValue() <= ConstantEnum.ZERO.intValue()) {
                                if (StringUtils.isEmpty(it.getEcu())) {
                                    booleanMap.put(it.getId(), true);
                                    return;
                                }
                                return;
                            }
                            booleanMap.put(it.getId(), false);
                            Integer integer = (Integer) scoreMaps.get(it.getId());
                            if (!ObjectUtils.isEmpty(integer)) {
                                scoreMaps.put(it.getId(), Integer.valueOf(integer.intValue() + 2));
                            } else {
                                scoreMaps.put(it.getId(), 2);
                            }
                        });
                    }
                }
                Boolean aBoolean = (Boolean) booleanMap.get(it.getId());
                if (ObjectUtils.isEmpty(aBoolean) || aBoolean.booleanValue()) {
                    diaGcidCheckDataVo.setSoftwareStatus(GcidCheckStatusEnum.NORMAL.ao());
                } else {
                    diaGcidCheckDataVo.setSoftwareStatus(GcidCheckStatusEnum.EXCEPTION.ao());
                }
                Integer score = (Integer) scoreMaps.get(it.getId());
                if (!ObjectUtils.isEmpty(score)) {
                    diaGcidCheckDataVo.setGrade(score);
                } else {
                    diaGcidCheckDataVo.setGrade(ConstantEnum.ZERO);
                }
                if (ObjectUtils.nullSafeEquals(diaGcidCheckDataVo.getDtcStatus(), GcidCheckStatusEnum.EXCEPTION.ao()) && ObjectUtils.nullSafeEquals(diaGcidCheckDataVo.getSoftwareStatus(), GcidCheckStatusEnum.EXCEPTION.ao())) {
                    diaGcidCheckExceptionDataVos.add(diaGcidCheckDataVo);
                } else {
                    arrayList.add(diaGcidCheckDataVo);
                }
            });
        }
        diaGcidCheckExceptionDataVos.sort(new Comparator<DiaGcidCheckDataVo>() { // from class: com.geely.gnds.tester.service.impl.FmeaServiceImpl.1
            @Override // java.util.Comparator
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public int compare(DiaGcidCheckDataVo o1, DiaGcidCheckDataVo o2) {
                if (o1.getGrade().intValue() > o2.getGrade().intValue()) {
                    return -1;
                }
                if (o1.getGrade().intValue() < o2.getGrade().intValue()) {
                    return 1;
                }
                return 0;
            }
        });
        arrayList.sort(new Comparator<DiaGcidCheckDataVo>() { // from class: com.geely.gnds.tester.service.impl.FmeaServiceImpl.2
            @Override // java.util.Comparator
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public int compare(DiaGcidCheckDataVo o1, DiaGcidCheckDataVo o2) {
                if (o1.getGrade().intValue() > o2.getGrade().intValue()) {
                    return -1;
                }
                if (o1.getGrade().intValue() < o2.getGrade().intValue()) {
                    return 1;
                }
                return 0;
            }
        });
        diaGcidCheckExceptionDataVos.addAll(arrayList);
        if ("MADS".equalsIgnoreCase(this.tenantName)) {
            diaGcidCheckExceptionDataVos.sort(Comparator.comparing(diaGcidCheckDataVo -> {
                return Boolean.valueOf(StringUtils.isBlank(diaGcidCheckDataVo.getWdid()));
            }).thenComparing(diaGcidCheckDataVo2 -> {
                return Boolean.valueOf(diaGcidCheckDataVo2.getWdid() != null && diaGcidCheckDataVo2.getWdid().startsWith("15/"));
            }).thenComparing(diaGcidCheckDataVo3 -> {
                String rate = diaGcidCheckDataVo3.getHistoryFailureRate();
                if (StringUtils.isBlank(rate)) {
                    return Double.valueOf(0.0d);
                }
                return Double.valueOf(Double.parseDouble(rate.replace("%", "")));
            }).reversed());
        }
        for (int i = 0; i < diaGcidCheckExceptionDataVos.size(); i++) {
            diaGcidCheckExceptionDataVos.get(i).setSort(Integer.valueOf(i));
        }
        return diaGcidCheckExceptionDataVos;
    }

    @Override // com.geely.gnds.tester.service.FmeaService
    public List<DiaFmeaAndCscVo> getStandardFemaAndCscList(String vin, String customerFunctionType) throws Exception {
        String fmeaAndCscStr = this.cloud.y(vin, customerFunctionType);
        if (StringUtils.isEmpty(fmeaAndCscStr)) {
            return null;
        }
        List<DiaFmeaAndCscVo> standardList = ObjectMapperUtils.jsonStr2List(fmeaAndCscStr, DiaFmeaAndCscVo.class);
        List<DiaFmeaVO> diaFmeaList = (List) standardList.stream().flatMap(diaFmeaAndCscVo -> {
            return diaFmeaAndCscVo.getFmeaList().stream();
        }).collect(Collectors.toList());
        g(diaFmeaList, vin);
        return standardList;
    }
}
