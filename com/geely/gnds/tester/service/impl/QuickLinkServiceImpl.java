package com.geely.gnds.tester.service.impl;

import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.QuickLinkDTO;
import com.geely.gnds.tester.service.QuickLinkService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: QuickLinkServiceImpl.class */
public class QuickLinkServiceImpl implements QuickLinkService {

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.tester.service.QuickLinkService
    public List<QuickLinkDTO> getList(Long brandId) throws Exception {
        String quickLinkList = this.cloud.b(brandId);
        return ObjectMapperUtils.jsonStr2List(quickLinkList, QuickLinkDTO.class);
    }
}
