package com.geely.gnds.tester.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.compile.ScriptHandler;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.FixSeqDto;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.FixSeqService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
/* loaded from: FixSeqServiceImpl.class */
public class FixSeqServiceImpl implements FixSeqService {
    private static final Logger log = LoggerFactory.getLogger(FixSeqServiceImpl.class);

    @Autowired
    private Cloud cloud;

    @Autowired
    private FileCache fileCache;

    @Value("${tester.tenantName}")
    private String tenantName;
    private SingletonManager manager = SingletonManager.getInstance();

    @Override // com.geely.gnds.tester.service.FixSeqService
    public List queryFixSeqList(String vin) throws Exception {
        log.info("======> 开始执行queryFixSeqList方法 <======");
        List<FixSeqDto> list = new ArrayList<>();
        log.info("======> 开始调用云端接口 cloud.getFixSeqList <======");
        String fix = this.cloud.p(vin, this.cloud.an(vin));
        log.info("======> 调用云端接口 cloud.getFixSeqList结束 <======");
        JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(fix, JsonNode.class);
        if (readValue.isArray()) {
            readValue.forEach(obj -> {
                FixSeqDto fixSeqDto = (FixSeqDto) ObjectMapperUtils.getInstance().convertValue(obj, FixSeqDto.class);
                list.add(fixSeqDto);
            });
        }
        log.info("======> 执行queryFixSeqList方法结束 <======");
        return list;
    }

    @Override // com.geely.gnds.tester.service.FixSeqService
    public String initFlush(InitializeUiDto initializeUiDto, LoginUser user) throws Exception {
        ScriptHandler handler;
        String vin = initializeUiDto.getVin();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        String seqCode = initializeUiDto.getSeqCode();
        String args = initializeUiDto.getArgs();
        String type = initializeUiDto.getType();
        String initializeParams = initializeUiDto.getInitializeParams() == null ? StrFormatter.EMPTY_JSON : ObjectMapperUtils.obj2JsonStr(initializeUiDto.getInitializeParams());
        if (TesterLoginUtils.isOnLine()) {
            if (StringUtils.isBlank(seqCode)) {
                seqCode = this.cloud.l(type, vin);
            }
            ReloadDto seqBycode = this.cloud.B(seqCode, vin);
            String seq = seqBycode.getSeqContentString();
            String version = seqBycode.getVersion();
            if (StringUtils.isBlank(seq)) {
                Optional.ofNullable(tcpClient).ifPresent(client -> {
                    client.geTxtLogger().write(new Date(), "维修序列", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117).getBytes());
                });
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00117));
            }
            handler = new ScriptHandler(seq, vin, args, seqCode, initializeParams, version, user.getUsername(), this.tenantName, user.getUser().getServiceStationCode(), seqBycode.getRequestId());
        } else {
            String filePath = this.fileCache.ag(seqCode);
            if (StringUtils.isBlank(filePath)) {
                Optional.ofNullable(tcpClient).ifPresent(client2 -> {
                    client2.geTxtLogger().write(new Date(), "维修序列", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119).getBytes());
                });
                throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00119));
            }
            handler = new ScriptHandler(new File(filePath), vin, args, seqCode, "1.0", "");
        }
        SeqManager.getInstance().addScriptHandler(vin + seqCode, handler);
        return handler.getInitializeUi();
    }
}
