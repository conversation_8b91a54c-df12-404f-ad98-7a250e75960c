package com.geely.gnds.tester.service.impl;

import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.bean.BeanUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.BroadcastDto;
import com.geely.gnds.tester.dto.BroadcastVehicleDataDTO;
import com.geely.gnds.tester.dto.CloudBroadcastDTO;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.VehicleConfigDTO;
import com.geely.gnds.tester.entity.TesterReadoutCacheEntity;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.ManufacturingYearEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.DtcDataAnalysisService;
import com.geely.gnds.tester.service.ParamService;
import com.geely.gnds.tester.service.ReadoutCacheService;
import com.geely.gnds.tester.service.VehicleDetailsService;
import com.geely.gnds.tester.util.CommonFunctionsUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.vo.VehicleBroadcastVO;
import com.geely.gnds.tester.vo.VehicleConfigVO;
import com.geely.gnds.tester.vo.VehicleMaintainVO;
import com.geely.gnds.tester.vo.language.GetDataIdentifierParam;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: VehicleDetailsServiceImpl.class */
public class VehicleDetailsServiceImpl implements VehicleDetailsService {
    private static final Logger log = LoggerFactory.getLogger(VehicleDetailsServiceImpl.class);

    @Autowired
    private Cloud cloud;

    @Value("#{${vehicleInfo}}")
    private Map<String, String> vehicleInfoConfig;

    @Autowired
    private DtcDataAnalysisService pu;

    @Autowired
    private ReadoutCacheService ji;

    @Autowired
    private ParamService jf;

    @Autowired
    private TokenService tokenService;
    private SingletonManager manager = SingletonManager.getInstance();

    @Override // com.geely.gnds.tester.service.VehicleDetailsService
    public VehicleConfigVO vehicleConfig(VehicleBroadcastVO vehicleBroadcast, String vin) throws Exception {
        TesterReadoutCacheEntity vehicleConfig = this.ji.getVehicleConfig(vin);
        if (ObjectUtils.isEmpty(vehicleConfig)) {
            return null;
        }
        VehicleConfigVO vo = new VehicleConfigVO();
        List<EcuBroadcastDto> ecuBroadcast = vehicleBroadcast.getEcuBroadcast();
        VehicleConfigDTO vehicleConfigDTO = (VehicleConfigDTO) ObjectMapperUtils.jsonStr2Clazz(vehicleConfig.getVehicleConfig(), VehicleConfigDTO.class);
        VehicleConfigDTO.VehicleConfigData config = vehicleConfigDTO.getConfig();
        if (ObjectUtils.isNotEmpty(config)) {
            EcuBroadcastDto configEcu = ecuBroadcast.stream().filter(e -> {
                return e.getEcuName().equals(config.getEcu());
            }).findFirst().orElse(null);
            if (ObjectUtils.isNotEmpty(configEcu)) {
                vo.setConfig(analysisVehicleConfig(vin, configEcu.getDiagnosticPartNumber() + configEcu.getDiagnosticPartVersion(), config.getDid(), config.getValue(), configEcu.getEcuName()));
            }
        }
        VehicleConfigDTO.VehicleConfigData mileage = vehicleConfigDTO.getMileage();
        if (ObjectUtils.isNotEmpty(mileage)) {
            EcuBroadcastDto mileageEcu = ecuBroadcast.stream().filter(e2 -> {
                return e2.getEcuName().equals(mileage.getEcu());
            }).findFirst().orElse(null);
            if (ObjectUtils.isNotEmpty(mileageEcu)) {
                vo.setMileage(analysisVehicleConfig(vin, mileageEcu.getDiagnosticPartNumber() + mileageEcu.getDiagnosticPartVersion(), mileage.getDid(), mileage.getValue(), mileageEcu.getEcuName()));
            }
        }
        vo.setUpdateTime(vehicleConfig.getUpdateTime());
        return vo;
    }

    @Override // com.geely.gnds.tester.service.VehicleDetailsService
    public List<DiagResItemParseResultDto> analysisVehicleConfig(String vin, String diagnosticNumber, String did, String data, String ecuName) throws Exception {
        if (ObjectUtils.isEmpty(data)) {
            return new ArrayList();
        }
        List<DiagResItemGroupDto> didSizeGroups = null;
        try {
            String json = this.cloud.e(vin, diagnosticNumber, "22", ecuName);
            didSizeGroups = ObjectMapperUtils.jsonStr2List(json, DiagResItemGroupDto.class);
        } catch (Exception e) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00084);
            log.error("{},diagnosticNumber={},serviceId={},e=", new Object[]{formatMsg, diagnosticNumber, "22", e});
        }
        List<Long> didList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(didSizeGroups)) {
            didSizeGroups = (List) didSizeGroups.stream().filter(d -> {
                return d.getDataIdentifierId().equals(did);
            }).collect(Collectors.toList());
            didSizeGroups.stream().forEach(x -> {
                x.setDiagnosticPartNumber(diagnosticNumber);
            });
            didList.addAll((Collection) didSizeGroups.stream().map((v0) -> {
                return v0.getId();
            }).collect(Collectors.toList()));
        }
        List<DiagResItemGroupDto> didListResponseItem = null;
        if (CollectionUtils.isEmpty(didList)) {
            return new ArrayList();
        }
        try {
            GetDataIdentifierParam param = new GetDataIdentifierParam();
            param.setDiagnosisPartNum(diagnosticNumber);
            param.setLanguage(HttpUtils.getLanguage());
            param.setDidIds((Long[]) didList.toArray(new Long[0]));
            String didResponseItemJson = this.cloud.a(param);
            didListResponseItem = ObjectMapperUtils.jsonStr2List(didResponseItemJson, DiagResItemGroupDto.class);
        } catch (Exception e2) {
            String formatMsg2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00086);
            log.error("{},diagnosticNumber={},serviceId={},e=", new Object[]{formatMsg2, diagnosticNumber, "22", e2});
        }
        if (CollectionUtils.isEmpty(didSizeGroups) || CollectionUtils.isEmpty(didListResponseItem)) {
            return new ArrayList();
        }
        List<DiagResItemParseResultDto> itemParseResults = new ArrayList<>();
        Optional<DiagResItemGroupDto> thisDid = didListResponseItem.stream().filter(c -> {
            return c.getDataIdentifierId().equals(did);
        }).findFirst();
        thisDid.ifPresent(t -> {
            Map<String, List<DiagResItemDto>> offsetMap = (Map) t.getResponseItemDtoList().stream().sorted(Comparator.comparing((v0) -> {
                return v0.getOffset();
            })).collect(Collectors.groupingBy((v0) -> {
                return v0.getOffset();
            }));
            for (String s : offsetMap.keySet()) {
                try {
                    this.jf.parseResponseByResItem(itemParseResults, t, offsetMap.get(s), data);
                } catch (Exception e3) {
                    log.error("解析应答数据时出现异常,did={},data={},e={}", new Object[]{((DiagResItemGroupDto) thisDid.get()).getDataIdentifierId(), data, e3});
                    return;
                }
            }
        });
        return itemParseResults;
    }

    @Override // com.geely.gnds.tester.service.VehicleDetailsService
    public VehicleBroadcastVO getVehicleBroadcast(String vin) throws Exception {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String data = this.cloud.c(Arrays.asList(vin), loginUser.getUsername());
        List<CloudBroadcastDTO> broadcasts = ObjectMapperUtils.jsonStr2List(data, CloudBroadcastDTO.class);
        if (CollectionUtils.isEmpty(broadcasts)) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00118));
        }
        CloudBroadcastDTO cloudBroadcastDTO = broadcasts.get(0);
        VehicleBroadcastVO vehicleBroadcastVO = new VehicleBroadcastVO();
        List<VehicleBroadcastVO.VehicleVdnData> copyListProperties = BeanUtils.copyListProperties(cloudBroadcastDTO.getVdnData(), VehicleBroadcastVO.VehicleVdnData::new);
        Map<String, List<VehicleBroadcastVO.VehicleVdnData>> mapGroupVdn = (Map) copyListProperties.stream().collect(Collectors.groupingBy((v0) -> {
            return v0.getAttribute();
        }));
        String language = HttpUtils.getLanguage();
        log.info("vdn数据语言：{}", language);
        mapGroupVdn.forEach((k, v) -> {
            VehicleBroadcastVO.VehicleVdnData groupVdn = (VehicleBroadcastVO.VehicleVdnData) v.stream().filter(x -> {
                return x.getValue().equals(k + "00");
            }).findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(groupVdn)) {
                List<VehicleBroadcastVO.VehicleVdnData> child = (List) v.stream().filter(x2 -> {
                    return !x2.getValue().equals(new StringBuilder().append(k).append("00").toString());
                }).collect(Collectors.toList());
                child.forEach(x3 -> {
                    x3.setGroupLd(a(language, groupVdn));
                    x3.setGroupSd(groupVdn.getSd());
                    x3.setGroupsName(groupVdn.getName());
                    x3.setGroupValue(groupVdn.getValue());
                    x3.setGroupAttribute(groupVdn.getAttribute());
                });
                mapGroupVdn.put(k, child);
            }
        });
        List<VehicleBroadcastVO.VehicleVdnData> finalVehicleVdnDatas = (List) mapGroupVdn.values().stream().flatMap((v0) -> {
            return v0.stream();
        }).collect(Collectors.toList());
        vehicleBroadcastVO.setVdnDatas(finalVehicleVdnDatas);
        vehicleBroadcastVO.setEcuBroadcast(cloudBroadcastDTO.getVehicleData().getEcus());
        vehicleBroadcastVO.setGeneralAttribute(a(cloudBroadcastDTO.getVehicleData(), vehicleBroadcastVO.getVdnDatas()));
        return vehicleBroadcastVO;
    }

    @Override // com.geely.gnds.tester.service.VehicleDetailsService
    public void vehicleRealtimeCheck(String vin, String carModel) throws Exception {
        this.cloud.H(vin, carModel);
        log.info("VehicleDetailsServiceImpl#vehicleRealtimeCheck success");
    }

    @Override // com.geely.gnds.tester.service.VehicleDetailsService
    public List<VehicleMaintainVO> vehicleHealthInspection(String vin, String carModel) throws Exception {
        String res = this.cloud.I(vin, carModel);
        log.info("VehicleDetailsServiceImpl#vehicleHealthInspection result:{}", res);
        if (StringUtils.isNotBlank(res)) {
            return ObjectMapperUtils.jsonStr2List(res, VehicleMaintainVO.class);
        }
        return Collections.emptyList();
    }

    @Override // com.geely.gnds.tester.service.VehicleDetailsService
    public void vehicleMaintainReset(String vin, String carModel, List<String> maintainItems) throws Exception {
        this.cloud.a(vin, carModel, maintainItems);
        log.info("VehicleDetailsServiceImpl#vehicleMaintainReset success");
    }

    private String a(String language, VehicleBroadcastVO.VehicleVdnData groupVdn) {
        String res = groupVdn.getLd();
        if (!StringUtils.isNotBlank(language)) {
            return res;
        }
        if (!"zh-CN".equals(language)) {
            res = groupVdn.getSd();
        }
        return StringUtils.isNotBlank(res) ? res : groupVdn.getLd();
    }

    private BroadcastDto a(BroadcastVehicleDataDTO vehicleData, List<VehicleBroadcastVO.VehicleVdnData> vdnDatas) {
        BroadcastDto generalAttribute = new BroadcastDto();
        String vin = vehicleData.getVin();
        generalAttribute.setVin(vin);
        generalAttribute.setStructureWeek(vehicleData.getStructureWeek());
        generalAttribute.setUpdateTime(vehicleData.getUpdateTime());
        generalAttribute.setPno18(vehicleData.getPno18());
        this.vehicleInfoConfig.forEach((k, v) -> {
            vdnDatas.stream().filter(x -> {
                return x.getAttribute().equals(v);
            }).findFirst().ifPresent(z -> {
                try {
                    String ld = z.getLd();
                    String sd = z.getSd();
                    Field declaredField = generalAttribute.getClass().getDeclaredField(k);
                    declaredField.setAccessible(true);
                    if (Objects.equals(k, "manufacturingYear")) {
                        ld = ld.replace("年", "");
                    }
                    if ("zh-CN".equalsIgnoreCase(HttpUtils.getLanguage())) {
                        declaredField.set(generalAttribute, ld);
                    } else {
                        declaredField.set(generalAttribute, sd);
                    }
                } catch (IllegalAccessException | NoSuchFieldException e) {
                    log.error("设置字段出现异常");
                }
            });
        });
        if (ObjectUtils.isEmpty(generalAttribute.getManufacturingYear())) {
            String manufacturingYear = ManufacturingYearEnum.cn(vin.substring(9, 10));
            generalAttribute.setManufacturingYear(manufacturingYear);
            log.info("manufacturingYear:{}转换{}", vin, manufacturingYear);
        }
        generalAttribute.setFyon(vehicleData.getFyon());
        try {
            String bss = this.manager.getGlobalStringDefaultNull(GlobalVariableEnum.md, vin).toString();
            if (StringUtils.isNotBlank(bss)) {
                generalAttribute.setCurrentBssId(CommonFunctionsUtils.hexStringToAscii(CommonFunctionsUtils.hexStringToAscii(bss)));
            }
            String version = this.manager.getGlobalStringDefaultNull(GlobalVariableEnum.me, vin).toString();
            if (StringUtils.isNotBlank(version)) {
                generalAttribute.setCurrentDisplayVersion(CommonFunctionsUtils.hexStringToAscii(CommonFunctionsUtils.hexStringToAscii(version)));
            }
            String driving = this.manager.getGlobalStringDefaultNull(GlobalVariableEnum.mc, vin).toString();
            if (StringUtils.isNotBlank(driving)) {
                generalAttribute.setDrivingDistance(Integer.toString(CommonFunctionsUtils.stringToUnsignedInt(driving, 16)));
            }
        } catch (Exception e) {
            log.error("转换失败", e);
        }
        return generalAttribute;
    }
}
