package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.dto.RemoteSeqExecuteDTO;
import com.geely.gnds.tester.dto.RemoteStatusDTO;
import com.geely.gnds.tester.dto.RemoteTaskDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.vo.DtcDetailedVO;
import java.util.List;

/* loaded from: RemoteDiagnosisService.class */
public interface RemoteDiagnosisService {
    List<RemoteTaskDTO> remoteTask(String str) throws Exception;

    List<DtcInfoDTO> queryDtc(String str, String str2, Integer num) throws Exception;

    List<DiagResItemParseResultDto> queryParam(String str) throws Exception;

    DtcDetailedVO queryDtcDetail(String str, String str2, String str3, String str4, String str5, String str6, String str7) throws Exception;

    List<RemoteTaskDTO> remoteTask3(String str) throws Exception;

    List<DtcInfoDTO> queryDtc3(String str, String str2, Integer num) throws Exception;

    List<DiagResItemParseResultDto> queryParam3(String str) throws Exception;

    DtcDetailedVO queryDtcDetail3(String str, String str2, String str3, String str4, String str5, String str6, String str7) throws Exception;

    RemoteStatusDTO connect(String str, String str2, String str3, String str4, String str5, List<ReloadDto> list, Integer num) throws Exception;

    RemoteStatusDTO readoutParam3(List<DiagResItemGroupDto> list, String str, String str2, String str3, String str4, String str5) throws Exception;

    RemoteStatusDTO remoteControl(String str) throws Exception;

    RemoteStatusDTO readoutDtc(String str, String str2, String str3, String str4, String str5) throws Exception;

    RemoteStatusDTO clearDtc(String str, String str2, String str3, String str4, String str5) throws Exception;

    RemoteStatusDTO readoutParam(List<DiagResItemGroupDto> list, String str, String str2, String str3, String str4, String str5) throws Exception;

    List<EcuBroadcastDto> getEcuList(String str) throws Exception;

    RemoteStatusDTO ecuIdentify(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8) throws Exception;

    List<ReloadDto> getRemoteSeqByEcu(String str, String str2, String str3) throws Exception;

    List<RemoteSeqExecuteDTO> querySeqDetail(String str) throws Exception;

    void hideApp(String str, boolean z) throws Exception;
}
