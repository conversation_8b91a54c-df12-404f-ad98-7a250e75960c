package com.geely.gnds.tester.service;

import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.tester.dto.AppSsoConfigDTO;
import com.geely.gnds.tester.dto.CepUserDTO;
import com.geely.gnds.tester.dto.LoginDto;
import com.geely.gnds.tester.dto.LoginInfoDTO;
import com.geely.gnds.tester.dto.SysTenantDto;
import com.geely.gnds.tester.vo.language.LanguageVo;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

/* loaded from: LoginService.class */
public interface LoginService {
    Boolean loginCloud() throws Exception;

    LoginInfoDTO onlineOauth2Login(LoginDto loginDto) throws Exception;

    LoginInfoDTO onlineLogin(LoginDto loginDto) throws Exception;

    LoginInfoDTO onlineLogin(LoginDto loginDto, String str) throws Exception;

    String getPublicKey(HttpServletRequest httpServletRequest) throws Exception;

    void updateUserStatus(SysUser sysUser) throws Exception;

    void logout();

    void logout(String str);

    String sendMobileCode(String str, String str2, int i, Boolean bool, Boolean bool2, String str3) throws Exception;

    SysTenantDto getTenantCodeByCode() throws Exception;

    void heartBeatStop();

    AppSsoConfigDTO getAppSsoConfig(HttpServletRequest httpServletRequest) throws Exception;

    CepUserDTO getCepLoginName(String str, String str2) throws Exception;

    List<LanguageVo> getUiLanguageResource(String str) throws Exception;
}
