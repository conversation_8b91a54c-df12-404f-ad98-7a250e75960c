package com.geely.gnds.tester.service;

import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.dto.InitializeUiDto;
import java.util.List;

/* loaded from: ReloadOrOtherService.class */
public interface ReloadOrOtherService {
    List queryReloadSeqList(String str) throws Exception;

    List queryOtherSeqList(String str) throws Exception;

    List queryDevelopSeqList(String str) throws Exception;

    String initFlush(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;

    String initFlush2(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;
}
