package com.geely.gnds.tester.service;

import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.dto.InitializeUiDto;
import java.util.List;

/* loaded from: FunctionSeqService.class */
public interface FunctionSeqService {
    List queryFunctionSeqList(String str) throws Exception;

    String initFlush(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;
}
