package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.DrCscNode;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpActivityDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisOrderDetailOrderDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTjDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpTrDTO;
import com.geely.gnds.tester.dto.gbop.gidp.GidpUpdateDiagnosisOrderDTO;
import com.github.pagehelper.PageInfo;
import java.util.List;
import java.util.Map;

/* loaded from: GidpService.class */
public interface GidpService {
    String getGidpToken(String str) throws Exception;

    GidpDiagnosisDTO saveGidpDiagnosis(GidpDiagnosisOrderDetailOrderDTO gidpDiagnosisOrderDetailOrderDTO) throws Exception;

    GidpDiagnosisDTO updateGidpDiagnosis(GidpUpdateDiagnosisOrderDTO gidpUpdateDiagnosisOrderDTO) throws Exception;

    PageInfo<GidpDiagnosisDTO> getPageDiagnosisList(String str, Integer num, Integer num2, Integer num3) throws Exception;

    PageInfo<GidpDiagnosisDTO> getPageDiagnosisListByVin(String str, String str2, Integer num, Integer num2, String str3) throws Exception;

    List<DrCscNode> getGidpIssueFunctions() throws Exception;

    PageInfo<GidpActivityDTO> getGidpActivity(String str, String str2, Integer num, Integer num2) throws Exception;

    Map responseActivityAction(String str, String str2, String str3, String str4) throws Exception;

    PageInfo<GidpTjDTO> searchTjDone(String str, String str2, String str3, String str4, String str5, Integer num, Integer num2) throws Exception;

    Map responseTjAction(String str, String str2, String str3, String str4) throws Exception;

    PageInfo<GidpTrDTO> getTrList(String str, String str2, Integer num, Integer num2) throws Exception;

    GidpTrDTO getCreateTrUrl() throws Exception;

    GidpDiagnosisDTO remoteUpdateGidpDiagnosis(String str, List<DtcInfoDTO> list);

    String getWorkOrderMenuList(String str) throws Exception;
}
