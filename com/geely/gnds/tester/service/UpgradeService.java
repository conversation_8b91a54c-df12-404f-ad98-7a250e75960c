package com.geely.gnds.tester.service;

import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.cache.Download;
import com.geely.gnds.tester.dto.DownloadDto;
import com.geely.gnds.tester.dto.DownloadProgressDto;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.UpgradeDto;
import com.geely.gnds.tester.enums.SoftwareTypeEnum;

/* loaded from: UpgradeService.class */
public interface UpgradeService {
    UpgradeDto query(String str, SoftwareTypeEnum softwareTypeEnum, String str2) throws Exception;

    void download(DownloadDto downloadDto) throws Exception;

    void upgradeTester(DownloadDto downloadDto) throws Exception;

    String getDownloadId();

    DownloadProgressDto getDownloadProgress(String str) throws Exception;

    Download stopDownload(String str);

    String initFlush(InitializeUiDto initializeUiDto, LoginUser loginUser) throws Exception;

    String initFlush2(InitializeUiDto initializeUiDto) throws Exception;
}
