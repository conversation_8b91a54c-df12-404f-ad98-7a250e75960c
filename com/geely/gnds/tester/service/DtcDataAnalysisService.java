package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.dtc.DtcExtendedDataRecordDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcSnapshotRecordDTO;
import com.geely.gnds.tester.vo.DtcExtendedDataVO;
import com.geely.gnds.tester.vo.ExtendedIndicatorVO;
import com.geely.gnds.tester.vo.SnapshotRecordVO;
import java.util.Date;
import java.util.List;

/* loaded from: DtcDataAnalysisService.class */
public interface DtcDataAnalysisService {
    List<DtcInfoDTO> analysisDtcList(String str);

    DtcExtendedDataRecordDTO analysisDtcExtendedDataRecord(String str);

    List<DtcSnapshotRecordDTO> analysisDtcDtcSnapshotRecord(List<DiagResItemGroupDto> list, String str);

    ExtendedIndicatorVO analysisDtcExtendedIndicator(byte[] bArr, ExtendedIndicatorVO extendedIndicatorVO);

    ExtendedIndicatorVO analysisDtcStatusMask(byte[] bArr, ExtendedIndicatorVO extendedIndicatorVO);

    DtcExtendedDataVO convertExtendDto2Vo(DtcExtendedDataRecordDTO dtcExtendedDataRecordDTO, String str, DtcExtendedDataVO dtcExtendedDataVO);

    SnapshotRecordVO fillTimeAxis(DtcExtendedDataRecordDTO dtcExtendedDataRecordDTO, SnapshotRecordVO snapshotRecordVO, Long l, Date date, int i);
}
