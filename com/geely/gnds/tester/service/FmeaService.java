package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.DiaGcidCheckDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.vo.DiaCscVO;
import com.geely.gnds.tester.vo.DiaFmeaAndCscVo;
import com.geely.gnds.tester.vo.DiaFmeaVO;
import com.geely.gnds.tester.vo.DiaGcidCheckDataVo;
import com.geely.gnds.tester.vo.DiaGcidVo;
import java.util.List;

/* loaded from: FmeaService.class */
public interface FmeaService {
    List<DiaFmeaVO> getNetworkList(String str, String str2) throws Exception;

    List<DiaFmeaVO> getStandardList(String str, String str2) throws Exception;

    List<DiaCscVO> getStandardCscList(String str, Long l) throws Exception;

    List<DiaGcidVo> getGcidList(String str, Long l) throws Exception;

    List<DiaGcidVo> getStandardGcidList(String str, Long l, String str2) throws Exception;

    void updateScore(int i, Long l, Long l2, String str) throws Exception;

    List<DiaFmeaVO> getDtcFmeaList(String str, String str2, String str3) throws Exception;

    List<DiaFmeaAndCscVo> getDtcFemaFunctionList(String str, String str2, String str3) throws Exception;

    List<DtcInfoDTO> getDtcList(String str, String str2, Long l, String str3) throws Exception;

    List<DiaGcidCheckDataVo> getGcidCheckdata(DiaGcidCheckDTO diaGcidCheckDTO) throws Exception;

    List<DiaFmeaAndCscVo> getStandardFemaAndCscList(String str, String str2) throws Exception;
}
