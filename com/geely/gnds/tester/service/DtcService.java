package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.TechnicalDTO;
import com.geely.gnds.tester.dto.TechnicalReqDTO;
import com.geely.gnds.tester.dto.dtc.DtcCalibrationInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcDmeGeneralInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcExtendedDataRecordDTO;
import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import com.geely.gnds.tester.dto.dtc.DtcSnapshotRecordDTO;
import com.geely.gnds.tester.vo.DtcDetailedVO;
import com.geely.gnds.tester.vo.DtcExtendedDataVO;
import com.geely.gnds.tester.vo.SnapshotRecordVO;
import java.util.Date;
import java.util.List;

/* loaded from: DtcService.class */
public interface DtcService {
    DtcDmeGeneralInfoDTO getGeneralInfo(String str, String str2, String str3, String str4) throws Exception;

    DtcCalibrationInfoDTO getCalibrationGeneralInfo(String str, String str2) throws Exception;

    DtcDetailedVO getDetailedInfo(String str, String str2, String str3, String str4, String str5, String str6, Date date, String str7);

    DtcDetailedVO getDetailedInfo2(String str, String str2, String str3, String str4, String str5, String str6);

    String getDtcStatus(String str, String str2, String str3);

    DtcExtendedDataRecordDTO getDtcExtendedDetailInfo(String str, String str2, String str3, String str4, String str5);

    DtcExtendedDataRecordDTO getRefreshDtcExtendedDetailInfo(String str, String str2, String str3, String str4);

    List<DtcSnapshotRecordDTO> getDtcSnapshotRecordDetailInfo(List<DiagResItemGroupDto> list, String str, String str2, String str3);

    DtcExtendedDataVO covertExtendedDataVO(DtcExtendedDataRecordDTO dtcExtendedDataRecordDTO, String str, DtcExtendedDataVO dtcExtendedDataVO);

    SnapshotRecordVO covertSnapshotRecordVO(List<DiagResItemGroupDto> list, DtcExtendedDataRecordDTO dtcExtendedDataRecordDTO, List<DtcSnapshotRecordDTO> list2, String str, String str2, SnapshotRecordVO snapshotRecordVO, String str3, Long l, int i);

    List<List<String>> getDtcListByUds(String str, String str2, String str3, List<String> list) throws Exception;

    List<DtcInfoDTO> getDtcList(String str, String str2, String str3, String str4) throws Exception;

    List<DtcInfoDTO> excludeDevelopDtc(List<DtcInfoDTO> list);

    List<SnapshotRecordVO.FrozenValuesVO> getDidListParseValue(List<DiagResItemGroupDto> list, List<DtcSnapshotRecordDTO> list2);

    List<TechnicalDTO> getTechnologyList(TechnicalReqDTO technicalReqDTO) throws Exception;

    String getDtcReadValue(String str);
}
