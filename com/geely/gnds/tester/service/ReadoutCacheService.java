package com.geely.gnds.tester.service;

import com.geely.gnds.tester.dto.TesterReadoutCacheDTO;
import com.geely.gnds.tester.entity.TesterReadoutCacheEntity;
import com.geely.gnds.tester.vo.DtcCacheQueryVo;
import java.text.ParseException;
import java.util.List;

/* loaded from: ReadoutCacheService.class */
public interface ReadoutCacheService {
    void save(TesterReadoutCacheEntity testerReadoutCacheEntity);

    TesterReadoutCacheEntity getById(Long l);

    List<TesterReadoutCacheDTO> getLasted(String str);

    void updateReadoutCache(TesterReadoutCacheEntity testerReadoutCacheEntity);

    void getInfo(DtcCacheQueryVo dtcCacheQueryVo) throws ParseException;

    TesterReadoutCacheDTO checkDtcInfo(String str);

    void saveOrUpdateVehicleConfig(TesterReadoutCacheEntity testerReadoutCacheEntity);

    TesterReadoutCacheEntity getVehicleConfig(String str);

    void deleteReadoutCache();
}
