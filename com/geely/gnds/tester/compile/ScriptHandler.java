package com.geely.gnds.tester.compile;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.geely.gnds.doip.client.dro.FdDroFile;
import com.geely.gnds.doip.client.dro.document.event.DiagnosticReadoutEvent;
import com.geely.gnds.doip.client.dro.document.event.ScriptInformation;
import com.geely.gnds.doip.client.dro.document.event.VersionInformation;
import com.geely.gnds.doip.client.dro.document.general.ApplicationInformation;
import com.geely.gnds.doip.client.dro.document.general.Engine;
import com.geely.gnds.doip.client.dro.document.general.General;
import com.geely.gnds.doip.client.dro.document.general.Model;
import com.geely.gnds.doip.client.dro.document.general.ModelYear;
import com.geely.gnds.doip.client.dro.document.general.PublisherInformation;
import com.geely.gnds.doip.client.dro.document.general.TimeZone;
import com.geely.gnds.doip.client.dro.document.general.Transmission;
import com.geely.gnds.doip.client.dro.document.general.VehicleInformation;
import com.geely.gnds.doip.client.dro.document.general.VehicleProfile;
import com.geely.gnds.doip.client.dro.document.root.DroResult;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.txt.FdTxtLogger;
import com.geely.gnds.doip.client.xml.FdXmlLogger;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.common.UploadCloudBean;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.BroadcastVehicleDataDTO;
import com.geely.gnds.tester.dto.DroDTO;
import com.geely.gnds.tester.dto.DroUdsDto;
import com.geely.gnds.tester.dto.EcuBroadcastDto;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.GlobalVariableEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.DxSequence;
import com.geely.gnds.tester.seq.SeqClassGenerator;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.TesterConfigService;
import com.geely.gnds.tester.service.impl.SeqServiceImpl;
import com.geely.gnds.tester.util.AesUtils;
import com.geely.gnds.tester.util.CommonFunctionsUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.VehicleUtils;
import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/* loaded from: ScriptHandler.class */
public class ScriptHandler {
    private String scriptJson;
    private String seqCode;
    private String username;
    private String serviceStationCode;
    private String tenantName;
    private static final String VEHICLE_CONFIG = "Vehicle_Config";
    private static final String READOUT_DD00_GLOBALTIME = "Vehicle_Config";
    private static final String READOUT_DD0A = "Vehicle_Config";
    private FdTxtLogger txtLogger;
    private FdDroFile droFile;
    private String vin;
    private static final Logger log = LoggerFactory.getLogger(SeqServiceImpl.class);
    private static final Logger LOGGER = LoggerFactory.getLogger(ScriptHandler.class);
    private DxSequence dxSequence = null;
    private AtomicInteger seqCount = new AtomicInteger();
    private FdTcpClient fdTcpClient = null;
    private SingletonManager manager = SingletonManager.getInstance();
    SeqManager seqManager = SeqManager.getInstance();
    private FdXmlLogger fdXmlLogger = null;
    private SeqClassGenerator scg = null;

    public DxSequence getDxSequence() {
        return this.dxSequence;
    }

    public void setDxSequence(DxSequence dxSequence) {
        this.dxSequence = dxSequence;
    }

    public ScriptHandler(String scriptJson, String vin, String initializeArgs, String seqCode, String version, String username, String tenantName, String serviceStationCode, String requestId) throws Exception {
        this.scriptJson = null;
        this.scriptJson = scriptJson;
        this.username = username;
        this.serviceStationCode = serviceStationCode;
        this.tenantName = tenantName;
        this.droFile = new FdDroFile(vin, tenantName, username);
        initDxSequence(vin, initializeArgs, seqCode, version, requestId);
    }

    public ScriptHandler(String scriptJson, String vin, String initializeArgs, String seqCode, String initializeParams, String version, String username, String tenantName, String serviceStationCode, String requestId) throws Exception {
        this.scriptJson = null;
        this.scriptJson = scriptJson;
        this.username = username;
        this.serviceStationCode = serviceStationCode;
        this.tenantName = tenantName;
        this.droFile = new FdDroFile(vin, tenantName, username);
        initDxSequence(vin, initializeArgs, seqCode, initializeParams, version, requestId);
    }

    public ScriptHandler(String scriptJson, String vin, String initializeArgs, String seqCode, String initializeParams, Boolean isStatusReadout, String version, String username, String tenantName, String serviceStationCode, String requestId) throws Exception {
        this.scriptJson = null;
        this.scriptJson = scriptJson;
        this.username = username;
        this.serviceStationCode = serviceStationCode;
        this.tenantName = tenantName;
        this.droFile = new FdDroFile(vin, tenantName, username);
        initDxSequence(vin, initializeArgs, seqCode, initializeParams, isStatusReadout, version, requestId);
    }

    public void setException() {
        this.dxSequence.setException(true);
    }

    public ScriptHandler(File file, String vin, String initializeArgs, String seqCode, String version, String requestId) throws Exception {
        this.scriptJson = null;
        if (file == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00112));
        }
        AesUtils.decodeFileSelf(file);
        this.scriptJson = FileUtil.readString(file, StandardCharsets.UTF_8);
        AesUtils.encodeFileSelf(file);
        initDxSequence(vin, initializeArgs, seqCode, version, requestId);
    }

    public ScriptHandler(File file, String vin, String initializeArgs, String seqCode, String initializeParams, String version, String username, String tenantName, String serviceStationCode, String requestId) throws Exception {
        this.scriptJson = null;
        if (file == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00112));
        }
        this.username = username;
        this.serviceStationCode = serviceStationCode;
        this.tenantName = tenantName;
        this.droFile = new FdDroFile(vin, tenantName, username);
        this.scriptJson = FileUtil.readString(file, StandardCharsets.UTF_8);
        initDxSequence(vin, initializeArgs, seqCode, initializeParams, version, requestId);
    }

    public ScriptHandler(File file, String vin, String initializeArgs, String seqCode, String initializeParams, Boolean isStatusReadout, String version, String username, String requestId) throws Exception {
        this.scriptJson = null;
        if (file == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00112));
        }
        AesUtils.decodeFileSelf(file);
        this.scriptJson = FileUtil.readString(file, StandardCharsets.UTF_8);
        AesUtils.encodeFileSelf(file);
        initDxSequence(vin, initializeArgs, seqCode, initializeParams, isStatusReadout, version, requestId);
    }

    public ScriptHandler(String scriptJson, String vin, String initializeArgs, String seqCode, String initializeParams, String version, String username, String tenantName, String serviceStationCode, boolean dsa, Boolean isStatusReadout, FdDsaLogger fdDsaLogger, String requestId) throws Exception {
        this.scriptJson = null;
        this.scriptJson = scriptJson;
        this.username = username;
        this.serviceStationCode = serviceStationCode;
        this.tenantName = tenantName;
        this.droFile = new FdDroFile(vin, tenantName, username);
        initDxSequence(vin, initializeArgs, seqCode, initializeParams, version, dsa, isStatusReadout.booleanValue(), fdDsaLogger, requestId);
    }

    private void initDxSequence(String vin, String initializeArgs, String seqCode, String version, String requestId) throws Exception {
        SeqClassGenerator scg = new SeqClassGenerator(this.scriptJson);
        this.dxSequence = scg.getSequeneceClass(seqCode, version);
        this.dxSequence.setDro(scg.getDro());
        this.dxSequence.setMdp(scg.getMdp());
        this.dxSequence.setTimeout(scg.getTimeout());
        this.dxSequence.setEdr(scg.getEdr());
        this.dxSequence.setVin(vin);
        this.dxSequence.setSeqCode(seqCode);
        this.dxSequence.setSeqName(scg.getSeqName());
        this.dxSequence.setInitializeArgs(initializeArgs);
        this.dxSequence.setMaskCodes(scg.getMaskCode());
        this.dxSequence.setUserName(this.username);
        this.vin = vin;
        this.seqCode = seqCode;
        this.dxSequence.setVersion(version);
        this.fdTcpClient = this.manager.getFdTcpClient(vin);
        this.dxSequence.setRequestId(requestId);
        this.scg = scg;
        initLogScript();
    }

    private void initDxSequence(String vin, String initializeArgs, String seqCode, String initializeParams, String version, String requestId) throws Exception {
        SeqClassGenerator scg = new SeqClassGenerator(this.scriptJson);
        this.dxSequence = scg.getSequeneceClass(seqCode, version);
        this.dxSequence.setVin(vin);
        this.dxSequence.setMdp(scg.getMdp());
        this.dxSequence.setTimeout(scg.getTimeout());
        this.dxSequence.setSeqCode(seqCode);
        this.dxSequence.setSeqName(scg.getSeqName());
        this.dxSequence.setInitializeArgs(initializeArgs);
        this.dxSequence.setInitializeParams(initializeParams, scg.getDro());
        this.dxSequence.setEdr(scg.getEdr());
        this.dxSequence.setMaskCodes(scg.getMaskCode());
        this.dxSequence.setUserName(this.username);
        this.vin = vin;
        this.seqCode = seqCode;
        this.dxSequence.setVersion(version);
        this.fdTcpClient = this.manager.getFdTcpClient(vin);
        this.dxSequence.setRequestId(requestId);
        this.scg = scg;
        initLogScript();
    }

    private void initDxSequence(String vin, String initializeArgs, String seqCode, String initializeParams, String version, boolean dsa, boolean isStatusReadout, FdDsaLogger fdDsaLogger, String requestId) throws Exception {
        SeqClassGenerator scg = new SeqClassGenerator(this.scriptJson);
        this.dxSequence = scg.getSequeneceClass(seqCode, version);
        this.dxSequence.setVin(vin);
        this.dxSequence.setSeqCode(seqCode);
        this.dxSequence.setSeqName(scg.getSeqName());
        this.dxSequence.setInitializeArgs(initializeArgs);
        this.dxSequence.setInitializeParams(initializeParams, scg.getDro());
        this.dxSequence.setEdr(scg.getEdr());
        this.dxSequence.setMaskCodes(scg.getMaskCode());
        this.dxSequence.setUserName(this.username);
        this.dxSequence.setStatusReadout(Boolean.valueOf(isStatusReadout));
        this.dxSequence.setFdDsaLogger(fdDsaLogger);
        this.dxSequence.setRequestId(requestId);
        this.vin = vin;
        this.seqCode = seqCode;
        this.dxSequence.setVersion(version);
        this.fdTcpClient = this.manager.getFdTcpClient(vin);
        this.scg = scg;
        this.dxSequence.setDsa(dsa);
        initLogScript();
    }

    private void initDxSequence(String vin, String initializeArgs, String seqCode, String initializeParams, Boolean isStatusReadout, String version, String requestId) throws Exception {
        SeqClassGenerator scg = new SeqClassGenerator(this.scriptJson);
        this.dxSequence = scg.getSequeneceClass(seqCode, version);
        this.dxSequence.setVin(vin);
        this.dxSequence.setMdp(scg.getMdp());
        this.dxSequence.setTimeout(scg.getTimeout());
        this.dxSequence.setSeqCode(seqCode);
        this.dxSequence.setSeqName(scg.getSeqName());
        this.dxSequence.setInitializeArgs(initializeArgs);
        this.dxSequence.setInitializeParams(initializeParams, scg.getDro());
        this.dxSequence.setEdr(scg.getEdr());
        this.dxSequence.setStatusReadout(isStatusReadout);
        this.dxSequence.setVersion(version);
        this.dxSequence.setMaskCodes(scg.getMaskCode());
        this.dxSequence.setUserName(this.username);
        this.dxSequence.setRequestId(requestId);
        this.vin = vin;
        this.seqCode = seqCode;
        this.fdTcpClient = this.manager.getFdTcpClient(vin);
        this.scg = scg;
        initLogScript();
    }

    public void initLogScript() {
        FdTcpClient tcpClient = this.manager.getFdTcpClient(this.vin);
        Optional.ofNullable(tcpClient).ifPresent(client -> {
            this.txtLogger = client.geTxtLogger();
            this.dxSequence.setTxtLogger(this.txtLogger);
            String content = "Init Script: " + this.seqCode;
            this.txtLogger.write(new Date(), "Script", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
        });
    }

    private void droStart() {
        try {
            try {
                if (this.fdTcpClient != null && this.fdTcpClient.isVirtualVehicle()) {
                    this.fdTcpClient.setDroResult(new DroResult());
                    this.fdTcpClient.setDiagnosticReadoutEvent(new DiagnosticReadoutEvent());
                    this.fdTcpClient.setEcuList(new ArrayList());
                    return;
                }
                DroDTO droDTO = new DroDTO();
                String distance = "00000000";
                Object drivingDistance = this.manager.getGlobal(GlobalVariableEnum.mc, this.vin);
                if (!ObjectUtils.isEmpty(drivingDistance)) {
                    log.info("droStart DrivingDistance:" + drivingDistance);
                    distance = CommonFunctionsUtils.stringCompletion(Integer.toString(CommonFunctionsUtils.stringToUnsignedInt(drivingDistance.toString(), 16)), 8);
                }
                this.droFile.setFileNameDro(distance);
                droDTO.setDroFile(this.droFile);
                droDTO.setVin(this.vin);
                Object globalDtcInfo = this.manager.getGlobal("DTC_info", this.vin);
                String dtcInfo = ObjectUtils.isEmpty(globalDtcInfo) ? "" : globalDtcInfo.toString();
                droDTO.setDtcInfo(dtcInfo);
                String fileName = this.droFile.getFileName();
                droDTO.setLanguage(HttpUtils.getLanguage());
                startRecordDro(droDTO);
                List<DroUdsDto> droUdsList = this.fdTcpClient.getDroUdsList();
                droDTO.setDroUdsList(droUdsList);
                String data = this.manager.getGlobal(GlobalVariableEnum.lY, this.vin).toString();
                BroadcastVehicleDataDTO broadcasts = (BroadcastVehicleDataDTO) ObjectMapperUtils.jsonStr2Clazz(data, BroadcastVehicleDataDTO.class);
                if (!Objects.isNull(broadcasts)) {
                    List<EcuBroadcastDto> ecus = broadcasts.getEcus();
                    droDTO.setEcuBroadcastDtoList(ecus);
                }
                UploadCloudBean uploadCloud = this.droFile.f();
                droDTO.setUploadCloudBean(uploadCloud);
                try {
                    Cloud cloud = (Cloud) SpringUtils.getBean(Cloud.class);
                    OssUtils ossUtils = (OssUtils) SpringUtils.getBean(OssUtils.class);
                    String id = ossUtils.saveLogToCloudDro(uploadCloud);
                    if (StringUtils.hasText(id)) {
                        uploadCloud.setId(id);
                    }
                    cloud.a(droDTO, this.username);
                } catch (Exception e) {
                    LOGGER.error("DRO数据保存到云端失败，开始保存到json文件", e);
                    File file = new File(ConstantEnum.POINT, "dro");
                    if (!file.exists()) {
                        file.mkdirs();
                    }
                    File jsonFile = new File(file, fileName + ConstantEnum.JSON);
                    FileUtil.writeString(JSONUtil.toJsonStr(droDTO), jsonFile, StandardCharsets.UTF_8);
                }
                this.fdTcpClient.setDroResult(new DroResult());
                this.fdTcpClient.setDiagnosticReadoutEvent(new DiagnosticReadoutEvent());
                this.fdTcpClient.setEcuList(new ArrayList());
            } catch (Exception e2) {
                LOGGER.error("DRO生成失败", e2);
                this.fdTcpClient.setDroResult(new DroResult());
                this.fdTcpClient.setDiagnosticReadoutEvent(new DiagnosticReadoutEvent());
                this.fdTcpClient.setEcuList(new ArrayList());
            }
        } catch (Throwable th) {
            this.fdTcpClient.setDroResult(new DroResult());
            this.fdTcpClient.setDiagnosticReadoutEvent(new DiagnosticReadoutEvent());
            this.fdTcpClient.setEcuList(new ArrayList());
            throw th;
        }
    }

    public void startRecordDro(DroDTO droDTO) throws JsonProcessingException {
        DroResult droResult = new DroResult();
        Object broadcastQuery = this.manager.getGlobal(GlobalVariableEnum.lX, this.vin);
        String brand = "";
        String st = "";
        String vehicleType = "";
        String fyon = "";
        String aaCid = "";
        String myCid = "";
        String baCid = "";
        String caCid = "";
        String aaDes = "";
        String myDes = "";
        String baDes = "";
        String caDes = "";
        if (broadcastQuery != null) {
            String bc = broadcastQuery.toString();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(bc)) {
                VehicleUtils vehicleUtils = (VehicleUtils) SpringUtils.getBean(VehicleUtils.class);
                Map map = vehicleUtils.settleVehicleInfo(bc);
                brand = (String) map.get("brandName");
                st = (String) map.get("st");
                vehicleType = (String) map.get("vehicleType");
                fyon = (String) map.get("fyon");
                aaCid = (String) map.get(Constants.AA);
                myCid = (String) map.get(Constants.MY);
                baCid = (String) map.get(Constants.BA);
                caCid = (String) map.get(Constants.CA);
                aaDes = (String) map.get(Constants.AA + "dro");
                myDes = (String) map.get(Constants.MY + "dro");
                baDes = (String) map.get(Constants.BA + "dro");
                caDes = (String) map.get(Constants.CA + "dro");
            }
        }
        General general = new General();
        VehicleInformation vehicleInformation = new VehicleInformation();
        vehicleInformation.setVin(this.vin);
        vehicleInformation.setFyon(fyon);
        vehicleInformation.setClassisNumber(this.vin.substring(this.vin.length() - 6));
        vehicleInformation.setVehicleType(vehicleType);
        vehicleInformation.setStructureWeek(st);
        PublisherInformation publisherInformation = new PublisherInformation();
        publisherInformation.setParmaId("");
        publisherInformation.setPartnerId(this.serviceStationCode);
        TesterConfigService testerConfigService = (TesterConfigService) SpringUtils.getBean(TesterConfigService.class);
        TesterConfigDto testerConfigDto = testerConfigService.getConfig();
        AtomicReference<String> testCode = new AtomicReference<>("未获取到客户端编号");
        Optional.ofNullable(testerConfigDto).ifPresent(config -> {
            testCode.set(config.getTesterCode());
        });
        publisherInformation.setDeviceId(this.tenantName + "-" + testCode.get());
        droDTO.getDroFile().setTesterId(testCode.get());
        publisherInformation.setUserId(this.username);
        ApplicationInformation applicationInformation = new ApplicationInformation();
        applicationInformation.setType(StrUtil.blankToDefault(brand, ""));
        applicationInformation.setVersion("********");
        VehicleProfile vehicleProfile = new VehicleProfile();
        Model model = new Model();
        model.setCid(aaCid);
        model.setDescription(aaDes);
        ModelYear modelYear = new ModelYear();
        modelYear.setCid(myCid);
        modelYear.setDescription(myDes);
        Engine engine = new Engine();
        engine.setCid(baCid);
        engine.setDescription(baDes);
        Transmission transmission = new Transmission();
        transmission.setCid(caCid);
        transmission.setDescription(caDes);
        vehicleProfile.setProfileIds(new ArrayList());
        vehicleProfile.setModel(model);
        vehicleProfile.setModelYear(modelYear);
        vehicleProfile.setEngine(engine);
        vehicleProfile.setTransmission(transmission);
        general.setVehicleInformation(vehicleInformation);
        general.setPublisherInformation(publisherInformation);
        general.setApplicationInformation(applicationInformation);
        general.setTimeZone(new TimeZone());
        general.setVehicleProfile(vehicleProfile);
        droResult.setGeneral(general);
        DiagnosticReadoutEvent diagnosticReadoutEvent = new DiagnosticReadoutEvent();
        Object config2 = this.manager.getGlobal("Vehicle_Config", this.vin);
        String odometerValue = config2 == null ? "" : (String) config2;
        diagnosticReadoutEvent.setOdometerValue(odometerValue);
        Object dd00GlobalTime = this.manager.getGlobal("Vehicle_Config", this.vin);
        String globalTime = dd00GlobalTime == null ? "" : (String) dd00GlobalTime;
        diagnosticReadoutEvent.setVehicleGlobalTime(globalTime);
        Object dd0a = this.manager.getGlobal("Vehicle_Config", this.vin);
        String powerModel = dd0a == null ? "" : (String) dd0a;
        diagnosticReadoutEvent.setPowerMode(powerModel);
        List<ScriptInformation> scriptInformations = new ArrayList<>();
        scriptInformations.add(new ScriptInformation());
        diagnosticReadoutEvent.setScriptInformation(scriptInformations);
        diagnosticReadoutEvent.setVersionInformation(new VersionInformation());
        droDTO.setDroResult(droResult);
        droDTO.setDiagnosticReadoutEvent(diagnosticReadoutEvent);
    }

    /* JADX WARN: Removed duplicated region for block: B:25:0x0068  */
    /* JADX WARN: Removed duplicated region for block: B:29:0x00a0  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.String callFinalModule() throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 285
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.compile.ScriptHandler.callFinalModule():java.lang.String");
    }

    public void notifySeq(InitializeUiDto initializeUiDto) throws Exception {
        setFiled(initializeUiDto);
        this.dxSequence.setClickUi(initializeUiDto.getInitializeUi());
        this.dxSequence.notifySeq();
    }

    private void setFiled(InitializeUiDto initializeUiDto) throws IllegalAccessException, NoSuchFieldException, IllegalArgumentException {
        Map<String, Object> resultMap = initializeUiDto.getResultMap();
        if (resultMap != null && resultMap.size() > 0) {
            for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value instanceof List) {
                    value = JSONObject.toJSONString(value);
                }
                try {
                    log.error("设置字段key:{}---value:{}", key, value);
                    Class<?> classSub = this.scg.getClassName();
                    Field declaredField = classSub.getDeclaredField(key);
                    if (declaredField != null) {
                        declaredField.setAccessible(true);
                        declaredField.set(this.dxSequence, value);
                    }
                } catch (Exception e) {
                    log.error("设置字段失败{}---{}", new Object[]{key, value, e});
                }
            }
        }
    }

    public String pollSetUi() throws Exception {
        String pollSetUi = this.dxSequence.pollSetUi();
        Optional.ofNullable(this.txtLogger).ifPresent(logger -> {
            String content = "Running Script: " + this.seqCode + " , pollSetUi:[" + pollSetUi + "]";
            logger.write(new Date(), "Script", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
        });
        return pollSetUi;
    }

    public String getInitializeUi() throws Exception {
        String res = "";
        try {
            res = this.dxSequence.initializeUiModule();
        } catch (Exception e) {
            String seqCodeFather = this.dxSequence.getSeqCodeFather();
            handleException(seqCodeFather);
        }
        return res;
    }

    /* JADX WARN: Can't wrap try/catch for region: R(11:(17:11|128|20|21|31|(1:33)|109|34|35|(1:37)|90|(1:92)|93|(1:97)|98|(1:100)(2:101|102)|24)|109|34|35|(0)|90|(0)|93|(2:95|97)|98|(0)(0)) */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0211, code lost:
    
        r12 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x0213, code lost:
    
        r13 = false;
        r7.dxSequence.setSeqResult2(false);
        r0 = r7.dxSequence.getSeqCodeFather();
        r15 = r12.getMessage();
        r16 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x0239, code lost:
    
        if ((r12 instanceof java.lang.reflect.InvocationTargetException) != false) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x023c, code lost:
    
        r0 = (java.lang.reflect.InvocationTargetException) r12;
        r0 = r0.getTargetException();
        r15 = r0.getMessage();
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x0256, code lost:
    
        if ((r0 instanceof com.geely.gnds.doip.client.exception.CancelByUserException) != false) goto L44;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x0259, code lost:
    
        r13 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x0264, code lost:
    
        if (com.geely.gnds.ruoyi.common.utils.StringUtils.isBlank(r15) != false) goto L49;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x0271, code lost:
    
        r15 = com.geely.gnds.tester.enums.TesterErrorCodeEnum.formatMsg(com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00160);
        com.geely.gnds.tester.compile.ScriptHandler.LOGGER.error(r15, r12);
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0285, code lost:
    
        r0 = com.geely.gnds.tester.enums.TesterErrorCodeEnum.getEnumByCodeOrStr(r15);
        r0 = r0.getStackTrace();
        r0 = r0.length;
        r22 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x029f, code lost:
    
        if (r22 < r0) goto L53;
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x02a2, code lost:
    
        r0 = r0[r22];
        r0 = r0.getFileName();
        r0 = r7.dxSequence.getClass().getName() + ".java";
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x02d0, code lost:
    
        if (r0 == null) goto L115;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x02e2, code lost:
    
        r0 = r0.getLineNumber();
        r0 = r7.scg.getLineCode(r0);
        r16 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x02f9, code lost:
    
        if (r0 == null) goto L61;
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x02fc, code lost:
    
        r15 = com.geely.gnds.dsa.enums.LanguageEnum.ERROR_CODE_LINE.valueByLanguage() + "_" + r0 + ";" + com.geely.gnds.dsa.enums.LanguageEnum.ERROR_CODE.valueByLanguage() + "_" + r0 + ";" + com.geely.gnds.dsa.enums.LanguageEnum.ERROR_JAVA.valueByLanguage() + "(" + r15 + ")";
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x0353, code lost:
    
        r15 = r15 + ";" + com.geely.gnds.dsa.enums.LanguageEnum.ERROR_CODE_LINE.valueByLanguage() + "_" + r0 + ";" + com.geely.gnds.dsa.enums.LanguageEnum.ERROR_CODE.valueByLanguage() + "_" + r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x0397, code lost:
    
        r22 = r22 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x039d, code lost:
    
        handleException(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x03a3, code lost:
    
        com.geely.gnds.tester.compile.ScriptHandler.log.info("scriptHandle handleClickEvent  targetException.getMessage()");
     */
    /* JADX WARN: Code restructure failed: missing block: B:66:0x03ad, code lost:
    
        com.geely.gnds.tester.compile.ScriptHandler.log.info("scriptHandle handleClickEvent while (true)结束");
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x03bc, code lost:
    
        com.geely.gnds.tester.compile.ScriptHandler.log.error("Exception处理异常", r12);
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x03ca, code lost:
    
        if (r13 == false) goto L71;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x03cd, code lost:
    
        r7.dxSequence.setUiException(r0, r15);
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x03de, code lost:
    
        if (r7.dxSequence.isNeedThrowUserCancelException() != false) goto L74;
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x03e3, code lost:
    
        if (r16 != false) goto L76;
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x03e6, code lost:
    
        r7.dxSequence.logSeqFaultResult(r15, com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00255);
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x03f5, code lost:
    
        r7.dxSequence.logSeqFaultResult(r15, com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00043);
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x0401, code lost:
    
        handleUserCancelException(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:80:0x0408, code lost:
    
        r7.dxSequence.addSetUi("{\"Entrane_Name\":\"" + r0 + "\",\"Logical_control\":\"SET_UI_END\"}");
        r7.seqCount.decrementAndGet();
        r7.dxSequence.reduceSeqProcessCount();
        r7.dxSequence.pcapFragment();
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x0440, code lost:
    
        if (r7.fdTcpClient != null) goto L82;
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x0443, code lost:
    
        r7.fdTcpClient.cleanSeqCode();
     */
    /* JADX WARN: Removed duplicated region for block: B:100:0x0510 A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:101:0x0515  */
    /* JADX WARN: Removed duplicated region for block: B:33:0x01a3  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x0207  */
    /* JADX WARN: Removed duplicated region for block: B:92:0x049c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.String handleClickEvent(com.geely.gnds.tester.dto.InitializeUiDto r8) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 1307
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.compile.ScriptHandler.handleClickEvent(com.geely.gnds.tester.dto.InitializeUiDto):java.lang.String");
    }

    public void createEdr() {
        this.dxSequence.createEdr();
    }

    public void setClickUi(InitializeUiDto initializeUiDto) throws IllegalAccessException, NoSuchFieldException, IllegalArgumentException {
        setFiled(initializeUiDto);
        this.dxSequence.setClickUi(initializeUiDto.getInitializeUi());
        this.dxSequence.notifySeq();
    }

    public void setCloseWindowFlag(Boolean value) {
        this.dxSequence.setCloseWindowFlag(value);
    }

    public Boolean getCloseWindowFlag() {
        return this.dxSequence.getCloseWindowFlag();
    }

    public Object getGlobal(String name) {
        return this.dxSequence.getGlobal(name);
    }

    private void handleUserCancelException(String seqCode) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(seqCode)) {
            LOGGER.error("子诊断序列被中断，开始处理{}", seqCode);
            ScriptHandler handler = this.seqManager.getScriptHandler(this.vin + seqCode);
            if (handler != null) {
                try {
                    handler.notifySeq(new InitializeUiDto());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void handleException(String seqCode) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(seqCode)) {
            LOGGER.error("脚本循环调用出错，开始处理{}", seqCode);
            ScriptHandler handler = this.seqManager.getScriptHandler(this.vin + seqCode);
            if (handler != null) {
                handler.setException();
                try {
                    handler.notifySeq(new InitializeUiDto());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public String getSeqResult() {
        return this.dxSequence.getSeqResult();
    }

    public int getSeqCount() {
        return this.seqCount.get();
    }

    public void logSeqFaultResult(String msg, TesterErrorCodeEnum enumByCode) {
        this.dxSequence.logSeqFaultResult(msg, enumByCode);
    }

    /* JADX WARN: Can't wrap try/catch for region: R(13:(18:5|39|14|15|25|161|26|(1:28)(4:29|(2:32|30)|169|33)|34|(3:36|(1:38)(4:39|(2:42|40)|168|43)|44)(1:45)|46|47|(1:49)|50|(4:52|(2:54|(1:58))(2:59|(1:61))|62|(1:66))|156|(1:158)(2:159|160)|18)|161|26|(0)(0)|34|(0)(0)|46|47|(0)|50|(0)|156|(0)(0)) */
    /* JADX WARN: Code restructure failed: missing block: B:100:0x03fb, code lost:
    
        r5.dxSequence.setUiException("Main", r12);
     */
    /* JADX WARN: Code restructure failed: missing block: B:102:0x040d, code lost:
    
        if (r5.dxSequence.isNeedThrowUserCancelException() != false) goto L103;
     */
    /* JADX WARN: Code restructure failed: missing block: B:104:0x0411, code lost:
    
        if (r0 != null) goto L105;
     */
    /* JADX WARN: Code restructure failed: missing block: B:105:0x0414, code lost:
    
        r0.setExceptionMsg(r12);
     */
    /* JADX WARN: Code restructure failed: missing block: B:107:0x041c, code lost:
    
        if (r13 != false) goto L108;
     */
    /* JADX WARN: Code restructure failed: missing block: B:108:0x041f, code lost:
    
        r5.dxSequence.logSeqFaultResult(r12, com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00255);
     */
    /* JADX WARN: Code restructure failed: missing block: B:109:0x042e, code lost:
    
        r5.dxSequence.logSeqFaultResult(r12, com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00011);
     */
    /* JADX WARN: Code restructure failed: missing block: B:110:0x043a, code lost:
    
        handleUserCancelException(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:112:0x0441, code lost:
    
        r5.dxSequence.addSetUi("{\"Entrane_Name\":\"Main\",\"Logical_control\":\"SET_UI_END\"}");
        r5.seqCount.decrementAndGet();
        r5.dxSequence.reduceSeqProcessCount();
        java.util.Optional.ofNullable(r5.txtLogger).ifPresent((v1) -> { // java.util.function.Consumer.accept(java.lang.Object):void
            lambda$callMainModule$4(v1);
        });
     */
    /* JADX WARN: Code restructure failed: missing block: B:113:0x0469, code lost:
    
        if (r6 != null) goto L114;
     */
    /* JADX WARN: Code restructure failed: missing block: B:114:0x046c, code lost:
    
        r6.p();
     */
    /* JADX WARN: Code restructure failed: missing block: B:115:0x0470, code lost:
    
        r5.dxSequence.setMoudleEnd();
        r5.dxSequence.uploadXml();
     */
    /* JADX WARN: Code restructure failed: missing block: B:116:0x0482, code lost:
    
        if (r5.fdTcpClient != null) goto L117;
     */
    /* JADX WARN: Code restructure failed: missing block: B:117:0x0485, code lost:
    
        r5.fdTcpClient.cleanSeqCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:118:0x0493, code lost:
    
        if (r5.dxSequence.getDroFather() != null) goto L119;
     */
    /* JADX WARN: Code restructure failed: missing block: B:120:0x04a0, code lost:
    
        if (r5.dxSequence.getDroFather().booleanValue() == false) goto L121;
     */
    /* JADX WARN: Code restructure failed: missing block: B:123:0x04ad, code lost:
    
        droStart();
     */
    /* JADX WARN: Code restructure failed: missing block: B:125:0x04bb, code lost:
    
        if (r5.dxSequence.isDro() != false) goto L126;
     */
    /* JADX WARN: Code restructure failed: missing block: B:126:0x04be, code lost:
    
        droStart();
     */
    /* JADX WARN: Code restructure failed: missing block: B:128:0x04cc, code lost:
    
        if (com.geely.gnds.ruoyi.common.utils.StringUtils.isNotBlank(r5.dxSequence.getEdr()) != false) goto L129;
     */
    /* JADX WARN: Code restructure failed: missing block: B:131:0x04d9, code lost:
    
        r5.dxSequence.createEdr();
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x0239, code lost:
    
        r9 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x023b, code lost:
    
        r5.dxSequence.setSeqResult2(false);
        r0 = r5.dxSequence.getSeqCodeFather();
        r11 = false;
        com.geely.gnds.tester.compile.ScriptHandler.LOGGER.error(com.geely.gnds.tester.enums.TesterErrorCodeEnum.formatMsg(com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00011), r9);
        r12 = r9.getMessage();
        r13 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x0271, code lost:
    
        if ((r9 instanceof java.lang.reflect.InvocationTargetException) != false) goto L71;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x0274, code lost:
    
        r0 = (java.lang.reflect.InvocationTargetException) r9;
        r0 = r0.getTargetException();
        r12 = r0.getMessage();
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x028e, code lost:
    
        if ((r0 instanceof com.geely.gnds.doip.client.exception.CancelByUserException) != false) goto L73;
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x0291, code lost:
    
        r11 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x029c, code lost:
    
        if (com.geely.gnds.ruoyi.common.utils.StringUtils.isBlank(r12) != false) goto L78;
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x02a9, code lost:
    
        r12 = com.geely.gnds.tester.enums.TesterErrorCodeEnum.formatMsg(com.geely.gnds.tester.enums.TesterErrorCodeEnum.SG00160);
        com.geely.gnds.tester.compile.ScriptHandler.LOGGER.error(r12, r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x02bd, code lost:
    
        r0 = com.geely.gnds.tester.enums.TesterErrorCodeEnum.getEnumByCodeOrStr(r12);
        r0 = r0.getStackTrace();
        r0 = r0.length;
        r19 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x02d7, code lost:
    
        if (r19 < r0) goto L82;
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x02da, code lost:
    
        r0 = r0[r19];
        r0 = r0.getFileName();
        r0 = r5.dxSequence.getClass().getName() + ".java";
     */
    /* JADX WARN: Code restructure failed: missing block: B:83:0x0308, code lost:
    
        if (r0 == null) goto L172;
     */
    /* JADX WARN: Code restructure failed: missing block: B:88:0x031a, code lost:
    
        r0 = r0.getLineNumber();
        r0 = r5.scg.getLineCode(r0);
        r13 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:89:0x0331, code lost:
    
        if (r0 == null) goto L90;
     */
    /* JADX WARN: Code restructure failed: missing block: B:90:0x0334, code lost:
    
        r12 = com.geely.gnds.dsa.enums.LanguageEnum.ERROR_CODE_LINE.valueByLanguage() + "_" + r0 + ";" + com.geely.gnds.dsa.enums.LanguageEnum.ERROR_CODE.valueByLanguage() + "_" + r0 + ";" + com.geely.gnds.dsa.enums.LanguageEnum.ERROR_JAVA.valueByLanguage() + "(" + r12 + ")";
     */
    /* JADX WARN: Code restructure failed: missing block: B:91:0x038b, code lost:
    
        r12 = r12 + ";" + com.geely.gnds.dsa.enums.LanguageEnum.ERROR_CODE_LINE.valueByLanguage() + "_" + r0 + ";" + com.geely.gnds.dsa.enums.LanguageEnum.ERROR_CODE.valueByLanguage() + "_" + r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:92:0x03cf, code lost:
    
        r19 = r19 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:93:0x03d5, code lost:
    
        handleException(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:94:0x03db, code lost:
    
        com.geely.gnds.tester.compile.ScriptHandler.log.info("scriptHandle handleClickEvent  targetException.getMessage()");
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x03ea, code lost:
    
        com.geely.gnds.tester.compile.ScriptHandler.log.error("Exception处理异常", r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:99:0x03f8, code lost:
    
        if (r11 == false) goto L100;
     */
    /* JADX WARN: Removed duplicated region for block: B:158:0x058c A[RETURN, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:159:0x0591  */
    /* JADX WARN: Removed duplicated region for block: B:28:0x00cb A[Catch: Exception -> 0x0239, all -> 0x04e3, TryCatch #2 {Exception -> 0x0239, blocks: (B:26:0x00a8, B:28:0x00cb, B:36:0x0126, B:38:0x0134, B:44:0x0180, B:39:0x0145, B:40:0x0154, B:42:0x0160, B:43:0x0174, B:45:0x0188, B:29:0x00e0, B:30:0x00ef, B:32:0x00fb, B:33:0x0112), top: B:161:0x00a8, outer: #0 }] */
    /* JADX WARN: Removed duplicated region for block: B:29:0x00e0 A[Catch: Exception -> 0x0239, all -> 0x04e3, TryCatch #2 {Exception -> 0x0239, blocks: (B:26:0x00a8, B:28:0x00cb, B:36:0x0126, B:38:0x0134, B:44:0x0180, B:39:0x0145, B:40:0x0154, B:42:0x0160, B:43:0x0174, B:45:0x0188, B:29:0x00e0, B:30:0x00ef, B:32:0x00fb, B:33:0x0112), top: B:161:0x00a8, outer: #0 }] */
    /* JADX WARN: Removed duplicated region for block: B:36:0x0126 A[Catch: Exception -> 0x0239, all -> 0x04e3, TryCatch #2 {Exception -> 0x0239, blocks: (B:26:0x00a8, B:28:0x00cb, B:36:0x0126, B:38:0x0134, B:44:0x0180, B:39:0x0145, B:40:0x0154, B:42:0x0160, B:43:0x0174, B:45:0x0188, B:29:0x00e0, B:30:0x00ef, B:32:0x00fb, B:33:0x0112), top: B:161:0x00a8, outer: #0 }] */
    /* JADX WARN: Removed duplicated region for block: B:45:0x0188 A[Catch: Exception -> 0x0239, all -> 0x04e3, TryCatch #2 {Exception -> 0x0239, blocks: (B:26:0x00a8, B:28:0x00cb, B:36:0x0126, B:38:0x0134, B:44:0x0180, B:39:0x0145, B:40:0x0154, B:42:0x0160, B:43:0x0174, B:45:0x0188, B:29:0x00e0, B:30:0x00ef, B:32:0x00fb, B:33:0x0112), top: B:161:0x00a8, outer: #0 }] */
    /* JADX WARN: Removed duplicated region for block: B:49:0x01c2  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x01db  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public java.lang.String callMainModule(com.geely.gnds.doip.client.xml.FdXmlLogger r6) throws java.lang.Exception {
        /*
            Method dump skipped, instructions count: 1430
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.tester.compile.ScriptHandler.callMainModule(com.geely.gnds.doip.client.xml.FdXmlLogger):java.lang.String");
    }

    public String callStatusMainModule(FdXmlLogger xmlLogger) throws Exception {
        this.dxSequence.setXmlLogger(xmlLogger);
        if (this.fdTcpClient != null) {
            this.fdTcpClient.setSeqCode(this.seqCode);
        }
        AtomicInteger seqProcessCount = (AtomicInteger) this.manager.getGlobal("seqProcessCount", this.vin);
        if (seqProcessCount == null) {
            seqProcessCount = new AtomicInteger();
        }
        AtomicInteger seqStatusReadoutCount = (AtomicInteger) this.manager.getGlobal("seqStatusReadoutCount", this.vin);
        if (seqStatusReadoutCount == null) {
            seqStatusReadoutCount = new AtomicInteger();
        }
        if (seqProcessCount.get() != 0) {
            return "";
        }
        try {
            if (seqStatusReadoutCount.get() != 0) {
                return "";
            }
            try {
                this.dxSequence.addSeqProcessCount();
                this.seqCount.incrementAndGet();
                Method module = this.dxSequence.getClass().getMethod("mainModule", new Class[0]);
                Object result = module.invoke(this.dxSequence, new Object[0]);
                this.dxSequence.addSetUi("{\"Entrane_Name\":\"Main\",\"Logical_control\":\"SET_UI_END\"}");
                this.seqCount.decrementAndGet();
                this.dxSequence.reduceSeqProcessCount();
                if (this.fdTcpClient != null) {
                    this.fdTcpClient.cleanSeqCode();
                }
                return result == null ? "" : result.toString();
            } catch (Exception e) {
                LOGGER.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00011), e);
                String msg = e.getMessage();
                if (e instanceof InvocationTargetException) {
                    InvocationTargetException e1 = (InvocationTargetException) e;
                    Throwable targetException = e1.getTargetException();
                    msg = targetException.getMessage();
                }
                LOGGER.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00011), e);
                if (com.geely.gnds.ruoyi.common.utils.StringUtils.isBlank(e.getMessage()) || ConstantEnum.NULL.equals(e.getMessage())) {
                    msg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00160);
                    LOGGER.error(msg, e);
                }
                this.dxSequence.setUiException("Main", msg);
                throw e;
            }
        } catch (Throwable th) {
            this.dxSequence.addSetUi("{\"Entrane_Name\":\"Main\",\"Logical_control\":\"SET_UI_END\"}");
            this.seqCount.decrementAndGet();
            this.dxSequence.reduceSeqProcessCount();
            if (this.fdTcpClient != null) {
                this.fdTcpClient.cleanSeqCode();
            }
            throw th;
        }
    }

    public void notifySeq() {
        this.dxSequence.notifySeq();
    }

    public boolean isResultSetBySeq() {
        return this.dxSequence.isResultSetBySeq();
    }
}
