package com.geely.gnds.tester.compile;

import com.geely.gnds.tester.enums.ConstantEnum;
import com.sun.tools.javac.file.BaseFileObject;
import com.sun.tools.javac.file.JavacFileManager;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Writer;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import javax.tools.JavaFileObject;

/* loaded from: MemorySpringBootInfoJavaClassObject.class */
public class MemorySpringBootInfoJavaClassObject extends BaseFileObject {
    private final String className;
    private URL url;

    MemorySpringBootInfoJavaClassObject(String className, URL url, JavacFileManager javacFileManager) {
        super(javacFileManager);
        this.className = className;
        this.url = url;
    }

    public JavaFileObject.Kind getKind() {
        return JavaFileObject.Kind.valueOf("CLASS");
    }

    public URI toUri() {
        try {
            return this.url.toURI();
        } catch (URISyntaxException e) {
            e.printStackTrace();
            return null;
        }
    }

    public String getName() {
        return this.className;
    }

    public InputStream openInputStream() {
        try {
            return this.url.openStream();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public OutputStream openOutputStream() throws IOException {
        return null;
    }

    public CharSequence getCharContent(boolean ignoreEncodingErrors) throws IOException {
        return null;
    }

    public Writer openWriter() throws IOException {
        return null;
    }

    public long getLastModified() {
        return 0L;
    }

    public boolean delete() {
        return false;
    }

    public String inferBinaryName() {
        return this.className;
    }

    public String getShortName() {
        return this.className.substring(this.className.lastIndexOf(ConstantEnum.POINT));
    }

    protected String inferBinaryName(Iterable<? extends File> iterable) {
        return this.className;
    }

    public boolean equals(Object o) {
        return false;
    }

    public int hashCode() {
        return 0;
    }

    public boolean isNameCompatible(String simpleName, JavaFileObject.Kind kind) {
        return false;
    }
}
