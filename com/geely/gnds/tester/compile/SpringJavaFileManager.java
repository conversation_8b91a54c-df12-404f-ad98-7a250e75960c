package com.geely.gnds.tester.compile;

import com.sun.tools.javac.file.JavacFileManager;
import com.sun.tools.javac.util.Context;
import com.sun.tools.javac.util.ListBuffer;
import java.io.File;
import java.lang.reflect.Constructor;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.Charset;
import javax.tools.JavaFileManager;

/* loaded from: SpringJavaFileManager.class */
public class SpringJavaFileManager extends JavacFileManager {
    public SpringJavaFileManager(Context context, boolean b, Charset charset) {
        super(context, b, charset);
    }

    public ClassLoader getClassLoader(JavaFileManager.Location location) {
        nullCheck(location);
        Iterable<File> var2 = getLocation(location);
        if (var2 == null) {
            return null;
        }
        ListBuffer var3 = new ListBuffer();
        for (File var5 : var2) {
            try {
                var3.append(var5.toURI().toURL());
            } catch (MalformedURLException var7) {
                throw new AssertionError(var7);
            }
        }
        return getClassLoader((URL[]) var3.toArray(new URL[var3.size()]));
    }

    protected ClassLoader getClassLoader(URL[] var1) {
        ClassLoader var2 = getClass().getClassLoader();
        try {
            Class loaderClass = Class.forName("org.springframework.boot.loader.LaunchedURLClassLoader");
            Class[] var4 = {URL[].class, ClassLoader.class};
            Constructor var5 = loaderClass.getConstructor(var4);
            return (ClassLoader) var5.newInstance(var1, var2);
        } catch (Throwable th) {
            return new URLClassLoader(var1, var2);
        }
    }
}
