package com.geely.gnds.tester.compile;

import cn.hutool.core.io.FileUtil;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.util.RuntimeUtils;
import com.sun.tools.javac.file.JavacFileManager;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FilterOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.nio.CharBuffer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.jar.JarEntry;
import java.util.stream.Collectors;
import javax.tools.FileObject;
import javax.tools.ForwardingJavaFileManager;
import javax.tools.JavaFileManager;
import javax.tools.JavaFileObject;
import javax.tools.SimpleJavaFileObject;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.loader.jar.JarFile;

/* loaded from: MemoryJavaFileManager.class */
public class MemoryJavaFileManager extends ForwardingJavaFileManager<JavaFileManager> {
    final Map<String, byte[]> classBytes;
    final Map<String, List<JavaFileObject>> classObjectPackageMap;
    private JavacFileManager javaFileManager;
    private boolean isInit;
    private static final Logger log = LoggerFactory.getLogger(MemoryJavaFileManager.class);
    public static final Map<String, List<JavaFileObject>> CLASS_OBJECT_PACKAGE_MAP = new HashMap();
    private static final Object LOCK = new Object();

    public void init() {
        JarFile jarFile = null;
        try {
            try {
                String jarBase = MemoryClassLoader.getPath();
                File jarBaseFile = new File(jarBase);
                loadLibrary();
                if (jarBaseFile.isFile()) {
                    jarFile = new JarFile(jarBaseFile);
                    List<JarEntry> entries = (List) jarFile.stream().filter(jarEntry -> {
                        return jarEntry.getName().endsWith(".jar");
                    }).collect(Collectors.toList());
                    for (JarEntry entry : entries) {
                        JarFile libTempJarFile = jarFile.getNestedJarFile(jarFile.getEntry(entry.getName()));
                        if (!libTempJarFile.getName().contains("tools-1.8.251.jar")) {
                            loadClass(libTempJarFile);
                        }
                    }
                    loadClass(jarFile.getNestedJarFile(jarFile.getJarEntry("BOOT-INF/classes")));
                }
                if (jarFile != null) {
                    try {
                        jarFile.close();
                    } catch (IOException e) {
                        log.error(e.getMessage());
                    }
                }
            } catch (Exception e2) {
                log.error(e2.getMessage());
                if (jarFile != null) {
                    try {
                        jarFile.close();
                    } catch (IOException e3) {
                        log.error(e3.getMessage());
                    }
                }
            }
            this.isInit = true;
        } catch (Throwable th) {
            if (jarFile != null) {
                try {
                    jarFile.close();
                } catch (IOException e4) {
                    log.error(e4.getMessage());
                }
            }
            throw th;
        }
    }

    private void loadLibrary() {
        String libs = RuntimeUtils.getHomePath() + File.separator + "lib";
        log.info("当前全路径：" + libs);
        if (FileUtil.isDirectory(libs)) {
            Collection<File> libJars = FileUtils.listFiles(new File(libs), new String[]{"jar"}, false);
            libJars.forEach(jar -> {
                try {
                    log.info("载入Jar: {}", jar.getAbsolutePath());
                    loadClass(new JarFile(jar));
                } catch (IOException e) {
                    log.error("无法载入lib目录下的jar包：{}", e.getMessage(), e);
                }
            });
        }
    }

    private void loadClass(JarFile jarFile) throws MalformedURLException {
        Enumeration<JarEntry> tempEntriesEnum = jarFile.entries();
        while (tempEntriesEnum.hasMoreElements()) {
            JarEntry jarEntry = tempEntriesEnum.nextElement();
            String classPath = jarEntry.getName().replace("/", ConstantEnum.POINT);
            if (classPath.endsWith(".class") && jarEntry.getName().lastIndexOf("/") != -1) {
                String packgeName = classPath.substring(0, jarEntry.getName().lastIndexOf("/"));
                List<JavaFileObject> onePackgeJavaFiles = CLASS_OBJECT_PACKAGE_MAP.containsKey(packgeName) ? CLASS_OBJECT_PACKAGE_MAP.get(packgeName) : new ArrayList<>();
                onePackgeJavaFiles.add(new MemorySpringBootInfoJavaClassObject(jarEntry.getName().replace("/", ConstantEnum.POINT).replace(".class", ""), new URL(jarFile.getUrl(), jarEntry.getName()), this.javaFileManager));
                CLASS_OBJECT_PACKAGE_MAP.put(packgeName, onePackgeJavaFiles);
            }
        }
    }

    MemoryJavaFileManager(JavaFileManager fileManager) {
        super(fileManager);
        this.classBytes = new HashMap();
        this.classObjectPackageMap = new HashMap();
        this.isInit = false;
        this.javaFileManager = (JavacFileManager) fileManager;
    }

    public Map<String, byte[]> getClassBytes() {
        return new HashMap(this.classBytes);
    }

    public void flush() throws IOException {
    }

    public void close() throws IOException {
        this.classBytes.clear();
    }

    public List<JavaFileObject> getLibJarsOptions(String packgeName) {
        synchronized (LOCK) {
            if (!this.isInit) {
                init();
            }
        }
        return CLASS_OBJECT_PACKAGE_MAP.get(packgeName);
    }

    public void preloadedJars() {
        synchronized (LOCK) {
            if (!this.isInit) {
                init();
            }
        }
    }

    public Iterable<JavaFileObject> list(JavaFileManager.Location location, String packageName, Set<JavaFileObject.Kind> kinds, boolean recurse) throws IOException {
        List<JavaFileObject> result;
        if (ConstantEnum.CLASS_PATH.equals(location.getName()) && MemoryClassLoader.isJar() && (result = getLibJarsOptions(packageName)) != null) {
            return result;
        }
        Iterable<JavaFileObject> it = super.list(location, packageName, kinds, recurse);
        if (kinds.contains(JavaFileObject.Kind.CLASS)) {
            List<JavaFileObject> javaFileObjectList = this.classObjectPackageMap.get(packageName);
            if (javaFileObjectList != null) {
                if (it != null) {
                    for (JavaFileObject javaFileObject : it) {
                        javaFileObjectList.add(javaFileObject);
                    }
                }
                return javaFileObjectList;
            }
            return it;
        }
        return it;
    }

    public String inferBinaryName(JavaFileManager.Location location, JavaFileObject file) {
        if (file instanceof MemoryInputJavaClassObject) {
            return ((MemoryInputJavaClassObject) file).inferBinaryName();
        }
        return super.inferBinaryName(location, file);
    }

    public JavaFileObject getJavaFileForOutput(JavaFileManager.Location location, String className, JavaFileObject.Kind kind, FileObject sibling) throws IOException {
        if (kind == JavaFileObject.Kind.CLASS) {
            return new MemoryOutputJavaClassObject(className);
        }
        return super.getJavaFileForOutput(location, className, kind, sibling);
    }

    JavaFileObject makeStringSource(String className, final String code) {
        String classPath = className.replace('.', '/') + JavaFileObject.Kind.SOURCE.extension;
        return new SimpleJavaFileObject(URI.create("string:///" + classPath), JavaFileObject.Kind.SOURCE) { // from class: com.geely.gnds.tester.compile.MemoryJavaFileManager.1
            /* renamed from: getCharContent, reason: merged with bridge method [inline-methods] */
            public CharBuffer m178getCharContent(boolean ignoreEncodingErrors) {
                return CharBuffer.wrap(code);
            }
        };
    }

    void makeBinaryClass(String className, byte[] bs) {
        JavaFileObject javaFileObject = new MemoryInputJavaClassObject(className, bs);
        String packageName = "";
        int pos = className.lastIndexOf(46);
        if (pos > 0) {
            packageName = className.substring(0, pos);
        }
        List<JavaFileObject> javaFileObjectList = this.classObjectPackageMap.get(packageName);
        if (javaFileObjectList == null) {
            List<JavaFileObject> javaFileObjectList2 = new LinkedList<>();
            javaFileObjectList2.add(javaFileObject);
            this.classObjectPackageMap.put(packageName, javaFileObjectList2);
            return;
        }
        javaFileObjectList.add(javaFileObject);
    }

    /* loaded from: MemoryJavaFileManager$MemoryInputJavaClassObject.class */
    class MemoryInputJavaClassObject extends SimpleJavaFileObject {
        final String className;
        final byte[] bs;

        MemoryInputJavaClassObject(String className, byte[] bs) {
            super(URI.create("string:///" + className.replace('.', '/') + JavaFileObject.Kind.CLASS.extension), JavaFileObject.Kind.CLASS);
            this.className = className;
            this.bs = bs;
        }

        public InputStream openInputStream() {
            return new ByteArrayInputStream(this.bs);
        }

        public String inferBinaryName() {
            return this.className;
        }
    }

    /* loaded from: MemoryJavaFileManager$MemoryOutputJavaClassObject.class */
    class MemoryOutputJavaClassObject extends SimpleJavaFileObject {
        final String className;

        MemoryOutputJavaClassObject(String className) {
            super(URI.create("string:///" + className.replace('.', '/') + JavaFileObject.Kind.CLASS.extension), JavaFileObject.Kind.CLASS);
            this.className = className;
        }

        public OutputStream openOutputStream() {
            return new FilterOutputStream(new ByteArrayOutputStream()) { // from class: com.geely.gnds.tester.compile.MemoryJavaFileManager.MemoryOutputJavaClassObject.1
                @Override // java.io.FilterOutputStream, java.io.OutputStream, java.io.Closeable, java.lang.AutoCloseable
                public void close() throws IOException {
                    this.out.close();
                    ByteArrayOutputStream bos = (ByteArrayOutputStream) this.out;
                    byte[] bs = bos.toByteArray();
                    MemoryJavaFileManager.this.classBytes.put(MemoryOutputJavaClassObject.this.className, bs);
                    MemoryJavaFileManager.this.makeBinaryClass(MemoryOutputJavaClassObject.this.className, bs);
                }
            };
        }
    }
}
