package com.geely.gnds.tester.compile;

import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.sun.tools.javac.util.Context;
import com.sun.tools.javac.util.Log;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.Writer;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.PreDestroy;
import javax.tools.Diagnostic;
import javax.tools.DiagnosticCollector;
import javax.tools.DiagnosticListener;
import javax.tools.JavaCompiler;
import javax.tools.JavaFileObject;
import javax.tools.ToolProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Component;

@Component
/* loaded from: MemoryClassLoader.class */
public class MemoryClassLoader extends URLClassLoader {
    private MemoryJavaFileManager manager;
    private static final String KEY_CLASSLOADER = "key_classloader";
    private static final Logger log = LoggerFactory.getLogger(MemoryClassLoader.class);
    private static final Map<String, byte[]> CLASS_BYTES = new ConcurrentHashMap();
    private static MemoryClassLoader instance = new MemoryClassLoader();
    private static final Map<String, MemoryClassLoader> CLASSLOADER_MAP = new ConcurrentHashMap<String, MemoryClassLoader>() { // from class: com.geely.gnds.tester.compile.MemoryClassLoader.1
        private static final long serialVersionUID = -5398651452664526443L;

        {
            put(MemoryClassLoader.KEY_CLASSLOADER, new MemoryClassLoader());
        }
    };

    @PreDestroy
    public void destroy() {
        try {
            if (this.manager != null) {
                this.manager.close();
            }
        } catch (IOException e) {
            log.error("释放动态编译资源失败！", e);
        }
    }

    private MemoryClassLoader() {
        super(new URL[0], MemoryClassLoader.class.getClassLoader());
        this.manager = null;
    }

    public static MemoryClassLoader genInstance() {
        long start = System.currentTimeMillis();
        synchronized (instance) {
            if (instance.manager == null) {
                CLASSLOADER_MAP.put(KEY_CLASSLOADER, instance);
                SpringJavaFileManager standardFileManager = getStandardFileManager(null, null, null);
                instance.manager = new MemoryJavaFileManager(standardFileManager);
                instance.manager.preloadedJars();
            }
        }
        long end = System.currentTimeMillis();
        log.info("动态编译生成实例耗时：{}毫秒", Long.valueOf(end - start));
        return instance;
    }

    public void registerJava(String className, String javaStr) throws Exception {
        try {
            CLASS_BYTES.putAll(compile(className, javaStr));
        } catch (Exception e) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00009), e);
            throw e;
        }
    }

    public Class<?> registerJava2(String className, String javaStr) throws Exception {
        try {
            Map<String, byte[]> map = compile(className, javaStr);
            byte[] buf = map.get(className);
            return defineClass(className, buf, 0, buf.length);
        } catch (Exception e) {
            log.error("注册Java字符串到内存类加载器中失败", e);
            throw e;
        }
    }

    public static SpringJavaFileManager getStandardFileManager(DiagnosticListener<? super JavaFileObject> var1, Locale var2, Charset var3) {
        Context var4 = new Context();
        var4.put(Locale.class, var2);
        if (var1 != null) {
            var4.put(DiagnosticListener.class, var1);
        }
        PrintWriter var5 = var3 == null ? new PrintWriter((OutputStream) System.err, true) : new PrintWriter((Writer) new OutputStreamWriter(System.err, var3), true);
        var4.put(Log.outKey, var5);
        return new SpringJavaFileManager(var4, true, var3);
    }

    public Map<String, byte[]> compile(String className, String javaStr) {
        Boolean result;
        List<Diagnostic<? extends JavaFileObject>> diagnostics;
        log.debug("开始动态编译：" + className);
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        synchronized (this) {
            try {
                JavaFileObject javaFileObject = this.manager.makeStringSource(className, javaStr);
                DiagnosticCollector<JavaFileObject> collector = new DiagnosticCollector<>();
                JavaCompiler.CompilationTask task = compiler.getTask((Writer) null, this.manager, collector, (Iterable) null, (Iterable) null, Arrays.asList(javaFileObject));
                result = task.call();
                diagnostics = collector.getDiagnostics();
            } catch (Exception e) {
                log.error("动态编译出错", e);
            }
            if (result != null && result.booleanValue()) {
                log.info("动态编译成功：" + className);
                return this.manager.getClassBytes();
            }
            log.error("动态编译失败：" + className);
            StringBuilder sb = new StringBuilder();
            for (Diagnostic<? extends JavaFileObject> diagnostic : diagnostics) {
                sb.append("\n").append("line:" + diagnostic.getLineNumber()).append("\n").append("msg:" + diagnostic.getMessage(Locale.ENGLISH));
            }
            log.error(sb.toString());
            return null;
        }
    }

    @Override // java.net.URLClassLoader, java.lang.ClassLoader
    public Class<?> findClass(String name) throws ClassNotFoundException {
        byte[] buf = CLASS_BYTES.get(name);
        if (buf == null) {
            return super.findClass(name);
        }
        CLASS_BYTES.remove(name);
        return defineClass(name, buf, 0, buf.length);
    }

    public Class<?> getClass(String name) throws ClassNotFoundException {
        return findClass(name);
    }

    public static String getPath() {
        ApplicationHome home = new ApplicationHome(MemoryJavaFileManager.class);
        String path = home.getSource().getPath();
        return path;
    }

    public static boolean isJar() {
        return getPath().endsWith(".jar");
    }
}
