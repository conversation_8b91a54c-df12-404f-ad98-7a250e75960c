package com.geely.gnds.tester.dto;

/* loaded from: DownloadProgressDto.class */
public class DownloadProgressDto {
    private Float downloadTime;
    private Float progress;
    private boolean checkMd5;
    private boolean executeSql;
    private String speed;

    public DownloadProgressDto(Float downloadTime, Float progress, boolean checkMd5, boolean executeSql, String speed) {
        this.downloadTime = downloadTime;
        this.progress = progress;
        this.checkMd5 = checkMd5;
        this.executeSql = executeSql;
        this.speed = speed;
    }

    public Float getDownloadTime() {
        return this.downloadTime;
    }

    public void setDownloadTime(Float downloadTime) {
        this.downloadTime = downloadTime;
    }

    public Float getProgress() {
        return this.progress;
    }

    public void setProgress(Float progress) {
        this.progress = progress;
    }

    public boolean isCheckMd5() {
        return this.checkMd5;
    }

    public void setCheckMd5(boolean checkMd5) {
        this.checkMd5 = checkMd5;
    }

    public boolean isExecuteSql() {
        return this.executeSql;
    }

    public void setExecuteSql(boolean executeSql) {
        this.executeSql = executeSql;
    }

    public DownloadProgressDto() {
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("DownloadProgressDto{");
        sb.append("downloadTime=").append(this.downloadTime);
        sb.append(", progress=").append(this.progress);
        sb.append(", checkMd5=").append(this.checkMd5);
        sb.append(", executeSql=").append(this.executeSql);
        sb.append('}');
        return sb.toString();
    }

    public String getSpeed() {
        return this.speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }
}
