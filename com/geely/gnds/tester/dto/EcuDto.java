package com.geely.gnds.tester.dto;

import java.io.Serializable;
import java.util.List;

/* loaded from: EcuDto.class */
public class EcuDto implements Serializable {
    private String ecuAddress;
    private String ecuName;
    private List<SoftwareDto> softwares;
    private List<EcuPinCodeDto> ecuPinCodes;
    private String name;
    private int id;
    private String hardwareStatus;
    private String hardwareFailedMsg;
    private String softwareStatus;
    private String softwareFailedMsg;
    private Long vbfSize;
    private float downloadTime;
    private String ecuFullName;
    private boolean packageTips;
    private List<SoftwarePackageDTO> softwarePackages;

    public String getHardwareStatus() {
        return this.hardwareStatus;
    }

    public void setHardwareStatus(String hardwareStatus) {
        this.hardwareStatus = hardwareStatus;
    }

    public String getHardwareFailedMsg() {
        return this.hardwareFailedMsg;
    }

    public void setHardwareFailedMsg(String hardwareFailedMsg) {
        this.hardwareFailedMsg = hardwareFailedMsg;
    }

    public String getSoftwareStatus() {
        return this.softwareStatus;
    }

    public void setSoftwareStatus(String softwareStatus) {
        this.softwareStatus = softwareStatus;
    }

    public String getSoftwareFailedMsg() {
        return this.softwareFailedMsg;
    }

    public void setSoftwareFailedMsg(String softwareFailedMsg) {
        this.softwareFailedMsg = softwareFailedMsg;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public List<SoftwareDto> getSoftwares() {
        return this.softwares;
    }

    public void setSoftwares(List<SoftwareDto> softwares) {
        this.softwares = softwares;
    }

    public List<EcuPinCodeDto> getEcuPinCodes() {
        return this.ecuPinCodes;
    }

    public void setEcuPinCodes(List<EcuPinCodeDto> ecuPinCodes) {
        this.ecuPinCodes = ecuPinCodes;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Long getVbfSize() {
        return this.vbfSize;
    }

    public void setVbfSize(Long vbfSize) {
        this.vbfSize = vbfSize;
    }

    public float getDownloadTime() {
        return this.downloadTime;
    }

    public void setDownloadTime(float downloadTime) {
        this.downloadTime = downloadTime;
    }

    public String getEcuFullName() {
        return this.ecuFullName;
    }

    public void setEcuFullName(String ecuFullName) {
        this.ecuFullName = ecuFullName;
    }

    public boolean isPackageTips() {
        return this.packageTips;
    }

    public void setPackageTips(boolean packageTips) {
        this.packageTips = packageTips;
    }

    public List<SoftwarePackageDTO> getSoftwarePackages() {
        return this.softwarePackages;
    }

    public void setSoftwarePackages(List<SoftwarePackageDTO> softwarePackages) {
        this.softwarePackages = softwarePackages;
    }

    public String toString() {
        return "EcuDto{ecuAddress='" + this.ecuAddress + "', ecuName='" + this.ecuName + "', softwares=" + this.softwares + ", ecuPinCodes=" + this.ecuPinCodes + ", name='" + this.name + "', id=" + this.id + ", hardwareStatus='" + this.hardwareStatus + "', hardwareFailedMsg='" + this.hardwareFailedMsg + "', softwareStatus='" + this.softwareStatus + "', softwareFailedMsg='" + this.softwareFailedMsg + "', vbfSize=" + this.vbfSize + ", downloadTime=" + this.downloadTime + ", ecuFullName='" + this.ecuFullName + "', packageTips=" + this.packageTips + ", softwarePackages=" + this.softwarePackages + '}';
    }
}
