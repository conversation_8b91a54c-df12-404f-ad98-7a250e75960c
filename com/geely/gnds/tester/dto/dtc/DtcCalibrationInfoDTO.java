package com.geely.gnds.tester.dto.dtc;

import java.util.StringJoiner;

/* loaded from: DtcCalibrationInfoDTO.class */
public class DtcCalibrationInfoDTO {
    private String dtcId;
    private String serviceId;
    private String name;
    private Integer jR;
    private Boolean jS;
    private Integer jT;
    private Integer jU;
    private Integer jV;
    private Integer jW;
    private Integer jX;
    private Integer jY;
    private String agingCounter;
    private Integer jZ;
    private Boolean ka;

    public String getDtcId() {
        return this.dtcId;
    }

    public void setDtcId(String dtcId) {
        this.dtcId = dtcId;
    }

    public String getServiceId() {
        return this.serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getConfirmedDtcLimit() {
        return this.jR;
    }

    public void setConfirmedDtcLimit(Integer confirmedDtcLimit) {
        this.jR = confirmedDtcLimit;
    }

    public Boolean getWarningIndicator() {
        return this.jS;
    }

    public void setWarningIndicator(Boolean warningIndicator) {
        this.jS = warningIndicator;
    }

    public Integer getUnConfirmedDtcLimit() {
        return this.jT;
    }

    public void setUnConfirmedDtcLimit(Integer unConfirmedDtcLimit) {
        this.jT = unConfirmedDtcLimit;
    }

    public Integer getAgedDtcLimit() {
        return this.jU;
    }

    public void setAgedDtcLimit(Integer agedDtcLimit) {
        this.jU = agedDtcLimit;
    }

    public Integer getIncrementStepSize() {
        return this.jV;
    }

    public void setIncrementStepSize(Integer incrementStepSize) {
        this.jV = incrementStepSize;
    }

    public Integer getDecrementStepSize() {
        return this.jW;
    }

    public void setDecrementStepSize(Integer decrementStepSize) {
        this.jW = decrementStepSize;
    }

    public Integer getFailedThreshold() {
        return this.jX;
    }

    public void setFailedThreshold(Integer failedThreshold) {
        this.jX = failedThreshold;
    }

    public Integer getPassedThreshold() {
        return this.jY;
    }

    public void setPassedThreshold(Integer passedThreshold) {
        this.jY = passedThreshold;
    }

    public String getAgingCounter() {
        return this.agingCounter;
    }

    public void setAgingCounter(String agingCounter) {
        this.agingCounter = agingCounter;
    }

    public Integer getDtceventPriority() {
        return this.jZ;
    }

    public void setDtceventPriority(Integer dtceventPriority) {
        this.jZ = dtceventPriority;
    }

    public Boolean getJumpDown() {
        return this.ka;
    }

    public void setJumpDown(Boolean jumpDown) {
        this.ka = jumpDown;
    }

    public String toString() {
        return new StringJoiner(", ", DtcCalibrationInfoDTO.class.getSimpleName() + "[", "]").add("dtcId='" + this.dtcId + "'").add("serviceId='" + this.serviceId + "'").add("name='" + this.name + "'").add("confirmedDtcLimit=" + this.jR).add("warningIndicator=" + this.jS).add("unConfirmedDtcLimit=" + this.jT).add("agedDtcLimit=" + this.jU).add("incrementStepSize=" + this.jV).add("decrementStepSize=" + this.jW).add("failedThreshold=" + this.jX).add("passedThreshold=" + this.jY).add("agingCounter='" + this.agingCounter + "'").add("dtceventPriority=" + this.jZ).add("jumpDown=" + this.ka).toString();
    }
}
