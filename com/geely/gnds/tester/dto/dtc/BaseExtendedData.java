package com.geely.gnds.tester.dto.dtc;

import java.io.Serializable;
import java.util.StringJoiner;

/* loaded from: BaseExtendedData.class */
public class BaseExtendedData<R> implements Serializable {
    private static final long serialVersionUID = -6431693179839641616L;
    private String name;
    private R value;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public R getValue() {
        return this.value;
    }

    public void setValue(R value) {
        this.value = value;
    }

    public String toString() {
        return new StringJoiner(", ", BaseExtendedData.class.getSimpleName() + "[", "]").add("name='" + this.name + "'").add("value='" + this.value + "'").toString();
    }
}
