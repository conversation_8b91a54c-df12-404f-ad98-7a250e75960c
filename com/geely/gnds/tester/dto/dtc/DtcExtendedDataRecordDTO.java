package com.geely.gnds.tester.dto.dtc;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/* loaded from: DtcExtendedDataRecordDTO.class */
public class DtcExtendedDataRecordDTO implements Serializable {
    private static final long serialVersionUID = 2459514225234045125L;

    @JsonIgnore
    private String kg;
    private List<ExtendedDataOccDTO> kh;
    private List<ExtendedDataFdcDTO> ki;
    private List<ExtendedDataTimestampDTO> kj;
    private ExtendedDataDtcIndicatorDTO kk;

    public List<ExtendedDataOccDTO> getOccDTO() {
        return this.kh;
    }

    public void setOccDTO(List<ExtendedDataOccDTO> occDTO) {
        this.kh = occDTO;
    }

    public List<ExtendedDataFdcDTO> getFdcDTO() {
        return this.ki;
    }

    public void setFdcDTO(List<ExtendedDataFdcDTO> fdcDTO) {
        this.ki = fdcDTO;
    }

    public List<ExtendedDataTimestampDTO> getTimestampDTO() {
        return this.kj;
    }

    public void setTimestampDTO(List<ExtendedDataTimestampDTO> timestampDTO) {
        this.kj = timestampDTO;
    }

    public ExtendedDataDtcIndicatorDTO getIndicatorDTO() {
        return this.kk;
    }

    public void setIndicatorDTO(ExtendedDataDtcIndicatorDTO indicatorDTO) {
        this.kk = indicatorDTO;
    }

    public String getRowData() {
        return this.kg;
    }

    public void setRowData(String rowData) {
        this.kg = rowData;
    }

    public String toString() {
        return new StringJoiner(", ", DtcExtendedDataRecordDTO.class.getSimpleName() + "[", "]").add("rowData='" + this.kg + "'").add("occDTO=" + this.kh).add("fdcDTO=" + this.ki).add("timestampDTO=" + this.kj).add("indicatorDTO=" + this.kk).toString();
    }
}
