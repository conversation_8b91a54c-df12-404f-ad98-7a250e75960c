package com.geely.gnds.tester.dto.dtc;

import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: DtcInfoDTO.class */
public class DtcInfoDTO implements Serializable {
    private static final long serialVersionUID = -1466366141856118396L;
    private String id;
    private String dtcId;
    private String name;
    private String kl;
    private String km;
    private String ecuName;
    private String kn;
    private String ko;
    private Integer status;
    private String ecuAddress;
    private String kp;
    private Boolean kq;
    private String diagnosticNumber;
    private String kr;

    public String getFunctionIds() {
        return this.kr;
    }

    public void setFunctionIds(String functionIds) {
        this.kr = functionIds;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getIsBold() {
        return this.kq;
    }

    public void setIsBold(Boolean isBold) {
        this.kq = isBold;
    }

    public String getDtc() {
        return this.kl;
    }

    public void setDtc(String id) {
        this.kl = id;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getDtcValue() {
        return this.kn;
    }

    public void setDtcValue(String dtcValue) {
        this.kn = dtcValue;
    }

    public String getDtcName() {
        return this.ko;
    }

    public void setDtcName(String dtcName) {
        this.ko = dtcName;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDtcId() {
        return this.dtcId;
    }

    public void setDtcId(String dtcId) {
        this.dtcId = dtcId;
    }

    public String getDtcInfoId() {
        return this.kp;
    }

    public void setDtcInfoId(String dtcInfoId) {
        this.kp = dtcInfoId;
    }

    public Boolean getBold() {
        return this.kq;
    }

    public void setBold(Boolean bold) {
        this.kq = bold;
    }

    public String getDiagnosticNumber() {
        return this.diagnosticNumber;
    }

    public void setDiagnosticNumber(String diagnosticNumber) {
        this.diagnosticNumber = diagnosticNumber;
    }

    public String getRawDtc() {
        return this.km;
    }

    public void setRawDtc(String rawDtc) {
        this.km = rawDtc;
    }

    public String toString() {
        return new ToStringBuilder(this).append("id", this.id).append("dtcId", this.dtcId).append("name", this.name).append("dtc", this.kl).append("rawDtc", this.km).append("ecuName", this.ecuName).append("dtcValue", this.kn).append("dtcName", this.ko).append("status", this.status).append("ecuAddress", this.ecuAddress).append("dtcInfoId", this.kp).append("isBold", this.kq).append("diagnosticNumber", this.diagnosticNumber).toString();
    }
}
