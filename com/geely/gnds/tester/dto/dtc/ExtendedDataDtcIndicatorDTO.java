package com.geely.gnds.tester.dto.dtc;

import java.io.Serializable;
import java.util.StringJoiner;

/* loaded from: ExtendedDataDtcIndicatorDTO.class */
public class ExtendedDataDtcIndicatorDTO extends BaseExtendedData implements Serializable {
    private static final long serialVersionUID = 8740398937413939327L;

    @Override // com.geely.gnds.tester.dto.dtc.BaseExtendedData
    public String toString() {
        return new StringJoiner(", ", ExtendedDataDtcIndicatorDTO.class.getSimpleName() + "[", "]").add("name='" + super.getName() + "'").add("value='" + super.getValue() + "'").toString();
    }
}
