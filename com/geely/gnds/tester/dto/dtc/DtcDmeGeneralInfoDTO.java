package com.geely.gnds.tester.dto.dtc;

import java.io.Serializable;
import java.util.StringJoiner;

/* loaded from: DtcDmeGeneralInfoDTO.class */
public class DtcDmeGeneralInfoDTO implements Serializable {
    private static final long serialVersionUID = 3618030473128418389L;
    private String kb;
    private String kc;
    private String kd;
    private String ke;
    private String kf;

    public String getDetectedFaultError() {
        return this.kb;
    }

    public void setDetectedFaultError(String detectedFaultError) {
        this.kb = detectedFaultError;
    }

    public String getTestRunCriteria() {
        return this.kc;
    }

    public void setTestRunCriteria(String testRunCriteria) {
        this.kc = testRunCriteria;
    }

    public String getDetectionCriteria() {
        return this.kd;
    }

    public void setDetectionCriteria(String detectionCriteria) {
        this.kd = detectionCriteria;
    }

    public String getFaultEffectCriteriaInEcu() {
        return this.ke;
    }

    public void setFaultEffectCriteriaInEcu(String faultEffectCriteriaInEcu) {
        this.ke = faultEffectCriteriaInEcu;
    }

    public String getFaultCause() {
        return this.kf;
    }

    public void setFaultCause(String faultCause) {
        this.kf = faultCause;
    }

    public String toString() {
        return new StringJoiner(", ", DtcDmeGeneralInfoDTO.class.getSimpleName() + "[", "]").add("detectedFaultError='" + this.kb + "'").add("testRunCriteria='" + this.kc + "'").add("detectionCriteria='" + this.kd + "'").add("faultEffectCriteriaInEcu='" + this.ke + "'").add("faultCause='" + this.kf + "'").toString();
    }
}
