package com.geely.gnds.tester.dto.dtc;

import java.io.Serializable;
import java.util.StringJoiner;

/* loaded from: ExtendedDataFdcDTO.class */
public class ExtendedDataFdcDTO<Integer> extends BaseExtendedData implements Serializable {
    private static final long serialVersionUID = 2924834044292152412L;

    @Override // com.geely.gnds.tester.dto.dtc.BaseExtendedData
    public String toString() {
        return new StringJoiner(", ", ExtendedDataFdcDTO.class.getSimpleName() + "[", "]").add("name='" + super.getName() + "'").add("value='" + super.getValue() + "'").toString();
    }
}
