package com.geely.gnds.tester.dto.dtc;

import java.io.Serializable;
import java.util.StringJoiner;

/* loaded from: ExtendedDataOccDTO.class */
public class ExtendedDataOccDTO extends BaseExtendedData implements Serializable {
    private static final long serialVersionUID = -7459591843124390083L;

    @Override // com.geely.gnds.tester.dto.dtc.BaseExtendedData
    public String toString() {
        return new StringJoiner(", ", ExtendedDataOccDTO.class.getSimpleName() + "[", "]").add("name='" + super.getName() + "'").add("value='" + super.getValue() + "'").toString();
    }
}
