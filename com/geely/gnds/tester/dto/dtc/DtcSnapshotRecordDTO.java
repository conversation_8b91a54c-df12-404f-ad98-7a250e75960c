package com.geely.gnds.tester.dto.dtc;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/* loaded from: DtcSnapshotRecordDTO.class */
public class DtcSnapshotRecordDTO implements Serializable {
    private static final long serialVersionUID = -8362167242111925166L;

    @JsonIgnore
    private String ks;
    private String name;
    private Integer kt;
    private List<DidDTO> ku;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getParametersNumber() {
        return this.kt;
    }

    public void setParametersNumber(Integer parametersNumber) {
        this.kt = parametersNumber;
    }

    public List<DidDTO> getDidList() {
        return this.ku;
    }

    public void setDidList(List<DidDTO> didList) {
        this.ku = didList;
    }

    public String getRawData() {
        return this.ks;
    }

    public void setRawData(String rawData) {
        this.ks = rawData;
    }

    public String toString() {
        return new StringJoiner(", ", DtcSnapshotRecordDTO.class.getSimpleName() + "[", "]").add("rawData='" + this.ks + "'").add("name='" + this.name + "'").add("parametersNumber=" + this.kt).add("didList=" + this.ku).toString();
    }

    /* loaded from: DtcSnapshotRecordDTO$DidDTO.class */
    public static class DidDTO implements Serializable {
        private static final long serialVersionUID = 7448137161811742420L;

        @JsonIgnore
        private Long id;
        private String didName;
        private String kv;

        public String getDidName() {
            return this.didName;
        }

        public Long getId() {
            return this.id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public void setDidName(String didName) {
            this.didName = didName;
        }

        public String getDidValue() {
            return this.kv;
        }

        public void setDidValue(String didValue) {
            this.kv = didValue;
        }

        public String toString() {
            return new StringJoiner(", ", DidDTO.class.getSimpleName() + "[", "]").add("id='" + this.id + "'").add("didName='" + this.didName + "'").add("didValue='" + this.kv + "'").toString();
        }
    }
}
