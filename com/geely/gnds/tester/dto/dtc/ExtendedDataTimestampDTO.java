package com.geely.gnds.tester.dto.dtc;

import java.io.Serializable;
import java.util.StringJoiner;

/* loaded from: ExtendedDataTimestampDTO.class */
public class ExtendedDataTimestampDTO<Long> extends BaseExtendedData implements Serializable {
    private static final long serialVersionUID = 7808684504482462847L;

    @Override // com.geely.gnds.tester.dto.dtc.BaseExtendedData
    public String toString() {
        return new StringJoiner(", ", ExtendedDataTimestampDTO.class.getSimpleName() + "[", "]").add("name='" + super.getName() + "'").add("value='" + super.getValue() + "'").toString();
    }
}
