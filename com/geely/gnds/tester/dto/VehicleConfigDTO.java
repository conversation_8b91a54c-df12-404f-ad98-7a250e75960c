package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/* loaded from: VehicleConfigDTO.class */
public class VehicleConfigDTO {
    private VehicleConfigData config;
    private VehicleConfigData mileage;

    @JsonProperty("Config")
    public VehicleConfigData getConfig() {
        return this.config;
    }

    @JsonProperty("Config")
    public void setConfig(VehicleConfigData config) {
        this.config = config;
    }

    @JsonProperty("Mileage")
    public VehicleConfigData getMileage() {
        return this.mileage;
    }

    @JsonProperty("Mileage")
    public void setMileage(VehicleConfigData mileage) {
        this.mileage = mileage;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("VehicleConfigDTO{");
        sb.append("config=").append(this.config);
        sb.append(", mileage=").append(this.mileage);
        sb.append('}');
        return sb.toString();
    }

    /* loaded from: VehicleConfigDTO$VehicleConfigData.class */
    public static class VehicleConfigData {
        private String ecu;
        private String did;
        private String value;

        @JsonProperty("ECU")
        public String getEcu() {
            return this.ecu;
        }

        @JsonProperty("ECU")
        public void setEcu(String ecu) {
            this.ecu = ecu;
        }

        @JsonProperty("DID")
        public String getDid() {
            return this.did;
        }

        @JsonProperty("DID")
        public void setDid(String did) {
            this.did = did;
        }

        @JsonProperty("Value")
        public String getValue() {
            return this.value;
        }

        @JsonProperty("Value")
        public void setValue(String value) {
            this.value = value;
        }

        public String toString() {
            StringBuilder sb = new StringBuilder("VehicleConfigData{");
            sb.append("ecu='").append(this.ecu).append('\'');
            sb.append(", did='").append(this.did).append('\'');
            sb.append(", value='").append(this.value).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }
}
