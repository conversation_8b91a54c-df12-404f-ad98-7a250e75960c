package com.geely.gnds.tester.dto;

import com.geely.gnds.doip.client.dro.FdDroFile;
import com.geely.gnds.doip.client.dro.document.event.DiagnosticReadoutEvent;
import com.geely.gnds.doip.client.dro.document.event.Ecu;
import com.geely.gnds.doip.client.dro.document.root.DroResult;
import com.geely.gnds.tester.common.UploadCloudBean;
import java.util.List;

/* loaded from: DroDTO.class */
public class DroDTO {
    private String vin;
    private List<EcuBroadcastDto> ecuBroadcastDtoList;
    private DroResult droResult;
    private List<Ecu> ecuList;
    private DiagnosticReadoutEvent diagnosticReadoutEvent;
    private FdDroFile droFile;
    private List<DroUdsDto> droUdsList;
    private String language;
    private Integer platformCode;
    private String dtcInfo;
    private UploadCloudBean uploadCloudBean;

    public DroResult getDroResult() {
        return this.droResult;
    }

    public void setDroResult(Dro<PERSON><PERSON>ult droResult) {
        this.droResult = droResult;
    }

    public List<Ecu> getEcuList() {
        return this.ecuList;
    }

    public void setEcuList(List<Ecu> ecuList) {
        this.ecuList = ecuList;
    }

    public DiagnosticReadoutEvent getDiagnosticReadoutEvent() {
        return this.diagnosticReadoutEvent;
    }

    public void setDiagnosticReadoutEvent(DiagnosticReadoutEvent diagnosticReadoutEvent) {
        this.diagnosticReadoutEvent = diagnosticReadoutEvent;
    }

    public FdDroFile getDroFile() {
        return this.droFile;
    }

    public void setDroFile(FdDroFile droFile) {
        this.droFile = droFile;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public List<EcuBroadcastDto> getEcuBroadcastDtoList() {
        return this.ecuBroadcastDtoList;
    }

    public void setEcuBroadcastDtoList(List<EcuBroadcastDto> ecuBroadcastDtoList) {
        this.ecuBroadcastDtoList = ecuBroadcastDtoList;
    }

    public List<DroUdsDto> getDroUdsList() {
        return this.droUdsList;
    }

    public void setDroUdsList(List<DroUdsDto> droUdsList) {
        this.droUdsList = droUdsList;
    }

    public String getLanguage() {
        return this.language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getPlatformCode() {
        return this.platformCode;
    }

    public void setPlatformCode(Integer platformCode) {
        this.platformCode = platformCode;
    }

    public String getDtcInfo() {
        return this.dtcInfo;
    }

    public void setDtcInfo(String dtcInfo) {
        this.dtcInfo = dtcInfo;
    }

    public UploadCloudBean getUploadCloudBean() {
        return this.uploadCloudBean;
    }

    public void setUploadCloudBean(UploadCloudBean uploadCloudBean) {
        this.uploadCloudBean = uploadCloudBean;
    }
}
