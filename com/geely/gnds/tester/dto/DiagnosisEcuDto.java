package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import java.util.Objects;

/* loaded from: DiagnosisEcuDto.class */
public class DiagnosisEcuDto {

    @JsonProperty("ECU_address")
    private String ecuAddress;

    @JsonProperty("ECU_name")
    private String ecuName;

    @JsonProperty("DTC_info")
    private Map dtcInfo;

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public Map getDtcInfo() {
        return this.dtcInfo;
    }

    public void setDtcInfo(Map dtcInfo) {
        this.dtcInfo = dtcInfo;
    }

    public String toString() {
        return "DiagnosisEcuDto{ecuAddress='" + this.ecuAddress + "', ecuName='" + this.ecuName + "', dtcInfo=" + this.dtcInfo + '}';
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DiagnosisEcuDto that = (DiagnosisEcuDto) o;
        return Objects.equals(this.ecuAddress, that.ecuAddress) && Objects.equals(this.ecuName, that.ecuName) && Objects.equals(this.dtcInfo, that.dtcInfo);
    }

    public int hashCode() {
        return Objects.hash(this.ecuAddress, this.ecuName, this.dtcInfo);
    }

    public DiagnosisEcuDto(String ecuAddress, String ecuName, Map dtcInfo) {
        this.ecuAddress = ecuAddress;
        this.ecuName = ecuName;
        this.dtcInfo = dtcInfo;
    }

    public DiagnosisEcuDto() {
    }
}
