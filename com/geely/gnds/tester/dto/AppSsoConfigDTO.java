package com.geely.gnds.tester.dto;

import java.util.Objects;

/* loaded from: AppSsoConfigDTO.class */
public class AppSsoConfigDTO {
    private String clientId;
    private String clientSecret;
    private String pageUrl;
    private String state;

    public String getClientId() {
        return this.clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return this.clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getPageUrl() {
        return this.pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public AppSsoConfigDTO() {
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AppSsoConfigDTO that = (AppSsoConfigDTO) o;
        return Objects.equals(this.clientId, that.clientId) && Objects.equals(this.clientSecret, that.clientSecret) && Objects.equals(this.pageUrl, that.pageUrl);
    }

    public int hashCode() {
        return Objects.hash(this.clientId, this.clientSecret, this.pageUrl);
    }

    public String toString() {
        return "CepConfigDTO{clientId='" + this.clientId + "', clientSecret='" + this.clientSecret + "', cepUaaUrl='" + this.pageUrl + "'}";
    }

    public AppSsoConfigDTO(String clientId, String clientSecret, String pageUrl) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.pageUrl = pageUrl;
    }
}
