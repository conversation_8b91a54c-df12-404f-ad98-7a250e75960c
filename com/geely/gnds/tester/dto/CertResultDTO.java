package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

/* loaded from: CertResultDTO.class */
public class CertResultDTO implements Serializable {

    @JsonProperty("a")
    private String aesEncryptionKey;

    @JsonProperty("b")
    private String certContent;

    @JsonProperty("c")
    private String privateKey;

    public CertResultDTO() {
    }

    public CertResultDTO(String aesEncryptionKey, String certContent, String privateKey) {
        this.aesEncryptionKey = aesEncryptionKey;
        this.certContent = certContent;
        this.privateKey = privateKey;
    }

    public String getAesEncryptionKey() {
        return this.aesEncryptionKey;
    }

    public void setAesEncryptionKey(String aesEncryptionKey) {
        this.aesEncryptionKey = aesEncryptionKey;
    }

    public String getCertContent() {
        return this.certContent;
    }

    public void setCertContent(String certContent) {
        this.certContent = certContent;
    }

    public String getPrivateKey() {
        return this.privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }
}
