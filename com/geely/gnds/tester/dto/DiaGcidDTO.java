package com.geely.gnds.tester.dto;

/* loaded from: DiaGcidDTO.class */
public class DiaGcidDTO {
    private Long id;
    private String gcid;
    private String gcidName;
    private String locationName;
    private String listName;
    private String wdid;
    private String ecu;
    private String structureWeekFrom;
    private String structureWeekTo;
    private String variant;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGcid() {
        return this.gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public String getGcidName() {
        return this.gcidName;
    }

    public void setGcidName(String gcidName) {
        this.gcidName = gcidName;
    }

    public String getLocationName() {
        return this.locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getListName() {
        return this.listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public String getWdid() {
        return this.wdid;
    }

    public void setWdid(String wdid) {
        this.wdid = wdid;
    }

    public String getEcu() {
        return this.ecu;
    }

    public void setEcu(String ecu) {
        this.ecu = ecu;
    }

    public String getStructureWeekFrom() {
        return this.structureWeekFrom;
    }

    public void setStructureWeekFrom(String structureWeekFrom) {
        this.structureWeekFrom = structureWeekFrom;
    }

    public String getStructureWeekTo() {
        return this.structureWeekTo;
    }

    public void setStructureWeekTo(String structureWeekTo) {
        this.structureWeekTo = structureWeekTo;
    }

    public String getVariant() {
        return this.variant;
    }

    public void setVariant(String variant) {
        this.variant = variant;
    }

    public String toString() {
        return "DiaGcidDTO{id=" + this.id + ", gcid='" + this.gcid + "', gcidName='" + this.gcidName + "', locationName='" + this.locationName + "', listName='" + this.listName + "', wdid='" + this.wdid + "', ecu='" + this.ecu + "', structureWeekFrom='" + this.structureWeekFrom + "', structureWeekTo='" + this.structureWeekTo + "', variant='" + this.variant + "'}";
    }
}
