package com.geely.gnds.tester.dto;

/* loaded from: TechnicalReqDTO.class */
public class TechnicalReqDTO {
    private static final long serialVersionUID = 3444446297300591995L;
    private String accessToken;
    private int pageNum;
    private int pageSize;
    private String vin;
    private String dtcCodes;
    private String cscType;
    private String cscFg2;
    private String infoCsc;

    public String getAccessToken() {
        return this.accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public int getPageNum() {
        return this.pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getDtcCodes() {
        return this.dtcCodes;
    }

    public void setDtcCodes(String dtcCodes) {
        this.dtcCodes = dtcCodes;
    }

    public String getCscType() {
        return this.cscType;
    }

    public void setCscType(String cscType) {
        this.cscType = cscType;
    }

    public String getCscFg2() {
        return this.cscFg2;
    }

    public void setCscFg2(String cscFg2) {
        this.cscFg2 = cscFg2;
    }

    public String getInfoCsc() {
        return this.infoCsc;
    }

    public void setInfoCsc(String infoCsc) {
        this.infoCsc = infoCsc;
    }
}
