package com.geely.gnds.tester.dto;

import java.io.Serializable;

/* loaded from: VehicleTodoDTO.class */
public class VehicleTodoDTO implements Serializable {
    private static final long serialVersionUID = 7397898342323779915L;
    private Long id;
    private String vin;
    private String model;
    private String item;
    private Integer itemCategory;
    private String operation;
    private String seqCode;
    private Integer state;
    private Long tenantCode;
    private String url;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getItem() {
        return this.item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public Integer getItemCategory() {
        return this.itemCategory;
    }

    public void setItemCategory(Integer itemCategory) {
        this.itemCategory = itemCategory;
    }

    public String getOperation() {
        return this.operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getSeqCode() {
        return this.seqCode;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public Integer getState() {
        return this.state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(Long tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer("VehicleTodoDTO{");
        sb.append("id=").append(this.id);
        sb.append(", vin='").append(this.vin).append('\'');
        sb.append(", model='").append(this.model).append('\'');
        sb.append(", item='").append(this.item).append('\'');
        sb.append(", itemCategory=").append(this.itemCategory);
        sb.append(", operation='").append(this.operation).append('\'');
        sb.append(", seqCode='").append(this.seqCode).append('\'');
        sb.append(", state=").append(this.state);
        sb.append(", tenantCode=").append(this.tenantCode);
        sb.append('}');
        return sb.toString();
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
