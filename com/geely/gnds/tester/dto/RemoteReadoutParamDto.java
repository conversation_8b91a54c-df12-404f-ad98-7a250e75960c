package com.geely.gnds.tester.dto;

/* loaded from: RemoteReadoutParamDto.class */
public class RemoteReadoutParamDto {
    private String address;
    private String diagnosticPartNumber;
    private String dataIdentifierId;

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public String getDataIdentifierId() {
        return this.dataIdentifierId;
    }

    public void setDataIdentifierId(String dataIdentifierId) {
        this.dataIdentifierId = dataIdentifierId;
    }

    public String toString() {
        return "RemoteReadoutParamDto{address='" + this.address + "', diagnosticPartNumber='" + this.diagnosticPartNumber + "', dataIdentifierId='" + this.dataIdentifierId + "'}";
    }
}
