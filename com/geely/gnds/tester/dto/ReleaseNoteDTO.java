package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: ReleaseNoteDTO.class */
public class ReleaseNoteDTO implements Serializable {
    private static final long serialVersionUID = 7044612423796587895L;
    private Long id;
    private String version;
    private String versionPrefix;
    private String versionSuffix;
    private Boolean forced;
    private String releaseContent;

    @NotNull(message = "租户编码不能为空")
    private Long tenantCode;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createDate;
    private String fileDigest;
    private Integer appType;
    private Boolean isNewVersion;
    private String jarUrl;
    private Long jarSize;
    private String sqlUrl;
    private Integer status;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getVersionPrefix() {
        return this.versionPrefix;
    }

    public void setVersionPrefix(String versionPrefix) {
        this.versionPrefix = versionPrefix;
    }

    public String getVersionSuffix() {
        return this.versionSuffix;
    }

    public void setVersionSuffix(String versionSuffix) {
        this.versionSuffix = versionSuffix;
    }

    public Boolean getForced() {
        return this.forced;
    }

    public void setForced(Boolean forced) {
        this.forced = forced;
    }

    public String getReleaseContent() {
        return this.releaseContent;
    }

    public void setReleaseContent(String releaseContent) {
        this.releaseContent = releaseContent;
    }

    public Long getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(Long tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Boolean getNewVersion() {
        return this.isNewVersion;
    }

    public void setNewVersion(Boolean newVersion) {
        this.isNewVersion = newVersion;
    }

    public String getJarUrl() {
        return this.jarUrl;
    }

    public void setJarUrl(String jarUrl) {
        this.jarUrl = jarUrl;
    }

    public String getSqlUrl() {
        return this.sqlUrl;
    }

    public void setSqlUrl(String sqlUrl) {
        this.sqlUrl = sqlUrl;
    }

    public Long getJarSize() {
        return this.jarSize;
    }

    public void setJarSize(Long jarSize) {
        this.jarSize = jarSize;
    }

    public String getFileDigest() {
        return this.fileDigest;
    }

    public void setFileDigest(String fileDigest) {
        this.fileDigest = fileDigest;
    }

    public Integer getAppType() {
        return this.appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String toString() {
        return new ToStringBuilder(this).append("id", this.id).append("version", this.version).append("versionPrefix", this.versionPrefix).append("versionSuffix", this.versionSuffix).append("forced", this.forced).append("releaseContent", this.releaseContent).append(ConstantEnum.TENANTCODE_STR, this.tenantCode).append("createDate", this.createDate).append("fileDigest", this.fileDigest).append("appType", this.appType).append("isNewVersion", this.isNewVersion).append("jarUrl", this.jarUrl).append("jarSize", this.jarSize).append("sqlUrl", this.sqlUrl).append("status", this.status).toString();
    }
}
