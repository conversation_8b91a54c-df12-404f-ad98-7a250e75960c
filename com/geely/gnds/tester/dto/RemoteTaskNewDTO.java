package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.io.Serializable;
import java.util.Date;

/* loaded from: RemoteTaskNewDTO.class */
public class RemoteTaskNewDTO implements Serializable {
    private static final long serialVersionUID = -5211005702684698660L;
    private Long id;
    private String daId;
    private String daName;
    private Integer daVersion;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date taskEndtime;
    private String taskType;
    private String vin;
    private String state;
    private String reqParam;
    private Integer respCode;
    private String respDesc;
    private String respJson;
    private String createName;
    private Long creator;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createDate;
    private Long updater;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateDate;
    private Integer delFlag;
    private String serviceStationCode;
    private String serviceStationName;
    private String taskTypeTranslate;
    private String status;
    private String model;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDaId() {
        return this.daId;
    }

    public void setDaId(String daId) {
        this.daId = daId;
    }

    public String getDaName() {
        return this.daName;
    }

    public void setDaName(String daName) {
        this.daName = daName;
    }

    public Integer getDaVersion() {
        return this.daVersion;
    }

    public void setDaVersion(Integer daVersion) {
        this.daVersion = daVersion;
    }

    public Date getTaskEndtime() {
        return this.taskEndtime;
    }

    public void setTaskEndtime(Date taskEndtime) {
        this.taskEndtime = taskEndtime;
    }

    public String getTaskType() {
        return this.taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getReqParam() {
        return this.reqParam;
    }

    public void setReqParam(String reqParam) {
        this.reqParam = reqParam;
    }

    public Integer getRespCode() {
        return this.respCode;
    }

    public void setRespCode(Integer respCode) {
        this.respCode = respCode;
    }

    public String getRespDesc() {
        return this.respDesc;
    }

    public void setRespDesc(String respDesc) {
        this.respDesc = respDesc;
    }

    public String getRespJson() {
        return this.respJson;
    }

    public void setRespJson(String respJson) {
        this.respJson = respJson;
    }

    public String getCreateName() {
        return this.createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getServiceStationCode() {
        return this.serviceStationCode;
    }

    public void setServiceStationCode(String serviceStationCode) {
        this.serviceStationCode = serviceStationCode;
    }

    public String getServiceStationName() {
        return this.serviceStationName;
    }

    public void setServiceStationName(String serviceStationName) {
        this.serviceStationName = serviceStationName;
    }

    public String getTaskTypeTranslate() {
        return this.taskTypeTranslate;
    }

    public void setTaskTypeTranslate(String taskTypeTranslate) {
        this.taskTypeTranslate = taskTypeTranslate;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
