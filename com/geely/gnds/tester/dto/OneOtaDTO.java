package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.io.Serializable;
import java.util.Date;

/* loaded from: OneOtaDTO.class */
public class OneOtaDTO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String vin;
    private String status;
    private String progress;
    private String username;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createDate;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateDate;
    private String remark;
    private Integer flag;
    private Integer retryCnt;
    private String state;
    private Boolean isSupportApp;
    private Integer platformCode;

    public Integer getPlatformCode() {
        return this.platformCode;
    }

    public void setPlatformCode(Integer platformCode) {
        this.platformCode = platformCode;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getProgress() {
        return this.progress;
    }

    public void setProgress(String progress) {
        this.progress = progress;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getFlag() {
        return this.flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public Integer getRetryCnt() {
        return this.retryCnt;
    }

    public void setRetryCnt(Integer retryCnt) {
        this.retryCnt = retryCnt;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Boolean getIsSupportApp() {
        return this.isSupportApp;
    }

    public void setIsSupportApp(Boolean supportApp) {
        this.isSupportApp = supportApp;
    }
}
