package com.geely.gnds.tester.dto;

import java.util.Date;

/* loaded from: DiaImgDTO.class */
public class DiaImgDTO {
    private static final long serialVersionUID = 3444496297300591285L;
    private Long id;
    private String gcid;
    private String gcidName;
    private String location;
    private String imageType;
    private String nevisImage;
    private String status;
    private String wdid;
    private String structureWeekFrom;
    private String structureWeekTo;
    private String variant;
    private String ivpVariant;
    private String connector;
    private Long tenantCode;
    private Integer delFlag;
    private Long creator;
    private Date createDate;
    private Long updater;
    private Date updateDate;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGcid() {
        return this.gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public String getGcidName() {
        return this.gcidName;
    }

    public void setGcidName(String gcidName) {
        this.gcidName = gcidName;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getImageType() {
        return this.imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getNevisImage() {
        return this.nevisImage;
    }

    public void setNevisImage(String nevisImage) {
        this.nevisImage = nevisImage;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWdid() {
        return this.wdid;
    }

    public void setWdid(String wdid) {
        this.wdid = wdid;
    }

    public String getStructureWeekFrom() {
        return this.structureWeekFrom;
    }

    public void setStructureWeekFrom(String structureWeekFrom) {
        this.structureWeekFrom = structureWeekFrom;
    }

    public String getStructureWeekTo() {
        return this.structureWeekTo;
    }

    public void setStructureWeekTo(String structureWeekTo) {
        this.structureWeekTo = structureWeekTo;
    }

    public String getVariant() {
        return this.variant;
    }

    public void setVariant(String variant) {
        this.variant = variant;
    }

    public String getIvpVariant() {
        return this.ivpVariant;
    }

    public void setIvpVariant(String ivpVariant) {
        this.ivpVariant = ivpVariant;
    }

    public String getConnector() {
        return this.connector;
    }

    public void setConnector(String connector) {
        this.connector = connector;
    }

    public Long getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(Long tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Integer getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
