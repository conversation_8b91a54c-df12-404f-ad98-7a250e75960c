package com.geely.gnds.tester.dto;

import java.util.Objects;

/* loaded from: LogCollectionProgressDto.class */
public class LogCollectionProgressDto {
    private int progress;
    private String id;
    private String status;
    private String msg;
    private String imageUrl;
    private String logUrl;
    private int statusInt = 0;

    public int getProgress() {
        return this.progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getImageUrl() {
        return this.imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getLogUrl() {
        return this.logUrl;
    }

    public void setLogUrl(String logUrl) {
        this.logUrl = logUrl;
    }

    public LogCollectionProgressDto() {
    }

    public LogCollectionProgressDto(int progress, String id, String status, String msg, String imageUrl, String logUrl) {
        this.progress = progress;
        this.id = id;
        this.status = status;
        this.msg = msg;
        this.imageUrl = imageUrl;
        this.logUrl = logUrl;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogCollectionProgressDto that = (LogCollectionProgressDto) o;
        return this.progress == that.progress && Objects.equals(this.id, that.id) && Objects.equals(this.status, that.status) && Objects.equals(this.msg, that.msg) && Objects.equals(this.imageUrl, that.imageUrl) && Objects.equals(this.logUrl, that.logUrl);
    }

    public int hashCode() {
        return Objects.hash(Integer.valueOf(this.progress), this.id, this.status, this.msg, this.imageUrl, this.logUrl);
    }

    public String toString() {
        return "LogCollectionProgressDto{progress=" + this.progress + ", id='" + this.id + "', status='" + this.status + "', msg='" + this.msg + "', imgPath='" + this.imageUrl + "', logPath='" + this.logUrl + "'}";
    }

    public int getStatusInt() {
        return this.statusInt;
    }

    public void setStatusInt(int statusInt) {
        this.statusInt = statusInt;
    }
}
