package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: TesterReadoutCacheDTO.class */
public class TesterReadoutCacheDTO {
    private Long id;
    private String vin;
    private String globalTime;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)
    private Date createGlobalTime;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)
    private Date createTime;
    private String cacheTime;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getGlobalTime() {
        return this.globalTime;
    }

    public void setGlobalTime(String globalTime) {
        this.globalTime = globalTime;
    }

    public Date getCreateGlobalTime() {
        return this.createGlobalTime;
    }

    public void setCreateGlobalTime(Date createGlobalTime) {
        this.createGlobalTime = createGlobalTime;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCacheTime() {
        return this.cacheTime;
    }

    public void setCacheTime(String cacheTime) {
        this.cacheTime = cacheTime;
    }

    public String toString() {
        return new ToStringBuilder(this).append("id", this.id).append("vin", this.vin).append("globalTime", this.globalTime).append("createGlobalTime", this.createGlobalTime).append("createTime", this.createTime).append("cacheTime", this.cacheTime).toString();
    }
}
