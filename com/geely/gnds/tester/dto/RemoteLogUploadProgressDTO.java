package com.geely.gnds.tester.dto;

import java.util.Date;

/* loaded from: RemoteLogUploadProgressDTO.class */
public class RemoteLogUploadProgressDTO {
    private String vin;
    private String ecuName;
    private String uploadProgress;
    private Boolean supportProgressUpload;
    private Date uploadTime;
    private Boolean uploadflag;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getUploadProgress() {
        return this.uploadProgress;
    }

    public void setUploadProgress(String uploadProgress) {
        this.uploadProgress = uploadProgress;
    }

    public Boolean getSupportProgressUpload() {
        return this.supportProgressUpload;
    }

    public void setSupportProgressUpload(Boolean supportProgressUpload) {
        this.supportProgressUpload = supportProgressUpload;
    }

    public Date getUploadTime() {
        return this.uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Boolean getUploadflag() {
        return this.uploadflag;
    }

    public void setUploadflag(Boolean uploadflag) {
        this.uploadflag = uploadflag;
    }
}
