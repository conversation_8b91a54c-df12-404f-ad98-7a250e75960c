package com.geely.gnds.tester.dto;

import java.util.Objects;

/* loaded from: LogCollectionDto.class */
public class LogCollectionDto {
    String vin;
    String id;
    String imgEncode;
    String logPath;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getImgEncode() {
        return this.imgEncode;
    }

    public void setImgEncode(String imgEncode) {
        this.imgEncode = imgEncode;
    }

    public String getLogPath() {
        return this.logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogCollectionDto that = (LogCollectionDto) o;
        return Objects.equals(this.vin, that.vin) && Objects.equals(this.id, that.id) && Objects.equals(this.imgEncode, that.imgEncode) && Objects.equals(this.logPath, that.logPath);
    }

    public int hashCode() {
        return Objects.hash(this.vin, this.id, this.imgEncode, this.logPath);
    }

    public String toString() {
        return "LogCollectionDto{vin='" + this.vin + "', id='" + this.id + "', imgEncode='" + this.imgEncode + "', logPath='" + this.logPath + "'}";
    }
}
