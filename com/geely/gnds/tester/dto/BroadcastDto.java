package com.geely.gnds.tester.dto;

import java.util.Date;

/* loaded from: BroadcastDto.class */
public class BroadcastDto {
    String vin;
    String vehicle;
    String manufacturingYear;
    String engine;
    String gearbox;
    String steering;
    String vehicleBodyForm;
    String specialVehicle;
    String structureWeek;
    String pno18;
    Date updateTime;
    String brand;
    String market;
    String fyon;
    String drivingDistance;
    String currentDisplayVersion;
    String currentBssId;
    String yearEdition;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVehicle() {
        return this.vehicle;
    }

    public void setVehicle(String vehicle) {
        this.vehicle = vehicle;
    }

    public String getManufacturingYear() {
        return this.manufacturingYear;
    }

    public void setManufacturingYear(String manufacturingYear) {
        this.manufacturingYear = manufacturingYear;
    }

    public String getEngine() {
        return this.engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getGearbox() {
        return this.gearbox;
    }

    public void setGearbox(String gearbox) {
        this.gearbox = gearbox;
    }

    public String getSteering() {
        return this.steering;
    }

    public void setSteering(String steering) {
        this.steering = steering;
    }

    public String getVehicleBodyForm() {
        return this.vehicleBodyForm;
    }

    public void setVehicleBodyForm(String vehicleBodyForm) {
        this.vehicleBodyForm = vehicleBodyForm;
    }

    public String getSpecialVehicle() {
        return this.specialVehicle;
    }

    public void setSpecialVehicle(String specialVehicle) {
        this.specialVehicle = specialVehicle;
    }

    public String getStructureWeek() {
        return this.structureWeek;
    }

    public void setStructureWeek(String structureWeek) {
        this.structureWeek = structureWeek;
    }

    public String getPno18() {
        return this.pno18;
    }

    public void setPno18(String pno18) {
        this.pno18 = pno18;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBrand() {
        return this.brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getMarket() {
        return this.market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getFyon() {
        return this.fyon;
    }

    public void setFyon(String fyon) {
        this.fyon = fyon;
    }

    public String getDrivingDistance() {
        return this.drivingDistance;
    }

    public void setDrivingDistance(String drivingDistance) {
        this.drivingDistance = drivingDistance;
    }

    public String getCurrentDisplayVersion() {
        return this.currentDisplayVersion;
    }

    public void setCurrentDisplayVersion(String currentDisplayVersion) {
        this.currentDisplayVersion = currentDisplayVersion;
    }

    public String getCurrentBssId() {
        return this.currentBssId;
    }

    public void setCurrentBssId(String currentBssId) {
        this.currentBssId = currentBssId;
    }

    public String getYearEdition() {
        return this.yearEdition;
    }

    public void setYearEdition(String yearEdition) {
        this.yearEdition = yearEdition;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("BroadcastDto{");
        sb.append("vin='").append(this.vin).append('\'');
        sb.append(", vehicle='").append(this.vehicle).append('\'');
        sb.append(", manufacturingYear='").append(this.manufacturingYear).append('\'');
        sb.append(", engine='").append(this.engine).append('\'');
        sb.append(", gearbox='").append(this.gearbox).append('\'');
        sb.append(", steering='").append(this.steering).append('\'');
        sb.append(", vehicleBodyForm='").append(this.vehicleBodyForm).append('\'');
        sb.append(", specialVehicle='").append(this.specialVehicle).append('\'');
        sb.append(", structureWeek='").append(this.structureWeek).append('\'');
        sb.append(", pno18='").append(this.pno18).append('\'');
        sb.append(", updateTime=").append(this.updateTime);
        sb.append(", brand='").append(this.brand).append('\'');
        sb.append(", market='").append(this.market).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
