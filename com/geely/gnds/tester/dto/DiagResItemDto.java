package com.geely.gnds.tester.dto;

import java.util.Objects;

/* loaded from: DiagResItemDto.class */
public class DiagResItemDto {
    private String dmeName;
    private String originalName;
    private String name;
    private String inDataType;
    private String outDataType;
    private String offset;
    private String size;
    private String resultPrecision;
    private String formula;
    private String unit;
    private String originalUnit;
    private String compareValue;
    private Integer sort;
    private Boolean selected = false;

    public String getDmeName() {
        return this.dmeName;
    }

    public void setDmeName(String dmeName) {
        this.dmeName = dmeName;
    }

    public String getOriginalUnit() {
        return this.originalUnit;
    }

    public void setOriginalUnit(String originalUnit) {
        this.originalUnit = originalUnit;
    }

    public String getOriginalName() {
        return this.originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getInDataType() {
        return this.inDataType;
    }

    public void setInDataType(String inDataType) {
        this.inDataType = inDataType;
    }

    public String getOutDataType() {
        return this.outDataType;
    }

    public void setOutDataType(String outDataType) {
        this.outDataType = outDataType;
    }

    public String getOffset() {
        return this.offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getSize() {
        return this.size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getResultPrecision() {
        return this.resultPrecision;
    }

    public void setResultPrecision(String resultPrecision) {
        this.resultPrecision = resultPrecision;
    }

    public String getFormula() {
        return this.formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCompareValue() {
        return this.compareValue;
    }

    public void setCompareValue(String compareValue) {
        this.compareValue = compareValue;
    }

    public Boolean getSelected() {
        return this.selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public Integer getSort() {
        return this.sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DiagResItemDto that = (DiagResItemDto) o;
        return Objects.equals(this.dmeName, that.dmeName) && Objects.equals(this.originalName, that.originalName) && Objects.equals(this.name, that.name) && Objects.equals(this.inDataType, that.inDataType) && Objects.equals(this.outDataType, that.outDataType) && Objects.equals(this.offset, that.offset) && Objects.equals(this.size, that.size) && Objects.equals(this.resultPrecision, that.resultPrecision) && Objects.equals(this.formula, that.formula) && Objects.equals(this.unit, that.unit) && Objects.equals(this.compareValue, that.compareValue) && Objects.equals(this.sort, that.sort) && Objects.equals(this.selected, that.selected);
    }

    public int hashCode() {
        return Objects.hash(this.dmeName, this.originalName, this.name, this.inDataType, this.outDataType, this.offset, this.size, this.resultPrecision, this.formula, this.unit, this.compareValue, this.sort, this.selected);
    }

    public String toString() {
        return "DiagResItemDto{dmeName='" + this.dmeName + "', originalName='" + this.originalName + "', name='" + this.name + "', inDataType='" + this.inDataType + "', outDataType='" + this.outDataType + "', offset='" + this.offset + "', size='" + this.size + "', resultPrecision='" + this.resultPrecision + "', formula='" + this.formula + "', unit='" + this.unit + "', compareValue='" + this.compareValue + "', sort=" + this.sort + ", selected=" + this.selected + '}';
    }
}
