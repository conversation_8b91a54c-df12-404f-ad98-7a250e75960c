package com.geely.gnds.tester.dto;

import cn.hutool.core.bean.BeanUtil;

/* loaded from: AppSsoExtDTO.class */
public class AppSsoExtDTO extends AppSsoConfigDTO {
    private boolean showSsoLink = true;
    private boolean redirectSsoLink = false;

    public AppSsoExtDTO(AppSsoExtDTO dto) {
        BeanUtil.copyProperties(dto, this, new String[0]);
    }

    public AppSsoExtDTO(AppSsoConfigDTO dto, boolean showSsoLink, boolean redirectSsoLink) {
        BeanUtil.copyProperties(dto, this, new String[0]);
        setShowSsoLink(showSsoLink);
        setRedirectSsoLink(redirectSsoLink);
    }

    public boolean isShowSsoLink() {
        return this.showSsoLink;
    }

    public void setShowSsoLink(boolean showSsoLink) {
        this.showSsoLink = showSsoLink;
    }

    public boolean isRedirectSsoLink() {
        return this.redirectSsoLink;
    }

    public void setRedirectSsoLink(boolean redirectSsoLink) {
        this.redirectSsoLink = redirectSsoLink;
    }
}
