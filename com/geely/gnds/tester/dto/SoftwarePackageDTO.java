package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.io.Serializable;
import java.util.Date;

/* loaded from: SoftwarePackageDTO.class */
public class SoftwarePackageDTO implements Serializable {
    private String projectName;
    private String bssId;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date bssIdTime;
    private String ecuName;
    private String description;
    private String translateDescription;

    public String getProjectName() {
        return this.projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBssId() {
        return this.bssId;
    }

    public void setBssId(String bssId) {
        this.bssId = bssId;
    }

    public Date getBssIdTime() {
        return this.bssIdTime;
    }

    public void setBssIdTime(Date bssIdTime) {
        this.bssIdTime = bssIdTime;
    }

    public String getTranslateDescription() {
        return this.translateDescription;
    }

    public void setTranslateDescription(String translateDescription) {
        this.translateDescription = translateDescription;
    }
}
