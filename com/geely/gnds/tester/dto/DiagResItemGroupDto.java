package com.geely.gnds.tester.dto;

import com.geely.gnds.dsa.dto.DiaSessionDTO;
import java.util.List;
import java.util.StringJoiner;

/* loaded from: DiagResItemGroupDto.class */
public class DiagResItemGroupDto {
    private Long id;
    private String address;
    private String diagnosticPartNumber;
    private String name;
    private String ecuName;
    private String serviceId;
    private String dataIdentifierId;
    private String size;
    private String securityAccessRefs;
    private String sessionId;
    private String maxNumberOfDidsForReadDid;
    private Integer responseItemNum;
    private List<DiaSessionDTO> sessionList;
    private List<DiagResItemDto> responseItemDtoList;
    private String elType;
    private Integer position;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public String getServiceId() {
        return this.serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getDataIdentifierId() {
        return this.dataIdentifierId;
    }

    public void setDataIdentifierId(String dataIdentifierId) {
        this.dataIdentifierId = dataIdentifierId;
    }

    public String getSize() {
        return this.size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getSecurityAccessRefs() {
        return this.securityAccessRefs;
    }

    public void setSecurityAccessRefs(String securityAccessRefs) {
        this.securityAccessRefs = securityAccessRefs;
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<DiagResItemDto> getResponseItemDtoList() {
        return this.responseItemDtoList;
    }

    public void setResponseItemDtoList(List<DiagResItemDto> responseItemDtoList) {
        this.responseItemDtoList = responseItemDtoList;
    }

    public String getElType() {
        return this.elType;
    }

    public void setElType(String elType) {
        this.elType = elType;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMaxNumberOfDidsForReadDid() {
        return this.maxNumberOfDidsForReadDid;
    }

    public void setMaxNumberOfDidsForReadDid(String maxNumberOfDidsForReadDid) {
        this.maxNumberOfDidsForReadDid = maxNumberOfDidsForReadDid;
    }

    public Integer getResponseItemNum() {
        return this.responseItemNum;
    }

    public void setResponseItemNum(Integer responseItemNum) {
        this.responseItemNum = responseItemNum;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public List<DiaSessionDTO> getSessionList() {
        return this.sessionList;
    }

    public void setSessionList(List<DiaSessionDTO> sessionList) {
        this.sessionList = sessionList;
    }

    public Integer getPosition() {
        return this.position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public String toString() {
        return new StringJoiner(", ", DiagResItemGroupDto.class.getSimpleName() + "[", "]").add("id=" + this.id).add("address='" + this.address + "'").add("diagnosticPartNumber='" + this.diagnosticPartNumber + "'").add("name='" + this.name + "'").add("ecuName='" + this.ecuName + "'").add("serviceId='" + this.serviceId + "'").add("dataIdentifierId='" + this.dataIdentifierId + "'").add("size='" + this.size + "'").add("securityAccessRefs='" + this.securityAccessRefs + "'").add("sessionId='" + this.sessionId + "'").add("maxNumberOfDidsForReadDid='" + this.maxNumberOfDidsForReadDid + "'").add("responseItemNum=" + this.responseItemNum).add("sessionList=" + this.sessionList).add("responseItemDtoList=" + this.responseItemDtoList).add("elType='" + this.elType + "'").toString();
    }
}
