package com.geely.gnds.tester.dto;

import java.util.Date;

/* loaded from: DroRecordDTO.class */
public class DroRecordDTO {
    private static final long serialVersionUID = 1;
    private Long id;
    private String data;
    private String vin;
    private String module;
    private String fileName;
    private String failureReason;
    private Integer delFlag;
    private Integer status;
    private Date readoutTime;
    private Long creator;
    private Date createDate;
    private Long updater;
    private Date updateDate;
    private String readoutAccount;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getData() {
        return this.data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getModule() {
        return this.module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFailureReason() {
        return this.failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    public Integer getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getReadoutTime() {
        return this.readoutTime;
    }

    public void setReadoutTime(Date readoutTime) {
        this.readoutTime = readoutTime;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getReadoutAccount() {
        return this.readoutAccount;
    }

    public void setReadoutAccount(String readoutAccount) {
        this.readoutAccount = readoutAccount;
    }
}
