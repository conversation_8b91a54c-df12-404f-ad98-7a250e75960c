package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.Serializable;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: LogUploadServerDto.class */
public class LogUploadServerDto implements Serializable {
    private String id;
    private String vin;
    private String tenantId;
    private String testerId;
    private String username;
    private String filename;
    private Integer year;
    private Integer month;
    private Integer day;
    private String logPath;
    private Integer state;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date logCreateTime;
    private String logType;
    private Double fileSize;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date fileCreateTime;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date fileUpdateTime;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getTenantId() {
        return this.tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTesterId() {
        return this.testerId;
    }

    public void setTesterId(String testerId) {
        this.testerId = testerId;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFilename() {
        return this.filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Integer getYear() {
        return this.year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return this.month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getDay() {
        return this.day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public String getLogType() {
        return this.logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public Integer getState() {
        return this.state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Double getFileSize() {
        return this.fileSize;
    }

    public void setFileSize(Double fileSize) {
        this.fileSize = fileSize;
    }

    public String getLogPath() {
        return this.logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public Date getFileCreateTime() {
        return this.fileCreateTime;
    }

    public void setFileCreateTime(Date fileCreateTime) {
        this.fileCreateTime = fileCreateTime;
    }

    public Date getFileUpdateTime() {
        return this.fileUpdateTime;
    }

    public void setFileUpdateTime(Date fileUpdateTime) {
        this.fileUpdateTime = fileUpdateTime;
    }

    public Date getLogCreateTime() {
        return this.logCreateTime;
    }

    public void setLogCreateTime(Date logCreateTime) {
        this.logCreateTime = logCreateTime;
    }

    public String toString() {
        return new ToStringBuilder(this).append("id", this.id).append("vin", this.vin).append("tenantId", this.tenantId).append("testerId", this.testerId).append(ConstantEnum.USERNAME_STR, this.username).append("filename", this.filename).append("year", this.year).append("month", this.month).append("day", this.day).append("logPath", this.logPath).append("state", this.state).append("logCreateTime", this.logCreateTime).append("logType", this.logType).append("fileSize", this.fileSize).append("fileCreateTime", this.fileCreateTime).append("fileUpdateTime", this.fileUpdateTime).toString();
    }
}
