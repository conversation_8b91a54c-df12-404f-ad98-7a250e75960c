package com.geely.gnds.tester.dto;

import java.util.List;

/* loaded from: ResponseDidDTO.class */
public class ResponseDidDTO {
    private Long id;
    private boolean valid;
    private String invalidReason;
    private Long ecuId;
    private String ecuAddress;
    private Integer addressType;
    private String ecuName;
    private boolean positive;
    private String code;
    private String codeDescription;
    private String service;
    private String serviceDescription;
    private String subFunction;
    private List<ResponseValueDTO> valueList;
    private String rawValue;
    private String input;
    private String inputDescription;
    private DiaServiceDTO diaService;
    private String diagnosticPartNum;
    private String diagnosticPartNumBak;
    private String type1904;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public boolean isValid() {
        return this.valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getInvalidReason() {
        return this.invalidReason;
    }

    public void setInvalidReason(String invalidReason) {
        this.invalidReason = invalidReason;
    }

    public Long getEcuId() {
        return this.ecuId;
    }

    public void setEcuId(Long ecuId) {
        this.ecuId = ecuId;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public Integer getAddressType() {
        return this.addressType;
    }

    public void setAddressType(Integer addressType) {
        this.addressType = addressType;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public boolean isPositive() {
        return this.positive;
    }

    public void setPositive(boolean positive) {
        this.positive = positive;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCodeDescription() {
        return this.codeDescription;
    }

    public void setCodeDescription(String codeDescription) {
        this.codeDescription = codeDescription;
    }

    public String getService() {
        return this.service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getServiceDescription() {
        return this.serviceDescription;
    }

    public void setServiceDescription(String serviceDescription) {
        this.serviceDescription = serviceDescription;
    }

    public String getSubFunction() {
        return this.subFunction;
    }

    public void setSubFunction(String subFunction) {
        this.subFunction = subFunction;
    }

    public List<ResponseValueDTO> getValueList() {
        return this.valueList;
    }

    public void setValueList(List<ResponseValueDTO> valueList) {
        this.valueList = valueList;
    }

    public String getRawValue() {
        return this.rawValue;
    }

    public void setRawValue(String rawValue) {
        this.rawValue = rawValue;
    }

    public String getInput() {
        return this.input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    public String getInputDescription() {
        return this.inputDescription;
    }

    public void setInputDescription(String inputDescription) {
        this.inputDescription = inputDescription;
    }

    public DiaServiceDTO getDiaService() {
        return this.diaService;
    }

    public void setDiaService(DiaServiceDTO diaService) {
        this.diaService = diaService;
    }

    public String getDiagnosticPartNum() {
        return this.diagnosticPartNum;
    }

    public void setDiagnosticPartNum(String diagnosticPartNum) {
        this.diagnosticPartNum = diagnosticPartNum;
    }

    public String getDiagnosticPartNumBak() {
        return this.diagnosticPartNumBak;
    }

    public void setDiagnosticPartNumBak(String diagnosticPartNumBak) {
        this.diagnosticPartNumBak = diagnosticPartNumBak;
    }

    public String getType1904() {
        return this.type1904;
    }

    public void setType1904(String type1904) {
        this.type1904 = type1904;
    }
}
