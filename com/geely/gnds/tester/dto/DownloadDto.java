package com.geely.gnds.tester.dto;

import java.io.Serializable;
import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: DownloadDto.class */
public class DownloadDto implements Serializable {
    private List<EcuDto> ecuDtoList;
    private String id;
    private String vin;
    private Boolean encrypted;
    private ReleaseNoteDTO releaseNoteDTO;

    public List<EcuDto> getEcuDtoList() {
        return this.ecuDtoList;
    }

    public void setEcuDtoList(List<EcuDto> ecuDtoList) {
        this.ecuDtoList = ecuDtoList;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public DownloadDto(List<EcuDto> ecuDtoList, String id) {
        this.ecuDtoList = ecuDtoList;
        this.id = id;
    }

    public DownloadDto() {
    }

    public ReleaseNoteDTO getReleaseNoteDTO() {
        return this.releaseNoteDTO;
    }

    public void setReleaseNoteDTO(ReleaseNoteDTO releaseNoteDTO) {
        this.releaseNoteDTO = releaseNoteDTO;
    }

    public Boolean getEncrypted() {
        return this.encrypted;
    }

    public void setEncrypted(Boolean encrypted) {
        this.encrypted = encrypted;
    }

    public DownloadDto(List<EcuDto> ecuDtoList, String id, String vin, Boolean encrypted, ReleaseNoteDTO releaseNoteDTO) {
        this.ecuDtoList = ecuDtoList;
        this.id = id;
        this.vin = vin;
        this.encrypted = encrypted;
        this.releaseNoteDTO = releaseNoteDTO;
    }

    public String toString() {
        return new ToStringBuilder(this).append("ecuDtoList", this.ecuDtoList).append("id", this.id).append("vin", this.vin).append("encrypted", this.encrypted).append("releaseNoteDTO", this.releaseNoteDTO).toString();
    }
}
