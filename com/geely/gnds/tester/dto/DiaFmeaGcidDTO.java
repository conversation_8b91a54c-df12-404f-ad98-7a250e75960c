package com.geely.gnds.tester.dto;

/* loaded from: DiaFmeaGcidDTO.class */
public class DiaFmeaGcidDTO {
    private Long id;
    private Long fmeaTableId;
    private String gcidName;
    private String location;
    private String wdid;
    private String type;
    private String signalId;
    private String score;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFmeaTableId() {
        return this.fmeaTableId;
    }

    public void setFmeaTableId(Long fmeaTableId) {
        this.fmeaTableId = fmeaTableId;
    }

    public String getGcidName() {
        return this.gcidName;
    }

    public void setGcidName(String gcidName) {
        this.gcidName = gcidName;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getWdid() {
        return this.wdid;
    }

    public void setWdid(String wdid) {
        this.wdid = wdid;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSignalId() {
        return this.signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public String getScore() {
        return this.score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("DiaFmeaGcidDTO{");
        sb.append("id=").append(this.id);
        sb.append(", fmeaTableId=").append(this.fmeaTableId);
        sb.append(", gcidName='").append(this.gcidName).append('\'');
        sb.append(", location='").append(this.location).append('\'');
        sb.append(", wdid='").append(this.wdid).append('\'');
        sb.append(", type='").append(this.type).append('\'');
        sb.append(", signalId='").append(this.signalId).append('\'');
        sb.append(", score='").append(this.score).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
