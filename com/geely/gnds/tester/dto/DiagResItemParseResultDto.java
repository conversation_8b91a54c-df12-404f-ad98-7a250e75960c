package com.geely.gnds.tester.dto;

import com.geely.gnds.tester.enums.ConstantEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/* loaded from: DiagResItemParseResultDto.class */
public class DiagResItemParseResultDto {
    private String dataIdentifierId;
    private String name;
    private String value;
    private String originalName;
    private String originalUnit;
    private String unit;
    private String dmeName;
    private Boolean readFlag;
    private String outDataType;
    private String errorMsg;
    private Boolean hasNegativeRes;
    private String diagnosticPartNumber;
    private String ecuName;
    private Integer offset;
    private String unParseValue;
    private Boolean hasCompareValue;
    private Integer position;
    private Integer sort;
    private String originalResponse;

    public String getOutDataType() {
        return this.outDataType;
    }

    public void setOutDataType(String outDataType) {
        this.outDataType = outDataType;
    }

    public String getOriginalUnit() {
        return this.originalUnit;
    }

    public void setOriginalUnit(String originalUnit) {
        this.originalUnit = originalUnit;
    }

    public String getOriginalName() {
        return this.originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getDataIdentifierId() {
        return this.dataIdentifierId;
    }

    public void setDataIdentifierId(String dataIdentifierId) {
        this.dataIdentifierId = dataIdentifierId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getDmeName() {
        return this.dmeName;
    }

    public void setDmeName(String dmeName) {
        this.dmeName = dmeName;
    }

    public Boolean getReadFlag() {
        return this.readFlag;
    }

    public void setReadFlag(Boolean readFlag) {
        this.readFlag = readFlag;
    }

    public String getErrorMsg() {
        return this.errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Boolean getHasNegativeRes() {
        return this.hasNegativeRes;
    }

    public void setHasNegativeRes(Boolean hasNegativeRes) {
        this.hasNegativeRes = hasNegativeRes;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getUnParseValue() {
        return this.unParseValue;
    }

    public void setUnParseValue(String unParseValue) {
        this.unParseValue = unParseValue;
    }

    public Boolean getHasCompareValue() {
        return this.hasCompareValue;
    }

    public void setHasCompareValue(Boolean hasCompareValue) {
        this.hasCompareValue = hasCompareValue;
    }

    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append(this.name).append(": ").append(ObjectUtils.isEmpty(this.value) ? "" : this.value).append(ConstantEnum.EMPTY).append(ObjectUtils.isEmpty(this.unit) ? "" : this.unit);
        if (StringUtils.isNotBlank(this.errorMsg)) {
            s.append("errorMsg='" + this.errorMsg + "'");
        }
        s.append(System.getProperty("line.separator"));
        return s.toString();
    }

    public Integer getPosition() {
        return this.position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public Integer getSort() {
        return this.sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getOriginalResponse() {
        return this.originalResponse;
    }

    public void setOriginalResponse(String originalResponse) {
        this.originalResponse = originalResponse;
    }
}
