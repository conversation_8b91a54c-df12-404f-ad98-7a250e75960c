package com.geely.gnds.tester.dto;

import java.io.Serializable;
import java.util.StringJoiner;
import javax.validation.constraints.NotNull;

/* loaded from: QuickLinkDTO.class */
public class QuickLinkDTO implements Serializable {
    private static final long serialVersionUID = -3931276729818829661L;
    private Long id;
    private String title;
    private String url;

    @NotNull(message = "品牌参数不能为空")
    private Long brandId;

    @NotNull(message = "租户编码不能为空")
    private Long tenantCode;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getBrandId() {
        return this.brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public Long getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(Long tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String toString() {
        return new StringJoiner(", ", QuickLinkDTO.class.getSimpleName() + "[", "]").add("id=" + this.id).add("title='" + this.title + "'").add("url='" + this.url + "'").add("brandId=" + this.brandId).add("tenantCode=" + this.tenantCode).toString();
    }
}
