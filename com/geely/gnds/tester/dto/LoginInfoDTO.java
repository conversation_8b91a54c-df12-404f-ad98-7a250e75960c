package com.geely.gnds.tester.dto;

import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: LoginInfoDTO.class */
public class LoginInfoDTO {
    private String msg;
    private String userName;
    private String thirdPartyCode;

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getThirdPartyCode() {
        return this.thirdPartyCode;
    }

    public void setThirdPartyCode(String thirdPartyCode) {
        this.thirdPartyCode = thirdPartyCode;
    }

    public String toString() {
        return new ToStringBuilder(this).append(AjaxResult.gB, this.msg).append("userName", this.userName).append("thirdPartyToken", this.thirdPartyCode).toString();
    }
}
