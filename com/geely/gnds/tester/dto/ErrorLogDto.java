package com.geely.gnds.tester.dto;

import java.io.Serializable;
import java.util.Map;

/* loaded from: ErrorLogDto.class */
public class ErrorLogDto implements Serializable {
    private String code;
    private String uri;
    private String msg;
    private Map<String, Object> params;

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUri() {
        return this.uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Map<String, Object> getParams() {
        return this.params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
