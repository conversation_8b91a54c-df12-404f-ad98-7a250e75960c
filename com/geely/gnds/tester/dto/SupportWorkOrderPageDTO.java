package com.geely.gnds.tester.dto;

import java.io.Serializable;
import java.util.Date;

/* loaded from: SupportWorkOrderPageDTO.class */
public class SupportWorkOrderPageDTO implements Serializable {
    private static final long serialVersionUID = 189463211262869928L;
    private Long id;
    private String vin;
    private String model;
    private String problemDesc;
    private String consultContent;
    private Date submitDate;
    private Integer orderStatus;
    private Integer clientStatus;
    private String solution;
    private Integer notice;
    private String attachedFilePath;

    public String getAttachedFilePath() {
        return this.attachedFilePath;
    }

    public void setAttachedFilePath(String attachedFilePath) {
        this.attachedFilePath = attachedFilePath;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getProblemDesc() {
        return this.problemDesc;
    }

    public void setProblemDesc(String problemDesc) {
        this.problemDesc = problemDesc;
    }

    public String getConsultContent() {
        return this.consultContent;
    }

    public void setConsultContent(String consultContent) {
        this.consultContent = consultContent;
    }

    public Date getSubmitDate() {
        return this.submitDate;
    }

    public void setSubmitDate(Date submitDate) {
        this.submitDate = submitDate;
    }

    public Integer getOrderStatus() {
        return this.orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getSolution() {
        return this.solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public Integer getNotice() {
        return this.notice;
    }

    public void setNotice(Integer notice) {
        this.notice = notice;
    }

    public Integer getClientStatus() {
        return this.clientStatus;
    }

    public void setClientStatus(Integer clientStatus) {
        this.clientStatus = clientStatus;
    }
}
