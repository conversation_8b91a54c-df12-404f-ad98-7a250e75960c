package com.geely.gnds.tester.dto;

import java.util.List;

/* loaded from: RemoteConnectDto.class */
public class RemoteConnectDto {
    String vin;
    String vehicle;
    String first;
    String second;
    String model;
    List<ReloadDto> seqList;
    Integer taskRemoteType;

    public RemoteConnectDto() {
    }

    public RemoteConnectDto(String vin, String vehicle, String first, String second, String model, List<ReloadDto> seqList) {
        this.vin = vin;
        this.vehicle = vehicle;
        this.first = first;
        this.second = second;
        this.model = model;
        this.seqList = seqList;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVehicle() {
        return this.vehicle;
    }

    public void setVehicle(String vehicle) {
        this.vehicle = vehicle;
    }

    public String getFirst() {
        return this.first;
    }

    public void setFirst(String first) {
        this.first = first;
    }

    public String getSecond() {
        return this.second;
    }

    public void setSecond(String second) {
        this.second = second;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<ReloadDto> getSeqList() {
        return this.seqList;
    }

    public void setSeqList(List<ReloadDto> seqList) {
        this.seqList = seqList;
    }

    public Integer getTaskRemoteType() {
        return this.taskRemoteType;
    }

    public void setTaskRemoteType(Integer taskRemoteType) {
        this.taskRemoteType = taskRemoteType;
    }
}
