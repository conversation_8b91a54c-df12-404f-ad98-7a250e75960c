package com.geely.gnds.tester.dto;

import java.util.Objects;

/* loaded from: CepUserDTO.class */
public class CepUserDTO {
    private String displayName;
    private Boolean bind;
    private String username;
    private String password;

    public String getDisplayName() {
        return this.displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Boolean getBind() {
        return this.bind;
    }

    public void setBind(Boolean bind) {
        this.bind = bind;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CepUserDTO that = (CepUserDTO) o;
        return Objects.equals(this.displayName, that.displayName) && Objects.equals(this.bind, that.bind) && Objects.equals(this.username, that.username) && Objects.equals(this.password, that.password);
    }

    public int hashCode() {
        return Objects.hash(this.displayName, this.bind, this.username, this.password);
    }

    public String toString() {
        return "CepUserDTO{displayName='" + this.displayName + "', bind=" + this.bind + ", username='" + this.username + "', password='" + this.password + "'}";
    }
}
