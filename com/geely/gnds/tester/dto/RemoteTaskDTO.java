package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.io.Serializable;
import java.util.Date;

/* loaded from: RemoteTaskDTO.class */
public class RemoteTaskDTO implements Serializable {
    private static final long serialVersionUID = 5279464735456044073L;
    private String id;
    private Long rvdcTaskId;
    private String taskName;
    private Integer taskTimeout;
    private Date taskEndtime;
    private String taskType;
    private String vin;
    private String state;
    private String reqParam;
    private String seqDexReports;
    private String mdpRaw;
    private String mdpPath;
    private Integer respCode;
    private String respDesc;
    private String respJson;
    private String createName;
    private Long creator;
    private String reason;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createDate;
    private Long updater;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateDate;
    private Integer delFlag;
    private String taskTypeTranslate;
    private String status;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getRvdcTaskId() {
        return this.rvdcTaskId;
    }

    public void setRvdcTaskId(Long rvdcTaskId) {
        this.rvdcTaskId = rvdcTaskId;
    }

    public String getTaskName() {
        return this.taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Integer getTaskTimeout() {
        return this.taskTimeout;
    }

    public void setTaskTimeout(Integer taskTimeout) {
        this.taskTimeout = taskTimeout;
    }

    public Date getTaskEndtime() {
        return this.taskEndtime;
    }

    public void setTaskEndtime(Date taskEndtime) {
        this.taskEndtime = taskEndtime;
    }

    public String getTaskType() {
        return this.taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getReqParam() {
        return this.reqParam;
    }

    public void setReqParam(String reqParam) {
        this.reqParam = reqParam;
    }

    public Integer getRespCode() {
        return this.respCode;
    }

    public void setRespCode(Integer respCode) {
        this.respCode = respCode;
    }

    public String getRespDesc() {
        return this.respDesc;
    }

    public void setRespDesc(String respDesc) {
        this.respDesc = respDesc;
    }

    public String getRespJson() {
        return this.respJson;
    }

    public void setRespJson(String respJson) {
        this.respJson = respJson;
    }

    public String getCreateName() {
        return this.createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getTaskTypeTranslate() {
        return this.taskTypeTranslate;
    }

    public void setTaskTypeTranslate(String taskTypeTranslate) {
        this.taskTypeTranslate = taskTypeTranslate;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReason() {
        return this.reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getSeqDexReports() {
        return this.seqDexReports;
    }

    public void setSeqDexReports(String seqDexReports) {
        this.seqDexReports = seqDexReports;
    }

    public String getMdpRaw() {
        return this.mdpRaw;
    }

    public void setMdpRaw(String mdpRaw) {
        this.mdpRaw = mdpRaw;
    }

    public String getMdpPath() {
        return this.mdpPath;
    }

    public void setMdpPath(String mdpPath) {
        this.mdpPath = mdpPath;
    }
}
