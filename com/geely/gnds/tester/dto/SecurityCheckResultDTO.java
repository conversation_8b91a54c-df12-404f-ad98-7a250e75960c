package com.geely.gnds.tester.dto;

/* loaded from: SecurityCheckResultDTO.class */
public class SecurityCheckResultDTO {
    private String hkey;
    private String key;
    private String subKey;
    private Boolean result;

    public String getHkey() {
        return this.hkey;
    }

    public void setHkey(String hkey) {
        this.hkey = hkey;
    }

    public String getKey() {
        return this.key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getSubKey() {
        return this.subKey;
    }

    public void setSubKey(String subKey) {
        this.subKey = subKey;
    }

    public Boolean getResult() {
        return this.result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer("SecurityCheckResultDTO{");
        sb.append("hkey='").append(this.hkey).append('\'');
        sb.append(", key='").append(this.key).append('\'');
        sb.append(", subKey='").append(this.subKey).append('\'');
        sb.append(", result=").append(this.result);
        sb.append('}');
        return sb.toString();
    }
}
