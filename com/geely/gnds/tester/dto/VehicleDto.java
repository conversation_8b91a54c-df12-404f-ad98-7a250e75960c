package com.geely.gnds.tester.dto;

import java.util.Map;
import java.util.Objects;

/* loaded from: VehicleDto.class */
public class VehicleDto {
    private String ip;
    private int port;
    private int sourceAddress;
    private int targetAddress;
    private int gatewayAddress;
    private String vin;
    private String gid;
    private String eid;
    private Boolean connect;
    private String broadcast;
    private Map broadcastMap;
    private int status;
    private String oldVin;
    private Boolean readFromXml;
    private boolean vbfswdl;
    private String heartBeatId;
    private Integer isFromCloud;
    private boolean needTls;
    private Integer connectType;
    private boolean hasPermission;
    private boolean hasConnected;
    private String permissionFlag;

    public String getPermissionFlag() {
        return this.permissionFlag;
    }

    public void setPermissionFlag(String permissionFlag) {
        this.permissionFlag = permissionFlag;
    }

    public VehicleDto() {
        this.connect = false;
        this.status = 0;
        this.vbfswdl = false;
        this.isFromCloud = 0;
        this.needTls = false;
    }

    public VehicleDto(String ip, int port, int sourceAddress, int targetAddress, int gatewayAddress, String vin, String gid, String eid, Boolean connect, String broadcast, Map broadcastMap) {
        this.ip = ip;
        this.port = port;
        this.sourceAddress = sourceAddress;
        this.targetAddress = targetAddress;
        this.gatewayAddress = gatewayAddress;
        this.vin = vin;
        this.gid = gid;
        this.eid = eid;
        this.connect = connect;
        this.broadcast = broadcast;
        this.broadcastMap = broadcastMap;
        this.status = 0;
        this.vbfswdl = false;
        this.isFromCloud = 0;
        this.needTls = false;
    }

    public Boolean getReadFromXml() {
        return this.readFromXml;
    }

    public void setReadFromXml(Boolean readFromXml) {
        this.readFromXml = readFromXml;
    }

    public String getIp() {
        return this.ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public int getPort() {
        return this.port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public int getSourceAddress() {
        return this.sourceAddress;
    }

    public void setSourceAddress(int sourceAddress) {
        this.sourceAddress = sourceAddress;
    }

    public int getTargetAddress() {
        return this.targetAddress;
    }

    public void setTargetAddress(int targetAddress) {
        this.targetAddress = targetAddress;
    }

    public int getGatewayAddress() {
        return this.gatewayAddress;
    }

    public void setGatewayAddress(int gatewayAddress) {
        this.gatewayAddress = gatewayAddress;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getGid() {
        return this.gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getEid() {
        return this.eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public Boolean getConnect() {
        return this.connect;
    }

    public void setConnect(Boolean connect) {
        this.connect = connect;
    }

    public String getBroadcast() {
        return this.broadcast;
    }

    public void setBroadcast(String broadcast) {
        this.broadcast = broadcast;
    }

    public Map getBroadcastMap() {
        return this.broadcastMap;
    }

    public void setBroadcastMap(Map broadcastMap) {
        this.broadcastMap = broadcastMap;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        VehicleDto that = (VehicleDto) o;
        return this.port == that.port && this.sourceAddress == that.sourceAddress && this.targetAddress == that.targetAddress && this.gatewayAddress == that.gatewayAddress && this.status == that.status && this.vbfswdl == that.vbfswdl && Objects.equals(this.ip, that.ip) && Objects.equals(this.vin, that.vin) && Objects.equals(this.gid, that.gid) && Objects.equals(this.eid, that.eid) && Objects.equals(this.connect, that.connect) && Objects.equals(this.broadcast, that.broadcast) && Objects.equals(this.broadcastMap, that.broadcastMap) && Objects.equals(this.oldVin, that.oldVin) && Objects.equals(this.readFromXml, that.readFromXml) && Objects.equals(this.heartBeatId, that.heartBeatId);
    }

    public int hashCode() {
        return Objects.hash(this.ip, Integer.valueOf(this.port), Integer.valueOf(this.sourceAddress), Integer.valueOf(this.targetAddress), Integer.valueOf(this.gatewayAddress), this.vin, this.gid, this.eid, this.connect, this.broadcast, this.broadcastMap, Integer.valueOf(this.status), this.oldVin, this.readFromXml, Boolean.valueOf(this.vbfswdl), this.heartBeatId);
    }

    public int getStatus() {
        return this.status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getHeartBeatId() {
        return this.heartBeatId;
    }

    public void setHeartBeatId(String heartBeatId) {
        this.heartBeatId = heartBeatId;
    }

    public boolean isVbfswdl() {
        return this.vbfswdl;
    }

    public void setVbfswdl(boolean vbfswdl) {
        this.vbfswdl = vbfswdl;
    }

    public String getOldVin() {
        return this.oldVin;
    }

    public void setOldVin(String oldVin) {
        this.oldVin = oldVin;
    }

    public Integer getIsFromCloud() {
        return this.isFromCloud;
    }

    public void setIsFromCloud(Integer isFromCloud) {
        this.isFromCloud = isFromCloud;
    }

    public boolean isNeedTls() {
        return this.needTls;
    }

    public void setNeedTls(boolean needTls) {
        this.needTls = needTls;
    }

    public String toString() {
        return "VehicleDto{ip='" + this.ip + "', port=" + this.port + ", sourceAddress=" + this.sourceAddress + ", targetAddress=" + this.targetAddress + ", gatewayAddress=" + this.gatewayAddress + ", vin='" + this.vin + "', gid='" + this.gid + "', eid='" + this.eid + "', connect=" + this.connect + ", broadcast='" + this.broadcast + "', broadcastMap=" + this.broadcastMap + ", status=" + this.status + ", oldVin='" + this.oldVin + "', readFromXml=" + this.readFromXml + ", vbfswdl=" + this.vbfswdl + ", heartBeatId='" + this.heartBeatId + "', isFromCloud=" + this.isFromCloud + ", needTls=" + this.needTls + ", connectType=" + this.connectType + '}';
    }

    public Integer getConnectType() {
        return this.connectType;
    }

    public void setConnectType(Integer connectType) {
        this.connectType = connectType;
    }

    public boolean isHasPermission() {
        return this.hasPermission;
    }

    public void setHasPermission(boolean hasPermission) {
        this.hasPermission = hasPermission;
    }

    public boolean isHasConnected() {
        return this.hasConnected;
    }

    public void setHasConnected(boolean hasConnected) {
        this.hasConnected = hasConnected;
    }
}
