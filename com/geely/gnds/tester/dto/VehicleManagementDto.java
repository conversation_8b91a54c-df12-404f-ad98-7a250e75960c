package com.geely.gnds.tester.dto;

import java.util.Map;
import java.util.Objects;

/* loaded from: VehicleManagementDto.class */
public class VehicleManagementDto {
    private String vin;
    private Map broadcastMap;
    private int connectStatus;
    private boolean status;
    private String username;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Map getBroadcastMap() {
        return this.broadcastMap;
    }

    public void setBroadcastMap(Map broadcastMap) {
        this.broadcastMap = broadcastMap;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public int getConnectStatus() {
        return this.connectStatus;
    }

    public void setConnectStatus(int connectStatus) {
        this.connectStatus = connectStatus;
    }

    public boolean isStatus() {
        return this.status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        VehicleManagementDto that = (VehicleManagementDto) o;
        return this.connectStatus == that.connectStatus && this.status == that.status && Objects.equals(this.vin, that.vin) && Objects.equals(this.broadcastMap, that.broadcastMap) && Objects.equals(this.username, that.username);
    }

    public int hashCode() {
        return Objects.hash(this.vin, this.broadcastMap, Integer.valueOf(this.connectStatus), Boolean.valueOf(this.status), this.username);
    }

    public String toString() {
        return "VehicleManagementDto{vin='" + this.vin + "', broadcastMap=" + this.broadcastMap + ", connectStatus=" + this.connectStatus + ", status=" + this.status + ", username='" + this.username + "'}";
    }
}
