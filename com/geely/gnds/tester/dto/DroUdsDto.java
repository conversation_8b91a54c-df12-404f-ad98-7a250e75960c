package com.geely.gnds.tester.dto;

import java.util.Objects;

/* loaded from: DroUdsDto.class */
public class DroUdsDto {
    private String diagnosisRequest;
    private String ecuAddress;
    private String unparsedResponse;
    private String udsType;

    public String getDiagnosisRequest() {
        return this.diagnosisRequest;
    }

    public void setDiagnosisRequest(String diagnosisRequest) {
        this.diagnosisRequest = diagnosisRequest;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getUnparsedResponse() {
        return this.unparsedResponse;
    }

    public void setUnparsedResponse(String unparsedResponse) {
        this.unparsedResponse = unparsedResponse;
    }

    public String getUdsType() {
        return this.udsType;
    }

    public void setUdsType(String udsType) {
        this.udsType = udsType;
    }

    public DroUdsDto() {
    }

    public DroUdsDto(String diagnosisRequest, String ecuAddress, String unparsedResponse, String udsType) {
        this.diagnosisRequest = diagnosisRequest;
        this.ecuAddress = ecuAddress;
        this.unparsedResponse = unparsedResponse;
        this.udsType = udsType;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DroUdsDto droUdsDto = (DroUdsDto) o;
        return Objects.equals(this.diagnosisRequest, droUdsDto.diagnosisRequest) && Objects.equals(this.ecuAddress, droUdsDto.ecuAddress) && Objects.equals(this.unparsedResponse, droUdsDto.unparsedResponse) && Objects.equals(this.udsType, droUdsDto.udsType);
    }

    public int hashCode() {
        return Objects.hash(this.diagnosisRequest, this.ecuAddress, this.unparsedResponse, this.udsType);
    }

    public String toString() {
        return "DroUdsDto{diagnosisRequest='" + this.diagnosisRequest + "', ecuAddress='" + this.ecuAddress + "', unparsedResponse='" + this.unparsedResponse + "', udsType='" + this.udsType + "'}";
    }
}
