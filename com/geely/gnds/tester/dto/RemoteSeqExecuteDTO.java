package com.geely.gnds.tester.dto;

import java.util.Date;

/* loaded from: RemoteSeqExecuteDTO.class */
public class RemoteSeqExecuteDTO {
    private Long id;
    private Long taskId;
    private String seqCode;
    private String name;
    private String seqVersion;
    private Integer dexStatus;
    private String dexSerror;
    private Long creator;
    private Date createDate;
    private Long updater;
    private Date updateDate;
    private String mdpRawSeq;
    private String mdpPathSeq;
    private String respJsonSeq;
    private int masterFlag;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return this.taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getSeqCode() {
        return this.seqCode;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSeqVersion() {
        return this.seqVersion;
    }

    public void setSeqVersion(String seqVersion) {
        this.seqVersion = seqVersion;
    }

    public Integer getDexStatus() {
        return this.dexStatus;
    }

    public void setDexStatus(Integer dexStatus) {
        this.dexStatus = dexStatus;
    }

    public String getDexSerror() {
        return this.dexSerror;
    }

    public void setDexSerror(String dexSerror) {
        this.dexSerror = dexSerror;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getMdpRawSeq() {
        return this.mdpRawSeq;
    }

    public void setMdpRawSeq(String mdpRawSeq) {
        this.mdpRawSeq = mdpRawSeq;
    }

    public String getMdpPathSeq() {
        return this.mdpPathSeq;
    }

    public void setMdpPathSeq(String mdpPathSeq) {
        this.mdpPathSeq = mdpPathSeq;
    }

    public String getRespJsonSeq() {
        return this.respJsonSeq;
    }

    public void setRespJsonSeq(String respJsonSeq) {
        this.respJsonSeq = respJsonSeq;
    }

    public int getMasterFlag() {
        return this.masterFlag;
    }

    public void setMasterFlag(int masterFlag) {
        this.masterFlag = masterFlag;
    }
}
