package com.geely.gnds.tester.dto.xml;

import java.io.Serializable;
import java.util.Date;

/* loaded from: SysLogXmlImmediateDTO.class */
public class SysLogXmlImmediateDTO implements Serializable {
    private static final long serialVersionUID = 288804723951241111L;
    private Long id;
    private String lC;
    private String vin;
    private Date cT;
    private String fileName;
    private String cQ;
    private String mac;
    private String model;
    private String testId;
    private String testName;
    private Date lD;
    private String lE;
    private String lF;
    private Long tenantCode;
    private Long creator;
    private Date createDate;
    private Long updater;
    private Date updateDate;
    private String lG;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSeqRequestId() {
        return this.lC;
    }

    public void setSeqRequestId(String seqRequestId) {
        this.lC = seqRequestId;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Date getFileTime() {
        return this.cT;
    }

    public void setFileTime(Date fileTime) {
        this.cT = fileTime;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getAccount() {
        return this.cQ;
    }

    public void setAccount(String account) {
        this.cQ = account;
    }

    public String getMac() {
        return this.mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getTestId() {
        return this.testId;
    }

    public void setTestId(String testId) {
        this.testId = testId;
    }

    public String getTestName() {
        return this.testName;
    }

    public void setTestName(String testName) {
        this.testName = testName;
    }

    public Date getTestTime() {
        return this.lD;
    }

    public void setTestTime(Date testTime) {
        this.lD = testTime;
    }

    public String getTestStatus() {
        return this.lE;
    }

    public void setTestStatus(String testStatus) {
        this.lE = testStatus;
    }

    public String getTestVer() {
        return this.lF;
    }

    public void setTestVer(String testVer) {
        this.lF = testVer;
    }

    public Long getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(Long tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getClientVersion() {
        return this.lG;
    }

    public void setClientVersion(String clientVersion) {
        this.lG = clientVersion;
    }
}
