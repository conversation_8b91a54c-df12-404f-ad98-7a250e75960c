package com.geely.gnds.tester.dto.xml;

import java.io.Serializable;
import java.util.Date;

/* loaded from: SysLogXmlFaultCodesImmediateDTO.class */
public class SysLogXmlFaultCodesImmediateDTO implements Serializable {
    private static final long serialVersionUID = -4335530047699052229L;
    private Long id;
    private Long lv;
    private String lw;
    private String lx;
    private String ly;
    private Date dB;
    private String ecuAddress;
    private String ecuName;
    private String lz;
    private String lA;
    private String lB;
    private String uri;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSysLogXmlId() {
        return this.lv;
    }

    public void setSysLogXmlId(Long sysLogXmlId) {
        this.lv = sysLogXmlId;
    }

    public String getFaultStatus() {
        return this.lw;
    }

    public void setFaultStatus(String faultStatus) {
        this.lw = faultStatus;
    }

    public String getFaultCode() {
        return this.lx;
    }

    public void setFaultCode(String faultCode) {
        this.lx = faultCode;
    }

    public String getFaultDesc() {
        return this.ly;
    }

    public void setFaultDesc(String faultDesc) {
        this.ly = faultDesc;
    }

    public Date getFaultTime() {
        return this.dB;
    }

    public void setFaultTime(Date faultTime) {
        this.dB = faultTime;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getSendUds() {
        return this.lz;
    }

    public void setSendUds(String sendUds) {
        this.lz = sendUds;
    }

    public String getReceiveUds() {
        return this.lA;
    }

    public void setReceiveUds(String receiveUds) {
        this.lA = receiveUds;
    }

    public String getLogComponentId() {
        return this.lB;
    }

    public void setLogComponentId(String logComponentId) {
        this.lB = logComponentId;
    }

    public String getUri() {
        return this.uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }
}
