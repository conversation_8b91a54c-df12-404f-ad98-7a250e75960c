package com.geely.gnds.tester.dto.xml;

import java.io.Serializable;
import java.util.List;

/* loaded from: SysRequestDTO.class */
public class SysRequestDTO implements Serializable {
    private static final long serialVersionUID = -4335530047699053699L;
    private String requestId;
    private String lI;
    private String tenantCode;
    private SysLogXmlImmediateDTO lJ;
    private List<SysLogXmlFaultCodesImmediateDTO> lK;
    private List<SysLogXmlLoggersImmediateDTO> lL;

    public String getRequestId() {
        return this.requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getXmlResult() {
        return this.lI;
    }

    public void setXmlResult(String xmlResult) {
        this.lI = xmlResult;
    }

    public SysLogXmlImmediateDTO getSysLogXmlImmediateDTO() {
        return this.lJ;
    }

    public void setSysLogXmlImmediateDTO(SysLogXmlImmediateDTO sysLogXmlImmediateDTO) {
        this.lJ = sysLogXmlImmediateDTO;
    }

    public List<SysLogXmlFaultCodesImmediateDTO> getSysLogXmlFaultCodesImmediateDTO() {
        return this.lK;
    }

    public void setSysLogXmlFaultCodesImmediateDTO(List<SysLogXmlFaultCodesImmediateDTO> sysLogXmlFaultCodesImmediateDTO) {
        this.lK = sysLogXmlFaultCodesImmediateDTO;
    }

    public List<SysLogXmlLoggersImmediateDTO> getSysLogXmlLoggersImmediateDTO() {
        return this.lL;
    }

    public void setSysLogXmlLoggersImmediateDTO(List<SysLogXmlLoggersImmediateDTO> sysLogXmlLoggersImmediateDTO) {
        this.lL = sysLogXmlLoggersImmediateDTO;
    }

    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
}
