package com.geely.gnds.tester.dto.xml;

import java.io.Serializable;

/* loaded from: SysLogXmlLoggersImmediateDTO.class */
public class SysLogXmlLoggersImmediateDTO implements Serializable {
    private static final long serialVersionUID = -11966630303334021L;
    private Long id;
    private Long lv;
    private String ecuAddress;
    private String dw;
    private String lH;
    private String lB;
    private String uri;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSysLogXmlId() {
        return this.lv;
    }

    public void setSysLogXmlId(Long sysLogXmlId) {
        this.lv = sysLogXmlId;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getLogName() {
        return this.dw;
    }

    public void setLogName(String logName) {
        this.dw = logName;
    }

    public String getLogVal() {
        return this.lH;
    }

    public void setLogVal(String logVal) {
        this.lH = logVal;
    }

    public String getLogComponentId() {
        return this.lB;
    }

    public void setLogComponentId(String logComponentId) {
        this.lB = logComponentId;
    }

    public String getUri() {
        return this.uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }
}
