package com.geely.gnds.tester.dto;

import java.util.List;

/* loaded from: BroadcastVehicleDataDTO.class */
public class BroadcastVehicleDataDTO extends BroadcastDto {
    private List<EcuBroadcastDto> ecus;
    private List<String> vdns;

    public List<EcuBroadcastDto> getEcus() {
        return this.ecus;
    }

    public void setEcus(List<EcuBroadcastDto> ecus) {
        this.ecus = ecus;
    }

    public List<String> getVdns() {
        return this.vdns;
    }

    public void setVdns(List<String> vdns) {
        this.vdns = vdns;
    }

    @Override // com.geely.gnds.tester.dto.BroadcastDto
    public String toString() {
        StringBuilder sb = new StringBuilder("BroadcastVehicleDataDTO{");
        sb.append("vin='").append(this.vin).append('\'');
        sb.append(", vehicle='").append(this.vehicle).append('\'');
        sb.append(", manufacturingYear='").append(this.manufacturingYear).append('\'');
        sb.append(", engine='").append(this.engine).append('\'');
        sb.append(", gearbox='").append(this.gearbox).append('\'');
        sb.append(", steering='").append(this.steering).append('\'');
        sb.append(", vehicleBodyForm='").append(this.vehicleBodyForm).append('\'');
        sb.append(", specialVehicle='").append(this.specialVehicle).append('\'');
        sb.append(", structureWeek='").append(this.structureWeek).append('\'');
        sb.append(", pno18='").append(this.pno18).append('\'');
        sb.append(", updateTime=").append(this.updateTime);
        sb.append(", brand='").append(this.brand).append('\'');
        sb.append(", market='").append(this.market).append('\'');
        sb.append(", ecus=").append(this.ecus);
        sb.append(", vdns=").append(this.vdns);
        sb.append('}');
        return sb.toString();
    }
}
