package com.geely.gnds.tester.dto;

import com.geely.gnds.tester.enums.TesterRunningState;

/* loaded from: TesterServiceInfoDto.class */
public class TesterServiceInfoDto {
    private String tenantName;
    private String tenantShowName;
    private String environment;
    private String version;
    private String pid;
    private TesterRunningState runningState;
    private String initStatus;
    private String cloudId;
    private String vehicleDataRegion;
    private String language;
    private String selfUrl;

    public String getSelfUrl() {
        return this.selfUrl;
    }

    public void setSelfUrl(String selfUrl) {
        this.selfUrl = selfUrl;
    }

    public String getCloudId() {
        return this.cloudId;
    }

    public void setCloudId(String cloudId) {
        this.cloudId = cloudId;
    }

    public String getLanguage() {
        return this.language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTenantName() {
        return this.tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPid() {
        return this.pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getEnvironment() {
        return this.environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public TesterRunningState getRunningState() {
        return this.runningState;
    }

    public void setRunningState(TesterRunningState runningState) {
        this.runningState = runningState;
    }

    public String getInitStatus() {
        return this.initStatus;
    }

    public void setInitStatus(String initStatus) {
        this.initStatus = initStatus;
    }

    public String getTenantShowName() {
        return this.tenantShowName;
    }

    public void setTenantShowName(String tenantShowName) {
        this.tenantShowName = tenantShowName;
    }

    public String getVehicleDataRegion() {
        return this.vehicleDataRegion;
    }

    public void setVehicleDataRegion(String vehicleDataRegion) {
        this.vehicleDataRegion = vehicleDataRegion;
    }

    public String toString() {
        return "TesterServiceInfoDto{tenantName='" + this.tenantName + "', tenantShowName='" + this.tenantShowName + "', environment='" + this.environment + "', version='" + this.version + "', pid='" + this.pid + "', runningState=" + this.runningState + ", initStatus='" + this.initStatus + "'}";
    }
}
