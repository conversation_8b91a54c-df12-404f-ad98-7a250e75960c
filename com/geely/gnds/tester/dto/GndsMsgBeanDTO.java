package com.geely.gnds.tester.dto;

import com.geely.gnds.tester.socket.GndsMsgCodeEnum;
import java.io.Serializable;

/* loaded from: GndsMsgBeanDTO.class */
public class GndsMsgBeanDTO implements Serializable {
    private String data;
    private String dataType;
    private Boolean status;

    public String getData() {
        return this.data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public GndsMsgCodeEnum getDataType() {
        return GndsMsgCodeEnum.enumType(this.dataType);
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Boolean getStatus() {
        return this.status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public String toString() {
        return "GndsMsgBean{d='" + this.data + "', e='" + this.dataType + "', s=" + this.status + '}';
    }
}
