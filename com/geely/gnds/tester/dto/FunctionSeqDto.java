package com.geely.gnds.tester.dto;

import java.io.Serializable;

/* loaded from: FunctionSeqDto.class */
public class FunctionSeqDto implements Serializable {
    private String name;
    private String seqCode;
    private String description;
    private int priorityLevel;
    private String version;
    private String applic;

    public FunctionSeqDto(String name, String seqCode, String description, int priorityLevel, String version, String applic) {
        this.name = name;
        this.seqCode = seqCode;
        this.description = description;
        this.priorityLevel = priorityLevel;
        this.version = version;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSeqCode() {
        return this.seqCode;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getPriorityLevel() {
        return this.priorityLevel;
    }

    public void setPriorityLevel(int priorityLevel) {
        this.priorityLevel = priorityLevel;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getApplic() {
        return this.applic;
    }

    public void setApplic(String applic) {
        this.applic = applic;
    }

    public FunctionSeqDto() {
    }
}
