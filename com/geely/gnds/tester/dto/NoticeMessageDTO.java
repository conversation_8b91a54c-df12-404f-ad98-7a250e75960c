package com.geely.gnds.tester.dto;

/* loaded from: NoticeMessageDTO.class */
public class NoticeMessageDTO {
    private Long id;
    private String title;
    private String content;
    private Integer maxRemindTimes;
    private String remindDeadline;
    private String messageDeadline;
    private Integer status;
    private String fileName;
    private Long creator;
    private String createDate;
    private Long updater;
    private String updateDate;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getMaxRemindTimes() {
        return this.maxRemindTimes;
    }

    public void setMaxRemindTimes(Integer maxRemindTimes) {
        this.maxRemindTimes = maxRemindTimes;
    }

    public String getRemindDeadline() {
        return this.remindDeadline;
    }

    public void setRemindDeadline(String remindDeadline) {
        this.remindDeadline = remindDeadline;
    }

    public String getMessageDeadline() {
        return this.messageDeadline;
    }

    public void setMessageDeadline(String messageDeadline) {
        this.messageDeadline = messageDeadline;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public String getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public String getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
