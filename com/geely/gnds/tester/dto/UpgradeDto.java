package com.geely.gnds.tester.dto;

import java.util.List;

/* loaded from: UpgradeDto.class */
public class UpgradeDto {
    private String displayVersion;
    private String vehicleStatus;
    private String bssId;
    private Boolean encrypted;
    private VcpPincodeDTO vcp;
    private boolean emptyAssignment;
    private List<EcuDto> ecuInstallationInstructions;

    public boolean isEmptyAssignment() {
        return this.emptyAssignment;
    }

    public void setEmptyAssignment(boolean emptyAssignment) {
        this.emptyAssignment = emptyAssignment;
    }

    public String getVehicleStatus() {
        return this.vehicleStatus;
    }

    public void setVehicleStatus(String vehicleStatus) {
        this.vehicleStatus = vehicleStatus;
    }

    public String getDisplayVersion() {
        return this.displayVersion;
    }

    public void setDisplayVersion(String displayVersion) {
        this.displayVersion = displayVersion;
    }

    public List<EcuDto> getEcuInstallationInstructions() {
        return this.ecuInstallationInstructions;
    }

    public void setEcuInstallationInstructions(List<EcuDto> ecuInstallationInstructions) {
        this.ecuInstallationInstructions = ecuInstallationInstructions;
    }

    public UpgradeDto(String displayVersion, List<EcuDto> ecuInstallationInstructions) {
        this.displayVersion = displayVersion;
        this.ecuInstallationInstructions = ecuInstallationInstructions;
    }

    public UpgradeDto() {
    }

    public String getBssId() {
        return this.bssId;
    }

    public void setBssId(String bssId) {
        this.bssId = bssId;
    }

    public Boolean getEncrypted() {
        return this.encrypted;
    }

    public void setEncrypted(Boolean encrypted) {
        this.encrypted = encrypted;
    }

    public VcpPincodeDTO getVcp() {
        return this.vcp;
    }

    public void setVcp(VcpPincodeDTO vcp) {
        this.vcp = vcp;
    }
}
