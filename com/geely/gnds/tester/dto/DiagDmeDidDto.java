package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.io.Serializable;
import java.util.Date;

/* loaded from: DiagDmeDidDto.class */
public class DiagDmeDidDto implements Serializable {
    private static final long serialVersionUID = -5962621754816376427L;
    private Long id;
    private String identifier;
    private String didName;
    private String parameterName;
    private String parameter;
    private String description;
    private String parameterUsage;
    private String parameterOrigin;
    private String statusDescription;
    private String parameterValues;
    private String minAndMaxValues;
    private String activationPurpose;
    private String preconditions;
    private String expectedResults;
    private String alternative;
    private String scaling;
    private String unit;
    private Long creator;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createDate;
    private Long updater;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date updateDate;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIdentifier() {
        return this.identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getDidName() {
        return this.didName;
    }

    public void setDidName(String didName) {
        this.didName = didName;
    }

    public String getParameterName() {
        return this.parameterName;
    }

    public void setParameterName(String parameterName) {
        this.parameterName = parameterName;
    }

    public String getParameter() {
        return this.parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getParameterUsage() {
        return this.parameterUsage;
    }

    public void setParameterUsage(String parameterUsage) {
        this.parameterUsage = parameterUsage;
    }

    public String getParameterOrigin() {
        return this.parameterOrigin;
    }

    public void setParameterOrigin(String parameterOrigin) {
        this.parameterOrigin = parameterOrigin;
    }

    public String getStatusDescription() {
        return this.statusDescription;
    }

    public void setStatusDescription(String statusDescription) {
        this.statusDescription = statusDescription;
    }

    public String getParameterValues() {
        return this.parameterValues;
    }

    public void setParameterValues(String parameterValues) {
        this.parameterValues = parameterValues;
    }

    public String getMinAndMaxValues() {
        return this.minAndMaxValues;
    }

    public void setMinAndMaxValues(String minAndMaxValues) {
        this.minAndMaxValues = minAndMaxValues;
    }

    public String getActivationPurpose() {
        return this.activationPurpose;
    }

    public void setActivationPurpose(String activationPurpose) {
        this.activationPurpose = activationPurpose;
    }

    public String getPreconditions() {
        return this.preconditions;
    }

    public void setPreconditions(String preconditions) {
        this.preconditions = preconditions;
    }

    public String getExpectedResults() {
        return this.expectedResults;
    }

    public void setExpectedResults(String expectedResults) {
        this.expectedResults = expectedResults;
    }

    public String getAlternative() {
        return this.alternative;
    }

    public void setAlternative(String alternative) {
        this.alternative = alternative;
    }

    public String getScaling() {
        return this.scaling;
    }

    public void setScaling(String scaling) {
        this.scaling = scaling;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String toString() {
        return "DiagDmeDidDto{id=" + this.id + ", identifier='" + this.identifier + "', didName='" + this.didName + "', parameterName='" + this.parameterName + "', parameter='" + this.parameter + "', description='" + this.description + "', parameterUsage='" + this.parameterUsage + "', parameterOrigin='" + this.parameterOrigin + "', statusDescription='" + this.statusDescription + "', parameterValues='" + this.parameterValues + "', minAndMaxValues='" + this.minAndMaxValues + "', activationPurpose='" + this.activationPurpose + "', preconditions='" + this.preconditions + "', expectedResults='" + this.expectedResults + "', alternative='" + this.alternative + "', scaling='" + this.scaling + "', unit='" + this.unit + "', creator=" + this.creator + ", createDate=" + this.createDate + ", updater=" + this.updater + ", updateDate=" + this.updateDate + '}';
    }
}
