package com.geely.gnds.tester.dto;

import java.io.Serializable;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: RemoteLogCollectionDTO.class */
public class RemoteLogCollectionDTO implements Serializable {
    private static final long serialVersionUID = 539570256542962181L;
    private Long id;
    private String vin;
    private Long userId;
    private String taskId;
    private Integer updateIHULog;
    private Integer updateIHULogLevel;
    private Boolean isIHUOperate;
    private Date startTimeIHU;
    private Date endTimeIHU;
    private Integer status;
    private String ossFileJson;
    private String errorInfo;
    private Integer delFlag;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTaskId() {
        return this.taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getUpdateIHULog() {
        return this.updateIHULog;
    }

    public void setUpdateIHULog(Integer updateIHULog) {
        this.updateIHULog = updateIHULog;
    }

    public Integer getUpdateIHULogLevel() {
        return this.updateIHULogLevel;
    }

    public void setUpdateIHULogLevel(Integer updateIHULogLevel) {
        this.updateIHULogLevel = updateIHULogLevel;
    }

    public Boolean getIsIHUOperate() {
        return this.isIHUOperate;
    }

    public void setIsIHUOperate(Boolean IHUOperate) {
        this.isIHUOperate = IHUOperate;
    }

    public Date getStartTimeIHU() {
        return this.startTimeIHU;
    }

    public void setStartTimeIHU(Date startTimeIHU) {
        this.startTimeIHU = startTimeIHU;
    }

    public Date getEndTimeIHU() {
        return this.endTimeIHU;
    }

    public void setEndTimeIHU(Date endTimeIHU) {
        this.endTimeIHU = endTimeIHU;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOssFileJson() {
        return this.ossFileJson;
    }

    public void setOssFileJson(String ossFileJson) {
        this.ossFileJson = ossFileJson;
    }

    public String getErrorInfo() {
        return this.errorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        this.errorInfo = errorInfo;
    }

    public Integer getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String toString() {
        return new ToStringBuilder(this).append("id", this.id).append("vin", this.vin).append("userId", this.userId).append("taskId", this.taskId).append("updateIHULog", this.updateIHULog).append("updateIHULogLevel", this.updateIHULogLevel).append("isIHUOperate", this.isIHUOperate).append("startTimeIHU", this.startTimeIHU).append("endTimeIHU", this.endTimeIHU).append("status", this.status).append("ossFileJson", this.ossFileJson).append("errorInfo", this.errorInfo).append("delFlag", this.delFlag).toString();
    }
}
