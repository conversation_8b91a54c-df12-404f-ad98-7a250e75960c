package com.geely.gnds.tester.dto;

import java.io.Serializable;
import java.util.StringJoiner;

/* loaded from: EcuMessageDTO.class */
public class EcuMessageDTO implements Serializable {
    private static final long serialVersionUID = -7978461199454956585L;
    private Long id;
    private String domain;
    private String ecuShortName;
    private String ecuAddress;
    private String ecuName;
    private String diagnosticNetwork;
    private String chineseName;
    private String ecuProject;
    private String ecuNameInBom;
    private String defaultDiagnosticPartNumber;
    private String codeId;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDomain() {
        return this.domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getEcuShortName() {
        return this.ecuShortName;
    }

    public void setEcuShortName(String ecuShortName) {
        this.ecuShortName = ecuShortName;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getDiagnosticNetwork() {
        return this.diagnosticNetwork;
    }

    public void setDiagnosticNetwork(String diagnosticNetwork) {
        this.diagnosticNetwork = diagnosticNetwork;
    }

    public String getChineseName() {
        return this.chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getEcuProject() {
        return this.ecuProject;
    }

    public void setEcuProject(String ecuProject) {
        this.ecuProject = ecuProject;
    }

    public String getEcuNameInBom() {
        return this.ecuNameInBom;
    }

    public void setEcuNameInBom(String ecuNameInBom) {
        this.ecuNameInBom = ecuNameInBom;
    }

    public String getDefaultDiagnosticPartNumber() {
        return this.defaultDiagnosticPartNumber;
    }

    public void setDefaultDiagnosticPartNumber(String defaultDiagnosticPartNumber) {
        this.defaultDiagnosticPartNumber = defaultDiagnosticPartNumber;
    }

    public String getCodeId() {
        return this.codeId;
    }

    public void setCodeId(String codeId) {
        this.codeId = codeId;
    }

    public String toString() {
        return new StringJoiner(", ", EcuMessageDTO.class.getSimpleName() + "[", "]").add("id=" + this.id).add("domain='" + this.domain + "'").add("ecuShortName='" + this.ecuShortName + "'").add("ecuAddress='" + this.ecuAddress + "'").add("ecuName='" + this.ecuName + "'").add("diagnosticNetwork='" + this.diagnosticNetwork + "'").add("chineseName='" + this.chineseName + "'").add("ecuProject='" + this.ecuProject + "'").add("ecuNameInBom='" + this.ecuNameInBom + "'").add("defaultDiagnosticPartNumber='" + this.defaultDiagnosticPartNumber + "'").add("codeId='" + this.codeId + "'").toString();
    }
}
