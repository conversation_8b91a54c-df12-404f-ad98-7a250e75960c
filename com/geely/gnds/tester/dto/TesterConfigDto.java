package com.geely.gnds.tester.dto;

import java.util.Date;
import java.util.StringJoiner;

/* loaded from: TesterConfigDto.class */
public class TesterConfigDto {
    private int id;
    private String testerCode;
    private String creator;
    private Date createTime;
    private String updater;
    private Date updateTime;
    private String vbfPath;
    private int vbfDefaultSize;
    private Integer vbfCleanFlag;
    private String dsaSeqPath;

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTesterCode() {
        return this.testerCode;
    }

    public void setTesterCode(String testerCode) {
        this.testerCode = testerCode;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdater() {
        return this.updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getVbfPath() {
        return this.vbfPath;
    }

    public void setVbfPath(String vbfPath) {
        this.vbfPath = vbfPath;
    }

    public int getVbfDefaultSize() {
        return this.vbfDefaultSize;
    }

    public void setVbfDefaultSize(int vbfDefaultSize) {
        this.vbfDefaultSize = vbfDefaultSize;
    }

    public Integer getVbfCleanFlag() {
        return this.vbfCleanFlag;
    }

    public void setVbfCleanFlag(Integer vbfCleanFlag) {
        this.vbfCleanFlag = vbfCleanFlag;
    }

    public String getDsaSeqPath() {
        return this.dsaSeqPath;
    }

    public void setDsaSeqPath(String dsaSeqPath) {
        this.dsaSeqPath = dsaSeqPath;
    }

    public String toString() {
        return new StringJoiner(", ", TesterConfigDto.class.getSimpleName() + "[", "]").add("id=" + this.id).add("testerCode='" + this.testerCode + "'").add("creator='" + this.creator + "'").add("createTime=" + this.createTime).add("updater='" + this.updater + "'").add("updateTime=" + this.updateTime).add("vbfPath='" + this.vbfPath + "'").add("vbfDefaultSize=" + this.vbfDefaultSize).add("vbfCleanFlag=" + this.vbfCleanFlag).add("dsaSeqPath='" + this.dsaSeqPath + "'").toString();
    }
}
