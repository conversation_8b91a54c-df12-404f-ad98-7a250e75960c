package com.geely.gnds.tester.dto;

import java.io.Serializable;

/* loaded from: SoftwareDto.class */
public class SoftwareDto implements Serializable {
    private String softwareNumber;
    private String softwareVersion;
    private String softwareType;
    private Long vbfSize;
    private float downloadTime;
    private String url;
    private String checksum;
    private String vbfName;

    public String getSoftwareNumber() {
        return this.softwareNumber;
    }

    public void setSoftwareNumber(String softwareNumber) {
        this.softwareNumber = softwareNumber;
    }

    public String getSoftwareVersion() {
        return this.softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public String getSoftwareType() {
        return this.softwareType;
    }

    public void setSoftwareType(String softwareType) {
        this.softwareType = softwareType;
    }

    public Long getVbfSize() {
        return this.vbfSize;
    }

    public void setVbfSize(Long vbfSize) {
        this.vbfSize = vbfSize;
    }

    public float getDownloadTime() {
        return this.downloadTime;
    }

    public void setDownloadTime(float downloadTime) {
        this.downloadTime = downloadTime;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getChecksum() {
        return this.checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public String getVbfName() {
        return this.vbfName;
    }

    public void setVbfName(String vbfName) {
        this.vbfName = vbfName;
    }

    public SoftwareDto(String softwareNumber, String softwareVersion, String softwareType, Long vbfSize, float downloadTime, String url, String checksum, String vbfName) {
        this.softwareNumber = softwareNumber;
        this.softwareVersion = softwareVersion;
        this.softwareType = softwareType;
        this.vbfSize = vbfSize;
        this.downloadTime = downloadTime;
        this.url = url;
        this.checksum = checksum;
        this.vbfName = vbfName;
    }

    public SoftwareDto() {
    }

    public String toString() {
        return "SoftwareDto{softwareNumber='" + this.softwareNumber + "', softwareVersion='" + this.softwareVersion + "', softwareType='" + this.softwareType + "', vbfSize=" + this.vbfSize + ", downloadTime=" + this.downloadTime + ", url='" + this.url + "', checksum='" + this.checksum + "', vbfName='" + this.vbfName + "'}";
    }
}
