package com.geely.gnds.tester.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

/* loaded from: SupportWorkOrderDto.class */
public class SupportWorkOrderDto implements Serializable {
    private static final long serialVersionUID = 189463211262869926L;
    private Long id;
    private Long tenantCode;
    private String vin;
    private String odometer;
    private String vehicleType;
    private String vehicleYear;
    private String engine;
    private String transmission;
    private String vehicleWeek;
    private Long submitUserId;
    private String presentWorker;
    private String mobile;
    private String vehicleStatus;
    private String customerWait;
    private String emergency;
    private String model;
    private String orderType;
    private String problemType;
    private String problemSubType;
    private String problemDesc;
    private String consultContent;
    private String logUrl;
    private String imageUrl;
    private List<String> attachUrl;

    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date submitDate;

    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date problemDate;
    private Integer orderStatus;
    private Integer orderProperty;
    private Long currentOperator;
    private String personResponsible;
    private Long updater;
    private Date updateDate;
    private String problemSubject;

    @JsonIgnore
    private List<MultipartFile> files;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(Long tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getOdometer() {
        return this.odometer;
    }

    public void setOdometer(String odometer) {
        this.odometer = odometer;
    }

    public String getVehicleType() {
        return this.vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public Long getSubmitUserId() {
        return this.submitUserId;
    }

    public void setSubmitUserId(Long submitUserId) {
        this.submitUserId = submitUserId;
    }

    public String getPresentWorker() {
        return this.presentWorker;
    }

    public void setPresentWorker(String presentWorker) {
        this.presentWorker = presentWorker;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getVehicleStatus() {
        return this.vehicleStatus;
    }

    public void setVehicleStatus(String vehicleStatus) {
        this.vehicleStatus = vehicleStatus;
    }

    public String getCustomerWait() {
        return this.customerWait;
    }

    public void setCustomerWait(String customerWait) {
        this.customerWait = customerWait;
    }

    public String getEmergency() {
        return this.emergency;
    }

    public void setEmergency(String emergency) {
        this.emergency = emergency;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getOrderType() {
        return this.orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getProblemType() {
        return this.problemType;
    }

    public void setProblemType(String problemType) {
        this.problemType = problemType;
    }

    public String getProblemSubType() {
        return this.problemSubType;
    }

    public void setProblemSubType(String problemSubType) {
        this.problemSubType = problemSubType;
    }

    public String getProblemDesc() {
        return this.problemDesc;
    }

    public void setProblemDesc(String problemDesc) {
        this.problemDesc = problemDesc;
    }

    public String getConsultContent() {
        return this.consultContent;
    }

    public void setConsultContent(String consultContent) {
        this.consultContent = consultContent;
    }

    public String getLogUrl() {
        return this.logUrl;
    }

    public void setLogUrl(String logUrl) {
        this.logUrl = logUrl;
    }

    public String getImageUrl() {
        return this.imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Date getSubmitDate() {
        return this.submitDate;
    }

    public void setSubmitDate(Date submitDate) {
        this.submitDate = submitDate;
    }

    public Date getProblemDate() {
        return this.problemDate;
    }

    public void setProblemDate(Date problemDate) {
        this.problemDate = problemDate;
    }

    public Integer getOrderStatus() {
        return this.orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getOrderProperty() {
        return this.orderProperty;
    }

    public void setOrderProperty(Integer orderProperty) {
        this.orderProperty = orderProperty;
    }

    public Long getCurrentOperator() {
        return this.currentOperator;
    }

    public void setCurrentOperator(Long currentOperator) {
        this.currentOperator = currentOperator;
    }

    public String getPersonResponsible() {
        return this.personResponsible;
    }

    public void setPersonResponsible(String personResponsible) {
        this.personResponsible = personResponsible;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getVehicleYear() {
        return this.vehicleYear;
    }

    public void setVehicleYear(String vehicleYear) {
        this.vehicleYear = vehicleYear;
    }

    public String getEngine() {
        return this.engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getTransmission() {
        return this.transmission;
    }

    public void setTransmission(String transmission) {
        this.transmission = transmission;
    }

    public String getVehicleWeek() {
        return this.vehicleWeek;
    }

    public void setVehicleWeek(String vehicleWeek) {
        this.vehicleWeek = vehicleWeek;
    }

    public String getProblemSubject() {
        return this.problemSubject;
    }

    public void setProblemSubject(String problemSubject) {
        this.problemSubject = problemSubject;
    }

    public List<String> getAttachUrl() {
        return this.attachUrl;
    }

    public void setAttachUrl(List<String> attachUrl) {
        this.attachUrl = attachUrl;
    }

    public List<MultipartFile> getFiles() {
        return this.files;
    }

    public void setFiles(List<MultipartFile> files) {
        this.files = files;
    }
}
