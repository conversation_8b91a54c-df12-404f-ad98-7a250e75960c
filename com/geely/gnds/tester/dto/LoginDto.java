package com.geely.gnds.tester.dto;

import cn.hutool.core.util.StrUtil;
import java.security.interfaces.RSAPrivateKey;
import java.util.List;

/* loaded from: LoginDto.class */
public class LoginDto {
    private String username;
    private String password;
    private int loginType;
    private int sendCodeType;
    private String mobileCode;
    private String securitySoftwarePath;
    private List<SecurityCheckResultDTO> securityCheckResult;
    private String tenantCode;
    private String testerCode;
    private static String motherBoard;
    private static String bios;
    private static String processorId;
    private static String diskSn;
    private static String mac;
    private static String biosUuid;
    private static String productId;
    private static String machineGuid;
    private RSAPrivateKey requestKey;
    private String state;
    private String code;
    private String provider;
    private String redirectUri;
    private Boolean cepLogin;
    private Boolean geelySsoLogin;
    private String thirdPartyCode;
    private Boolean bindCep;
    private String displayName;
    private static String cpuInfo = "";
    private static String memoryInfo = "";
    private static String diskInfo = "";
    private static String networkInfo = "";

    public Boolean getBindCep() {
        return this.bindCep;
    }

    public void setBindCep(Boolean bindCep) {
        this.bindCep = bindCep;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public LoginDto(String username, String password, String mobileCode) {
        this.username = username;
        this.password = password;
        this.mobileCode = mobileCode;
    }

    public static String getMotherBoard() {
        return motherBoard;
    }

    public static void setMotherBoard(String motherBoard2) {
        motherBoard = motherBoard2;
    }

    public static String getBios() {
        return bios;
    }

    public static void setBios(String bios2) {
        bios = bios2;
    }

    public static String getProcessorId() {
        return processorId;
    }

    public static void setProcessorId(String processorId2) {
        processorId = processorId2;
    }

    public static String getDiskSn() {
        return diskSn;
    }

    public static void setDiskSn(String diskSn2) {
        diskSn = diskSn2;
    }

    public static String getMac() {
        return mac;
    }

    public static void setMac(String mac2) {
        mac = mac2;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMobileCode() {
        return this.mobileCode;
    }

    public void setMobileCode(String mobileCode) {
        this.mobileCode = mobileCode;
    }

    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getTesterCode() {
        return this.testerCode;
    }

    public void setTesterCode(String testerCode) {
        this.testerCode = testerCode;
    }

    public static String getBiosUuid() {
        return biosUuid;
    }

    public static void setBiosUuid(String biosUuid2) {
        biosUuid = biosUuid2;
    }

    public static String getProductId() {
        return productId;
    }

    public static void setProductId(String productId2) {
        productId = productId2;
    }

    public static String getMachineGuid() {
        return machineGuid;
    }

    public static void setMachineGuid(String machineGuid2) {
        machineGuid = machineGuid2;
    }

    public int getSendCodeType() {
        return this.sendCodeType;
    }

    public void setSendCodeType(int sendCodeType) {
        this.sendCodeType = sendCodeType;
    }

    public int getLoginType() {
        return this.loginType;
    }

    public void setLoginType(int loginType) {
        this.loginType = loginType;
    }

    public String getSecuritySoftwarePath() {
        return this.securitySoftwarePath;
    }

    public void setSecuritySoftwarePath(String securitySoftwarePath) {
        this.securitySoftwarePath = securitySoftwarePath;
    }

    public List<SecurityCheckResultDTO> getSecurityCheckResult() {
        return this.securityCheckResult;
    }

    public void setSecurityCheckResult(List<SecurityCheckResultDTO> securityCheckResult) {
        this.securityCheckResult = securityCheckResult;
    }

    public LoginDto() {
    }

    public String getProvider() {
        if (StrUtil.contains(this.state, "_")) {
            setProvider(StrUtil.subAfter(this.state, "_", false));
        }
        return this.provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public RSAPrivateKey getRequestKey() {
        return this.requestKey;
    }

    public void setRequestKey(RSAPrivateKey requestKey) {
        this.requestKey = requestKey;
    }

    public String getRedirectUri() {
        return this.redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public Boolean getCepLogin() {
        return this.cepLogin;
    }

    public void setCepLogin(Boolean cepLogin) {
        this.cepLogin = cepLogin;
    }

    public Boolean getGeelySsoLogin() {
        return this.geelySsoLogin;
    }

    public void setGeelySsoLogin(Boolean geelySsoLogin) {
        this.geelySsoLogin = geelySsoLogin;
    }

    public String getThirdPartyCode() {
        return this.thirdPartyCode;
    }

    public void setThirdPartyCode(String thirdPartyCode) {
        this.thirdPartyCode = thirdPartyCode;
    }

    public static String getDiskInfo() {
        return diskInfo;
    }

    public static void setDiskInfo(String diskInfo2) {
        diskInfo = diskInfo2;
    }

    public static String getCpuInfo() {
        return cpuInfo;
    }

    public static void setCpuInfo(String cpuInfo2) {
        cpuInfo = cpuInfo2;
    }

    public static String getMemoryInfo() {
        return memoryInfo;
    }

    public static void setMemoryInfo(String memoryInfo2) {
        memoryInfo = memoryInfo2;
    }

    public static String getNetworkInfo() {
        return networkInfo;
    }

    public static void setNetworkInfo(String networkInfo2) {
        networkInfo = networkInfo2;
    }
}
