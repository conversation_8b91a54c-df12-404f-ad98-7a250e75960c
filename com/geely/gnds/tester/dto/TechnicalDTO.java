package com.geely.gnds.tester.dto;

/* loaded from: TechnicalDTO.class */
public class TechnicalDTO {
    private static final long serialVersionUID = 3444496297300591995L;
    private String id;
    private String orderTypeDesc;
    private String advTheme;
    private String orderId;
    private String tsapprovalSendApprovedon;
    private String tsapprovalStatus;
    private String tsapprovalStatusDesc;
    private String matchScore;
    private String url;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderTypeDesc() {
        return this.orderTypeDesc;
    }

    public void setOrderTypeDesc(String orderTypeDesc) {
        this.orderTypeDesc = orderTypeDesc;
    }

    public String getAdvTheme() {
        return this.advTheme;
    }

    public void setAdvTheme(String advTheme) {
        this.advTheme = advTheme;
    }

    public String getOrderId() {
        return this.orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTsapprovalSendApprovedon() {
        return this.tsapprovalSendApprovedon;
    }

    public void setTsapprovalSendApprovedon(String tsapprovalSendApprovedon) {
        this.tsapprovalSendApprovedon = tsapprovalSendApprovedon;
    }

    public String getTsapprovalStatus() {
        return this.tsapprovalStatus;
    }

    public void setTsapprovalStatus(String tsapprovalStatus) {
        this.tsapprovalStatus = tsapprovalStatus;
    }

    public String getTsapprovalStatusDesc() {
        return this.tsapprovalStatusDesc;
    }

    public void setTsapprovalStatusDesc(String tsapprovalStatusDesc) {
        this.tsapprovalStatusDesc = tsapprovalStatusDesc;
    }

    public String getMatchScore() {
        return this.matchScore;
    }

    public void setMatchScore(String matchScore) {
        this.matchScore = matchScore;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
