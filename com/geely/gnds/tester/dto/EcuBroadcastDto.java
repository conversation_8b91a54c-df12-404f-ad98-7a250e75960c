package com.geely.gnds.tester.dto;

import java.util.List;

/* loaded from: EcuBroadcastDto.class */
public class EcuBroadcastDto {
    private String ecuName;
    private String ecuAddress;
    private String du;
    private String hardwarePartNumber;
    private String hardwareSerialNumber;
    private List<String> softwarePartNumbers;
    private String diagnosticPartNumber;
    private String diagnosticPartVersion;
    private String diagnosticNumber;
    private boolean readByVehicle;
    private int dtcCount;
    private String ecuFullName;
    private String wdid;
    private String gcid;
    private String gcidName;
    private String gcidTranName;
    private String locationName;
    private String locationTranName;

    public EcuBroadcastDto(String ecuAddress, String ecuName, String diagnosticPartNumber, String diagnosticPartVersion, boolean readByVehicle, int dtcCount) {
        this.ecuAddress = ecuAddress;
        this.ecuName = ecuName;
        this.diagnosticPartNumber = diagnosticPartNumber;
        this.diagnosticPartVersion = diagnosticPartVersion;
        this.readByVehicle = readByVehicle;
        this.dtcCount = dtcCount;
    }

    public EcuBroadcastDto() {
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public String getDiagnosticPartVersion() {
        return this.diagnosticPartVersion;
    }

    public void setDiagnosticPartVersion(String diagnosticPartVersion) {
        this.diagnosticPartVersion = diagnosticPartVersion;
    }

    public boolean isReadByVehicle() {
        return this.readByVehicle;
    }

    public void setReadByVehicle(boolean readByVehicle) {
        this.readByVehicle = readByVehicle;
    }

    public int getDtcCount() {
        return this.dtcCount;
    }

    public void setDtcCount(int dtcCount) {
        this.dtcCount = dtcCount;
    }

    public String getDiagnosticNumber() {
        return this.diagnosticNumber;
    }

    public void setDiagnosticNumber(String diagnosticNumber) {
        this.diagnosticNumber = diagnosticNumber;
    }

    public String getDu() {
        return this.du;
    }

    public void setDu(String du) {
        this.du = du;
    }

    public String getHardwarePartNumber() {
        return this.hardwarePartNumber;
    }

    public void setHardwarePartNumber(String hardwarePartNumber) {
        this.hardwarePartNumber = hardwarePartNumber;
    }

    public String getHardwareSerialNumber() {
        return this.hardwareSerialNumber;
    }

    public void setHardwareSerialNumber(String hardwareSerialNumber) {
        this.hardwareSerialNumber = hardwareSerialNumber;
    }

    public List<String> getSoftwarePartNumbers() {
        return this.softwarePartNumbers;
    }

    public void setSoftwarePartNumbers(List<String> softwarePartNumbers) {
        this.softwarePartNumbers = softwarePartNumbers;
    }

    public String getEcuFullName() {
        return this.ecuFullName;
    }

    public void setEcuFullName(String ecuFullName) {
        this.ecuFullName = ecuFullName;
    }

    public String getWdid() {
        return this.wdid;
    }

    public void setWdid(String wdid) {
        this.wdid = wdid;
    }

    public String getGcid() {
        return this.gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public String getGcidName() {
        return this.gcidName;
    }

    public void setGcidName(String gcidName) {
        this.gcidName = gcidName;
    }

    public String getLocationName() {
        return this.locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getGcidTranName() {
        return this.gcidTranName;
    }

    public void setGcidTranName(String gcidTranName) {
        this.gcidTranName = gcidTranName;
    }

    public String getLocationTranName() {
        return this.locationTranName;
    }

    public void setLocationTranName(String locationTranName) {
        this.locationTranName = locationTranName;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("EcuBroadcastDto{");
        sb.append("ecuName='").append(this.ecuName).append('\'');
        sb.append(", ecuAddress='").append(this.ecuAddress).append('\'');
        sb.append(", du='").append(this.du).append('\'');
        sb.append(", hardwarePartNumber='").append(this.hardwarePartNumber).append('\'');
        sb.append(", hardwareSerialNumber='").append(this.hardwareSerialNumber).append('\'');
        sb.append(", softwarePartNumbers=").append(this.softwarePartNumbers);
        sb.append(", diagnosticPartNumber='").append(this.diagnosticPartNumber).append('\'');
        sb.append(", diagnosticPartVersion='").append(this.diagnosticPartVersion).append('\'');
        sb.append(", diagnosticNumber='").append(this.diagnosticNumber).append('\'');
        sb.append(", readByVehicle=").append(this.readByVehicle);
        sb.append(", dtcCount=").append(this.dtcCount);
        sb.append(", ecuFullName='").append(this.ecuFullName).append('\'');
        sb.append(", wdid='").append(this.wdid).append('\'');
        sb.append(", gcid='").append(this.gcid).append('\'');
        sb.append(", gcidName='").append(this.gcidName).append('\'');
        sb.append(", locationName='").append(this.locationName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
