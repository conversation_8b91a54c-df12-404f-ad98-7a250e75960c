package com.geely.gnds.tester.dto.gbop.gidp;

import java.util.List;
import java.util.StringJoiner;

/* loaded from: GidpUpdateDiagnosisOrderDTO.class */
public class GidpUpdateDiagnosisOrderDTO {
    private String vin;
    private String diagnosticNumber;
    private String lq;
    private List<GidpSubDtcDTO> lr;
    private List<String> kS;
    private List<List<String>> kT;
    private String kM;
    private String kQ;
    private String kR;

    public String getDiagnosticNumber() {
        return this.diagnosticNumber;
    }

    public void setDiagnosticNumber(String diagnosticNumber) {
        this.diagnosticNumber = diagnosticNumber;
    }

    public String getUpdateType() {
        return this.lq;
    }

    public void setUpdateType(String updateType) {
        this.lq = updateType;
    }

    public List<GidpSubDtcDTO> getSubdtcList() {
        return this.lr;
    }

    public void setSubdtcList(List<GidpSubDtcDTO> subdtcList) {
        this.lr = subdtcList;
    }

    public List<String> getIssueFunctions() {
        return this.kS;
    }

    public void setIssueFunctions(List<String> issueFunctions) {
        this.kS = issueFunctions;
    }

    public List<List<String>> getIssueCsc() {
        return this.kT;
    }

    public void setIssueCsc(List<List<String>> issueCsc) {
        this.kT = issueCsc;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVehicleKm() {
        return this.kM;
    }

    public void setVehicleKm(String vehicleKm) {
        this.kM = vehicleKm;
    }

    public String getCurrentBssid() {
        return this.kQ;
    }

    public void setCurrentBssid(String currentBssid) {
        this.kQ = currentBssid;
    }

    public String getCurrentVehicleVersionNumber() {
        return this.kR;
    }

    public void setCurrentVehicleVersionNumber(String currentVehicleVersionNumber) {
        this.kR = currentVehicleVersionNumber;
    }

    public String toString() {
        return new StringJoiner(", ", GidpUpdateDiagnosisOrderDTO.class.getSimpleName() + "[", "]").add("vin='" + this.vin + "'").add("diagnosticNumber='" + this.diagnosticNumber + "'").add("updateType='" + this.lq + "'").add("subdtcList=" + this.lr).add("issueFunctions=" + this.kS).add("issueCsc=" + this.kT).add("vehicleKm='" + this.kM + "'").add("currentBssid='" + this.kQ + "'").add("currentVehicleVersionNumber='" + this.kR + "'").toString();
    }
}
