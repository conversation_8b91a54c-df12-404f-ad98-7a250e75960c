package com.geely.gnds.tester.dto.gbop.gidp;

import com.geely.gnds.ruoyi.common.constant.Constants;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: GidpTokenDTO.class */
public class GidpTokenDTO {
    private String gc;
    private String lj;

    public String getToken() {
        return this.gc;
    }

    public void setToken(String token) {
        this.gc = token;
    }

    public String getExpireTime() {
        return this.lj;
    }

    public void setExpireTime(String expireTime) {
        this.lj = expireTime;
    }

    public String toString() {
        return new ToStringBuilder(this).append(Constants.TOKEN, this.gc).append("expireTime", this.lj).toString();
    }
}
