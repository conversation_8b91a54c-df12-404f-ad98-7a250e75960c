package com.geely.gnds.tester.dto.gbop.gidp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: GidpTrDTO.class */
public class GidpTrDTO {
    private Integer lk;
    private String ll;
    private String lm;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date ln;
    private String lo;
    private String url;
    private String lp;

    public Integer getTrNo() {
        return this.lk;
    }

    public void setTrNo(Integer trNo) {
        this.lk = trNo;
    }

    public String getTrNumber() {
        return this.ll;
    }

    public void setTrNumber(String trNumber) {
        this.ll = trNumber;
    }

    public String getTrTitle() {
        return this.lm;
    }

    public void setTrTitle(String trTitle) {
        this.lm = trTitle;
    }

    public Date getTrCreationTime() {
        return this.ln;
    }

    public void setTrCreationTime(Date trCreationTime) {
        this.ln = trCreationTime;
    }

    public String getTrStatus() {
        return this.lo;
    }

    public void setTrStatus(String trStatus) {
        this.lo = trStatus;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCreateTrUrl() {
        return this.lp;
    }

    public void setCreateTrUrl(String createTrUrl) {
        this.lp = createTrUrl;
    }

    public String toString() {
        return new ToStringBuilder(this).append("trNo", this.lk).append("trNumber", this.ll).append("trTitle", this.lm).append("trCreationTime", this.ln).append("trStatus", this.lo).append("url", this.url).append("createTrUrl", this.lp).toString();
    }
}
