package com.geely.gnds.tester.dto.gbop.gidp;

import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: GidpSubDtcDTO.class */
public class GidpSubDtcDTO {
    private String kV;
    private String kW;
    private String kX;
    private String kY;
    private String kZ;

    public String getDtcEcu() {
        return this.kV;
    }

    public void setDtcEcu(String dtcEcu) {
        this.kV = dtcEcu;
    }

    public String getDtcDtc() {
        return this.kW;
    }

    public void setDtcDtc(String dtcDtc) {
        this.kW = dtcDtc;
    }

    public String getDtcDescription() {
        return this.kX;
    }

    public void setDtcDescription(String dtcDescription) {
        this.kX = dtcDescription;
    }

    public String getDtcType() {
        return this.kY;
    }

    public void setDtcType(String dtcType) {
        this.kY = dtcType;
    }

    public String getReadTime() {
        return this.kZ;
    }

    public void setReadTime(String readTime) {
        this.kZ = readTime;
    }

    public String toString() {
        return new ToStringBuilder(this).append("dtcEcu", this.kV).append("dtcDtc", this.kW).append("dtcDescription", this.kX).append("dtcType", this.kY).append("readTime", this.kZ).toString();
    }
}
