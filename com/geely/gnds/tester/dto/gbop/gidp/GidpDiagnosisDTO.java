package com.geely.gnds.tester.dto.gbop.gidp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: GidpDiagnosisDTO.class */
public class GidpDiagnosisDTO {
    private String diagnosticNumber;
    private String vin;
    private String vehicleType;
    private String kC;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date kD;
    private List<String> kE;
    private String kF;
    private List<String> kG;
    private Double kH;
    private String url;

    public String getDiagnosticNumber() {
        return this.diagnosticNumber;
    }

    public void setDiagnosticNumber(String diagnosticNumber) {
        this.diagnosticNumber = diagnosticNumber;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVehicleType() {
        return this.vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getRepairDescription() {
        return this.kC;
    }

    public void setRepairDescription(String repairDescription) {
        this.kC = repairDescription;
    }

    public Date getDiagnosisDate() {
        return this.kD;
    }

    public void setDiagnosisDate(Date diagnosisDate) {
        this.kD = diagnosisDate;
    }

    public List<String> getRepairType() {
        return this.kE;
    }

    public void setRepairType(List<String> repairType) {
        this.kE = repairType;
    }

    public String getReportStatus() {
        return this.kF;
    }

    public void setReportStatus(String reportStatus) {
        this.kF = reportStatus;
    }

    public List<String> getReason() {
        return this.kG;
    }

    public void setReason(List<String> reason) {
        this.kG = reason;
    }

    public Double getMaintenanceTime() {
        return this.kH;
    }

    public void setMaintenanceTime(Double maintenanceTime) {
        this.kH = maintenanceTime;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GidpDiagnosisDTO that = (GidpDiagnosisDTO) o;
        return new EqualsBuilder().append(this.diagnosticNumber, that.diagnosticNumber).append(this.vin, that.vin).append(this.vehicleType, that.vehicleType).append(this.kC, that.kC).append(this.kD, that.kD).append(this.kE, that.kE).append(this.kF, that.kF).append(this.kG, that.kG).append(this.kH, that.kH).append(this.url, that.url).isEquals();
    }

    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(this.diagnosticNumber).append(this.vin).append(this.vehicleType).append(this.kC).append(this.kD).append(this.kE).append(this.kF).append(this.kG).append(this.kH).append(this.url).toHashCode();
    }

    public String toString() {
        return new ToStringBuilder(this).append("diagnosticNumber", this.diagnosticNumber).append("vin", this.vin).append("vehicleType", this.vehicleType).append("repairDescription", this.kC).append("diagnosisDate", this.kD).append("repairType", this.kE).append("reportStatus", this.kF).append("reason", this.kG).append("maintenanceTime", this.kH).append("url", this.url).toString();
    }
}
