package com.geely.gnds.tester.dto.gbop.gidp;

/* loaded from: GidpActivityDTO.class */
public class GidpActivityDTO {
    private Long kw;
    private String kx;
    private String title;
    private String ky;
    private String kz;
    private String kA;
    private String kB;
    private String url;

    public Long getActivityNo() {
        return this.kw;
    }

    public void setActivityNo(Long activityNo) {
        this.kw = activityNo;
    }

    public String getActivityNumber() {
        return this.kx;
    }

    public void setActivityNumber(String activityNumber) {
        this.kx = activityNumber;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCorrelation() {
        return this.ky;
    }

    public void setCorrelation(String correlation) {
        this.ky = correlation;
    }

    public String getExecutionStatus() {
        return this.kz;
    }

    public void setExecutionStatus(String executionStatus) {
        this.kz = executionStatus;
    }

    public String getActivityReadTime() {
        return this.kA;
    }

    public void setActivityReadTime(String activityReadTime) {
        this.kA = activityReadTime;
    }

    public String getFeedBack() {
        return this.kB;
    }

    public void setFeedBack(String feedBack) {
        this.kB = feedBack;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
