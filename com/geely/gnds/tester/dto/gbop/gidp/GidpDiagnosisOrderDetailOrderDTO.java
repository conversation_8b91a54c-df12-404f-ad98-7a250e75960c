package com.geely.gnds.tester.dto.gbop.gidp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: GidpDiagnosisOrderDetailOrderDTO.class */
public class GidpDiagnosisOrderDetailOrderDTO extends GidpDiagnosisDTO {
    private String kI;
    private String kJ;
    private String kK;
    private String clientId;
    private String kL;
    private String kM;
    private String kN;
    private String kO;
    private String kP;
    private String kQ;
    private String kR;
    private List<String> kS;
    private List<List<String>> kT;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date kU;

    public String getDiagnosisMode() {
        return this.kI;
    }

    public void setDiagnosisMode(String diagnosisMode) {
        this.kI = diagnosisMode;
    }

    public String getDealerCode() {
        return this.kJ;
    }

    public void setDealerCode(String dealerCode) {
        this.kJ = dealerCode;
    }

    public String getDealerShortName() {
        return this.kK;
    }

    public void setDealerShortName(String dealerShortName) {
        this.kK = dealerShortName;
    }

    public String getClientId() {
        return this.clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getVehicleSerial() {
        return this.kL;
    }

    public void setVehicleSerial(String vehicleSerial) {
        this.kL = vehicleSerial;
    }

    public String getVehicleKm() {
        return this.kM;
    }

    public void setVehicleKm(String vehicleKm) {
        this.kM = vehicleKm;
    }

    public String getVehicleEngine() {
        return this.kN;
    }

    public void setVehicleEngine(String vehicleEngine) {
        this.kN = vehicleEngine;
    }

    public String getVehicleGear() {
        return this.kO;
    }

    public void setVehicleGear(String vehicleGear) {
        this.kO = vehicleGear;
    }

    public String getVehicleStructureWeek() {
        return this.kP;
    }

    public void setVehicleStructureWeek(String vehicleStructureWeek) {
        this.kP = vehicleStructureWeek;
    }

    public String getCurrentBssid() {
        return this.kQ;
    }

    public void setCurrentBssid(String currentBssid) {
        this.kQ = currentBssid;
    }

    public String getCurrentVehicleVersionNumber() {
        return this.kR;
    }

    public void setCurrentVehicleVersionNumber(String currentVehicleVersionNumber) {
        this.kR = currentVehicleVersionNumber;
    }

    public Date getClosedLoopDate() {
        return this.kU;
    }

    public void setClosedLoopDate(Date closedLoopDate) {
        this.kU = closedLoopDate;
    }

    public List<String> getIssueFunctions() {
        return this.kS;
    }

    public void setIssueFunctions(List<String> issueFunctions) {
        this.kS = issueFunctions;
    }

    public List<List<String>> getIssueCsc() {
        return this.kT;
    }

    public void setIssueCsc(List<List<String>> issueCsc) {
        this.kT = issueCsc;
    }

    @Override // com.geely.gnds.tester.dto.gbop.gidp.GidpDiagnosisDTO
    public String toString() {
        return new ToStringBuilder(this).append("diagnosisMode", this.kI).append("dealerCode", this.kJ).append("dealerShortName", this.kK).append("clientId", this.clientId).append("vehicleSerial", this.kL).append("vehicleKm", this.kM).append("vehicleEngine", this.kN).append("vehicleGear", this.kO).append("vehicleStructureWeek", this.kP).append("currentBssid", this.kQ).append("currentVehicleVersionNumber", this.kR).append("closedLoopDate", this.kU).append("issueFunctions" + this.kS).append("issueCsc" + this.kT).toString();
    }
}
