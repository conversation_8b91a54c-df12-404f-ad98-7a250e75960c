package com.geely.gnds.tester.dto;

import java.io.Serializable;

/* loaded from: ReloadDto.class */
public class ReloadDto implements Serializable {
    private String name;
    private String seqCode;
    private String description;
    private int priorityLevel;
    private String version;
    private String applic;
    private String seqContentString;
    private String category;
    private String requestId;
    private String vins;
    private String usageMode;
    private int taskType;
    private Long masterId;

    public ReloadDto(String name, String seqCode, String description, int priorityLevel, String version, String applic, String seqContent) {
        this.name = name;
        this.seqCode = seqCode;
        this.description = description;
        this.priorityLevel = priorityLevel;
        this.version = version;
        this.applic = applic;
        this.seqContentString = seqContent;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSeqCode() {
        return this.seqCode;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getPriorityLevel() {
        return this.priorityLevel;
    }

    public void setPriorityLevel(int priorityLevel) {
        this.priorityLevel = priorityLevel;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getApplic() {
        return this.applic;
    }

    public void setApplic(String applic) {
        this.applic = applic;
    }

    public String getSeqContentString() {
        return this.seqContentString;
    }

    public void setSeqContentString(String seqContentString) {
        this.seqContentString = seqContentString;
    }

    public ReloadDto() {
    }

    public String getRequestId() {
        return this.requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getCategory() {
        return this.category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getVins() {
        return this.vins;
    }

    public void setVins(String vins) {
        this.vins = vins;
    }

    public String getUsageMode() {
        return this.usageMode;
    }

    public void setUsageMode(String usageMode) {
        this.usageMode = usageMode;
    }

    public int getTaskType() {
        return this.taskType;
    }

    public void setTaskType(int taskType) {
        this.taskType = taskType;
    }

    public Long getMasterId() {
        return this.masterId;
    }

    public void setMasterId(Long masterId) {
        this.masterId = masterId;
    }
}
