package com.geely.gnds.tester.dto;

/* loaded from: VehicleStatusDto.class */
public class VehicleStatusDto {
    String connectStatus;
    String vehicleVoltage;
    String vehicleUsage;
    boolean tryConnect = true;
    String hvStatus;
    String drivingDistance;

    public String getConnectStatus() {
        return this.connectStatus;
    }

    public void setConnectStatus(String connectStatus) {
        this.connectStatus = connectStatus;
    }

    public String getVehicleVoltage() {
        return this.vehicleVoltage;
    }

    public void setVehicleVoltage(String vehicleVoltage) {
        this.vehicleVoltage = vehicleVoltage;
    }

    public String getVehicleUsage() {
        return this.vehicleUsage;
    }

    public void setVehicleUsage(String vehicleUsage) {
        this.vehicleUsage = vehicleUsage;
    }

    public boolean isTryConnect() {
        return this.tryConnect;
    }

    public void setTryConnect(boolean tryConnect) {
        this.tryConnect = tryConnect;
    }

    public String getHvStatus() {
        return this.hvStatus;
    }

    public void setHvStatus(String hvStatus) {
        this.hvStatus = hvStatus;
    }

    public String getDrivingDistance() {
        return this.drivingDistance;
    }

    public void setDrivingDistance(String drivingDistance) {
        this.drivingDistance = drivingDistance;
    }

    public String toString() {
        return "VehicleStatusDto{connectStatus='" + this.connectStatus + "', vehicleVoltage='" + this.vehicleVoltage + "', vehicleUsage='" + this.vehicleUsage + "', tryConnect=" + this.tryConnect + ", hvStatus='" + this.hvStatus + "', drivingDistance='" + this.drivingDistance + "'}";
    }
}
