package com.geely.gnds.tester.dto;

/* loaded from: DiaEcuWdidDTO.class */
public class DiaEcuWdidDTO {
    private Long id;
    private String importFile;
    private String fileVariant;
    private String ecuName;
    private String wdid;
    private String variant;
    private String gcid;
    private String gcidName;
    private String gcidTranName;
    private String locationName;
    private String locationTranName;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getImportFile() {
        return this.importFile;
    }

    public void setImportFile(String importFile) {
        this.importFile = importFile;
    }

    public String getFileVariant() {
        return this.fileVariant;
    }

    public void setFileVariant(String fileVariant) {
        this.fileVariant = fileVariant;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getWdid() {
        return this.wdid;
    }

    public void setWdid(String wdid) {
        this.wdid = wdid;
    }

    public String getVariant() {
        return this.variant;
    }

    public void setVariant(String variant) {
        this.variant = variant;
    }

    public String getGcid() {
        return this.gcid;
    }

    public void setGcid(String gcid) {
        this.gcid = gcid;
    }

    public String getGcidName() {
        return this.gcidName;
    }

    public void setGcidName(String gcidName) {
        this.gcidName = gcidName;
    }

    public String getLocationName() {
        return this.locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getGcidTranName() {
        return this.gcidTranName;
    }

    public void setGcidTranName(String gcidTranName) {
        this.gcidTranName = gcidTranName;
    }

    public String getLocationTranName() {
        return this.locationTranName;
    }

    public void setLocationTranName(String locationTranName) {
        this.locationTranName = locationTranName;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("DiaEcuWdidDTO{");
        sb.append("id=").append(this.id);
        sb.append(", importFile='").append(this.importFile).append('\'');
        sb.append(", fileVariant='").append(this.fileVariant).append('\'');
        sb.append(", ecuName='").append(this.ecuName).append('\'');
        sb.append(", wdid='").append(this.wdid).append('\'');
        sb.append(", variant='").append(this.variant).append('\'');
        sb.append(", gcid='").append(this.gcid).append('\'');
        sb.append(", gcidName='").append(this.gcidName).append('\'');
        sb.append(", locationName='").append(this.locationName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
