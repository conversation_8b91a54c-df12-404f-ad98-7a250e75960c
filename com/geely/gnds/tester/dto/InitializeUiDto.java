package com.geely.gnds.tester.dto;

import java.util.Map;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: InitializeUiDto.class */
public class InitializeUiDto {
    private String name;
    private String vin;
    private String args;
    private String type;
    private String tabType;
    private Boolean encrypted;
    private boolean nested;
    private String version;
    private Map<String, Object> resultMap;
    private Object initializeParams;
    private String windowType;
    private Map<String, Map> initializeUi;
    private String uiComponentName;
    private String seqCode;
    private String seqCodeChild;
    private String typeChild;
    private String filePath;

    public String getWindowType() {
        return this.windowType;
    }

    public void setWindowType(String windowType) {
        this.windowType = windowType;
    }

    public InitializeUiDto(String name, String vin, String args, String type, String uiComponentName, String seqCode, String initializeParams) {
        this.name = name;
        this.vin = vin;
        this.args = args;
        this.type = type;
        this.uiComponentName = uiComponentName;
        this.seqCode = seqCode;
        this.initializeParams = initializeParams;
    }

    public String getTabType() {
        return this.tabType;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getArgs() {
        return this.args;
    }

    public void setArgs(String args) {
        this.args = args;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUiComponentName() {
        return this.uiComponentName;
    }

    public void setUiComponentName(String uiComponentName) {
        this.uiComponentName = uiComponentName;
    }

    public String getSeqCode() {
        return this.seqCode;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public void setTabType(String tabType) {
        this.tabType = tabType;
    }

    public String getSeqCodeChild() {
        return this.seqCodeChild;
    }

    public void setSeqCodeChild(String seqCodeChild) {
        this.seqCodeChild = seqCodeChild;
    }

    public String getTypeChild() {
        return this.typeChild;
    }

    public void setTypeChild(String typeChild) {
        this.typeChild = typeChild;
    }

    public Object getInitializeParams() {
        return this.initializeParams;
    }

    public void setInitializeParams(Object initializeParams) {
        this.initializeParams = initializeParams;
    }

    public Map<String, Map> getInitializeUi() {
        return this.initializeUi;
    }

    public void setInitializeUi(Map<String, Map> initializeUi) {
        this.initializeUi = initializeUi;
    }

    public InitializeUiDto() {
    }

    public boolean isNested() {
        return this.nested;
    }

    public void setNested(boolean nested) {
        this.nested = nested;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Boolean getEncrypted() {
        return this.encrypted;
    }

    public void setEncrypted(Boolean encrypted) {
        this.encrypted = encrypted;
    }

    public Map<String, Object> getResultMap() {
        return this.resultMap;
    }

    public void setResultMap(Map<String, Object> resultMap) {
        this.resultMap = resultMap;
    }

    public String toString() {
        return new ToStringBuilder(this).append("name", this.name).append("vin", this.vin).append("args", this.args).append("type", this.type).append("tabType", this.tabType).append("encrypted", this.encrypted).append("nested", this.nested).append("version", this.version).append("initializeParams", this.initializeParams).append("windowType", this.windowType).append("initializeUi", this.initializeUi).append("uiComponentName", this.uiComponentName).append("seqCode", this.seqCode).append("seqCodeChild", this.seqCodeChild).append("typeChild", this.typeChild).append("filePath", this.filePath).toString();
    }
}
