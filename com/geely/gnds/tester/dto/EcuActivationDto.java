package com.geely.gnds.tester.dto;

/* loaded from: EcuActivationDto.class */
public class EcuActivationDto {
    private Long id;
    private String gcidName;
    private String location;
    private String ecu;
    private String did;
    private String activationName;
    private String translationActivationName;
    private String variant;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGcidName() {
        return this.gcidName;
    }

    public void setGcidName(String gcidName) {
        this.gcidName = gcidName;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getEcu() {
        return this.ecu;
    }

    public void setEcu(String ecu) {
        this.ecu = ecu;
    }

    public String getDid() {
        return this.did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getActivationName() {
        return this.activationName;
    }

    public void setActivationName(String activationName) {
        this.activationName = activationName;
    }

    public String getTranslationActivationName() {
        return this.translationActivationName;
    }

    public void setTranslationActivationName(String translationActivationName) {
        this.translationActivationName = translationActivationName;
    }

    public String getVariant() {
        return this.variant;
    }

    public void setVariant(String variant) {
        this.variant = variant;
    }

    public String toString() {
        return "DiaEcuActivationDto{id=" + this.id + ", gcidName='" + this.gcidName + "', location='" + this.location + "', ecu='" + this.ecu + "', did='" + this.did + "', activationName='" + this.activationName + "', translationActivationName='" + this.translationActivationName + "', variant='" + this.variant + "'}";
    }
}
