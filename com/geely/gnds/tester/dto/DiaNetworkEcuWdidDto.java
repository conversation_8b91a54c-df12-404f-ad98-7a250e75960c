package com.geely.gnds.tester.dto;

import java.util.Objects;

/* loaded from: DiaNetworkEcuWdidDto.class */
public class DiaNetworkEcuWdidDto {
    String ecuName;
    String wdid;
    String fileEcu;
    Integer type;

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getWdid() {
        return this.wdid;
    }

    public void setWdid(String wdid) {
        this.wdid = wdid;
    }

    public String getFileEcu() {
        return this.fileEcu;
    }

    public void setFileEcu(String fileEcu) {
        this.fileEcu = fileEcu;
    }

    public Integer getType() {
        return this.type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DiaNetworkEcuWdidDto that = (DiaNetworkEcuWdidDto) o;
        return Objects.equals(this.ecuName, that.ecuName) && Objects.equals(this.wdid, that.wdid) && Objects.equals(this.fileEcu, that.fileEcu) && Objects.equals(this.type, that.type);
    }

    public int hashCode() {
        return Objects.hash(this.ecuName, this.wdid, this.fileEcu, this.type);
    }

    public String toString() {
        return "DiaNetworkEcuWdidDto{ecuName='" + this.ecuName + "', wdid='" + this.wdid + "', fileEcu='" + this.fileEcu + "', type=" + this.type + '}';
    }

    public DiaNetworkEcuWdidDto() {
    }

    public DiaNetworkEcuWdidDto(String ecuName, String wdid, String fileEcu, Integer type) {
        this.ecuName = ecuName;
        this.wdid = wdid;
        this.fileEcu = fileEcu;
        this.type = type;
    }
}
