package com.geely.gnds.tester.dto;

import java.io.Serializable;

/* loaded from: RemoteDtcDTO.class */
public class RemoteDtcDTO implements Serializable {
    private static final long serialVersionUID = -955685942133555653L;
    private Long id;
    private Long taskId;
    private String vin;
    private String vehicleReadout;
    private String dtcInfo;
    private String ecuAddress;
    private String dtcInfo4;
    private String dtcInfo6;
    private String dd00;
    private Long tenantCode;
    private Long delFlag;
    private Long creator;
    private String createDate;
    private Long updater;
    private String updateDate;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return this.taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVehicleReadout() {
        return this.vehicleReadout;
    }

    public void setVehicleReadout(String vehicleReadout) {
        this.vehicleReadout = vehicleReadout;
    }

    public String getDtcInfo() {
        return this.dtcInfo;
    }

    public void setDtcInfo(String dtcInfo) {
        this.dtcInfo = dtcInfo;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getDtcInfo4() {
        return this.dtcInfo4;
    }

    public void setDtcInfo4(String dtcInfo4) {
        this.dtcInfo4 = dtcInfo4;
    }

    public String getDtcInfo6() {
        return this.dtcInfo6;
    }

    public void setDtcInfo6(String dtcInfo6) {
        this.dtcInfo6 = dtcInfo6;
    }

    public String getDd00() {
        return this.dd00;
    }

    public void setDd00(String dd00) {
        this.dd00 = dd00;
    }

    public Long getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(Long tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Long getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
    }

    public Long getCreator() {
        return this.creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public String getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Long getUpdater() {
        return this.updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public String getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }
}
