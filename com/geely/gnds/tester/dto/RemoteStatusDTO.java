package com.geely.gnds.tester.dto;

import com.geely.gnds.tester.dto.dtc.DtcInfoDTO;
import java.io.Serializable;
import java.util.List;

/* loaded from: RemoteStatusDTO.class */
public class RemoteStatusDTO implements Serializable {
    private static final long serialVersionUID = 4009262334635784471L;
    String vehicleVoltage;
    String vehicleUsage;
    String remoteDtc;
    private String respJson;
    List<DtcInfoDTO> list;
    boolean success;
    String taskId;
    String status;
    String reason;
    boolean complete;
    String msg;
    List<DaSeqDexReportDTO> seqDexReports;
    List<DiagResItemParseResultDto> paramList;

    public String getVehicleVoltage() {
        return this.vehicleVoltage;
    }

    public void setVehicleVoltage(String vehicleVoltage) {
        this.vehicleVoltage = vehicleVoltage;
    }

    public String getVehicleUsage() {
        return this.vehicleUsage;
    }

    public void setVehicleUsage(String vehicleUsage) {
        this.vehicleUsage = vehicleUsage;
    }

    public List<DtcInfoDTO> getList() {
        return this.list;
    }

    public String getRemoteDtc() {
        return this.remoteDtc;
    }

    public void setRemoteDtc(String remoteDtc) {
        this.remoteDtc = remoteDtc;
    }

    public void setList(List<DtcInfoDTO> list) {
        this.list = list;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getTaskId() {
        return this.taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isComplete() {
        return this.complete;
    }

    public void setComplete(boolean complete) {
        this.complete = complete;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DiagResItemParseResultDto> getParamList() {
        return this.paramList;
    }

    public void setParamList(List<DiagResItemParseResultDto> paramList) {
        this.paramList = paramList;
    }

    public String getReason() {
        return this.reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String toString() {
        return "RemoteStatusDTO{vehicleVoltage='" + this.vehicleVoltage + "', vehicleUsage='" + this.vehicleUsage + "', list='" + this.list + "', success=" + this.success + '}';
    }

    public String getRespJson() {
        return this.respJson;
    }

    public void setRespJson(String respJson) {
        this.respJson = respJson;
    }

    public List<DaSeqDexReportDTO> getSeqDexReports() {
        return this.seqDexReports;
    }

    public void setSeqDexReports(List<DaSeqDexReportDTO> seqDexReports) {
        this.seqDexReports = seqDexReports;
    }
}
