package com.geely.gnds.ruoyi.project.system.service;

import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import java.util.List;

/* loaded from: ISysUserService.class */
public interface ISysUserService {
    List<SysUser> selectUserList(SysUser sysUser);

    SysUser selectUserByUserName(String str);

    SysUser getUserByUserName(String str);

    SysUser selectUserById(Long l);

    String selectUserRoleGroup(String str);

    String selectUserPostGroup(String str);

    String checkUserNameUnique(String str);

    String checkPhoneUnique(SysUser sysUser);

    String checkEmailUnique(SysUser sysUser);

    void checkUserAllowed(SysUser sysUser);

    int insertUser(SysUser sysUser);

    int updateUser(SysUser sysUser);

    int updateUserStatus(SysUser sysUser);

    int updateUserProfile(SysUser sysUser);

    boolean updateUserAvatar(String str, String str2);

    int resetPwd(SysUser sysUser);

    int resetUserPwd(String str, String str2);

    int deleteUserById(Long l);

    int deleteUserByIds(Long[] lArr);

    String importUser(List<SysUser> list, Boolean bool, String str);
}
