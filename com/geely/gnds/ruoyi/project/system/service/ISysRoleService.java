package com.geely.gnds.ruoyi.project.system.service;

import com.geely.gnds.ruoyi.project.system.domain.SysRole;
import java.util.List;
import java.util.Set;

/* loaded from: ISysRoleService.class */
public interface ISysRoleService {
    List<SysRole> selectRoleList(SysRole sysRole);

    Set<String> selectRolePermissionByUserId(Long l);

    List<SysRole> selectRoleAll();

    List<Integer> selectRoleListByUserId(Long l);

    SysRole selectRoleById(Long l);

    String checkRoleNameUnique(SysRole sysRole);

    String checkRoleKeyUnique(SysRole sysRole);

    void checkRoleAllowed(SysRole sysRole);

    int countUserRoleByRoleId(Long l);

    int insertRole(SysRole sysRole);

    int updateRole(SysRole sysRole);

    int updateRoleStatus(SysRole sysRole);

    int authDataScope(SysRole sysRole);

    int deleteRoleById(Long l);

    int deleteRoleByIds(Long[] lArr);
}
