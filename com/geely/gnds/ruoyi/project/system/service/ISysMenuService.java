package com.geely.gnds.ruoyi.project.system.service;

import com.geely.gnds.ruoyi.framework.web.domain.TreeSelect;
import com.geely.gnds.ruoyi.project.system.domain.SysMenu;
import com.geely.gnds.ruoyi.project.system.domain.vo.RouterVo;
import java.util.List;
import java.util.Set;

/* loaded from: ISysMenuService.class */
public interface ISysMenuService {
    List<SysMenu> selectMenuList(Long l);

    List<SysMenu> selectMenuList(SysMenu sysMenu, Long l);

    Set<String> selectMenuPermsByUserId(Long l);

    List<SysMenu> selectMenuTreeByUserId(Long l);

    List<Integer> selectMenuListByRoleId(Long l);

    List<RouterVo> buildMenus(List<SysMenu> list);

    List<SysMenu> buildMenuTree(List<SysMenu> list);

    List<TreeSelect> buildMenuTreeSelect(List<SysMenu> list);

    SysMenu selectMenuById(Long l);

    boolean hasChildByMenuId(Long l);

    boolean checkMenuExistRole(Long l);

    int insertMenu(SysMenu sysMenu);

    int updateMenu(SysMenu sysMenu);

    int deleteMenuById(Long l);

    String checkMenuNameUnique(SysMenu sysMenu);
}
