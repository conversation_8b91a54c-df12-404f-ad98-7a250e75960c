package com.geely.gnds.ruoyi.project.system.service;

import com.geely.gnds.ruoyi.project.system.domain.SysConfig;
import java.util.List;

/* loaded from: ISysConfigService.class */
public interface ISysConfigService {
    SysConfig selectConfigById(Long l);

    String selectConfigByKey(String str);

    List<SysConfig> selectConfigList(SysConfig sysConfig);

    int insertConfig(SysConfig sysConfig);

    int updateConfig(SysConfig sysConfig);

    int deleteConfigByIds(Long[] lArr);

    void clearCache();

    String checkConfigKeyUnique(SysConfig sysConfig);
}
