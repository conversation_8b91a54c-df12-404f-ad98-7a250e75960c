package com.geely.gnds.ruoyi.project.system.service.impl;

import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.ruoyi.framework.aspectj.lang.annotation.DataScope;
import com.geely.gnds.ruoyi.project.system.domain.SysRole;
import com.geely.gnds.ruoyi.project.system.domain.SysRoleDept;
import com.geely.gnds.ruoyi.project.system.domain.SysRoleMenu;
import com.geely.gnds.ruoyi.project.system.mapper.SysRoleDeptMapper;
import com.geely.gnds.ruoyi.project.system.mapper.SysRoleMapper;
import com.geely.gnds.ruoyi.project.system.mapper.SysRoleMenuMapper;
import com.geely.gnds.ruoyi.project.system.mapper.SysUserRoleMapper;
import com.geely.gnds.ruoyi.project.system.service.ISysRoleService;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
/* loaded from: SysRoleServiceImpl.class */
public class SysRoleServiceImpl implements ISysRoleService {

    @Autowired
    private SysRoleMapper hx;

    @Autowired
    private SysRoleMenuMapper hw;

    @Autowired
    private SysUserRoleMapper hy;

    @Autowired
    private SysRoleDeptMapper hz;

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    @DataScope(deptAlias = "d")
    public List<SysRole> selectRoleList(SysRole role) {
        return this.hx.selectRoleList(role);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<SysRole> perms = this.hx.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms) {
            if (StringUtils.isNotNull(perm)) {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(ConstantEnum.COMMA)));
            }
        }
        return permsSet;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public List<SysRole> selectRoleAll() {
        return ((SysRoleServiceImpl) SpringUtils.getAopProxy(this)).selectRoleList(new SysRole());
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public List<Integer> selectRoleListByUserId(Long userId) {
        return this.hx.selectRoleListByUserId(userId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public SysRole selectRoleById(Long roleId) {
        return this.hx.selectRoleById(roleId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public String checkRoleNameUnique(SysRole role) {
        Long roleId = Long.valueOf(StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId().longValue());
        SysRole info = this.hx.checkRoleNameUnique(role.getRoleName());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue()) {
            return "1";
        }
        return "0";
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public String checkRoleKeyUnique(SysRole role) {
        Long roleId = Long.valueOf(StringUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId().longValue());
        SysRole info = this.hx.checkRoleKeyUnique(role.getRoleKey());
        if (StringUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue()) {
            return "1";
        }
        return "0";
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public void checkRoleAllowed(SysRole role) {
        if (StringUtils.isNotNull(role.getRoleId()) && role.isAdmin()) {
            throw new CustomException("不允许操作超级管理员角色");
        }
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public int countUserRoleByRoleId(Long roleId) {
        return this.hy.countUserRoleByRoleId(roleId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    @Transactional(rollbackFor = {Exception.class})
    public int insertRole(SysRole role) {
        role.setCreateTime(System.currentTimeMillis() + "");
        this.hx.insertRole(role);
        return f(role);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    @Transactional(rollbackFor = {Exception.class})
    public int updateRole(SysRole role) {
        role.setUpdateTime(System.currentTimeMillis() + "");
        this.hx.updateRole(role);
        this.hw.deleteRoleMenuByRoleId(role.getRoleId());
        return f(role);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public int updateRoleStatus(SysRole role) {
        return this.hx.updateRole(role);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    @Transactional(rollbackFor = {Exception.class})
    public int authDataScope(SysRole role) {
        this.hx.updateRole(role);
        this.hz.deleteRoleDeptByRoleId(role.getRoleId());
        return g(role);
    }

    public int f(SysRole role) {
        int rows = 1;
        List<SysRoleMenu> list = new ArrayList<>();
        for (Long menuId : role.getMenuIds()) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (list.size() > 0) {
            rows = this.hw.batchRoleMenu(list);
        }
        return rows;
    }

    public int g(SysRole role) {
        int rows = 1;
        List<SysRoleDept> list = new ArrayList<>();
        for (Long deptId : role.getDeptIds()) {
            SysRoleDept rd = new SysRoleDept();
            rd.setRoleId(role.getRoleId());
            rd.setDeptId(deptId);
            list.add(rd);
        }
        if (list.size() > 0) {
            rows = this.hz.batchRoleDept(list);
        }
        return rows;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public int deleteRoleById(Long roleId) {
        return this.hx.deleteRoleById(roleId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysRoleService
    public int deleteRoleByIds(Long[] roleIds) {
        for (Long roleId : roleIds) {
            checkRoleAllowed(new SysRole(roleId));
            SysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0) {
                throw new CustomException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }
        return this.hx.deleteRoleByIds(roleIds);
    }
}
