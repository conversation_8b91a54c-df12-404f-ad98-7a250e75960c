package com.geely.gnds.ruoyi.project.system.service.impl;

import com.geely.gnds.ruoyi.common.constant.UserConstants;
import com.geely.gnds.ruoyi.common.utils.SecurityUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.web.domain.TreeSelect;
import com.geely.gnds.ruoyi.project.system.domain.SysMenu;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.domain.vo.MetaVo;
import com.geely.gnds.ruoyi.project.system.domain.vo.RouterVo;
import com.geely.gnds.ruoyi.project.system.mapper.SysMenuMapper;
import com.geely.gnds.ruoyi.project.system.mapper.SysRoleMenuMapper;
import com.geely.gnds.ruoyi.project.system.service.ISysMenuService;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: SysMenuServiceImpl.class */
public class SysMenuServiceImpl implements ISysMenuService {
    public static final String hu = "perms[\"{0}\"]";

    @Autowired
    private SysMenuMapper hv;

    @Autowired
    private SysRoleMenuMapper hw;

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public List<SysMenu> selectMenuList(Long userId) {
        return selectMenuList(new SysMenu(), userId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public List<SysMenu> selectMenuList(SysMenu menu, Long userId) {
        List<SysMenu> menuList;
        if (SysUser.isAdmin(userId)) {
            menuList = this.hv.selectMenuList(menu);
        } else {
            menu.getParams().put("userId", userId);
            menuList = this.hv.selectMenuListByUserId(menu);
        }
        return menuList;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public Set<String> selectMenuPermsByUserId(Long userId) {
        List<String> perms = this.hv.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(ConstantEnum.COMMA)));
            }
        }
        return permsSet;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public List<SysMenu> selectMenuTreeByUserId(Long userId) {
        List<SysMenu> menus;
        if (SecurityUtils.isAdmin(userId)) {
            menus = this.hv.selectMenuTreeAll();
        } else {
            menus = this.hv.selectMenuTreeByUserId(userId);
        }
        return a(menus, 0);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public List<Integer> selectMenuListByRoleId(Long roleId) {
        return this.hv.selectMenuListByRoleId(roleId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public List<RouterVo> buildMenus(List<SysMenu> menus) {
        List<RouterVo> routers = new LinkedList<>();
        for (SysMenu menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(a(menu));
            router.setPath(b(menu));
            router.setComponent(c(menu));
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
            List<SysMenu> cMenus = menu.getChildren();
            if (!cMenus.isEmpty() && cMenus.size() > 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (d(menu)) {
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public List<SysMenu> buildMenuTree(List<SysMenu> menus) {
        List<SysMenu> returnList = new ArrayList();
        for (SysMenu t : menus) {
            if (t.getParentId().longValue() == 0) {
                a(menus, t);
                returnList.add(t);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus) {
        List<SysMenu> menuTrees = buildMenuTree(menus);
        return (List) menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public SysMenu selectMenuById(Long menuId) {
        return this.hv.selectMenuById(menuId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public boolean hasChildByMenuId(Long menuId) {
        int result = this.hv.hasChildByMenuId(menuId);
        return result > 0;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public boolean checkMenuExistRole(Long menuId) {
        int result = this.hw.checkMenuExistRole(menuId);
        return result > 0;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public int insertMenu(SysMenu menu) {
        menu.setCreateTime(System.currentTimeMillis() + "");
        return this.hv.insertMenu(menu);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public int updateMenu(SysMenu menu) {
        menu.setUpdateTime(System.currentTimeMillis() + "");
        return this.hv.updateMenu(menu);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public int deleteMenuById(Long menuId) {
        return this.hv.deleteMenuById(menuId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysMenuService
    public String checkMenuNameUnique(SysMenu menu) {
        Long menuId = Long.valueOf(StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId().longValue());
        SysMenu info = this.hv.checkMenuNameUnique(menu.getMenuName(), menu.getParentId());
        if (StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue()) {
            return "1";
        }
        return "0";
    }

    public String a(SysMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        if (d(menu)) {
            routerName = "";
        }
        return routerName;
    }

    public String b(SysMenu menu) {
        String routerPath = menu.getPath();
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(menu.getMenuType()) && "1".equals(menu.getIsFrame())) {
            routerPath = "/" + menu.getPath();
        } else if (d(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    public String c(SysMenu menu) {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !d(menu)) {
            component = menu.getComponent();
        }
        return component;
    }

    public boolean d(SysMenu menu) {
        return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(menu.getMenuType()) && menu.getIsFrame().equals("1");
    }

    public List<SysMenu> a(List<SysMenu> list, int parentId) {
        List<SysMenu> returnList = new ArrayList<>();
        for (SysMenu t : list) {
            if (t.getParentId().longValue() == parentId) {
                a(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    private void a(List<SysMenu> list, SysMenu t) {
        List<SysMenu> childList = b(list, t);
        t.setChildren(childList);
        for (SysMenu tChild : childList) {
            if (c(list, tChild)) {
                for (SysMenu n : childList) {
                    a(list, n);
                }
            }
        }
    }

    private List<SysMenu> b(List<SysMenu> list, SysMenu t) {
        List<SysMenu> tlist = new ArrayList<>();
        for (SysMenu n : list) {
            if (n.getParentId().longValue() == t.getMenuId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    private boolean c(List<SysMenu> list, SysMenu t) {
        return b(list, t).size() > 0;
    }
}
