package com.geely.gnds.ruoyi.project.system.service.impl;

import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.SecurityUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.aspectj.lang.annotation.DataScope;
import com.geely.gnds.ruoyi.project.system.domain.SysPost;
import com.geely.gnds.ruoyi.project.system.domain.SysRole;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.domain.SysUserPost;
import com.geely.gnds.ruoyi.project.system.domain.SysUserRole;
import com.geely.gnds.ruoyi.project.system.mapper.SysPostMapper;
import com.geely.gnds.ruoyi.project.system.mapper.SysRoleMapper;
import com.geely.gnds.ruoyi.project.system.mapper.SysUserMapper;
import com.geely.gnds.ruoyi.project.system.mapper.SysUserPostMapper;
import com.geely.gnds.ruoyi.project.system.mapper.SysUserRoleMapper;
import com.geely.gnds.ruoyi.project.system.service.ISysConfigService;
import com.geely.gnds.ruoyi.project.system.service.ISysUserService;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
/* loaded from: SysUserServiceImpl.class */
public class SysUserServiceImpl implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper hA;

    @Autowired
    private SysRoleMapper hx;

    @Autowired
    private SysPostMapper hB;

    @Autowired
    private SysUserRoleMapper hy;

    @Autowired
    private SysUserPostMapper hC;

    @Autowired
    private ISysConfigService hD;

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        return this.hA.selectUserList(user);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public SysUser selectUserByUserName(String userName) {
        return this.hA.selectUserByUserName(userName);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public SysUser getUserByUserName(String userName) {
        return this.hA.getUserByUserName(userName);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public SysUser selectUserById(Long userId) {
        return this.hA.selectUserById(userId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = this.hx.selectRolesByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysRole role : list) {
            idsStr.append(role.getRoleName()).append(ConstantEnum.COMMA);
        }
        if (StringUtils.isNotEmpty(idsStr.toString())) {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = this.hB.selectPostsByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysPost post : list) {
            idsStr.append(post.getPostName()).append(ConstantEnum.COMMA);
        }
        if (StringUtils.isNotEmpty(idsStr.toString())) {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public String checkUserNameUnique(String userName) {
        int count = this.hA.checkUserNameUnique(userName);
        if (count > 0) {
            return "1";
        }
        return "0";
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public String checkPhoneUnique(SysUser user) {
        Long userId = Long.valueOf(StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId().longValue());
        SysUser info = this.hA.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return "1";
        }
        return "0";
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public String checkEmailUnique(SysUser user) {
        Long userId = Long.valueOf(StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId().longValue());
        SysUser info = this.hA.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return "1";
        }
        return "0";
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new CustomException("不允许操作超级管理员用户");
        }
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    @Transactional(rollbackFor = {Exception.class})
    public int insertUser(SysUser user) {
        user.setCreateTime(System.currentTimeMillis() + "");
        int rows = this.hA.insertUser(user);
        j(user);
        i(user);
        return rows;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    @Transactional(rollbackFor = {Exception.class})
    public int updateUser(SysUser user) {
        user.setUpdateTime(System.currentTimeMillis() + "");
        Long userId = user.getUserId();
        this.hy.deleteUserRoleByUserId(userId);
        i(user);
        this.hC.deleteUserPostByUserId(userId);
        j(user);
        return this.hA.updateUser(user);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public int updateUserStatus(SysUser user) {
        return this.hA.updateUser(user);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public int updateUserProfile(SysUser user) {
        return this.hA.updateUser(user);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public boolean updateUserAvatar(String userName, String avatar) {
        return this.hA.updateUserAvatar(userName, avatar) > 0;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public int resetPwd(SysUser user) {
        return this.hA.updateUser(user);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public int resetUserPwd(String userName, String password) {
        return this.hA.resetUserPwd(userName, password);
    }

    public void i(SysUser user) {
        Long[] roles = user.getRoleIds();
        if (StringUtils.isNotNull(roles)) {
            List<SysUserRole> list = new ArrayList<>();
            for (Long roleId : roles) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(user.getUserId());
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0) {
                this.hy.batchUserRole(list);
            }
        }
    }

    public void j(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotNull(posts)) {
            List<SysUserPost> list = new ArrayList<>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            if (list.size() > 0) {
                this.hC.batchUserPost(list);
            }
        }
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public int deleteUserById(Long userId) {
        this.hy.deleteUserRoleByUserId(userId);
        this.hC.deleteUserPostByUserId(userId);
        return this.hA.deleteUserById(userId);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
        }
        return this.hA.deleteUserByIds(userIds);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysUserService
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new CustomException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = this.hD.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList) {
            try {
                SysUser u = this.hA.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport.booleanValue()) {
                    user.setUpdateBy(operName);
                    updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        }
        successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        return successMsg.toString();
    }
}
