package com.geely.gnds.ruoyi.project.system.service.impl;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.core.text.Convert;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.project.system.domain.SysConfig;
import com.geely.gnds.ruoyi.project.system.mapper.SysConfigMapper;
import com.geely.gnds.ruoyi.project.system.service.ISysConfigService;
import java.util.Collection;
import java.util.List;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: SysConfigServiceImpl.class */
public class SysConfigServiceImpl implements ISysConfigService {

    @Autowired
    private SysConfigMapper ht;

    @Autowired
    private EhcacheClient ehcacheClient;

    @PostConstruct
    public void init() {
        List<SysConfig> configsList = this.ht.selectConfigList(new SysConfig());
        for (SysConfig config : configsList) {
            this.ehcacheClient.a(getCacheKey(config.getConfigKey()), 0, config.getConfigValue());
        }
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysConfigService
    public SysConfig selectConfigById(Long configId) {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        return this.ht.selectConfig(config);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysConfigService
    public String selectConfigByKey(String configKey) {
        String configValue = Convert.toStr(this.ehcacheClient.w(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue)) {
            return configValue;
        }
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = this.ht.selectConfig(config);
        if (StringUtils.isNotNull(retConfig)) {
            this.ehcacheClient.a(getCacheKey(configKey), 0, retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return "";
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysConfigService
    public List<SysConfig> selectConfigList(SysConfig config) {
        return this.ht.selectConfigList(config);
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysConfigService
    public int insertConfig(SysConfig config) {
        config.setCreateTime(System.currentTimeMillis() + "");
        int row = this.ht.insertConfig(config);
        if (row > 0) {
            this.ehcacheClient.a(getCacheKey(config.getConfigKey()), 0, config.getConfigValue());
        }
        return row;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysConfigService
    public int updateConfig(SysConfig config) {
        config.setUpdateTime(System.currentTimeMillis() + "");
        int row = this.ht.updateConfig(config);
        if (row > 0) {
            this.ehcacheClient.a(getCacheKey(config.getConfigKey()), 0, config.getConfigValue());
        }
        return row;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysConfigService
    public int deleteConfigByIds(Long[] configIds) {
        int count = this.ht.deleteConfigByIds(configIds);
        if (count > 0) {
            Collection<String> keys = this.ehcacheClient.x("sys_config:*");
            for (String key : keys) {
                this.ehcacheClient.y(key);
            }
        }
        return count;
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysConfigService
    public void clearCache() {
        Collection<String> keys = this.ehcacheClient.x("sys_config:*");
        for (String key : keys) {
            this.ehcacheClient.y(key);
        }
    }

    @Override // com.geely.gnds.ruoyi.project.system.service.ISysConfigService
    public String checkConfigKeyUnique(SysConfig config) {
        Long configId = Long.valueOf(StringUtils.isNull(config.getConfigId()) ? -1L : config.getConfigId().longValue());
        SysConfig info = this.ht.checkConfigKeyUnique(config.getConfigKey());
        if (StringUtils.isNotNull(info) && info.getConfigId().longValue() != configId.longValue()) {
            return "1";
        }
        return "0";
    }

    private String getCacheKey(String configKey) {
        return Constants.SYS_CONFIG_KEY + configKey;
    }
}
