package com.geely.gnds.ruoyi.project.system.mapper;

import com.geely.gnds.ruoyi.project.system.domain.SysUserRole;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/* loaded from: SysUserRoleMapper.class */
public interface SysUserRoleMapper {
    int deleteUserRoleByUserId(Long l);

    int deleteUserRole(Long[] lArr);

    int countUserRoleByRoleId(Long l);

    int batchUserRole(List<SysUserRole> list);

    int deleteUserRoleInfo(SysUserRole sysUserRole);

    int deleteUserRoleInfos(@Param("roleId") Long l, @Param("userIds") Long[] lArr);
}
