package com.geely.gnds.ruoyi.project.system.mapper;

import com.geely.gnds.ruoyi.project.system.domain.SysRole;
import java.util.List;

/* loaded from: SysRoleMapper.class */
public interface SysRoleMapper {
    List<SysRole> selectRoleList(SysRole sysRole);

    List<SysRole> selectRolePermissionByUserId(Long l);

    List<SysRole> selectRoleAll();

    List<Integer> selectRoleListByUserId(Long l);

    SysRole selectRoleById(Long l);

    List<SysRole> selectRolesByUserName(String str);

    SysRole checkRoleNameUnique(String str);

    SysRole checkRoleKeyUnique(String str);

    int updateRole(SysRole sysRole);

    int insertRole(SysRole sysRole);

    int deleteRoleById(Long l);

    int deleteRoleByIds(Long[] lArr);
}
