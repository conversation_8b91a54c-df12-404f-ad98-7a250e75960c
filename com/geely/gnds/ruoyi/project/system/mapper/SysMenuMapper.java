package com.geely.gnds.ruoyi.project.system.mapper;

import com.geely.gnds.ruoyi.project.system.domain.SysMenu;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/* loaded from: SysMenuMapper.class */
public interface SysMenuMapper {
    List<SysMenu> selectMenuList(SysMenu sysMenu);

    List<String> selectMenuPerms();

    List<SysMenu> selectMenuListByUserId(SysMenu sysMenu);

    List<String> selectMenuPermsByUserId(Long l);

    List<SysMenu> selectMenuTreeAll();

    List<SysMenu> selectMenuTreeByUserId(Long l);

    List<Integer> selectMenuListByRoleId(Long l);

    SysMenu selectMenuById(Long l);

    int hasChildByMenuId(Long l);

    int insertMenu(SysMenu sysMenu);

    int updateMenu(SysMenu sysMenu);

    int deleteMenuById(Long l);

    SysMenu checkMenuNameUnique(@Param("menuName") String str, @Param("parentId") Long l);
}
