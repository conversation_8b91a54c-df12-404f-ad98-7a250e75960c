package com.geely.gnds.ruoyi.project.system.mapper;

import com.geely.gnds.ruoyi.project.system.domain.SysConfig;
import java.util.List;

/* loaded from: SysConfigMapper.class */
public interface SysConfigMapper {
    SysConfig selectConfig(SysConfig sysConfig);

    List<SysConfig> selectConfigList(SysConfig sysConfig);

    SysConfig checkConfigKeyUnique(String str);

    int insertConfig(SysConfig sysConfig);

    int updateConfig(SysConfig sysConfig);

    int deleteConfigById(Long l);

    int deleteConfigByIds(Long[] lArr);
}
