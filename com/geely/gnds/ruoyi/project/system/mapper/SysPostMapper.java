package com.geely.gnds.ruoyi.project.system.mapper;

import com.geely.gnds.ruoyi.project.system.domain.SysPost;
import java.util.List;

/* loaded from: SysPostMapper.class */
public interface SysPostMapper {
    List<SysPost> selectPostList(SysPost sysPost);

    List<SysPost> selectPostAll();

    SysPost selectPostById(Long l);

    List<Integer> selectPostListByUserId(Long l);

    List<SysPost> selectPostsByUserName(String str);

    int deletePostById(Long l);

    int deletePostByIds(Long[] lArr);

    int updatePost(SysPost sysPost);

    int insertPost(SysPost sysPost);

    SysPost checkPostNameUnique(String str);

    SysPost checkPostCodeUnique(String str);
}
