package com.geely.gnds.ruoyi.project.system.mapper;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
/* loaded from: SysUserMapper.class */
public interface SysUserMapper {
    List<SysUser> selectUserList(SysUser sysUser);

    SysUser selectUserByUserName(String str);

    SysUser getUserByUserName(String str);

    SysUser selectUserById(Long l);

    int insertUser(SysUser sysUser);

    int updateUser(SysUser sysUser);

    int updateUserAvatar(@Param("userName") String str, @Param(Constants.JWT_AVATAR) String str2);

    int resetUserPwd(@Param("userName") String str, @Param("password") String str2);

    int deleteUserById(Long l);

    int deleteUserByIds(Long[] lArr);

    int checkUserNameUnique(String str);

    SysUser checkPhoneUnique(String str);

    SysUser checkEmailUnique(String str);
}
