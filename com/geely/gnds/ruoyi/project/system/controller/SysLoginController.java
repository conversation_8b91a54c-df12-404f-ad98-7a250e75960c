package com.geely.gnds.ruoyi.project.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginBody;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.SysLoginService;
import com.geely.gnds.ruoyi.framework.security.service.SysPermissionService;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.ruoyi.project.system.domain.SysMenu;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.service.ISysMenuService;
import com.geely.gnds.ruoyi.project.system.service.ISysUserService;
import com.geely.gnds.tester.common.CacheUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.CepUserDTO;
import com.geely.gnds.tester.dto.LoginDto;
import com.geely.gnds.tester.dto.LoginInfoDTO;
import com.geely.gnds.tester.dto.SecurityCheckResultDTO;
import com.geely.gnds.tester.dto.SysTenantDto;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ConnectedService;
import com.geely.gnds.tester.service.LoginService;
import com.geely.gnds.tester.service.SecurityCheckService;
import com.geely.gnds.tester.service.SynchronousService;
import com.geely.gnds.tester.util.RsaUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.security.interfaces.RSAPrivateKey;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
/* loaded from: SysLoginController.class */
public class SysLoginController {

    @Autowired
    private SysLoginService hl;

    @Autowired
    private ISysMenuService gv;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private LoginService hm;

    @Autowired
    private SynchronousService hn;

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private ISysUserService ho;

    @Autowired
    private CacheUtils hp;

    @Autowired
    private TesterConfigDao eC;

    @Autowired
    private Cloud cloud;

    @Autowired
    private SecurityCheckService hq;

    @Autowired
    private ConnectedService hr;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger LOG = LoggerFactory.getLogger(SysLoginController.class);

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.springframework.security.authentication.BadCredentialsException */
    @PostMapping({"/login"})
    public AjaxResult a(HttpServletRequest request, @RequestBody LoginDto loginDto) throws BadCredentialsException {
        String token;
        this.hp.clearAll();
        AjaxResult ajax = AjaxResult.z();
        String password = loginDto.getPassword();
        Boolean cepLogin = loginDto.getCepLogin();
        String username = loginDto.getUsername();
        if (cepLogin == null) {
            cepLogin = false;
        }
        if (!cepLogin.booleanValue()) {
            try {
                RSAPrivateKey privateKey = (RSAPrivateKey) request.getSession().getAttribute(ConstantEnum.PRIVATEKEY_STR);
                password = RsaUtils.decryptData(privateKey, password);
                loginDto.setRequestKey(privateKey);
            } catch (Exception e) {
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
            }
        }
        List<SecurityCheckResultDTO> securityCheckResult = this.hq.checkSecurityInstallKey(loginDto.getSecuritySoftwarePath());
        loginDto.setSecurityCheckResult(securityCheckResult);
        try {
            LoginInfoDTO loginInfoDTO = this.hm.onlineLogin(loginDto);
            String msg = loginInfoDTO.getMsg();
            int index = msg.indexOf("TesterSessionId-");
            if (StringUtils.isNotBlank(msg)) {
                if ("mobileAuth".equals(msg)) {
                    ajax.put(AjaxResult.gA, 1);
                    return ajax;
                }
                if ("emailAuth".equals(msg)) {
                    ajax.put(AjaxResult.gA, 2);
                    return ajax;
                }
                if (index != -1) {
                    msg.substring(index);
                } else {
                    throw new BadCredentialsException(msg);
                }
            }
            SysUser user = this.hn.synchronousUsers(username);
            TesterConfigDto config = this.eC.getConfig();
            if (user == null) {
                LOG.error("云端登录成功，但客户端同步用户信息时出错，需要去云端退出登录");
                this.cloud.r(config.getTesterCode(), user.getUserName());
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
            }
            String accountExpired = user.getAccountExpired();
            if (StringUtils.isNotBlank(accountExpired)) {
                Date date = new Date(Long.valueOf(accountExpired).longValue());
                Date now = new Date();
                if (now.after(date)) {
                    try {
                        this.cloud.r(config.getTesterCode(), user.getUserName());
                    } catch (Exception e2) {
                        LOG.error("密码过期时退出云端的登录----请求失败", e2);
                    }
                    return AjaxResult.N(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00204));
                }
            }
            ajax.put("userInfo", user);
            if (!cepLogin.booleanValue()) {
                token = this.hl.i(username, password);
            } else {
                token = this.hl.G(username);
            }
            O(username);
            LoginUser loginUser = this.tokenService.H(token);
            loginUser.setUser(user);
            this.tokenService.c(loginUser);
            ajax.put(Constants.TOKEN, token);
            TesterLoginUtils.onLine();
            TesterLoginUtils.setLoginUserName(username);
            TesterLoginUtils.addLoginUsers(username);
            this.testerThread.getPool().execute(() -> {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                if (TesterLoginUtils.isOnLine()) {
                    try {
                        this.hn.synchronousDsaSeqs(username);
                    } catch (Exception e3) {
                        e3.printStackTrace();
                    }
                }
            });
            this.testerThread.getPool().execute(() -> {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                if (TesterLoginUtils.isOnLine()) {
                    try {
                        this.hn.synchronousSeqs(username);
                    } catch (Exception e3) {
                        e3.printStackTrace();
                    }
                }
            });
            return ajax;
        } catch (Exception e3) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00024), e3);
            throw new BadCredentialsException(e3.getMessage());
        }
    }

    private void O(String username) {
        this.testerThread.getPool().execute(() -> {
            Set<String> vinList = this.manager.getVehicles(username);
            LOG.error("登录后检查用户已连接的车：{}", vinList);
            if (!CollectionUtils.isEmpty(vinList)) {
                try {
                    if (!CollectionUtils.isEmpty(vinList)) {
                        Iterator<String> iterator = vinList.iterator();
                        if (iterator.hasNext()) {
                            this.hr.disconnect(username, iterator.next());
                        }
                    }
                } catch (Exception e) {
                    LOG.error("dicConnectByName方法异常,用户名{},e{}", username, e);
                }
            }
        });
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.springframework.security.authentication.BadCredentialsException */
    @PostMapping({"/cepLogin"})
    public AjaxResult b(HttpServletRequest request, @RequestBody LoginDto loginDto) throws BadCredentialsException {
        this.hp.clearAll();
        AjaxResult ajax = AjaxResult.z();
        List<SecurityCheckResultDTO> securityCheckResult = this.hq.checkSecurityInstallKey(loginDto.getSecuritySoftwarePath());
        loginDto.setSecurityCheckResult(securityCheckResult);
        try {
            Boolean bindCep = loginDto.getBindCep();
            loginDto.getUsername();
            if (bindCep != null && bindCep.booleanValue()) {
                RSAPrivateKey privateKey = (RSAPrivateKey) request.getSession().getAttribute(ConstantEnum.PRIVATEKEY_STR);
                loginDto.setRequestKey(privateKey);
                loginDto.setDisplayName(loginDto.getDisplayName());
            } else {
                String state = loginDto.getState();
                List<String> stateFinal = TesterLoginUtils.getStateFinal();
                if (!StringUtils.isNotBlank(state) || !stateFinal.contains(state)) {
                    throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00267));
                }
                CepUserDTO cepUserDTO = this.hm.getCepLoginName(loginDto.getCode(), loginDto.getRedirectUri());
                String cepLoginName = cepUserDTO.getUsername();
                Boolean bind = cepUserDTO.getBind();
                if (!bind.booleanValue()) {
                    ajax.put(AjaxResult.gA, 3);
                    ajax.put(AjaxResult.gC, cepUserDTO.getDisplayName());
                    return ajax;
                }
                loginDto.setCepLogin(true);
                LOG.info("cepLogin username={}", cepLoginName);
                loginDto.setUsername(cepLoginName);
            }
            LoginInfoDTO loginInfoDTO = this.hm.onlineLogin(loginDto);
            String username = loginInfoDTO.getUserName();
            String msg = loginInfoDTO.getMsg();
            int index = msg.indexOf("TesterSessionId-");
            ajax.put(AjaxResult.gC, loginInfoDTO);
            if (StringUtils.isNotBlank(msg)) {
                if ("mobileAuth".equals(msg)) {
                    ajax.put(AjaxResult.gA, 1);
                    return ajax;
                }
                if ("emailAuth".equals(msg)) {
                    ajax.put(AjaxResult.gA, 2);
                    return ajax;
                }
                if (index != -1) {
                    msg.substring(index);
                } else {
                    throw new BadCredentialsException(msg);
                }
            }
            SysUser user = this.hn.synchronousUsers(username);
            TesterConfigDto config = this.eC.getConfig();
            if (user == null) {
                LOG.error("云端登录成功，但客户端同步用户信息时出错，需要去云端退出登录");
                this.cloud.r(config.getTesterCode(), user.getUserName());
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
            }
            String accountExpired = user.getAccountExpired();
            if (StringUtils.isNotBlank(accountExpired)) {
                Date date = new Date(Long.valueOf(accountExpired).longValue());
                Date now = new Date();
                if (now.after(date)) {
                    try {
                        this.cloud.r(config.getTesterCode(), user.getUserName());
                    } catch (Exception e) {
                        LOG.error("密码过期时退出云端的登录----请求失败", e);
                    }
                    return AjaxResult.N(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00204));
                }
            }
            ajax.put("userInfo", user);
            String token = this.hl.G(username);
            LoginUser loginUser = this.tokenService.H(token);
            loginUser.setUser(user);
            this.tokenService.c(loginUser);
            ajax.put(Constants.TOKEN, token);
            TesterLoginUtils.onLine();
            TesterLoginUtils.setLoginUserName(username);
            TesterLoginUtils.addLoginUsers(username);
            O(username);
            this.testerThread.getPool().execute(() -> {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                if (TesterLoginUtils.isOnLine()) {
                    try {
                        this.hn.synchronousDsaSeqs(username);
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }
            });
            this.testerThread.getPool().execute(() -> {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                if (TesterLoginUtils.isOnLine()) {
                    try {
                        this.hn.synchronousSeqs(username);
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }
            });
            return ajax;
        } catch (Exception e2) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00024), e2);
            throw new BadCredentialsException(e2.getMessage());
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.springframework.security.authentication.BadCredentialsException */
    @PostMapping({"/geelyLogin"})
    public AjaxResult a(@RequestBody LoginDto loginDto) throws BadCredentialsException {
        this.hp.clearAll();
        AjaxResult ajax = AjaxResult.z();
        try {
            loginDto.setGeelySsoLogin(true);
            LoginInfoDTO loginInfoDTO = this.hm.onlineLogin(loginDto);
            String username = loginInfoDTO.getUserName();
            LOG.info("geely 域账号使用用户名为username={}", username);
            String msg = loginInfoDTO.getMsg();
            ajax.put(AjaxResult.gC, loginInfoDTO);
            int index = msg.indexOf("TesterSessionId-");
            if (StringUtils.isNotBlank(msg)) {
                if ("mobileAuth".equals(msg)) {
                    ajax.put(AjaxResult.gA, 1);
                    return ajax;
                }
                if ("emailAuth".equals(msg)) {
                    ajax.put(AjaxResult.gA, 2);
                    return ajax;
                }
                if (index != -1) {
                    msg.substring(index);
                } else {
                    throw new BadCredentialsException(msg);
                }
            }
            SysUser user = this.hn.synchronousUsers(username);
            TesterConfigDto config = this.eC.getConfig();
            if (user == null) {
                LOG.error("云端登录成功，但客户端同步用户信息时出错，需要去云端退出登录");
                this.cloud.r(config.getTesterCode(), user.getUserName());
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
            }
            String accountExpired = user.getAccountExpired();
            if (StringUtils.isNotBlank(accountExpired)) {
                Date date = new Date(Long.valueOf(accountExpired).longValue());
                Date now = new Date();
                if (now.after(date)) {
                    try {
                        this.cloud.r(config.getTesterCode(), user.getUserName());
                    } catch (Exception e) {
                        LOG.error("密码过期时退出云端的登录----请求失败", e);
                    }
                    return AjaxResult.N(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00204));
                }
            }
            ajax.put("userInfo", user);
            String token = this.hl.G(username);
            LoginUser loginUser = this.tokenService.H(token);
            loginUser.setUser(user);
            this.tokenService.c(loginUser);
            ajax.put(Constants.TOKEN, token);
            TesterLoginUtils.onLine();
            TesterLoginUtils.setLoginUserName(username);
            TesterLoginUtils.addLoginUsers(username);
            O(username);
            this.testerThread.getPool().execute(() -> {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                if (TesterLoginUtils.isOnLine()) {
                    try {
                        this.hn.synchronousDsaSeqs(username);
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }
            });
            this.testerThread.getPool().execute(() -> {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                if (TesterLoginUtils.isOnLine()) {
                    try {
                        this.hn.synchronousSeqs(username);
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }
            });
            return ajax;
        } catch (Exception e2) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00024), e2);
            throw new BadCredentialsException(e2.getMessage());
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.springframework.security.authentication.BadCredentialsException */
    @PostMapping({"/offlineLogin"})
    public AjaxResult a(HttpServletRequest request, @RequestBody LoginBody loginBody) throws BadCredentialsException {
        AjaxResult ajax = AjaxResult.z();
        String password = loginBody.getPassword();
        try {
            RSAPrivateKey privateKey = (RSAPrivateKey) request.getSession().getAttribute(ConstantEnum.PRIVATEKEY_STR);
            String password2 = RsaUtils.decryptData(privateKey, password);
            try {
                String username = loginBody.getUsername();
                String token = this.hl.i(username, password2);
                ajax.put(Constants.TOKEN, token);
                SysUser user = this.ho.getUserByUserName(username);
                String accountExpired = user.getAccountExpired();
                if (StringUtils.isNotBlank(accountExpired)) {
                    Date date = new Date(Long.valueOf(accountExpired).longValue());
                    Date now = new Date();
                    if (now.after(date)) {
                        return AjaxResult.N(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00204));
                    }
                }
                String permission = user.getPermission();
                JSONObject jsonObject = JSONObject.parseObject(permission);
                LOG.info("离线登录用户权限：{}", permission);
                Integer offline = jsonObject.getInteger("offline");
                if (Integer.valueOf("1").equals(offline)) {
                    ajax.put("userInfo", user);
                    TesterLoginUtils.offLine();
                } else {
                    ajax = AjaxResult.N(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00167));
                }
                Integer offlineLoginValidity = user.getOfflineLoginValidity();
                String lastOnlineLoginTime = user.getLastOnlineLoginTime();
                SimpleDateFormat format = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
                Date parse = format.parse(lastOnlineLoginTime);
                Calendar rightNow = Calendar.getInstance();
                rightNow.setTime(parse);
                rightNow.add(6, offlineLoginValidity.intValue());
                Date offlineLoginValidityDate = rightNow.getTime();
                LOG.info("离线登录-上次在线登录日期：{}，离线登录有效天数：{}，有效期至：{}", new Object[]{lastOnlineLoginTime, offlineLoginValidity, format.format(offlineLoginValidityDate)});
                System.out.println("离线登录-上次在线登录日期：" + lastOnlineLoginTime + " ，离线登录有效天数：" + offlineLoginValidity + "，有效期至：" + format.format(offlineLoginValidityDate));
                boolean before = offlineLoginValidityDate.before(new Date());
                if (before) {
                    ajax = AjaxResult.N(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00254));
                }
                O(username);
                return ajax;
            } catch (Exception e) {
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
            }
        } catch (Exception e2) {
            throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00104));
        }
    }

    @GetMapping({"getInfo"})
    public AjaxResult getInfo() {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        Set<String> roles = this.permissionService.a(user);
        Set<String> permissions = this.permissionService.b(user);
        AjaxResult ajax = AjaxResult.z();
        TesterConfigDto config = this.eC.getConfig();
        user.setVbfPath(config.getVbfPath());
        user.setVbfDefaultSize(config.getVbfDefaultSize());
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    @GetMapping({"getRouters"})
    public AjaxResult getRouters() {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        List<SysMenu> menus = this.gv.selectMenuTreeByUserId(user.getUserId());
        return AjaxResult.d(this.gv.buildMenus(menus));
    }

    @PostMapping({"sendMobileCode"})
    public AjaxResult c(HttpServletRequest request, @RequestBody LoginDto loginDto) {
        LOG.info("------------------------执行获取验证码接口-------------------------------");
        String password = loginDto.getPassword();
        Boolean cepLogin = loginDto.getCepLogin();
        Boolean geelySsoLogin = loginDto.getGeelySsoLogin();
        String thirdPartyCode = loginDto.getThirdPartyCode();
        if (cepLogin == null) {
            cepLogin = false;
        }
        if (geelySsoLogin == null) {
            geelySsoLogin = false;
        }
        if (cepLogin.booleanValue() || geelySsoLogin.booleanValue()) {
            if (StringUtils.isEmpty(thirdPartyCode)) {
                return AjaxResult.N("第三方认证缺少临时code");
            }
            LOG.info(loginDto.getUsername() + " 使用三方系统认证登录，cepLogin={}，geelySsoLogin={}", cepLogin, geelySsoLogin);
        } else {
            try {
                RSAPrivateKey privateKey = (RSAPrivateKey) request.getSession().getAttribute(ConstantEnum.PRIVATEKEY_STR);
                password = RsaUtils.decryptData(privateKey, password);
            } catch (Exception e) {
                return AjaxResult.N(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
            }
        }
        try {
            String errorCode = this.hm.sendMobileCode(loginDto.getUsername(), password, loginDto.getSendCodeType(), cepLogin, geelySsoLogin, thirdPartyCode);
            if (StringUtils.isNotBlank(errorCode)) {
                return AjaxResult.N(errorCode);
            }
            return AjaxResult.z();
        } catch (Exception e2) {
            return AjaxResult.N("验证码发送失败");
        }
    }

    @GetMapping({"getTenantInfo"})
    public AjaxResult getTenantInfo() throws Exception {
        LOG.info("------------------------执行获取租户信息接口-------------------------------");
        SysTenantDto tenantDto = this.hm.getTenantCodeByCode();
        return AjaxResult.d(tenantDto);
    }
}
