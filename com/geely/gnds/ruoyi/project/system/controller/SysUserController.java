package com.geely.gnds.ruoyi.project.system.controller;

import com.geely.gnds.ruoyi.common.utils.SecurityUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.aspectj.lang.annotation.Log;
import com.geely.gnds.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.web.controller.BaseController;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.ruoyi.framework.web.page.TableDataInfo;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.service.ISysRoleService;
import com.geely.gnds.ruoyi.project.system.service.ISysUserService;
import com.geely.gnds.tester.service.LoginService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/system/user"})
@RestController
/* loaded from: SysUserController.class */
public class SysUserController extends BaseController {

    @Autowired
    private ISysUserService ho;

    @Autowired
    private ISysRoleService gu;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private LoginService hs;

    @GetMapping({"/list"})
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    public TableDataInfo c(SysUser user) {
        y();
        List<SysUser> list = this.ho.selectUserList(user);
        return h(list);
    }

    @GetMapping({"/", "/{userId}"})
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    public AjaxResult a(@PathVariable(value = "userId", required = false) Long userId) {
        AjaxResult ajax = AjaxResult.z();
        ajax.put("roles", this.gu.selectRoleAll());
        if (StringUtils.isNotNull(userId)) {
            ajax.put(AjaxResult.gC, this.ho.selectUserById(userId));
            ajax.put("roleIds", this.gu.selectRoleListByUserId(userId));
        }
        return ajax;
    }

    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    public AjaxResult d(@Validated @RequestBody SysUser user) {
        if ("1".equals(this.ho.checkUserNameUnique(user.getUserName()))) {
            return AjaxResult.N("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        if ("1".equals(this.ho.checkPhoneUnique(user))) {
            return AjaxResult.N("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if ("1".equals(this.ho.checkEmailUnique(user))) {
            return AjaxResult.N("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return b(this.ho.insertUser(user));
    }

    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    public AjaxResult e(@Validated @RequestBody SysUser user) {
        this.ho.checkUserAllowed(user);
        if ("1".equals(this.ho.checkPhoneUnique(user))) {
            return AjaxResult.N("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if ("1".equals(this.ho.checkEmailUnique(user))) {
            return AjaxResult.N("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        return b(this.ho.updateUser(user));
    }

    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping({"/{userIds}"})
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    public AjaxResult a(@PathVariable Long[] userIds) {
        return b(this.ho.deleteUserByIds(userIds));
    }

    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping({"/resetPwd"})
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    public AjaxResult f(@RequestBody SysUser user) {
        this.ho.checkUserAllowed(user);
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        return b(this.ho.resetPwd(user));
    }

    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping({"/changeStatus"})
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    public AjaxResult g(@RequestBody SysUser user) {
        this.ho.checkUserAllowed(user);
        user.setUpdateBy(SecurityUtils.getUsername());
        return b(this.ho.updateUserStatus(user));
    }

    @PostMapping({"updateUserStatus"})
    public Result<Object> h(@RequestBody SysUser sysUser) throws Exception {
        this.hs.updateUserStatus(sysUser);
        return new Result().ok("");
    }
}
