package com.geely.gnds.ruoyi.project.system.controller;

import com.geely.gnds.ruoyi.common.utils.SecurityUtils;
import com.geely.gnds.ruoyi.framework.aspectj.lang.annotation.Log;
import com.geely.gnds.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.geely.gnds.ruoyi.framework.web.controller.BaseController;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.ruoyi.framework.web.page.TableDataInfo;
import com.geely.gnds.ruoyi.project.system.domain.SysRole;
import com.geely.gnds.ruoyi.project.system.service.ISysRoleService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/system/role"})
@RestController
/* loaded from: SysRoleController.class */
public class SysRoleController extends BaseController {

    @Autowired
    private ISysRoleService gu;

    @GetMapping({"/list"})
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    public TableDataInfo a(SysRole role) {
        y();
        List<SysRole> list = this.gu.selectRoleList(role);
        return h(list);
    }

    @GetMapping({"/{roleId}"})
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    public AjaxResult a(@PathVariable Long roleId) {
        return AjaxResult.d(this.gu.selectRoleById(roleId));
    }

    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    @PreAuthorize("@ss.hasPermi('system:role:add')")
    public AjaxResult b(@Validated @RequestBody SysRole role) {
        if ("1".equals(this.gu.checkRoleNameUnique(role))) {
            return AjaxResult.N("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        if ("1".equals(this.gu.checkRoleKeyUnique(role))) {
            return AjaxResult.N("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        role.setCreateBy(SecurityUtils.getUsername());
        return b(this.gu.insertRole(role));
    }

    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    public AjaxResult c(@Validated @RequestBody SysRole role) {
        this.gu.checkRoleAllowed(role);
        if ("1".equals(this.gu.checkRoleNameUnique(role))) {
            return AjaxResult.N("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        }
        if ("1".equals(this.gu.checkRoleKeyUnique(role))) {
            return AjaxResult.N("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        role.setUpdateBy(SecurityUtils.getUsername());
        return b(this.gu.updateRole(role));
    }

    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping({"/dataScope"})
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    public AjaxResult d(@RequestBody SysRole role) {
        this.gu.checkRoleAllowed(role);
        return b(this.gu.authDataScope(role));
    }

    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping({"/changeStatus"})
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    public AjaxResult e(@RequestBody SysRole role) {
        this.gu.checkRoleAllowed(role);
        role.setUpdateBy(SecurityUtils.getUsername());
        return b(this.gu.updateRoleStatus(role));
    }

    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping({"/{roleIds}"})
    @PreAuthorize("@ss.hasPermi('system:role:remove')")
    public AjaxResult a(@PathVariable Long[] roleIds) {
        return b(this.gu.deleteRoleByIds(roleIds));
    }

    @GetMapping({"/optionselect"})
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    public AjaxResult F() {
        return AjaxResult.d(this.gu.selectRoleAll());
    }
}
