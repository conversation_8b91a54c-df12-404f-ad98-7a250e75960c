package com.geely.gnds.ruoyi.project.system.controller;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.SysLoginService;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.service.ISysUserService;
import com.geely.gnds.tester.common.CacheUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.LoginDto;
import com.geely.gnds.tester.dto.LoginInfoDTO;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ConnectedService;
import com.geely.gnds.tester.service.LoginService;
import com.geely.gnds.tester.service.SynchronousService;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.util.Date;
import java.util.Iterator;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/oauth"})
@RestController
/* loaded from: SysOauth2Controller.class */
public class SysOauth2Controller {

    @Autowired
    private SysLoginService hl;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private LoginService hm;

    @Autowired
    private SynchronousService hn;

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private ISysUserService ho;

    @Autowired
    private CacheUtils hp;

    @Autowired
    private TesterConfigDao eC;

    @Autowired
    private Cloud cloud;

    @Autowired
    private ConnectedService hr;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger LOG = LoggerFactory.getLogger(SysOauth2Controller.class);

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.springframework.security.authentication.BadCredentialsException */
    @PostMapping({"/login"})
    public AjaxResult b(@RequestBody LoginDto loginDto) throws BadCredentialsException {
        this.hp.clearAll();
        AjaxResult ajax = AjaxResult.z();
        try {
            loginDto.setGeelySsoLogin(false);
            LoginInfoDTO loginInfoDTO = this.hm.onlineOauth2Login(loginDto);
            String username = loginInfoDTO.getUserName();
            LOG.info("账号使用用户名为username={}", username);
            String msg = loginInfoDTO.getMsg();
            ajax.put(AjaxResult.gC, loginInfoDTO);
            int index = msg.indexOf("TesterSessionId-");
            if (StringUtils.isNotBlank(msg)) {
                if ("mobileAuth".equals(msg)) {
                    ajax.put(AjaxResult.gA, 1);
                    return ajax;
                }
                if ("emailAuth".equals(msg)) {
                    ajax.put(AjaxResult.gA, 2);
                    return ajax;
                }
                if (index != -1) {
                    msg.substring(index);
                } else {
                    throw new BadCredentialsException(msg);
                }
            }
            SysUser user = this.hn.synchronousUsers(username);
            TesterConfigDto config = this.eC.getConfig();
            if (user == null) {
                LOG.error("云端登录成功，但客户端同步用户信息时出错，需要去云端退出登录");
                this.cloud.r(config.getTesterCode(), user.getUserName());
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
            }
            String accountExpired = user.getAccountExpired();
            if (StringUtils.isNotBlank(accountExpired)) {
                Date date = new Date(Long.valueOf(accountExpired).longValue());
                Date now = new Date();
                if (now.after(date)) {
                    try {
                        this.cloud.r(config.getTesterCode(), user.getUserName());
                    } catch (Exception e) {
                        LOG.error("密码过期时退出云端的登录----请求失败", e);
                    }
                    return AjaxResult.N(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00204));
                }
            }
            ajax.put("userInfo", user);
            String token = this.hl.G(username);
            LoginUser loginUser = this.tokenService.H(token);
            loginUser.setUser(user);
            this.tokenService.c(loginUser);
            ajax.put(Constants.TOKEN, token);
            TesterLoginUtils.onLine();
            TesterLoginUtils.setLoginUserName(username);
            TesterLoginUtils.addLoginUsers(username);
            O(username);
            this.testerThread.getPool().execute(() -> {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                if (TesterLoginUtils.isOnLine()) {
                    try {
                        this.hn.synchronousDsaSeqs(username);
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }
            });
            this.testerThread.getPool().execute(() -> {
                ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                if (TesterLoginUtils.isOnLine()) {
                    try {
                        this.hn.synchronousSeqs(username);
                    } catch (Exception e2) {
                        e2.printStackTrace();
                    }
                }
            });
            return ajax;
        } catch (Exception e2) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00024), e2);
            throw new BadCredentialsException(e2.getMessage());
        }
    }

    private void O(String username) {
        this.testerThread.getPool().execute(() -> {
            Set<String> vinList = this.manager.getVehicles(username);
            LOG.error("登录后检查用户已连接的车：{}", vinList);
            if (!CollectionUtils.isEmpty(vinList)) {
                try {
                    if (!CollectionUtils.isEmpty(vinList)) {
                        Iterator<String> iterator = vinList.iterator();
                        if (iterator.hasNext()) {
                            this.hr.disconnect(username, iterator.next());
                        }
                    }
                } catch (Exception e) {
                    LOG.error("dicConnectByName方法异常,用户名{},e{}", username, e);
                }
            }
        });
    }
}
