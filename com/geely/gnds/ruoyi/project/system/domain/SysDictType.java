package com.geely.gnds.ruoyi.project.system.domain;

import com.geely.gnds.ruoyi.common.utils.file.FileUploadUtils;
import com.geely.gnds.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.geely.gnds.ruoyi.framework.web.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/* loaded from: SysDictType.class */
public class SysDictType extends BaseEntity {
    private static final long serialVersionUID = 1;

    @Excel(name = "字典主键", cellType = Excel.ColumnType.NUMERIC)
    private Long dictId;

    @Excel(name = "字典名称")
    private String dictName;

    @Excel(name = "字典类型")
    private String dictType;

    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public Long getDictId() {
        return this.dictId;
    }

    public void setDictId(Long dictId) {
        this.dictId = dictId;
    }

    @NotBlank(message = "字典名称不能为空")
    @Size(min = 0, max = FileUploadUtils.DEFAULT_FILE_NAME_LENGTH, message = "字典类型名称长度不能超过100个字符")
    public String getDictName() {
        return this.dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    @NotBlank(message = "字典类型不能为空")
    @Size(min = 0, max = FileUploadUtils.DEFAULT_FILE_NAME_LENGTH, message = "字典类型类型长度不能超过100个字符")
    public String getDictType() {
        return this.dictType;
    }

    public void setDictType(String dictType) {
        this.dictType = dictType;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("dictId", getDictId()).append("dictName", getDictName()).append("dictType", getDictType()).append("status", getStatus()).append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy()).append("updateTime", getUpdateTime()).append("remark", getRemark()).toString();
    }
}
