package com.geely.gnds.ruoyi.project.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.geely.gnds.ruoyi.framework.aspectj.lang.annotation.Excels;
import com.geely.gnds.ruoyi.framework.web.domain.BaseEntity;
import com.sun.jna.Platform;
import java.util.List;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: SysUser.class */
public class SysUser extends BaseEntity {
    private static final long serialVersionUID = 1;

    @Excel(name = "用户序号", cellType = Excel.ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;

    @Excel(name = "部门编号", type = Excel.Type.IMPORT)
    private Long deptId;

    @Excel(name = "登录名称")
    private String userName;

    @Excel(name = "用户名称")
    private String nickName;

    @Excel(name = "用户邮箱")
    private String email;

    @Excel(name = "手机号码")
    private String phonenumber;

    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;
    private String avatar;

    @JsonIgnore
    private String password;
    private String salt;

    @Excel(name = "帐号状态", readConverterExp = "0=停用,1=正常")
    private String status;
    private String delFlag;

    @Excel(name = "最后登陆IP", type = Excel.Type.EXPORT)
    private String loginIp;

    @Excel(name = "最后登陆时间", width = 30.0d, dateFormat = DateUtils.YYYY_MM_DD_HH_MM_SS, type = Excel.Type.EXPORT)
    private String loginDate;

    @Excels({@Excel(name = "部门名称", targetAttr = "deptName", type = Excel.Type.EXPORT), @Excel(name = "部门负责人", targetAttr = "leader", type = Excel.Type.EXPORT)})
    private SysDept dept;
    private List<SysRole> roles;
    private Long[] roleIds;
    private Long[] postIds;
    private Integer seqStatus;
    private Integer softwareStatus;
    private Integer passwordExpired;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private String accountExpired;
    private Long brandSelect;
    private Integer timeSelect;
    private Integer readNum;
    private Integer vinDecode;
    private Integer recentVehicles;
    private String startTab;
    private String permission;
    private String lang;
    private String vbfPath;
    private int vbfDefaultSize;
    private String serviceStationCode;
    private String serviceStationName;
    private String serviceStationLargeArea;
    private String serviceStationArea;
    private String serviceStationProvince;
    private String serviceStationCity;
    private Integer offlineLoginValidity;
    private String lastOnlineLoginTime;
    private Integer paramShowType;

    public SysUser() {
    }

    public SysUser(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public boolean isAdmin() {
        return isAdmin(this.userId);
    }

    public static boolean isAdmin(Long userId) {
        return userId != null && serialVersionUID == userId.longValue();
    }

    public Long getDeptId() {
        return this.deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    public String getNickName() {
        return this.nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Size(min = 0, max = Platform.NETBSD, message = "手机号码长度不能超过11个字符")
    public String getPhonenumber() {
        return this.phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getAvatar() {
        return this.avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    @JsonIgnore
    @JsonProperty
    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSalt() {
        return this.salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return this.delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getLoginIp() {
        return this.loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getLoginDate() {
        return this.loginDate;
    }

    public void setLoginDate(String loginDate) {
        this.loginDate = loginDate;
    }

    public SysDept getDept() {
        return this.dept;
    }

    public void setDept(SysDept dept) {
        this.dept = dept;
    }

    public List<SysRole> getRoles() {
        return this.roles;
    }

    public void setRoles(List<SysRole> roles) {
        this.roles = roles;
    }

    public Long[] getRoleIds() {
        return this.roleIds;
    }

    public void setRoleIds(Long[] roleIds) {
        this.roleIds = roleIds;
    }

    public Long[] getPostIds() {
        return this.postIds;
    }

    public void setPostIds(Long[] postIds) {
        this.postIds = postIds;
    }

    public Integer getSeqStatus() {
        return this.seqStatus;
    }

    public void setSeqStatus(Integer seqStatus) {
        this.seqStatus = seqStatus;
    }

    public Integer getSoftwareStatus() {
        return this.softwareStatus;
    }

    public void setSoftwareStatus(Integer softwareStatus) {
        this.softwareStatus = softwareStatus;
    }

    public Long getBrandSelect() {
        return this.brandSelect;
    }

    public void setBrandSelect(Long brandSelect) {
        this.brandSelect = brandSelect;
    }

    public Integer getTimeSelect() {
        return this.timeSelect;
    }

    public void setTimeSelect(Integer timeSelect) {
        this.timeSelect = timeSelect;
    }

    public Integer getReadNum() {
        return this.readNum;
    }

    public void setReadNum(Integer readNum) {
        this.readNum = readNum;
    }

    public Integer getVinDecode() {
        return this.vinDecode;
    }

    public void setVinDecode(Integer vinDecode) {
        this.vinDecode = vinDecode;
    }

    public Integer getRecentVehicles() {
        return this.recentVehicles;
    }

    public void setRecentVehicles(Integer recentVehicles) {
        this.recentVehicles = recentVehicles;
    }

    public String getStartTab() {
        return this.startTab;
    }

    public void setStartTab(String startTab) {
        this.startTab = startTab;
    }

    public Integer getPasswordExpired() {
        return this.passwordExpired;
    }

    public void setPasswordExpired(Integer passwordExpired) {
        this.passwordExpired = passwordExpired;
    }

    public String getAccountExpired() {
        return this.accountExpired;
    }

    public void setAccountExpired(String accountExpired) {
        this.accountExpired = accountExpired;
    }

    public String getPermission() {
        return this.permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getLang() {
        return this.lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getVbfPath() {
        return this.vbfPath;
    }

    public void setVbfPath(String vbfPath) {
        this.vbfPath = vbfPath;
    }

    public int getVbfDefaultSize() {
        return this.vbfDefaultSize;
    }

    public void setVbfDefaultSize(int vbfDefaultSize) {
        this.vbfDefaultSize = vbfDefaultSize;
    }

    public String getServiceStationCode() {
        return this.serviceStationCode;
    }

    public void setServiceStationCode(String serviceStationCode) {
        this.serviceStationCode = serviceStationCode;
    }

    public String getServiceStationName() {
        return this.serviceStationName;
    }

    public void setServiceStationName(String serviceStationName) {
        this.serviceStationName = serviceStationName;
    }

    public String getServiceStationLargeArea() {
        return this.serviceStationLargeArea;
    }

    public void setServiceStationLargeArea(String serviceStationLargeArea) {
        this.serviceStationLargeArea = serviceStationLargeArea;
    }

    public String getServiceStationArea() {
        return this.serviceStationArea;
    }

    public void setServiceStationArea(String serviceStationArea) {
        this.serviceStationArea = serviceStationArea;
    }

    public String getServiceStationProvince() {
        return this.serviceStationProvince;
    }

    public void setServiceStationProvince(String serviceStationProvince) {
        this.serviceStationProvince = serviceStationProvince;
    }

    public String getServiceStationCity() {
        return this.serviceStationCity;
    }

    public void setServiceStationCity(String serviceStationCity) {
        this.serviceStationCity = serviceStationCity;
    }

    public Integer getOfflineLoginValidity() {
        return this.offlineLoginValidity;
    }

    public void setOfflineLoginValidity(Integer offlineLoginValidity) {
        this.offlineLoginValidity = offlineLoginValidity;
    }

    public String getLastOnlineLoginTime() {
        return this.lastOnlineLoginTime;
    }

    public void setLastOnlineLoginTime(String lastOnlineLoginTime) {
        this.lastOnlineLoginTime = lastOnlineLoginTime;
    }

    public Integer getParamShowType() {
        return this.paramShowType;
    }

    public void setParamShowType(Integer paramShowType) {
        this.paramShowType = paramShowType;
    }

    public String toString() {
        return new ToStringBuilder(this).append("userId", this.userId).append("deptId", this.deptId).append("userName", this.userName).append("nickName", this.nickName).append("email", this.email).append("phonenumber", this.phonenumber).append("sex", this.sex).append(Constants.JWT_AVATAR, this.avatar).append("password", this.password).append("salt", this.salt).append("status", this.status).append("delFlag", this.delFlag).append("loginIp", this.loginIp).append("loginDate", this.loginDate).append("dept", this.dept).append("roles", this.roles).append("roleIds", this.roleIds).append("postIds", this.postIds).append("seqStatus", this.seqStatus).append("softwareStatus", this.softwareStatus).append("passwordExpired", this.passwordExpired).append("accountExpired", this.accountExpired).append("brandSelect", this.brandSelect).append("timeSelect", this.timeSelect).append("readNum", this.readNum).append("vinDecode", this.vinDecode).append("recentVehicles", this.recentVehicles).append("startTab", this.startTab).append("permission", this.permission).append("lang", this.lang).append("vbfPath", this.vbfPath).append("vbfDefaultSize", this.vbfDefaultSize).append("serviceStationCode", this.serviceStationCode).append("serviceStationName", this.serviceStationName).append("serviceStationLargeArea", this.serviceStationLargeArea).append("serviceStationArea", this.serviceStationArea).append("serviceStationProvince", this.serviceStationProvince).append("serviceStationCity", this.serviceStationCity).append("offlineLoginValidity", this.offlineLoginValidity).append("lastOnlineLoginTime", this.lastOnlineLoginTime).append("paramShowType", this.paramShowType).toString();
    }
}
