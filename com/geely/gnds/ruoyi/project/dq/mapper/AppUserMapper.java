package com.geely.gnds.ruoyi.project.dq.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;

@Mapper
/* loaded from: AppUserMapper.class */
public interface AppUserMapper {
    Map<String, Object> get(Long l);

    Map<String, Object> getByUsername(String str);

    List<Map<String, Object>> list(Map<String, Object> map);

    int count(Map<String, Object> map);

    int remove(Long l);

    int batchRemove(Long[] lArr);
}
