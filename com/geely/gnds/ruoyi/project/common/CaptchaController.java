package com.geely.gnds.ruoyi.project.common;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.IdUtils;
import com.geely.gnds.ruoyi.common.utils.VerifyCodeUtils;
import com.geely.gnds.ruoyi.common.utils.sign.Base64;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
/* loaded from: CaptchaController.class */
public class CaptchaController {

    @Autowired
    private EhcacheClient ehcacheClient;

    @GetMapping({"/captchaImage"})
    public AjaxResult getCode(HttpServletResponse response) throws IOException {
        String verifyCode = VerifyCodeUtils.generateVerifyCode(4);
        String uuid = IdUtils.simpleUuid();
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        this.ehcacheClient.a(verifyKey, Constants.CAPTCHA_EXPIRATION.intValue() * 60, verifyCode);
        try {
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            Throwable th = null;
            try {
                VerifyCodeUtils.outputImage(111, 36, stream, verifyCode);
                AjaxResult ajax = AjaxResult.z();
                ajax.put("uuid", uuid);
                ajax.put("img", Base64.encode(stream.toByteArray()));
                if (stream != null) {
                    if (0 != 0) {
                        try {
                            stream.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        stream.close();
                    }
                }
                return ajax;
            } finally {
            }
        } catch (Exception e) {
            return AjaxResult.N(e.getMessage());
        }
    }
}
