package com.geely.gnds.ruoyi.framework.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.core.annotation.Order;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Order(Integer.MIN_VALUE)
/* loaded from: RequestLimit.class */
public @interface RequestLimit {
    int count() default Integer.MAX_VALUE;

    long time() default 60000;
}
