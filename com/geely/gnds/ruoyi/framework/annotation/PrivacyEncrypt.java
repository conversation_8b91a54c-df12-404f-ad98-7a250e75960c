package com.geely.gnds.ruoyi.framework.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.geely.gnds.ruoyi.common.enums.PrivacyTypeEnum;
import com.geely.gnds.ruoyi.framework.config.PrivacySerializer;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@JsonSerialize(using = PrivacySerializer.class)
@JacksonAnnotationsInside
@Retention(RetentionPolicy.RUNTIME)
/* loaded from: PrivacyEncrypt.class */
public @interface PrivacyEncrypt {
    PrivacyTypeEnum type();

    int prefixNoMaskLen() default 1;

    int suffixNoMaskLen() default 1;

    String symbol() default "*";
}
