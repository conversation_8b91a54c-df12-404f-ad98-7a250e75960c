package com.geely.gnds.ruoyi.framework.security.data;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.Collection;
import java.util.Map;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/* loaded from: WebUserDetails.class */
public class WebUserDetails implements UserDetails {
    private Map<String, Object> gm;

    public WebUserDetails(Map<String, Object> user) {
        this.gm = user;
    }

    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    public String getPassword() {
        return this.gm.get("password").toString();
    }

    public String getUsername() {
        return this.gm.get(ConstantEnum.USERNAME_STR).toString();
    }

    public boolean isAccountNonExpired() {
        return false;
    }

    public boolean isAccountNonLocked() {
        return false;
    }

    public boolean isCredentialsNonExpired() {
        return false;
    }

    public boolean isEnabled() {
        return false;
    }

    public Map<String, Object> getUser() {
        return this.gm;
    }

    public void setUser(Map<String, Object> user) {
        this.gm = user;
    }
}
