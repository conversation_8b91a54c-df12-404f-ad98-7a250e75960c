package com.geely.gnds.ruoyi.framework.security.data;

import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import java.util.Arrays;
import java.util.Collection;
import java.util.Set;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/* loaded from: AdminUserDetails.class */
public class AdminUserDetails implements UserDetails {
    private SysUser gj;
    private Set<String> gl;

    public AdminUserDetails(SysUser user, Set<String> permissionList) {
        this.gj = user;
        this.gl = permissionList;
    }

    public Collection<? extends GrantedAuthority> getAuthorities() {
        SimpleGrantedAuthority simp = new SimpleGrantedAuthority(getPermissionSet().toString());
        return Arrays.asList(simp);
    }

    public String getPassword() {
        return this.gj.getPassword();
    }

    public String getUsername() {
        return this.gj.getUserName();
    }

    public boolean isAccountNonExpired() {
        return true;
    }

    public boolean isAccountNonLocked() {
        return true;
    }

    public boolean isCredentialsNonExpired() {
        return true;
    }

    public boolean isEnabled() {
        return !"0".equals(this.gj.getDelFlag());
    }

    public SysUser getUser() {
        return this.gj;
    }

    public void setUser(SysUser user) {
        this.gj = user;
    }

    public Set<String> getPermissionSet() {
        return this.gl;
    }

    public void setPermissionSet(Set<String> permissionSet) {
        this.gl = permissionSet;
    }
}
