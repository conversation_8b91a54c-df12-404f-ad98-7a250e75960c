package com.geely.gnds.ruoyi.framework.security.filter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

@WebFilter(filterName = "shutdownFilter", urlPatterns = {"/actuator/*"})
/* loaded from: ShutdownFilter.class */
public class ShutdownFilter implements Filter {
    private static final Logger log = LoggerFactory.getLogger(ShutdownFilter.class);

    @Value("${tester.shutdown.whitelist}")
    private String[] shutdownIpWhitelist;

    public void destroy() {
    }

    public void doFilter(ServletRequest srequest, ServletResponse sresponse, FilterChain filterChain) throws ServletException, IOException {
        HttpServletRequest request = (HttpServletRequest) srequest;
        String ip = b(request);
        log.info("访问shutdown的机器的原始IP：{}", ip);
        if (!z(ip)) {
            sresponse.setContentType("application/json");
            sresponse.setCharacterEncoding("UTF-8");
            PrintWriter writer = sresponse.getWriter();
            writer.write("{\"code\":401}");
            writer.flush();
            writer.close();
            return;
        }
        filterChain.doFilter(srequest, sresponse);
    }

    public void init(FilterConfig arg0) throws ServletException {
        log.info("shutdown filter is init.....");
    }

    private boolean z(String ip) {
        List<String> list = Arrays.asList(this.shutdownIpWhitelist);
        if (list.contains(ip)) {
            return true;
        }
        return list.stream().anyMatch(data -> {
            return ip.startsWith(data);
        });
    }

    private String b(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
