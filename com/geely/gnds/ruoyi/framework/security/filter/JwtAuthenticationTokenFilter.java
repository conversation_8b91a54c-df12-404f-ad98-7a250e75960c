package com.geely.gnds.ruoyi.framework.security.filter;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.SecurityUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.LoginWebUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.security.service.TokenWebService;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.exception.TokenInvalidException;
import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Component
/* loaded from: JwtAuthenticationTokenFilter.class */
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private TokenWebService gn;

    @Value("${token.header}")
    private String tokenHeader;

    @Value("${token.headerPortal}")
    private String tokenHeadPortal;

    @Value("${token.headerAdmin}")
    private String tokenHeadAdmin;

    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        String authHeader = request.getHeader(this.tokenHeader);
        response.setCharacterEncoding("UTF-8");
        if (authHeader != null) {
            if (authHeader.startsWith(Constants.TOKEN_PREFIX + this.tokenHeadAdmin) || authHeader.startsWith(this.tokenHeadAdmin)) {
                try {
                    LoginUser loginUser = this.tokenService.c(request);
                    if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
                        this.tokenService.b(loginUser);
                        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, (Object) null, loginUser.getAuthorities());
                        authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                    }
                } catch (TokenInvalidException e) {
                    AjaxResult rs = AjaxResult.a(HttpStatus.UNAUTHORIZED, "登录已失效，请重新登录");
                    ServletUtil.write(response, JSONUtil.toJsonStr(rs), "application/json");
                    return;
                }
            } else if (authHeader.startsWith(Constants.TOKEN_PREFIX + this.tokenHeadPortal) || authHeader.startsWith(this.tokenHeadPortal)) {
                try {
                    LoginWebUser loginWebUser = this.gn.e(request);
                    if (StringUtils.isNotNull(loginWebUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
                        this.gn.b(loginWebUser);
                        UsernamePasswordAuthenticationToken authenticationToken2 = new UsernamePasswordAuthenticationToken(loginWebUser, (Object) null, loginWebUser.getAuthorities());
                        authenticationToken2.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authenticationToken2);
                    }
                } catch (TokenInvalidException e2) {
                    AjaxResult rs2 = AjaxResult.a(HttpStatus.UNAUTHORIZED, "登录已失效，请重新登录");
                    ServletUtil.write(response, JSONUtil.toJsonStr(rs2), "application/json");
                    return;
                }
            }
        }
        chain.doFilter(request, response);
    }
}
