package com.geely.gnds.ruoyi.framework.security;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import java.util.Collection;
import java.util.Set;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/* loaded from: LoginUser.class */
public class LoginUser implements UserDetails {
    private static final long serialVersionUID = 1;
    private String gc;
    private Long gd;
    private Long expireTime;
    private String ge;
    private String gf;
    private String gg;
    private String gh;
    private Set<String> gi;
    private SysUser gj;

    public String getToken() {
        return this.gc;
    }

    public void setToken(String token) {
        this.gc = token;
    }

    public LoginUser() {
    }

    public LoginUser(SysUser user, Set<String> permissions) {
        this.gj = user;
        this.gi = permissions;
    }

    @JsonIgnore
    public String getPassword() {
        return this.gj.getPassword();
    }

    public String getUsername() {
        return this.gj.getUserName();
    }

    @JsonIgnore
    public boolean isAccountNonExpired() {
        return true;
    }

    @JsonIgnore
    public boolean isAccountNonLocked() {
        return true;
    }

    @JsonIgnore
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @JsonIgnore
    public boolean isEnabled() {
        return true;
    }

    public Long getLoginTime() {
        return this.gd;
    }

    public void setLoginTime(Long loginTime) {
        this.gd = loginTime;
    }

    public String getIpaddr() {
        return this.ge;
    }

    public void setIpaddr(String ipaddr) {
        this.ge = ipaddr;
    }

    public String getLoginLocation() {
        return this.gf;
    }

    public void setLoginLocation(String loginLocation) {
        this.gf = loginLocation;
    }

    public String getBrowser() {
        return this.gg;
    }

    public void setBrowser(String browser) {
        this.gg = browser;
    }

    public String getOs() {
        return this.gh;
    }

    public void setOs(String os) {
        this.gh = os;
    }

    public Long getExpireTime() {
        return this.expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public Set<String> getPermissions() {
        return this.gi;
    }

    public void setPermissions(Set<String> permissions) {
        this.gi = permissions;
    }

    public SysUser getUser() {
        return this.gj;
    }

    public void setUser(SysUser user) {
        this.gj = user;
    }

    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }
}
