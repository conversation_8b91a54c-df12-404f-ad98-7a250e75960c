package com.geely.gnds.ruoyi.framework.security.handle;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

@Component
/* loaded from: ForbiddenEntryPointImpl.class */
public class ForbiddenEntryPointImpl implements AccessDeniedHandler {
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException arg2) throws ServletException, IOException {
        String msg = StringUtils.format("请求访问：{}，访问受限，授权过期", request.getRequestURI());
        ServletUtils.renderString(response, ObjectMapperUtils.obj2JsonStr(AjaxResult.a(HttpStatus.FORBIDDEN, msg)));
    }
}
