package com.geely.gnds.ruoyi.framework.security.handle;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.io.IOException;
import java.io.Serializable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

@Component
/* loaded from: AuthenticationEntryPointImpl.class */
public class AuthenticationEntryPointImpl implements Serializable, AuthenticationEntryPoint {
    private static final long serialVersionUID = -8970718410437077606L;

    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) throws IOException {
        String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
        ServletUtils.renderString(response, ObjectMapperUtils.obj2JsonStr(AjaxResult.a(HttpStatus.UNAUTHORIZED, msg)));
    }
}
