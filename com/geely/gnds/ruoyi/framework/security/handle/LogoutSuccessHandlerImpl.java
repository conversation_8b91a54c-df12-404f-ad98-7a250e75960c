package com.geely.gnds.ruoyi.framework.security.handle;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.io.IOException;
import java.util.concurrent.Executor;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

@Configuration
/* loaded from: LogoutSuccessHandlerImpl.class */
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private Executor executor;
    private static final Logger LOG = LoggerFactory.getLogger(LogoutSuccessHandlerImpl.class);

    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws ServletException, IOException {
        LoginUser loginUser = null;
        try {
            loginUser = this.tokenService.c(request);
            if (loginUser != null) {
                loginUser.getUsername();
                this.tokenService.I(loginUser.getToken());
            }
            ServletUtils.renderString(response, ObjectMapperUtils.obj2JsonStr(AjaxResult.a(HttpStatus.SUCCESS, "退出成功")));
        } catch (Exception e) {
            LOG.error("用户退出异常，loginUser={},e={}", loginUser, e);
            ServletUtils.renderString(response, ObjectMapperUtils.obj2JsonStr(AjaxResult.a(HttpStatus.SUCCESS, "退出成功")));
        }
    }
}
