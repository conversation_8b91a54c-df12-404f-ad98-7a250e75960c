package com.geely.gnds.ruoyi.framework.security.service;

import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.project.system.domain.SysRole;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service("ss")
/* loaded from: PermissionService.class */
public class PermissionService {
    private static final String go = "*:*:*";
    private static final String gp = "admin";
    private static final String gq = ",";
    private static final String gr = ",";

    @Autowired
    private TokenService tokenService;

    public boolean A(String permission) {
        if (StringUtils.isEmpty(permission)) {
            return false;
        }
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions())) {
            return false;
        }
        return a(loginUser.getPermissions(), permission);
    }

    public boolean B(String permission) {
        return !A(permission);
    }

    public boolean C(String permissions) {
        if (StringUtils.isEmpty(permissions)) {
            return false;
        }
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions())) {
            return false;
        }
        Set<String> authorities = loginUser.getPermissions();
        for (String permission : permissions.split(ConstantEnum.COMMA)) {
            if (permission != null && a(authorities, permission)) {
                return true;
            }
        }
        return false;
    }

    public boolean D(String role) {
        if (StringUtils.isEmpty(role)) {
            return false;
        }
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getUser().getRoles())) {
            return false;
        }
        for (SysRole sysRole : loginUser.getUser().getRoles()) {
            String roleKey = sysRole.getRoleKey();
            if (gp.contains(roleKey) || roleKey.contains(StringUtils.trim(role))) {
                return true;
            }
        }
        return false;
    }

    public boolean E(String role) {
        return !D(role);
    }

    public boolean F(String roles) {
        if (StringUtils.isEmpty(roles)) {
            return false;
        }
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getUser().getRoles())) {
            return false;
        }
        for (String role : roles.split(ConstantEnum.COMMA)) {
            if (D(role)) {
                return true;
            }
        }
        return false;
    }

    private boolean a(Set<String> permissions, String permission) {
        return permissions.contains(go) || permissions.contains(StringUtils.trim(permission));
    }
}
