package com.geely.gnds.ruoyi.framework.security.service;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.exception.user.CaptchaException;
import com.geely.gnds.ruoyi.common.exception.user.CaptchaExpireException;
import com.geely.gnds.ruoyi.common.exception.user.UserPasswordNotMatchException;
import com.geely.gnds.ruoyi.common.utils.SecurityUtils;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.LoginWebUser;
import com.geely.gnds.ruoyi.framework.security.data.AdminUserDetails;
import com.geely.gnds.ruoyi.framework.security.data.WebUserDetails;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

@Component
/* loaded from: SysLoginService.class */
public class SysLoginService {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private Executor executor;

    @Autowired
    private TokenWebService gn;

    @Autowired
    @Qualifier("adminUserDetailsService")
    private UserDetailsService gs;

    @Autowired
    @Qualifier("webUserDetailsService")
    private UserDetailsService gt;

    @Autowired
    private EhcacheClient ehcacheClient;

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.springframework.security.authentication.BadCredentialsException */
    public String c(String username, String password, String code, String uuid) throws BadCredentialsException {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = (String) this.ehcacheClient.w(verifyKey);
        this.ehcacheClient.y(verifyKey);
        if (captcha == null) {
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException();
        }
        try {
            UserDetails userDetails = this.gs.loadUserByUsername(username);
            if (!SecurityUtils.matchesPassword(password, userDetails.getPassword())) {
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00103));
            }
            UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(userDetails, (Object) null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
            AdminUserDetails adminUserDetails = (AdminUserDetails) usernamePasswordAuthenticationToken.getPrincipal();
            LoginUser loginUser = new LoginUser(adminUserDetails.getUser(), adminUserDetails.getPermissionSet());
            return this.tokenService.a(loginUser);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                throw new UserPasswordNotMatchException();
            }
            throw new CustomException(e.getMessage());
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.springframework.security.authentication.BadCredentialsException */
    public String i(String username, String password) throws BadCredentialsException {
        try {
            UserDetails userDetails = this.gs.loadUserByUsername(username);
            if (!SecurityUtils.matchesPassword(password, userDetails.getPassword())) {
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
            }
            UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(userDetails, (Object) null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
            AdminUserDetails adminUserDetails = (AdminUserDetails) usernamePasswordAuthenticationToken.getPrincipal();
            LoginUser loginUser = new LoginUser(adminUserDetails.getUser(), adminUserDetails.getPermissionSet());
            return this.tokenService.a(loginUser);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                throw new UserPasswordNotMatchException();
            }
            throw new CustomException(e.getMessage());
        }
    }

    public String G(String username) {
        try {
            UserDetails userDetails = this.gs.loadUserByUsername(username);
            UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(userDetails, (Object) null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
            AdminUserDetails adminUserDetails = (AdminUserDetails) usernamePasswordAuthenticationToken.getPrincipal();
            LoginUser loginUser = new LoginUser(adminUserDetails.getUser(), adminUserDetails.getPermissionSet());
            return this.tokenService.a(loginUser);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                throw new UserPasswordNotMatchException();
            }
            throw new CustomException(e.getMessage());
        }
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.springframework.security.authentication.BadCredentialsException */
    public String h(String username, String password, String uuid) throws BadCredentialsException {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        this.ehcacheClient.y(verifyKey);
        try {
            UserDetails userDetails = this.gt.loadUserByUsername(username);
            if (!password.equals(userDetails.getPassword())) {
                throw new BadCredentialsException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00103));
            }
            UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(userDetails, (Object) null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
            WebUserDetails webUserDetails = (WebUserDetails) usernamePasswordAuthenticationToken.getPrincipal();
            Set<String> set = new HashSet<>();
            LoginWebUser loginUser = new LoginWebUser(webUserDetails.getUser(), set);
            return this.gn.a(loginUser);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                throw new UserPasswordNotMatchException();
            }
            throw new CustomException(e.getMessage());
        }
    }
}
