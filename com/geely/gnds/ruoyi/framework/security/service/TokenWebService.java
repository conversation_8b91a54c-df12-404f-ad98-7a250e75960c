package com.geely.gnds.ruoyi.framework.security.service;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.IdUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.ip.AddressUtils;
import com.geely.gnds.ruoyi.common.utils.ip.IpUtils;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.framework.security.LoginWebUser;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
/* loaded from: TokenWebService.class */
public class TokenWebService {

    @Value("${token.header}")
    private String header;

    @Value("${token.headerPortal}")
    private String tokenHeadPortal;

    @Value("${token.expireTime}")
    private int expireTime;
    private final String secret = IdUtils.fastUuid();
    protected static final long gw = 1000;
    protected static final long gx = 60000;
    private static final Long gy = 1200000L;

    @Autowired
    private EhcacheClient ehcacheClient;

    public LoginWebUser e(HttpServletRequest request) {
        String token = d(request);
        if (StringUtils.isNotEmpty(token)) {
            Claims claims = J(token);
            String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
            String userKey = L(uuid);
            LoginWebUser user = (LoginWebUser) this.ehcacheClient.w(userKey);
            return user;
        }
        return null;
    }

    public void setLoginUser(LoginWebUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            c(loginUser);
        }
    }

    public void I(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userKey = L(token);
            this.ehcacheClient.y(userKey);
        }
    }

    public String a(LoginWebUser loginUser) {
        String token = IdUtils.fastUuid();
        loginUser.setToken(token);
        setUserAgent(loginUser);
        c(loginUser);
        Map<String, Object> claims = new HashMap<>(1);
        claims.put(Constants.LOGIN_USER_KEY, token);
        return a(claims);
    }

    public void b(LoginWebUser loginUser) {
        long expireTime = loginUser.getExpireTime().longValue();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= gy.longValue()) {
            c(loginUser);
        }
    }

    public void c(LoginWebUser loginUser) {
        loginUser.setLoginTime(Long.valueOf(System.currentTimeMillis()));
        loginUser.setExpireTime(Long.valueOf(loginUser.getLoginTime().longValue() + (this.expireTime * gx)));
        String userKey = L(loginUser.getToken());
        this.ehcacheClient.a(userKey, this.expireTime * 60, loginUser);
    }

    public void setUserAgent(LoginWebUser loginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        loginUser.setIpaddr(ip);
        loginUser.setLoginLocation(AddressUtils.getRealAddressByIp(ip));
        loginUser.setBrowser(userAgent.getBrowser().getName());
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    private String a(Map<String, Object> claims) {
        String token = Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, this.secret).compact();
        return this.tokenHeadPortal + token;
    }

    private Claims J(String token) {
        return (Claims) Jwts.parser().setSigningKey(this.secret).parseClaimsJws(token).getBody();
    }

    public String K(String token) {
        Claims claims = J(token);
        return claims.getSubject();
    }

    private String d(HttpServletRequest request) {
        String token = request.getHeader(this.header);
        if (StringUtils.isNotBlank(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        if (StringUtils.isNotBlank(token) && token.startsWith(this.tokenHeadPortal)) {
            token = token.replace(this.tokenHeadPortal, "");
        }
        return token;
    }

    private String L(String uuid) {
        return Constants.LOGIN_WEB_TOKEN_KEY + uuid;
    }
}
