package com.geely.gnds.ruoyi.framework.security.service;

import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.service.ISysMenuService;
import com.geely.gnds.ruoyi.project.system.service.ISysRoleService;
import java.util.HashSet;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
/* loaded from: SysPermissionService.class */
public class SysPermissionService {

    @Autowired
    private ISysRoleService gu;

    @Autowired
    private ISysMenuService gv;

    public Set<String> a(SysUser user) {
        Set<String> roles = new HashSet<>();
        if (user.isAdmin()) {
            roles.add("admin");
        } else {
            roles.addAll(this.gu.selectRolePermissionByUserId(user.getUserId()));
        }
        return roles;
    }

    public Set<String> b(SysUser user) {
        Set<String> perms = new HashSet<>();
        if (user.isAdmin()) {
            perms.add("*:*:*");
        } else {
            perms.addAll(this.gv.selectMenuPermsByUserId(user.getUserId()));
        }
        return perms;
    }
}
