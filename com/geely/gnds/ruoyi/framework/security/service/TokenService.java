package com.geely.gnds.ruoyi.framework.security.service;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.IdUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.ip.AddressUtils;
import com.geely.gnds.ruoyi.common.utils.ip.IpUtils;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.TokenInvalidException;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
/* loaded from: TokenService.class */
public class TokenService {

    @Value("${token.header}")
    private String header;

    @Value("${token.headerAdmin}")
    private String tokenHeadAdmin;

    @Value("${token.expireTime}")
    private int expireTime;
    private final String secret = IdUtils.fastUuid();
    protected static final long gw = 1000;
    protected static final long gx = 60000;
    private static final Long gy = 1200000L;

    @Autowired
    private EhcacheClient ehcacheClient;

    public LoginUser c(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String token = d(request);
        if (StringUtils.isNotEmpty(token)) {
            try {
                Claims claims = J(token);
                String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
                String userKey = L(uuid);
                LoginUser user = (LoginUser) this.ehcacheClient.w(userKey);
                return user;
            } catch (Exception e) {
                throw new TokenInvalidException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00113), Integer.valueOf(HttpStatus.FORBIDDEN));
            }
        }
        return null;
    }

    public LoginUser H(String token) {
        if (StringUtils.isNotEmpty(token)) {
            if (StringUtils.isNotBlank(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
                token = token.replace(Constants.TOKEN_PREFIX, "");
            }
            if (StringUtils.isNotBlank(token) && token.startsWith(this.tokenHeadAdmin)) {
                token = token.replace(this.tokenHeadAdmin, "");
            }
            try {
                Claims claims = J(token);
                String uuid = (String) claims.get(Constants.LOGIN_USER_KEY);
                String userKey = L(uuid);
                LoginUser user = (LoginUser) this.ehcacheClient.w(userKey);
                return user;
            } catch (Exception e) {
                throw new TokenInvalidException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00113), Integer.valueOf(HttpStatus.FORBIDDEN));
            }
        }
        return null;
    }

    public LoginUser getLoginUser() {
        LoginUser loginUser = ThreadLocalUtils.CURRENT_USER.get();
        if (loginUser == null) {
            return c(ServletUtils.getRequest());
        }
        return loginUser;
    }

    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            c(loginUser);
        }
    }

    public void I(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userKey = L(token);
            this.ehcacheClient.y(userKey);
        }
    }

    public String a(LoginUser loginUser) {
        String token = IdUtils.fastUuid();
        loginUser.setToken(token);
        setUserAgent(loginUser);
        c(loginUser);
        Map<String, Object> claims = new HashMap<>(1);
        claims.put(Constants.LOGIN_USER_KEY, token);
        return a(claims);
    }

    public void b(LoginUser loginUser) {
        long expireTime = loginUser.getExpireTime().longValue();
        long currentTime = System.currentTimeMillis();
        if (expireTime - currentTime <= gy.longValue()) {
            c(loginUser);
        }
    }

    public void c(LoginUser loginUser) {
        loginUser.setLoginTime(Long.valueOf(System.currentTimeMillis()));
        loginUser.setExpireTime(Long.valueOf(loginUser.getLoginTime().longValue() + (this.expireTime * gx)));
        String userKey = L(loginUser.getToken());
        this.ehcacheClient.a(userKey, this.expireTime * 60, loginUser);
    }

    public void setUserAgent(LoginUser loginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        loginUser.setIpaddr(ip);
        loginUser.setLoginLocation(AddressUtils.getRealAddressByIp(ip));
        loginUser.setBrowser(userAgent.getBrowser().getName());
        loginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    private String a(Map<String, Object> claims) {
        String token = Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, this.secret).compact();
        return this.tokenHeadAdmin + token;
    }

    private Claims J(String token) {
        return (Claims) Jwts.parser().setSigningKey(this.secret).parseClaimsJws(token).getBody();
    }

    public String K(String token) {
        Claims claims = J(token);
        return claims.getSubject();
    }

    public String d(HttpServletRequest request) {
        String token = request.getHeader(this.header);
        if (StringUtils.isBlank(token)) {
            token = request.getParameter(this.header);
        }
        if (StringUtils.isNotBlank(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        if (StringUtils.isNotBlank(token) && token.startsWith(this.tokenHeadAdmin)) {
            token = token.replace(this.tokenHeadAdmin, "");
        }
        return token;
    }

    private String L(String uuid) {
        return Constants.LOGIN_TOKEN_KEY + uuid;
    }

    public String getHeader() {
        return this.header;
    }
}
