package com.geely.gnds.ruoyi.framework.manager;

import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.util.TesterThread;
import javax.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
/* loaded from: ShutdownManager.class */
public class ShutdownManager {
    private static final Logger logger = LoggerFactory.getLogger(ShutdownManager.class);

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private Cloud cloud;

    @PreDestroy
    public void destroy() {
        this.testerThread.shutdown();
    }
}
