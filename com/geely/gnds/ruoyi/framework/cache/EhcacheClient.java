package com.geely.gnds.ruoyi.framework.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Component;

@Component
/* loaded from: EhcacheClient.class */
public class EhcacheClient {
    private Cache<String, Object> fN = Caffeine.newBuilder().build();

    public Cache<String, Object> getCache() {
        return this.fN;
    }

    public void setCache(Cache<String, Object> cache) {
        this.fN = cache;
    }

    public <T> T w(String str) {
        try {
            return (T) this.fN.getIfPresent(str);
        } catch (RuntimeException e) {
            return null;
        }
    }

    public List<String> x(String key) {
        List<String> keyList = new ArrayList<>();
        this.fN.asMap().forEach((k, v) -> {
            if (k.startsWith(key)) {
                keyList.add(k);
            }
        });
        return keyList;
    }

    public void a(String key, int expiredTime, Object value) {
        if (expiredTime == 0) {
            this.fN.put(key, value);
            return;
        }
        Cache<String, Object> customCache = Caffeine.newBuilder().expireAfterWrite(expiredTime, TimeUnit.SECONDS).build();
        customCache.put(key, value);
        this.fN.put(key, customCache.getIfPresent(key));
    }

    public void y(String key) {
        this.fN.invalidate(key);
    }
}
