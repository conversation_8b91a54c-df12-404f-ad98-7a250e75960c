package com.geely.gnds.ruoyi.framework.web.page;

import java.io.Serializable;
import java.util.List;

/* loaded from: TableDataInfo.class */
public class TableDataInfo implements Serializable {
    private static final long serialVersionUID = 1;
    private long hg;
    private List<?> rows;
    private int code;
    private String msg;

    public TableDataInfo() {
    }

    public TableDataInfo(List<?> list, int total) {
        this.rows = list;
        this.hg = total;
    }

    public long getTotal() {
        return this.hg;
    }

    public void setTotal(long total) {
        this.hg = total;
    }

    public List<?> getRows() {
        return this.rows;
    }

    public void setRows(List<?> rows) {
        this.rows = rows;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
