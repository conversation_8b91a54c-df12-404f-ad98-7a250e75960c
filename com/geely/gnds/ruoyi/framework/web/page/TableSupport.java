package com.geely.gnds.ruoyi.framework.web.page;

import com.geely.gnds.ruoyi.common.utils.ServletUtils;

/* loaded from: TableSupport.class */
public class TableSupport {
    public static final String hh = "pageNum";
    public static final String hi = "pageSize";
    public static final String hj = "orderByColumn";
    public static final String hk = "isAsc";

    public static PageDomain getPageDomain() {
        PageDomain pageDomain = new PageDomain();
        pageDomain.setPageNum(ServletUtils.getParameterToInt(hh));
        pageDomain.setPageSize(ServletUtils.getParameterToInt(hi));
        pageDomain.setOrderByColumn(ServletUtils.getParameter(hj));
        pageDomain.setIsAsc(ServletUtils.getParameter(hk));
        return pageDomain;
    }

    public static PageDomain E() {
        return getPageDomain();
    }
}
