package com.geely.gnds.ruoyi.framework.web.page;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.enums.ConstantEnum;

/* loaded from: PageDomain.class */
public class PageDomain {
    private Integer hc;
    private Integer hd;
    private String he;
    private String hf;

    public String getOrderBy() {
        if (StringUtils.isEmpty(this.he)) {
            return "";
        }
        return StringUtils.toUnderScoreCase(this.he) + ConstantEnum.EMPTY + this.hf;
    }

    public Integer getPageNum() {
        return this.hc;
    }

    public void setPageNum(Integer pageNum) {
        this.hc = pageNum;
    }

    public Integer getPageSize() {
        return this.hd;
    }

    public void setPageSize(Integer pageSize) {
        this.hd = pageSize;
    }

    public String getOrderByColumn() {
        return this.he;
    }

    public void setOrderByColumn(String orderByColumn) {
        this.he = orderByColumn;
    }

    public String getIsAsc() {
        return this.hf;
    }

    public void setIsAsc(String isAsc) {
        this.hf = isAsc;
    }
}
