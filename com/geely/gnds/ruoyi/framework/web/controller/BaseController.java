package com.geely.gnds.ruoyi.framework.web.controller;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.sql.SqlUtil;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.ruoyi.framework.web.page.PageDomain;
import com.geely.gnds.ruoyi.framework.web.page.TableDataInfo;
import com.geely.gnds.ruoyi.framework.web.page.TableSupport;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

/* loaded from: BaseController.class */
public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(BaseController.class);

    @InitBinder
    public void a(WebDataBinder binder) {
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() { // from class: com.geely.gnds.ruoyi.framework.web.controller.BaseController.1
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    protected void y() {
        PageDomain pageDomain = TableSupport.E();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum.intValue(), pageSize.intValue(), orderBy);
        }
    }

    protected TableDataInfo h(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    protected AjaxResult b(int rows) {
        return rows > 0 ? AjaxResult.z() : AjaxResult.A();
    }
}
