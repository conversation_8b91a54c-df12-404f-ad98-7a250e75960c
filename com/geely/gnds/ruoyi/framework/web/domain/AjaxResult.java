package com.geely.gnds.ruoyi.framework.web.domain;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import java.util.HashMap;

/* loaded from: AjaxResult.class */
public class AjaxResult extends HashMap<String, Object> {
    private static final long serialVersionUID = 1;
    public static final String gA = "code";
    public static final String gB = "msg";
    public static final String gC = "data";

    public AjaxResult() {
    }

    public AjaxResult(int code, String msg) {
        super.put(gA, Integer.valueOf(code));
        super.put(gB, msg);
    }

    public AjaxResult(int code, String msg, Object data) {
        super.put(gA, Integer.valueOf(code));
        super.put(gB, msg);
        if (StringUtils.isNotNull(data)) {
            super.put(gC, data);
        }
    }

    public static AjaxResult z() {
        return M("操作成功");
    }

    public static AjaxResult d(Object data) {
        return a("操作成功", data);
    }

    public static AjaxResult M(String msg) {
        return a(msg, (Object) null);
    }

    public static AjaxResult a(String msg, Object data) {
        return new AjaxResult(HttpStatus.SUCCESS, msg, data);
    }

    public static AjaxResult A() {
        return N("操作失败");
    }

    public static AjaxResult N(String msg) {
        return b(msg, null);
    }

    public static AjaxResult b(String msg, Object data) {
        return new AjaxResult(HttpStatus.ERROR, msg, data);
    }

    public static AjaxResult a(int code, String msg) {
        return new AjaxResult(code, msg, null);
    }

    @Override // java.util.HashMap, java.util.AbstractMap, java.util.Map
    /* renamed from: c, reason: merged with bridge method [inline-methods] */
    public AjaxResult put(String key, Object value) {
        super.put(key, value);
        return this;
    }
}
