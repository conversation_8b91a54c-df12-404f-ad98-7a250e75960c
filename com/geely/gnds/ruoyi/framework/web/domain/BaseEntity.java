package com.geely.gnds.ruoyi.framework.web.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/* loaded from: BaseEntity.class */
public class BaseEntity implements Serializable {
    private static final long serialVersionUID = 1;
    private String searchValue;
    private String createBy;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private String createTime;
    private String updateBy;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private String updateTime;
    private String remark;

    @JsonIgnore
    private String beginTime;

    @JsonIgnore
    private String endTime;
    private Map<String, Object> params;

    public String getSearchValue() {
        return this.searchValue;
    }

    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBeginTime() {
        return this.beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Map<String, Object> getParams() {
        if (this.params == null) {
            this.params = new HashMap(2);
        }
        return this.params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }
}
