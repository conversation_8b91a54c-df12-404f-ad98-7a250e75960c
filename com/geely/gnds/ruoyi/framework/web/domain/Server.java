package com.geely.gnds.ruoyi.framework.web.domain;

import com.geely.gnds.ruoyi.common.utils.Arith;
import com.geely.gnds.ruoyi.common.utils.ip.IpUtils;
import com.geely.gnds.ruoyi.framework.web.domain.server.Cpu;
import com.geely.gnds.ruoyi.framework.web.domain.server.Jvm;
import com.geely.gnds.ruoyi.framework.web.domain.server.Mem;
import com.geely.gnds.ruoyi.framework.web.domain.server.Sys;
import com.geely.gnds.ruoyi.framework.web.domain.server.SysFile;
import java.net.UnknownHostException;
import java.util.LinkedList;
import java.util.List;
import java.util.Properties;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.FileSystem;
import oshi.software.os.OSFileStore;
import oshi.software.os.OperatingSystem;
import oshi.util.Util;

/* loaded from: Server.class */
public class Server {
    private static final int gD = 1000;
    private Cpu gE = new Cpu();
    private Mem gF = new Mem();
    private Jvm gG = new Jvm();
    private Sys gH = new Sys();
    private List<SysFile> gI = new LinkedList();

    public Cpu getCpu() {
        return this.gE;
    }

    public void setCpu(Cpu cpu) {
        this.gE = cpu;
    }

    public Mem getMem() {
        return this.gF;
    }

    public void setMem(Mem mem) {
        this.gF = mem;
    }

    public Jvm getJvm() {
        return this.gG;
    }

    public void setJvm(Jvm jvm) {
        this.gG = jvm;
    }

    public Sys getSys() {
        return this.gH;
    }

    public void setSys(Sys sys) {
        this.gH = sys;
    }

    public List<SysFile> getSysFiles() {
        return this.gI;
    }

    public void setSysFiles(List<SysFile> sysFiles) {
        this.gI = sysFiles;
    }

    public void B() throws Exception {
        SystemInfo si = new SystemInfo();
        HardwareAbstractionLayer hal = si.getHardware();
        setCpuInfo(hal.getProcessor());
        setMemInfo(hal.getMemory());
        C();
        D();
        setSysFiles(si.getOperatingSystem());
    }

    private void setCpuInfo(CentralProcessor processor) {
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        Util.sleep(1000L);
        long[] ticks = processor.getSystemCpuLoadTicks();
        long nice = ticks[CentralProcessor.TickType.NICE.getIndex()] - prevTicks[CentralProcessor.TickType.NICE.getIndex()];
        long irq = ticks[CentralProcessor.TickType.IRQ.getIndex()] - prevTicks[CentralProcessor.TickType.IRQ.getIndex()];
        long softirq = ticks[CentralProcessor.TickType.SOFTIRQ.getIndex()] - prevTicks[CentralProcessor.TickType.SOFTIRQ.getIndex()];
        long steal = ticks[CentralProcessor.TickType.STEAL.getIndex()] - prevTicks[CentralProcessor.TickType.STEAL.getIndex()];
        long cSys = ticks[CentralProcessor.TickType.SYSTEM.getIndex()] - prevTicks[CentralProcessor.TickType.SYSTEM.getIndex()];
        long user = ticks[CentralProcessor.TickType.USER.getIndex()] - prevTicks[CentralProcessor.TickType.USER.getIndex()];
        long iowait = ticks[CentralProcessor.TickType.IOWAIT.getIndex()] - prevTicks[CentralProcessor.TickType.IOWAIT.getIndex()];
        long idle = ticks[CentralProcessor.TickType.IDLE.getIndex()] - prevTicks[CentralProcessor.TickType.IDLE.getIndex()];
        long totalCpu = user + nice + cSys + idle + iowait + irq + softirq + steal;
        this.gE.setCpuNum(processor.getLogicalProcessorCount());
        this.gE.setTotal(totalCpu);
        this.gE.setSys(cSys);
        this.gE.setUsed(user);
        this.gE.setWait(iowait);
        this.gE.setFree(idle);
    }

    private void setMemInfo(GlobalMemory memory) {
        this.gF.setTotal(memory.getTotal());
        this.gF.setUsed(memory.getTotal() - memory.getAvailable());
        this.gF.setFree(memory.getAvailable());
    }

    private void C() {
        Properties props = System.getProperties();
        this.gH.setComputerName(IpUtils.getHostName());
        this.gH.setComputerIp(IpUtils.getHostIp());
        this.gH.setOsName(props.getProperty("os.name"));
        this.gH.setOsArch(props.getProperty("os.arch"));
        this.gH.setUserDir(props.getProperty("user.dir"));
    }

    private void D() throws UnknownHostException {
        Properties props = System.getProperties();
        this.gG.setTotal(Runtime.getRuntime().totalMemory());
        this.gG.setMax(Runtime.getRuntime().maxMemory());
        this.gG.setFree(Runtime.getRuntime().freeMemory());
        this.gG.setVersion(props.getProperty("java.version"));
        this.gG.setHome(props.getProperty("java.home"));
    }

    private void setSysFiles(OperatingSystem os) {
        FileSystem fileSystem = os.getFileSystem();
        List<OSFileStore> fsArray = fileSystem.getFileStores();
        for (OSFileStore fs : fsArray) {
            long free = fs.getUsableSpace();
            long total = fs.getTotalSpace();
            long used = total - free;
            SysFile sysFile = new SysFile();
            sysFile.setDirName(fs.getMount());
            sysFile.setSysTypeName(fs.getType());
            sysFile.setTypeName(fs.getName());
            sysFile.setTotal(a(total));
            sysFile.setFree(a(free));
            sysFile.setUsed(a(used));
            sysFile.setUsage(Arith.mul(Arith.div(used, total, 4), 100.0d));
            this.gI.add(sysFile);
        }
    }

    public String a(long size) {
        long mb = 1024 * 1024;
        long gb = mb * 1024;
        if (size >= gb) {
            return String.format("%.1f GB", Float.valueOf(size / gb));
        }
        if (size >= mb) {
            float f = size / mb;
            return String.format(f > 100.0f ? "%.0f MB" : "%.1f MB", Float.valueOf(f));
        }
        if (size >= 1024) {
            float f2 = size / 1024;
            return String.format(f2 > 100.0f ? "%.0f KB" : "%.1f KB", Float.valueOf(f2));
        }
        return String.format("%d B", Long.valueOf(size));
    }
}
