package com.geely.gnds.ruoyi.framework.web.domain;

import java.util.ArrayList;
import java.util.List;

/* loaded from: TreeEntity.class */
public class TreeEntity extends BaseEntity {
    private static final long serialVersionUID = 1;
    private String parentName;
    private Long parentId;
    private Integer gJ;
    private String ancestors;
    private List<?> children = new ArrayList();

    public String getParentName() {
        return this.parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getOrderNum() {
        return this.gJ;
    }

    public void setOrderNum(Integer orderNum) {
        this.gJ = orderNum;
    }

    public String getAncestors() {
        return this.ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public List<?> getChildren() {
        return this.children;
    }

    public void setChildren(List<?> children) {
        this.children = children;
    }
}
