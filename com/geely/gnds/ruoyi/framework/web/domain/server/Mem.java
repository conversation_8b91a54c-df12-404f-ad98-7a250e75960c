package com.geely.gnds.ruoyi.framework.web.domain.server;

import com.geely.gnds.ruoyi.common.utils.Arith;

/* loaded from: Mem.class */
public class Mem {
    private double gL;
    private double gN;
    private double gP;

    public double getTotal() {
        return Arith.div(this.gL, 1.073741824E9d, 2);
    }

    public void setTotal(long total) {
        this.gL = total;
    }

    public double getUsed() {
        return Arith.div(this.gN, 1.073741824E9d, 2);
    }

    public void setUsed(long used) {
        this.gN = used;
    }

    public double getFree() {
        return Arith.div(this.gP, 1.073741824E9d, 2);
    }

    public void setFree(long free) {
        this.gP = free;
    }

    public double getUsage() {
        return Arith.mul(Arith.div(this.gN, this.gL, 4), 100.0d);
    }
}
