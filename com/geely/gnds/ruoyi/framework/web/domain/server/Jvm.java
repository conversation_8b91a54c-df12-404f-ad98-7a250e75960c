package com.geely.gnds.ruoyi.framework.web.domain.server;

import com.geely.gnds.ruoyi.common.utils.Arith;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.lang.management.ManagementFactory;

/* loaded from: Jvm.class */
public class Jvm {
    private double gL;
    private double gQ;
    private double gP;
    private String version;
    private String gR;

    public double getTotal() {
        return Arith.div(this.gL, 1048576.0d, 2);
    }

    public void setTotal(double total) {
        this.gL = total;
    }

    public double getMax() {
        return Arith.div(this.gQ, 1048576.0d, 2);
    }

    public void setMax(double max) {
        this.gQ = max;
    }

    public double getFree() {
        return Arith.div(this.gP, 1048576.0d, 2);
    }

    public void setFree(double free) {
        this.gP = free;
    }

    public double getUsed() {
        return Arith.div(this.gL - this.gP, 1048576.0d, 2);
    }

    public double getUsage() {
        return Arith.mul(Arith.div(this.gL - this.gP, this.gL, 4), 100.0d);
    }

    public String getName() {
        return ManagementFactory.getRuntimeMXBean().getVmName();
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getHome() {
        return this.gR;
    }

    public void setHome(String home) {
        this.gR = home;
    }

    public String getStartTime() {
        return DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getServerStartDate());
    }

    public String getRunTime() {
        return DateUtils.getDatePoor(DateUtils.getNowDate(), DateUtils.getServerStartDate());
    }
}
