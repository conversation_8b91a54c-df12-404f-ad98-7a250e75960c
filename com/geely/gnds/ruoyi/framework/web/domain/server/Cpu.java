package com.geely.gnds.ruoyi.framework.web.domain.server;

import com.geely.gnds.ruoyi.common.utils.Arith;

/* loaded from: Cpu.class */
public class Cpu {
    private int gK;
    private double gL;
    private double gM;
    private double gN;
    private double gO;
    private double gP;

    public int getCpuNum() {
        return this.gK;
    }

    public void setCpuNum(int cpuNum) {
        this.gK = cpuNum;
    }

    public double getTotal() {
        return Arith.round(Arith.mul(this.gL, 100.0d), 2);
    }

    public void setTotal(double total) {
        this.gL = total;
    }

    public double getSys() {
        return Arith.round(Arith.mul(this.gM / this.gL, 100.0d), 2);
    }

    public void setSys(double sys) {
        this.gM = sys;
    }

    public double getUsed() {
        return Arith.round(Arith.mul(this.gN / this.gL, 100.0d), 2);
    }

    public void setUsed(double used) {
        this.gN = used;
    }

    public double getWait() {
        return Arith.round(Arith.mul(this.gO / this.gL, 100.0d), 2);
    }

    public void setWait(double wait) {
        this.gO = wait;
    }

    public double getFree() {
        return Arith.round(Arith.mul(this.gP / this.gL, 100.0d), 2);
    }

    public void setFree(double free) {
        this.gP = free;
    }
}
