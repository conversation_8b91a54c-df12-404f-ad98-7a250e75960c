package com.geely.gnds.ruoyi.framework.web.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.geely.gnds.ruoyi.project.system.domain.SysDept;
import com.geely.gnds.ruoyi.project.system.domain.SysMenu;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/* loaded from: TreeSelect.class */
public class TreeSelect implements Serializable {
    private static final long serialVersionUID = 1;
    private Long id;
    private String label;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    public TreeSelect() {
    }

    public TreeSelect(SysDept dept) {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.children = (List) dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(SysMenu menu) {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = (List) menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return this.label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<TreeSelect> getChildren() {
        return this.children;
    }

    public void setChildren(List<TreeSelect> children) {
        this.children = children;
    }
}
