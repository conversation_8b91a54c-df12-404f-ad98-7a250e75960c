package com.geely.gnds.ruoyi.framework.web.exception;

import cn.hutool.core.util.StrUtil;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.BaseException;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.exception.DemoModeException;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

@RestControllerAdvice
/* loaded from: GlobalExceptionHandler.class */
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler({BaseException.class})
    public AjaxResult a(BaseException e) {
        return AjaxResult.N(e.getMessage());
    }

    @ExceptionHandler({CustomException.class})
    public AjaxResult a(CustomException e) {
        if (StringUtils.isNull(e.getCode())) {
            return AjaxResult.N(e.getMessage());
        }
        return AjaxResult.a(e.getCode().intValue(), e.getMessage());
    }

    @ExceptionHandler({GlobalException.class})
    public AjaxResult a(GlobalException e) {
        Cloud bean = (Cloud) SpringUtils.getBean(Cloud.class);
        log.error(e.getMessage(), e);
        if (e.getCause() == null || !StringUtils.isNotBlank(e.getCause().getMessage())) {
            return AjaxResult.a(e.getCode() > 0 ? e.getCode() : HttpStatus.ERROR, bean.aE(e.getMessage()));
        }
        return AjaxResult.a(e.getCode() > 0 ? e.getCode() : HttpStatus.ERROR, bean.aE(StrUtil.toStringOrNull(e.getCause().getMessage())));
    }

    @ExceptionHandler({NoHandlerFoundException.class})
    public AjaxResult a(Exception e) {
        log.error(e.getMessage(), e);
        return AjaxResult.a(HttpStatus.NOT_FOUND, "路径不存在，请检查路径是否正确");
    }

    @ExceptionHandler({AccessDeniedException.class})
    public AjaxResult a(AccessDeniedException e) {
        log.error(e.getMessage());
        return AjaxResult.a(HttpStatus.FORBIDDEN, "没有权限，请联系管理员授权");
    }

    @ExceptionHandler({AccountExpiredException.class})
    public AjaxResult a(AccountExpiredException e) {
        log.error(e.getMessage(), e);
        return AjaxResult.N(e.getMessage());
    }

    @ExceptionHandler({UsernameNotFoundException.class})
    public AjaxResult a(UsernameNotFoundException e) {
        log.error(e.getMessage(), e);
        return AjaxResult.N(e.getMessage());
    }

    @ExceptionHandler({Exception.class})
    public AjaxResult b(Exception e) {
        log.error(e.getMessage(), e);
        String msg = e.getMessage();
        if (StringUtils.isBlank(e.getMessage()) || ConstantEnum.NULL.equals(e.getMessage())) {
            msg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00160);
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(msg) && msg.contains("云端token失效")) {
            return AjaxResult.a(HttpStatus.UNAUTHORIZED, msg);
        }
        Cloud bean = (Cloud) SpringUtils.getBean(Cloud.class);
        return AjaxResult.N(bean.aE(msg));
    }

    @ExceptionHandler({BindException.class})
    public AjaxResult a(BindException e) {
        log.error(e.getMessage(), e);
        String message = ((ObjectError) e.getAllErrors().get(0)).getDefaultMessage();
        return AjaxResult.N(message);
    }

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public Object a(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return AjaxResult.N(message);
    }

    @ExceptionHandler({DemoModeException.class})
    public AjaxResult a(DemoModeException e) {
        return AjaxResult.N("演示模式，不允许操作");
    }
}
