package com.geely.gnds.ruoyi.framework.interceptor.impl;

import com.geely.gnds.ruoyi.common.filter.RepeatedlyRequestWrapper;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpHelper;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.framework.interceptor.AbstractRepeatSubmitInterceptor;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
/* loaded from: SameUrlDataInterceptor.class */
public class SameUrlDataInterceptor extends AbstractRepeatSubmitInterceptor {

    @Autowired
    private EhcacheClient ehcacheClient;
    public final String fX = "repeatParams";
    public final String fY = "repeatTime";
    public final String fZ = "repeatData";
    private int ga = 10;

    public void setIntervalTime(int intervalTime) {
        this.ga = intervalTime;
    }

    @Override // com.geely.gnds.ruoyi.framework.interceptor.AbstractRepeatSubmitInterceptor
    public boolean a(HttpServletRequest request) throws IOException {
        RepeatedlyRequestWrapper repeatedlyRequest = (RepeatedlyRequestWrapper) request;
        String nowParams = HttpHelper.getBodyString(repeatedlyRequest);
        if (StringUtils.isEmpty(nowParams)) {
            nowParams = ObjectMapperUtils.obj2JsonStr(request.getParameterMap());
        }
        HashMap map = new HashMap(2);
        map.put("repeatParams", nowParams);
        map.put("repeatTime", Long.valueOf(System.currentTimeMillis()));
        String url = request.getRequestURI();
        Object sessionObj = this.ehcacheClient.w("repeatData");
        if (sessionObj != null) {
            Map<String, Object> sessionMap = (Map) sessionObj;
            if (sessionMap.containsKey(url)) {
                Map<String, Object> preDataMap = (Map) sessionMap.get(url);
                if (a(map, preDataMap) && b(map, preDataMap)) {
                    return true;
                }
            }
        }
        Map<String, Object> cacheMap = new HashMap<>(1);
        cacheMap.put(url, map);
        this.ehcacheClient.a("repeatData", this.ga, cacheMap);
        return false;
    }

    private boolean a(Map<String, Object> nowMap, Map<String, Object> preMap) {
        String nowParams = (String) nowMap.get("repeatParams");
        String preParams = (String) preMap.get("repeatParams");
        return nowParams.equals(preParams);
    }

    private boolean b(Map<String, Object> nowMap, Map<String, Object> preMap) {
        long time1 = ((Long) nowMap.get("repeatTime")).longValue();
        long time2 = ((Long) preMap.get("repeatTime")).longValue();
        if (time1 - time2 < this.ga * 1000) {
            return true;
        }
        return false;
    }
}
