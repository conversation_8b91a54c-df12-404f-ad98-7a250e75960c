package com.geely.gnds.ruoyi.framework.interceptor;

import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.interceptor.annotation.RepeatSubmit;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.lang.reflect.Method;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
/* loaded from: AbstractRepeatSubmitInterceptor.class */
public abstract class AbstractRepeatSubmitInterceptor implements HandlerInterceptor {
    public abstract boolean a(HttpServletRequest httpServletRequest);

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            RepeatSubmit annotation = (RepeatSubmit) method.getAnnotation(RepeatSubmit.class);
            if (annotation != null && a(request)) {
                AjaxResult ajaxResult = AjaxResult.N("不允许重复提交，请稍后再试");
                ServletUtils.renderString(response, ObjectMapperUtils.obj2JsonStr(ajaxResult));
                return false;
            }
            return true;
        }
        return true;
    }
}
