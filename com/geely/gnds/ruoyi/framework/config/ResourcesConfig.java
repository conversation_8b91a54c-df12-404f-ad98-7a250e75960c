package com.geely.gnds.ruoyi.framework.config;

import com.geely.gnds.ruoyi.framework.interceptor.AbstractRepeatSubmitInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
/* loaded from: ResourcesConfig.class */
public class ResourcesConfig implements WebMvcConfigurer {

    @Autowired
    private AbstractRepeatSubmitInterceptor repeatSubmitInterceptor;

    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler(new String[]{"/profile/**"}).addResourceLocations(new String[]{"file:" + RuoYiConfig.getProfile() + "/"});
        registry.addResourceHandler(new String[]{"swagger-ui.html"}).addResourceLocations(new String[]{"classpath:/META-INF/resources/"});
        registry.addResourceHandler(new String[]{"/webjars/**"}).addResourceLocations(new String[]{"classpath:/META-INF/resources/webjars/"});
        registry.addResourceHandler(new String[]{"/static/**"}).addResourceLocations(new String[]{"classpath:/META-INF/resources/static/"});
    }

    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(this.repeatSubmitInterceptor).excludePathPatterns(new String[]{"/ds*", "/ds/*", "/ds/**"}).addPathPatterns(new String[]{"/**"});
    }
}
