package com.geely.gnds.ruoyi.framework.config;

import com.geely.gnds.ruoyi.framework.security.data.AdminUserDetails;
import com.geely.gnds.ruoyi.framework.security.data.WebUserDetails;
import com.geely.gnds.ruoyi.framework.security.filter.JwtAuthenticationTokenFilter;
import com.geely.gnds.ruoyi.framework.security.handle.AuthenticationEntryPointImpl;
import com.geely.gnds.ruoyi.framework.security.handle.ForbiddenEntryPointImpl;
import com.geely.gnds.ruoyi.framework.security.handle.LogoutSuccessHandlerImpl;
import com.geely.gnds.ruoyi.framework.security.service.SysPermissionService;
import com.geely.gnds.ruoyi.project.dq.mapper.AppUserMapper;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.ruoyi.project.system.mapper.SysUserMapper;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
/* loaded from: MultiHttpSecurityConfig.class */
public class MultiHttpSecurityConfig {

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private AppUserMapper appUserMapper;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private JwtAuthenticationTokenFilter authenticationTokenFilter;

    @Autowired
    private LogoutSuccessHandlerImpl logoutSuccessHandler;

    @Autowired
    private AuthenticationEntryPointImpl unauthorizedHandler;

    @Autowired
    private ForbiddenEntryPointImpl forbiddenHandler;

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Configuration
    @Order(1)
    /* loaded from: MultiHttpSecurityConfig$UserWebSecurityConfig.class */
    public class UserWebSecurityConfig extends WebSecurityConfigurerAdapter {
        public UserWebSecurityConfig() {
        }

        protected void configure(HttpSecurity httpSecurity) throws Exception {
            ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) httpSecurity.csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and().antMatcher("/api/**").authorizeRequests().antMatchers(new String[]{"/ds/**"})).anonymous().antMatchers(new String[]{"/api/web/system/captchaImage"})).anonymous().antMatchers(new String[]{"/api/web/login"})).anonymous().antMatchers(new String[]{"/api/web/cepLogin"})).anonymous().antMatchers(new String[]{"/api/web/offlineLogin"})).anonymous().antMatchers(new String[]{"/api/web/logout"})).anonymous().antMatchers(new String[]{"/api/web/register"})).anonymous().antMatchers(new String[]{"/api/web/yh/weizhi"})).anonymous().antMatchers(new String[]{"/api/web/yh/getqrcode"})).anonymous().antMatchers(new String[]{"/api/web/yh/checkqrcode"})).anonymous().antMatchers(new String[]{"/api/fzalipay/notify_pay"})).anonymous().antMatchers(new String[]{"/api/v1/login/**"})).permitAll().antMatchers(new String[]{"/api/v1/oauth/**"})).permitAll().antMatchers(new String[]{"/api/v1/version/**"})).permitAll().antMatchers(new String[]{"/api/v1/socket/**"})).permitAll().antMatchers(new String[]{"/api/v1/upgrade/**"})).permitAll().antMatchers(new String[]{"/actuator/health"})).permitAll().antMatchers(new String[]{"/api/v1/testerService/**"})).permitAll().antMatchers(new String[]{"/api/v1/testerConfig/getConfig"})).permitAll().antMatchers(new String[]{"/api/v1/testerConfig/color"})).permitAll().antMatchers(new String[]{"/api/v1/log/timeoutLog"})).permitAll().antMatchers(new String[]{"/api/v1/circuitDiagram/getCircuitDiagram"})).permitAll().antMatchers(new String[]{"/api/v1/circuitDiagram/getImage"})).permitAll().antMatchers(new String[]{"/api/v1/circuitDiagram/getImageByWdid"})).permitAll().antMatchers(new String[]{"/api/v1/circuitDiagram/getHarnessImage"})).permitAll().antMatchers(new String[]{"/api/v1/networkTopology/getNetworkTopology"})).permitAll().antMatchers(new String[]{"/api/v1/doc/getFile/**"})).permitAll().antMatchers(new String[]{"/api/v1/log/clientLog"})).permitAll().anyRequest()).authenticated().and().headers().frameOptions().disable();
            httpSecurity.addFilterBefore(MultiHttpSecurityConfig.this.authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
            httpSecurity.exceptionHandling().accessDeniedHandler(MultiHttpSecurityConfig.this.forbiddenHandler).authenticationEntryPoint(MultiHttpSecurityConfig.this.unauthorizedHandler);
        }

        protected void configure(AuthenticationManagerBuilder auth) throws Exception {
            auth.userDetailsService(MultiHttpSecurityConfig.this.webUserDetailsService());
        }
    }

    @Configuration
    @Order(2)
    /* loaded from: MultiHttpSecurityConfig$AdminWebSecurityConfig.class */
    public class AdminWebSecurityConfig extends WebSecurityConfigurerAdapter {
        public AdminWebSecurityConfig() {
        }

        protected void configure(HttpSecurity httpSecurity) throws Exception {
            ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) httpSecurity.csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and().antMatcher("/sys/**").antMatcher("/system/**").antMatcher("/tool/**").antMatcher("/gen/**").antMatcher("/common/**").antMatcher("/dq/**").authorizeRequests().anyRequest()).authenticated().and().headers().frameOptions().disable();
            httpSecurity.addFilterBefore(MultiHttpSecurityConfig.this.authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
            httpSecurity.exceptionHandling().accessDeniedHandler(MultiHttpSecurityConfig.this.forbiddenHandler).authenticationEntryPoint(MultiHttpSecurityConfig.this.unauthorizedHandler);
        }

        protected void configure(AuthenticationManagerBuilder auth) throws Exception {
            auth.userDetailsService(MultiHttpSecurityConfig.this.adminUserDetailsService());
        }
    }

    @Configuration
    @Order(3)
    /* loaded from: MultiHttpSecurityConfig$OtherSecurityConfigurationAdapter.class */
    public class OtherSecurityConfigurationAdapter extends WebSecurityConfigurerAdapter {
        public OtherSecurityConfigurationAdapter() {
        }

        protected void configure(HttpSecurity httpSecurity) throws Exception {
            ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) ((ExpressionUrlAuthorizationConfigurer.AuthorizedUrl) httpSecurity.csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and().authorizeRequests().antMatchers(new String[]{"/login"})).anonymous().antMatchers(new String[]{"/cepLogin"})).anonymous().antMatchers(new String[]{"/getSsoConfig"})).anonymous().antMatchers(new String[]{"/geelyLogin"})).anonymous().antMatchers(new String[]{"/sendMobileCode"})).anonymous().antMatchers(new String[]{"/getTenantInfo"})).anonymous().antMatchers(new String[]{"/offlineLogin"})).anonymous().antMatchers(new String[]{"/profile/**"})).anonymous().antMatchers(new String[]{"/common/download**"})).anonymous().antMatchers(new String[]{"/common/download/resource**"})).anonymous().antMatchers(new String[]{"/webjars/**"})).anonymous().antMatchers(new String[]{"/druid/**"})).anonymous().antMatchers(new String[]{"/captchaImage"})).anonymous().antMatchers(new String[]{"/actuator/**", "/shutdown"})).permitAll().antMatchers(HttpMethod.GET, new String[]{"/", "/*.html", "/favicon.ico", "/**/*.html", "/**/*.htm", "/static/**", "/ds/**", "/**/*.css", "/**/*.js", "/swagger-resources/**"})).permitAll().anyRequest()).authenticated().and().headers().frameOptions().disable();
            httpSecurity.logout().logoutUrl("/logout").logoutSuccessHandler(MultiHttpSecurityConfig.this.logoutSuccessHandler);
            httpSecurity.addFilterBefore(MultiHttpSecurityConfig.this.authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
            httpSecurity.exceptionHandling().accessDeniedHandler(MultiHttpSecurityConfig.this.forbiddenHandler).authenticationEntryPoint(MultiHttpSecurityConfig.this.unauthorizedHandler);
        }
    }

    @Bean(name = {"adminUserDetailsService"})
    public UserDetailsService adminUserDetailsService() {
        return username -> {
            SysUser admin = this.sysUserMapper.selectUserByUserName(username);
            if (admin != null) {
                Set<String> permissionList = this.permissionService.b(admin);
                return new AdminUserDetails(admin, permissionList);
            }
            throw new UsernameNotFoundException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
        };
    }

    @Bean(name = {"webUserDetailsService"})
    public UserDetailsService webUserDetailsService() {
        return username -> {
            Map<String, Object> user = this.appUserMapper.getByUsername(username);
            if (user != null) {
                return new WebUserDetails(user);
            }
            throw new UsernameNotFoundException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00102));
        };
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern("*");
        config.setAllowCredentials(true);
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        source.registerCorsConfiguration("/**", config);
        FilterRegistrationBean bean = new FilterRegistrationBean(new CorsFilter(source), new ServletRegistrationBean[0]);
        bean.setOrder(0);
        return new CorsFilter(source);
    }
}
