package com.geely.gnds.ruoyi.framework.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.geely.gnds.ruoyi.common.enums.PrivacyTypeEnum;
import com.geely.gnds.ruoyi.common.utils.PrivacyUtil;
import com.geely.gnds.ruoyi.framework.annotation.PrivacyEncrypt;
import java.io.IOException;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/* loaded from: PrivacySerializer.class */
public class PrivacySerializer extends JsonSerializer<String> implements ContextualSerializer {
    private PrivacyTypeEnum fO;
    private Integer fP;
    private Integer fQ;
    private String fR;

    public PrivacySerializer() {
    }

    public PrivacySerializer(PrivacyTypeEnum privacyTypeEnum, Integer prefixNoMaskLen, Integer suffixNoMaskLen, String symbol) {
        this.fO = privacyTypeEnum;
        this.fP = prefixNoMaskLen;
        this.fQ = suffixNoMaskLen;
        this.fR = symbol;
    }

    /* renamed from: a, reason: merged with bridge method [inline-methods] */
    public void serialize(String origin, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (StringUtils.isNotBlank(origin) && null != this.fO) {
            switch (this.fO) {
                case CUSTOMER:
                    jsonGenerator.writeString(PrivacyUtil.desValue(origin, this.fP.intValue(), this.fQ.intValue(), this.fR));
                    return;
                case NAME:
                    jsonGenerator.writeString(PrivacyUtil.hideChineseName(origin));
                    return;
                case ID_CARD:
                    jsonGenerator.writeString(PrivacyUtil.hideIdCard(origin));
                    return;
                case PHONE:
                    jsonGenerator.writeString(PrivacyUtil.hidePhone(origin));
                    return;
                case EMAIL:
                    jsonGenerator.writeString(PrivacyUtil.hideEmail(origin));
                    return;
                default:
                    throw new IllegalArgumentException("unknown privacy type enum " + this.fO);
            }
        }
        jsonGenerator.writeNull();
    }

    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty != null) {
            if (Objects.equals(beanProperty.getType().getRawClass(), String.class)) {
                PrivacyEncrypt privacyEncrypt = (PrivacyEncrypt) beanProperty.getAnnotation(PrivacyEncrypt.class);
                if (privacyEncrypt == null) {
                    privacyEncrypt = (PrivacyEncrypt) beanProperty.getContextAnnotation(PrivacyEncrypt.class);
                }
                if (privacyEncrypt != null) {
                    return new PrivacySerializer(privacyEncrypt.type(), Integer.valueOf(privacyEncrypt.prefixNoMaskLen()), Integer.valueOf(privacyEncrypt.suffixNoMaskLen()), privacyEncrypt.symbol());
                }
            }
            return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
        }
        return serializerProvider.findNullValueSerializer((BeanProperty) null);
    }
}
