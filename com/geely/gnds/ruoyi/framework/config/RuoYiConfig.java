package com.geely.gnds.ruoyi.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "ruoyi")
@Component
/* loaded from: RuoYiConfig.class */
public class RuoYiConfig {
    private String name;
    private String version;
    private String fT;
    private boolean fU;
    private static String fV;
    private static boolean fW;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCopyrightYear() {
        return this.fT;
    }

    public void setCopyrightYear(String copyrightYear) {
        this.fT = copyrightYear;
    }

    public boolean isDemoEnabled() {
        return this.fU;
    }

    public void setDemoEnabled(boolean demoEnabled) {
        this.fU = demoEnabled;
    }

    public static String getProfile() {
        return fV;
    }

    public void setProfile(String profile) {
        fV = profile;
    }

    public static boolean isAddressEnabled() {
        return fW;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        fW = addressEnabled;
    }

    public static String getAvatarPath() {
        return getProfile() + "/avatar";
    }

    public static String getDownloadPath() {
        return getProfile() + "/download/";
    }

    public static String getUploadPath() {
        return getProfile() + "/upload";
    }
}
