package com.geely.gnds.ruoyi.framework.config;

import com.geely.gnds.ruoyi.common.filter.RepeatableFilter;
import com.geely.gnds.ruoyi.common.filter.XssFilter;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.DispatcherType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
/* loaded from: FilterConfig.class */
public class FilterConfig {

    @Value("${xss.enabled}")
    private String enabled;

    @Value("${xss.excludes}")
    private String excludes;

    @Value("${xss.urlPatterns}")
    private String urlPatterns;

    @Bean
    public FilterRegistrationBean xssFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setDispatcherTypes(DispatcherType.REQUEST, new DispatcherType[0]);
        registration.setFilter(new XssFilter());
        registration.addUrlPatterns(StringUtils.split(this.urlPatterns, ConstantEnum.COMMA));
        registration.setName("xssFilter");
        registration.setOrder(Integer.MIN_VALUE);
        Map<String, String> initParameters = new HashMap<>(2);
        initParameters.put("excludes", this.excludes);
        initParameters.put("enabled", this.enabled);
        registration.setInitParameters(initParameters);
        return registration;
    }

    @Bean
    public FilterRegistrationBean someFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new RepeatableFilter());
        registration.addUrlPatterns(new String[]{"/*"});
        registration.setName("repeatableFilter");
        registration.setOrder(30);
        return registration;
    }
}
