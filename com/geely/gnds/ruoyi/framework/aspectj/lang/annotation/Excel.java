package com.geely.gnds.ruoyi.framework.aspectj.lang.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
/* loaded from: Excel.class */
public @interface Excel {
    String name() default "";

    String dateFormat() default "";

    String readConverterExp() default "";

    ColumnType cellType() default ColumnType.STRING;

    double height() default 14.0d;

    double width() default 16.0d;

    String suffix() default "";

    String defaultValue() default "";

    String prompt() default "";

    String[] combo() default {};

    boolean isExport() default true;

    String targetAttr() default "";

    Type type() default Type.ALL;

    /* loaded from: Excel$Type.class */
    public enum Type {
        ALL(0),
        EXPORT(1),
        IMPORT(2);

        private final int value;

        Type(int value) {
            this.value = value;
        }

        public int x() {
            return this.value;
        }
    }

    /* loaded from: Excel$ColumnType.class */
    public enum ColumnType {
        NUMERIC(0),
        STRING(1);

        private final int value;

        ColumnType(int value) {
            this.value = value;
        }

        public int x() {
            return this.value;
        }
    }
}
