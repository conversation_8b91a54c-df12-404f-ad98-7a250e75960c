package com.geely.gnds.ruoyi.common.filter;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/* loaded from: XssFilter.class */
public class XssFilter implements Filter {
    public List<String> excludes = new ArrayList();
    public boolean enabled = false;

    public void init(FilterConfig filterConfig) throws ServletException {
        String tempExcludes = filterConfig.getInitParameter("excludes");
        String tempEnabled = filterConfig.getInitParameter("enabled");
        if (StringUtils.isNotEmpty(tempExcludes)) {
            String[] url = tempExcludes.split(ConstantEnum.COMMA);
            for (int i = 0; url != null && i < url.length; i++) {
                this.excludes.add(url[i]);
            }
        }
        if (StringUtils.isNotEmpty(tempEnabled)) {
            this.enabled = Boolean.valueOf(tempEnabled).booleanValue();
        }
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;
        if (handleExcludeUrl(req, resp)) {
            chain.doFilter(request, response);
        } else {
            XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper((HttpServletRequest) request);
            chain.doFilter(xssRequest, response);
        }
    }

    private boolean handleExcludeUrl(HttpServletRequest request, HttpServletResponse response) {
        if (!this.enabled) {
            return true;
        }
        if (this.excludes == null || this.excludes.isEmpty()) {
            return false;
        }
        String url = request.getServletPath();
        for (String pattern : this.excludes) {
            Pattern p = Pattern.compile("^" + pattern);
            Matcher m = p.matcher(url);
            if (m.find()) {
                return true;
            }
        }
        return false;
    }

    public void destroy() {
    }
}
