package com.geely.gnds.ruoyi.common.filter;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import java.io.IOException;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

/* loaded from: RepeatableFilter.class */
public class RepeatableFilter implements Filter {
    public static final String APPLICATION_JSON_UTF8_VALUE = "application/json;charset=UTF-8";

    public void init(FilterConfig filterConfig) throws ServletException {
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
        RepeatedlyRequestWrapper repeatedlyRequestWrapper = null;
        if ((request instanceof HttpServletRequest) && StringUtils.equalsAnyIgnoreCase(request.getContentType(), new CharSequence[]{"application/json", "application/json;charset=UTF-8"})) {
            repeatedlyRequestWrapper = new RepeatedlyRequestWrapper((HttpServletRequest) request, response);
        }
        if (null == repeatedlyRequestWrapper) {
            chain.doFilter(request, response);
        } else {
            chain.doFilter(repeatedlyRequestWrapper, response);
        }
    }

    public void destroy() {
    }
}
