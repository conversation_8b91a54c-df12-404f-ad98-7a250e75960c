package com.geely.gnds.ruoyi.common.filter;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.html.EscapeUtil;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import org.apache.commons.io.IOUtils;

/* loaded from: XssHttpServletRequestWrapper.class */
public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {
    public static final String APPLICATION_JSON_UTF8_VALUE = "application/json;charset=UTF-8";

    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    public String[] getParameterValues(String name) {
        String[] values = super.getParameterValues(name);
        if (values != null) {
            int length = values.length;
            String[] escapseValues = new String[length];
            for (int i = 0; i < length; i++) {
                escapseValues[i] = EscapeUtil.clean(values[i]).trim();
            }
            return escapseValues;
        }
        return super.getParameterValues(name);
    }

    public ServletInputStream getInputStream() throws IOException {
        if (!isJsonRequest()) {
            return super.getInputStream();
        }
        String json = IOUtils.toString(super.getInputStream(), "utf-8");
        if (StringUtils.isEmpty(json)) {
            return super.getInputStream();
        }
        final ByteArrayInputStream bis = new ByteArrayInputStream(EscapeUtil.clean(json).trim().getBytes("utf-8"));
        return new ServletInputStream() { // from class: com.geely.gnds.ruoyi.common.filter.XssHttpServletRequestWrapper.1
            public boolean isFinished() {
                return true;
            }

            public boolean isReady() {
                return true;
            }

            public void setReadListener(ReadListener readListener) {
            }

            public int read() throws IOException {
                return bis.read();
            }
        };
    }

    public boolean isJsonRequest() {
        String header = super.getHeader("Content-Type");
        return "application/json".equalsIgnoreCase(header) || "application/json;charset=UTF-8".equalsIgnoreCase(header);
    }
}
