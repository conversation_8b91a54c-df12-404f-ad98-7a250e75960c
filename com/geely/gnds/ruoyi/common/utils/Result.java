package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.framework.web.domain.AjaxResult;
import java.util.HashMap;
import java.util.Map;

/* loaded from: Result.class */
public class Result extends HashMap<String, Object> {
    private static final long serialVersionUID = 1;

    public Result() {
        put(AjaxResult.gA, (Object) 0);
        put(AjaxResult.gB, "操作成功");
    }

    public static Result error() {
        return error(1, "操作失败");
    }

    public static Result error(String msg) {
        return error(HttpStatus.ERROR, msg);
    }

    public static Result error(int code, String msg) {
        Result r = new Result();
        r.put(AjaxResult.gA, (Object) Integer.valueOf(code));
        r.put(AjaxResult.gB, (Object) msg);
        return r;
    }

    public static Result ok(String msg) {
        Result r = new Result();
        r.put(AjaxResult.gB, (Object) msg);
        return r;
    }

    public static Result ok(Map<String, Object> map) {
        Result r = new Result();
        r.putAll(map);
        return r;
    }

    public static Result ok() {
        return new Result();
    }

    @Override // java.util.HashMap, java.util.AbstractMap, java.util.Map
    public Result put(String key, Object value) {
        super.put((Result) key, (String) value);
        return this;
    }
}
