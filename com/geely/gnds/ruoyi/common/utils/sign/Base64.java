package com.geely.gnds.ruoyi.common.utils.sign;

import com.geely.gnds.tester.enums.ConstantEnum;

/* loaded from: Base64.class */
public final class Base64 {
    private static final int LOOKUPLENGTH = 64;
    private static final int TWENTYFOURBITGROUP = 24;
    private static final int EIGHTBIT = 8;
    private static final int SIXTEENBIT = 16;
    private static final int FOURBYTE = 4;
    private static final int SIGN = -128;
    private static final char PAD = '=';
    private static final int BASELENGTH = 128;
    private static final byte[] BASE64_ALPHABET = new byte[BASELENGTH];
    private static final char[] LOOKUP_BASE64_ALPHABET = new char[64];

    static {
        for (int i = 0; i < BASELENGTH; i++) {
            BASE64_ALPHABET[i] = -1;
        }
        for (int i2 = 90; i2 >= 65; i2--) {
            BASE64_ALPHABET[i2] = (byte) (i2 - 65);
        }
        for (int i3 = 122; i3 >= 97; i3--) {
            BASE64_ALPHABET[i3] = (byte) ((i3 - 97) + 26);
        }
        for (int i4 = ConstantEnum.NINE.intValue(); i4 >= ConstantEnum.ZERO.intValue(); i4--) {
            BASE64_ALPHABET[i4] = (byte) ((i4 - ConstantEnum.ZERO.intValue()) + 52);
        }
        BASE64_ALPHABET[43] = 62;
        BASE64_ALPHABET[47] = 63;
        for (int i5 = 0; i5 <= 25; i5++) {
            LOOKUP_BASE64_ALPHABET[i5] = (char) (65 + i5);
        }
        int i6 = 26;
        int j = 0;
        while (i6 <= 51) {
            LOOKUP_BASE64_ALPHABET[i6] = (char) (97 + j);
            i6++;
            j++;
        }
        int i7 = 52;
        int j2 = 0;
        while (i7 <= PAD) {
            LOOKUP_BASE64_ALPHABET[i7] = (char) (48 + j2);
            i7++;
            j2++;
        }
        LOOKUP_BASE64_ALPHABET[62] = '+';
        LOOKUP_BASE64_ALPHABET[63] = '/';
    }

    private static boolean isWhiteSpace(char octect) {
        return octect == ' ' || octect == '\r' || octect == '\n' || octect == '\t';
    }

    private static boolean isPad(char octect) {
        return octect == PAD;
    }

    private static boolean isData(char octect) {
        return octect < BASELENGTH && BASE64_ALPHABET[octect] != -1;
    }

    public static String encode(byte[] binaryData) {
        if (binaryData == null) {
            return null;
        }
        int lengthDataBits = binaryData.length * 8;
        if (lengthDataBits == 0) {
            return "";
        }
        int fewerThan24bits = lengthDataBits % TWENTYFOURBITGROUP;
        int numberTriplets = lengthDataBits / TWENTYFOURBITGROUP;
        int numberQuartet = fewerThan24bits != 0 ? numberTriplets + 1 : numberTriplets;
        char[] encodedData = new char[numberQuartet * 4];
        int encodedIndex = 0;
        int dataIndex = 0;
        for (int i = 0; i < numberTriplets; i++) {
            int i2 = dataIndex;
            int dataIndex2 = dataIndex + 1;
            byte b1 = binaryData[i2];
            int dataIndex3 = dataIndex2 + 1;
            byte b2 = binaryData[dataIndex2];
            dataIndex = dataIndex3 + 1;
            byte b3 = binaryData[dataIndex3];
            byte l = (byte) (b2 & 15);
            byte k = (byte) (b1 & 3);
            byte val1 = (b1 & SIGN) == 0 ? (byte) (b1 >> 2) : (byte) ((b1 >> 2) ^ 192);
            byte val2 = (b2 & SIGN) == 0 ? (byte) (b2 >> 4) : (byte) ((b2 >> 4) ^ 240);
            byte val3 = (byte) ((b3 & SIGN) == 0 ? b3 >> 6 : (b3 >> 6) ^ 252);
            int i3 = encodedIndex;
            int encodedIndex2 = encodedIndex + 1;
            encodedData[i3] = LOOKUP_BASE64_ALPHABET[val1];
            int encodedIndex3 = encodedIndex2 + 1;
            encodedData[encodedIndex2] = LOOKUP_BASE64_ALPHABET[val2 | (k << 4)];
            int encodedIndex4 = encodedIndex3 + 1;
            encodedData[encodedIndex3] = LOOKUP_BASE64_ALPHABET[(l << 2) | val3];
            encodedIndex = encodedIndex4 + 1;
            encodedData[encodedIndex4] = LOOKUP_BASE64_ALPHABET[b3 & 63];
        }
        if (fewerThan24bits == 8) {
            byte b12 = binaryData[dataIndex];
            byte k2 = (byte) (b12 & 3);
            byte val12 = (b12 & SIGN) == 0 ? (byte) (b12 >> 2) : (byte) ((b12 >> 2) ^ 192);
            int i4 = encodedIndex;
            int encodedIndex5 = encodedIndex + 1;
            encodedData[i4] = LOOKUP_BASE64_ALPHABET[val12];
            int encodedIndex6 = encodedIndex5 + 1;
            encodedData[encodedIndex5] = LOOKUP_BASE64_ALPHABET[k2 << 4];
            int encodedIndex7 = encodedIndex6 + 1;
            encodedData[encodedIndex6] = '=';
            int i5 = encodedIndex7 + 1;
            encodedData[encodedIndex7] = '=';
        } else if (fewerThan24bits == 16) {
            byte b13 = binaryData[dataIndex];
            byte b22 = binaryData[dataIndex + 1];
            byte l2 = (byte) (b22 & 15);
            byte k3 = (byte) (b13 & 3);
            byte val13 = (b13 & SIGN) == 0 ? (byte) (b13 >> 2) : (byte) ((b13 >> 2) ^ 192);
            byte val22 = (b22 & SIGN) == 0 ? (byte) (b22 >> 4) : (byte) ((b22 >> 4) ^ 240);
            int i6 = encodedIndex;
            int encodedIndex8 = encodedIndex + 1;
            encodedData[i6] = LOOKUP_BASE64_ALPHABET[val13];
            int encodedIndex9 = encodedIndex8 + 1;
            encodedData[encodedIndex8] = LOOKUP_BASE64_ALPHABET[val22 | (k3 << 4)];
            int encodedIndex10 = encodedIndex9 + 1;
            encodedData[encodedIndex9] = LOOKUP_BASE64_ALPHABET[l2 << 2];
            int i7 = encodedIndex10 + 1;
            encodedData[encodedIndex10] = '=';
        }
        return new String(encodedData);
    }

    public static byte[] decode(String encoded) {
        if (encoded == null) {
            return null;
        }
        char[] base64Data = encoded.toCharArray();
        int len = removeWhiteSpace(base64Data);
        if (len % 4 != 0) {
            return null;
        }
        int numberQuadruple = len / 4;
        if (numberQuadruple == 0) {
            return new byte[0];
        }
        int i = 0;
        int encodedIndex = 0;
        int dataIndex = 0;
        byte[] decodedData = new byte[numberQuadruple * 3];
        while (i < numberQuadruple - 1) {
            int i2 = dataIndex;
            int dataIndex2 = dataIndex + 1;
            char d1 = base64Data[i2];
            if (!isData(d1)) {
                return null;
            }
            int dataIndex3 = dataIndex2 + 1;
            char d2 = base64Data[dataIndex2];
            if (isData(d2)) {
                int dataIndex4 = dataIndex3 + 1;
                char d3 = base64Data[dataIndex3];
                if (!isData(d3)) {
                    return null;
                }
                dataIndex = dataIndex4 + 1;
                char d4 = base64Data[dataIndex4];
                if (!isData(d4)) {
                    return null;
                }
                byte b1 = BASE64_ALPHABET[d1];
                byte b2 = BASE64_ALPHABET[d2];
                byte b3 = BASE64_ALPHABET[d3];
                byte b4 = BASE64_ALPHABET[d4];
                int i3 = encodedIndex;
                int encodedIndex2 = encodedIndex + 1;
                decodedData[i3] = (byte) ((b1 << 2) | (b2 >> 4));
                int encodedIndex3 = encodedIndex2 + 1;
                decodedData[encodedIndex2] = (byte) (((b2 & 15) << 4) | ((b3 >> 2) & 15));
                encodedIndex = encodedIndex3 + 1;
                decodedData[encodedIndex3] = (byte) ((b3 << 6) | b4);
                i++;
            } else {
                return null;
            }
        }
        int i4 = dataIndex;
        int dataIndex5 = dataIndex + 1;
        char d12 = base64Data[i4];
        if (!isData(d12)) {
            return null;
        }
        int dataIndex6 = dataIndex5 + 1;
        char d22 = base64Data[dataIndex5];
        if (!isData(d22)) {
            return null;
        }
        byte b12 = BASE64_ALPHABET[d12];
        byte b22 = BASE64_ALPHABET[d22];
        int dataIndex7 = dataIndex6 + 1;
        char d32 = base64Data[dataIndex6];
        int i5 = dataIndex7 + 1;
        char d42 = base64Data[dataIndex7];
        if (!isData(d32) || !isData(d42)) {
            return handle(decodedData, b12, b22, d32, d42, i, encodedIndex);
        }
        byte b32 = BASE64_ALPHABET[d32];
        byte b42 = BASE64_ALPHABET[d42];
        int i6 = encodedIndex;
        int encodedIndex4 = encodedIndex + 1;
        decodedData[i6] = (byte) ((b12 << 2) | (b22 >> 4));
        int encodedIndex5 = encodedIndex4 + 1;
        decodedData[encodedIndex4] = (byte) (((b22 & 15) << 4) | ((b32 >> 2) & 15));
        int i7 = encodedIndex5 + 1;
        decodedData[encodedIndex5] = (byte) ((b32 << 6) | b42);
        return decodedData;
    }

    private static byte[] handle(byte[] decodedData, byte b1, byte b2, char d3, char d4, int i, int encodedIndex) {
        if (isPad(d3) && isPad(d4)) {
            if ((b2 & 15) != 0) {
                return null;
            }
            byte[] tmp = new byte[(i * 3) + 1];
            System.arraycopy(decodedData, 0, tmp, 0, i * 3);
            tmp[encodedIndex] = (byte) ((b1 << 2) | (b2 >> 4));
            return tmp;
        }
        if (!isPad(d3) && isPad(d4)) {
            byte b3 = BASE64_ALPHABET[d3];
            if ((b3 & 3) != 0) {
                return null;
            }
            byte[] tmp2 = new byte[(i * 3) + 2];
            System.arraycopy(decodedData, 0, tmp2, 0, i * 3);
            tmp2[encodedIndex] = (byte) ((b1 << 2) | (b2 >> 4));
            tmp2[encodedIndex + 1] = (byte) (((b2 & 15) << 4) | ((b3 >> 2) & 15));
            return tmp2;
        }
        return null;
    }

    private static int removeWhiteSpace(char[] data) {
        if (data == null) {
            return 0;
        }
        int newSize = 0;
        int len = data.length;
        for (int i = 0; i < len; i++) {
            if (!isWhiteSpace(data[i])) {
                int i2 = newSize;
                newSize++;
                data[i2] = data[i];
            }
        }
        return newSize;
    }
}
