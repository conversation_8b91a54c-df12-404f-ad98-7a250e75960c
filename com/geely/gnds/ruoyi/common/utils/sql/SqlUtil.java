package com.geely.gnds.ruoyi.common.utils.sql;

import com.geely.gnds.ruoyi.common.utils.StringUtils;

/* loaded from: SqlUtil.class */
public class SqlUtil {
    public static String SQL_PATTERN = "[a-zA-Z0-9_\\ \\,]+";

    public static String escapeOrderBySql(String value) {
        if (StringUtils.isNotEmpty(value) && !isValidOrderBySql(value)) {
            return "";
        }
        return value;
    }

    public static boolean isValidOrderBySql(String value) {
        return value.matches(SQL_PATTERN);
    }
}
