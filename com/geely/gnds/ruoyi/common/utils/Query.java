package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.LinkedHashMap;
import java.util.Map;

/* loaded from: Query.class */
public class Query extends LinkedHashMap<String, Object> {
    private static final long serialVersionUID = 1;
    private int offset;
    private int limit;

    public Query(Map<String, Object> params) {
        putAll(params);
        if (params.containsKey(ConstantEnum.OFFSET) && params.containsKey(ConstantEnum.LIMIT)) {
            this.offset = Integer.parseInt(params.get(ConstantEnum.OFFSET).toString());
            this.limit = Integer.parseInt(params.get(ConstantEnum.LIMIT).toString());
            put(ConstantEnum.OFFSET, Integer.valueOf(this.offset));
            put(ConstantEnum.PAGE, Integer.valueOf((this.offset / this.limit) + 1));
            put(ConstantEnum.LIMIT, Integer.valueOf(this.limit));
        }
    }

    public int getOffset() {
        return this.offset;
    }

    public void setOffset(int offset) {
        put(ConstantEnum.OFFSET, Integer.valueOf(offset));
    }

    public int getLimit() {
        return this.limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }
}
