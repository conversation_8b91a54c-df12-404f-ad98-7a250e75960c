package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.project.system.domain.SysDictData;
import java.util.Collection;
import java.util.List;

/* loaded from: DictUtils.class */
public class DictUtils {
    public static void setDictCache(String key, List<SysDictData> dictDatas) {
        ((EhcacheClient) SpringUtils.getBean(EhcacheClient.class)).a(getCacheKey(key), 0, dictDatas);
    }

    public static List<SysDictData> getDictCache(String key) {
        Object cacheObj = ((EhcacheClient) SpringUtils.getBean(EhcacheClient.class)).w(getCacheKey(key));
        if (StringUtils.isNotNull(cacheObj)) {
            List<SysDictData> dictDatas = (List) StringUtils.cast(cacheObj);
            return dictDatas;
        }
        return null;
    }

    public static void clearDictCache() {
        Collection<String> keys = ((EhcacheClient) SpringUtils.getBean(EhcacheClient.class)).x("sys_dict:*");
        for (String key : keys) {
            ((EhcacheClient) SpringUtils.getBean(EhcacheClient.class)).y(key);
        }
    }

    public static String getCacheKey(String configKey) {
        return Constants.SYS_DICT_KEY + configKey;
    }
}
