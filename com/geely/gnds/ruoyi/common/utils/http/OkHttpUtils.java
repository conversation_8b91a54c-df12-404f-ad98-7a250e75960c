package com.geely.gnds.ruoyi.common.utils.http;

import com.alibaba.fastjson.JSON;
import com.geely.gnds.ruoyi.common.utils.http.secret.OkHttpInterceptor;
import java.io.IOException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: OkHttpUtils.class */
public class OkHttpUtils {
    private static final Logger log;
    private static volatile OkHttpClient okHttpClient;
    private static volatile Semaphore semaphore;
    private Map<String, String> headerMap;
    private Map<String, String> paramMap;
    private String url;
    private Request.Builder request;
    static final /* synthetic */ boolean $assertionsDisabled;

    /* loaded from: OkHttpUtils$ICallBack.class */
    public interface ICallBack {
        void onSuccessful(Call call, String str);

        void onFailure(Call call, String str);
    }

    static {
        $assertionsDisabled = !OkHttpUtils.class.desiredAssertionStatus();
        log = LoggerFactory.getLogger(OkHttpUtils.class);
        okHttpClient = null;
        semaphore = null;
    }

    private OkHttpUtils(int time) {
        if (okHttpClient == null) {
            synchronized (OkHttpUtils.class) {
                if (okHttpClient == null) {
                    log.info("开始初始化 OkHttpClient");
                    TrustManager[] trustManagers = buildTrustManagers();
                    okHttpClient = new OkHttpClient.Builder().dns(HttpDns.getDns()).addInterceptor(new OkHttpInterceptor()).connectTimeout(8L, TimeUnit.SECONDS).writeTimeout(10L, TimeUnit.SECONDS).readTimeout(time, TimeUnit.SECONDS).callTimeout(time, TimeUnit.SECONDS).sslSocketFactory(createSslSocketFactory(trustManagers), (X509TrustManager) trustManagers[0]).hostnameVerifier((hostName, session) -> {
                        return true;
                    }).retryOnConnectionFailure(true).build();
                    addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
                }
            }
            return;
        }
        if (8 != time) {
            TrustManager[] trustManagers2 = buildTrustManagers();
            okHttpClient = new OkHttpClient.Builder().dns(HttpDns.getDns()).addInterceptor(new OkHttpInterceptor()).connectTimeout(8L, TimeUnit.SECONDS).writeTimeout(10L, TimeUnit.SECONDS).readTimeout(time, TimeUnit.SECONDS).callTimeout(time, TimeUnit.SECONDS).sslSocketFactory(createSslSocketFactory(trustManagers2), (X509TrustManager) trustManagers2[0]).hostnameVerifier((hostName2, session2) -> {
                return true;
            }).retryOnConnectionFailure(true).build();
            addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
        } else {
            TrustManager[] trustManagers3 = buildTrustManagers();
            okHttpClient = new OkHttpClient.Builder().dns(HttpDns.getDns()).addInterceptor(new OkHttpInterceptor()).connectTimeout(8L, TimeUnit.SECONDS).writeTimeout(10L, TimeUnit.SECONDS).readTimeout(8, TimeUnit.SECONDS).callTimeout(8, TimeUnit.SECONDS).sslSocketFactory(createSslSocketFactory(trustManagers3), (X509TrustManager) trustManagers3[0]).hostnameVerifier((hostName3, session3) -> {
                return true;
            }).retryOnConnectionFailure(true).build();
            addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36");
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static Semaphore getSemaphoreInstance() {
        synchronized (OkHttpUtils.class) {
            if (semaphore == null) {
                semaphore = new Semaphore(0);
            }
        }
        return semaphore;
    }

    public static OkHttpUtils builder(int time) {
        return new OkHttpUtils(time);
    }

    public OkHttpUtils url(String url) {
        this.url = url;
        return this;
    }

    public OkHttpUtils addParam(String key, String value) {
        if (this.paramMap == null) {
            this.paramMap = new LinkedHashMap();
        }
        this.paramMap.put(key, value);
        return this;
    }

    public OkHttpUtils addParam(Map<String, String> param) {
        if (this.paramMap == null) {
            this.paramMap = new LinkedHashMap();
        }
        Optional optionalOfNullable = Optional.ofNullable(param);
        Map<String, String> map = this.paramMap;
        map.getClass();
        optionalOfNullable.ifPresent(map::putAll);
        return this;
    }

    public OkHttpUtils addHeader(String key, String value) {
        if (this.headerMap == null) {
            this.headerMap = new LinkedHashMap();
        }
        this.headerMap.put(key, value);
        return this;
    }

    public OkHttpUtils addHeader(Map<String, String> headParam) {
        if (this.headerMap == null) {
            this.headerMap = new LinkedHashMap();
        }
        Optional optionalOfNullable = Optional.ofNullable(headParam);
        Map<String, String> map = this.headerMap;
        map.getClass();
        optionalOfNullable.ifPresent(map::putAll);
        return this;
    }

    public OkHttpUtils get() {
        this.request = new Request.Builder().get();
        StringBuilder urlBuilder = new StringBuilder(this.url);
        if (this.paramMap != null) {
            urlBuilder.append("?");
            try {
                for (Map.Entry<String, String> entry : this.paramMap.entrySet()) {
                    urlBuilder.append(URLEncoder.encode(entry.getKey(), "utf-8")).append("=").append(URLEncoder.encode(entry.getValue(), "utf-8")).append("&");
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            urlBuilder.deleteCharAt(urlBuilder.length() - 1);
        }
        this.request.url(urlBuilder.toString());
        return this;
    }

    public OkHttpUtils post(boolean isJsonPost) {
        RequestBody requestBody;
        if (isJsonPost) {
            String json = "";
            if (this.paramMap != null) {
                json = JSON.toJSONString(this.paramMap);
            }
            MediaType mediaType = MediaType.Companion.parse("application/json;charset=utf-8");
            requestBody = RequestBody.Companion.create(json, mediaType);
        } else {
            FormBody.Builder formBody = new FormBody.Builder();
            if (this.paramMap != null) {
                Map<String, String> map = this.paramMap;
                formBody.getClass();
                map.forEach(formBody::add);
            }
            requestBody = formBody.build();
        }
        this.request = new Request.Builder().post(requestBody).url(this.url);
        return this;
    }

    public String sync() throws IOException {
        setHeader(this.request);
        Response response = okHttpClient.newCall(this.request.build()).execute();
        if ($assertionsDisabled || response.body() != null) {
            return response.body().string();
        }
        throw new AssertionError();
    }

    public String async() throws InterruptedException {
        final StringBuilder buffer = new StringBuilder("");
        setHeader(this.request);
        okHttpClient.newCall(this.request.build()).enqueue(new Callback() { // from class: com.geely.gnds.ruoyi.common.utils.http.OkHttpUtils.1
            static final /* synthetic */ boolean $assertionsDisabled;

            static {
                $assertionsDisabled = !OkHttpUtils.class.desiredAssertionStatus();
            }

            public void onFailure(Call call, IOException e) {
                buffer.append("请求出错：").append(e.getMessage());
            }

            public void onResponse(Call call, Response response) throws IOException {
                if (!$assertionsDisabled && response.body() == null) {
                    throw new AssertionError();
                }
                buffer.append(response.body().string());
                OkHttpUtils.getSemaphoreInstance().release();
            }
        });
        getSemaphoreInstance().acquire();
        return buffer.toString();
    }

    public void async(final ICallBack callBack) {
        setHeader(this.request);
        okHttpClient.newCall(this.request.build()).enqueue(new Callback() { // from class: com.geely.gnds.ruoyi.common.utils.http.OkHttpUtils.2
            static final /* synthetic */ boolean $assertionsDisabled;

            static {
                $assertionsDisabled = !OkHttpUtils.class.desiredAssertionStatus();
            }

            public void onFailure(Call call, IOException e) {
                callBack.onFailure(call, e.getMessage());
            }

            public void onResponse(Call call, Response response) throws IOException {
                if (!$assertionsDisabled && response.body() == null) {
                    throw new AssertionError();
                }
                callBack.onSuccessful(call, response.body().string());
            }
        });
    }

    private void setHeader(Request.Builder request) {
        if (this.headerMap != null) {
            try {
                for (Map.Entry<String, String> entry : this.headerMap.entrySet()) {
                    request.addHeader(entry.getKey(), entry.getValue());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    private static SSLSocketFactory createSslSocketFactory(TrustManager[] trustAllCerts) throws NoSuchAlgorithmException, KeyManagementException {
        SSLSocketFactory ssfFactory = null;
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            ssfFactory = sc.getSocketFactory();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ssfFactory;
    }

    private static TrustManager[] buildTrustManagers() {
        return new TrustManager[]{new X509TrustManager() { // from class: com.geely.gnds.ruoyi.common.utils.http.OkHttpUtils.3
            @Override // javax.net.ssl.X509TrustManager
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override // javax.net.ssl.X509TrustManager
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            @Override // javax.net.ssl.X509TrustManager
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        }};
    }
}
