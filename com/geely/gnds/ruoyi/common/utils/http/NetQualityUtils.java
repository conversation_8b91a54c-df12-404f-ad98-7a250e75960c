package com.geely.gnds.ruoyi.common.utils.http;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.util.TesterThread;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: NetQualityUtils.class */
public class NetQualityUtils {
    private static final Logger log = LoggerFactory.getLogger(NetQualityUtils.class);
    private static boolean runing = false;
    public static boolean timeout = false;
    public static String pingUrl = "https://www.bing.com/";
    public static String pingHost = "www.bing.com";

    private static void checkNetwork(String host) {
        TesterThread testerThread = (TesterThread) SpringUtils.getBean(TesterThread.class);
        testerThread.getPool().execute(() -> {
            try {
                try {
                    checkPing(host);
                    runing = false;
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    runing = false;
                }
            } catch (Throwable th) {
                runing = false;
                throw th;
            }
        });
    }

    public static void checkHttpNetworkStatus(String url) {
        if (!runing) {
            try {
                runing = true;
                log.info("开始检测 {} 的网络质量...", url);
                String host = URLUtil.url(url).getHost();
                checkNetwork(host);
                log.info("开始检测 {} 的网络质量...", pingUrl);
                String host2 = URLUtil.url(pingUrl).getHost();
                checkNetwork(host2);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    private static void checkPing(String host) {
        List<Integer> counts = new ArrayList<>();
        String str = String.format("ping %s -n 10 -w 1000", host);
        List<String> list = RuntimeUtil.execForLines(new String[]{str});
        log.info(CollUtil.join(list, "\n"));
        for (String s : list) {
            if (StrUtil.containsIgnoreCase(s, "TTL=")) {
                String ttl = StrUtil.subAfter(s, "=", true);
                counts.add(Integer.valueOf(Integer.parseInt(ttl)));
            }
        }
        double cnt = counts.stream().mapToInt((v0) -> {
            return v0.intValue();
        }).average().orElse(0.0d);
        if (counts.size() <= 10 && counts.size() > 0) {
            log.info("网络不稳定，丢包率：{}%,{}", BigDecimal.valueOf(10 - counts.size()).divide(new BigDecimal("10"), 2, 4).multiply(new BigDecimal("100")), host);
            log.info(NetQualityLevel.of(cnt).toString());
            if (pingHost.equals(host)) {
                timeout = false;
                return;
            }
            return;
        }
        if (pingHost.equals(host)) {
            timeout = true;
        }
        log.error("请求 {} 网络不通，丢包率: 100%", host);
    }

    public static NetQualityLevel checkNetworkLevel(String url, int pingCount) {
        String host = URLUtil.url(url).getHost();
        List<Integer> counts = new ArrayList<>();
        String str = String.format("ping %s -n %s -w 1000", host, Integer.valueOf(pingCount));
        List<String> list = RuntimeUtil.execForLines(new String[]{str});
        for (String s : list) {
            if (StrUtil.containsIgnoreCase(s, "时间=")) {
                String ttl = StrUtil.subAfter(s, "时间", false);
                String time = ReUtil.getGroup1("(\\d+)(.*)", ttl);
                counts.add(Integer.valueOf(Integer.parseInt(time)));
            } else if (StrUtil.containsIgnoreCase(s, "time=")) {
                String ttl2 = StrUtil.subAfter(s, "time", false);
                String time2 = ReUtil.getGroup1("(\\d+)(.*)", ttl2);
                counts.add(Integer.valueOf(Integer.parseInt(time2)));
            }
        }
        int size = counts.size();
        if (size == 0) {
            NetQualityLevel unknown = NetQualityLevel.getUnknown();
            log.info("检查网络质量:{}", unknown);
            return unknown;
        }
        double lost = (pingCount - size) / pingCount;
        double cnt = counts.stream().mapToInt((v0) -> {
            return v0.intValue();
        }).average().orElse(0.0d);
        NetQualityLevel netQualityLevel = NetQualityLevel.of(BigDecimal.valueOf(cnt).setScale(2, RoundingMode.HALF_UP).doubleValue(), lost);
        log.info("检查网络质量:{}", netQualityLevel);
        return netQualityLevel;
    }
}
