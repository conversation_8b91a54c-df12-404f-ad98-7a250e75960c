package com.geely.gnds.ruoyi.common.utils.http;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/* loaded from: NetQualityLevel.class */
public enum NetQualityLevel {
    EXCELLENT(30, "网络质量极好", "Excellent network quality"),
    GOOD(50, "网络质量好", "Good network quality"),
    MEDIUM(100, "网络质量正常", "Normal network quality"),
    BAD(HttpStatus.SUCCESS, "网络质量差", "Poor network quality"),
    DIE(HttpStatus.ERROR, "网络不稳定", "Network instability"),
    UNKNOWN(1000, "网络异常", "Network exception");

    private int avgPing;
    private String desc;
    private String descEn;
    private double delay;
    private double lost;

    NetQualityLevel(int avgPing, String desc, String descEn) {
        this.avgPing = avgPing;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static NetQualityLevel of(double avgPing) {
        NetQualityLevel netQualityLevel = (NetQualityLevel) Arrays.stream(values()).sorted().filter(x -> {
            return ((double) x.avgPing) >= avgPing;
        }).findFirst().orElse(null);
        return netQualityLevel;
    }

    public static NetQualityLevel of(double avgPing, double lost) {
        NetQualityLevel netQualityLevel = (NetQualityLevel) Arrays.stream(values()).sorted().filter(x -> {
            return ((double) x.avgPing) >= avgPing;
        }).findFirst().orElse(null);
        netQualityLevel.setDelay(avgPing);
        netQualityLevel.setLost(lost);
        return netQualityLevel;
    }

    public static NetQualityLevel getUnknown() {
        NetQualityLevel unknown = UNKNOWN;
        unknown.setLost(100.0d);
        unknown.setDelay(1000.0d);
        return UNKNOWN;
    }

    public static NetQualityLevel of2(double avgPing, int pingNum, int pingCounts) {
        if (pingNum == 0) {
            return UNKNOWN;
        }
        NetQualityLevel n = (NetQualityLevel) Arrays.stream(values()).sorted().filter(x -> {
            return ((double) x.avgPing) >= avgPing;
        }).findFirst().orElse(null);
        Optional.ofNullable(n).ifPresent(s -> {
            s.setDelay(avgPing);
            BigDecimal b = BigDecimal.valueOf(pingNum - pingCounts).divide(new BigDecimal(pingCounts)).setScale(2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            s.setLost(b.doubleValue());
        });
        return n;
    }

    @Override // java.lang.Enum
    public String toString() {
        String language = HttpUtils.getLanguage();
        String descByLanguage = this.desc;
        if (!"zh-CN".equals(language)) {
            descByLanguage = this.descEn;
        }
        return "{\"avgPing\":" + this.avgPing + ", \"desc\":\"" + descByLanguage + "\", \"delay\":" + this.delay + ", \"lost\":" + this.lost + '}';
    }

    public Map<String, Object> toMap() {
        Map map = new HashMap(2);
        map.put("avgPing", Integer.valueOf(this.avgPing));
        map.put("desc", this.desc);
        return map;
    }

    public double getDelay() {
        return this.delay;
    }

    public void setDelay(double delay) {
        this.delay = delay;
    }

    public double getLost() {
        return this.lost;
    }

    public void setLost(double lost) {
        this.lost = lost;
    }
}
