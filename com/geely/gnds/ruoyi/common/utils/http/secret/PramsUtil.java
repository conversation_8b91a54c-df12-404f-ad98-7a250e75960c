package com.geely.gnds.ruoyi.common.utils.http.secret;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import java.io.IOException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okio.Buffer;

/* loaded from: PramsUtil.class */
public class PramsUtil {
    public static String getBodyParams(Request originalRequest) throws IOException {
        RequestBody body = originalRequest.body();
        if (body == null) {
            return "";
        }
        Buffer buffer = new Buffer();
        body.writeTo(buffer);
        return buffer.readUtf8();
    }

    public static String getFormParams(Request originalRequest) {
        Map<String, Object> params = new HashMap<>();
        FormBody formBodyBody = originalRequest.body();
        if (formBodyBody instanceof FormBody) {
            for (int i = 0; i < formBodyBody.size(); i++) {
                params.put(formBodyBody.encodedName(i), formBodyBody.encodedValue(i));
            }
        }
        TreeMap<String, Object> treeMap = MapUtil.newTreeMap(params, Comparator.naturalOrder());
        return JSONUtil.toJsonStr(treeMap);
    }

    public static String getQueryParams(Request originalRequest) {
        HashMap<String, String> hashMap = (HashMap) originalRequest.url().queryParameterNames().stream().collect(HashMap::new, (m, v) -> {
        }, (v0, v1) -> {
            v0.putAll(v1);
        });
        TreeMap<String, String> treeMap = MapUtil.newTreeMap(hashMap, Comparator.naturalOrder());
        return JSONUtil.toJsonStr(treeMap);
    }
}
