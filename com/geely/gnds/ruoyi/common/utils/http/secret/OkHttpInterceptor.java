package com.geely.gnds.ruoyi.common.utils.http.secret;

import cn.hutool.http.Method;
import com.geely.gnds.tester.common.EncryptContext;
import com.geely.gnds.tester.common.SecClient;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: OkHttpInterceptor.class */
public class OkHttpInterceptor implements Interceptor {
    private static final Logger log = LoggerFactory.getLogger(OkHttpInterceptor.class);
    private final MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
    private static final String ENCRYPT_HEADER = "SECRET_RESPONSE";

    @NotNull
    public Response intercept(Interceptor.Chain chain) {
        Request originalRequest = chain.request();
        try {
            Response response = chain.proceed(convertRequest(originalRequest));
            try {
                String responseBody = convertResponse(response);
                return response.newBuilder().body(ResponseBody.create(responseBody, response.body().contentType())).build();
            } catch (Exception e) {
                log.error("请求异常,解密失败：{}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        } catch (Exception e2) {
            log.error("请求异常,加密失败：{}", e2.getMessage(), e2);
            throw new RuntimeException(e2);
        }
    }

    private Request convertRequest(Request request) throws Exception {
        if (request.body() != null && request.method().equalsIgnoreCase(Method.POST.toString())) {
            boolean encryptionEnabled = EncryptContext.isEncryptRequest();
            String bodyParams = PramsUtil.getBodyParams(request);
            if (encryptionEnabled) {
                bodyParams = SecClient.encrypt(bodyParams).getData();
            }
            RequestBody encryptedBody = RequestBody.create(bodyParams, this.mediaType);
            request = request.newBuilder().method(request.method(), encryptedBody).headers(SignUtil.signPost(request, encryptionEnabled, EncryptContext.isEncryptResponse())).build();
        }
        return request;
    }

    private String convertResponse(Response response) throws Exception {
        String body = null;
        if (response != null) {
            String encrypted = response.header(ENCRYPT_HEADER);
            if (encrypted != null) {
                String bodyData = response.body().string();
                log.info("云端加密返回:{}", bodyData);
                SecClient.EncData decrypted = SecClient.decrypt(bodyData);
                if (!decrypted.isSuccess()) {
                    log.error("解密失败：{}", decrypted.getData());
                }
                body = decrypted.getData();
            } else {
                body = response.body().string();
            }
        }
        return body;
    }
}
