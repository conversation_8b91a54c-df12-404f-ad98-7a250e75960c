package com.geely.gnds.ruoyi.common.utils.http.secret;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.common.SecClient;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.IOException;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import okhttp3.Headers;
import okhttp3.Request;
import org.springframework.util.MultiValueMap;

/* loaded from: SignUtil.class */
public class SignUtil {
    private static String signPost(String queryParam, String bodyJson, String requestId, String dateTimestamp) {
        String str = "POST" + queryParam + StrUtil.nullToEmpty(bodyJson) + requestId + dateTimestamp;
        return DigestUtil.sha256Hex(str);
    }

    public static Headers signPost(Request originalRequest, boolean encryptionEnabled, boolean encryptionResponse) throws IOException {
        Map<String, String> headerMap = new HashMap<>();
        if (originalRequest.method().equalsIgnoreCase(Method.POST.toString())) {
            long timestamp = System.currentTimeMillis();
            String requestId = IdUtil.fastSimpleUUID();
            headerMap.put("timestamp", String.valueOf(timestamp));
            headerMap.put("requestId", requestId);
            if (encryptionEnabled) {
                headerMap.put("SECRET_REQUEST", "true");
            }
            if (encryptionResponse) {
                headerMap.put("SECRET_RESPONSE", "true");
            }
            String sign = signPost(PramsUtil.getQueryParams(originalRequest), PramsUtil.getBodyParams(originalRequest), requestId, String.valueOf(timestamp));
            headerMap.put("X-Client-Signature", sign);
            return originalRequest.headers().newBuilder().addAll(Headers.of(headerMap).newBuilder().build()).build();
        }
        return originalRequest.headers();
    }

    private static Map<String, String> getCustomHeaders(Request originalRequest) {
        Map<String, String> headers = new HashMap<>();
        Headers originalHeaders = originalRequest.headers();
        Optional.ofNullable(originalHeaders.get("IP")).ifPresent(s -> {
        });
        Optional.ofNullable(originalHeaders.get("language")).ifPresent(s2 -> {
        });
        Optional.ofNullable(originalHeaders.get("Ota-Region")).ifPresent(s3 -> {
        });
        Optional.ofNullable(originalHeaders.get("nonce")).ifPresent(s4 -> {
        });
        Optional.ofNullable(originalHeaders.get("Cloud-Id")).ifPresent(s5 -> {
        });
        Optional.ofNullable(originalHeaders.get("testerId")).ifPresent(s6 -> {
        });
        headers.put("version", "********");
        headers.put("machineId", SecClient.generateMachineID());
        headers.put("User-Agent", getUserAgent());
        headers.put(ConstantEnum.TENANTCODE_STR, String.valueOf(TokenManager.getTesterTenantCode()));
        return headers;
    }

    private static String getUserAgent() {
        String javaVersion = System.getProperty("java.version");
        String javaRuntime = System.getProperty("java.runtime.name");
        String javaVm = System.getProperty("java.vm.name");
        String osName = System.getProperty("os.name");
        String osVersion = System.getProperty("os.version");
        String osArch = System.getProperty("os.arch");
        return String.format("GNDS/%s (%s %s; %s) Java/%s (%s; %s)", "********", osName, osVersion, osArch, javaVersion, javaRuntime, javaVm);
    }

    private static String getParamMap(MultiValueMap<String, String> queryParams) {
        TreeMap<String, String> treeMap = MapUtil.newTreeMap(queryParams.toSingleValueMap(), Comparator.naturalOrder());
        return JSONUtil.toJsonStr(treeMap);
    }
}
