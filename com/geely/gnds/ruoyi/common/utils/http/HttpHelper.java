package com.geely.gnds.ruoyi.common.utils.http;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletRequest;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: HttpHelper.class */
public class HttpHelper {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpHelper.class);

    public static String getBodyString(ServletRequest request) throws IOException {
        ServletInputStream inputStream;
        Throwable th;
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = null;
        try {
            try {
                inputStream = request.getInputStream();
                th = null;
            } catch (IOException e) {
                LOGGER.warn("getBodyString出现问题！");
                if (0 != 0) {
                    try {
                        reader.close();
                    } catch (IOException e2) {
                        LOGGER.error(ExceptionUtils.getFullStackTrace(e2));
                    }
                }
            }
            try {
                try {
                    BufferedReader reader2 = new BufferedReader(new InputStreamReader((InputStream) inputStream, Charset.forName("UTF-8")));
                    while (true) {
                        String line = reader2.readLine();
                        if (line == null) {
                            break;
                        }
                        sb.append(line);
                    }
                    if (inputStream != null) {
                        if (0 != 0) {
                            try {
                                inputStream.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            inputStream.close();
                        }
                    }
                    if (reader2 != null) {
                        try {
                            reader2.close();
                        } catch (IOException e3) {
                            LOGGER.error(ExceptionUtils.getFullStackTrace(e3));
                        }
                    }
                    return sb.toString();
                } catch (Throwable th3) {
                    if (inputStream != null) {
                        if (th != null) {
                            try {
                                inputStream.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            inputStream.close();
                        }
                    }
                    throw th3;
                }
            } finally {
            }
        } catch (Throwable th5) {
            if (0 != 0) {
                try {
                    reader.close();
                } catch (IOException e4) {
                    LOGGER.error(ExceptionUtils.getFullStackTrace(e4));
                }
            }
            throw th5;
        }
    }
}
