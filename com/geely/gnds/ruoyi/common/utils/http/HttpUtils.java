package com.geely.gnds.ruoyi.common.utils.http;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.CloseUtil;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.common.utils.ip.IpUtils;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.common.TesterContext;
import com.geely.gnds.tester.config.MDCTraceUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.CloudInterfaceException;
import com.geely.gnds.tester.socket.GndsWebSocket;
import com.geely.gnds.tester.util.AesUtils;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.OpenApiSignUtils;
import com.google.common.collect.Maps;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/* loaded from: HttpUtils.class */
public class HttpUtils {
    private static final String METHOD_SECRET = "gnds.getSecret";
    private static final String METHOD_LOGIN = "gnds.login";
    private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);
    private static JSONArray checkList = JSONArray.parseArray("[\"gnds.getReloadSeqList\",\"gnds.getTargetSoftwareVehicles\"]");

    public static String sendNewGet(String url, Map param) {
        try {
            return OkHttpUtils.builder(30).url(url).addParam(param).get().sync();
        } catch (IOException e) {
            log.error("调用HttpsUtil.sendGet Exception, url={}", url, e);
            return null;
        }
    }

    public static byte[] sendGet(String url) throws IOException {
        StringBuilder result = new StringBuilder();
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        try {
            log.info("sendGet - {}", url);
            URL realUrl = new URL(url);
            URLConnection connection = realUrl.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            byte[] buffer = new byte[DoipUtil.MAX_BYTE_ARRAY_SIZE];
            while (true) {
                int count = inputStream.read(buffer);
                if (count == -1) {
                    break;
                }
                swapStream.write(buffer, 0, count);
            }
            log.info("recv - {}", result);
        } catch (ConnectException e) {
            log.error("调用HttpUtils.sendGet ConnectException, url=" + url, e);
        } catch (SocketTimeoutException e2) {
            log.error("调用HttpUtils.sendGet SocketTimeoutException, url=" + url, e2);
        } catch (IOException e3) {
            log.error("调用HttpUtils.sendGet IOException, url=" + url, e3);
        } catch (Exception e4) {
            log.error("调用HttpsUtil.sendGet Exception, url=" + url, e4);
        }
        return swapStream.toByteArray();
    }

    public static String sendGet(String url, String param, String contentType) throws IOException {
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        try {
            try {
                try {
                    try {
                        try {
                            String urlNameString = url + "?" + param;
                            log.info("sendGet - {}", urlNameString);
                            URL realUrl = new URL(urlNameString);
                            URLConnection connection = realUrl.openConnection();
                            connection.setRequestProperty("accept", "*/*");
                            connection.setRequestProperty("connection", "Keep-Alive");
                            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
                            connection.connect();
                            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), contentType));
                            while (true) {
                                String line = in.readLine();
                                if (line == null) {
                                    break;
                                }
                                result.append(line);
                            }
                            log.info("recv - {}", result);
                            if (in != null) {
                                try {
                                    in.close();
                                } catch (Exception ex) {
                                    log.error("调用in.close Exception, url=" + url + ",param=" + param, ex);
                                }
                            }
                        } catch (ConnectException e) {
                            log.error("调用HttpUtils.sendGet ConnectException, url=" + url + ",param=" + param, e);
                            if (in != null) {
                                try {
                                    in.close();
                                } catch (Exception ex2) {
                                    log.error("调用in.close Exception, url=" + url + ",param=" + param, ex2);
                                }
                            }
                        }
                    } catch (IOException e2) {
                        log.error("调用HttpUtils.sendGet IOException, url=" + url + ",param=" + param, e2);
                        if (in != null) {
                            try {
                                in.close();
                            } catch (Exception ex3) {
                                log.error("调用in.close Exception, url=" + url + ",param=" + param, ex3);
                            }
                        }
                    }
                } catch (SocketTimeoutException e3) {
                    log.error("调用HttpUtils.sendGet SocketTimeoutException, url=" + url + ",param=" + param, e3);
                    if (in != null) {
                        try {
                            in.close();
                        } catch (Exception ex4) {
                            log.error("调用in.close Exception, url=" + url + ",param=" + param, ex4);
                        }
                    }
                }
            } catch (Exception e4) {
                log.error("调用HttpsUtil.sendGet Exception, url=" + url + ",param=" + param, e4);
                if (in != null) {
                    try {
                        in.close();
                    } catch (Exception ex5) {
                        log.error("调用in.close Exception, url=" + url + ",param=" + param, ex5);
                    }
                }
            }
            return result.toString();
        } catch (Throwable th) {
            if (in != null) {
                try {
                    in.close();
                } catch (Exception ex6) {
                    log.error("调用in.close Exception, url=" + url + ",param=" + param, ex6);
                    throw th;
                }
            }
            throw th;
        }
    }

    public static String sendNewPost(String url, Map param) throws Exception {
        return sendOkHttpPost(url, param, 0, 0, false, 16);
    }

    public static String sendNewPost(String url, Map param, int count, int time) throws Exception {
        return sendOkHttpPost(url, param, count, 0, false, time);
    }

    private static String sendOkHttpPost(String url, Map param, int retryCnt, int retryCount, boolean retry, int time) throws Exception {
        log.info("云端请求地址：{}", url);
        if (retry) {
            log.error("开始第【{}】次重试...", Integer.valueOf(retryCount));
        }
        param.put("nonce", UUID.randomUUID().toString());
        String timestamp = String.valueOf(System.currentTimeMillis());
        param.put("timestamp", timestamp);
        String testerId = TokenManager.getTesterId();
        param.put("testerId", testerId);
        param.put("format", "JSON");
        param.put("charset", "utf-8");
        param.put("signType", "SHA256");
        param.put("version", "1.36.0.2");
        param.put(ConstantEnum.TENANTCODE_STR, TokenManager.getTesterTenantCode());
        String methodName = (String) param.get("method");
        if (checkList.contains(methodName)) {
            String key = AesUtils.getRandomKey() + AesUtils.getBiosAes();
            String content = timestamp + methodName;
            param.put("check", AesUtils.encode(key, content));
        }
        String secret = TokenManager.getSecret();
        if (!METHOD_SECRET.equals(methodName)) {
            Object token = param.get(Constants.TOKEN);
            if (ObjectUtils.isNotEmpty(token)) {
                String username = TokenManager.getNameTokenMap(token.toString());
                secret = TokenManager.getSecretMap(username);
            }
            secret = OpenApiSignUtils.getSha256("secretKey" + testerId + secret + "!@#");
        }
        String sign = OpenApiSignUtils.getSignOfOpenApi(param, secret);
        param.put("sign", sign);
        Map paraLog = new HashMap();
        paraLog.putAll(param);
        paraLog.put(Constants.TOKEN, "");
        if (METHOD_LOGIN.equals(methodName)) {
            paraLog.put("bizContent", "");
        }
        paraLog.put("sign", "");
        String writeValueAsString = ObjectMapperUtils.getInstance().writeValueAsString(paraLog);
        log.info("云端调用参数 {}", writeValueAsString);
        String token2 = (String) param.get(Constants.TOKEN);
        Map<String, String> headMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(token2)) {
            headMap.put("TesterAuthorization", token2);
        }
        headMap.put("Content-type", "application/json");
        headMap.put("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        HttpServletRequest request = ServletUtils.getRequest();
        headMap.put("IP", IpUtils.getIpAddr(request));
        headMap.put("x-traceId-header", getTraceId());
        headMap.put("language", getLanguage());
        headMap.put("Ota-Region", AppConfig.getActiveRegion());
        headMap.put("Cloud-Id", AppConfig.getCloudId());
        log.info("客户端请求Ota-Region={},language={}", getLanguage(), AppConfig.getActiveRegion());
        Optional.ofNullable(TesterContext.getTesterConfigDto()).ifPresent(dto -> {
        });
        Optional.ofNullable(TokenManager.getTesterTenantCode()).ifPresent(code -> {
        });
        try {
            long reqTime = System.currentTimeMillis();
            log.info("----->>>开始发送请求...traceId:{}", MDCTraceUtils.getTraceId());
            String body = OkHttpUtils.builder(time).url(url).addHeader(headMap).addParam(param).post(true).sync();
            long reqTime2 = System.currentTimeMillis() - reqTime;
            if (reqTime2 > 3000) {
                log.warn("请求云端接口：{} 耗时：{} 毫秒", url, Long.valueOf(reqTime2));
            }
            if (body.length() < 102400) {
                log.info("----->>>云端返回原始数据:{}", body);
            } else {
                log.info("----->>>云端返回原始数据：{}", body.substring(0, 102400) + "...");
            }
            return body;
        } catch (IOException e) {
            String err = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200);
            if (StringUtils.containsAny(e.getMessage(), "timeout")) {
                OssUtils.setTimeoutLimit();
                String err2 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00137).concat(ConstantEnum.COMMA + MessageUtils.getMessage("Please check your network environment") + "！");
                String formatMsg = MessageUtils.getMessage("Cloud interface") + "：" + param.getOrDefault("method", MessageUtils.getMessage("Unknown interface")) + MessageUtils.getMessage("overtime");
                NetQualityUtils.checkHttpNetworkStatus(url);
                if (retryCnt > 0) {
                    log.error(formatMsg + "剩余尝试重试次数：【{}】 次", Integer.valueOf(retryCnt));
                    return sendOkHttpPost(url, param, retryCnt - 1, retryCount + 1, true, time);
                }
                log.error(formatMsg + ",地址：{},参数：{}", url, writeValueAsString);
                throw new CloudInterfaceException(err2 + formatMsg);
            }
            log.error("请求云端接口异常：{}", e.getMessage(), e);
            throw new CloudInterfaceException(err + e.getMessage());
        } catch (Exception e2) {
            String err3 = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00200);
            log.error("请求云端接口异常：{}", e2.getMessage(), e2);
            throw new CloudInterfaceException(err3 + e2.getMessage());
        }
    }

    private static String getTraceId() {
        return (String) Optional.ofNullable(MDCTraceUtils.getTraceId()).orElseGet(() -> {
            MDCTraceUtils.addTrace();
            return MDCTraceUtils.getTraceId();
        });
    }

    public static String sendPostJson(String url, Map map, String encoding) throws Exception {
        String writeValueAsString = ObjectMapperUtils.getInstance().writeValueAsString(map);
        log.info("云端调用参数 {}", writeValueAsString);
        String body = "";
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        try {
            try {
                RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(HttpStatus.SETTLEMENT_FAIL).setConnectionRequestTimeout(1000).setSocketTimeout(50000).build();
                client = HttpClients.createDefault();
                URL u = new URL(url);
                URI uri = new URI(u.getProtocol(), u.getUserInfo(), u.getHost(), u.getPort(), u.getPath(), u.getQuery(), null);
                HttpPost httpPost = new HttpPost(uri);
                httpPost.setConfig(requestConfig);
                StringEntity s = new StringEntity(writeValueAsString, "utf-8");
                s.setContentEncoding(new BasicHeader("Content-Type", "application/json"));
                httpPost.setEntity(s);
                log.info("请求地址: {}", url);
                String token = (String) map.get(Constants.TOKEN);
                if (StringUtils.isNotBlank(token)) {
                    httpPost.setHeader("TesterAuthorization", token);
                }
                httpPost.setHeader("Content-type", "application/json");
                httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
                response = client.execute(httpPost);
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    body = EntityUtils.toString(entity, encoding);
                }
                EntityUtils.consume(entity);
                response.close();
                CloseUtil.close("httpUtils工具类的client.close方法出现异常，异常原因--->{}:", client);
                CloseUtil.close("httpUtils工具类的response.close方法出现异常，异常原因--->{}:", response);
                return body;
            } catch (Exception e) {
                log.error("httpUtils工具类的sendPostJson方法出现异常，异常原因--->", e);
                throw e;
            }
        } catch (Throwable th) {
            CloseUtil.close("httpUtils工具类的client.close方法出现异常，异常原因--->{}:", client);
            CloseUtil.close("httpUtils工具类的response.close方法出现异常，异常原因--->{}:", response);
            throw th;
        }
    }

    public static String getDefaultLanguage() {
        return StrUtil.blankToDefault(AppConfig.getIniProperty("tester.language"), "zh-CN");
    }

    public static void setDefaultLanguage(String defaultLanguage) {
        AppConfig.writeIni("tester.language", defaultLanguage);
        GndsWebSocket.cG(defaultLanguage);
    }

    public static String getLanguage() {
        ServletRequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(requestAttributes)) {
            return getDefaultLanguage();
        }
        HttpServletRequest request = requestAttributes.getRequest();
        String tempLanguage = request.getHeader("Accept-Language");
        if (StringUtils.isNotBlank(tempLanguage)) {
            return tempLanguage;
        }
        return "zh_CN";
    }
}
