package com.geely.gnds.ruoyi.common.utils.http;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import okhttp3.Dns;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

/* loaded from: HttpDns.class */
public class HttpDns implements Dns {
    private static TimedCache<String, List<InetAddress>> timedCache = CacheUtil.newTimedCache(86400000);
    private static final Logger log = LoggerFactory.getLogger(HttpDns.class);
    private static final Dns SYSTEM = Dns.SYSTEM;
    private static HttpDns httpDns = null;

    private HttpDns() {
    }

    public static Dns getDns() {
        return (Dns) Optional.ofNullable(httpDns).orElseGet(() -> {
            httpDns = new HttpDns();
            return httpDns;
        });
    }

    public List<InetAddress> lookup(String hostname) throws UnknownHostException {
        List<InetAddress> ret;
        log.info("开始读取缓存DNS:{},Time:{}", hostname, Long.valueOf(System.currentTimeMillis()));
        List<InetAddress> ret2 = (List) timedCache.get(hostname, false);
        if (!CollectionUtils.isEmpty(ret2)) {
            log.info("DNS缓存未失效返回结果:{},Time:{}", ret2, Long.valueOf(System.currentTimeMillis()));
            return ret2;
        }
        log.info("开始查询HttpDNS:{},Time:{}", hostname, Long.valueOf(System.currentTimeMillis()));
        List<String> ips = getHttpDns(hostname);
        if (!CollectionUtils.isEmpty(ips)) {
            log.info("HttpDNS查询结果： {} --> {},Time:{}", new Object[]{hostname, ips, Long.valueOf(System.currentTimeMillis())});
            List<InetAddress> inetAddresses = new ArrayList<>();
            for (String s : ips) {
                inetAddresses.add(InetAddress.getByName(s));
            }
            ret = inetAddresses;
        } else {
            ret = SYSTEM.lookup(hostname);
            log.info("HttpDNS查询 {} 结果是NUll,Time:{}", hostname, Long.valueOf(System.currentTimeMillis()));
        }
        log.info("DNS结果:{},Time:{}", ret, Long.valueOf(System.currentTimeMillis()));
        List<InetAddress> ret3 = (List) Optional.ofNullable(ret).orElse(SYSTEM.lookup(hostname));
        log.info("DNS结果{}开始写入缓存:{},Time:{}", new Object[]{hostname, ret3, Long.valueOf(System.currentTimeMillis())});
        timedCache.put(hostname, ret3);
        log.info("DNS结果缓存写入结束{}-->{},Time:{}", new Object[]{hostname, ret3, Long.valueOf(System.currentTimeMillis())});
        log.info("域名解析:{},Time:{}", ret3, Long.valueOf(System.currentTimeMillis()));
        return ret3;
    }

    private List<String> getHttpDns(String hostname) {
        try {
            HttpResponse response = HttpRequest.get("https://*********/resolve").timeout(2000).form("name", hostname).form("type", "1").form("short", "true").execute();
            if (null != response) {
                String val = response.body();
                if (StringUtils.isNotBlank(val)) {
                    return JSONUtil.parseArray(val).toList(String.class);
                }
                return null;
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
