package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.framework.security.LoginWebUser;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/* loaded from: SecurityWebUtils.class */
public class SecurityWebUtils {
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00101), Integer.valueOf(HttpStatus.UNAUTHORIZED));
        }
    }

    public static Long getWebUserId() {
        try {
            return Long.valueOf(getLoginUser().getWebUserId());
        } catch (Exception e) {
            throw new CustomException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00101), Integer.valueOf(HttpStatus.UNAUTHORIZED));
        }
    }

    public static LoginWebUser getLoginUser() {
        try {
            return (LoginWebUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new CustomException("获取用户信息异常", Integer.valueOf(HttpStatus.UNAUTHORIZED));
        }
    }

    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    public static boolean isAdmin(Long userId) {
        return userId != null && 1 == userId.longValue();
    }
}
