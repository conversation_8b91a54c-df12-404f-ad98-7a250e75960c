package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import org.apache.commons.lang3.exception.ExceptionUtils;

/* loaded from: ExceptionUtil.class */
public class ExceptionUtil {
    public static String getExceptionMessage(Throwable e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter((Writer) sw, true));
        String str = sw.toString();
        return str;
    }

    public static String getRootErrorMseeage(Exception e) {
        Throwable root = ExceptionUtils.getRootCause(e);
        Throwable root2 = root == null ? e : root;
        if (root2 == null) {
            return "";
        }
        String msg = root2.getMessage();
        if (msg == null) {
            return ConstantEnum.NULL;
        }
        return StringUtils.defaultString(msg);
    }
}
