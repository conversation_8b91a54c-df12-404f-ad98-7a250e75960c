package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.ruoyi.common.core.lang.UUID;

/* loaded from: IdUtils.class */
public class IdUtils {
    public static String randomUuid() {
        return UUID.randomUuid().toString();
    }

    public static String simpleUuid() {
        return UUID.randomUuid().toString(true);
    }

    public static String fastUuid() {
        return UUID.fastUuid().toString();
    }

    public static String fastSimpleUuid() {
        return UUID.fastUuid().toString(true);
    }
}
