package com.geely.gnds.ruoyi.common.utils;

/* loaded from: PrivacyUtil.class */
public class PrivacyUtil {
    public static String hidePhone(String phone) {
        return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    public static String hideEmail(String email) {
        return email.replaceAll("(\\w?)(\\w+)(\\w)(@\\w+\\.[a-z]+(\\.[a-z]+)?)", "$1****$3$4");
    }

    public static String hideIdCard(String idCard) {
        return idCard.replaceAll("(\\d{4})\\d{10}(\\w{4})", "$1*****$2");
    }

    public static String hideChineseName(String chineseName) {
        if (chineseName == null) {
            return null;
        }
        return desValue(chineseName, 1, 0, "*");
    }

    public static String desValue(String origin, int prefixNoMaskLen, int suffixNoMaskLen, String maskStr) {
        if (origin == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        int n = origin.length();
        for (int i = 0; i < n; i++) {
            if (i < prefixNoMaskLen) {
                sb.append(origin.charAt(i));
            } else if (i > (n - suffixNoMaskLen) - 1) {
                sb.append(origin.charAt(i));
            } else {
                sb.append(maskStr);
            }
        }
        return sb.toString();
    }
}
