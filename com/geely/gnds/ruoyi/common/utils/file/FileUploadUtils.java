package com.geely.gnds.ruoyi.common.utils.file;

import com.geely.gnds.ruoyi.common.exception.file.FileNameLengthLimitExceededException;
import com.geely.gnds.ruoyi.common.exception.file.FileSizeLimitExceededException;
import com.geely.gnds.ruoyi.common.exception.file.InvalidExtensionException;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.security.Md5Utils;
import com.geely.gnds.ruoyi.framework.config.RuoYiConfig;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.File;
import java.io.IOException;
import org.apache.commons.io.FilenameUtils;
import org.apache.tomcat.util.http.fileupload.FileUploadException;
import org.springframework.web.multipart.MultipartFile;

/* loaded from: FileUploadUtils.class */
public class FileUploadUtils {
    public static final long DEFAULT_MAX_SIZE = 52428800;
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;
    private static String defaultBaseDir = RuoYiConfig.getProfile();
    private static int counter = 0;

    public static void setDefaultBaseDir(String defaultBaseDir2) {
        defaultBaseDir = defaultBaseDir2;
    }

    public static String getDefaultBaseDir() {
        return defaultBaseDir;
    }

    public static final String upload(MultipartFile file) throws IOException {
        try {
            return upload(getDefaultBaseDir(), file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static final String upload(String baseDir, MultipartFile file) throws IOException {
        try {
            return upload(baseDir, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /* JADX WARN: Byte code manipulation detected: skipped illegal throws declarations: [com.geely.gnds.ruoyi.common.exception.file.InvalidExtensionException] */
    public static final String upload(String baseDir, MultipartFile file, String[] allowedExtension) throws FileNameLengthLimitExceededException, FileSizeLimitExceededException, FileUploadException, IOException {
        int fileNamelength = file.getOriginalFilename().length();
        if (fileNamelength > 100) {
            throw new FileNameLengthLimitExceededException(100);
        }
        assertAllowed(file, allowedExtension);
        String fileName = extractFilename(file);
        File desc = getAbsoluteFile(baseDir, fileName);
        file.transferTo(desc);
        String pathFileName = getPathFileName(baseDir, fileName);
        return pathFileName;
    }

    public static final String extractFilename(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        return DateUtils.datePath() + "/" + encodingFilename(fileName) + ConstantEnum.POINT + extension;
    }

    private static final File getAbsoluteFile(String uploadDir, String fileName) throws IOException {
        File desc = new File(uploadDir + File.separator + fileName);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        if (!desc.exists()) {
            desc.createNewFile();
        }
        return desc;
    }

    private static final String getPathFileName(String uploadDir, String fileName) throws IOException {
        int dirLastIndex = RuoYiConfig.getProfile().length() + 1;
        String currentDir = StringUtils.substring(uploadDir, dirLastIndex);
        String pathFileName = "/profile/" + currentDir + "/" + fileName;
        return pathFileName;
    }

    private static final String encodingFilename(String fileName) {
        StringBuilder sbAppend = new StringBuilder().append(fileName.replace("_", ConstantEnum.EMPTY)).append(System.nanoTime());
        int i = counter;
        counter = i + 1;
        String fileName2 = Md5Utils.hash(sbAppend.append(i).toString());
        return fileName2;
    }

    /* JADX INFO: Thrown type has an unknown type hierarchy: org.apache.tomcat.util.http.fileupload.FileUploadException */
    /* JADX WARN: Byte code manipulation detected: skipped illegal throws declarations: [com.geely.gnds.ruoyi.common.exception.file.InvalidExtensionException] */
    public static final void assertAllowed(MultipartFile file, String[] allowedExtension) throws FileSizeLimitExceededException, FileUploadException {
        long size = file.getSize();
        if (size > DEFAULT_MAX_SIZE) {
            throw new FileSizeLimitExceededException(50L);
        }
        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION) {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension, fileName);
            }
            if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION) {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension, fileName);
            }
            if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION) {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension, fileName);
            }
            throw new InvalidExtensionException(allowedExtension, extension, fileName);
        }
    }

    public static final boolean isAllowedExtension(String extension, String[] allowedExtension) {
        for (String str : allowedExtension) {
            if (str.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    public static final String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(file.getContentType());
        }
        return extension;
    }
}
