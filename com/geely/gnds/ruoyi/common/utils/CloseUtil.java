package com.geely.gnds.ruoyi.common.utils;

import java.io.Closeable;
import java.io.IOException;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: CloseUtil.class */
public class CloseUtil {
    private static final Logger log = LoggerFactory.getLogger(CloseUtil.class);

    public static void close(String msg, Closeable closeable) throws IOException {
        try {
            if (!Objects.isNull(closeable)) {
                closeable.close();
            }
        } catch (IOException e) {
            log.error(msg, e.getMessage());
        }
    }
}
