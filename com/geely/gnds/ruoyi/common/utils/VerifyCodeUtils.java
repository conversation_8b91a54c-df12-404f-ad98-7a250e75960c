package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Random;
import javax.imageio.ImageIO;

/* loaded from: VerifyCodeUtils.class */
public class VerifyCodeUtils {
    public static final String VERIFY_CODES = "123456789ABCDEFGHJKLMNPQRSTUVWXYZ";
    private static Random random = new SecureRandom();

    public static String generateVerifyCode(int verifySize) {
        return generateVerifyCode(verifySize, VERIFY_CODES);
    }

    public static String generateVerifyCode(int verifySize, String sources) {
        if (sources == null || sources.length() == 0) {
            sources = VERIFY_CODES;
        }
        int codesLen = sources.length();
        Random rand = new Random(System.currentTimeMillis());
        StringBuilder verifyCode = new StringBuilder(verifySize);
        for (int i = 0; i < verifySize; i++) {
            verifyCode.append(sources.charAt(rand.nextInt(codesLen - 1)));
        }
        return verifyCode.toString();
    }

    public static void outputImage(int w, int h, OutputStream os, String code) throws IOException {
        int verifySize = code.length();
        BufferedImage image = new BufferedImage(w, h, 1);
        Random rand = new Random();
        Graphics2D g2 = image.createGraphics();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        Color[] colors = new Color[5];
        Color[] colorSpaces = {Color.WHITE, Color.CYAN, Color.GRAY, Color.LIGHT_GRAY, Color.MAGENTA, Color.ORANGE, Color.PINK, Color.YELLOW};
        float[] fractions = new float[colors.length];
        for (int i = 0; i < colors.length; i++) {
            colors[i] = colorSpaces[rand.nextInt(colorSpaces.length)];
            fractions[i] = rand.nextFloat();
        }
        Arrays.sort(fractions);
        g2.setColor(Color.GRAY);
        g2.fillRect(0, 0, w, h);
        Color c = getRandColor(HttpStatus.SUCCESS, 250);
        g2.setColor(c);
        g2.fillRect(0, 2, w, h - 4);
        Random random2 = new Random();
        g2.setColor(getRandColor(160, HttpStatus.SUCCESS));
        for (int i2 = 0; i2 < 20; i2++) {
            int x = random2.nextInt(w - 1);
            int y = random2.nextInt(h - 1);
            int xl = random2.nextInt(6) + 1;
            int yl = random2.nextInt(12) + 1;
            g2.drawLine(x, y, x + xl + 40, y + yl + 20);
        }
        int area = (int) (0.05f * w * h);
        for (int i3 = 0; i3 < area; i3++) {
            int x2 = random2.nextInt(w);
            int y2 = random2.nextInt(h);
            int rgb = getRandomIntColor();
            image.setRGB(x2, y2, rgb);
        }
        shear(g2, w, h, c);
        g2.setColor(getRandColor(100, 160));
        int fontSize = h - 4;
        Font font = new Font("Algerian", 2, fontSize);
        g2.setFont(font);
        char[] chars = code.toCharArray();
        for (int i4 = 0; i4 < verifySize; i4++) {
            AffineTransform affine = new AffineTransform();
            affine.setToRotation(0.7853981633974483d * rand.nextDouble() * (rand.nextBoolean() ? 1 : -1), ((w / verifySize) * i4) + (fontSize / 2), h / 2);
            g2.setTransform(affine);
            g2.drawChars(chars, i4, 1, (((w - 10) / verifySize) * i4) + 5, ((h / 2) + (fontSize / 2)) - 10);
        }
        g2.dispose();
        ImageIO.write(image, "jpg", os);
    }

    private static Color getRandColor(int fc, int bc) {
        if (fc > ConstantEnum.MAX_NAME_LENGTH.intValue()) {
            fc = 255;
        }
        if (bc > ConstantEnum.MAX_NAME_LENGTH.intValue()) {
            bc = 255;
        }
        int r = fc + random.nextInt(bc - fc);
        int g = fc + random.nextInt(bc - fc);
        int b = fc + random.nextInt(bc - fc);
        return new Color(r, g, b);
    }

    private static int getRandomIntColor() {
        int[] rgb = getRandomRgb();
        int color = 0;
        for (int c : rgb) {
            color = (color << 8) | c;
        }
        return color;
    }

    private static int[] getRandomRgb() {
        int[] rgb = new int[3];
        for (int i = 0; i < ConstantEnum.THREE.intValue(); i++) {
            rgb[i] = random.nextInt(255);
        }
        return rgb;
    }

    private static void shear(Graphics g, int w1, int h1, Color color) {
        shearx(g, w1, h1, color);
        sheary(g, w1, h1, color);
    }

    private static void shearx(Graphics g, int w1, int h1, Color color) {
        int period = random.nextInt(2);
        int phase = random.nextInt(2);
        for (int i = 0; i < h1; i++) {
            double d = (period >> 1) * Math.sin((i / period) + ((6.283185307179586d * phase) / 1));
            g.copyArea(0, i, w1, 1, (int) d, 0);
            if (1 != 0) {
                g.setColor(color);
                g.drawLine((int) d, i, 0, i);
                g.drawLine(((int) d) + w1, i, w1, i);
            }
        }
    }

    private static void sheary(Graphics g, int w1, int h1, Color color) {
        int period = random.nextInt(40) + 10;
        for (int i = 0; i < w1; i++) {
            double d = (period >> 1) * Math.sin((i / period) + ((6.283185307179586d * 7) / 20));
            g.copyArea(i, 0, 1, h1, 0, (int) d);
            if (1 != 0) {
                g.setColor(color);
                g.drawLine(i, (int) d, i, 0);
                g.drawLine(i, ((int) d) + h1, i, h1);
            }
        }
    }
}
