package com.geely.gnds.ruoyi.common.utils.bean;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

/* loaded from: BeanUtils.class */
public class BeanUtils extends org.springframework.beans.BeanUtils {
    private static final int BEAN_METHOD_PROP_INDEX = 3;
    private static final Logger LOG = LoggerFactory.getLogger(BeanUtils.class);
    private static final Pattern GET_PATTERN = Pattern.compile("get(\\p{javaUpperCase}\\w*)");
    private static final Pattern SET_PATTERN = Pattern.compile("set(\\p{javaUpperCase}\\w*)");

    public static void copyBeanProp(Object dest, Object src) {
        try {
            copyProperties(src, dest);
        } catch (Exception e) {
            LOG.error(e.getMessage());
        }
    }

    public static List<Method> getSetterMethods(Object obj) throws SecurityException {
        List<Method> setterMethods = new ArrayList<>();
        Method[] methods = obj.getClass().getMethods();
        for (Method method : methods) {
            Matcher m = SET_PATTERN.matcher(method.getName());
            if (m.matches() && method.getParameterTypes().length == 1) {
                setterMethods.add(method);
            }
        }
        return setterMethods;
    }

    public static List<Method> getGetterMethods(Object obj) throws SecurityException {
        List<Method> getterMethods = new ArrayList<>();
        Method[] methods = obj.getClass().getMethods();
        for (Method method : methods) {
            Matcher m = GET_PATTERN.matcher(method.getName());
            if (m.matches() && method.getParameterTypes().length == 0) {
                getterMethods.add(method);
            }
        }
        return getterMethods;
    }

    public static boolean isMethodPropEquals(String m1, String m2) {
        return m1.substring(3).equals(m2.substring(3));
    }

    public static <S, T> List<T> copyListProperties(List<S> sources, Supplier<T> target) {
        if (ObjectUtils.isEmpty(sources)) {
            LOG.error("copyListProperties 失败，sources=null");
            return new ArrayList();
        }
        List<T> list = new ArrayList<>(sources.size());
        for (S source : sources) {
            T t = target.get();
            copyProperties(source, t);
            list.add(t);
        }
        return list;
    }
}
