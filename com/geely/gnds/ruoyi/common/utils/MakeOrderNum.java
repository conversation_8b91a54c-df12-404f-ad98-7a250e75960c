package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.text.SimpleDateFormat;
import java.util.Date;

/* loaded from: MakeOrderNum.class */
public class MakeOrderNum {
    public static final String RECHARGE_PREFIX = "R";
    private static Object lockObj = "lockerOrder";
    private static long orderNumCount = 0;
    private int maxPerMsecSize = 1000;

    public String makeOrderNum() {
        String finOrderNum;
        try {
            synchronized (lockObj) {
                long nowLong = Long.parseLong(new SimpleDateFormat("yyyyMMddHHmmSS").format(new Date()));
                if (orderNumCount >= this.maxPerMsecSize) {
                    orderNumCount = 0L;
                }
                String countStr = (this.maxPerMsecSize + orderNumCount) + "";
                finOrderNum = nowLong + countStr.substring(1);
                orderNumCount++;
            }
        } catch (Exception e) {
            finOrderNum = "";
        }
        return finOrderNum;
    }

    public String makeRechargeOrderNo() {
        String orderNo = makeOrderNum();
        for (int getCount = 1; org.apache.commons.lang3.StringUtils.isBlank(orderNo) && getCount <= ConstantEnum.FIVE.intValue(); getCount++) {
            orderNo = makeOrderNum();
        }
        return RECHARGE_PREFIX + orderNo;
    }
}
