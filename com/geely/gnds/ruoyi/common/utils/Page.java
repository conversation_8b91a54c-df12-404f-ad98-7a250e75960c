package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.LinkedHashMap;
import java.util.Map;

/* loaded from: Page.class */
public class Page extends LinkedHashMap<String, Object> {
    private static final long serialVersionUID = 1;
    private int page;
    private int limit;

    public Page(Map<String, Object> params) {
        putAll(params);
        if (params.containsKey(ConstantEnum.PAGE) && params.containsKey(ConstantEnum.LIMIT)) {
            this.page = Integer.parseInt(params.get(ConstantEnum.PAGE).toString());
            this.limit = Integer.parseInt(params.get(ConstantEnum.LIMIT).toString());
            put(ConstantEnum.PAGE, Integer.valueOf(this.page));
            put(ConstantEnum.OFFSET, Integer.valueOf((this.page - 1) * this.limit));
            put(ConstantEnum.LIMIT, Integer.valueOf(this.limit));
        }
    }

    public int getPage() {
        return this.page;
    }

    public void setPage(int offset) {
        put(ConstantEnum.OFFSET, Integer.valueOf(offset));
    }

    public int getLimit() {
        return this.limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }
}
