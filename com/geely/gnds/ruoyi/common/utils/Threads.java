package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: Threads.class */
public class Threads {
    private static final Logger logger = LoggerFactory.getLogger(Threads.class);

    public static void sleep(long milliseconds) throws InterruptedException {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
        }
    }

    public static void shutdownAndAwaitTermination(ExecutorService pool) {
        if (pool != null && !pool.isShutdown()) {
            pool.shutdown();
            try {
                if (!pool.awaitTermination(HttpStatus.SUCCESS, TimeUnit.SECONDS)) {
                    pool.shutdownNow();
                    if (!pool.awaitTermination(HttpStatus.SUCCESS, TimeUnit.SECONDS)) {
                        logger.info("Pool did not terminate");
                    }
                }
            } catch (InterruptedException e) {
                pool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    public static void printException(Runnable r, Throwable t) throws ExecutionException, InterruptedException {
        if (t == null && (r instanceof Future)) {
            try {
                Future<?> future = (Future) r;
                if (future.isDone()) {
                    future.get();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } catch (CancellationException ce) {
                t = ce;
            } catch (ExecutionException ee) {
                t = ee.getCause();
            }
        }
        if (t != null) {
            logger.error(t.getMessage(), t);
        }
    }
}
