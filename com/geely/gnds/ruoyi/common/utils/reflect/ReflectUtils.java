package com.geely.gnds.ruoyi.common.utils.reflect;

import com.geely.gnds.ruoyi.common.core.text.Convert;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.poi.ss.usermodel.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: ReflectUtils.class */
public class ReflectUtils {
    private static final String SETTER_PREFIX = "set";
    private static final String GETTER_PREFIX = "get";
    private static final String CGLIB_CLASS_SEPARATOR = "$$";
    private static Logger logger = LoggerFactory.getLogger(ReflectUtils.class);

    public static <E> E invokeGetter(Object obj, String str) throws NoSuchMethodException, SecurityException {
        Object objInvokeMethod = obj;
        for (String str2 : StringUtils.split(str, ConstantEnum.POINT)) {
            objInvokeMethod = invokeMethod(objInvokeMethod, GETTER_PREFIX + StringUtils.capitalize(str2), new Class[0], new Object[0]);
        }
        return (E) objInvokeMethod;
    }

    public static <E> void invokeSetter(Object obj, String propertyName, E value) throws NoSuchMethodException, SecurityException {
        Object object = obj;
        String[] names = StringUtils.split(propertyName, ConstantEnum.POINT);
        for (int i = 0; i < names.length; i++) {
            if (i < names.length - 1) {
                String getterMethodName = GETTER_PREFIX + StringUtils.capitalize(names[i]);
                object = invokeMethod(object, getterMethodName, new Class[0], new Object[0]);
            } else {
                String setterMethodName = SETTER_PREFIX + StringUtils.capitalize(names[i]);
                invokeMethodByName(object, setterMethodName, new Object[]{value});
            }
        }
    }

    public static <E> E getFieldValue(Object obj, String str) throws IllegalAccessException, NoSuchFieldException, IllegalArgumentException {
        Field accessibleField = getAccessibleField(obj, str);
        if (accessibleField == null) {
            logger.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + str + "] 字段 ");
            return null;
        }
        Object obj2 = null;
        try {
            obj2 = accessibleField.get(obj);
        } catch (IllegalAccessException e) {
            logger.error("不可能抛出的异常{}", e.getMessage());
        }
        return (E) obj2;
    }

    public static <E> void setFieldValue(Object obj, String fieldName, E value) throws IllegalAccessException, NoSuchFieldException, IllegalArgumentException {
        Field field = getAccessibleField(obj, fieldName);
        if (field == null) {
            logger.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + fieldName + "] 字段 ");
            return;
        }
        try {
            field.set(obj, value);
        } catch (IllegalAccessException e) {
            logger.error("不可能抛出的异常: {}", e.getMessage());
        }
    }

    public static <E> E invokeMethod(Object obj, String str, Class<?>[] clsArr, Object[] objArr) throws NoSuchMethodException, SecurityException {
        if (obj == null || str == null) {
            return null;
        }
        Method accessibleMethod = getAccessibleMethod(obj, str, clsArr);
        if (accessibleMethod == null) {
            logger.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + str + "] 方法 ");
            return null;
        }
        try {
            return (E) accessibleMethod.invoke(obj, objArr);
        } catch (Exception e) {
            throw convertReflectionExceptionToUnchecked("method: " + accessibleMethod + ", obj: " + obj + ", args: " + objArr + "", e);
        }
    }

    public static <E> E invokeMethodByName(Object obj, String str, Object[] objArr) throws SecurityException {
        Method accessibleMethodByName = getAccessibleMethodByName(obj, str, objArr.length);
        if (accessibleMethodByName == null) {
            logger.debug("在 [" + obj.getClass() + "] 中，没有找到 [" + str + "] 方法 ");
            return null;
        }
        try {
            Class<?>[] parameterTypes = accessibleMethodByName.getParameterTypes();
            for (int i = 0; i < parameterTypes.length; i++) {
                if (objArr[i] != null && !objArr[i].getClass().equals(parameterTypes[i])) {
                    if (parameterTypes[i] == String.class) {
                        objArr[i] = Convert.toStr(objArr[i]);
                        if (StringUtils.endsWith((String) objArr[i], ".0")) {
                            objArr[i] = StringUtils.substringBefore((String) objArr[i], ".0");
                        }
                    } else if (parameterTypes[i] == Integer.class) {
                        objArr[i] = Convert.toInt(objArr[i]);
                    } else if (parameterTypes[i] == Long.class) {
                        objArr[i] = Convert.toLong(objArr[i]);
                    } else if (parameterTypes[i] == Double.class) {
                        objArr[i] = Convert.toDouble(objArr[i]);
                    } else if (parameterTypes[i] == Float.class) {
                        objArr[i] = Convert.toFloat(objArr[i]);
                    } else if (parameterTypes[i] == Date.class) {
                        if (objArr[i] instanceof String) {
                            objArr[i] = DateUtils.parseDate(objArr[i]);
                        } else {
                            objArr[i] = DateUtil.getJavaDate(((Double) objArr[i]).doubleValue());
                        }
                    }
                }
            }
            return (E) accessibleMethodByName.invoke(obj, objArr);
        } catch (Exception e) {
            throw convertReflectionExceptionToUnchecked("method: " + accessibleMethodByName + ", obj: " + obj + ", args: " + objArr + "", e);
        }
    }

    public static Field getAccessibleField(Object obj, String fieldName) throws NoSuchFieldException {
        if (obj == null) {
            return null;
        }
        Validate.notBlank(fieldName, "fieldName can't be blank", new Object[0]);
        Class<?> superclass = obj.getClass();
        while (true) {
            Class<?> superClass = superclass;
            if (superClass != Object.class) {
                try {
                    Field field = superClass.getDeclaredField(fieldName);
                    makeAccessible(field);
                    return field;
                } catch (NoSuchFieldException e) {
                    superclass = superClass.getSuperclass();
                }
            } else {
                return null;
            }
        }
    }

    public static Method getAccessibleMethod(Object obj, String methodName, Class<?>... parameterTypes) throws NoSuchMethodException, SecurityException {
        if (obj == null) {
            return null;
        }
        Validate.notBlank(methodName, "methodName can't be blank", new Object[0]);
        Class<?> superclass = obj.getClass();
        while (true) {
            Class<?> searchType = superclass;
            if (searchType != Object.class) {
                try {
                    Method method = searchType.getDeclaredMethod(methodName, parameterTypes);
                    makeAccessible(method);
                    return method;
                } catch (NoSuchMethodException e) {
                    superclass = searchType.getSuperclass();
                }
            } else {
                return null;
            }
        }
    }

    public static Method getAccessibleMethodByName(Object obj, String methodName, int argsNum) throws SecurityException {
        if (obj == null) {
            return null;
        }
        Validate.notBlank(methodName, "methodName can't be blank", new Object[0]);
        Class<?> superclass = obj.getClass();
        while (true) {
            Class<?> searchType = superclass;
            if (searchType != Object.class) {
                Method[] methods = searchType.getDeclaredMethods();
                for (Method method : methods) {
                    if (method.getName().equals(methodName) && method.getParameterTypes().length == argsNum) {
                        makeAccessible(method);
                        return method;
                    }
                }
                superclass = searchType.getSuperclass();
            } else {
                return null;
            }
        }
    }

    public static void makeAccessible(Method method) {
        Boolean flag = Boolean.valueOf(((Modifier.isPublic(method.getModifiers()) && Modifier.isPublic(method.getDeclaringClass().getModifiers())) || method.isAccessible()) ? false : true);
        if (flag.booleanValue()) {
            method.setAccessible(true);
        }
    }

    public static void makeAccessible(Field field) {
        Boolean flag = Boolean.valueOf(((Modifier.isPublic(field.getModifiers()) && Modifier.isPublic(field.getDeclaringClass().getModifiers()) && !Modifier.isFinal(field.getModifiers())) || field.isAccessible()) ? false : true);
        if (flag.booleanValue()) {
            field.setAccessible(true);
        }
    }

    public static <T> Class<T> getClassGenricType(Class clazz) {
        return getClassGenricType(clazz, 0);
    }

    public static Class getClassGenricType(Class clazz, int index) {
        Type genType = clazz.getGenericSuperclass();
        if (!(genType instanceof ParameterizedType)) {
            logger.debug(clazz.getSimpleName() + "'s superclass not ParameterizedType");
            return Object.class;
        }
        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
        if (index >= params.length || index < 0) {
            logger.debug("Index: " + index + ", Size of " + clazz.getSimpleName() + "'s Parameterized Type: " + params.length);
            return Object.class;
        }
        if (!(params[index] instanceof Class)) {
            logger.debug(clazz.getSimpleName() + " not set the actual class on superclass generic parameter");
            return Object.class;
        }
        return (Class) params[index];
    }

    public static Class<?> getUserClass(Object instance) {
        Class<?> superClass;
        if (instance == null) {
            throw new RuntimeException("Instance must not be null");
        }
        Class clazz = instance.getClass();
        if (clazz != null && clazz.getName().contains(CGLIB_CLASS_SEPARATOR) && (superClass = clazz.getSuperclass()) != null && !Object.class.equals(superClass)) {
            return superClass;
        }
        return clazz;
    }

    public static RuntimeException convertReflectionExceptionToUnchecked(String msg, Exception e) {
        if ((e instanceof IllegalAccessException) || (e instanceof IllegalArgumentException) || (e instanceof NoSuchMethodException)) {
            return new IllegalArgumentException(msg, e);
        }
        if (e instanceof InvocationTargetException) {
            return new RuntimeException(msg, ((InvocationTargetException) e).getTargetException());
        }
        return new RuntimeException(msg, e);
    }
}
