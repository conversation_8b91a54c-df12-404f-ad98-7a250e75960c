package com.geely.gnds.ruoyi.common.utils.ip;

import com.fasterxml.jackson.databind.JsonNode;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.ruoyi.framework.config.RuoYiConfig;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: AddressUtils.class */
public class AddressUtils {
    private static final Logger log = LoggerFactory.getLogger(AddressUtils.class);
    public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";
    public static final String UNKNOWN = "XX XX";

    public static String getRealAddressByIp(String ip) {
        if (IpUtils.internalIp(ip)) {
            return "内网IP";
        }
        if (RuoYiConfig.isAddressEnabled()) {
            try {
                String rspStr = HttpUtils.sendGet(IP_URL, "ip=" + ip + "&json=true", "GBK");
                if (StringUtils.isEmpty(rspStr)) {
                    log.error("获取地理位置异常 {}", ip);
                    return UNKNOWN;
                }
                JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(rspStr, JsonNode.class);
                String region = readValue.get("pro").asText();
                String city = readValue.get("city").asText();
                return String.format("%s %s", region, city);
            } catch (Exception e) {
                log.error("获取地理位置异常 {}", ip);
            }
        }
        return UNKNOWN;
    }
}
