package com.geely.gnds.ruoyi.common.utils.ip;

import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.sun.jna.Platform;
import java.net.InetAddress;
import java.net.UnknownHostException;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: IpUtils.class */
public class IpUtils {
    private static final Logger LOG = LoggerFactory.getLogger(IpUtils.class);

    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return "0:0:0:0:0:0:0:1".equals(ip) ? ConstantEnum.LOCAL : ip;
    }

    public static boolean internalIp(String ip) throws NumberFormatException {
        byte[] addr = textToNumericFormatV4(ip);
        return internalIp(addr) || ConstantEnum.LOCAL.equals(ip);
    }

    private static boolean internalIp(byte[] addr) {
        if (StringUtils.isNull(addr) || addr.length < ConstantEnum.TWO.intValue()) {
            return true;
        }
        byte b0 = addr[0];
        byte b1 = addr[1];
        switch (b0) {
            case -84:
                if (b1 >= 16 && b1 <= 31) {
                    return true;
                }
                break;
            case -64:
                break;
            case Platform.KFREEBSD /* 10 */:
                return true;
            default:
                return false;
        }
        switch (b1) {
            case -88:
                break;
        }
        return true;
    }

    public static byte[] textToNumericFormatV4(String text) throws NumberFormatException {
        if (text.length() == 0) {
            return null;
        }
        byte[] bytes = new byte[4];
        String[] elements = text.split("\\.", -1);
        try {
            switch (elements.length) {
                case 1:
                    long l = Long.parseLong(elements[0]);
                    if (l < 0 || l > 4294967295L) {
                        return null;
                    }
                    bytes[0] = (byte) ((l >> 24) & 255);
                    bytes[1] = (byte) (((l & 16777215) >> 16) & 255);
                    bytes[2] = (byte) (((l & 65535) >> 8) & 255);
                    bytes[3] = (byte) (l & 255);
                    break;
                    break;
                case 2:
                    long l2 = Integer.parseInt(elements[0]);
                    if (l2 < 0 || l2 > 255) {
                        return null;
                    }
                    bytes[0] = (byte) (l2 & 255);
                    long l3 = Integer.parseInt(elements[1]);
                    if (l3 < 0 || l3 > 4294967295L) {
                        return null;
                    }
                    bytes[1] = (byte) ((l3 >> 16) & 255);
                    bytes[2] = (byte) (((l3 & 65535) >> 8) & 255);
                    bytes[3] = (byte) (l3 & 255);
                    break;
                    break;
                case 3:
                    for (int i = 0; i < ConstantEnum.TWO.intValue(); i++) {
                        long l4 = Integer.parseInt(elements[i]);
                        if (l4 < 0 || l4 > 255) {
                            return null;
                        }
                        bytes[i] = (byte) (l4 & 255);
                    }
                    long l5 = Integer.parseInt(elements[2]);
                    if (l5 < 0 || l5 > 65535) {
                        return null;
                    }
                    bytes[2] = (byte) ((l5 >> 8) & 255);
                    bytes[3] = (byte) (l5 & 255);
                    break;
                    break;
                case 4:
                    for (int i2 = 0; i2 < ConstantEnum.FORE.intValue(); i2++) {
                        long l6 = Integer.parseInt(elements[i2]);
                        if (l6 < 0 || l6 > 255) {
                            return null;
                        }
                        bytes[i2] = (byte) (l6 & 255);
                    }
                    break;
                default:
                    return null;
            }
            return bytes;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public static String getHostIp() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            LOG.error("UnknownHostException:" + e.getMessage());
            return ConstantEnum.LOCAL;
        }
    }

    public static String getHostName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            LOG.error("UnknownHostException:" + e.getMessage());
            return "未知";
        }
    }
}
