package com.geely.gnds.ruoyi.common.utils;

import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/* loaded from: StringUtils.class */
public class StringUtils extends org.apache.commons.lang3.StringUtils {
    private static final String NULLSTR = "";
    private static final char SEPARATOR = '_';

    public static <T> T nvl(T value, T defaultValue) {
        return value != null ? value : defaultValue;
    }

    public static boolean isEmpty(Collection<?> coll) {
        return isNull(coll) || coll.isEmpty();
    }

    public static boolean isNotEmpty(Collection<?> coll) {
        return !isEmpty(coll);
    }

    public static boolean isEmpty(Object[] objects) {
        return isNull(objects) || objects.length == 0;
    }

    public static boolean isNotEmpty(Object[] objects) {
        return !isEmpty(objects);
    }

    public static boolean isEmpty(Map<?, ?> map) {
        return isNull(map) || map.isEmpty();
    }

    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    public static boolean isEmpty(String str) {
        return isNull(str) || NULLSTR.equals(str.trim());
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean isNull(Object object) {
        return object == null;
    }

    public static boolean isNotNull(Object object) {
        return !isNull(object);
    }

    public static boolean isArray(Object object) {
        return isNotNull(object) && object.getClass().isArray();
    }

    public static String trim(String str) {
        return str == null ? NULLSTR : str.trim();
    }

    public static String substring(String str, int start) {
        if (str == null) {
            return NULLSTR;
        }
        if (start < 0) {
            start = str.length() + start;
        }
        if (start < 0) {
            start = 0;
        }
        if (start > str.length()) {
            return NULLSTR;
        }
        return str.substring(start);
    }

    public static String substring(String str, int start, int end) {
        if (str == null) {
            return NULLSTR;
        }
        if (end < 0) {
            end = str.length() + end;
        }
        if (start < 0) {
            start = str.length() + start;
        }
        if (end > str.length()) {
            end = str.length();
        }
        if (start > end) {
            return NULLSTR;
        }
        if (start < 0) {
            start = 0;
        }
        if (end < 0) {
            end = 0;
        }
        return str.substring(start, end);
    }

    public static String format(String template, Object... params) {
        if (isEmpty(params) || isEmpty(template)) {
            return template;
        }
        return StrFormatter.format(template, params);
    }

    public static final Set<String> str2Set(String str, String sep) {
        return new HashSet(str2List(str, sep, true, false));
    }

    public static final List<String> str2List(String str, String sep, boolean filterBlank, boolean trim) {
        List<String> list = new ArrayList<>();
        if (isEmpty(str)) {
            return list;
        }
        if (filterBlank && isBlank(str)) {
            return list;
        }
        String[] split = str.split(sep);
        for (String string : split) {
            if (!filterBlank || !isBlank(string)) {
                if (trim) {
                    string = string.trim();
                }
                list.add(string);
            }
        }
        return list;
    }

    public static String toUnderScoreCase(String str) {
        boolean preCharIsUpperCase;
        if (str == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        boolean nexteCharIsUpperCase = true;
        int i = 0;
        while (i < str.length()) {
            char c = str.charAt(i);
            if (i > 0) {
                preCharIsUpperCase = Character.isUpperCase(str.charAt(i - 1));
            } else {
                preCharIsUpperCase = false;
            }
            boolean curreCharIsUpperCase = Character.isUpperCase(c);
            if (i < str.length() - 1) {
                nexteCharIsUpperCase = Character.isUpperCase(str.charAt(i + 1));
            }
            Boolean flag = Boolean.valueOf((i == 0 || preCharIsUpperCase || !curreCharIsUpperCase) ? false : true);
            if (preCharIsUpperCase && curreCharIsUpperCase && !nexteCharIsUpperCase) {
                sb.append('_');
            } else if (flag.booleanValue()) {
                sb.append('_');
            }
            sb.append(Character.toLowerCase(c));
            i++;
        }
        return sb.toString();
    }

    public static boolean inStringIgnoreCase(String str, String... strs) {
        if (str != null && strs != null) {
            for (String s : strs) {
                if (str.equalsIgnoreCase(trim(s))) {
                    return true;
                }
            }
            return false;
        }
        return false;
    }

    public static String convertToCamelCase(String name) {
        StringBuilder result = new StringBuilder();
        if (name == null || name.isEmpty()) {
            return NULLSTR;
        }
        if (!name.contains("_")) {
            return name.substring(0, 1).toUpperCase() + name.substring(1);
        }
        String[] camels = name.split("_");
        for (String camel : camels) {
            if (!camel.isEmpty()) {
                result.append(camel.substring(0, 1).toUpperCase());
                result.append(camel.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

    public static String toCamelCase(String s) {
        if (s == null) {
            return null;
        }
        String s2 = s.toLowerCase();
        StringBuilder sb = new StringBuilder(s2.length());
        boolean upperCase = false;
        for (int i = 0; i < s2.length(); i++) {
            char c = s2.charAt(i);
            if (c == SEPARATOR) {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static <T> T cast(Object obj) {
        return obj;
    }

    public static String getString(Object obj) {
        return obj != null ? obj.toString() : NULLSTR;
    }

    public static String getStringByDelTrim(Object obj) {
        return obj != null ? obj.toString().trim() : NULLSTR;
    }

    public static boolean isNullOrEmpty(Object obj) {
        String str = obj != null ? obj.toString() : NULLSTR;
        return NULLSTR.equals(str);
    }

    public static BigDecimal stringToBigDecimal(Object price) {
        if (!isNullOrEmpty(price)) {
            try {
                BigDecimal bigDecimal = new BigDecimal(price.toString());
                return bigDecimal;
            } catch (Exception e) {
                return new BigDecimal("0.00");
            }
        }
        return new BigDecimal("0.00");
    }

    public static int stringToInt(Object price) {
        if (!isNullOrEmpty(price)) {
            return Integer.parseInt(price.toString().trim());
        }
        return 0;
    }

    public static long stringToLong(Object price) {
        if (!isNullOrEmpty(price)) {
            return Long.parseLong(price.toString().trim());
        }
        return 0L;
    }

    public static String urlEncode(String str, String charset) {
        try {
            return URLEncoder.encode(str, charset);
        } catch (UnsupportedEncodingException ex) {
            ex.printStackTrace();
            return NULLSTR;
        }
    }

    public static String urlDecode(String str, String charset) {
        try {
            return URLDecoder.decode(str, charset);
        } catch (UnsupportedEncodingException ex) {
            ex.printStackTrace();
            return NULLSTR;
        }
    }

    public static String getStringByRemoveComma(Object oldStr) {
        return oldStr == null ? NULLSTR : oldStr.toString().trim().replaceAll(ConstantEnum.COMMA, NULLSTR).replaceAll("，", NULLSTR);
    }

    public static String getSexByCard(String cardCode) {
        String sex;
        if (Integer.parseInt(cardCode.substring(16).substring(0, 1)) % ConstantEnum.TWO.intValue() == 0) {
            sex = "0";
        } else {
            sex = "1";
        }
        return sex;
    }

    public static int getAgeByCard(String cardCode) {
        int age;
        String year = cardCode.substring(6).substring(0, 4);
        String yue = cardCode.substring(10).substring(0, 2);
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String fyear = format.format(date).substring(0, 4);
        String fyue = format.format(date).substring(5, 7);
        if (Integer.parseInt(yue) <= Integer.parseInt(fyue)) {
            age = (Integer.parseInt(fyear) - Integer.parseInt(year)) + 1;
        } else {
            age = Integer.parseInt(fyear) - Integer.parseInt(year);
        }
        return age;
    }
}
