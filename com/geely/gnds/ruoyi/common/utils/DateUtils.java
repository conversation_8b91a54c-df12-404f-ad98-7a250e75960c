package com.geely.gnds.ruoyi.common.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import org.apache.commons.lang3.time.DateFormatUtils;

/* loaded from: DateUtils.class */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static final String YYYY = "yyyy";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String HHMMSS = "hh:mm:ss";
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static final String YYYYMMDDHHMMSSSSS = "yyyyMMddHHmmssSSS";
    public static final String YYYY_MM_DD_HH_MM_SS_TWELVE = "yyyy-MM-dd hh:mm:ss";
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss:SSS";
    public static final String YYYYMMDD_HHMMSS = "yyyyMMdd_HHmmss";
    public static final String YYYY_MM_DD2 = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM_SS3 = "yyyy-MM-dd-HH-mm-ss-";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM = "yyyy-MM";
    public static final String YYYY_MM_DD_HH_MM_SS2 = "yyyy/MM/dd HH:mm:ss";
    private static String[] parsePatterns = {"yyyy-MM-dd", YYYY_MM_DD_HH_MM_SS, "yyyy-MM-dd HH:mm", YYYY_MM, "yyyy/MM/dd", YYYY_MM_DD_HH_MM_SS2, "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    public static Date getNowDate() {
        return new Date();
    }

    public static String getDate() {
        return dateTimeNow("yyyy-MM-dd");
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(Date date) {
        return parseDateToStr("yyyy-MM-dd", date);
    }

    public static final String parseDateToStr(String format, Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(String format, String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, YYYYMMDD);
    }

    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    public static String getDatePoor(Date endDate, Date nowDate) {
        long diff = endDate.getTime() - nowDate.getTime();
        long day = diff / 86400000;
        long hour = (diff % 86400000) / 3600000;
        long min = ((diff % 86400000) % 3600000) / 60000;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    public static String getUtcTime(String pattern) {
        LocalDateTime dateTime = LocalDateTimeUtil.ofUTC(System.currentTimeMillis());
        return LocalDateTimeUtil.format(dateTime, DateTimeFormatter.ofPattern(pattern));
    }

    public static String getUtcZoneTime() {
        return DateFormatUtils.format(new Date(), "XXX") + ":00";
    }

    public static int calcMinuteDiff(String startTime, String endTime) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);
        LocalDateTime start = LocalDateTime.parse(startTime, df);
        LocalDateTime end = LocalDateTime.parse(endTime, df);
        Duration duration = Duration.between(start, end);
        long minutes = duration.toMinutes();
        return (int) minutes;
    }

    public static void main(String[] args) {
        LocalDateTime dateTime = LocalDateTimeUtil.ofUTC(System.currentTimeMillis());
        System.out.println(LocalDateTimeUtil.format(dateTime, DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS)));
    }
}
