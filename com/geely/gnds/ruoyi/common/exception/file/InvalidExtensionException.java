package com.geely.gnds.ruoyi.common.exception.file;

import java.util.Arrays;
import org.apache.tomcat.util.http.fileupload.FileUploadException;

/* loaded from: InvalidExtensionException.class */
public class InvalidExtensionException extends FileUploadException {
    private static final long serialVersionUID = 1;
    private String[] allowedExtension;
    private String extension;
    private String filename;

    public InvalidExtensionException(String[] allowedExtension, String extension, String filename) {
        super("filename : [" + filename + "], extension : [" + extension + "], allowed extension : [" + Arrays.toString(allowedExtension) + "]");
        this.allowedExtension = allowedExtension;
        this.extension = extension;
        this.filename = filename;
    }

    public String[] getAllowedExtension() {
        return this.allowedExtension;
    }

    public String getExtension() {
        return this.extension;
    }

    public String getFilename() {
        return this.filename;
    }

    /* loaded from: InvalidExtensionException$InvalidImageExtensionException.class */
    public static class InvalidImageExtensionException extends InvalidExtensionException {
        private static final long serialVersionUID = 1;

        public InvalidImageExtensionException(String[] allowedExtension, String extension, String filename) {
            super(allowedExtension, extension, filename);
        }
    }

    /* loaded from: InvalidExtensionException$InvalidFlashExtensionException.class */
    public static class InvalidFlashExtensionException extends InvalidExtensionException {
        private static final long serialVersionUID = 1;

        public InvalidFlashExtensionException(String[] allowedExtension, String extension, String filename) {
            super(allowedExtension, extension, filename);
        }
    }

    /* loaded from: InvalidExtensionException$InvalidMediaExtensionException.class */
    public static class InvalidMediaExtensionException extends InvalidExtensionException {
        private static final long serialVersionUID = 1;

        public InvalidMediaExtensionException(String[] allowedExtension, String extension, String filename) {
            super(allowedExtension, extension, filename);
        }
    }
}
