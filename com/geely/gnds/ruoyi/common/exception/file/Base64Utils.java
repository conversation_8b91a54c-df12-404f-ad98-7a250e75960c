package com.geely.gnds.ruoyi.common.exception.file;

import java.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: Base64Utils.class */
public class Base64Utils {
    private static final Base64.Decoder DECODER = Base64.getDecoder();
    private static final Base64.Encoder ENCODER = Base64.getEncoder();
    private static final Logger log = LoggerFactory.getLogger(Base64Utils.class);

    public static String base64Encode(String text) throws Exception {
        try {
            byte[] textByte = text.getBytes("UTF-8");
            String encodedText = ENCODER.encodeToString(textByte);
            return encodedText;
        } catch (Exception e) {
            log.error("base64Encode Error :{}", e.getMessage());
            throw e;
        }
    }

    public static String base64Decode(String encodedText) throws Exception {
        try {
            String text = new String(DECODER.decode(encodedText), "UTF-8");
            return text;
        } catch (Exception e) {
            log.error("base64Decode Error :{}", e.getMessage());
            throw e;
        }
    }

    public static byte[] base64DecodeBytes(String encodedText) throws Exception {
        try {
            byte[] decode = DECODER.decode(encodedText);
            return decode;
        } catch (Exception e) {
            log.error("base64Decode Error :{}", e.getMessage());
            throw e;
        }
    }

    public static String base64EncodeBytes(byte[] textByte) throws Exception {
        try {
            String encodedText = ENCODER.encodeToString(textByte);
            return encodedText;
        } catch (Exception e) {
            log.error("base64Encode Error :{}", e.getMessage());
            throw e;
        }
    }

    public static void main(String[] args) throws Exception {
        System.out.println(base64Decode("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"));
    }
}
