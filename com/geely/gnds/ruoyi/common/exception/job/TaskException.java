package com.geely.gnds.ruoyi.common.exception.job;

/* loaded from: TaskException.class */
public class TaskException extends Exception {
    private static final long serialVersionUID = 1;
    private Code code;

    /* loaded from: TaskException$Code.class */
    public enum Code {
        TASK_EXISTS,
        NO_TASK_EXISTS,
        TASK_ALREADY_STARTED,
        UNKNOWN,
        CONFIG_ERROR,
        TASK_NODE_NOT_AVAILABLE
    }

    public TaskException(String msg, Code code) {
        this(msg, code, null);
    }

    public TaskException(String msg, Code code, Exception nestedEx) {
        super(msg, nestedEx);
        this.code = code;
    }

    public Code getCode() {
        return this.code;
    }
}
