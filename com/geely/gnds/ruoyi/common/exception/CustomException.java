package com.geely.gnds.ruoyi.common.exception;

/* loaded from: CustomException.class */
public class CustomException extends RuntimeException {
    private static final long serialVersionUID = 1;
    private Integer code;
    private String message;

    public CustomException(String message) {
        this.message = message;
    }

    public CustomException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public CustomException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    @Override // java.lang.Throwable
    public String getMessage() {
        return this.message;
    }

    public Integer getCode() {
        return this.code;
    }
}
