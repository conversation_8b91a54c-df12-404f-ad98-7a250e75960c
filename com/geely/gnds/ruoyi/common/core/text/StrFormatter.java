package com.geely.gnds.ruoyi.common.core.text;

import com.geely.gnds.ruoyi.common.utils.StringUtils;

/* loaded from: StrFormatter.class */
public class StrFormatter {
    public static final String EMPTY_JSON = "{}";
    public static final char C_BACKSLASH = '\\';
    public static final char C_DELIM_START = '{';
    public static final char C_DELIM_END = '}';

    public static String format(String strPattern, Object... argArray) {
        int i;
        int i2;
        if (StringUtils.isEmpty(strPattern) || StringUtils.isEmpty(argArray)) {
            return strPattern;
        }
        int strPatternLength = strPattern.length();
        StringBuilder sbuf = new StringBuilder(strPatternLength + 50);
        int handledPosition = 0;
        int argIndex = 0;
        while (argIndex < argArray.length) {
            int delimIndex = strPattern.indexOf(EMPTY_JSON, handledPosition);
            if (delimIndex == -1) {
                if (handledPosition == 0) {
                    return strPattern;
                }
                sbuf.append((CharSequence) strPattern, handledPosition, strPatternLength);
                return sbuf.toString();
            }
            if (delimIndex > 0 && strPattern.charAt(delimIndex - 1) == '\\') {
                if (delimIndex > 1 && strPattern.charAt(delimIndex - 2) == '\\') {
                    sbuf.append((CharSequence) strPattern, handledPosition, delimIndex - 1);
                    sbuf.append(Convert.utf8Str(argArray[argIndex]));
                    i = delimIndex;
                    i2 = 2;
                } else {
                    argIndex--;
                    sbuf.append((CharSequence) strPattern, handledPosition, delimIndex - 1);
                    sbuf.append('{');
                    i = delimIndex;
                    i2 = 1;
                }
            } else {
                sbuf.append((CharSequence) strPattern, handledPosition, delimIndex);
                sbuf.append(Convert.utf8Str(argArray[argIndex]));
                i = delimIndex;
                i2 = 2;
            }
            handledPosition = i + i2;
            argIndex++;
        }
        sbuf.append((CharSequence) strPattern, handledPosition, strPattern.length());
        return sbuf.toString();
    }
}
