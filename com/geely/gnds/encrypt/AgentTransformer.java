package com.geely.gnds.encrypt;

import com.geely.gnds.encrypt.util.JarUtils;
import com.geely.gnds.encrypt.util.StrUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.UnsupportedEncodingException;
import java.lang.instrument.ClassFileTransformer;
import java.security.ProtectionDomain;

/* loaded from: AgentTransformer.class */
public class AgentTransformer implements ClassFileTransformer {
    private char[] pwd;

    public AgentTransformer(char[] pwd) {
        this.pwd = pwd;
    }

    public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined, ProtectionDomain domain, byte[] classBuffer) throws UnsupportedEncodingException {
        if (className == null || domain == null || loader == null) {
            return classBuffer;
        }
        String projectPath = JarUtils.getRootPath(domain.getCodeSource().getLocation().getPath());
        if (StrUtils.isEmpty(projectPath)) {
            return classBuffer;
        }
        byte[] bytes = JarDecryptor.getInstance().doDecrypt(projectPath, className.replace("/", ConstantEnum.POINT).replace("\\", ConstantEnum.POINT), this.pwd);
        if (bytes != null && bytes[0] == -54 && bytes[1] == -2 && bytes[2] == -70 && bytes[3] == -66) {
            return bytes;
        }
        return classBuffer;
    }
}
