package com.geely.gnds.encrypt;

import com.geely.gnds.encrypt.util.EncryptUtils;
import com.geely.gnds.encrypt.util.IoUtils;
import com.geely.gnds.encrypt.util.JarUtils;
import com.geely.gnds.encrypt.util.Log;
import com.geely.gnds.encrypt.util.Me;
import com.geely.gnds.encrypt.util.StrUtils;
import com.geely.gnds.encrypt.util.SysUtils;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;

/* loaded from: JarDecryptor.class */
public class JarDecryptor {
    private static final JarDecryptor single = new JarDecryptor();
    private char[] code = SysUtils.makeMarchinCode();
    private static final String ENCRYPT_PATH = "META-INF/.classes/";

    public static JarDecryptor getInstance() {
        return single;
    }

    /* JADX WARN: Type inference failed for: r0v6, types: [char[], char[][]] */
    public byte[] doDecrypt(String projectPath, String fileName, char[] password) {
        long t1 = System.currentTimeMillis();
        File workDir = new File(projectPath);
        byte[] bytes = readEncryptedFile(workDir, fileName);
        if (bytes == null) {
            return null;
        }
        StrUtils.merger(new char[]{password, fileName.toCharArray()});
        Log.debug("开始解密：" + fileName);
        byte[] bytes2 = Me.decrypt(bytes);
        long t2 = System.currentTimeMillis();
        Log.debug("解密: " + fileName + " (" + (t2 - t1) + " ms)");
        return bytes2;
    }

    public static byte[] readEncryptedFile(File workDir, String name) throws IOException {
        byte[] bytes = null;
        String fileName = ENCRYPT_PATH + name;
        if (workDir.isFile()) {
            bytes = JarUtils.getFileFromJar(workDir, fileName);
        } else {
            File file = new File(workDir, fileName);
            if (file.exists()) {
                bytes = IoUtils.readFileToByte(file);
            }
        }
        return bytes;
    }

    public static char[] readPassFromJar(File workDir) throws IOException {
        byte[] passbyte = readEncryptedFile(workDir, Const.CONFIG_PASS);
        if (passbyte != null) {
            char[] pass = StrUtils.toChars(passbyte);
            return EncryptUtils.md5(pass);
        }
        return null;
    }

    public InputStream decryptConfigFile(String path, InputStream in, char[] pass) throws UnsupportedEncodingException {
        if (path.endsWith(".class")) {
            return in;
        }
        String projectPath = JarUtils.getRootPath(null);
        if (StrUtils.isEmpty(projectPath)) {
            return in;
        }
        byte[] bytes = null;
        try {
            bytes = IoUtils.toBytes(in);
        } catch (Exception e) {
            Log.error(e.getMessage());
        }
        if (bytes == null || bytes.length == 0) {
            bytes = doDecrypt(projectPath, path, pass);
        }
        Log.debug("解密文件-->" + path + "内容：" + new String(bytes));
        if (bytes == null) {
            return in;
        }
        return new ByteArrayInputStream(bytes);
    }
}
