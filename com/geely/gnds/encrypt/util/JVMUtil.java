package com.geely.gnds.encrypt.util;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.NoSuchElementException;
import sun.misc.Unsafe;

/* loaded from: JVMUtil.class */
public class JVMUtil {
    private static final Method findNative;
    private static final ClassLoader classLoader;
    public static final Unsafe unsafe = getUnsafe();

    static {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("windows")) {
            String vmName = System.getProperty("java.vm.name");
            String dll = vmName.contains("Client VM") ? "/bin/client/jvm.dll" : "/bin/server/jvm.dll";
            try {
                System.load(System.getProperty("java.home") + dll);
                classLoader = JVMUtil.class.getClassLoader();
            } catch (UnsatisfiedLinkError e) {
                throw new RuntimeException("Cannot find jvm.dll. Unsupported JVM?");
            }
        } else {
            classLoader = null;
        }
        try {
            findNative = ClassLoader.class.getDeclaredMethod("findNative", ClassLoader.class, String.class);
            findNative.setAccessible(true);
        } catch (NoSuchMethodException e2) {
            throw new RuntimeException("Method ClassLoader.findNative not found");
        }
    }

    public static long getSymbol(String name) {
        long address = lookup(name);
        if (address == 0) {
            throw new NoSuchElementException("No such symbol: " + name);
        }
        return getLong(address);
    }

    public static long getLong(long addr) {
        return unsafe.getLong(addr);
    }

    public static void putAddress(long addr, long val) {
        unsafe.putAddress(addr, val);
    }

    public static long lookup(String name) {
        try {
            return ((Long) findNative.invoke(null, classLoader, name)).longValue();
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e2) {
            throw new RuntimeException(e2.getTargetException());
        }
    }

    private static Unsafe getUnsafe() throws NoSuchFieldException {
        try {
            Field f = Unsafe.class.getDeclaredField("theUnsafe");
            f.setAccessible(true);
            return (Unsafe) f.get(null);
        } catch (Exception e) {
            throw new RuntimeException("Unable to get Unsafe", e);
        }
    }
}
