package com.geely.gnds.encrypt.util;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicBoolean;

/* loaded from: Log.class */
public final class Log {
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS);
    public static final AtomicBoolean ENABLE_DEBUG = new AtomicBoolean(false);

    private Log() {
    }

    public static void debug(String msg) {
        if (ENABLE_DEBUG.get()) {
            String log = DATE_TIME_FORMATTER.format(LocalDateTime.now()) + " [DEBUG] " + msg;
            System.out.println(log);
        }
    }

    public static void debug(Class<?> clazz, String msg) {
        if (ENABLE_DEBUG.get()) {
            String log = DATE_TIME_FORMATTER.format(LocalDateTime.now()) + " [DEBUG] " + clazz.getName() + ": " + msg;
            System.out.println(log);
        }
    }

    public static void simpleInfo(String msg) {
        System.out.println("[INFO] " + msg);
    }

    public static void info(String msg) {
        System.out.println(DATE_TIME_FORMATTER.format(LocalDateTime.now()) + " [ INFO] " + msg);
    }

    public static void info(Class<?> clazz, String msg) {
        System.out.println(DATE_TIME_FORMATTER.format(LocalDateTime.now()) + " [ INFO] " + clazz.getName() + ": " + msg);
    }

    public static void warn(String msg) {
        System.out.println(DATE_TIME_FORMATTER.format(LocalDateTime.now()) + " [ WARN] " + msg);
    }

    public static void warn(Class<?> clazz, String msg) {
        System.out.println(DATE_TIME_FORMATTER.format(LocalDateTime.now()) + " [ WARN] " + clazz.getName() + ": " + msg);
    }

    public static void error(String msg) {
        System.out.println(DATE_TIME_FORMATTER.format(LocalDateTime.now()) + " [ERROR] " + msg);
    }

    public static void error(Class<?> clazz, String msg) {
        System.out.println(DATE_TIME_FORMATTER.format(LocalDateTime.now()) + " [ERROR] " + clazz.getName() + ": " + msg);
    }
}
