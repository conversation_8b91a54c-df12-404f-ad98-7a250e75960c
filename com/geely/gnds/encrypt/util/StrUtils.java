package com.geely.gnds.encrypt.util;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import org.springframework.boot.loader.util.SystemPropertyUtils;

/* loaded from: StrUtils.class */
public class StrUtils {
    public static List<String> toList(String strs) {
        List<String> list = new ArrayList<>();
        if (strs != null && strs.length() > 0) {
            String[] ss = strs.split(ConstantEnum.COMMA);
            for (String s : ss) {
                if (s.trim().length() > 0) {
                    list.add(s.trim());
                }
            }
        }
        return list;
    }

    public static boolean isEmpty(String str) {
        return str == null || str.length() == 0;
    }

    public static boolean isEmpty(char[] str) {
        return str == null || str.length == 0;
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean isNotEmpty(char[] str) {
        return !isEmpty(str);
    }

    public static char[] merger(char[]... bts) {
        int lenght = 0;
        for (char[] cArr : bts) {
            lenght += cArr.length;
        }
        char[] bt = new char[lenght];
        int lastLength = 0;
        for (char[] b : bts) {
            System.arraycopy(b, 0, bt, lastLength, b.length);
            lastLength += b.length;
        }
        return bt;
    }

    public static char[] toChars(byte[] bytes) {
        byte[] bytes0 = new byte[bytes.length];
        System.arraycopy(bytes, 0, bytes0, 0, bytes.length);
        Charset cs = Charset.forName("UTF-8");
        ByteBuffer bb = ByteBuffer.allocate(bytes.length);
        bb.put(bytes).flip();
        CharBuffer cb = cs.decode(bb);
        return cb.array();
    }

    public static byte[] toBytes(char[] chars) {
        char[] chars0 = new char[chars.length];
        System.arraycopy(chars, 0, chars0, 0, chars.length);
        CharBuffer charBuffer = CharBuffer.wrap(chars0);
        ByteBuffer byteBuffer = Charset.forName("UTF-8").encode(charBuffer);
        byte[] bytes = Arrays.copyOfRange(byteBuffer.array(), byteBuffer.position(), byteBuffer.limit());
        Arrays.fill(charBuffer.array(), (char) 0);
        Arrays.fill(byteBuffer.array(), (byte) 0);
        return bytes;
    }

    public static boolean equal(char[] char1, char[] char2) {
        if (char1.length != char2.length) {
            return false;
        }
        for (int i = 0; i < char1.length; i++) {
            if (char1[i] != char2[i]) {
                return false;
            }
        }
        return true;
    }

    public static boolean containsArray(String str, String[] array) {
        for (String e : array) {
            if (str.contains(e)) {
                return true;
            }
        }
        return false;
    }

    public static String toCharArrayCode(char[] chars) {
        List<Integer> list = new ArrayList<>();
        for (char c : chars) {
            list.add(Integer.valueOf(c));
        }
        return list.toString().replace("[", "{").replace("]", SystemPropertyUtils.PLACEHOLDER_SUFFIX);
    }

    public static String insertStringArray(String[] arrayStr, String insertStr, String pos) {
        StringBuffer newStr = new StringBuffer();
        boolean isInsert = false;
        for (int i = 0; i < arrayStr.length; i++) {
            newStr.append(arrayStr[i]).append("\r\n");
            if (arrayStr[i].startsWith(pos)) {
                newStr.append(insertStr).append("\r\n");
                isInsert = true;
            }
        }
        if (!isInsert) {
            newStr.append(insertStr).append("\r\n");
        }
        return newStr.toString();
    }

    public static boolean isMatch(String match, String testString) {
        String regex = match.replaceAll("\\?", "(.?)").replaceAll("\\*+", "(.*?)");
        return Pattern.matches(regex, testString);
    }

    public static boolean isMatchs(List<String> matches, String testString) {
        return isMatchs(matches, testString, false);
    }

    public static boolean isMatchs(List<String> matches, String testString, boolean dv) {
        if (matches == null || matches.size() == 0) {
            return dv;
        }
        for (String m : matches) {
            if (isMatch(m, testString) || testString.startsWith(m) || testString.endsWith(m)) {
                return true;
            }
        }
        return false;
    }
}
