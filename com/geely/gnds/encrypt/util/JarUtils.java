package com.geely.gnds.encrypt.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

/* loaded from: JarUtils.class */
public class JarUtils {
    public static final String[] DLE_FILES = {".DS_Store", "Thumbs.db"};

    public static String doJar(String jarDir, String targetJar) throws IOException {
        File jarDirFile = new File(jarDir);
        List<File> files = new ArrayList<>();
        IoUtils.listFile(files, jarDirFile);
        ZipOutputStream zos = null;
        OutputStream out = null;
        try {
            try {
                File jar = new File(targetJar);
                if (jar.exists()) {
                    jar.delete();
                }
                out = new FileOutputStream(jar);
                zos = new ZipOutputStream(out);
                for (File file : files) {
                    if (!isDel(file)) {
                        String fileName = file.getAbsolutePath().substring(jarDirFile.getAbsolutePath().length() + 1).replace(File.separator, "/");
                        if (file.isDirectory()) {
                            ZipEntry ze = new ZipEntry(fileName + "/");
                            ze.setTime(System.currentTimeMillis());
                            zos.putNextEntry(ze);
                            zos.closeEntry();
                        } else if (fileName.endsWith(".jar")) {
                            byte[] bytes = IoUtils.readFileToByte(file);
                            ZipEntry ze2 = new ZipEntry(fileName);
                            ze2.setMethod(0);
                            ze2.setSize(bytes.length);
                            ze2.setTime(System.currentTimeMillis());
                            ze2.setCrc(IoUtils.crc32(bytes));
                            zos.putNextEntry(ze2);
                            zos.write(bytes);
                            zos.closeEntry();
                        } else {
                            ZipEntry ze3 = new ZipEntry(fileName);
                            ze3.setTime(System.currentTimeMillis());
                            zos.putNextEntry(ze3);
                            zos.write(IoUtils.readFileToByte(file));
                            zos.closeEntry();
                        }
                    }
                }
                IoUtils.close(zos, out);
            } catch (Exception e) {
                e.printStackTrace();
                IoUtils.close(zos, out);
            }
            return targetJar;
        } catch (Throwable th) {
            IoUtils.close(zos, out);
            throw th;
        }
    }

    public static List<String> unJar(String jarPath, String targetDir) {
        return unJar(jarPath, targetDir, null);
    }

    public static List<String> unJar(String jarPath, String targetDir, List<String> includeFiles) throws IOException {
        List<String> list = new ArrayList<>();
        File target = new File(targetDir);
        if (!target.exists()) {
            target.mkdirs();
        }
        ZipFile zipFile = null;
        try {
            try {
                zipFile = new ZipFile(new File(jarPath));
                Enumeration<?> entries = zipFile.entries();
                while (entries.hasMoreElements()) {
                    ZipEntry entry = entries.nextElement();
                    if (entry.isDirectory()) {
                        File targetFile = new File(target, entry.getName());
                        if (!targetFile.exists()) {
                            targetFile.mkdirs();
                        }
                    } else {
                        int lastSeparatorIndex = entry.getName().lastIndexOf("/");
                        if (lastSeparatorIndex > 0) {
                            File targetFile2 = new File(target, entry.getName().substring(0, lastSeparatorIndex));
                            if (!targetFile2.exists()) {
                                targetFile2.mkdirs();
                            }
                        }
                    }
                }
                Enumeration<?> entries2 = zipFile.entries();
                while (entries2.hasMoreElements()) {
                    ZipEntry entry2 = entries2.nextElement();
                    if (!entry2.isDirectory()) {
                        File targetFile3 = new File(target, entry2.getName());
                        if (includeFiles == null || includeFiles.size() <= 0 || includeFiles.contains(targetFile3.getName())) {
                            byte[] bytes = IoUtils.toBytes(zipFile.getInputStream(entry2));
                            IoUtils.writeFile(targetFile3, bytes);
                            list.add(targetFile3.getAbsolutePath());
                        }
                    }
                }
                IoUtils.close(zipFile, null);
            } catch (Exception e) {
                e.printStackTrace();
                IoUtils.close(zipFile, null);
            }
            return list;
        } catch (Throwable th) {
            IoUtils.close(zipFile, null);
            throw th;
        }
    }

    public static String releaseFileFromJar(File zip, String fileName, File targetFile) throws IOException {
        byte[] bytes = getFileFromJar(zip, fileName);
        if (bytes == null) {
            return null;
        }
        IoUtils.writeFile(targetFile, bytes);
        return targetFile.getAbsolutePath();
    }

    public static byte[] getFileFromJar(File zip, String fileName) throws IOException {
        ZipFile zipFile = null;
        try {
            try {
                if (!zip.exists()) {
                    IoUtils.close(null);
                    return null;
                }
                zipFile = new ZipFile(zip);
                ZipEntry zipEntry = zipFile.getEntry(fileName);
                if (zipEntry == null) {
                    IoUtils.close(zipFile);
                    return null;
                }
                InputStream is = zipFile.getInputStream(zipEntry);
                byte[] bytes = IoUtils.toBytes(is);
                IoUtils.close(zipFile);
                return bytes;
            } catch (IOException e) {
                e.printStackTrace();
                IoUtils.close(zipFile);
                return null;
            }
        } catch (Throwable th) {
            IoUtils.close(zipFile);
            throw th;
        }
    }

    public static boolean isDel(File file) {
        for (String f : DLE_FILES) {
            if (file.getAbsolutePath().endsWith(f)) {
                return true;
            }
        }
        return false;
    }

    public static String getRootPath(String path) throws UnsupportedEncodingException {
        if (path == null) {
            path = JarUtils.class.getResource("").getPath();
        }
        try {
            path = URLDecoder.decode(path, "utf-8");
        } catch (UnsupportedEncodingException e) {
        }
        if (path.startsWith("jar:") || path.startsWith("war:")) {
            path = path.substring(4);
        }
        if (path.startsWith("file:")) {
            path = path.substring(5);
        }
        if (path.contains("*")) {
            return path.substring(0, path.indexOf("*"));
        }
        if (path.contains("WEB-INF")) {
            return path.substring(0, path.indexOf("WEB-INF"));
        }
        if (path.contains("!")) {
            return path.substring(0, path.indexOf("!"));
        }
        if (path.endsWith(".jar") || path.endsWith(".war")) {
            return path;
        }
        if (path.contains("/classes/")) {
            return path.substring(0, path.indexOf("/classes/") + 9);
        }
        return null;
    }
}
