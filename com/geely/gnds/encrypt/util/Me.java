package com.geely.gnds.encrypt.util;

import com.geely.gnds.doip.client.DoipUtil;
import com.sun.jna.Library;
import com.sun.jna.Native;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.locks.ReentrantLock;

/* loaded from: Me.class */
public class Me {
    private static final ReentrantLock INIT_LOCK = new ReentrantLock();
    private static volatile SecSo secSo;

    /* loaded from: Me$SecSo.class */
    public interface SecSo extends Library {
        String EncryptAes(String str);

        String DecryptAes(String str);
    }

    public static void init() {
        if (secSo != null) {
            return;
        }
        INIT_LOCK.lock();
        try {
            try {
                if (secSo == null) {
                    secSo = load();
                    Log.info("Security library initialized successfully");
                }
                INIT_LOCK.unlock();
            } catch (Exception e) {
                Log.error("Failed to initialize security library" + e.getMessage());
                throw new SecurityException("Security initialization failed", e);
            }
        } catch (Throwable th) {
            INIT_LOCK.unlock();
            throw th;
        }
    }

    public static String decrypt(String input) {
        checkInitialized();
        try {
            return secSo.DecryptAes(input);
        } catch (Exception e) {
            Log.error("Decryption failed for input: " + input + e.getMessage());
            throw new SecurityException("Decryption error", e);
        }
    }

    public static String encrypt(String input) {
        checkInitialized();
        try {
            return secSo.EncryptAes(input);
        } catch (Exception e) {
            Log.error("Encryption failed for input: " + input + e.getMessage());
            throw new SecurityException("Encryption error", e);
        }
    }

    public static byte[] encrypt(byte[] input) {
        String base64Str = Base64.getEncoder().encodeToString(input);
        String encrypted = encrypt(base64Str);
        return encrypted.getBytes(StandardCharsets.UTF_8);
    }

    public static byte[] decrypt(byte[] input) {
        String decrypted = decrypt(new String(input, StandardCharsets.UTF_8));
        return Base64.getDecoder().decode(decrypted);
    }

    private static SecSo load() throws Exception {
        String dllName = new String(Base64.getDecoder().decode("amVjLmRsbA=="), StandardCharsets.UTF_8);
        File jdkBinDir = new File((String) Objects.requireNonNull(System.getProperty("java.home"))).getParentFile().toPath().resolve("bin").toFile();
        File dllFile = new File(jdkBinDir, dllName);
        if (dllFile.exists()) {
            return (SecSo) Native.load(dllFile.getAbsolutePath(), SecSo.class);
        }
        String resourcePath = new String(Base64.getDecoder().decode("L2VuY3J5cHQuZGxs"), StandardCharsets.UTF_8);
        InputStream is = Me.class.getResourceAsStream(resourcePath);
        Throwable th = null;
        try {
            if (is == null) {
                throw new SecurityException("Security DLL not found in resources: " + resourcePath);
            }
            File tempFile = createTempFileFromStream(is);
            SecSo secSo2 = (SecSo) Native.load(tempFile.getAbsolutePath(), SecSo.class);
            if (is != null) {
                if (0 != 0) {
                    try {
                        is.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    is.close();
                }
            }
            return secSo2;
        } catch (Throwable th3) {
            if (is != null) {
                if (0 != 0) {
                    try {
                        is.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    is.close();
                }
            }
            throw th3;
        }
    }

    private static synchronized File createTempFileFromStream(InputStream is) throws Exception {
        File tempFile = File.createTempFile("sec_", ".dll");
        tempFile.deleteOnExit();
        FileOutputStream fos = new FileOutputStream(tempFile);
        Throwable th = null;
        try {
            byte[] buffer = new byte[DoipUtil.MAX_BYTE_ARRAY_SIZE];
            while (true) {
                int bytesRead = is.read(buffer);
                if (bytesRead == -1) {
                    break;
                }
                fos.write(buffer, 0, bytesRead);
            }
            return tempFile;
        } finally {
            if (fos != null) {
                if (0 != 0) {
                    try {
                        fos.close();
                    } catch (Throwable th2) {
                        th.addSuppressed(th2);
                    }
                } else {
                    fos.close();
                }
            }
        }
    }

    private static void checkInitialized() {
        if (secSo == null) {
            init();
        }
    }
}
