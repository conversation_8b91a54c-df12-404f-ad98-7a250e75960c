package com.geely.gnds.encrypt.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.zip.CRC32;

/* loaded from: IoUtils.class */
public class IoUtils {
    public static void writeFile(File file, byte[] fileBytes) throws IOException {
        OutputStream os = null;
        try {
            try {
                os = new FileOutputStream(file);
                os.write(fileBytes, 0, fileBytes.length);
                os.flush();
                close(os);
            } catch (Exception e) {
                e.printStackTrace();
                close(os);
            }
        } catch (Throwable th) {
            close(os);
            throw th;
        }
    }

    public static byte[] readFileToByte(File file) {
        try {
            FileInputStream inputStream = new FileInputStream(file);
            return toBytes(inputStream);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static byte[] toBytes(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        try {
            byte[] buffer = new byte[4096];
            while (true) {
                int n = input.read(buffer);
                if (-1 != n) {
                    output.write(buffer, 0, n);
                } else {
                    byte[] byteArray = output.toByteArray();
                    close(output, input);
                    return byteArray;
                }
            }
        } catch (Throwable th) {
            close(output, input);
            throw th;
        }
    }

    public static void listFile(List<File> fileList, File dir, String endWith) {
        if (!dir.exists()) {
            throw new IllegalArgumentException("目录[" + dir.getAbsolutePath() + "]不存在");
        }
        File[] files = dir.listFiles();
        for (File f : files) {
            if (f.isDirectory()) {
                listFile(fileList, f, endWith);
            } else if (f.isFile() && f.getName().endsWith(endWith)) {
                fileList.add(f);
            }
        }
    }

    public static void listFile(List<File> filess, File dir) {
        if (!dir.exists()) {
            throw new IllegalArgumentException("目录[" + dir.getAbsolutePath() + "]不存在");
        }
        File[] files = dir.listFiles();
        for (File f : files) {
            filess.add(f);
            if (f.isDirectory()) {
                listFile(filess, f);
            }
        }
    }

    public static void delete(File dir) {
        if (!dir.exists()) {
            return;
        }
        if (dir.isFile()) {
            dir.delete();
        } else {
            File[] files = dir.listFiles();
            for (File f : files) {
                delete(f);
            }
        }
        dir.delete();
    }

    public static int copy(InputStream input, OutputStream output) throws IOException {
        byte[] buffer = new byte[4096];
        int count = 0;
        while (true) {
            int n = input.read(buffer);
            if (-1 != n) {
                output.write(buffer, 0, n);
                count += n;
            } else {
                return count;
            }
        }
    }

    public static long crc32(byte[] bytes) {
        CRC32 crc = new CRC32();
        crc.update(bytes);
        return crc.getValue();
    }

    public static void close(Closeable... outs) throws IOException {
        if (outs != null) {
            for (Closeable out : outs) {
                if (out != null) {
                    try {
                        out.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    public static String readTxtFile(File file) throws IOException {
        StringBuffer txt = new StringBuffer("");
        InputStreamReader read = null;
        BufferedReader bufferedReader = null;
        try {
            try {
                read = new InputStreamReader(new FileInputStream(file), "UTF-8");
                bufferedReader = new BufferedReader(read);
                while (true) {
                    String lineTxt = bufferedReader.readLine();
                    if (lineTxt == null) {
                        break;
                    }
                    txt.append(lineTxt).append("\r\n");
                }
                close(bufferedReader, read);
            } catch (Exception e) {
                e.printStackTrace();
                close(bufferedReader, read);
            }
            return txt.toString();
        } catch (Throwable th) {
            close(bufferedReader, read);
            throw th;
        }
    }

    public static void writeTxtFile(File file, String txt) throws IOException {
        BufferedWriter out = null;
        try {
            try {
                if (!file.exists()) {
                    file.mkdirs();
                    file.delete();
                    file.createNewFile();
                }
                out = new BufferedWriter(new FileWriter(file));
                out.write(txt);
                out.flush();
                close(out);
            } catch (Exception e) {
                e.printStackTrace();
                close(out);
            }
        } catch (Throwable th) {
            close(out);
            throw th;
        }
    }

    public static byte[] merger(byte[]... bts) {
        int lenght = 0;
        for (byte[] bArr : bts) {
            lenght += bArr.length;
        }
        byte[] bt = new byte[lenght];
        int lastLength = 0;
        for (byte[] b : bts) {
            System.arraycopy(b, 0, bt, lastLength, b.length);
            lastLength += b.length;
        }
        return bt;
    }

    public static String readContentFromFile(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        FileInputStream fileInputStream = null;
        InputStreamReader read = null;
        BufferedReader bufferedReader = null;
        try {
            try {
                fileInputStream = new FileInputStream(file);
                read = new InputStreamReader(fileInputStream, StandardCharsets.UTF_8);
                bufferedReader = new BufferedReader(read);
                while (true) {
                    String line = bufferedReader.readLine();
                    if (line != null) {
                        content.append(line).append(System.lineSeparator());
                    } else {
                        close(bufferedReader, read, fileInputStream);
                        return content.toString();
                    }
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } catch (Throwable th) {
            close(bufferedReader, read, fileInputStream);
            throw th;
        }
    }
}
