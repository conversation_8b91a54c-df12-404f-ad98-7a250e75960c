package com.geely.gnds.encrypt.util;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.tester.util.RsaUtils;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;

/* loaded from: EncryptUtils.class */
public class EncryptUtils {
    public static final char[] SALT = {'(', '^', '#', '$', '!', '%', 'f', 'u', 'c', 'k', 'y', 'o', 'u', 'c', 'r', 'a', 'c', 'k', 'g', 'n', 'd', 's', 's', 'o', 'f', 't', '%', '!', '$', '#', '^', ')'};
    private static int KEY_LENGTH = DoipUtil.MAX_BYTE_ARRAY_SIZE;

    /* JADX WARN: Type inference failed for: r1v3, types: [char[], char[][]] */
    public static byte[] en(byte[] msg, char[] key, int type) {
        if (type == 1) {
            return enAES(msg, md5(StrUtils.merger(new char[]{key, SALT}), true));
        }
        return enSimple(msg, key);
    }

    /* JADX WARN: Type inference failed for: r1v3, types: [char[], char[][]] */
    public static byte[] de(byte[] msg, char[] key, int type) {
        if (type == 1) {
            return deAES(msg, md5(StrUtils.merger(new char[]{key, SALT}), true));
        }
        return deSimple(msg, key);
    }

    public static byte[] md5byte(char[] str) throws NoSuchAlgorithmException {
        byte[] b = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] buffer = StrUtils.toBytes(str);
            md.update(buffer);
            b = md.digest();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return b;
    }

    public static char[] md5(char[] str) {
        return md5(str, false);
    }

    /* JADX WARN: Type inference failed for: r0v13, types: [char[], char[][]] */
    public static char[] md5(char[] str, boolean sh0rt) throws NoSuchAlgorithmException {
        byte[] s = md5byte(str);
        if (s == null) {
            return null;
        }
        int begin = 0;
        int end = s.length;
        if (sh0rt) {
            begin = 8;
            end = 16;
        }
        char[] result = new char[0];
        for (int i = begin; i < end; i++) {
            result = StrUtils.merger(new char[]{result, Integer.toHexString((255 & s[i]) | (-256)).substring(6).toCharArray()});
        }
        return result;
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r3v1, types: [char[], char[][]] */
    /* JADX WARN: Type inference failed for: r3v5, types: [char[], char[][]] */
    public static byte[] enSimple(byte[] msg, int start, int end, char[] key) {
        byte[] keys = IoUtils.merger(new byte[]{md5byte(StrUtils.merger(new char[]{key, SALT})), md5byte(StrUtils.merger(new char[]{SALT, key}))});
        for (int i = start; i <= end; i++) {
            msg[i] = (byte) (msg[i] ^ keys[i % keys.length]);
        }
        return msg;
    }

    /* JADX WARN: Type inference failed for: r0v1, types: [byte[], byte[][]] */
    /* JADX WARN: Type inference failed for: r3v1, types: [char[], char[][]] */
    /* JADX WARN: Type inference failed for: r3v5, types: [char[], char[][]] */
    public static byte[] deSimple(byte[] msg, int start, int end, char[] key) {
        byte[] keys = IoUtils.merger(new byte[]{md5byte(StrUtils.merger(new char[]{key, SALT})), md5byte(StrUtils.merger(new char[]{SALT, key}))});
        for (int i = start; i <= end; i++) {
            msg[i] = (byte) (msg[i] ^ keys[i % keys.length]);
        }
        return msg;
    }

    public static byte[] enSimple(byte[] msg, char[] key) {
        return enSimple(msg, 0, msg.length - 1, key);
    }

    public static byte[] deSimple(byte[] msg, char[] key) {
        return deSimple(msg, 0, msg.length - 1, key);
    }

    public static String enRSA(String str, String publicKey) throws UnsupportedEncodingException {
        try {
            byte[] in = str.getBytes("UTF-8");
            byte[] out = enRSA(in, publicKey);
            String outStr = Base64.getEncoder().encodeToString(out);
            return outStr;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static byte[] enRSA(byte[] msg, String publicKey) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException {
        try {
            byte[] decoded = Base64.getDecoder().decode(publicKey.getBytes("UTF-8"));
            RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance(RsaUtils.KEY_ALGORITHM).generatePublic(new X509EncodedKeySpec(decoded));
            Cipher cipher = Cipher.getInstance(RsaUtils.KEY_ALGORITHM);
            cipher.init(1, pubKey);
            return cipherDoFinal(cipher, msg, 1);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String deRSA(String str, String privateKey) {
        try {
            byte[] inputByte = Base64.getDecoder().decode(str.getBytes("UTF-8"));
            String outStr = new String(deRSA(inputByte, privateKey));
            return outStr;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static byte[] deRSA(byte[] msg, String privateKey) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException {
        try {
            byte[] decoded = Base64.getDecoder().decode(privateKey.getBytes("UTF-8"));
            RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance(RsaUtils.KEY_ALGORITHM).generatePrivate(new PKCS8EncodedKeySpec(decoded));
            Cipher cipher = Cipher.getInstance(RsaUtils.KEY_ALGORITHM);
            cipher.init(2, priKey);
            return cipherDoFinal(cipher, msg, 2);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /* JADX WARN: Type inference failed for: r0v18, types: [byte[], byte[][]] */
    private static byte[] cipherDoFinal(Cipher cipher, byte[] msg, int mode) throws Exception {
        int in_length = 0;
        if (mode == 1) {
            in_length = (KEY_LENGTH / 8) - 11;
        } else if (mode == 2) {
            in_length = KEY_LENGTH / 8;
        }
        byte[] in = new byte[in_length];
        byte[] out = new byte[0];
        for (int i = 0; i < msg.length; i++) {
            if (msg.length - i < in_length && i % in_length == 0) {
                in = new byte[msg.length - i];
            }
            in[i % in_length] = msg[i];
            if (i == msg.length - 1 || (i % in_length) + 1 == in_length) {
                out = IoUtils.merger(new byte[]{out, cipher.doFinal(in)});
            }
        }
        return out;
    }

    public static Map<Integer, String> genKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(RsaUtils.KEY_ALGORITHM);
        keyPairGen.initialize(KEY_LENGTH, new SecureRandom());
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        BigInteger publicExponent = publicKey.getPublicExponent();
        BigInteger modulus = publicKey.getModulus();
        String publicKeyString = new String(Base64.getEncoder().encode(publicKey.getEncoded()));
        String privateKeyString = new String(Base64.getEncoder().encode(privateKey.getEncoded()));
        Map<Integer, String> keyMap = new HashMap<>();
        keyMap.put(0, publicKeyString);
        keyMap.put(1, privateKeyString);
        keyMap.put(2, modulus.toString(16));
        keyMap.put(3, publicExponent.toString(16));
        return keyMap;
    }

    public static String enAES(String str, char[] key) {
        byte[] encrypted = null;
        try {
            encrypted = enAES(str.getBytes("utf-8"), key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (encrypted == null) {
            return null;
        }
        return Base64.getEncoder().encodeToString(encrypted);
    }

    public static byte[] enAES(byte[] msg, char[] key) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException {
        byte[] encrypted = null;
        try {
            byte[] raw = StrUtils.toBytes(key);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(1, skeySpec);
            encrypted = cipher.doFinal(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return encrypted;
    }

    public static String deAES(String str, char[] key) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException {
        String originalString = null;
        byte[] msg = Base64.getDecoder().decode(str);
        byte[] original = deAES(msg, key);
        try {
            originalString = new String(original, "utf-8");
        } catch (Exception e) {
        }
        return originalString;
    }

    public static byte[] deAES(byte[] msg, char[] key) throws BadPaddingException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, InvalidKeyException {
        byte[] original = null;
        try {
            byte[] raw = StrUtils.toBytes(key);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(2, skeySpec);
            original = cipher.doFinal(msg);
        } catch (Exception e) {
        }
        return original;
    }

    public static char[] randChar(int lenght) {
        char[] result = new char[lenght];
        Character[] chars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '=', '_', '+', '.'};
        List<Character> list = Arrays.asList(chars);
        Collections.shuffle(list);
        for (int i = 0; i < lenght; i++) {
            result[i] = list.get(i).charValue();
        }
        return result;
    }
}
