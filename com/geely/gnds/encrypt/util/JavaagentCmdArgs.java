package com.geely.gnds.encrypt.util;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.LineNumberReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/* loaded from: JavaagentCmdArgs.class */
public class JavaagentCmdArgs {
    private String password;
    private String skipProjectPathPrefix;
    private String decryptProjectPathPrefix;
    private boolean debug;

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isDebug() {
        return this.debug;
    }

    public void setDebug(boolean debug) {
        this.debug = debug;
    }

    public String toString() {
        return "JavaagentCmdArgs{password='" + this.password + "'skipProjectPathPrefix='" + this.skipProjectPathPrefix + "'decryptProjectPathPrefix='" + this.decryptProjectPathPrefix + "', debug=" + this.debug + '}';
    }

    private static void printEnvVariables() {
        Map<String, String> env = System.getenv();
        for (String key : env.keySet()) {
            System.out.println(key + ": " + env.get(key));
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:37:0x01aa  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static com.geely.gnds.encrypt.util.JavaagentCmdArgs parseJavaagentCmdArgs(java.lang.String r6) throws java.lang.InterruptedException, java.io.IOException {
        /*
            Method dump skipped, instructions count: 488
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.encrypt.util.JavaagentCmdArgs.parseJavaagentCmdArgs(java.lang.String):com.geely.gnds.encrypt.util.JavaagentCmdArgs");
    }

    public static List<String> runShell(String shStr) throws InterruptedException, IOException {
        List<String> strList = new ArrayList<>(4);
        try {
            Process process = Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", shStr}, (String[]) null, (File) null);
            InputStreamReader inputStreamReader = new InputStreamReader(process.getInputStream());
            LineNumberReader lineNumberReader = new LineNumberReader(inputStreamReader);
            process.waitFor();
            while (true) {
                String line = lineNumberReader.readLine();
                if (line != null) {
                    strList.add(line);
                } else {
                    return strList;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Get result from shell error. \n" + shStr, e);
        }
    }

    public String getSkipProjectPathPrefix() {
        return this.skipProjectPathPrefix;
    }

    public void setSkipProjectPathPrefix(String skipProjectPathPrefix) {
        this.skipProjectPathPrefix = skipProjectPathPrefix;
    }

    public String getDecryptProjectPathPrefix() {
        return this.decryptProjectPathPrefix;
    }

    public void setDecryptProjectPathPrefix(String decryptProjectPathPrefix) {
        this.decryptProjectPathPrefix = decryptProjectPathPrefix;
    }
}
