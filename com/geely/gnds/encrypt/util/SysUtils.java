package com.geely.gnds.encrypt.util;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Scanner;

/* loaded from: SysUtils.class */
public class SysUtils {
    public static String runCmd(String cmd, int line) throws IOException {
        StringBuffer sb = new StringBuffer();
        try {
            Process process = Runtime.getRuntime().exec(cmd);
            process.getOutputStream().close();
            Scanner sc = new Scanner(process.getInputStream());
            int i = 0;
            while (sc.hasNextLine()) {
                i++;
                String str = sc.nextLine();
                if (line <= 0) {
                    sb.append(str).append("\r\n");
                } else if (i == line) {
                    String strTrim = str.trim();
                    IoUtils.close(sc);
                    return strTrim;
                }
            }
            sc.close();
            IoUtils.close(sc);
        } catch (Exception e) {
            IoUtils.close(null);
        } catch (Throwable th) {
            IoUtils.close(null);
            throw th;
        }
        return sb.toString();
    }

    public static String runCmd(String cmd, String substr) throws IOException {
        try {
            Process process = Runtime.getRuntime().exec(cmd);
            process.getOutputStream().close();
            Scanner sc = new Scanner(process.getInputStream());
            while (sc.hasNextLine()) {
                String str = sc.nextLine();
                if (str != null && str.contains(substr)) {
                    String strTrim = str.trim();
                    IoUtils.close(sc);
                    return strTrim;
                }
            }
            sc.close();
            IoUtils.close(sc);
            return null;
        } catch (Exception e) {
            IoUtils.close(null);
            return null;
        } catch (Throwable th) {
            IoUtils.close(null);
            throw th;
        }
    }

    public static List<String> getMacList() throws SocketException {
        NetworkInterface network;
        byte[] mac;
        ArrayList<String> list = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        try {
            Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces();
            while (en.hasMoreElements()) {
                NetworkInterface iface = en.nextElement();
                List<InterfaceAddress> addrs = iface.getInterfaceAddresses();
                for (InterfaceAddress addr : addrs) {
                    InetAddress ip = addr.getAddress();
                    if (!ip.isLinkLocalAddress() && (network = NetworkInterface.getByInetAddress(ip)) != null && (mac = network.getHardwareAddress()) != null) {
                        sb.delete(0, sb.length());
                        int i = 0;
                        while (i < mac.length) {
                            Object[] objArr = new Object[2];
                            objArr[0] = Byte.valueOf(mac[i]);
                            objArr[1] = i < mac.length - 1 ? "-" : "";
                            sb.append(String.format("%02X%s", objArr));
                            i++;
                        }
                        if (!list.contains(sb.toString())) {
                            list.add(sb.toString());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    public static String getCPUSerialNumber() throws IOException {
        String str;
        String sysName = System.getProperty("os.name");
        if (sysName.contains("Windows")) {
            return runCmd("wmic cpu get ProcessorId", 2);
        }
        if (sysName.contains("Linux")) {
            String str2 = runCmd("dmidecode |grep -A16 \"Processor Information$\"", "ID");
            if (str2 != null) {
                return str2.substring(str2.indexOf(":")).trim();
            }
            return "";
        }
        if (sysName.contains("Mac") && (str = runCmd("system_profiler SPHardwareDataType", "Serial Number")) != null) {
            return str.substring(str.indexOf(":") + 1).trim();
        }
        return "";
    }

    public static String getHardDiskSerialNumber() throws IOException {
        String str;
        String sysName = System.getProperty("os.name");
        if (sysName.contains("Windows")) {
            return runCmd("wmic path win32_physicalmedia get serialnumber", 2);
        }
        if (sysName.contains("Linux")) {
            String str2 = runCmd("dmidecode |grep -A16 \"System Information$\"", "Serial Number");
            if (str2 != null) {
                return str2.substring(str2.indexOf(":")).trim();
            }
            return "";
        }
        if (sysName.contains("Mac") && (str = runCmd("system_profiler SPStorageDataType", "Volume UUID")) != null) {
            return str.substring(str.indexOf(":") + 1).trim();
        }
        return "";
    }

    /* JADX WARN: Type inference failed for: r0v11, types: [char[], char[][]] */
    public static char[] makeMarchinCode() {
        char[] c1 = EncryptUtils.md5(getMacList().toString().toCharArray());
        char[] c2 = EncryptUtils.md5(getCPUSerialNumber().toCharArray());
        char[] c3 = EncryptUtils.md5(getHardDiskSerialNumber().toCharArray());
        char[] chars = StrUtils.merger(new char[]{c1, c2, c3});
        for (int i = 0; i < chars.length; i++) {
            chars[i] = Character.toUpperCase(chars[i]);
        }
        return chars;
    }
}
