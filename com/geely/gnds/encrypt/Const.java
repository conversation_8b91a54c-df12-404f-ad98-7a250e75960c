package com.geely.gnds.encrypt;

import com.geely.gnds.tester.enums.ConstantEnum;

/* loaded from: Const.class */
public class Const {
    public static final String FILE_NAME = ".classes";
    public static final String LIB_JAR_DIR = "__temp__";
    public static final int ENCRYPT_TYPE = 1;
    public static final String CONFIG_PASS = "org.springframework.config.Pass";
    public static final String CONFIG_CODE = "org.springframework.config.Code";
    public static final String CONFIG_PASSHASH = "org.springframework.config.PassHash";
    public static final String[] CLASS_FILES = {"CoreAgent.class", "JarDecryptor.class", "AgentTransformer.class", "Const.class", "EncryptUtils.class", "IoUtils.class", "JarUtils.class", "Log.class", "StrUtils.class", "SysUtils.class", "JavaagentCmdArgs.class", "JVMUtil.class", "Me.class", "Me$SecSo.class"};
    public static boolean DEBUG = false;
    public static String WHITE_SPACE = ConstantEnum.EMPTY;
}
