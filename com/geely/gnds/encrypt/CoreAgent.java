package com.geely.gnds.encrypt;

import com.geely.gnds.encrypt.util.JVMUtil;
import com.geely.gnds.encrypt.util.JavaagentCmdArgs;
import com.geely.gnds.encrypt.util.Log;
import java.lang.instrument.Instrumentation;
import java.util.concurrent.TimeUnit;

/* loaded from: CoreAgent.class */
public class CoreAgent {
    private static JavaagentCmdArgs javaagentCmdArgs = null;

    public static void premain(String args, Instrumentation inst) throws InterruptedException {
        if (javaagentCmdArgs == null) {
            try {
                javaagentCmdArgs = JavaagentCmdArgs.parseJavaagentCmdArgs(args);
            } catch (Exception e) {
                Log.error(CoreAgent.class, e.getMessage());
                exit(null);
            }
            Log.ENABLE_DEBUG.set(javaagentCmdArgs.isDebug());
        }
        try {
            long structs = JVMUtil.getSymbol("gHotSpotVMStructs");
            JVMUtil.putAddress(structs, 0L);
            Log.debug(CoreAgent.class, "Disabled VM Structs");
        } catch (Throwable e2) {
            Log.warn(CoreAgent.class, "Disabled VM Structs fail. " + e2.getMessage());
            if (Log.ENABLE_DEBUG.get()) {
                Log.debug(CoreAgent.class, e2.getMessage());
            }
        }
        char[] pwd = "#".toCharArray();
        if (inst != null) {
            AgentTransformer tran = new AgentTransformer(pwd);
            inst.addTransformer(tran);
        }
    }

    private static void exit(String projectPath) throws InterruptedException {
        if (projectPath != null) {
            try {
                Log.error(CoreAgent.class, "Curr projectPath is -> " + projectPath);
            } catch (InterruptedException e) {
            }
        }
        TimeUnit.SECONDS.sleep(10L);
        System.exit(-1);
    }
}
