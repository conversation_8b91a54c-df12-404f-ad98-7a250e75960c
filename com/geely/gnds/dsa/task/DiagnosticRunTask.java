package com.geely.gnds.dsa.task;

import com.geely.gnds.dsa.dto.DiaEcuSwDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticSeqLineDTO;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DiagnosticRunTask.class */
public class DiagnosticRunTask implements Runnable {
    private static final Logger log = LoggerFactory.getLogger(DiagnosticRunTask.class);
    private final String taskId;
    private Long userId;
    private final BlockingQueue<DsaDiagnosticSeqLineDTO> fg;
    private final String vin;
    public volatile boolean fh = false;
    public final String runType;
    private String platform;

    public DiagnosticRunTask(String taskId, Long userId, BlockingQueue<DsaDiagnosticSeqLineDTO> queue, String vin, String runType, String platform) {
        this.taskId = taskId;
        this.userId = userId;
        this.fg = queue;
        this.vin = vin;
        this.runType = runType;
        this.platform = platform;
    }

    /* JADX WARN: Code restructure failed: missing block: B:77:0x0324, code lost:
    
        r0.stopRun(r7.vin, r7.taskId);
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x0334, code lost:
    
        return;
     */
    @Override // java.lang.Runnable
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void run() {
        /*
            Method dump skipped, instructions count: 1449
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.dsa.task.DiagnosticRunTask.run():void");
    }

    private DiaEcuSwDTO e(List<DiaEcuSwDTO> ecuSwList) {
        return ecuSwList.stream().filter(e -> {
            return "PBL".equalsIgnoreCase(e.getType());
        }).findFirst().orElse(null);
    }
}
