package com.geely.gnds.dsa.task;

import com.geely.gnds.dsa.service.VehicleService;
import com.geely.gnds.dsa.socket.DsaVehicleStatusReadWebSocket;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.VehicleStatusDto;
import com.geely.gnds.tester.enums.SeqButtonTypeEnum;
import com.geely.gnds.tester.service.SeqService;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import com.google.common.util.concurrent.RateLimiter;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import javax.websocket.RemoteEndpoint;
import javax.websocket.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
/* loaded from: StatusReadoutTask.class */
public class StatusReadoutTask {
    private static final Logger LOG = LoggerFactory.getLogger(StatusReadoutTask.class);

    @Autowired
    private VehicleService ev;

    @Autowired
    private SeqService ek;
    private RateLimiter limiter = RateLimiter.create(0.5d);
    private RateLimiter fi = RateLimiter.create(0.1d);

    @Scheduled(fixedDelay = 100)
    public void w() throws InterruptedException {
        if (!this.limiter.tryAcquire()) {
            try {
                Thread.sleep(500L);
                return;
            } catch (InterruptedException e) {
                e.printStackTrace();
                return;
            }
        }
        if (DsaVehicleStatusReadWebSocket.v()) {
            CopyOnWriteArrayList<Session> vehicleStatusReadSessionList = DsaVehicleStatusReadWebSocket.getVehicleStatusReadSessionList();
            Iterator<Session> it = vehicleStatusReadSessionList.iterator();
            while (it.hasNext()) {
                Session session = it.next();
                Map<String, List<String>> paramMap = session.getRequestParameterMap();
                List<String> vins = paramMap.get("vin");
                if (!CollectionUtils.isEmpty(vins)) {
                    String vin = vins.get(0);
                    InitializeUiDto initializeUiDto = new InitializeUiDto();
                    initializeUiDto.setVin(vin);
                    Set<String> loginUsers = TesterLoginUtils.getLoginUsers();
                    if (!CollectionUtils.isEmpty(loginUsers)) {
                        Iterator<String> iterator = loginUsers.iterator();
                        if (iterator.hasNext()) {
                            String username = iterator.next();
                            ThreadLocalUtils.CURRENT_USER_NAME.set(username);
                        }
                    }
                    initializeUiDto.setType(SeqButtonTypeEnum.DSA_STATUS_READOUT.getValue());
                    VehicleStatusDto vehicleStatusDto = this.ek.statusDsa(initializeUiDto);
                    RemoteEndpoint.Async asyncRemote = session.getAsyncRemote();
                    String data = ObjectMapperUtils.obj2JsonStr(vehicleStatusDto);
                    asyncRemote.sendText(data);
                }
            }
            return;
        }
        if (this.fi.tryAcquire()) {
            LOG.info("statusReadTask--当前无车辆连接");
        }
    }
}
