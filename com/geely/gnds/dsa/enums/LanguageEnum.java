package com.geely.gnds.dsa.enums;

import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.util.MessageUtils;

/* loaded from: LanguageEnum.class */
public enum LanguageEnum {
    DORMANCY("休眠", "Abandoned"),
    NOT_WORKING("不工作", "Inactive"),
    CONVENIENCE("便利", "Convenience"),
    WORK("工作", "Active"),
    DRIVE("驾驶", "Driving"),
    CLOSED("闭合", "Closed"),
    OPEN("断开", "Open"),
    PROGRAMMING_SESSION_MODE("进入编程会话模式", "Enter programming session mode"),
    LANGUAGE_ENUM("擦除内存", "erase memory"),
    START_DOWNLOADING_SOFTWARE("开始下载软件", "Start downloading software"),
    SOFTWARE_DOWNLOAD("软件下载中", "Software Download"),
    TRANSMISSION_COMPLETION("传输完成", "Transmission completion"),
    CHECK_IN_MEMORY("检查内存中", "Check in memory"),
    CHECK_MEMORY_SUCCESS("检查内存成功", "Check memory success"),
    CHECK_MEMORY_FAIL("检查内存失败", "Check memory fail"),
    PLATFORM_ATTRIBUTE_EMPTY("平台属性为空", "Platform attribute is empty"),
    FAILED_ACTIVATE_SBL("激活SBL出错", "Failed to activate the SBL"),
    VBF_FLUSHING_FAILED("VBF刷写失败", "VBF flushing failed"),
    RESERT("软件下载完成，复位中", "Software download completed, reset"),
    INTEGRITY_CHECK_FAILS("完整性校验失败", "Integrity check fails"),
    INTEGRITY_CHECK_SUCCESS("完整性校验成功", "Integrity check success"),
    UNLOCK_ECU("解锁ECU", "Unlock ECU"),
    UNLOCK_ECU_REQUEST("解锁ECU请求：", "Unlock ECU Request:"),
    UNLOCK_ECU_RESPONSE("解锁ECU响应：", "Unlock ECU Response:"),
    ECU_UNLOCK_OK("解锁ECU成功：", "ECU unlock OK:"),
    STARTING_DOWNLOAD("开始下载", "Starting Download"),
    DOWNLOADING_VBF_DATA("下载VBF中", "Downloading VBF data."),
    READING_FILE("检查文件", "Reading File"),
    READING_COMPLETE("文件检查完成", "Reading Complete"),
    SECURITY_ALGORITHM_FAILS("安全算法未通过", "The security algorithm fails"),
    VERIFICATION_FAILED("校验失败，请检查vbf", "Verification failed. Please check vbf"),
    NO_ACTIVATED_MESSAGE("未收到车辆激活响应消息", "Vehicle activation response message not received"),
    CONNECT_TIMEOUT("未收到车辆激活响应消息", "Vehicle activation response message not receive"),
    NETWORK_UNREACHABLE("网络不可达", "network is unreachable:connect"),
    CONNECTED_TIMED_OUT("连接超时", "Connect timed out"),
    COMPLETE("完成", "complete"),
    NETWORK_ERROR("日志上传失败，请检查你的网络环境", "Log uploading failed. Please check your network environment"),
    S("开始下载软件", "Log type error"),
    ERROR_CODE("代码内容", "Code content"),
    ERROR_CODE_LINE("代码行数", "Code Line"),
    ERROR_JAVA("java报错", "Java error"),
    NOT_DIAGNOSTIC_DATA("SDDB无此诊断数据解析", "diagnostic data not found in SDDB, no analysis display"),
    ACTIVATION_00("0x00 (由于源地址未知，路由激活被拒绝)", "0x00 (routing activation denied due to unknown source address)"),
    ACTIVATION_01("0x01 (路由激活被拒绝，因为所有并发支持的TCP_DATA套接字都已注册并激活)", "0x01 (routing activation denied because all concurrently supported TCP_DATA sockets are registered and active)"),
    ACTIVATION_02("0x02 (路由激活被拒绝，因为在已经激活的TCP_DATA套接字上收到了与表连接项不同的SA)", "0x02 (routing activation denied because an SA different from the table connection entry was received on the already activated TCP_DATA socket)"),
    ACTIVATION_03("0x03 (路由激活被拒绝，因为SA已经在另一个TCP_DATA套接字上注册并激活)", "0x03 (routing activation denied because the SA is already registered and active on a different TCP_DATA socket)"),
    ACTIVATION_04("0x04 (路由激活被拒绝，因为缺少认证)", "0x04 (routing activation denied due to missing authentication)"),
    ACTIVATION_05("0x05 (由于确认被拒绝，路由激活被拒绝)", "0x05 (routing activation denied due to rejected confirmation)"),
    ACTIVATION_06("0x06 (由于不支持路由激活类型，路由激活被拒绝)", "0x06 (routing activation denied due to unsupported routing activation type)"),
    ACTIVATION_10("0x10 (路由成功激活)", "0x10 (routing successfull activated)"),
    ACTIVATION_11("0x11 (路由将被激活;需要确认)", "0x11 (routing will be activated; confirmation required)"),
    ECU_ADDRESS("ECU地址", "ecu address"),
    COMMAND("指令", "command"),
    RECEIVE_RESPONSE("收到响应", "receive response"),
    ERROR1("没有应答数据", "No reply data"),
    ERROR2("应答数据内不包含此DID", "The DID is not included in the response data"),
    ERROR3("解析应答必要条件不足", "The necessary condition for parsing the response is insufficient"),
    ERROR4("应答数据长度有误", "The answer data length is incorrect"),
    ERROR5("07类型数据长度有误", "The length of the type 07 data is incorrect"),
    ERROR6("08类型数据长度有误", "The length of type 08 is incorrect"),
    ERROR7("未匹配到正确的解析规则", "The correct parsing rule is not matched"),
    ERROR8("未知itemOutType数据类型", "The itemOutType data type is unknown");

    private String value;
    private String valueEn;

    LanguageEnum(String value, String valueEn) {
        this.value = value;
        this.valueEn = valueEn;
    }

    public String value() {
        return this.value;
    }

    public String valueEn() {
        return this.valueEn;
    }

    public String valueByLanguage() {
        return MessageUtils.getMessage(this.valueEn);
    }

    public static String replaceUsage(String content) {
        LanguageEnum[] languageEnumArrValues = values();
        int length = languageEnumArrValues.length;
        int i = 0;
        while (true) {
            if (i >= length) {
                break;
            }
            LanguageEnum languageEnum = languageEnumArrValues[i];
            if (!content.contains(languageEnum.value())) {
                i++;
            } else {
                content = content.replaceAll(languageEnum.value(), MessageUtils.getMessage(languageEnum.value()));
                break;
            }
        }
        return content;
    }

    public static String getValue(String enueString) {
        LanguageEnum languageEnum = valueOf(enueString);
        if ("zh-CN".equals(HttpUtils.getLanguage())) {
            return languageEnum.value;
        }
        return languageEnum.valueEn;
    }
}
