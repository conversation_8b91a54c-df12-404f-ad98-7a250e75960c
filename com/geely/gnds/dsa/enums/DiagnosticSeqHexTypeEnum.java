package com.geely.gnds.dsa.enums;

/* loaded from: DiagnosticSeqHexTypeEnum.class */
public enum DiagnosticSeqHexTypeEnum {
    SECURITY_HEX(1, "安全指令"),
    GENERAL_HEX(2, "普通指令");

    private int value;
    private String name;

    DiagnosticSeqHexTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return this.value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
