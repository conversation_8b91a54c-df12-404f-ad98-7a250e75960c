package com.geely.gnds.dsa.enums;

/* loaded from: DsaLogTypeEnum.class */
public enum DsaLogTypeEnum {
    COMMENT("comment"),
    ERROR("error"),
    WARN("warn"),
    ORDINARY_TEXT("ordinaryText"),
    <PERSON>ND_UDS("sendUds"),
    SECURITY_ACCESS("securityAccess"),
    FLUSH_Progress("flushProgress"),
    FLUSH_END("flushEnd"),
    FLUSH_STATUS("flushStatus"),
    SOFTWARE_DOWNLOAD("softwareDownload"),
    SOFTWARE_DOWNLOAD_ERROR("softwareDownloadError"),
    SOFTWARE_DOWNLOAD_WARN("softwareDownloadWarn"),
    SWITCH_POWER_MODE_FAILED("switchPowerModeFailed");

    private String value;

    DsaLogTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }
}
