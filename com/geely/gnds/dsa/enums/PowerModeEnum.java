package com.geely.gnds.dsa.enums;

import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;

/* loaded from: PowerModeEnum.class */
public enum PowerModeEnum {
    UNACTIVATED("Inactive", "Inactive", "01"),
    ACTIVATE("Active", "Active", "0B"),
    CONVENIENCE("Convenience", "Convenience", "02"),
    RETURN_CONTROL("ReturnControl", "ReturnControl", "00");

    private String value;
    private String valueEn;
    private String command;

    PowerModeEnum(String value, String valueEn, String command) {
        this.value = value;
        this.valueEn = valueEn;
        this.command = command;
    }

    public String value() {
        return this.value;
    }

    public String valueEn() {
        return this.valueEn;
    }

    public String command() {
        return this.command;
    }

    public String valueByLanguage() {
        if ("zh-CN".equals(HttpUtils.getLanguage())) {
            return this.value;
        }
        return this.valueEn;
    }

    @Override // java.lang.Enum
    public String toString() {
        return "{\"value\":\"" + this.value + "\",\"valueEn\":\"" + this.valueEn + "\",\"command\":\"" + this.command + "\"}";
    }
}
