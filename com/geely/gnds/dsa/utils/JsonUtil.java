package com.geely.gnds.dsa.utils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.text.SimpleDateFormat;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: JsonUtil.class */
public class JsonUtil {
    private static final Logger log = LoggerFactory.getLogger(JsonUtil.class);
    private static ObjectMapper MAPPER = new ObjectMapper();
    private static final String fj = "yyyy-MM-dd HH:mm:ss";

    static {
        MAPPER.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String b(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return object instanceof String ? (String) object : MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            log.error("method=toJson() is convert error, errorMsg:{}", e.getMessage(), e);
            return null;
        }
    }

    public static String c(Object object) {
        if (object == null) {
            return null;
        }
        try {
            MAPPER.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>() { // from class: com.geely.gnds.dsa.utils.JsonUtil.1
                public void serialize(Object param, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                    jsonGenerator.writeString("");
                }
            });
            return MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            log.error("method=toJsonEmpty() is convert error, errorMsg:{}", e.getMessage(), e);
            return null;
        }
    }

    public static <T> T a(String str, Class<T> cls) {
        if (StringUtils.isEmpty(str) || cls == null) {
            return null;
        }
        try {
            return (T) MAPPER.readValue(str, cls);
        } catch (Exception e) {
            log.error("method=toBean() is convert error, errorMsg:{}", e.getMessage(), e);
            return null;
        }
    }

    public static String u(String filePath) throws IOException {
        String jsonStr = null;
        Reader reader = null;
        try {
            try {
                File jsonFile = new File(filePath);
                reader = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
                StringBuffer sb = new StringBuffer();
                while (true) {
                    int ch = reader.read();
                    if (ch == -1) {
                        break;
                    }
                    sb.append((char) ch);
                }
                jsonStr = sb.toString();
                if (null != reader) {
                    try {
                        reader.close();
                    } catch (Exception e) {
                        log.error("关闭流失败！,e={}", e);
                    }
                }
                return jsonStr;
            } catch (Exception ex) {
                log.error("读取JSON文件失败,filePath={},e={}", filePath, ex);
                if (null != reader) {
                    try {
                        reader.close();
                    } catch (Exception e2) {
                        log.error("关闭流失败！,e={}", e2);
                    }
                }
                return jsonStr;
            }
        } catch (Throwable th) {
            if (null != reader) {
                try {
                    reader.close();
                } catch (Exception e3) {
                    log.error("关闭流失败！,e={}", e3);
                }
            }
            return jsonStr;
        }
    }

    /* JADX WARN: Finally extract failed */
    public static void saveJsonFile(String filePath, String content) throws IOException {
        BufferedWriter bw = null;
        try {
            try {
                bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath)));
                bw.write(content);
                bw.close();
                if (null != bw) {
                    try {
                        bw.close();
                    } catch (IOException e) {
                        log.error("关闭流失败！e={}", e);
                    }
                }
            } catch (IOException e2) {
                log.error("写入JSON文件失败,filePath={},pinCodes={},e={}", new Object[]{filePath, JSON.toJSONString(content), e2});
                if (null != bw) {
                    try {
                        bw.close();
                    } catch (IOException e3) {
                        log.error("关闭流失败！e={}", e3);
                    }
                }
            }
        } catch (Throwable th) {
            if (null != bw) {
                try {
                    bw.close();
                } catch (IOException e4) {
                    log.error("关闭流失败！e={}", e4);
                    throw th;
                }
            }
            throw th;
        }
    }
}
