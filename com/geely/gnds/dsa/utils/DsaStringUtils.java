package com.geely.gnds.dsa.utils;

import java.util.ArrayList;
import java.util.List;

/* loaded from: DsaStringUtils.class */
public class DsaStringUtils {
    public static List<String> a(String s, int chunkSize) {
        int chunkCount = (s.length() / chunkSize) + (s.length() % chunkSize == 0 ? 0 : 1);
        List<String> returnVal = new ArrayList<>();
        for (int i = 0; i < chunkCount; i++) {
            returnVal.add(s.substring(i * chunkSize, Math.min((i + 1) * chunkSize, s.length())));
        }
        return returnVal;
    }
}
