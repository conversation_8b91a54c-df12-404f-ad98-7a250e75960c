package com.geely.gnds.dsa.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
/* loaded from: SpringUtil.class */
public class SpringUtil implements ApplicationContextAware {
    private static ApplicationContext fk;

    public static ApplicationContext getContext() {
        return fk;
    }

    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        fk = applicationContext;
    }

    public static <T> T a(Class<T> cls) throws Exception {
        return (T) fk.getBean(cls);
    }

    public static Object v(String beanId) throws Exception {
        if (StringUtils.isNotBlank(beanId)) {
            return fk.getBean(beanId);
        }
        return null;
    }
}
