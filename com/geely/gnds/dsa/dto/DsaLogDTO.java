package com.geely.gnds.dsa.dto;

import java.io.Serializable;
import java.util.Objects;

/* loaded from: DsaLogDTO.class */
public class DsaLogDTO implements Serializable {
    private static final long serialVersionUID = 3537148611492161791L;
    private String vin;
    private String logPath;
    private String sddbPath;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getLogPath() {
        return this.logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public String getSddbPath() {
        return this.sddbPath;
    }

    public void setSddbPath(String sddbPath) {
        this.sddbPath = sddbPath;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DsaLogDTO dsaLogDTO = (DsaLogDTO) o;
        return Objects.equals(this.vin, dsaLogDTO.vin) && Objects.equals(this.logPath, dsaLogDTO.logPath) && Objects.equals(this.sddbPath, dsaLogDTO.sddbPath);
    }

    public int hashCode() {
        return Objects.hash(this.vin, this.logPath, this.sddbPath);
    }
}
