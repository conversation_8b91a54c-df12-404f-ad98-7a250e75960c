package com.geely.gnds.dsa.dto;

/* loaded from: DiaDtcDTO.class */
public class DiaDtcDTO {
    private Long id;
    private String dtcId;
    private Long serviceId;
    private String name;
    private String swLabel;
    private String confirmedDtcLimit;
    private String unConfirmedDtcLimit;
    private String agedDtcLimit;
    private String agingCounter;
    private String warningIndicator;
    private String dtceventPriority;
    private String failedThreshold;
    private String passedThreshold;
    private String incrementStepSize;
    private String decrementStepSize;
    private String jumpDown;
    private String diagnosticPartNumber;
    private String description;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDtcId() {
        return this.dtcId;
    }

    public void setDtcId(String dtcId) {
        this.dtcId = dtcId;
    }

    public Long getServiceId() {
        return this.serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSwLabel() {
        return this.swLabel;
    }

    public void setSwLabel(String swLabel) {
        this.swLabel = swLabel;
    }

    public String getConfirmedDtcLimit() {
        return this.confirmedDtcLimit;
    }

    public void setConfirmedDtcLimit(String confirmedDtcLimit) {
        this.confirmedDtcLimit = confirmedDtcLimit;
    }

    public String getUnConfirmedDtcLimit() {
        return this.unConfirmedDtcLimit;
    }

    public void setUnConfirmedDtcLimit(String unConfirmedDtcLimit) {
        this.unConfirmedDtcLimit = unConfirmedDtcLimit;
    }

    public String getAgedDtcLimit() {
        return this.agedDtcLimit;
    }

    public void setAgedDtcLimit(String agedDtcLimit) {
        this.agedDtcLimit = agedDtcLimit;
    }

    public String getAgingCounter() {
        return this.agingCounter;
    }

    public void setAgingCounter(String agingCounter) {
        this.agingCounter = agingCounter;
    }

    public String getWarningIndicator() {
        return this.warningIndicator;
    }

    public void setWarningIndicator(String warningIndicator) {
        this.warningIndicator = warningIndicator;
    }

    public String getDtceventPriority() {
        return this.dtceventPriority;
    }

    public void setDtceventPriority(String dtceventPriority) {
        this.dtceventPriority = dtceventPriority;
    }

    public String getFailedThreshold() {
        return this.failedThreshold;
    }

    public void setFailedThreshold(String failedThreshold) {
        this.failedThreshold = failedThreshold;
    }

    public String getPassedThreshold() {
        return this.passedThreshold;
    }

    public void setPassedThreshold(String passedThreshold) {
        this.passedThreshold = passedThreshold;
    }

    public String getIncrementStepSize() {
        return this.incrementStepSize;
    }

    public void setIncrementStepSize(String incrementStepSize) {
        this.incrementStepSize = incrementStepSize;
    }

    public String getDecrementStepSize() {
        return this.decrementStepSize;
    }

    public void setDecrementStepSize(String decrementStepSize) {
        this.decrementStepSize = decrementStepSize;
    }

    public String getJumpDown() {
        return this.jumpDown;
    }

    public void setJumpDown(String jumpDown) {
        this.jumpDown = jumpDown;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
