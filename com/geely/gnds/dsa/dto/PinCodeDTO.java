package com.geely.gnds.dsa.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.geely.gnds.ruoyi.common.enums.PrivacyTypeEnum;
import com.geely.gnds.ruoyi.framework.annotation.PrivacyEncrypt;
import java.util.StringJoiner;

/* loaded from: PinCodeDTO.class */
public class PinCodeDTO {
    private String ecuAddress;
    private String localPinCode;
    private String ecuName;
    private String codeId;

    @JsonIgnore
    private String cloudPinCode;

    @PrivacyEncrypt(type = PrivacyTypeEnum.CUSTOMER, prefixNoMaskLen = 2, suffixNoMaskLen = 2)
    private String privacyCloudPinCode;

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getLocalPinCode() {
        return this.localPinCode;
    }

    public void setLocalPinCode(String localPinCode) {
        this.localPinCode = localPinCode;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getCloudPinCode() {
        return this.cloudPinCode;
    }

    public void setCloudPinCode(String cloudPinCode) {
        this.cloudPinCode = cloudPinCode;
    }

    public String getCodeId() {
        return this.codeId;
    }

    public void setCodeId(String codeId) {
        this.codeId = codeId;
    }

    public String getPrivacyCloudPinCode() {
        return this.privacyCloudPinCode;
    }

    public void setPrivacyCloudPinCode(String privacyCloudPinCode) {
        this.privacyCloudPinCode = privacyCloudPinCode;
    }

    public String toString() {
        return new StringJoiner(", ", PinCodeDTO.class.getSimpleName() + "[", "]").add("ecuAddress='" + this.ecuAddress + "'").add("localPinCode='" + this.localPinCode + "'").add("ecuName='" + this.ecuName + "'").add("codeId='" + this.codeId + "'").add("cloudPinCode='" + this.cloudPinCode + "'").add("privacyCloudPinCode='" + this.privacyCloudPinCode + "'").toString();
    }
}
