package com.geely.gnds.dsa.dto;

/* loaded from: TestOrderItemDTO.class */
public class TestOrderItemDTO {
    private String type;
    private String description;
    private String geelyDid;
    private String volvoDid;
    private String geelyPartNo;
    private String geelyDidReadOut;
    private String volvoPartNo;
    private String volvoDidReadOut;
    private String comment;

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getGeelyDid() {
        return this.geelyDid;
    }

    public void setGeelyDid(String geelyDid) {
        this.geelyDid = geelyDid;
    }

    public String getVolvoDid() {
        return this.volvoDid;
    }

    public void setVolvoDid(String volvoDid) {
        this.volvoDid = volvoDid;
    }

    public String getGeelyPartNo() {
        return this.geelyPartNo;
    }

    public void setGeelyPartNo(String geelyPartNo) {
        this.geelyPartNo = geelyPartNo;
    }

    public String getGeelyDidReadOut() {
        return this.geelyDidReadOut;
    }

    public void setGeelyDidReadOut(String geelyDidReadOut) {
        this.geelyDidReadOut = geelyDidReadOut;
    }

    public String getVolvoPartNo() {
        return this.volvoPartNo;
    }

    public void setVolvoPartNo(String volvoPartNo) {
        this.volvoPartNo = volvoPartNo;
    }

    public String getVolvoDidReadOut() {
        return this.volvoDidReadOut;
    }

    public void setVolvoDidReadOut(String volvoDidReadOut) {
        this.volvoDidReadOut = volvoDidReadOut;
    }

    public String getComment() {
        return this.comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public TestOrderItemDTO() {
    }

    public TestOrderItemDTO(String type, String description, String geelyDid, String volvoDid, String geelyPartNo, String geelyDidReadOut, String volvoPartNo, String volvoDidReadOut, String comment) {
        this.type = type;
        this.description = description;
        this.geelyDid = geelyDid;
        this.volvoDid = volvoDid;
        this.geelyPartNo = geelyPartNo;
        this.geelyDidReadOut = geelyDidReadOut;
        this.volvoPartNo = volvoPartNo;
        this.volvoDidReadOut = volvoDidReadOut;
        this.comment = comment;
    }
}
