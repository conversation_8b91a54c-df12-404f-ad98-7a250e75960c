package com.geely.gnds.dsa.dto;

import java.io.Serializable;

/* loaded from: PowerModeDto.class */
public class PowerModeDto implements Serializable {
    private String value;
    private String command;
    private String powerModeType;

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCommand() {
        return this.command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public String getPowerModeType() {
        return this.powerModeType;
    }

    public void setPowerModeType(String powerModeType) {
        this.powerModeType = powerModeType;
    }

    public String toString() {
        return "PowerModeDto{value='" + this.value + "', command='" + this.command + "'}";
    }

    public PowerModeDto(String value, String command, String powerModeType) {
        this.value = value;
        this.command = command;
        this.powerModeType = powerModeType;
    }

    public PowerModeDto() {
    }
}
