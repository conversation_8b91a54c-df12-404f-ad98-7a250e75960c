package com.geely.gnds.dsa.dto;

import java.util.StringJoiner;

/* loaded from: DsaDiagnosticRunResultDTO.class */
public class DsaDiagnosticRunResultDTO {
    private String taskId;
    private String message;
    private String errorMessage;

    public String getTaskId() {
        return this.taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String toString() {
        return new StringJoiner(", ", DsaDiagnosticRunResultDTO.class.getSimpleName() + "[", "]").add("taskId='" + this.taskId + "'").add("message='" + this.message + "'").add("errorMessage='" + this.errorMessage + "'").toString();
    }
}
