package com.geely.gnds.dsa.dto;

import java.util.List;

/* loaded from: BssIdDTO.class */
public class BssIdDTO {
    private String id;
    private String bssId;
    private String bssDesc;
    private String bssReleaseType;
    private String bssState;
    private String bssChangeLog;
    private String sourceFrom;
    private String syncState;
    private String createdBy;
    private String createdAt;
    private String updatedBy;
    private String displayVersion;
    private String taskDescChinese;
    private String taskDescEnglish;
    private String descType;
    private List<VehicleModelDTO> vehicleModels;
    private List<NodeDTO> nodes;
    private String modelID;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBssId() {
        return this.bssId;
    }

    public void setBssId(String bssId) {
        this.bssId = bssId;
    }

    public String getBssDesc() {
        return this.bssDesc;
    }

    public void setBssDesc(String bssDesc) {
        this.bssDesc = bssDesc;
    }

    public String getBssReleaseType() {
        return this.bssReleaseType;
    }

    public void setBssReleaseType(String bssReleaseType) {
        this.bssReleaseType = bssReleaseType;
    }

    public String getBssState() {
        return this.bssState;
    }

    public void setBssState(String bssState) {
        this.bssState = bssState;
    }

    public String getBssChangeLog() {
        return this.bssChangeLog;
    }

    public void setBssChangeLog(String bssChangeLog) {
        this.bssChangeLog = bssChangeLog;
    }

    public String getSourceFrom() {
        return this.sourceFrom;
    }

    public void setSourceFrom(String sourceFrom) {
        this.sourceFrom = sourceFrom;
    }

    public String getSyncState() {
        return this.syncState;
    }

    public void setSyncState(String syncState) {
        this.syncState = syncState;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getDisplayVersion() {
        return this.displayVersion;
    }

    public void setDisplayVersion(String displayVersion) {
        this.displayVersion = displayVersion;
    }

    public String getTaskDescChinese() {
        return this.taskDescChinese;
    }

    public void setTaskDescChinese(String taskDescChinese) {
        this.taskDescChinese = taskDescChinese;
    }

    public String getTaskDescEnglish() {
        return this.taskDescEnglish;
    }

    public void setTaskDescEnglish(String taskDescEnglish) {
        this.taskDescEnglish = taskDescEnglish;
    }

    public String getDescType() {
        return this.descType;
    }

    public void setDescType(String descType) {
        this.descType = descType;
    }

    public List<VehicleModelDTO> getVehicleModels() {
        return this.vehicleModels;
    }

    public void setVehicleModels(List<VehicleModelDTO> vehicleModels) {
        this.vehicleModels = vehicleModels;
    }

    public List<NodeDTO> getNodes() {
        return this.nodes;
    }

    public void setNodes(List<NodeDTO> nodes) {
        this.nodes = nodes;
    }

    public String getModelID() {
        return this.modelID;
    }

    public void setModelID(String modelID) {
        this.modelID = modelID;
    }
}
