package com.geely.gnds.dsa.dto;

import java.util.List;
import java.util.Objects;

/* loaded from: NodeDTO.class */
public class NodeDTO {
    private String id;
    private List<HardwareDTO> hardwares;
    private String nodeAddress;
    private String nodeName;
    private String sequence;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<HardwareDTO> getHardwares() {
        return this.hardwares;
    }

    public void setHardwares(List<HardwareDTO> hardwares) {
        this.hardwares = hardwares;
    }

    public String getNodeAddress() {
        return this.nodeAddress;
    }

    public void setNodeAddress(String nodeAddress) {
        this.nodeAddress = nodeAddress;
    }

    public String getNodeName() {
        return this.nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getSequence() {
        return this.sequence;
    }

    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        NodeDTO nodeDTO = (NodeDTO) o;
        return Objects.equals(this.id, nodeDTO.id) && Objects.equals(this.hardwares, nodeDTO.hardwares) && Objects.equals(this.nodeAddress, nodeDTO.nodeAddress) && Objects.equals(this.nodeName, nodeDTO.nodeName) && Objects.equals(this.sequence, nodeDTO.sequence);
    }

    public int hashCode() {
        return Objects.hash(this.id, this.hardwares, this.nodeAddress, this.nodeName, this.sequence);
    }
}
