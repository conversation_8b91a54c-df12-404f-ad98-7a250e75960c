package com.geely.gnds.dsa.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/* loaded from: SoftwareDownloadDto.class */
public class SoftwareDownloadDto implements Serializable {
    private int type;
    private List<CalculationEcuDTO> calculationEcu;
    private List<VbfParseDto> vbfList;
    private String vin;
    private DsaSoftwareDownloadConfigDto dsaSoftwareDownloadConfig;
    private List<EcuParallelDto> ecuParallels;
    private TesterOrderParamDTO testerOrderParam;

    public List<VbfParseDto> getVbfList() {
        return this.vbfList;
    }

    public void setVbfList(List<VbfParseDto> vbfList) {
        this.vbfList = vbfList;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String toString() {
        return "SoftwareDownloadDto{vbfList=" + this.vbfList + ", vin='" + this.vin + "'}";
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SoftwareDownloadDto that = (SoftwareDownloadDto) o;
        return Objects.equals(this.vbfList, that.vbfList) && Objects.equals(this.vin, that.vin);
    }

    public int hashCode() {
        return Objects.hash(this.vbfList, this.vin);
    }

    public DsaSoftwareDownloadConfigDto getDsaSoftwareDownloadConfig() {
        return this.dsaSoftwareDownloadConfig;
    }

    public void setDsaSoftwareDownloadConfig(DsaSoftwareDownloadConfigDto dsaSoftwareDownloadConfig) {
        this.dsaSoftwareDownloadConfig = dsaSoftwareDownloadConfig;
    }

    public List<EcuParallelDto> getEcuParallels() {
        return this.ecuParallels;
    }

    public void setEcuParallels(List<EcuParallelDto> ecuParallels) {
        this.ecuParallels = ecuParallels;
    }

    public List<CalculationEcuDTO> getCalculationEcu() {
        return this.calculationEcu;
    }

    public void setCalculationEcu(List<CalculationEcuDTO> calculationEcu) {
        this.calculationEcu = calculationEcu;
    }

    public int getType() {
        return this.type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public TesterOrderParamDTO getTesterOrderParam() {
        return this.testerOrderParam;
    }

    public void setTesterOrderParam(TesterOrderParamDTO testerOrderParam) {
        this.testerOrderParam = testerOrderParam;
    }
}
