package com.geely.gnds.dsa.dto;

/* loaded from: DiaSessionDTO.class */
public class DiaSessionDTO {
    private Long id;
    private String sessionId;
    private String sessionName;
    private String s3Server;
    private String p2ServerMaxDefault;
    private String p2StarServerMaxDefault;
    private String p4ServerMaxDefault;
    private String deltaP2VehicleSingleframe;
    private String deltaP2VehicleMultiframe;
    private String deltaP2VehicleMultichannel;
    private String supportOfQueuedRequests;
    private String maxNumberOfDidsForReadDid;
    private String p2ServerMax;
    private String p4ServerMax;
    private String diagnosticPartNumber;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionName() {
        return this.sessionName;
    }

    public void setSessionName(String sessionName) {
        this.sessionName = sessionName;
    }

    public String getS3Server() {
        return this.s3Server;
    }

    public void setS3Server(String s3Server) {
        this.s3Server = s3Server;
    }

    public String getP2ServerMaxDefault() {
        return this.p2ServerMaxDefault;
    }

    public void setP2ServerMaxDefault(String p2ServerMaxDefault) {
        this.p2ServerMaxDefault = p2ServerMaxDefault;
    }

    public String getP2StarServerMaxDefault() {
        return this.p2StarServerMaxDefault;
    }

    public void setP2StarServerMaxDefault(String p2StarServerMaxDefault) {
        this.p2StarServerMaxDefault = p2StarServerMaxDefault;
    }

    public String getP4ServerMaxDefault() {
        return this.p4ServerMaxDefault;
    }

    public void setP4ServerMaxDefault(String p4ServerMaxDefault) {
        this.p4ServerMaxDefault = p4ServerMaxDefault;
    }

    public String getDeltaP2VehicleSingleframe() {
        return this.deltaP2VehicleSingleframe;
    }

    public void setDeltaP2VehicleSingleframe(String deltaP2VehicleSingleframe) {
        this.deltaP2VehicleSingleframe = deltaP2VehicleSingleframe;
    }

    public String getDeltaP2VehicleMultiframe() {
        return this.deltaP2VehicleMultiframe;
    }

    public void setDeltaP2VehicleMultiframe(String deltaP2VehicleMultiframe) {
        this.deltaP2VehicleMultiframe = deltaP2VehicleMultiframe;
    }

    public String getDeltaP2VehicleMultichannel() {
        return this.deltaP2VehicleMultichannel;
    }

    public void setDeltaP2VehicleMultichannel(String deltaP2VehicleMultichannel) {
        this.deltaP2VehicleMultichannel = deltaP2VehicleMultichannel;
    }

    public String getSupportOfQueuedRequests() {
        return this.supportOfQueuedRequests;
    }

    public void setSupportOfQueuedRequests(String supportOfQueuedRequests) {
        this.supportOfQueuedRequests = supportOfQueuedRequests;
    }

    public String getMaxNumberOfDidsForReadDid() {
        return this.maxNumberOfDidsForReadDid;
    }

    public void setMaxNumberOfDidsForReadDid(String maxNumberOfDidsForReadDid) {
        this.maxNumberOfDidsForReadDid = maxNumberOfDidsForReadDid;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public String getP2ServerMax() {
        return this.p2ServerMax;
    }

    public void setP2ServerMax(String p2ServerMax) {
        this.p2ServerMax = p2ServerMax;
    }

    public String getP4ServerMax() {
        return this.p4ServerMax;
    }

    public void setP4ServerMax(String p4ServerMax) {
        this.p4ServerMax = p4ServerMax;
    }
}
