package com.geely.gnds.dsa.dto;

/* loaded from: DiaResponseItemDTO.class */
public class DiaResponseItemDTO {
    private Long id;
    private Long serviceId;
    private String responseItemId;
    private Long dataIdentifierId;
    private Long routineIdentifierId;
    private String dataIdentifierStrId;
    private Long subfunctionId;
    private Long dataParameterId;
    private String name;
    private String inDataType;
    private String outDataType;
    private String offset;
    private String size;
    private String resultPrecision;
    private String formula;
    private String unit;
    private String compareValue;
    private Integer sort;
    private String statusBits;
    private String diagnosticPartNumber;
    private String dmeName;
    private String originalName;
    private String originalUnit;
    private String gcidName;
    private String location;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getServiceId() {
        return this.serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public String getResponseItemId() {
        return this.responseItemId;
    }

    public void setResponseItemId(String responseItemId) {
        this.responseItemId = responseItemId;
    }

    public Long getDataIdentifierId() {
        return this.dataIdentifierId;
    }

    public void setDataIdentifierId(Long dataIdentifierId) {
        this.dataIdentifierId = dataIdentifierId;
    }

    public Long getRoutineIdentifierId() {
        return this.routineIdentifierId;
    }

    public void setRoutineIdentifierId(Long routineIdentifierId) {
        this.routineIdentifierId = routineIdentifierId;
    }

    public String getDataIdentifierStrId() {
        return this.dataIdentifierStrId;
    }

    public void setDataIdentifierStrId(String dataIdentifierStrId) {
        this.dataIdentifierStrId = dataIdentifierStrId;
    }

    public Long getSubfunctionId() {
        return this.subfunctionId;
    }

    public void setSubfunctionId(Long subfunctionId) {
        this.subfunctionId = subfunctionId;
    }

    public Long getDataParameterId() {
        return this.dataParameterId;
    }

    public void setDataParameterId(Long dataParameterId) {
        this.dataParameterId = dataParameterId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getInDataType() {
        return this.inDataType;
    }

    public void setInDataType(String inDataType) {
        this.inDataType = inDataType;
    }

    public String getOutDataType() {
        return this.outDataType;
    }

    public void setOutDataType(String outDataType) {
        this.outDataType = outDataType;
    }

    public String getOffset() {
        return this.offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getSize() {
        return this.size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getResultPrecision() {
        return this.resultPrecision;
    }

    public void setResultPrecision(String resultPrecision) {
        this.resultPrecision = resultPrecision;
    }

    public String getFormula() {
        return this.formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCompareValue() {
        return this.compareValue;
    }

    public void setCompareValue(String compareValue) {
        this.compareValue = compareValue;
    }

    public Integer getSort() {
        return this.sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getStatusBits() {
        return this.statusBits;
    }

    public void setStatusBits(String statusBits) {
        this.statusBits = statusBits;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public String getDmeName() {
        return this.dmeName;
    }

    public void setDmeName(String dmeName) {
        this.dmeName = dmeName;
    }

    public String getOriginalName() {
        return this.originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getOriginalUnit() {
        return this.originalUnit;
    }

    public void setOriginalUnit(String originalUnit) {
        this.originalUnit = originalUnit;
    }

    public String getGcidName() {
        return this.gcidName;
    }

    public void setGcidName(String gcidName) {
        this.gcidName = gcidName;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
