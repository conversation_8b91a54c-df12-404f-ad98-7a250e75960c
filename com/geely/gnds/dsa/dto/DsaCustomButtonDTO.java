package com.geely.gnds.dsa.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: DsaCustomButtonDTO.class */
public class DsaCustomButtonDTO {
    private Long id;
    private String command;
    private String remark;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCommand() {
        return this.command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String toString() {
        return new ToStringBuilder(this).append("id", this.id).append("command", this.command).append("remark", this.remark).toString();
    }
}
