package com.geely.gnds.dsa.dto;

import java.util.List;
import java.util.StringJoiner;

/* loaded from: DsaDiagnosticRunReqDTO.class */
public class DsaDiagnosticRunReqDTO {
    private String vin;
    private String runType;
    private String ecuAddress;
    private String platform;
    private List<DsaDiagnosticSeqLineDTO> lines;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getRunType() {
        return this.runType;
    }

    public void setRunType(String runType) {
        this.runType = runType;
    }

    public List<DsaDiagnosticSeqLineDTO> getLines() {
        return this.lines;
    }

    public void setLines(List<DsaDiagnosticSeqLineDTO> lines) {
        this.lines = lines;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getPlatform() {
        return this.platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String toString() {
        return new StringJoiner(", ", DsaDiagnosticRunReqDTO.class.getSimpleName() + "[", "]").add("vin='" + this.vin + "'").add("runType='" + this.runType + "'").add("ecuAddress='" + this.ecuAddress + "'").add("lines=" + this.lines).add("platform=" + this.platform).toString();
    }
}
