package com.geely.gnds.dsa.dto;

import java.util.List;

/* loaded from: DiaDataIdentifierDTO.class */
public class DiaDataIdentifierDTO {
    private Long id;
    private String address;
    private String diagnosticPartNumber;
    private Long serviceId;
    private String dataIdentifierId;
    private String sessionId;
    private String maxNumberOfDidsForReadDid;
    private String name;
    private String ecuName;
    private String size;
    private String securityAccessRefs;
    private String useForAfterSale;
    private String activation;
    private List<DiaResponseItemDTO> responseItemDtoList;
    private Integer responseItemNum;
    private String gcidAndLoc;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public Long getServiceId() {
        return this.serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public String getDataIdentifierId() {
        return this.dataIdentifierId;
    }

    public void setDataIdentifierId(String dataIdentifierId) {
        this.dataIdentifierId = dataIdentifierId;
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getMaxNumberOfDidsForReadDid() {
        return this.maxNumberOfDidsForReadDid;
    }

    public void setMaxNumberOfDidsForReadDid(String maxNumberOfDidsForReadDid) {
        this.maxNumberOfDidsForReadDid = maxNumberOfDidsForReadDid;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getSize() {
        return this.size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getSecurityAccessRefs() {
        return this.securityAccessRefs;
    }

    public void setSecurityAccessRefs(String securityAccessRefs) {
        this.securityAccessRefs = securityAccessRefs;
    }

    public String getUseForAfterSale() {
        return this.useForAfterSale;
    }

    public void setUseForAfterSale(String useForAfterSale) {
        this.useForAfterSale = useForAfterSale;
    }

    public String getActivation() {
        return this.activation;
    }

    public void setActivation(String activation) {
        this.activation = activation;
    }

    public List<DiaResponseItemDTO> getResponseItemDtoList() {
        return this.responseItemDtoList;
    }

    public void setResponseItemDtoList(List<DiaResponseItemDTO> responseItemDtoList) {
        this.responseItemDtoList = responseItemDtoList;
    }

    public Integer getResponseItemNum() {
        return this.responseItemNum;
    }

    public void setResponseItemNum(Integer responseItemNum) {
        this.responseItemNum = responseItemNum;
    }

    public String getGcidAndLoc() {
        return this.gcidAndLoc;
    }

    public void setGcidAndLoc(String gcidAndLoc) {
        this.gcidAndLoc = gcidAndLoc;
    }
}
