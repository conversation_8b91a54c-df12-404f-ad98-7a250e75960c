package com.geely.gnds.dsa.dto;

import java.util.List;
import java.util.StringJoiner;

/* loaded from: PinCodeCloudValueDTO.class */
public class PinCodeCloudValueDTO {
    private String vin;
    private List<PinCode> pinCodes;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public List<PinCode> getPinCodes() {
        return this.pinCodes;
    }

    public void setPinCodes(List<PinCode> pinCodes) {
        this.pinCodes = pinCodes;
    }

    public String toString() {
        return new StringJoiner(", ", PinCodeCloudValueDTO.class.getSimpleName() + "[", "]").add("vin='" + this.vin + "'").add("pinCodes=" + this.pinCodes).toString();
    }
}
