package com.geely.gnds.dsa.dto;

import java.util.List;

/* loaded from: VehicleModelDTO.class */
public class VehicleModelDTO implements Comparable<VehicleModelDTO> {
    private String id;
    private List<NodeDTO> nodes;
    private String bssId;
    private String modelType;
    private String modelId;
    private String validTimeType;
    private String validTimeFrom;
    private String validTimeEnd;
    private String modelID;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<NodeDTO> getNodes() {
        return this.nodes;
    }

    public void setNodes(List<NodeDTO> nodes) {
        this.nodes = nodes;
    }

    public String getBssId() {
        return this.bssId;
    }

    public void setBssId(String bssId) {
        this.bssId = bssId;
    }

    public String getModelType() {
        return this.modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public String getModelId() {
        return this.modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getValidTimeType() {
        return this.validTimeType;
    }

    public void setValidTimeType(String validTimeType) {
        this.validTimeType = validTimeType;
    }

    public String getValidTimeFrom() {
        return this.validTimeFrom;
    }

    public void setValidTimeFrom(String validTimeFrom) {
        this.validTimeFrom = validTimeFrom;
    }

    public String getValidTimeEnd() {
        return this.validTimeEnd;
    }

    public void setValidTimeEnd(String validTimeEnd) {
        this.validTimeEnd = validTimeEnd;
    }

    public String getModelID() {
        return this.modelID;
    }

    public void setModelID(String modelID) {
        this.modelID = modelID;
    }

    @Override // java.lang.Comparable
    public int compareTo(VehicleModelDTO p) throws NumberFormatException {
        String pValidTimeEnd = p.getValidTimeEnd();
        String timeEnd = getValidTimeEnd();
        if ("UP".equalsIgnoreCase(pValidTimeEnd)) {
            return 1;
        }
        if ("UP".equalsIgnoreCase(timeEnd)) {
            return -1;
        }
        int pEnd = Integer.parseInt(pValidTimeEnd);
        int thisEnd = Integer.parseInt(timeEnd);
        return thisEnd - pEnd;
    }
}
