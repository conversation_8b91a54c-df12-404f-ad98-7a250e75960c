package com.geely.gnds.dsa.dto;

import java.util.List;

/* loaded from: HtmlTableDTO.class */
public class HtmlTableDTO {
    private String result;
    private String title;
    private List<String> lines;
    private List<String> headers;
    private List<HtmlRowDTO> rows;

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getHeaders() {
        return this.headers;
    }

    public void setHeaders(List<String> headers) {
        this.headers = headers;
    }

    public List<HtmlRowDTO> getRows() {
        return this.rows;
    }

    public void setRows(List<HtmlRowDTO> rows) {
        this.rows = rows;
    }

    public List<String> getLines() {
        return this.lines;
    }

    public void setLines(List<String> lines) {
        this.lines = lines;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
