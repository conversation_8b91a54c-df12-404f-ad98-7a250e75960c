package com.geely.gnds.dsa.dto;

/* loaded from: DiaRoutineIdentifierDTO.class */
public class DiaRoutineIdentifierDTO {
    private Long id;
    private String subfunctionIdStr;
    private String subfunctionName;
    private String routineIdentifierIdStr;
    private String routineIdentifierName;
    private String diagnosticPartNumber;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSubfunctionIdStr() {
        return this.subfunctionIdStr;
    }

    public void setSubfunctionIdStr(String subfunctionIdStr) {
        this.subfunctionIdStr = subfunctionIdStr;
    }

    public String getSubfunctionName() {
        return this.subfunctionName;
    }

    public void setSubfunctionName(String subfunctionName) {
        this.subfunctionName = subfunctionName;
    }

    public String getRoutineIdentifierIdStr() {
        return this.routineIdentifierIdStr;
    }

    public void setRoutineIdentifierIdStr(String routineIdentifierIdStr) {
        this.routineIdentifierIdStr = routineIdentifierIdStr;
    }

    public String getRoutineIdentifierName() {
        return this.routineIdentifierName;
    }

    public void setRoutineIdentifierName(String routineIdentifierName) {
        this.routineIdentifierName = routineIdentifierName;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }
}
