package com.geely.gnds.dsa.dto;

import java.util.List;
import java.util.Map;

/* loaded from: TestOrderDTO.class */
public class TestOrderDTO {
    private String title;
    private String time;
    private String comment;
    private String uesrname;
    private Integer passed;
    private Integer failed;
    private Integer executed;
    private Integer warning;
    private List<TestOrderCheckDTO> testOrderProgramming;
    private List<TestOrderCheckDTO> testOrderDefault;
    private List<TestOrderCheckDTO> testOrderExtended;
    private List<TestEcuDTO> testResult;
    private String summary;
    private List<PartNumberConsistencyDTO> partNumberConsistencyList;
    private List<HtmlChapterDTO> chapters;
    private Map<String, String> didRead;
    Map<String, String> didReadBeforeFlushMap;
    private TesterOrderParamDTO testerOrderParam;

    public String getComment() {
        return this.comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getUesrname() {
        return this.uesrname;
    }

    public void setUesrname(String uesrname) {
        this.uesrname = uesrname;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTime() {
        return this.time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Integer getPassed() {
        return this.passed;
    }

    public void setPassed(Integer passed) {
        this.passed = passed;
    }

    public Integer getFailed() {
        return this.failed;
    }

    public void setFailed(Integer failed) {
        this.failed = failed;
    }

    public Integer getExecuted() {
        return this.executed;
    }

    public void setExecuted(Integer executed) {
        this.executed = executed;
    }

    public Integer getWarning() {
        return this.warning;
    }

    public void setWarning(Integer warning) {
        this.warning = warning;
    }

    public List<TestOrderCheckDTO> getTestOrderProgramming() {
        return this.testOrderProgramming;
    }

    public void setTestOrderProgramming(List<TestOrderCheckDTO> testOrderProgramming) {
        this.testOrderProgramming = testOrderProgramming;
    }

    public List<TestOrderCheckDTO> getTestOrderDefault() {
        return this.testOrderDefault;
    }

    public void setTestOrderDefault(List<TestOrderCheckDTO> testOrderDefault) {
        this.testOrderDefault = testOrderDefault;
    }

    public List<TestOrderCheckDTO> getTestOrderExtended() {
        return this.testOrderExtended;
    }

    public void setTestOrderExtended(List<TestOrderCheckDTO> testOrderExtended) {
        this.testOrderExtended = testOrderExtended;
    }

    public List<TestEcuDTO> getTestResult() {
        return this.testResult;
    }

    public void setTestResult(List<TestEcuDTO> testResult) {
        this.testResult = testResult;
    }

    public String getSummary() {
        return this.summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public List<PartNumberConsistencyDTO> getPartNumberConsistencyList() {
        return this.partNumberConsistencyList;
    }

    public void setPartNumberConsistencyList(List<PartNumberConsistencyDTO> partNumberConsistencyList) {
        this.partNumberConsistencyList = partNumberConsistencyList;
    }

    public List<HtmlChapterDTO> getChapters() {
        return this.chapters;
    }

    public void setChapters(List<HtmlChapterDTO> chapters) {
        this.chapters = chapters;
    }

    public Map<String, String> getDidReadBeforeFlushMap() {
        return this.didReadBeforeFlushMap;
    }

    public void setDidReadBeforeFlushMap(Map<String, String> didReadBeforeFlushMap) {
        this.didReadBeforeFlushMap = didReadBeforeFlushMap;
    }

    public TesterOrderParamDTO getTesterOrderParam() {
        return this.testerOrderParam;
    }

    public void setTesterOrderParam(TesterOrderParamDTO testerOrderParam) {
        this.testerOrderParam = testerOrderParam;
    }

    public Map<String, String> getDidRead() {
        return this.didRead;
    }

    public void setDidRead(Map<String, String> didRead) {
        this.didRead = didRead;
    }
}
