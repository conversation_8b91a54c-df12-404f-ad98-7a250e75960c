package com.geely.gnds.dsa.dto;

import java.util.StringJoiner;

/* loaded from: DsaDtcRespDTO.class */
public class DsaDtcRespDTO {
    private String dtcId;
    private Long serviceId;
    private String name;
    private String diagnosticPartNumber;
    private DsaDtcStatusMaskDTO dtcStatus;

    public String getDtcId() {
        return this.dtcId;
    }

    public void setDtcId(String dtcId) {
        this.dtcId = dtcId;
    }

    public Long getServiceId() {
        return this.serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public DsaDtcStatusMaskDTO getDtcStatus() {
        return this.dtcStatus;
    }

    public void setDtcStatus(DsaDtcStatusMaskDTO dtcStatus) {
        this.dtcStatus = dtcStatus;
    }

    public String toString() {
        return new StringJoiner(", ", DsaDtcRespDTO.class.getSimpleName() + "[", "]").add("dtcId='" + this.dtcId + "'").add("serviceId=" + this.serviceId).add("name='" + this.name + "'").add("diagnosticPartNumber='" + this.diagnosticPartNumber + "'").add("dtcStatus=" + this.dtcStatus).toString();
    }
}
