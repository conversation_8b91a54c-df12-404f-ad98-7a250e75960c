package com.geely.gnds.dsa.dto;

import java.io.Serializable;

/* loaded from: EcuParallelDto.class */
public class EcuParallelDto implements Serializable {
    private String address;
    private int threadIndex;

    public EcuParallelDto(String address, int threadIndex) {
        this.address = address;
        this.threadIndex = threadIndex;
    }

    public EcuParallelDto() {
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getThreadIndex() {
        return this.threadIndex;
    }

    public void setThreadIndex(int threadIndex) {
        this.threadIndex = threadIndex;
    }
}
