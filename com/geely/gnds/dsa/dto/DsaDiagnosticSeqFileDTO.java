package com.geely.gnds.dsa.dto;

import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: DsaDiagnosticSeqFileDTO.class */
public class DsaDiagnosticSeqFileDTO {
    private String fileName;
    private String filePath;
    private List<DsaDiagnosticSeqLineDTO> lines;

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public List<DsaDiagnosticSeqLineDTO> getLines() {
        return this.lines;
    }

    public void setLines(List<DsaDiagnosticSeqLineDTO> lines) {
        this.lines = lines;
    }

    public String toString() {
        return new ToStringBuilder(this).append("fileName", this.fileName).append("filePath", this.filePath).append("lines", this.lines).toString();
    }
}
