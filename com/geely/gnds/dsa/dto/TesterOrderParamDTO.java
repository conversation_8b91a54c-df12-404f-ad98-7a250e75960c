package com.geely.gnds.dsa.dto;

import java.util.List;

/* loaded from: TesterOrderParamDTO.class */
public class TesterOrderParamDTO {
    private String platform;
    private List<CalculationEcuDTO> ecus;
    private Boolean afterVp;
    private String vin;
    private boolean testOrderCheck;
    private boolean partNumberConsistency;
    private String platformVehicle;
    private Boolean start;

    public String getPlatform() {
        return this.platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public List<CalculationEcuDTO> getEcus() {
        return this.ecus;
    }

    public void setEcus(List<CalculationEcuDTO> ecus) {
        this.ecus = ecus;
    }

    public Boolean getAfterVp() {
        return this.afterVp;
    }

    public void setAfterVp(Boolean afterVp) {
        this.afterVp = afterVp;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public boolean isTestOrderCheck() {
        return this.testOrderCheck;
    }

    public void setTestOrderCheck(boolean testOrderCheck) {
        this.testOrderCheck = testOrderCheck;
    }

    public String getPlatformVehicle() {
        return this.platformVehicle;
    }

    public void setPlatformVehicle(String platformVehicle) {
        this.platformVehicle = platformVehicle;
    }

    public boolean isPartNumberConsistency() {
        return this.partNumberConsistency;
    }

    public void setPartNumberConsistency(boolean partNumberConsistency) {
        this.partNumberConsistency = partNumberConsistency;
    }

    public Boolean getStart() {
        return this.start;
    }

    public void setStart(Boolean start) {
        this.start = start;
    }
}
