package com.geely.gnds.dsa.dto;

import java.util.List;

/* loaded from: SoftwareBssDTO.class */
public class SoftwareBssDTO {
    private String eolFlag;
    private String dualSourceFlag;
    private String partNum;
    private String partVersion;
    private String partType;
    private String changeNumber;
    private String type;
    private List<QualifierDTO> qualifiers;

    public String getEolFlag() {
        return this.eolFlag;
    }

    public void setEolFlag(String eolFlag) {
        this.eolFlag = eolFlag;
    }

    public String getDualSourceFlag() {
        return this.dualSourceFlag;
    }

    public void setDualSourceFlag(String dualSourceFlag) {
        this.dualSourceFlag = dualSourceFlag;
    }

    public String getPartNum() {
        return this.partNum;
    }

    public void setPartNum(String partNum) {
        this.partNum = partNum;
    }

    public String getPartVersion() {
        return this.partVersion;
    }

    public void setPartVersion(String partVersion) {
        this.partVersion = partVersion;
    }

    public String getPartType() {
        return this.partType;
    }

    public void setPartType(String partType) {
        this.partType = partType;
    }

    public String getChangeNumber() {
        return this.changeNumber;
    }

    public void setChangeNumber(String changeNumber) {
        this.changeNumber = changeNumber;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<QualifierDTO> getQualifiers() {
        return this.qualifiers;
    }

    public void setQualifiers(List<QualifierDTO> qualifiers) {
        this.qualifiers = qualifiers;
    }
}
