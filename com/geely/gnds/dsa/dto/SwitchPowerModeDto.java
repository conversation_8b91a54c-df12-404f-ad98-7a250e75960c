package com.geely.gnds.dsa.dto;

import java.io.Serializable;

/* loaded from: SwitchPowerModeDto.class */
public class SwitchPowerModeDto implements Serializable {
    private String vin;
    private PowerModeDto powerMode;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public PowerModeDto getPowerMode() {
        return this.powerMode;
    }

    public void setPowerMode(PowerModeDto powerMode) {
        this.powerMode = powerMode;
    }

    public SwitchPowerModeDto() {
    }

    public SwitchPowerModeDto(String vin, PowerModeDto powerMode) {
        this.vin = vin;
        this.powerMode = powerMode;
    }
}
