package com.geely.gnds.dsa.dto;

import java.util.List;

/* loaded from: TestOrderCheckDTO.class */
public class TestOrderCheckDTO {
    private String title;
    private String result;
    private String project;
    private String ecuAddress;
    private String supplier;
    private String platform;
    private String type;
    private List<TestOrderCheckDidDTO> testOrderCheckDidList;

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getProject() {
        return this.project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getSupplier() {
        return this.supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public List<TestOrderCheckDidDTO> getTestOrderCheckDidList() {
        return this.testOrderCheckDidList;
    }

    public void setTestOrderCheckDidList(List<TestOrderCheckDidDTO> testOrderCheckDidList) {
        this.testOrderCheckDidList = testOrderCheckDidList;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPlatform() {
        return this.platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
