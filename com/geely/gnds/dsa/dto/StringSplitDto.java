package com.geely.gnds.dsa.dto;

/* loaded from: StringSplitDto.class */
public class StringSplitDto implements Comparable<StringSplitDto> {
    private String did;
    private int index;
    private int start;
    private int end;

    public String getDid() {
        return this.did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public int getIndex() {
        return this.index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getStart() {
        return this.start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getEnd() {
        return this.end;
    }

    public void setEnd(int end) {
        this.end = end;
    }

    @Override // java.lang.Comparable
    public int compareTo(StringSplitDto sp) {
        return getIndex() - sp.getIndex();
    }
}
