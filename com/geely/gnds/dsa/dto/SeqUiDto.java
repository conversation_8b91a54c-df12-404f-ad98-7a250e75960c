package com.geely.gnds.dsa.dto;

import java.util.Objects;

/* loaded from: SeqUiDto.class */
public class SeqUiDto {
    private String seqCode;
    private String initUi;

    public String getSeqCode() {
        return this.seqCode;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public String getInitUi() {
        return this.initUi;
    }

    public void setInitUi(String initUi) {
        this.initUi = initUi;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SeqUiDto seqUiDto = (SeqUiDto) o;
        return Objects.equals(this.seqCode, seqUiDto.seqCode) && Objects.equals(this.initUi, seqUiDto.initUi);
    }

    public int hashCode() {
        return Objects.hash(this.seqCode, this.initUi);
    }
}
