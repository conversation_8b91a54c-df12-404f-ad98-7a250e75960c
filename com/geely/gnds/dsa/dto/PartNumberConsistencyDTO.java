package com.geely.gnds.dsa.dto;

/* loaded from: PartNumberConsistencyDTO.class */
public class PartNumberConsistencyDTO {
    private String title;
    private String result;
    private String project;
    private String ecuAddress;
    private String ecuName;
    private String supplier;
    private String softwareVersion;
    private String hwsdBeforeDefault;
    private String hwsdAfterDefault;
    private String hwsdBeforeProgramming;
    private String hwsdAfterProgramming;
    private String hwsdBeforeExtended;
    private String hwsdAfterExtended;
    private String duBeforeDefault;
    private String duAfterDefault;
    private String duBeforeProgramming;
    private String duAfterProgramming;
    private String duBeforeExtended;
    private String duAfterExtended;

    public String getSoftwareVersion() {
        return this.softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public String getHwsdBeforeDefault() {
        return this.hwsdBeforeDefault;
    }

    public void setHwsdBeforeDefault(String hwsdBeforeDefault) {
        this.hwsdBeforeDefault = hwsdBeforeDefault;
    }

    public String getHwsdAfterDefault() {
        return this.hwsdAfterDefault;
    }

    public void setHwsdAfterDefault(String hwsdAfterDefault) {
        this.hwsdAfterDefault = hwsdAfterDefault;
    }

    public String getHwsdBeforeProgramming() {
        return this.hwsdBeforeProgramming;
    }

    public void setHwsdBeforeProgramming(String hwsdBeforeProgramming) {
        this.hwsdBeforeProgramming = hwsdBeforeProgramming;
    }

    public String getHwsdAfterProgramming() {
        return this.hwsdAfterProgramming;
    }

    public void setHwsdAfterProgramming(String hwsdAfterProgramming) {
        this.hwsdAfterProgramming = hwsdAfterProgramming;
    }

    public String getHwsdBeforeExtended() {
        return this.hwsdBeforeExtended;
    }

    public void setHwsdBeforeExtended(String hwsdBeforeExtended) {
        this.hwsdBeforeExtended = hwsdBeforeExtended;
    }

    public String getHwsdAfterExtended() {
        return this.hwsdAfterExtended;
    }

    public void setHwsdAfterExtended(String hwsdAfterExtended) {
        this.hwsdAfterExtended = hwsdAfterExtended;
    }

    public String getDuBeforeDefault() {
        return this.duBeforeDefault;
    }

    public void setDuBeforeDefault(String duBeforeDefault) {
        this.duBeforeDefault = duBeforeDefault;
    }

    public String getDuAfterDefault() {
        return this.duAfterDefault;
    }

    public void setDuAfterDefault(String duAfterDefault) {
        this.duAfterDefault = duAfterDefault;
    }

    public String getDuBeforeProgramming() {
        return this.duBeforeProgramming;
    }

    public void setDuBeforeProgramming(String duBeforeProgramming) {
        this.duBeforeProgramming = duBeforeProgramming;
    }

    public String getDuAfterProgramming() {
        return this.duAfterProgramming;
    }

    public void setDuAfterProgramming(String duAfterProgramming) {
        this.duAfterProgramming = duAfterProgramming;
    }

    public String getDuBeforeExtended() {
        return this.duBeforeExtended;
    }

    public void setDuBeforeExtended(String duBeforeExtended) {
        this.duBeforeExtended = duBeforeExtended;
    }

    public String getDuAfterExtended() {
        return this.duAfterExtended;
    }

    public void setDuAfterExtended(String duAfterExtended) {
        this.duAfterExtended = duAfterExtended;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getProject() {
        return this.project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public void setEcuAddress(String ecuAddress) {
        this.ecuAddress = ecuAddress;
    }

    public String getSupplier() {
        return this.supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }
}
