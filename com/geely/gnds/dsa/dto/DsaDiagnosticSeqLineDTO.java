package com.geely.gnds.dsa.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import java.util.StringJoiner;

/* loaded from: DsaDiagnosticSeqLineDTO.class */
public class DsaDiagnosticSeqLineDTO {
    private Integer number;
    private String ecu;
    private String hexString;
    private Long delay;
    private String note;
    private int runStatus;

    @JsonIgnore
    private List<DiaSessionDTO> didSessions;
    private String securityPinCode;

    @JsonIgnore
    private Integer hexType;

    public String getEcu() {
        return this.ecu;
    }

    public void setEcu(String ecu) {
        this.ecu = ecu;
    }

    public String getHexString() {
        return this.hexString;
    }

    public void setHexString(String hexString) {
        this.hexString = hexString;
    }

    public Long getDelay() {
        return this.delay;
    }

    public void setDelay(Long delay) {
        this.delay = delay;
    }

    public String getNote() {
        return this.note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Integer getNumber() {
        return this.number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public int getRunStatus() {
        return this.runStatus;
    }

    public void setRunStatus(int runStatus) {
        this.runStatus = runStatus;
    }

    public List<DiaSessionDTO> getDidSessions() {
        return this.didSessions;
    }

    public void setDidSessions(List<DiaSessionDTO> didSessions) {
        this.didSessions = didSessions;
    }

    public String getSecurityPinCode() {
        return this.securityPinCode;
    }

    public void setSecurityPinCode(String securityPinCode) {
        this.securityPinCode = securityPinCode;
    }

    public Integer getHexType() {
        return this.hexType;
    }

    public void setHexType(Integer hexType) {
        this.hexType = hexType;
    }

    public String toString() {
        return new StringJoiner(", ", DsaDiagnosticSeqLineDTO.class.getSimpleName() + "[", "]").add("number=" + this.number).add("ecu='" + this.ecu + "'").add("hexString='" + this.hexString + "'").add("delay=" + this.delay).add("note='" + this.note + "'").add("runStatus=" + this.runStatus).add("didSessions=" + this.didSessions).add("securityPinCode='" + this.securityPinCode + "'").add("hexType=" + this.hexType).toString();
    }
}
