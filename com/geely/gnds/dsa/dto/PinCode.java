package com.geely.gnds.dsa.dto;

import java.util.StringJoiner;

/* loaded from: PinCode.class */
public class PinCode {
    private String pinCodeName;
    private String pinCodeValue;

    public String getPinCodeName() {
        return this.pinCodeName;
    }

    public void setPinCodeName(String pinCodeName) {
        this.pinCodeName = pinCodeName;
    }

    public String getPinCodeValue() {
        return this.pinCodeValue;
    }

    public void setPinCodeValue(String pinCodeValue) {
        this.pinCodeValue = pinCodeValue;
    }

    public String toString() {
        return new StringJoiner(", ", PinCode.class.getSimpleName() + "[", "]").add("pinCodeName='" + this.pinCodeName + "'").add("pinCodeValue='" + this.pinCodeValue + "'").toString();
    }
}
