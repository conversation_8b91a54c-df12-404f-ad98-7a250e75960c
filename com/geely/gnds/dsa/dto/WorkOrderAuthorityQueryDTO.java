package com.geely.gnds.dsa.dto;

import java.io.Serializable;
import java.util.List;

/* loaded from: WorkOrderAuthorityQueryDTO.class */
public class WorkOrderAuthorityQueryDTO implements Serializable {
    private static final long serialVersionUID = 189433211262999926L;
    private String workerOrderType;
    private List<Long> menuIdList;
    private String dbname;
    private String language;
    private String tenantCode;

    public String getWorkerOrderType() {
        return this.workerOrderType;
    }

    public void setWorkerOrderType(String workerOrderType) {
        this.workerOrderType = workerOrderType;
    }

    public List<Long> getMenuIdList() {
        return this.menuIdList;
    }

    public void setMenuIdList(List<Long> menuIdList) {
        this.menuIdList = menuIdList;
    }

    public String getDbname() {
        return this.dbname;
    }

    public void setDbname(String dbname) {
        this.dbname = dbname;
    }

    public String getLanguage() {
        return this.language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
}
