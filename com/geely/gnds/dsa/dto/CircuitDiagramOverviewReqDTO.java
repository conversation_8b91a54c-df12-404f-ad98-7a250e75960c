package com.geely.gnds.dsa.dto;

import java.io.Serializable;
import java.util.List;

/* loaded from: CircuitDiagramOverviewReqDTO.class */
public class CircuitDiagramOverviewReqDTO implements Serializable {
    private static final long serialVersionUID = -5962632454716376427L;
    private String vin;
    private List<String> wdids;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public List<String> getWdids() {
        return this.wdids;
    }

    public void setWdids(List<String> wdids) {
        this.wdids = wdids;
    }
}
