package com.geely.gnds.dsa.dto;

import java.util.StringJoiner;

/* loaded from: DsaDtcStatusMaskDTO.class */
public class DsaDtcStatusMaskDTO {
    private Boolean testFailed;
    private Boolean testFailedThisOperationCycle;
    private Boolean pendingDtc;
    private Boolean confirmedDtc;
    private Boolean testNotCompletedSinceLastClear;
    private Boolean testFailedSinceLastClear;
    private Boolean testNotCompletedThisOperationCycle;
    private Boolean warningIndicatorRequested;

    public Boolean getTestFailed() {
        return this.testFailed;
    }

    public void setTestFailed(Boolean testFailed) {
        this.testFailed = testFailed;
    }

    public Boolean getTestFailedThisOperationCycle() {
        return this.testFailedThisOperationCycle;
    }

    public void setTestFailedThisOperationCycle(Boolean testFailedThisOperationCycle) {
        this.testFailedThisOperationCycle = testFailedThisOperationCycle;
    }

    public Boolean getPendingDtc() {
        return this.pendingDtc;
    }

    public void setPendingDtc(Boolean pendingDtc) {
        this.pendingDtc = pendingDtc;
    }

    public Boolean getConfirmedDtc() {
        return this.confirmedDtc;
    }

    public void setConfirmedDtc(Boolean confirmedDtc) {
        this.confirmedDtc = confirmedDtc;
    }

    public Boolean getTestNotCompletedSinceLastClear() {
        return this.testNotCompletedSinceLastClear;
    }

    public void setTestNotCompletedSinceLastClear(Boolean testNotCompletedSinceLastClear) {
        this.testNotCompletedSinceLastClear = testNotCompletedSinceLastClear;
    }

    public Boolean getTestFailedSinceLastClear() {
        return this.testFailedSinceLastClear;
    }

    public void setTestFailedSinceLastClear(Boolean testFailedSinceLastClear) {
        this.testFailedSinceLastClear = testFailedSinceLastClear;
    }

    public Boolean getTestNotCompletedThisOperationCycle() {
        return this.testNotCompletedThisOperationCycle;
    }

    public void setTestNotCompletedThisOperationCycle(Boolean testNotCompletedThisOperationCycle) {
        this.testNotCompletedThisOperationCycle = testNotCompletedThisOperationCycle;
    }

    public Boolean getWarningIndicatorRequested() {
        return this.warningIndicatorRequested;
    }

    public void setWarningIndicatorRequested(Boolean warningIndicatorRequested) {
        this.warningIndicatorRequested = warningIndicatorRequested;
    }

    public String toString() {
        return new StringJoiner(", ", DsaDtcStatusMaskDTO.class.getSimpleName() + "[", "]").add("testFailed=" + this.testFailed).add("testFailedThisOperationCycle=" + this.testFailedThisOperationCycle).add("pendingDtc=" + this.pendingDtc).add("confirmedDtc=" + this.confirmedDtc).add("testNotCompletedSinceLastClear=" + this.testNotCompletedSinceLastClear).add("testFailedSinceLastClear=" + this.testFailedSinceLastClear).add("testNotCompletedThisOperationCycle=" + this.testNotCompletedThisOperationCycle).add("warningIndicatorRequested=" + this.warningIndicatorRequested).toString();
    }
}
