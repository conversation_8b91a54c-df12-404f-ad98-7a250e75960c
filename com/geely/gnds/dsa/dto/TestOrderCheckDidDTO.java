package com.geely.gnds.dsa.dto;

/* loaded from: TestOrderCheckDidDTO.class */
public class TestOrderCheckDidDTO {
    private String did;
    private String swName;
    private String diagSession;
    private String expectValue;
    private String softwareLevel;
    private String read;
    private String readHex;
    private String comment;
    private String result;

    public String getDid() {
        return this.did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getSwName() {
        return this.swName;
    }

    public void setSwName(String swName) {
        this.swName = swName;
    }

    public String getDiagSession() {
        return this.diagSession;
    }

    public void setDiagSession(String diagSession) {
        this.diagSession = diagSession;
    }

    public String getExpectValue() {
        return this.expectValue;
    }

    public void setExpectValue(String expectValue) {
        this.expectValue = expectValue;
    }

    public String getSoftwareLevel() {
        return this.softwareLevel;
    }

    public void setSoftwareLevel(String softwareLevel) {
        this.softwareLevel = softwareLevel;
    }

    public String getRead() {
        return this.read;
    }

    public void setRead(String read) {
        this.read = read;
    }

    public String getReadHex() {
        return this.readHex;
    }

    public void setReadHex(String readHex) {
        this.readHex = readHex;
    }

    public String getComment() {
        return this.comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getResult() {
        return this.result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public TestOrderCheckDidDTO(String did, String diagSession, String read, String readHex, String result) {
        this.did = did;
        this.diagSession = diagSession;
        this.read = read;
        this.readHex = readHex;
        this.result = result;
    }

    public TestOrderCheckDidDTO(String did, String diagSession, String read, String readHex, String result, String softwareLevel) {
        this.did = did;
        this.diagSession = diagSession;
        this.read = read;
        this.readHex = readHex;
        this.result = result;
        this.softwareLevel = softwareLevel;
    }

    public TestOrderCheckDidDTO() {
    }
}
