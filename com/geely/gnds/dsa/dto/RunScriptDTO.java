package com.geely.gnds.dsa.dto;

import java.util.List;

/* loaded from: RunScriptDTO.class */
public class RunScriptDTO {
    private List argument;
    private Object result;
    private String exceptionMsg;

    public List getArgument() {
        return this.argument;
    }

    public void setArgument(List argument) {
        this.argument = argument;
    }

    public Object getResult() {
        return this.result;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public String getExceptionMsg() {
        return this.exceptionMsg;
    }

    public void setExceptionMsg(String exceptionMsg) {
        this.exceptionMsg = exceptionMsg;
    }
}
