package com.geely.gnds.dsa.dto;

import java.io.Serializable;
import java.util.Objects;

/* loaded from: DsaSoftwareDownloadConfigDto.class */
public class DsaSoftwareDownloadConfigDto implements Serializable {
    private boolean queuedRequest;
    private int parallelDownload;
    private boolean startParallelDownload;
    private boolean preProgramming;
    private boolean completeAndCompatible;
    private boolean resetAfterDownload;
    private boolean checkMemory;
    private boolean firstCheckMemoryWithSignature;
    private boolean versionCheck;
    private boolean hvFlush;
    private int downloadCount;
    private int platform;

    public DsaSoftwareDownloadConfigDto() {
        this.preProgramming = true;
        this.completeAndCompatible = true;
        this.resetAfterDownload = true;
        this.checkMemory = true;
        this.versionCheck = false;
        this.hvFlush = false;
        this.platform = 2;
    }

    public boolean isQueuedRequest() {
        return this.queuedRequest;
    }

    public void setQueuedRequest(boolean queuedRequest) {
        this.queuedRequest = queuedRequest;
    }

    public int getParallelDownload() {
        return this.parallelDownload;
    }

    public void setParallelDownload(int parallelDownload) {
        this.parallelDownload = parallelDownload;
    }

    public boolean isStartParallelDownload() {
        return this.startParallelDownload;
    }

    public void setStartParallelDownload(boolean startParallelDownload) {
        this.startParallelDownload = startParallelDownload;
    }

    public boolean isPreProgramming() {
        return this.preProgramming;
    }

    public void setPreProgramming(boolean preProgramming) {
        this.preProgramming = preProgramming;
    }

    public boolean isCompleteAndCompatible() {
        return this.completeAndCompatible;
    }

    public void setCompleteAndCompatible(boolean completeAndCompatible) {
        this.completeAndCompatible = completeAndCompatible;
    }

    public boolean isResetAfterDownload() {
        return this.resetAfterDownload;
    }

    public void setResetAfterDownload(boolean resetAfterDownload) {
        this.resetAfterDownload = resetAfterDownload;
    }

    public boolean isCheckMemory() {
        return this.checkMemory;
    }

    public void setCheckMemory(boolean checkMemory) {
        this.checkMemory = checkMemory;
    }

    public boolean isFirstCheckMemoryWithDevSignature() {
        return this.firstCheckMemoryWithSignature;
    }

    public void setFirstCheckMemoryWithDevSignature(boolean firstCheckMemoryWithDevSignature) {
        this.firstCheckMemoryWithSignature = firstCheckMemoryWithDevSignature;
    }

    public boolean isFirstCheckMemoryWithSignature() {
        return this.firstCheckMemoryWithSignature;
    }

    public void setFirstCheckMemoryWithSignature(boolean firstCheckMemoryWithSignature) {
        this.firstCheckMemoryWithSignature = firstCheckMemoryWithSignature;
    }

    public boolean isVersionCheck() {
        return this.versionCheck;
    }

    public void setVersionCheck(boolean versionCheck) {
        this.versionCheck = versionCheck;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DsaSoftwareDownloadConfigDto that = (DsaSoftwareDownloadConfigDto) o;
        return this.queuedRequest == that.queuedRequest && this.parallelDownload == that.parallelDownload && this.startParallelDownload == that.startParallelDownload && this.preProgramming == that.preProgramming && this.completeAndCompatible == that.completeAndCompatible && this.resetAfterDownload == that.resetAfterDownload && this.checkMemory == that.checkMemory && this.firstCheckMemoryWithSignature == that.firstCheckMemoryWithSignature;
    }

    public int hashCode() {
        return Objects.hash(Boolean.valueOf(this.queuedRequest), Integer.valueOf(this.parallelDownload), Boolean.valueOf(this.startParallelDownload), Boolean.valueOf(this.preProgramming), Boolean.valueOf(this.completeAndCompatible), Boolean.valueOf(this.resetAfterDownload), Boolean.valueOf(this.checkMemory), Boolean.valueOf(this.firstCheckMemoryWithSignature));
    }

    public DsaSoftwareDownloadConfigDto(boolean queuedRequest, int parallelDownload, boolean startParallelDownload, boolean preProgramming, boolean completeAndCompatible, boolean resetAfterDownload, boolean checkMemory, boolean firstCheckMemoryWithDevSignature, boolean hvFlush) {
        this.queuedRequest = queuedRequest;
        this.parallelDownload = parallelDownload;
        this.startParallelDownload = startParallelDownload;
        this.preProgramming = preProgramming;
        this.completeAndCompatible = completeAndCompatible;
        this.resetAfterDownload = resetAfterDownload;
        this.checkMemory = checkMemory;
        this.firstCheckMemoryWithSignature = firstCheckMemoryWithDevSignature;
        this.hvFlush = hvFlush;
    }

    public boolean isHvFlush() {
        return this.hvFlush;
    }

    public void setHvFlush(boolean hvFlush) {
        this.hvFlush = hvFlush;
    }

    public int getDownloadCount() {
        return this.downloadCount;
    }

    public void setDownloadCount(int downloadCount) {
        this.downloadCount = downloadCount;
    }

    public int getPlatform() {
        return this.platform;
    }

    public void setPlatform(int platform) {
        this.platform = platform;
    }
}
