package com.geely.gnds.dsa.dto;

import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;

/* loaded from: SaveDsaPinCodeReqDTO.class */
public class SaveDsaPinCodeReqDTO {
    private List<PinCodeDTO> pinCodeList;

    public List<PinCodeDTO> getPinCodeList() {
        return this.pinCodeList;
    }

    public void setPinCodeList(List<PinCodeDTO> pinCodeList) {
        this.pinCodeList = pinCodeList;
    }

    public String toString() {
        return new ToStringBuilder(this).append("pinCodeList", this.pinCodeList).toString();
    }
}
