package com.geely.gnds.dsa.dto;

/* loaded from: DiaTreeNode.class */
public class DiaTreeNode {
    private String ecuName;
    private String address;
    private String strId;
    private String swName;
    private String diagnosticPartNum;
    private String serviceName;
    private String serviceId;
    private String dataIdentifierId;
    private String name;
    private String type;
    private boolean leaf;

    public String getServiceName() {
        return this.serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServiceId() {
        return this.serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getDataIdentifierId() {
        return this.dataIdentifierId;
    }

    public void setDataIdentifierId(String dataIdentifierId) {
        this.dataIdentifierId = dataIdentifierId;
    }

    public String getEcuName() {
        return this.ecuName;
    }

    public void setEcuName(String ecuName) {
        this.ecuName = ecuName;
    }

    public String getSwName() {
        return this.swName;
    }

    public void setSwName(String swName) {
        this.swName = swName;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDiagnosticPartNum() {
        return this.diagnosticPartNum;
    }

    public void setDiagnosticPartNum(String diagnosticPartNum) {
        this.diagnosticPartNum = diagnosticPartNum;
    }

    public String getStrId() {
        return this.strId;
    }

    public void setStrId(String strId) {
        this.strId = strId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean getLeaf() {
        return this.leaf;
    }

    public void setLeaf(boolean leaf) {
        this.leaf = leaf;
    }
}
