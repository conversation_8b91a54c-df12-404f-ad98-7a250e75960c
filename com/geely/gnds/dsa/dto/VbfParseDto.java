package com.geely.gnds.dsa.dto;

import java.io.Serializable;

/* loaded from: VbfParseDto.class */
public class VbfParseDto implements Serializable {
    private String address;
    private String filePath;
    private String vbfName;
    private String softwareType;
    private Long vbfSize;
    private String swPartNumber;
    private String swVersion;
    private String swPartType;
    private String fileChecksum;
    private int asciiCount;
    private Boolean flushSuccess;

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getVbfName() {
        return this.vbfName;
    }

    public void setVbfName(String vbfName) {
        this.vbfName = vbfName;
    }

    public String getSoftwareType() {
        return this.softwareType;
    }

    public void setSoftwareType(String softwareType) {
        this.softwareType = softwareType;
    }

    public Long getVbfSize() {
        return this.vbfSize;
    }

    public void setVbfSize(Long vbfSize) {
        this.vbfSize = vbfSize;
    }

    public String getSwPartNumber() {
        return this.swPartNumber;
    }

    public void setSwPartNumber(String swPartNumber) {
        this.swPartNumber = swPartNumber;
    }

    public String getSwVersion() {
        return this.swVersion;
    }

    public void setSwVersion(String swVersion) {
        this.swVersion = swVersion;
    }

    public String getSwPartType() {
        return this.swPartType;
    }

    public void setSwPartType(String swPartType) {
        this.swPartType = swPartType;
    }

    public String getFileChecksum() {
        return this.fileChecksum;
    }

    public void setFileChecksum(String fileChecksum) {
        this.fileChecksum = fileChecksum;
    }

    public int getAsciiCount() {
        return this.asciiCount;
    }

    public void setAsciiCount(int asciiCount) {
        this.asciiCount = asciiCount;
    }

    public Boolean getFlushSuccess() {
        return this.flushSuccess;
    }

    public void setFlushSuccess(Boolean flushSuccess) {
        this.flushSuccess = flushSuccess;
    }
}
