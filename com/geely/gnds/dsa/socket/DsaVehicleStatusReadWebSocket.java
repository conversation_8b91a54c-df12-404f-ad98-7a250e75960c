package com.geely.gnds.dsa.socket;

import java.util.concurrent.CopyOnWriteArrayList;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@ServerEndpoint("/api/v1/socket/dsaStatusRead")
@Component
/* loaded from: DsaVehicleStatusReadWebSocket.class */
public class DsaVehicleStatusReadWebSocket {
    private static final Logger LOG = LoggerFactory.getLogger(DsaVehicleStatusReadWebSocket.class);
    private static CopyOnWriteArrayList<Session> ff = new CopyOnWriteArrayList<>();

    public static boolean v() {
        return !CollectionUtils.isEmpty(ff);
    }

    public static CopyOnWriteArrayList<Session> getVehicleStatusReadSessionList() {
        return ff;
    }

    @OnOpen
    public void onOpen(Session session) {
        ff.add(session);
    }

    @OnClose
    public void onClose(Session session) {
        if (session != null) {
            ff.remove(session);
        }
    }

    @OnMessage
    public void onMessage(Session session, String message) {
        LOG.info("接收到车辆连接心跳：{}", message);
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        LOG.error("DSA读取车辆状态长链接异常", throwable);
    }
}
