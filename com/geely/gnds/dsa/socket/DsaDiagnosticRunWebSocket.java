package com.geely.gnds.dsa.socket;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArraySet;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@ServerEndpoint("/api/v1/socket/dsa/run/{userId}")
@Component
/* loaded from: DsaDiagnosticRunWebSocket.class */
public class DsaDiagnosticRunWebSocket {
    private Session fc;
    private static final Logger LOG = LoggerFactory.getLogger(DsaLogWebSocket.class);
    private static CopyOnWriteArraySet<DsaDiagnosticRunWebSocket> fd = new CopyOnWriteArraySet<>();
    private static Map<Long, Session> fe = new HashMap();

    @OnOpen
    public void a(Session session, @PathParam("userId") Long userId) {
        try {
            this.fc = session;
            fd.add(this);
            fe.put(userId, session);
            LOG.info("【websocket消息】有新的连接，总数为:" + fd.size());
        } catch (Exception e) {
            LOG.info("【websocket消息】onOpen 出现异常，总数为:" + fd.size());
        }
    }

    @OnClose
    public void onClose(Session session) {
        try {
            fd.remove(this);
            LOG.info("【websocket消息】连接断开，总数为:" + fd.size());
        } catch (Exception e) {
        }
    }

    @OnMessage
    public void onMessage(Session session, String message) {
        LOG.info("【websocket消息】收到客户端消息:" + message);
    }

    @OnError
    public void onError(Session session, Throwable error) {
        LOG.error("用户错误,原因:" + error.getMessage());
    }

    public void a(Long userId, String message) {
        Session session = fe.get(userId);
        if (session != null && session.isOpen()) {
            try {
                LOG.info("【websocket消息】 单点消息:" + message);
                session.getAsyncRemote().sendText(message);
            } catch (Exception e) {
                LOG.error("【websocket消息】 单点消息异常:userId={},message={},e={}", new Object[]{userId, message, e});
            }
        }
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DsaDiagnosticRunWebSocket that = (DsaDiagnosticRunWebSocket) o;
        return Objects.equals(this.fc, that.fc);
    }

    public int hashCode() {
        return Objects.hash(this.fc);
    }
}
