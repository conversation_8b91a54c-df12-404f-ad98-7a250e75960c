package com.geely.gnds.dsa.socket;

import java.util.concurrent.CopyOnWriteArrayList;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@ServerEndpoint("/api/v1/socket/log")
@Component
/* loaded from: DsaLogWebSocket.class */
public class DsaLogWebSocket {
    private static final Logger LOG = LoggerFactory.getLogger(DsaLogWebSocket.class);
    private static CopyOnWriteArrayList<Session> logSessionList = new CopyOnWriteArrayList<>();

    public static boolean hasLogSession() {
        return !CollectionUtils.isEmpty(logSessionList);
    }

    public static CopyOnWriteArrayList<Session> getLogSessionList() {
        return logSessionList;
    }

    @OnOpen
    public void onOpen(Session session) {
        logSessionList.add(session);
    }

    @OnClose
    public void onClose(Session session) {
        if (session != null) {
            logSessionList.remove(session);
        }
    }

    @OnMessage
    public void onMessage(Session session, String message) {
        LOG.info("接收到车辆连接心跳：{}", message);
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        LOG.error("读取车辆状态长链接异常", throwable);
    }
}
