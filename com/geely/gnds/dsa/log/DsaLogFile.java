package com.geely.gnds.dsa.log;

import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.StreamCloseUtil;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DsaLogFile.class */
public class DsaLogFile {
    private static final Logger LOGGER = LoggerFactory.getLogger(DsaLogFile.class);
    private FileOutputStream bI = null;
    private OutputStream bJ = null;

    public void a(File file) throws IOException {
        File parent = file.getParentFile();
        if (!parent.exists() && !parent.mkdirs()) {
            throw new IOException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00160) + file.getAbsolutePath());
        }
        try {
            this.bI = new FileOutputStream(file);
            this.bJ = new BufferedOutputStream(this.bI);
            this.bJ.flush();
        } catch (IOException e) {
            StreamCloseUtil.close(LOGGER, this.bJ, this.bI);
            throw e;
        }
    }

    public void close() throws IOException {
        StreamCloseUtil.closeAfterFlush(LOGGER, this.bJ, this.bI);
    }

    public void write(String type, String content) throws IOException {
        this.bJ.write(content.getBytes());
        this.bJ.write(System.getProperty("line.separator").getBytes());
        this.bJ.flush();
    }
}
