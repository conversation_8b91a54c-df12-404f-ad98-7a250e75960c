package com.geely.gnds.dsa.log;

import java.util.Objects;

/* loaded from: DsaFlushData.class */
public class DsaFlushData {
    private final String type;
    private final String status;
    private final Integer progress;
    private final String swPartNumber;
    private final String color;
    private final String ecuAddress;
    private final String flushType;

    public DsaFlushData(String type, Integer progress, String status, String swPartNumber, String color) {
        this.type = type;
        this.status = status;
        this.progress = progress;
        this.swPartNumber = swPartNumber;
        this.color = color;
        this.ecuAddress = "";
        this.flushType = "";
    }

    public DsaFlushData(String type, Integer progress, String status, String swPartNumber) {
        this.type = type;
        this.status = status;
        this.progress = progress;
        this.swPartNumber = swPartNumber;
        this.color = "Transparent";
        this.ecuAddress = "";
        this.flushType = "";
    }

    public DsaFlushData(String type, Integer progress, String status, String swPartNumber, String color, String ecuAddress, String flushType) {
        this.type = type;
        this.status = status;
        this.progress = progress;
        this.swPartNumber = swPartNumber;
        this.color = color;
        this.ecuAddress = ecuAddress;
        this.flushType = flushType;
    }

    public String getType() {
        return this.type;
    }

    public String getStatus() {
        return this.status;
    }

    public Integer getProgress() {
        return this.progress;
    }

    public String getSwPartNumber() {
        return this.swPartNumber;
    }

    public String getColor() {
        return this.color;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public String getFlushType() {
        return this.flushType;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DsaFlushData that = (DsaFlushData) o;
        return Objects.equals(this.type, that.type) && Objects.equals(this.status, that.status) && Objects.equals(this.progress, that.progress) && Objects.equals(this.swPartNumber, that.swPartNumber) && Objects.equals(this.color, that.color) && Objects.equals(this.ecuAddress, that.ecuAddress) && Objects.equals(this.flushType, that.flushType);
    }

    public int hashCode() {
        return Objects.hash(this.type, this.status, this.progress, this.swPartNumber, this.color, this.ecuAddress, this.flushType);
    }
}
