package com.geely.gnds.dsa.log;

import cn.hutool.core.io.FileUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.FdThread;
import com.geely.gnds.dsa.socket.DsaLogWebSocket;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.Iterator;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CopyOnWriteArrayList;
import javax.websocket.RemoteEndpoint;
import javax.websocket.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: FdDsaLogger.class */
public class FdDsaLogger implements IFdDsaLogger {
    private static final Logger logger = LoggerFactory.getLogger(FdDsaLogger.class);
    private static final long TIME_SLEEP = 1000;
    private static final String LOGGER_NAME = "TXT日志记录器";
    private final File file;
    private String vin;
    private String username;
    private ConcurrentLinkedQueue<DsaLogData> txtQueue = new ConcurrentLinkedQueue<>();
    private FdThread thread = null;
    private DsaLogFile dsaLogFile = null;
    private Object awakeObject = new Object();

    public FdDsaLogger(String path, String vin) throws InterruptedException {
        if (StringUtils.isBlank(path)) {
            String childPath = "GNDS_" + DateUtils.parseDateToStr(DateUtils.YYYYMMDD_HHMMSS, new Date()) + ".log";
            this.file = FileUtil.file(path, childPath);
        } else {
            this.file = FileUtil.file(path);
        }
        this.vin = vin;
        createInstance("dsaLogger-" + vin + "-" + System.currentTimeMillis());
    }

    private void createInstance(String threadName) throws InterruptedException {
        this.thread = new FdThread(threadName) { // from class: com.geely.gnds.dsa.log.FdDsaLogger.1
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() throws InterruptedException {
                FdDsaLogger.logger.info("===TXT日志记录器启动完成===");
                while (isFdAlived()) {
                    DsaLogData msg = (DsaLogData) FdDsaLogger.this.txtQueue.poll();
                    long startTime = System.currentTimeMillis() + FdDsaLogger.TIME_SLEEP;
                    while (msg != null) {
                        if (FdDsaLogger.this.dsaLogFile != null) {
                            try {
                                FdDsaLogger.this.dsaLogFile.write(msg.getType(), msg.getContent());
                            } catch (Exception e) {
                                FdDsaLogger.logger.error("===TXT日志记录器填写日志失败===");
                                FdDsaLogger.logger.error(FdHelper.getExceptionAsString(e));
                            }
                            if (isFdAlived() && System.currentTimeMillis() > startTime) {
                                try {
                                    Thread.sleep(FdDsaLogger.TIME_SLEEP);
                                } catch (InterruptedException e2) {
                                    FdDsaLogger.logger.error("TXT日志记录器连续处理时间超过1s停顿失败!!!");
                                    FdDsaLogger.logger.error(FdHelper.getExceptionAsString(e2));
                                }
                                startTime = System.currentTimeMillis() + FdDsaLogger.TIME_SLEEP;
                            }
                        }
                        msg = (DsaLogData) FdDsaLogger.this.txtQueue.poll();
                    }
                    if (isFdAlived()) {
                        FdDsaLogger.this.wait4Data();
                    }
                }
                FdDsaLogger.logger.info("===TXT日志记录器关闭===");
            }
        };
        this.dsaLogFile = new DsaLogFile();
        try {
            this.dsaLogFile.a(this.file);
        } catch (IOException e) {
            logger.error("TXT日志记录器启动失败,日志文件名:" + this.file.getAbsolutePath());
            logger.error(FdHelper.getExceptionAsString(e));
            this.dsaLogFile = null;
        }
        this.thread.start();
        try {
            Thread.sleep(100L);
        } catch (InterruptedException e2) {
            logger.error("TXT日志记录器启动Sleep中断,日志文件名:" + this.file.getAbsolutePath());
            logger.error(FdHelper.getExceptionAsString(e2));
        }
    }

    @Override // com.geely.gnds.dsa.log.IFdDsaLogger
    public void write(String type, String content) throws Exception {
        DsaLogData d = new DsaLogData(type, content);
        this.txtQueue.add(d);
        notify4Data();
        try {
            if (DsaLogWebSocket.hasLogSession()) {
                CopyOnWriteArrayList<Session> logSessionList = DsaLogWebSocket.getLogSessionList();
                Iterator<Session> it = logSessionList.iterator();
                while (it.hasNext()) {
                    Session session = it.next();
                    synchronized (session) {
                        RemoteEndpoint.Basic basicRemote = session.getBasicRemote();
                        String data = ObjectMapperUtils.obj2JsonStr(d);
                        logger.info("DSA日志socket推送:{}", data);
                        basicRemote.sendText(data);
                    }
                }
            } else {
                logger.info("DSA日志socket没有初始化，无法推送");
            }
        } catch (Exception e) {
            logger.error("DSA日志记录失败", e);
            try {
                throw e;
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
    }

    @Override // com.geely.gnds.dsa.log.IFdDsaLogger
    public void writeToLog(String type, String content) {
        DsaLogData d = new DsaLogData(type, content);
        this.txtQueue.add(d);
        notify4Data();
    }

    @Override // com.geely.gnds.dsa.log.IFdDsaLogger
    public void writeToLog(String content) {
        DsaLogData d = new DsaLogData("", content);
        this.txtQueue.add(d);
        notify4Data();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void wait4Data() {
        try {
            synchronized (this.awakeObject) {
                this.awakeObject.wait();
            }
        } catch (InterruptedException e) {
            logger.info("TXT日志记录器沉睡失败!!!");
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    private void notify4Data() {
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }

    @Override // com.geely.gnds.dsa.log.IFdDsaLogger
    public void close() throws IOException {
        this.thread.close();
        notify4Data();
        try {
            this.thread.join();
        } catch (InterruptedException e) {
            logger.error("dsa日志记录器关闭异常,异常原因:" + e.getMessage());
            logger.error(FdHelper.getExceptionAsString(e));
        }
        if (this.dsaLogFile != null) {
            this.dsaLogFile.close();
        }
    }
}
