package com.geely.gnds.dsa.controller;

import com.alibaba.fastjson.JSON;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.dto.BssIdDTO;
import com.geely.gnds.dsa.dto.CalculationEcuDTO;
import com.geely.gnds.dsa.dto.SoftwareDownloadDto;
import com.geely.gnds.dsa.dto.TestOrderDTO;
import com.geely.gnds.dsa.dto.TesterOrderParamDTO;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.IntegrationTestService;
import com.geely.gnds.dsa.vo.UploadVo;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.util.Result;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"/api/dsa/v1/integrationTest"})
@RestController
/* loaded from: IntegrationTestController.class */
public class IntegrationTestController {

    @Autowired
    private IntegrationTestService em;

    @Autowired
    private TokenService tokenService;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(IntegrationTestController.class);

    @PostMapping({"uploadBss"})
    public Result<BssIdDTO> a(@RequestBody UploadVo uploadVo) {
        log.info("DSA加载本地bss接口口入参----->{}", uploadVo);
        Result<BssIdDTO> result = new Result<>();
        try {
            this.em.uploadLocalBss(uploadVo);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------DSA加载本地bss成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.ERROR, TesterErrorCodeEnum.SG00269);
        }
        return result;
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r13v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r13v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 13, insn: 0x0138: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r13 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('inputStreamReader' java.io.InputStreamReader)]) A[TRY_LEAVE], block:B:48:0x0138 */
    /* JADX WARN: Not initialized variable reg: 14, insn: 0x013d: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r14 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:50:0x013d */
    /* JADX WARN: Type inference failed for: r13v1, names: [inputStreamReader], types: [java.io.InputStreamReader] */
    /* JADX WARN: Type inference failed for: r14v0, types: [java.lang.Throwable] */
    @PostMapping({"uploadBssBrowser"})
    public Result<BssIdDTO> b(@RequestParam("file") MultipartFile file, @RequestParam("vin") String vin) throws Exception {
        String filePath;
        ?? r13;
        ?? r14;
        log.info("浏览器上传bss-{}-{}", vin, file.getOriginalFilename());
        Result<BssIdDTO> result = new Result<>();
        try {
            File base = AppConfig.getAppDataDir();
            File sddb = new File(base, "sddb");
            if (sddb.exists()) {
                FileUtils.cleanDirectory(sddb);
            } else {
                sddb.mkdirs();
            }
            filePath = sddb.getAbsoluteFile() + "/" + file.getOriginalFilename();
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.ERROR, TesterErrorCodeEnum.SG00269);
        }
        try {
            try {
                InputStreamReader inputStreamReader = new InputStreamReader(file.getInputStream(), "UTF-8");
                Throwable th = null;
                OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(filePath), "UTF-8");
                Throwable th2 = null;
                try {
                    try {
                        char[] cArr = new char[DoipUtil.MAX_BYTE_ARRAY_SIZE];
                        while (true) {
                            int i = inputStreamReader.read(cArr);
                            if (i == -1) {
                                break;
                            }
                            outputStreamWriter.write(cArr, 0, i);
                        }
                        if (outputStreamWriter != null) {
                            if (0 != 0) {
                                try {
                                    outputStreamWriter.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                outputStreamWriter.close();
                            }
                        }
                        if (inputStreamReader != null) {
                            if (0 != 0) {
                                try {
                                    inputStreamReader.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                inputStreamReader.close();
                            }
                        }
                        UploadVo uploadVo = new UploadVo();
                        uploadVo.setFilePath(filePath);
                        uploadVo.setVin(vin);
                        this.em.uploadLocalBss(uploadVo);
                        File file2 = new File(filePath);
                        if (file2.exists()) {
                            file2.delete();
                        }
                        result.setCode(HttpStatus.SUCCESS);
                        result.setMsg("success");
                        log.info("------------------------DSA加载本地bss成功-------------------------------");
                        return result;
                    } catch (Throwable th5) {
                        if (outputStreamWriter != null) {
                            if (th2 != null) {
                                try {
                                    outputStreamWriter.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                outputStreamWriter.close();
                            }
                        }
                        throw th5;
                    }
                } finally {
                }
            } catch (Exception e3) {
                log.info("浏览器上传bss-保存bss失败", e3);
                throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00269));
            }
        } catch (Throwable th7) {
            if (r13 != 0) {
                if (r14 != 0) {
                    try {
                        r13.close();
                    } catch (Throwable th8) {
                        r14.addSuppressed(th8);
                    }
                } else {
                    r13.close();
                }
            }
            throw th7;
        }
    }

    @PostMapping({"uploadLocalVdn"})
    public Result<Object> b(@RequestBody UploadVo uploadVo) {
        log.info("DSA加载本地bss接口口入参----->{}", uploadVo);
        Result<Object> result = new Result<>();
        try {
            this.em.uploadLocalVdn(uploadVo);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------DSA加载本地VDN成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.ERROR, TesterErrorCodeEnum.SG00270);
        }
        return result;
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r13v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r13v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 13, insn: 0x0138: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r13 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('inputStreamReader' java.io.InputStreamReader)]) A[TRY_LEAVE], block:B:48:0x0138 */
    /* JADX WARN: Not initialized variable reg: 14, insn: 0x013d: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r14 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:50:0x013d */
    /* JADX WARN: Type inference failed for: r13v1, names: [inputStreamReader], types: [java.io.InputStreamReader] */
    /* JADX WARN: Type inference failed for: r14v0, types: [java.lang.Throwable] */
    @PostMapping({"uploadLocalVdnBrowser"})
    public Result<Object> c(@RequestParam("file") MultipartFile file, @RequestParam("vin") String vin) throws Exception {
        String filePath;
        ?? r13;
        ?? r14;
        log.info("浏览器上传bss-{}-{}", vin, file.getOriginalFilename());
        Result<Object> result = new Result<>();
        try {
            File base = AppConfig.getAppDataDir();
            File sddb = new File(base, "sddb");
            if (sddb.exists()) {
                FileUtils.cleanDirectory(sddb);
            } else {
                sddb.mkdirs();
            }
            filePath = sddb.getAbsoluteFile() + "/" + file.getOriginalFilename();
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.ERROR, TesterErrorCodeEnum.SG00269);
        }
        try {
            try {
                InputStreamReader inputStreamReader = new InputStreamReader(file.getInputStream(), "UTF-8");
                Throwable th = null;
                OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(filePath), "UTF-8");
                Throwable th2 = null;
                try {
                    try {
                        char[] cArr = new char[DoipUtil.MAX_BYTE_ARRAY_SIZE];
                        while (true) {
                            int i = inputStreamReader.read(cArr);
                            if (i == -1) {
                                break;
                            }
                            outputStreamWriter.write(cArr, 0, i);
                        }
                        if (outputStreamWriter != null) {
                            if (0 != 0) {
                                try {
                                    outputStreamWriter.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                outputStreamWriter.close();
                            }
                        }
                        if (inputStreamReader != null) {
                            if (0 != 0) {
                                try {
                                    inputStreamReader.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                inputStreamReader.close();
                            }
                        }
                        UploadVo uploadVo = new UploadVo();
                        uploadVo.setFilePath(filePath);
                        uploadVo.setVin(vin);
                        this.em.uploadLocalVdn(uploadVo);
                        File file2 = new File(filePath);
                        if (file2.exists()) {
                            file2.delete();
                        }
                        result.setCode(HttpStatus.SUCCESS);
                        result.setMsg("success");
                        log.info("------------------------DSA加载本地bss成功-------------------------------");
                        return result;
                    } catch (Throwable th5) {
                        if (outputStreamWriter != null) {
                            if (th2 != null) {
                                try {
                                    outputStreamWriter.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                outputStreamWriter.close();
                            }
                        }
                        throw th5;
                    }
                } finally {
                }
            } catch (Exception e3) {
                log.info("浏览器上传bss-保存bss失败", e3);
                throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00269));
            }
        } catch (Throwable th7) {
            if (r13 != 0) {
                if (r14 != 0) {
                    try {
                        r13.close();
                    } catch (Throwable th8) {
                        r14.addSuppressed(th8);
                    }
                } else {
                    r13.close();
                }
            }
            throw th7;
        }
    }

    @GetMapping({"getOnlineVdn"})
    public Result<Object> h(@RequestParam String vin) {
        log.info("DSA获取在线VDN----->{}", vin);
        Result<Object> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            String username = loginUser.getUsername();
            this.em.getOnlineVdn(vin, username);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------DSA获取在线VDN成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.ERROR, TesterErrorCodeEnum.SG00271);
        }
        return result;
    }

    @PostMapping({"calculating"})
    public Result<Object> c(@RequestBody UploadVo uploadVo) {
        log.info("DSA结算接口入参----->{}", uploadVo);
        Result<Object> result = new Result<>();
        try {
            List<CalculationEcuDTO> calculating = this.em.calculating(uploadVo.getVin(), uploadVo.getDirPaths());
            result.setData(calculating);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------DSA本地结算成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.ERROR, TesterErrorCodeEnum.SG00272);
        }
        return result;
    }

    @PostMapping({"startDownload"})
    public Result<Object> a(@RequestBody SoftwareDownloadDto softwareDownloadDto) {
        Result<Object> result = new Result<>();
        try {
            softwareDownloadDto.setType(2);
            this.em.download(softwareDownloadDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00226);
        }
        return result;
    }

    @PostMapping({"testerOrder"})
    public Result<Object> a(@RequestBody TesterOrderParamDTO testerOrderParamDTO) throws Exception {
        String vin;
        FdTcpClient fdTcpClient;
        Result<Object> result = new Result<>();
        try {
            log.info("testerOrder接口入参：{}", JSON.toJSONString(testerOrderParamDTO));
            vin = testerOrderParamDTO.getVin();
            fdTcpClient = this.manager.getFdTcpClientAndDsa(vin);
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00226);
        }
        if (fdTcpClient == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115));
        }
        if (testerOrderParamDTO.getStart().booleanValue()) {
            TestOrderDTO testOrderDTO = new TestOrderDTO();
            testOrderDTO.setTesterOrderParam(testerOrderParamDTO);
            this.manager.addTestOrder(vin, testOrderDTO);
        } else {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            String username = loginUser.getUsername();
            FdDsaLogger dsaLogger = fdTcpClient.getFdDsaLogger();
            if (dsaLogger == null) {
                throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00211));
            }
            TokenManager.getPool().execute(() -> {
                try {
                    this.em.testerOrder(testerOrderParamDTO, username, dsaLogger, this.manager.getTestOrder(vin));
                } catch (Exception e2) {
                    throw new RuntimeException(e2);
                }
            });
        }
        result.setCode(HttpStatus.SUCCESS);
        result.setMsg("success");
        return result;
    }

    @GetMapping({"getDsaIntegrationTestSeq"})
    public Result<Object> c(@RequestParam String vin, @RequestParam(required = false) String platform, @RequestParam(required = false) String category) {
        log.info("进入queryReloadSeqList接口，vin:{},platform:{},category:{}", new Object[]{vin, platform, category});
        Result<Object> result = new Result<>();
        FdTcpClient tcpClient = this.manager.getFdTcpClient(vin);
        try {
            result.setData(this.em.getDsaIntegrationTestSeq(vin, platform, category));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00282));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00282), e);
        } catch (Exception e2) {
            Optional.ofNullable(tcpClient).ifPresent(client -> {
                client.geTxtLogger().write(new Date(), "SoftwareReload", Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, (TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00282) + e2.getMessage()).getBytes());
            });
            GlobalException.a(e2, TesterErrorCodeEnum.SG00282);
        }
        return result;
    }
}
