package com.geely.gnds.dsa.controller;

import com.geely.gnds.dsa.dto.DsaHistoricRecordsDTO;
import com.geely.gnds.dsa.service.DsaHistoricRecordsService;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"api/v1/historicRecords"})
@RestController
/* loaded from: DsaHistoricRecordsController.class */
public class DsaHistoricRecordsController {
    private static final Logger log = LoggerFactory.getLogger(DsaHistoricRecordsController.class);

    @Autowired
    private DsaHistoricRecordsService ei;

    @GetMapping({"getList"})
    public Result<Object> getList() throws Exception {
        Result<Object> result = new Result<>();
        try {
            List<DsaHistoricRecordsDTO> list = this.ei.getList();
            result.ok(list);
            return result;
        } catch (Exception e) {
            log.error("获取历史记录列表失败", e);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00220));
        }
    }

    @GetMapping({"{id}"})
    public ResponseEntity<DsaHistoricRecordsDTO> d(@PathVariable("id") Integer id) {
        return ResponseEntity.ok(this.ei.queryById(id));
    }

    @PostMapping
    public ResponseEntity<DsaHistoricRecordsDTO> a(DsaHistoricRecordsDTO dsaHistoricRecords) {
        return ResponseEntity.ok(this.ei.insert(dsaHistoricRecords));
    }

    @PutMapping
    public ResponseEntity<DsaHistoricRecordsDTO> b(DsaHistoricRecordsDTO dsaHistoricRecords) {
        return ResponseEntity.ok(this.ei.update(dsaHistoricRecords));
    }

    @DeleteMapping
    public ResponseEntity<Boolean> e(Integer id) {
        return ResponseEntity.ok(Boolean.valueOf(this.ei.deleteById(id)));
    }
}
