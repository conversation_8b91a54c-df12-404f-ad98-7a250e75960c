package com.geely.gnds.dsa.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.dsa.component.SoftwareDownload;
import com.geely.gnds.dsa.dto.DsaSoftwareDownloadConfigDto;
import com.geely.gnds.dsa.dto.SoftwareDownloadDto;
import com.geely.gnds.dsa.dto.VbfParseDto;
import com.geely.gnds.dsa.service.DsaDtcService;
import com.geely.gnds.dsa.service.DsaParamService;
import com.geely.gnds.dsa.service.IntegrationTestService;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.dsa.service.UdsService;
import com.geely.gnds.dsa.utils.JsonUtil;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.Result;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/dsa/softwareDownload"})
@RestController
/* loaded from: SoftwareDownloadController.class */
public class SoftwareDownloadController {

    @Autowired
    private UdsService es;

    @Autowired
    private SddbService eo;

    @Autowired
    private DsaParamService et;

    @Autowired
    private DsaDtcService eu;

    @Autowired
    private IntegrationTestService em;

    @Autowired
    private Cloud cloud;
    private static final String ALL_ECU = "XX";
    private static final Logger log = LoggerFactory.getLogger(SoftwareDownloadController.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @PostMapping({"startDownload"})
    public Result<Object> a(@RequestBody SoftwareDownloadDto softwareDownloadDto) {
        Result<Object> result = new Result<>();
        try {
            softwareDownloadDto.setType(1);
            this.em.download(softwareDownloadDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00226);
        }
        return result;
    }

    @GetMapping({"abortDownload"})
    public Result<Object> j(@RequestParam String vin) {
        Result<Object> result = new Result<>();
        SoftwareDownload softwareDownload = this.manager.getSoftwareDownload(vin);
        if (softwareDownload != null) {
            softwareDownload.abort();
            result.setCode(HttpStatus.ACCEPTED);
        } else {
            result.setCode(HttpStatus.SUCCESS);
        }
        return result;
    }

    @GetMapping({"getFlushConfig"})
    public Result<Object> getFlushConfig() throws IOException {
        Result<Object> result = new Result<>();
        try {
            File config = new File(ConstantEnum.POINT, "config" + File.separator + "dsaFlushConfig.json");
            if (!config.exists()) {
                config.createNewFile();
                ObjectMapper instance = ObjectMapperUtils.getInstance();
                instance.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                DsaSoftwareDownloadConfigDto softwareDownloadConfigDto = new DsaSoftwareDownloadConfigDto();
                JsonUtil.saveJsonFile(config.getAbsolutePath(), instance.writeValueAsString(softwareDownloadConfigDto));
                result.setData(softwareDownloadConfigDto);
            } else {
                String json = FileUtils.readFileToString(config, "UTF-8");
                DsaSoftwareDownloadConfigDto softwareDownloadConfigDto2 = (DsaSoftwareDownloadConfigDto) ObjectMapperUtils.jsonStr2Clazz(json, DsaSoftwareDownloadConfigDto.class);
                softwareDownloadConfigDto2.setResetAfterDownload(true);
                softwareDownloadConfigDto2.setPreProgramming(true);
                softwareDownloadConfigDto2.setCompleteAndCompatible(true);
                softwareDownloadConfigDto2.setCheckMemory(true);
                softwareDownloadConfigDto2.setVersionCheck(false);
                softwareDownloadConfigDto2.setHvFlush(false);
                result.setData(softwareDownloadConfigDto2);
            }
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (Exception e) {
            GlobalException.a(e, TesterErrorCodeEnum.SG00226);
        }
        return result;
    }

    public static void main(String[] args) throws IOException {
        SoftwareDownloadDto softwareDownloadDto = new SoftwareDownloadDto();
        VbfParseDto vbfParseDto = new VbfParseDto();
        vbfParseDto.setFilePath("D:\\1\\8896592494A.vbf");
        vbfParseDto.setAddress("1632");
        vbfParseDto.setVbfName("8896592494A.vbf");
        vbfParseDto.setAsciiCount(1547);
        vbfParseDto.setSoftwareType("EXE");
        vbfParseDto.setSwPartNumber("8896592494");
        vbfParseDto.setFileChecksum("0C2E7564");
        vbfParseDto.setSwVersion("A");
        List<VbfParseDto> list = new ArrayList<>();
        list.add(vbfParseDto);
        softwareDownloadDto.setVbfList(list);
        softwareDownloadDto.setVin("GNDSTESTWHB900001");
        System.out.println(JSON.toJSONString(softwareDownloadDto));
        DsaSoftwareDownloadConfigDto dsaSoftwareDownloadConfigDto = new DsaSoftwareDownloadConfigDto(true, 10, true, false, false, false, true, false, false);
        System.out.println(JSON.toJSONString(dsaSoftwareDownloadConfigDto));
        new File(ConstantEnum.POINT, "config" + File.separator + "dsaFlushConfig.json");
    }
}
