package com.geely.gnds.dsa.controller;

import com.alibaba.fastjson.JSON;
import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.dsa.service.VehicleService;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.service.impl.SeqServiceImpl;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/dsa/vehicle"})
@RestController
/* loaded from: VehicleController.class */
public class VehicleController {

    @Autowired
    private VehicleService ev;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SeqServiceImpl ew;
    private static final Logger log = LoggerFactory.getLogger(VehicleController.class);

    @GetMapping({"query"})
    public Result<Object> k(@RequestParam String permissionFlag) {
        Result<Object> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            List<VehicleDto> query = this.ev.query(permissionFlag);
            log.info("执行connected/query, 行号96耗时{}毫秒", Long.valueOf(System.currentTimeMillis() - start));
            result.setData(query);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00014));
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00014), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00014);
        }
        return result;
    }

    @PutMapping({"connectTcp"})
    public Result<Object> a(@RequestBody VehicleDto vehicleDto) {
        log.info("------------------------执行车辆连接接口-------------------------------");
        log.info("车辆连接接口入参----->{}", JSON.toJSON(vehicleDto));
        Result<Object> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            SysUser user = loginUser.getUser();
            this.ev.connectTcp(vehicleDto, user);
            String username = loginUser.getUsername();
            this.ew.a(vehicleDto.getVin(), username, (Integer) 0, true);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015));
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015) + DoipAddressManager.getInstance().getIps());
        }
        return result;
    }

    @GetMapping({"checkConnectStatus"})
    public Result<Object> l(@RequestParam String vin) {
        log.info("dsa检查车辆连接状态接口入参【{}】", vin);
        Result<Object> result = new Result<>();
        try {
            result.setData(this.ev.checkConnectStatus(vin));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00208));
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00208), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00208);
        }
        return result;
    }

    @PostMapping({"disconnect"})
    public Result<Object> m(@RequestParam("vin") String vin) {
        log.info("======> 客户端开始请求api/v1/dsa/vehicle/connected下的disconnect断开车辆连接接口，入参vin：{} <======", vin);
        Result<Object> result = new Result<>();
        long start = System.currentTimeMillis();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            log.info("======> 客户端获取用户信息，耗时：{}ms <======", Long.valueOf(System.currentTimeMillis() - start));
            String username = loginUser.getUsername();
            this.ev.disconnect(username, vin);
            log.info("======> 客户端断开用户:{}，vin:{}的车辆连接，耗时：{}ms <======", new Object[]{username, vin, Long.valueOf(System.currentTimeMillis() - start)});
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00017), e);
        } catch (Exception e2) {
            GlobalException.a(e2, TesterErrorCodeEnum.SG00017);
        }
        log.info("======> 客户端请求api/v1/connected下的disconnect断开车辆连接接口结束，入参vin：{} <======", vin);
        return result;
    }
}
