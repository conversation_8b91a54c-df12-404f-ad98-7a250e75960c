package com.geely.gnds.dsa.controller;

import com.geely.gnds.dsa.dto.DsaCustomButtonDTO;
import com.geely.gnds.dsa.service.DsaCustomButtonService;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/dsa/custom/button"})
@RestController
/* loaded from: DsaCustomButtonController.class */
public class DsaCustomButtonController {

    @Resource
    private DsaCustomButtonService eg;

    @GetMapping({"/list"})
    public Result<List<DsaCustomButtonDTO>> r() {
        return new Result().ok(this.eg.queryAll());
    }

    @GetMapping({"{id}"})
    public Result<DsaCustomButtonDTO> c(@PathVariable("id") Integer id) {
        return new Result().ok(this.eg.queryById(id));
    }

    @PostMapping({"/save"})
    public Result<Boolean> a(@RequestBody DsaCustomButtonDTO dsaCustomCommand) {
        return new Result().ok(Boolean.valueOf(this.eg.insert(dsaCustomCommand)));
    }

    @PostMapping({"/update"})
    public Result<Boolean> b(@RequestBody DsaCustomButtonDTO dsaCustomCommand) {
        return new Result().ok(Boolean.valueOf(this.eg.update(dsaCustomCommand)));
    }
}
