package com.geely.gnds.dsa.controller;

import com.geely.gnds.dsa.dto.EcuInfoDTO;
import com.geely.gnds.dsa.dto.VbfParseDto;
import com.geely.gnds.dsa.service.DsaVbfService;
import com.geely.gnds.dsa.vo.GetEcuInfoVo;
import com.geely.gnds.dsa.vo.VbfUploadVo;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.util.Result;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"api/v1/vbf"})
@RestController
/* loaded from: DsaVbfController.class */
public class DsaVbfController {
    private static final Logger log = LoggerFactory.getLogger(DsaVbfController.class);

    @Autowired
    private DsaVbfService el;

    @PostMapping({"upload"})
    public Result<Object> a(@RequestBody VbfUploadVo vo) throws Exception {
        String extName;
        Result<Object> result = new Result<>();
        List<VbfParseDto> list = new ArrayList<>();
        List<String> filePaths = vo.getFilePath();
        for (String filePath : filePaths) {
            try {
                int index = filePath.lastIndexOf(ConstantEnum.POINT);
                extName = filePath.substring(index);
            } catch (Exception e) {
                log.error("vbf文件解析失败", e);
                GlobalException.a(e, TesterErrorCodeEnum.SG00224);
            }
            if (!extName.equals(ConstantEnum.VBF) && !extName.equals(ConstantEnum.BVBF)) {
                result.error(TesterErrorCodeEnum.SG00223.value());
                log.error("vbf文件格式错误:{}", filePath);
                return result;
            }
            VbfParseDto vbfParseDto = this.el.getVbfParseDto(filePath);
            list.add(vbfParseDto);
        }
        result.ok(list);
        return result;
    }

    @PostMapping({"uploadBrowser"})
    public Result<Object> c(@RequestParam("files") List<MultipartFile> files) throws Exception {
        String extName;
        Result<Object> result = new Result<>();
        List<VbfParseDto> list = new ArrayList<>();
        List<String> filePaths = d(files);
        for (String filePath : filePaths) {
            try {
                int index = filePath.lastIndexOf(ConstantEnum.POINT);
                extName = filePath.substring(index);
            } catch (Exception e) {
                log.error("vbf文件解析失败", e);
                GlobalException.a(e, TesterErrorCodeEnum.SG00224);
            }
            if (!extName.equals(ConstantEnum.VBF) && !extName.equals(ConstantEnum.BVBF)) {
                result.error(TesterErrorCodeEnum.SG00223.value());
                log.error("vbf文件格式错误:{}", filePath);
                return result;
            }
            VbfParseDto vbfParseDto = this.el.getVbfParseDto(filePath);
            list.add(vbfParseDto);
        }
        result.ok(list);
        return result;
    }

    private List<String> d(List<MultipartFile> files) throws IOException {
        List<String> list = new ArrayList<>();
        File base = AppConfig.getAppHomeDir();
        File vbf = new File(base, "vbfs-dsa");
        if (vbf.exists()) {
            FileUtils.cleanDirectory(vbf);
        } else {
            vbf.mkdirs();
        }
        for (MultipartFile file : files) {
            String fileFullName = vbf.getCanonicalPath() + "/" + file.getOriginalFilename();
            try {
                InputStream inputStream = file.getInputStream();
                Throwable th = null;
                try {
                    try {
                        FileOutputStream outputStream = new FileOutputStream(fileFullName);
                        Throwable th2 = null;
                        try {
                            try {
                                IOUtils.copy(inputStream, outputStream);
                                outputStream.flush();
                                if (outputStream != null) {
                                    if (0 != 0) {
                                        try {
                                            outputStream.close();
                                        } catch (Throwable th3) {
                                            th2.addSuppressed(th3);
                                        }
                                    } else {
                                        outputStream.close();
                                    }
                                }
                                if (inputStream != null) {
                                    if (0 != 0) {
                                        try {
                                            inputStream.close();
                                        } catch (Throwable th4) {
                                            th.addSuppressed(th4);
                                        }
                                    } else {
                                        inputStream.close();
                                    }
                                }
                                list.add(fileFullName);
                            } finally {
                            }
                        } finally {
                        }
                    } finally {
                    }
                } finally {
                }
            } catch (Exception e) {
                log.error("vbf保存失败", e);
                throw new RuntimeException(e);
            }
        }
        return list;
    }

    @PostMapping({"getEcuInfo"})
    public Result<Object> a(@RequestBody GetEcuInfoVo vo) throws Exception {
        Result<Object> result = new Result<>();
        try {
            List<EcuInfoDTO> list = this.el.getEcuInfo(vo);
            result.ok(list);
        } catch (Exception e) {
            log.error("ECU识别失败", e);
            GlobalException.a(e, TesterErrorCodeEnum.SG00225);
        }
        return result;
    }
}
