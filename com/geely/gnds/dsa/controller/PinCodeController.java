package com.geely.gnds.dsa.controller;

import com.geely.gnds.dsa.dto.PinCodeDTO;
import com.geely.gnds.dsa.dto.SaveDsaPinCodeReqDTO;
import com.geely.gnds.dsa.service.PinCodeService;
import com.geely.gnds.tester.util.Result;
import java.io.IOException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"api/v1/dsa/pinCode"})
@RestController
/* loaded from: PinCodeController.class */
public class PinCodeController {

    @Autowired
    private PinCodeService en;

    @GetMapping({"local/{vin}"})
    public Result<List<PinCodeDTO>> i(@PathVariable("vin") String vin) throws IOException {
        return new Result().ok(this.en.getLocalPinCode(vin));
    }

    @PostMapping({"save/{vin}"})
    public Result<Boolean> a(@PathVariable("vin") String vin, @RequestBody SaveDsaPinCodeReqDTO dto) throws IOException {
        return new Result().ok(this.en.savePinCode(vin, dto));
    }

    @PostMapping({"getCloud/{vin}"})
    public Result<List<PinCodeDTO>> a(@PathVariable("vin") String vin, @RequestBody List<PinCodeDTO> pinCodes) throws Exception {
        return new Result().ok(this.en.getCloudPinCode(vin, pinCodes));
    }

    @PostMapping({"clearCloud/{vin}"})
    public Result<Boolean> a(@PathVariable("vin") String vin, @RequestBody PinCodeDTO pinCode) {
        return new Result().ok(this.en.clearCloudPinCode(vin, pinCode));
    }
}
