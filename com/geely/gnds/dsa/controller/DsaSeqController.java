package com.geely.gnds.dsa.controller;

import com.geely.gnds.dsa.dto.SeqUiDto;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.dto.InitializeUiDto;
import com.geely.gnds.tester.dto.VehicleStatusDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.SeqService;
import com.geely.gnds.tester.util.Result;
import com.geely.gnds.tester.util.TesterThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"/api/dsa/v1/seq"})
@RestController
/* loaded from: DsaSeqController.class */
public class DsaSeqController {

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private SeqService ek;

    @Autowired
    private TokenService tokenService;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(DsaSeqController.class);

    @PostMapping({"init"})
    public Result<SeqUiDto> a(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("------------------------执行DSA诊断序列初始化UI接口-------------------------------");
        log.info("DSA诊断序列初始化UI接口口入参----->{}", initializeUiDto);
        Result<SeqUiDto> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            SeqUiDto seqUiDto = this.ek.initDsaSeq(initializeUiDto, loginUser);
            result.setData(seqUiDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------DSA诊断序列初始化UI成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00039);
        }
        return result;
    }

    @PostMapping({"initBrowser"})
    public Result<SeqUiDto> a(@RequestParam("seqFile") MultipartFile seqFile, @RequestParam("vin") String vin) {
        log.info("------------------------执行DSA诊断序列初始化UI接口-------------------------------");
        log.info("DSA诊断序列初始化UI接口口入参----->{}", vin);
        Result<SeqUiDto> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            SeqUiDto seqUiDto = this.ek.initDsaSeqBrowser(vin, seqFile, loginUser);
            result.setData(seqUiDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------DSA诊断序列初始化UI成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00039);
        }
        return result;
    }

    @PostMapping({"initDsaCloudSeq"})
    public Result<String> b(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("------------------------执行DSA诊断序列初始化UI接口-------------------------------");
        log.info("DSA诊断序列初始化UI接口口入参----->{}", initializeUiDto);
        Result<String> result = new Result<>();
        try {
            LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
            String seqUiDto = this.ek.initDsaCloudSeq(initializeUiDto, loginUser);
            result.setData(seqUiDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------DSA诊断序列初始化UI成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00039), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00039);
        }
        return result;
    }

    @PostMapping({"statusReadoutDsa"})
    public Result<VehicleStatusDto> c(@RequestBody InitializeUiDto initializeUiDto) {
        log.info("DSA车辆状态读取接口口入参----->{}", initializeUiDto);
        Result<VehicleStatusDto> result = new Result<>();
        try {
            VehicleStatusDto vehicleStatusDto = this.ek.statusReadoutDsa(initializeUiDto);
            result.setData(vehicleStatusDto);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
            log.info("------------------------DSA车辆状态读取成功-------------------------------");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00040));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00040), e);
        } catch (Exception e2) {
            GlobalException.a(e2, HttpStatus.CREATED, TesterErrorCodeEnum.SG00040);
        }
        return result;
    }
}
