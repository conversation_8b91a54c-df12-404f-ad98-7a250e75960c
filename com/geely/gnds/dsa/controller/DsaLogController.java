package com.geely.gnds.dsa.controller;

import com.geely.gnds.dsa.dto.DsaLogDTO;
import com.geely.gnds.dsa.service.DsaLogService;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.util.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/dsa/log"})
@RestController
/* loaded from: DsaLogController.class */
public class DsaLogController {

    @Autowired
    private DsaLogService ej;
    private static final Logger log = LoggerFactory.getLogger(DsaLogController.class);

    @PostMapping({"startLog"})
    public Result<Object> a(@RequestBody DsaLogDTO dsaLogDTO) {
        Result<Object> result = new Result<>();
        try {
            this.ej.startLog(dsaLogDTO.getVin(), dsaLogDTO.getLogPath());
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00213);
        }
        return result;
    }

    @GetMapping({"getLogPath"})
    public Result<Object> g(@RequestParam String type) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.ej.getLogPath(type));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00213);
        }
        return result;
    }

    @GetMapping({"addComment"})
    public Result<Object> c(@RequestParam String vin, @RequestParam String content) {
        Result<Object> result = new Result<>();
        try {
            this.ej.addComment(vin, content);
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00210));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00210), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00210), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00210);
        }
        return result;
    }

    @PostMapping({"startPcapLog"})
    public Result<Object> b(@RequestBody DsaLogDTO dsaLogDTO) {
        Result<Object> result = new Result<>();
        try {
            this.ej.startPcapLog(dsaLogDTO.getVin(), dsaLogDTO.getLogPath());
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00213), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00213);
        }
        return result;
    }
}
