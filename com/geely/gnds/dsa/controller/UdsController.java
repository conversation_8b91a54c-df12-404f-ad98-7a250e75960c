package com.geely.gnds.dsa.controller;

import com.geely.gnds.dsa.dto.DiaEcuSwDTO;
import com.geely.gnds.dsa.service.DsaDtcService;
import com.geely.gnds.dsa.service.DsaParamService;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.dsa.service.UdsService;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.constant.HttpStatus;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.exception.GlobalException;
import com.geely.gnds.tester.exception.UnAuthException;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.Result;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping({"/api/v1/dsa/uds"})
@RestController
/* loaded from: UdsController.class */
public class UdsController {

    @Autowired
    private UdsService es;

    @Autowired
    private SddbService eo;

    @Autowired
    private DsaParamService et;

    @Autowired
    private DsaDtcService eu;
    private static final Logger log = LoggerFactory.getLogger(UdsController.class);

    /* JADX WARN: Failed to find 'out' block for switch in B:12:0x005f. Please report as an issue. */
    @GetMapping({"sendUds"})
    public Result<Object> a(@RequestParam String vin, @RequestParam String address, @RequestParam String command, @RequestParam String diagnosticPartNumber, @RequestParam String operationType) {
        Result<Object> result = new Result<>();
        String commandReplace = command.replaceAll("\\s*", "");
        String ecuAddress = address.toUpperCase();
        try {
            switch (ecuAddress) {
                case "XX":
                    List<String> addressList = this.eo.getEcuAddressList();
                    if (!CollectionUtils.isEmpty(addressList)) {
                        for (String add : addressList) {
                            try {
                                this.es.udsData(add, commandReplace, vin);
                            } catch (Exception e) {
                                log.error("发送指令异常", e);
                            }
                        }
                    }
                    result.setCode(HttpStatus.SUCCESS);
                    result.setMsg("success");
                    break;
                case "1FFF":
                    String functionalResponse = this.es.udsData(address, commandReplace, vin);
                    Result<List<DiaEcuSwDTO>> ecuList = this.eo.getEcuList();
                    if (!CollectionUtils.isEmpty(ecuList.getData())) {
                        Map<String, String> addressDiagnosticPartNumMap = (Map) ecuList.getData().stream().collect(Collectors.toMap((v0) -> {
                            return v0.getAddress();
                        }, (v0) -> {
                            return v0.getDiagnosticPartNumber();
                        }));
                        List<Map> list = ObjectMapperUtils.jsonStr2List(functionalResponse, Map.class);
                        list.forEach(e2 -> {
                            String ecuAddress1 = (String) e2.get("ECU_address");
                            String diaPartNumber = (String) addressDiagnosticPartNumMap.get(ecuAddress1);
                            b(vin, address, diaPartNumber, commandReplace, functionalResponse);
                        });
                    }
                    result.setCode(HttpStatus.SUCCESS);
                    result.setMsg("success");
                    break;
                default:
                    String otherResponse = this.es.udsData(address, commandReplace, vin);
                    b(vin, address, diagnosticPartNumber, commandReplace, otherResponse);
                    result.setCode(HttpStatus.SUCCESS);
                    result.setMsg("success");
                    break;
            }
        } catch (UnAuthException e3) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00214));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00214), e3);
        } catch (Exception e4) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00214), e4);
            GlobalException.a(e4, TesterErrorCodeEnum.SG00214);
        }
        return result;
    }

    private void b(String vin, String address, String diaPartNumber, String commandReplace, String usdResponse) {
        try {
            if (StringUtils.isBlank(diaPartNumber)) {
                return;
            }
            if (commandReplace.startsWith("22")) {
                this.et.readParam(diaPartNumber, "22", commandReplace, usdResponse, address, vin);
            } else if (commandReplace.startsWith(Constants.S_1902)) {
                this.eu.readDtc(diaPartNumber, "19", usdResponse, address, vin);
            }
        } catch (Exception ex) {
            log.error("发送指令异常", ex);
        }
    }

    @GetMapping({"getSecurityAccess"})
    public Result<Object> b(@RequestParam String vin, @RequestParam String address, @RequestParam String command, @RequestParam String fixByte) {
        Result<Object> result = new Result<>();
        try {
            result.setData(this.es.getSecurityAccess(address, command, vin, fixByte, true));
            result.setCode(HttpStatus.SUCCESS);
            result.setMsg("success");
        } catch (UnAuthException e) {
            result.setCode(HttpStatus.UNAUTHORIZED);
            result.setMsg(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00215));
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00215), e);
        } catch (Exception e2) {
            log.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00215), e2);
            GlobalException.a(e2, TesterErrorCodeEnum.SG00215);
        }
        return result;
    }
}
