package com.geely.gnds.dsa.controller;

import com.geely.gnds.dsa.dto.DsaDiagnosticRunReqDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticRunResultDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticSeqFileDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticSeqLineDTO;
import com.geely.gnds.dsa.dto.SwitchPowerModeDto;
import com.geely.gnds.dsa.service.DsaDiagnosticService;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.Result;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"api/v1/dsa/diagnostic"})
@RestController
/* loaded from: DsaDiagnosticController.class */
public class DsaDiagnosticController {

    @Autowired
    private DsaDiagnosticService eh;

    @GetMapping({"/getSeqPath"})
    public Result<String> getSeqPath() {
        return new Result().ok(this.eh.getSeqPath());
    }

    @GetMapping({"/getSeqFileList"})
    public Result<List<String>> getSeqFileList() throws IOException {
        return new Result().ok(this.eh.getSeqFileList());
    }

    @PostMapping({"/setSeqPath"})
    public Result<Boolean> e(String filePath) {
        return new Result().ok(this.eh.setSeqPath(filePath));
    }

    @PostMapping({"/saveFile"})
    public Result<String> a(@RequestBody DsaDiagnosticSeqFileDTO fileDto) throws IOException {
        return new Result().ok(this.eh.saveSeqFile(fileDto));
    }

    @PostMapping({"/downloadFile"})
    public ResponseEntity<InputStreamResource> b(@RequestBody DsaDiagnosticSeqFileDTO fileDto) throws Exception {
        List<DsaDiagnosticSeqLineDTO> lines = fileDto.getLines();
        if (CollectionUtils.isEmpty(lines)) {
            throw new Exception(MessageUtils.getMessage("Data is empty"));
        }
        StringBuffer sb = new StringBuffer();
        for (DsaDiagnosticSeqLineDTO l : lines) {
            sb.append(l.getEcu()).append(ConstantEnum.EMPTY).append(l.getHexString()).append(StringUtils.isNotEmpty(l.getSecurityPinCode()) ? ConstantEnum.EMPTY + l.getSecurityPinCode() : "").append(";").append(ObjectUtils.isEmpty(l.getDelay()) ? "" : l.getDelay()).append(";").append(l.getNote());
            sb.append(System.getProperty("line.separator"));
        }
        String fileContent = sb.toString();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Content-Disposition", String.format("attachment; filename=\"%s\"", fileDto.getFileName()));
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        return ResponseEntity.ok().headers(headers).contentType(MediaType.parseMediaType("application/octet-stream")).body(new InputStreamResource(new ByteArrayInputStream(fileContent.getBytes(Charset.forName("UTF-8")))));
    }

    @PostMapping({"/loadFile"})
    public Result<List<DsaDiagnosticSeqLineDTO>> f(String filePath) throws IOException {
        return new Result().ok(this.eh.load(filePath));
    }

    @PostMapping({"/loadFile/browser"})
    public Result<List<DsaDiagnosticSeqLineDTO>> a(@RequestParam("file") MultipartFile file) throws IOException {
        return new Result().ok(this.eh.load(file));
    }

    @PostMapping({"/sequence/check"})
    public Result<DsaDiagnosticRunResultDTO> a(@RequestBody DsaDiagnosticRunReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getVin())) {
            throw new CustomException(MessageUtils.getMessage("Vehicle VIN number cannot be empty"));
        }
        return new Result().ok(this.eh.sequenceCheck(reqDTO.getVin(), reqDTO.getRunType(), reqDTO.getLines()));
    }

    @PostMapping({"/sequence/run"})
    public Result<DsaDiagnosticRunResultDTO> b(@RequestBody DsaDiagnosticRunReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getVin())) {
            throw new CustomException(MessageUtils.getMessage("Vehicle VIN number cannot be empty"));
        }
        return new Result().ok(this.eh.sequenceRun(reqDTO.getVin(), reqDTO.getRunType(), reqDTO.getPlatform(), reqDTO.getLines()));
    }

    @GetMapping({"/stop"})
    public Result<Boolean> b(String vin, String taskId) {
        if (StringUtils.isEmpty(vin)) {
            throw new CustomException(MessageUtils.getMessage("Vehicle VIN number cannot be empty"));
        }
        return new Result().ok(this.eh.stopRun(vin, taskId));
    }

    @PostMapping({"/switchPowerMode"})
    public Result<Boolean> a(@RequestBody SwitchPowerModeDto switchPowerModeDto) {
        this.eh.switchPowerMode(switchPowerModeDto);
        return new Result().ok(true);
    }

    @GetMapping({"/getPowerMode"})
    public Result<Object> getPowerMode() {
        return new Result().ok(this.eh.getPowerMode());
    }

    @PostMapping({"/allDid/run"})
    public Result<DsaDiagnosticRunResultDTO> c(@RequestBody DsaDiagnosticRunReqDTO reqDTO) throws Exception {
        if (StringUtils.isEmpty(reqDTO.getVin())) {
            throw new CustomException(MessageUtils.getMessage("Vehicle VIN number cannot be empty"));
        }
        return new Result().ok(this.eh.allDidRun(reqDTO.getVin(), reqDTO.getRunType(), reqDTO.getEcuAddress(), reqDTO.getPlatform()));
    }
}
