package com.geely.gnds.dsa.controller;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.dsa.dao.DsaHistoricRecordsDao;
import com.geely.gnds.dsa.dto.DiaEcuSwDTO;
import com.geely.gnds.dsa.dto.DiaRoutineIdentifierDTO;
import com.geely.gnds.dsa.dto.DiaTreeNode;
import com.geely.gnds.dsa.dto.DsaHistoricRecordsDTO;
import com.geely.gnds.dsa.enums.DsaHistoryRecordEnum;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.dsa.vo.SddbUploadVo;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.Result;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping({"api/v1/sddb"})
@RestController
/* loaded from: SddbController.class */
public class SddbController {

    @Autowired
    private SddbService eo;

    @Autowired
    private DsaHistoricRecordsDao ep;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private WebApplicationContext webApplicationContext;
    private static String eq = "sddbFilePath";
    private static String er = "sddbFileRealPath";
    private static final Logger log = LoggerFactory.getLogger(SddbController.class);

    @PostMapping({"upload"})
    public Result<Object> a(@RequestBody SddbUploadVo vo) throws Exception {
        log.info("Electron上传sddb-{}-{}", vo.getVin(), vo.getFilePath());
        Result<Object> result = new Result<>();
        File base = AppConfig.getAppDataDir();
        File sddb = new File(base, "sddb");
        if (sddb.exists()) {
            FileUtils.cleanDirectory(sddb);
        }
        List<DiaEcuSwDTO> list = b(vo);
        return result.ok(list);
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r13v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r13v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 13, insn: 0x0138: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r13 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('inputStreamReader' java.io.InputStreamReader)]) A[TRY_LEAVE], block:B:47:0x0138 */
    /* JADX WARN: Not initialized variable reg: 14, insn: 0x013d: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r14 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:49:0x013d */
    /* JADX WARN: Type inference failed for: r13v1, names: [inputStreamReader], types: [java.io.InputStreamReader] */
    /* JADX WARN: Type inference failed for: r14v0, types: [java.lang.Throwable] */
    @PostMapping({"uploadBrowser"})
    public Result<Object> d(@RequestParam("file") MultipartFile file, @RequestParam("vin") String vin) throws Exception {
        log.info("浏览器上传sddb-{}-{}", vin, file.getOriginalFilename());
        Result<Object> result = new Result<>();
        File base = AppConfig.getAppDataDir();
        File sddb = new File(base, "sddb");
        if (sddb.exists()) {
            FileUtils.cleanDirectory(sddb);
        } else {
            sddb.mkdirs();
        }
        String filePath = sddb.getAbsoluteFile() + "/" + file.getOriginalFilename();
        try {
            try {
                InputStreamReader inputStreamReader = new InputStreamReader(file.getInputStream(), "UTF-8");
                Throwable th = null;
                OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(filePath), "UTF-8");
                Throwable th2 = null;
                try {
                    try {
                        char[] cArr = new char[DoipUtil.MAX_BYTE_ARRAY_SIZE];
                        while (true) {
                            int i = inputStreamReader.read(cArr);
                            if (i == -1) {
                                break;
                            }
                            outputStreamWriter.write(cArr, 0, i);
                        }
                        if (outputStreamWriter != null) {
                            if (0 != 0) {
                                try {
                                    outputStreamWriter.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                outputStreamWriter.close();
                            }
                        }
                        if (inputStreamReader != null) {
                            if (0 != 0) {
                                try {
                                    inputStreamReader.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                inputStreamReader.close();
                            }
                        }
                        SddbUploadVo sddbUploadVo = new SddbUploadVo();
                        sddbUploadVo.setFilePath(filePath);
                        sddbUploadVo.setVin(vin);
                        return result.ok(b(sddbUploadVo));
                    } catch (Throwable th5) {
                        if (outputStreamWriter != null) {
                            if (th2 != null) {
                                try {
                                    outputStreamWriter.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                outputStreamWriter.close();
                            }
                        }
                        throw th5;
                    }
                } finally {
                }
            } catch (Exception e) {
                log.info("浏览器上传sddb-保存sddb失败", e);
                throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00218));
            }
        } finally {
        }
    }

    private List<DiaEcuSwDTO> b(SddbUploadVo vo) throws Exception {
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        String key1 = loginUser.getUsername() + "-" + eq;
        String key2 = loginUser.getUsername() + "-" + er;
        servletContext.setAttribute(key1, vo.getFilePath());
        new ArrayList();
        try {
            List<DiaEcuSwDTO> list = this.eo.importSddb(vo);
            DsaHistoricRecordsDTO historicRecords = new DsaHistoricRecordsDTO();
            historicRecords.setType(Integer.valueOf(DsaHistoryRecordEnum.SDDB.getValue()));
            historicRecords.setFileName(vo.getFilePath());
            historicRecords.setUserName(loginUser.getUsername());
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
            Date date = new Date();
            historicRecords.setCreateTime(sdf.format(date));
            DsaHistoricRecordsDTO dto = this.ep.queryBy(historicRecords);
            if (ObjectUtils.isEmpty(dto)) {
                List<DsaHistoricRecordsDTO> hisList = this.ep.getList();
                if (hisList.size() >= 10) {
                    DsaHistoricRecordsDTO recordsDto = hisList.get(hisList.size() - 1);
                    this.ep.deleteById(recordsDto.getId());
                }
                this.ep.insert(historicRecords);
            } else {
                Date d = new Date();
                dto.setCreateTime(sdf.format(d));
                this.ep.update(dto);
            }
            return list;
        } catch (Exception e) {
            servletContext.removeAttribute(key1);
            servletContext.removeAttribute(key2);
            log.error("sddb解析-sddb文件解析失败。", e);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00218));
        }
    }

    @GetMapping({"getDidList"})
    public Result<Object> d(@RequestParam("diagnosticPartNum") String diagnosticPartNum, @RequestParam("serviceId") String serviceId) throws Exception {
        Result<Object> result = new Result<>();
        try {
            if ("31".equals(serviceId)) {
                List<DiaRoutineIdentifierDTO> list = this.eo.getRoutineIdentifierList(diagnosticPartNum, serviceId);
                result.ok(list);
            } else {
                List<DiagResItemGroupDto> list2 = this.eo.getDidList(diagnosticPartNum, serviceId);
                result.ok(list2);
            }
            return result;
        } catch (Exception e) {
            log.error("sddb解析-根据诊断零件号和服务id获取数据失败", e);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00219));
        }
    }

    @GetMapping({"getEcuList"})
    public Result<List<DiaEcuSwDTO>> getEcuList() {
        Result<List<DiaEcuSwDTO>> result = this.eo.getEcuList();
        return result;
    }

    @GetMapping({"getSecurityAreaList"})
    public Result<Object> getSecurityAreaList(@RequestParam("ecuAddress") String ecuAddress, @RequestParam("ecuName") String ecuName) {
        Result<Object> result = this.eo.getSecurityAreaList(ecuAddress, ecuName);
        return result;
    }

    @GetMapping({"getDidByDiagnosisPartNumAdnDid"})
    public Result<Object> d(@RequestParam("diagnosisPartNum") String diagnosisPartNum, @RequestParam("serviceId") String serviceId, @RequestParam("identifierId") String identifierId) {
        List<DiagResItemGroupDto> dto = this.eo.getDidByDiagnosisNumAndDid(diagnosisPartNum, serviceId, Arrays.asList("480C", "C00B"));
        return new Result().ok(dto);
    }

    @GetMapping({"getElement"})
    public Result<Object> a(DiaTreeNode node) throws Exception {
        Result<Object> result = new Result<>();
        log.info("获取sddb节点-{}", JSONObject.toJSON(node));
        try {
            List<DiaTreeNode> list = this.eo.getElement(node);
            result.ok(list);
            return result;
        } catch (Exception e) {
            if (e.getMessage().equals(TesterErrorCodeEnum.SG00238.code())) {
                return result.error(TesterErrorCodeEnum.SG00238.valueByLanguage());
            }
            log.error("获取sddb节点-获取失败", e);
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00237));
        }
    }
}
