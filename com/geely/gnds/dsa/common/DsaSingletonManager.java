package com.geely.gnds.dsa.common;

import com.geely.gnds.dsa.dto.BssIdDTO;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DsaSingletonManager.class */
public class DsaSingletonManager {
    private static final Logger logger = LoggerFactory.getLogger(DsaSingletonManager.class);
    private static final DsaSingletonManager INSTANCE = new DsaSingletonManager();
    private static Map<String, List<String>> vdnMap = new ConcurrentHashMap();
    private static Map<String, BssIdDTO> bssMap = new ConcurrentHashMap();

    private DsaSingletonManager() {
    }

    public static DsaSingletonManager getInstance() {
        return INSTANCE;
    }

    public void setVdn(String vin, List<String> vdns) {
        vdnMap.put(vin, vdns);
    }

    public List<String> getVdn(String vin) {
        if (vdnMap.containsKey(vin)) {
            return vdnMap.get(vin);
        }
        return null;
    }

    public void setBss(String vin, BssIdDTO bssIdDTO) {
        bssMap.put(vin, bssIdDTO);
    }

    public BssIdDTO getBss(String vin) {
        if (bssMap.containsKey(vin)) {
            return bssMap.get(vin);
        }
        return null;
    }
}
