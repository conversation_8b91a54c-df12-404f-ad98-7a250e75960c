package com.geely.gnds.dsa.dao;

import com.geely.gnds.dsa.dto.DsaHistoricRecordsDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
/* loaded from: DsaHistoricRecordsDao.class */
public interface DsaHistoricRecordsDao {
    List<DsaHistoricRecordsDTO> getList();

    DsaHistoricRecordsDTO queryBy(DsaHistoricRecordsDTO dsaHistoricRecordsDTO);

    DsaHistoricRecordsDTO queryById(Integer num);

    int insert(DsaHistoricRecordsDTO dsaHistoricRecordsDTO);

    int insertBatch(@Param("entities") List<DsaHistoricRecordsDTO> list);

    int update(DsaHistoricRecordsDTO dsaHistoricRecordsDTO);

    int deleteById(Integer num);
}
