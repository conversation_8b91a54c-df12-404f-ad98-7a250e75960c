package com.geely.gnds.dsa.dao;

import com.geely.gnds.dsa.dto.DsaCustomButtonDTO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
/* loaded from: DsaCustomButtonDao.class */
public interface DsaCustomButtonDao {
    DsaCustomButtonDTO queryById(Integer num);

    List<DsaCustomButtonDTO> getList();

    int insert(DsaCustomButtonDTO dsaCustomButtonDTO);

    int insertBatch(@Param("entities") List<DsaCustomButtonDTO> list);

    int update(DsaCustomButtonDTO dsaCustomButtonDTO);
}
