package com.geely.gnds.dsa.component;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.dto.SoftwareDownloadDto;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.utils.JsonUtil;
import com.geely.gnds.tester.seq.IQopUdsDataQueuer;
import com.geely.gnds.tester.seq.IQopUdsProcessor;
import java.io.IOException;
import java.util.Map;

/* loaded from: SoftwareDownload.class */
public class SoftwareDownload implements IQopUdsProcessor {
    public void softwareDownload(SoftwareDownloadDto softwareDownloadDto) throws Exception {
    }

    public void writeFlushData(String type, Integer progress, String status, String swPartNumber) {
    }

    public void abort() {
    }

    public void initSoftwareDownload(String vin, FdTcpClient fdTcpClient, FdDsaLogger fdDsaLogger) {
    }

    @Override // com.geely.gnds.tester.seq.IQopUdsProcessor
    public String udsData(String inputJsonStr, IQopUdsDataQueuer queuer) throws Exception {
        return null;
    }

    public void saveJsonFile(String filePath, String content) throws IOException {
        JsonUtil.saveJsonFile(filePath, content);
    }

    @Override // com.geely.gnds.tester.seq.IQopUdsProcessor
    public String udsData(String inputJsonStr, IQopUdsDataQueuer queuer, boolean logXml) throws Exception {
        return "";
    }

    public Map<String, String> getDidReadBeforeFlushMap() {
        return null;
    }

    public boolean isAbort() {
        return false;
    }
}
