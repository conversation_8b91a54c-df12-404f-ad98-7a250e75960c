package com.geely.gnds.dsa.component;

import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.exception.CancelByUserException;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.exception.DoipRetryException;
import com.geely.gnds.doip.client.tcp.DoipMessageContanier;
import com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener;
import com.geely.gnds.doip.client.tcp.FdFunctionalAddressingReceiveListener;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.dto.CalculationEcuDTO;
import com.geely.gnds.dsa.dto.DsaSoftwareDownloadConfigDto;
import com.geely.gnds.dsa.dto.EcuDsaDto;
import com.geely.gnds.dsa.dto.EcuParallelDto;
import com.geely.gnds.dsa.dto.PinCodeDTO;
import com.geely.gnds.dsa.dto.SoftwareDownloadDto;
import com.geely.gnds.dsa.dto.VbfParseDto;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.enums.LanguageEnum;
import com.geely.gnds.dsa.log.DsaFlushData;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.socket.DsaLogWebSocket;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.IQopUdsDataQueuer;
import com.geely.gnds.tester.seq.UdsQopManager;
import com.geely.gnds.tester.seq.UdsQopThread;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.SecurityUtils;
import com.geely.gnds.tester.util.VbfParseUtils;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.websocket.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/* loaded from: SoftwareDownloadSeq_4.class */
public class SoftwareDownloadSeq_4 extends SoftwareDownload {
    private static final Logger LOG = LoggerFactory.getLogger(SoftwareDownloadSeq_4.class);
    public static final String dY = "SoftwareDownload";
    public static final String dZ = "IntegrationTest";
    private static final String FUNCTIONAL_ADDRESSING = "1FFF";
    private VbfParseUtils vbfParseUtils = VbfParseUtils.getInstance();
    private DoipUtil doipUtil = DoipUtil.getInstance();
    protected String vin = "";
    private FdDsaLogger fdDsaLogger = null;
    private boolean ea = false;
    private String eb = "";
    private FdTcpClient fdTcpClient = null;
    private Map didReadBeforeFlushMap = new HashMap();
    JSONArray ec = JSONArray.parseArray("[\"3\",\"4\"]");
    JSONArray ed = JSONArray.parseArray("[\"2\",\"5\",\"8\"]");
    JSONArray ee = JSONArray.parseArray("[\"7\",\"6\",\"C\"]");
    JSONArray ef = JSONArray.parseArray("[\"A\",\"0\",\"B\",\"1\",\"9\",\"D\",\"E\",\"F\"]");
    private AtomicInteger vbfswdlCount = new AtomicInteger();

    @Override // com.geely.gnds.dsa.component.SoftwareDownload
    public void abort() {
        this.ea = true;
    }

    @Override // com.geely.gnds.dsa.component.SoftwareDownload
    public boolean isAbort() {
        return this.ea;
    }

    private void q() throws Exception {
        if (this.ea) {
            throw new Exception(MessageUtils.getMessage("User interrupts DSA flashing"));
        }
    }

    @Override // com.geely.gnds.dsa.component.SoftwareDownload
    public void initSoftwareDownload(String vin, FdTcpClient fdTcpClient, FdDsaLogger fdDsaLogger) {
        this.vin = vin;
        this.fdTcpClient = fdTcpClient;
        this.fdDsaLogger = fdDsaLogger;
    }

    @Override // com.geely.gnds.dsa.component.SoftwareDownload
    public Map getDidReadBeforeFlushMap() {
        return this.didReadBeforeFlushMap;
    }

    public void setDidReadBeforeFlushMap(Map didReadBeforeFlushMap) {
        LOG.info("刷写设置didReadBeforeFlushMap变量的值：{}", JSON.toJSONString(didReadBeforeFlushMap));
        this.didReadBeforeFlushMap = didReadBeforeFlushMap;
    }

    @Override // com.geely.gnds.dsa.component.SoftwareDownload
    public void softwareDownload(SoftwareDownloadDto softwareDownloadDto) throws Exception {
        LOG.info("softwareDownload软件刷写入参：{}", JSON.toJSONString(softwareDownloadDto));
        DsaSoftwareDownloadConfigDto dsaSoftwareDownloadConfigDto = softwareDownloadDto.getDsaSoftwareDownloadConfig();
        File config = new File(ConstantEnum.POINT, "config" + File.separator + "dsaFlushConfig.json");
        ObjectMapper instance = ObjectMapperUtils.getInstance();
        instance.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        saveJsonFile(config.getAbsolutePath(), instance.writeValueAsString(dsaSoftwareDownloadConfigDto));
        boolean queuedRequest = dsaSoftwareDownloadConfigDto.isQueuedRequest();
        boolean hvFlush = dsaSoftwareDownloadConfigDto.isHvFlush();
        int platform = dsaSoftwareDownloadConfigDto.getPlatform();
        boolean supportQop = false;
        if (queuedRequest) {
            supportQop = true;
        }
        List<EcuDsaDto> ecus = new ArrayList();
        int type = softwareDownloadDto.getType();
        if (type == 1) {
            this.eb = dY;
            List<VbfParseDto> vbfList = softwareDownloadDto.getVbfList();
            ecus = b(vbfList);
        } else if (type == 2) {
            this.eb = dZ;
            List<CalculationEcuDTO> calculationEcus = softwareDownloadDto.getCalculationEcu();
            for (CalculationEcuDTO calculationEcuDTO : calculationEcus) {
                EcuDsaDto ecuDsaDto = new EcuDsaDto();
                ecuDsaDto.setAddress(calculationEcuDTO.getEcuAddress());
                ecuDsaDto.setVbfList(calculationEcuDTO.getTargetSoftwareNumber());
                ecus.add(ecuDsaDto);
            }
        }
        Map map = new HashMap();
        if (type == 2) {
            map = a(ecus, true, map);
            setDidReadBeforeFlushMap(map);
        }
        if (dsaSoftwareDownloadConfigDto.isPreProgramming()) {
            a(ecus);
        }
        try {
            try {
                if (ecus.size() > 0) {
                    Boolean startParallelDownload = Boolean.valueOf(dsaSoftwareDownloadConfigDto.isStartParallelDownload());
                    if (startParallelDownload != null && startParallelDownload.booleanValue()) {
                        int parallelDownload = dsaSoftwareDownloadConfigDto.getParallelDownload();
                        List<List<EcuDsaDto>> ecuLists = new ArrayList<>();
                        for (int i = 0; i < parallelDownload; i++) {
                            List<EcuDsaDto> ecuList = new ArrayList<>();
                            ecuLists.add(ecuList);
                        }
                        for (EcuDsaDto ecuDsaDto2 : ecus) {
                            String address = ecuDsaDto2.getAddress();
                            Iterator<EcuParallelDto> it = softwareDownloadDto.getEcuParallels().iterator();
                            while (true) {
                                if (it.hasNext()) {
                                    EcuParallelDto ecuParallel = it.next();
                                    String addressParallel = ecuParallel.getAddress();
                                    if (address.equals(addressParallel)) {
                                        int threadIndex = ecuParallel.getThreadIndex();
                                        List list = ecuLists.get(threadIndex - 1);
                                        list.add(ecuDsaDto2);
                                        break;
                                    }
                                }
                            }
                        }
                        a(ecus, hvFlush, platform);
                        List<List<EcuDsaDto>> ecuSblZroeOneLists = new ArrayList<>();
                        List<List<EcuDsaDto>> ecuSblLists = new ArrayList<>();
                        List<List<EcuDsaDto>> otherLists = new ArrayList<>();
                        ecuLists.stream().forEach(ecuDsaDtos -> {
                            ArrayList arrayList = new ArrayList();
                            ArrayList arrayList2 = new ArrayList();
                            ArrayList arrayList3 = new ArrayList();
                            if (!CollectionUtils.isEmpty(ecuDsaDtos)) {
                                ecuDsaDtos.stream().forEach(it2 -> {
                                    if (it2.getAddress().endsWith("01")) {
                                        if (!ObjectUtils.isEmpty(it2)) {
                                            List<VbfParseDto> vbfSblParseDtos = new ArrayList<>();
                                            List<VbfParseDto> vbfNotSblParseDtos = new ArrayList<>();
                                            List<VbfParseDto> vbfList2 = it2.getVbfList();
                                            if (!CollectionUtils.isEmpty(vbfList2)) {
                                                vbfList2.forEach(vbfParseDto -> {
                                                    if ("SBL".equals(vbfParseDto.getSwPartType())) {
                                                        vbfSblParseDtos.add(vbfParseDto);
                                                    } else {
                                                        vbfNotSblParseDtos.add(vbfParseDto);
                                                    }
                                                });
                                            }
                                            EcuDsaDto ecuDsaDto3 = new EcuDsaDto();
                                            ecuDsaDto3.setAddress(it2.getAddress());
                                            ecuDsaDto3.setVbfList(vbfSblParseDtos);
                                            arrayList.add(ecuDsaDto3);
                                            EcuDsaDto ecuDsaDto22 = new EcuDsaDto();
                                            ecuDsaDto22.setAddress(it2.getAddress());
                                            ecuDsaDto22.setVbfList(vbfNotSblParseDtos);
                                            arrayList3.add(ecuDsaDto22);
                                            return;
                                        }
                                        return;
                                    }
                                    if (!ObjectUtils.isEmpty(it2)) {
                                        List<VbfParseDto> vbfSblParseDtos2 = new ArrayList<>();
                                        List<VbfParseDto> vbfNotSblParseDtos2 = new ArrayList<>();
                                        List<VbfParseDto> vbfList3 = it2.getVbfList();
                                        if (!CollectionUtils.isEmpty(vbfList3)) {
                                            vbfList3.forEach(vbfParseDto2 -> {
                                                if ("SBL".equals(vbfParseDto2.getSwPartType())) {
                                                    vbfSblParseDtos2.add(vbfParseDto2);
                                                } else {
                                                    vbfNotSblParseDtos2.add(vbfParseDto2);
                                                }
                                            });
                                        }
                                        EcuDsaDto ecuDsaDto4 = new EcuDsaDto();
                                        ecuDsaDto4.setAddress(it2.getAddress());
                                        ecuDsaDto4.setVbfList(vbfSblParseDtos2);
                                        arrayList2.add(ecuDsaDto4);
                                        EcuDsaDto ecuDsaDto23 = new EcuDsaDto();
                                        ecuDsaDto23.setAddress(it2.getAddress());
                                        ecuDsaDto23.setVbfList(vbfNotSblParseDtos2);
                                        arrayList3.add(ecuDsaDto23);
                                    }
                                });
                            }
                            ecuSblZroeOneLists.add(arrayList);
                            ecuSblLists.add(arrayList2);
                            otherLists.add(arrayList3);
                        });
                        Set<String> securityList = new HashSet<>();
                        a(dsaSoftwareDownloadConfigDto, supportQop, parallelDownload, ecuSblZroeOneLists, securityList, false);
                        a(dsaSoftwareDownloadConfigDto, supportQop, parallelDownload, ecuSblLists, securityList, false);
                        a(dsaSoftwareDownloadConfigDto, supportQop, parallelDownload, otherLists, securityList, true);
                    } else {
                        a(ecus, hvFlush, platform);
                        a(ecus, dsaSoftwareDownloadConfigDto, supportQop, new HashSet());
                    }
                }
                try {
                    if (dsaSoftwareDownloadConfigDto.isResetAfterDownload()) {
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
                            logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send function reques:11 81"));
                        });
                        udsData(a("1FFF", "1181", 2));
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                            logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Software reset in progress, please wait for 10 seconds"));
                        });
                        Thread.sleep(10000L);
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                            logger3.write("softwareDownload", MessageUtils.getMessage("Reset ended"));
                        });
                    }
                    if (hvFlush) {
                        String ecuAddress = "1601";
                        if (platform == 3) {
                            ecuAddress = "1612";
                        }
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger4 -> {
                            logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Reqest:10 03"));
                        });
                        String udsData = udsData(a(ecuAddress, "1003", 2));
                        String res = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger5 -> {
                            logger5.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Response:" + getDsaLogCommand(res));
                            logger5.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Reqest:31 01 40 00 00"));
                        });
                        String udsData2 = udsData(a(ecuAddress, "3101400000", 2));
                        String res2 = JSONPath.eval(udsData2, "$.ECU_Response_value.Data_Value").toString();
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger6 -> {
                            logger6.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Response:" + getDsaLogCommand(res2));
                        });
                    }
                } catch (Exception e) {
                    LOG.error("发送1181失败", e);
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger7 -> {
                        logger7.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), e.getMessage());
                    });
                }
            } catch (Throwable th) {
                try {
                    if (dsaSoftwareDownloadConfigDto.isResetAfterDownload()) {
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger8 -> {
                            logger8.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send function reques:11 81"));
                        });
                        udsData(a("1FFF", "1181", 2));
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger22 -> {
                            logger22.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Software reset in progress, please wait for 10 seconds"));
                        });
                        Thread.sleep(10000L);
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger32 -> {
                            logger32.write("softwareDownload", MessageUtils.getMessage("Reset ended"));
                        });
                    }
                    if (hvFlush) {
                        String ecuAddress2 = "1601";
                        if (platform == 3) {
                            ecuAddress2 = "1612";
                        }
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger42 -> {
                            logger42.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Reqest:10 03"));
                        });
                        String udsData3 = udsData(a(ecuAddress2, "1003", 2));
                        String res3 = JSONPath.eval(udsData3, "$.ECU_Response_value.Data_Value").toString();
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger52 -> {
                            logger52.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Response:" + getDsaLogCommand(res3));
                            logger52.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Reqest:31 01 40 00 00"));
                        });
                        String udsData4 = udsData(a(ecuAddress2, "3101400000", 2));
                        String res22 = JSONPath.eval(udsData4, "$.ECU_Response_value.Data_Value").toString();
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger62 -> {
                            logger62.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Response:" + getDsaLogCommand(res22));
                        });
                    }
                } catch (Exception e2) {
                    LOG.error("发送1181失败", e2);
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger72 -> {
                        logger72.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), e2.getMessage());
                    });
                }
                throw th;
            }
        } catch (Exception e3) {
            LOG.error("DSA刷写失败", e3);
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger9 -> {
                logger9.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), e3.getMessage());
            });
            try {
                if (dsaSoftwareDownloadConfigDto.isResetAfterDownload()) {
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger82 -> {
                        logger82.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send function reques:11 81"));
                    });
                    udsData(a("1FFF", "1181", 2));
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger222 -> {
                        logger222.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Software reset in progress, please wait for 10 seconds"));
                    });
                    Thread.sleep(10000L);
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger322 -> {
                        logger322.write("softwareDownload", MessageUtils.getMessage("Reset ended"));
                    });
                }
                if (hvFlush) {
                    String ecuAddress3 = "1601";
                    if (platform == 3) {
                        ecuAddress3 = "1612";
                    }
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger422 -> {
                        logger422.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Reqest:10 03"));
                    });
                    String udsData5 = udsData(a(ecuAddress3, "1003", 2));
                    String res4 = JSONPath.eval(udsData5, "$.ECU_Response_value.Data_Value").toString();
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger522 -> {
                        logger522.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Response:" + getDsaLogCommand(res4));
                        logger522.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Reqest:31 01 40 00 00"));
                    });
                    String udsData6 = udsData(a(ecuAddress3, "3101400000", 2));
                    String res23 = JSONPath.eval(udsData6, "$.ECU_Response_value.Data_Value").toString();
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger622 -> {
                        logger622.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Response:" + getDsaLogCommand(res23));
                    });
                }
            } catch (Exception e4) {
                LOG.error("发送1181失败", e4);
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger722 -> {
                    logger722.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), e4.getMessage());
                });
            }
        }
        if (type == 2 && dsaSoftwareDownloadConfigDto.isVersionCheck()) {
            a(ecus, false, map);
        }
    }

    private void a(DsaSoftwareDownloadConfigDto dsaSoftwareDownloadConfigDto, boolean supportQop, int parallelDownload, List<List<EcuDsaDto>> ecuLists, Set<String> securityList, boolean needCompleteAndCompatible) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(parallelDownload);
        int size = ecuLists.size();
        LOG.info("开始刷写parallelDownload:{},ecuLists:{},size:{}", new Object[]{Integer.valueOf(parallelDownload), JSON.toJSONString(ecuLists), Integer.valueOf(size)});
        AtomicInteger count = new AtomicInteger(1);
        for (List<EcuDsaDto> ecuDsaDtos : ecuLists) {
            TokenManager.getPool().execute(() -> {
                try {
                    try {
                        if (!CollectionUtils.isEmpty(ecuDsaDtos)) {
                            a((List<EcuDsaDto>) ecuDsaDtos, dsaSoftwareDownloadConfigDto, supportQop, (Set<String>) securityList, needCompleteAndCompatible);
                        }
                        LOG.info("刷写完成count:{}", Integer.valueOf(count.getAndIncrement()));
                        latch.countDown();
                    } catch (Exception e) {
                        LOG.error("DSA刷写失败", e);
                        LOG.info("刷写完成count:{}", Integer.valueOf(count.getAndIncrement()));
                        latch.countDown();
                    }
                } catch (Throwable th) {
                    LOG.info("刷写完成count:{}", Integer.valueOf(count.getAndIncrement()));
                    latch.countDown();
                    throw th;
                }
            });
        }
        LOG.info("并行刷写结束");
        latch.await();
    }

    private Map a(List<EcuDsaDto> ecus, boolean afterFlush, Map afterFlushData) throws Exception {
        Map map = new HashMap();
        for (EcuDsaDto ecu : ecus) {
            q();
            String address = ecu.getAddress();
            try {
                Map res = new HashMap();
                a(ecu, "1001", map, afterFlush, afterFlushData, res);
                if (afterFlush) {
                    Thread.sleep(800L);
                }
                a(ecu, "1003", map, afterFlush, afterFlushData, res);
                udsData(a(address, "1001", 2));
                Thread.sleep(2000L);
                a(ecu, "1002", map, afterFlush, afterFlushData, res);
                if (!afterFlush) {
                    Thread.sleep(500L);
                    udsData(a(address, "1001", 2));
                    boolean flagF18c = false;
                    if (res.containsKey("1001F18C") && res.containsKey("1002F18C") && res.containsKey("1003F18C")) {
                        String s1 = res.get("1001F18C").toString();
                        String s2 = res.get("1002F18C").toString();
                        String s3 = res.get("1003F18C").toString();
                        if (s1.equals(s2) && s2.equals(s3)) {
                            flagF18c = true;
                        }
                    }
                    if (!flagF18c) {
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
                            logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), MessageUtils.getMessage("Warning: F18C information not match"));
                        });
                    }
                    if (res.containsKey("1001F1A5") && res.containsKey("1002F1A5") && res.containsKey("1003F1A5")) {
                        String s12 = res.get("1001F1A5").toString();
                        if (s12.equals(res.get("1002F1A5").toString())) {
                        }
                    }
                    boolean flagApp = false;
                    if (res.containsKey("1001F1AE") && res.containsKey("1003F1AE")) {
                        String s13 = res.get("1001F1AE").toString();
                        String s32 = res.get("1003F1AE").toString();
                        if (s13.equals(s32)) {
                            flagApp = true;
                        }
                    }
                    if (!flagApp) {
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                            logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), MessageUtils.getMessage("Warning: APP information not match"));
                        });
                    }
                    boolean flagSxdi = false;
                    if (res.containsKey("1001F1A0") && res.containsKey("1003F1A0")) {
                        String s14 = res.get("1001F1A0").toString();
                        String s33 = res.get("1003F1A0").toString();
                        if (s14.equals(s33)) {
                            flagSxdi = true;
                        }
                    }
                    if (!flagSxdi) {
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                            logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), MessageUtils.getMessage("Warning: SXDI-SWLM information not match"));
                        });
                    }
                }
            } catch (Exception e) {
                LOG.error("发送22失败：", e);
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger4 -> {
                    logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), "Warning: ecu:" + address + MessageUtils.getMessage("Version verification failed：") + e.getMessage());
                });
            }
        }
        return map;
    }

    private Map a(EcuDsaDto ecu, String mode, Map map, boolean afterFlush, Map afterFlushData, Map res) throws Exception {
        String address = ecu.getAddress();
        udsData(a(address, mode, 0));
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
            logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "ecuAddress " + address + "; mode:" + mode);
        });
        if ("1002".equals(mode)) {
            Thread.sleep(10000L);
        } else {
            Thread.sleep(500L);
        }
        String resDu = b(address, "22F1AB", "DU");
        String resHwsd = b(address, "22F1AA", "HWSD");
        if ("1002".equals(mode)) {
            String resPbl = b(address, "22F1A5", "PBL");
            String resSxbl = b(address, "22F1A1", "SXBL");
            String keyPbl = mode + address + "F1A5";
            String keySxbl = mode + address + "F1A1";
            if (afterFlush) {
                map.put(keyPbl, resPbl);
                map.put(keySxbl, resSxbl);
            } else {
                if (afterFlushData.containsKey(keyPbl)) {
                    String valuePbl = afterFlushData.get(keyPbl).toString();
                    if (!StringUtils.isNotBlank(valuePbl) || !valuePbl.equals(resPbl)) {
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                            logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), mode + "-->>");
                            logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "22F1A5");
                            logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), MessageUtils.getMessage("Warning: PBL information not match"));
                        });
                    }
                }
                if (afterFlushData.containsKey(keySxbl)) {
                    String valueSxbl = afterFlushData.get(keySxbl).toString();
                    if (!StringUtils.isNotBlank(valueSxbl) || !valueSxbl.equals(resSxbl)) {
                        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                            logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), mode + "-->>");
                            logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "22F1A1");
                            logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), MessageUtils.getMessage("Warning: SXBL information not match"));
                        });
                    }
                }
            }
        }
        String keyDu = mode + address + "F1AB";
        String keyHwsd = mode + address + "F1AA";
        if (afterFlush) {
            map.put(keyDu, resDu);
            map.put(keyHwsd, resHwsd);
        } else {
            String resF18c = b(address, "22F18C", "F18C");
            String keyf18c = mode + "F18C";
            if (StringUtils.isNotBlank(resF18c)) {
                res.put(keyf18c, resF18c);
            }
            if (!"1002".equals(mode)) {
                String resApp = b(address, "22F1AE", "APP");
                String resSxdi = b(address, "22F1A0", "SXDI-SWLM");
                String keyApp = mode + "F1AE";
                String keySxdi = mode + "F1A0";
                if (StringUtils.isNotBlank(resApp)) {
                    res.put(keyApp, resApp);
                }
                if (StringUtils.isNotBlank(resSxdi)) {
                    res.put(keySxdi, resSxdi);
                }
            }
            if (afterFlushData.containsKey(keyDu)) {
                String valueDu = afterFlushData.get(keyDu).toString();
                if (!StringUtils.isNotBlank(valueDu) || !valueDu.equals(resDu)) {
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger4 -> {
                        logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), mode + "-->>");
                        logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "22F1AB");
                        logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), MessageUtils.getMessage("Warning: DU information not match"));
                    });
                }
            }
            if (afterFlushData.containsKey(keyHwsd)) {
                String valueHwsd = afterFlushData.get(keyHwsd).toString();
                if (!StringUtils.isNotBlank(valueHwsd) || !valueHwsd.equals(resHwsd)) {
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger5 -> {
                        logger5.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), mode + "-->>");
                        logger5.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "22F1AA");
                        logger5.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), MessageUtils.getMessage("Warning: HWSD information not match"));
                    });
                }
            }
        }
        return res;
    }

    private String b(String address, String command, String name) throws Exception {
        String res = "";
        try {
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
                logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Sending Read " + name);
                logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Sending Read " + name + " Reqest:" + getDsaLogCommand(command));
            });
            String udsData = udsData(a(address, command, 2));
            res = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Sending Read " + name + " Response:" + getDsaLogCommand(res));
            });
        } catch (Exception e) {
            LOG.error("读取{}失败", name, e);
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), "Warning: Read " + name + " Fail:" + e.getMessage());
            });
        }
        return res;
    }

    private void a(List<EcuDsaDto> ecus) throws Exception {
        for (EcuDsaDto ecu : ecus) {
            q();
            try {
                String address = ecu.getAddress();
                Thread.sleep(500L);
                udsData(a(address, "1003", 0));
                Thread.sleep(500L);
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
                    logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Check Precondition"));
                    logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Check Precondition Reqest:31 01 02 06"));
                });
                String udsData = udsData(getUdsString(address, "31010206"));
                String res = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                    logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Check Precondition Response") + getDsaLogCommand(res));
                });
            } catch (Exception e) {
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                    logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_WARN.getValue(), MessageUtils.getMessage("Warning") + e.getMessage());
                });
                LOG.error("Check Precondition Fail", e);
            }
        }
    }

    private void a(List<EcuDsaDto> ecus, boolean isHvFlush, int platform) throws Exception {
        if (isHvFlush) {
            String ecuAddress = "1601";
            if (platform == 3) {
                ecuAddress = "1612";
            }
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
                logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Reqest:10 03"));
            });
            String res = JSONPath.eval(udsData(a(ecuAddress, "1003", 2)), "$.ECU_Response_value.Data_Value").toString();
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Response") + getDsaLogCommand(res));
                logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send Reqest:31 01 40 00 01"));
            });
            String udsData = udsData(a(ecuAddress, "3101400001", 2));
            String res2 = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Response") + getDsaLogCommand(res2));
            });
            String res3 = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
            if ("7101400011".equals(res3)) {
                throw new Exception(MessageUtils.getMessage("Received negative feedback") + res3);
            }
        }
        a(ecus, isHvFlush);
        q();
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger4 -> {
            logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Entering programming session, please wait for 5 seconds"));
        });
        Thread.sleep(5000L);
        q();
        a(ecus, isHvFlush);
        q();
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger5 -> {
            logger5.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Entering programming session, please wait for 0.5 seconds"));
        });
        Thread.sleep(500L);
        a(ecus, isHvFlush);
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger6 -> {
            logger6.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Entering programming session, please wait for 0.5 seconds"));
        });
        Thread.sleep(500L);
        q();
        a(ecus, isHvFlush);
        q();
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger7 -> {
            logger7.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Entering programming session, please wait for 50 seconds"));
        });
        Thread.sleep(50000L);
        q();
        a(ecus, isHvFlush);
        q();
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger8 -> {
            logger8.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Entering programming session, please wait for 3 seconds"));
        });
        Thread.sleep(3000L);
        q();
    }

    private void a(List<EcuDsaDto> ecus, boolean isHvFlush) throws Exception {
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
            logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("send function reques:10 82"));
        });
        udsData(a("1FFF", "1082", 0));
    }

    private void a(List<EcuDsaDto> ecus, DsaSoftwareDownloadConfigDto dsaSoftwareDownloadConfigDto, boolean supportQop, Set<String> securityList) {
        a(ecus, dsaSoftwareDownloadConfigDto, supportQop, securityList, true);
    }

    /* JADX WARN: Finally extract failed */
    /* JADX WARN: Removed duplicated region for block: B:133:0x0420 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:49:0x043e A[Catch: Exception -> 0x0454, all -> 0x06a9, Exception -> 0x070a, all -> 0x0759, TRY_ENTER, TRY_LEAVE, TryCatch #0 {Exception -> 0x070a, blocks: (B:3:0x0085, B:5:0x00ae, B:6:0x00ca, B:7:0x00f8, B:9:0x0103, B:15:0x0114, B:16:0x0127, B:17:0x019e, B:19:0x01a8, B:20:0x01d2, B:22:0x01e4, B:25:0x0207, B:26:0x0210, B:28:0x021a, B:29:0x028e, B:31:0x0344, B:34:0x0355, B:36:0x0363, B:40:0x0388, B:42:0x038f, B:44:0x03d3, B:45:0x03f1, B:46:0x0409, B:48:0x0420, B:49:0x043e, B:56:0x04c7, B:39:0x0387, B:51:0x0456, B:53:0x045e, B:55:0x04ac, B:54:0x0484, B:59:0x04f9, B:61:0x0500, B:62:0x0509, B:64:0x0513, B:66:0x052a, B:68:0x0545, B:70:0x0558, B:71:0x0564, B:73:0x056e, B:75:0x0585, B:78:0x05a3, B:79:0x05ac, B:81:0x05b6, B:83:0x05cd, B:90:0x063d, B:92:0x0697, B:99:0x06c2, B:86:0x0603, B:87:0x060c, B:89:0x0616, B:96:0x06b0, B:98:0x06c1), top: B:119:0x0085, outer: #2 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    private void a(java.util.List<com.geely.gnds.dsa.dto.EcuDsaDto> r15, com.geely.gnds.dsa.dto.DsaSoftwareDownloadConfigDto r16, boolean r17, java.util.Set<java.lang.String> r18, boolean r19) {
        /*
            Method dump skipped, instructions count: 1950
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.dsa.component.SoftwareDownloadSeq_4.a(java.util.List, com.geely.gnds.dsa.dto.DsaSoftwareDownloadConfigDto, boolean, java.util.Set, boolean):void");
    }

    private boolean a(EcuDsaDto ecuDsaDto) throws IOException {
        boolean res = true;
        for (VbfParseDto vbfParseDto : ecuDsaDto.getVbfList()) {
            String address = vbfParseDto.getAddress();
            String filePath = vbfParseDto.getFilePath();
            a(DsaLogTypeEnum.FLUSH_STATUS.getValue(), (Integer) null, LanguageEnum.READING_FILE.valueByLanguage(), vbfParseDto.getSwPartNumber(), address);
            boolean checkVbfCrc = a(filePath, vbfParseDto.getAsciiCount(), vbfParseDto.getFileChecksum());
            if (checkVbfCrc) {
                a(DsaLogTypeEnum.FLUSH_STATUS.getValue(), (Integer) null, LanguageEnum.READING_COMPLETE.valueByLanguage(), vbfParseDto.getSwPartNumber(), address);
            } else {
                b(DsaLogTypeEnum.FLUSH_STATUS.getValue(), null, LanguageEnum.VERIFICATION_FAILED.valueByLanguage(), vbfParseDto.getSwPartNumber(), address);
                res = false;
            }
        }
        return res;
    }

    private boolean a(Map<String, Object> vbfHeader, String ecuAddress, String vbf, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, String signatureFlag, boolean firstCheckMemoryWithDevSignature) throws Exception {
        String swSignatureDev = "";
        String swSignature = "";
        if (vbfHeader.containsKey("sw_signature_dev") && vbfHeader.get("sw_signature_dev") != null) {
            swSignatureDev = vbfHeader.get("sw_signature_dev").toString();
        }
        if (vbfHeader.containsKey("sw_signature") && vbfHeader.get("sw_signature") != null) {
            swSignature = vbfHeader.get("sw_signature").toString();
        }
        if ("".equals(swSignature) && "".equals(swSignatureDev)) {
            return true;
        }
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger -> {
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "Check Memory.");
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "===============================================");
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "");
        });
        if (!firstCheckMemoryWithDevSignature) {
            Boolean checkFlag = Boolean.valueOf(a(ecuAddress, swSignature, pClientVehicleMax, p2ServerMax, p4ServerMax, false));
            if (!checkFlag.booleanValue() && !"".equals(swSignatureDev)) {
                return a(ecuAddress, vbf, swSignatureDev, pClientVehicleMax, p2ServerMax, p4ServerMax, true);
            }
            return true;
        }
        Boolean checkFlag2 = Boolean.valueOf(a(ecuAddress, swSignatureDev, pClientVehicleMax, p2ServerMax, p4ServerMax, true));
        if (!checkFlag2.booleanValue() && !"".equals(swSignature)) {
            return a(ecuAddress, vbf, swSignature, pClientVehicleMax, p2ServerMax, p4ServerMax, false);
        }
        return true;
    }

    private boolean a(Map<String, Object> vbfHeader, String ecuAddress, String vbf, String pClientVehicleMax, String p2ServerMax, String p4ServerMax) throws Exception {
        boolean successFlag = true;
        String call = "";
        if (vbfHeader.containsKey("call") && vbfHeader.get("call") != null) {
            call = vbfHeader.get("call").toString();
        }
        if (!"".equals(call)) {
            if (call.length() < ConstantEnum.EIGHT.intValue()) {
                while (call.length() < ConstantEnum.EIGHT.intValue()) {
                    call = "00" + call;
                }
            }
            String callUds = "31010301" + call;
            String callMap = getUdsString(ecuAddress, callUds, pClientVehicleMax, p2ServerMax, p4ServerMax);
            String s2 = udsData(callMap);
            String res = JSONPath.eval(s2, "$.ECU_Response_value.Data_Value").toString();
            String[] splits = res.split("71010301");
            if (splits.length > 1) {
                String split = splits[1].substring(1, 2);
                if (!ConstantEnum.ZERO.toString().equals(split)) {
                    successFlag = false;
                    LOG.error("Request Download;ECU: {}; File: {}; UDS Send={}; UDS Response = {}", new Object[]{ecuAddress, vbf, callMap, s2});
                }
            } else {
                LOG.error("Request Download;ECU: {}; File: {}; UDS Send={}; UDS Response = {}", new Object[]{ecuAddress, vbf, callMap, s2});
            }
        }
        return successFlag;
    }

    private boolean a(String ecuAddress, String vbf, String swSignature, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, boolean isDev) throws Exception {
        boolean successFlag = true;
        LOG.info("第二次验签开始");
        String checkMemeroyUds = "31010212" + swSignature;
        String eraseMap = getUdsString(ecuAddress, checkMemeroyUds, pClientVehicleMax, p2ServerMax, p4ServerMax);
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger -> {
            if (isDev) {
                fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("check memory by using sw_signature_dev"));
            } else {
                fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("check memory by using sw_signature"));
            }
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Check Memory"));
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Check Memory Reqest") + getDsaLogCommand(checkMemeroyUds));
        });
        String checkMemeoryRespond = udsData(eraseMap);
        String res = JSONPath.eval(checkMemeoryRespond, "$.ECU_Response_value.Data_Value").toString();
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger2 -> {
            fdDsaLogger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Check Memory Response") + getDsaLogCommand(res));
        });
        if (!res.contains("710102121000")) {
            successFlag = false;
            LOG.error("Request Download;ECU: {}; File: {}; UDS Send={}; UDS Response = {}", new Object[]{ecuAddress, vbf, eraseMap, res});
        }
        boolean finalSuccessFlag = successFlag;
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger3 -> {
            if (finalSuccessFlag) {
                fdDsaLogger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("check memory OK"));
            } else {
                fdDsaLogger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Check Memory Fail"));
            }
        });
        return successFlag;
    }

    private boolean a(String ecuAddress, String swSignatureDev, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, boolean isDev) throws Exception {
        boolean checkFlag = false;
        try {
            if (!"".equals(swSignatureDev)) {
                LOG.info("第一次验签开始");
                String checkMemeroyuds = "31010212" + swSignatureDev;
                Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger -> {
                    if (isDev) {
                        fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("check memory by using sw_signature_dev"));
                    } else {
                        fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("check memory by using sw_signature"));
                    }
                    fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Check Memory"));
                    fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Check Memory Reqest") + getDsaLogCommand(checkMemeroyuds));
                });
                String checkMemeoryRespond = udsData(getUdsString(ecuAddress, checkMemeroyuds, pClientVehicleMax, p2ServerMax, p4ServerMax));
                String res = JSONPath.eval(checkMemeoryRespond, "$.ECU_Response_value.Data_Value").toString();
                Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger2 -> {
                    fdDsaLogger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Check Memory Response") + getDsaLogCommand(res));
                });
                if (res.contains("710102121000")) {
                    checkFlag = true;
                }
            }
        } catch (Exception e) {
            com.geely.gnds.tester.seq.Logger.error("第一次验签失败", e);
        }
        boolean finalCheckFlag = checkFlag;
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger3 -> {
            if (finalCheckFlag) {
                fdDsaLogger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("check memory OK"));
            } else {
                fdDsaLogger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Check Memory Fail"));
            }
        });
        return checkFlag;
    }

    private boolean a(String ecuAddress, String pClientVehicleMax, String p2ServerMax, String p4ServerMax) {
        boolean success = true;
        String res = "";
        try {
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
                logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("CheckCompleteAndCompatible"));
                logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("CheckCompleteAndCompatible Reqest") + getDsaLogCommand("31010205"));
            });
            String udsData = udsData(getUdsString(ecuAddress, "31010205", pClientVehicleMax, p2ServerMax, p4ServerMax));
            res = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("CheckCompleteAndCompatible Response") + getDsaLogCommand(res));
                logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("CheckCompleteAndCompatible ok"));
            });
            if (res.length() >= ConstantEnum.EIGHT.intValue() && !"710102051000000000".equals(res.substring(0, 18))) {
                LOG.info("31010205收到负相应");
                success = false;
            }
        } catch (Exception e) {
            success = false;
            TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(e.getMessage());
            if (enumByCodeOrStr == null) {
                enumByCodeOrStr = TesterErrorCodeEnum.SG00152;
            }
            TesterErrorCodeEnum finalEnumByCodeOrStr = enumByCodeOrStr;
            String finalRes = res;
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), TesterErrorCodeEnum.formatMsg(finalEnumByCodeOrStr) + "; 收到回复:" + finalRes);
            });
        }
        return success;
    }

    private String getResult(Boolean successFlag, List<Object> dataValue) {
        String res = "Fail";
        if (successFlag.booleanValue()) {
            res = "Successful";
        }
        Map map = new HashMap(2);
        HashMap map2 = new HashMap(2);
        map2.put("Data_Type", ConstantEnum.STRING);
        map2.put("Data_Value", res);
        map.put("SWDL_result", map2);
        HashMap map3 = new HashMap(2);
        map3.put("Data_Type", ConstantEnum.LIST);
        map3.put("Data_Value", dataValue);
        map.put("Error_info", map3);
        String result = JSON.toJSONString(map);
        LOG.info("Software Download Complete: " + result);
        return result;
    }

    private boolean checkFixByte(String fixByte, String platform) {
        if (ConstantEnum.GEEA2.equals(platform)) {
            if (fixByte.length() == 10) {
                return true;
            }
            return false;
        }
        if (ConstantEnum.GEEA3.equals(platform) && fixByte.length() == 32) {
            return true;
        }
        return false;
    }

    public boolean a(String ecuAddress, List<String> fixBytes, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, boolean isCloudPinCode, String platform) throws Exception {
        String fixByte;
        String seed;
        String platform2;
        boolean checkFixByte;
        boolean succes = false;
        LOG.info("安全算法入参：{},{}", fixBytes, platform);
        try {
            if (StringUtils.isBlank(platform)) {
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
                    logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Platform attribute is empty"));
                });
            }
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("enter Programing Session"));
                logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("enter Programing Session Reqest:10 02"));
            });
            String data = udsData(getUdsString(ecuAddress, "1002", pClientVehicleMax, p2ServerMax, p4ServerMax));
            String sub = JSONPath.eval(data, "$.ECU_Response_value.Data_Value").toString();
            Optional.ofNullable(this.fdDsaLogger).ifPresent(logger3 -> {
                logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("enter Programing Session Response") + getDsaLogCommandHex(sub));
                logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("enter Programing Session OK"));
                logger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Condition fulfilled to enter program mode received for Ecu"));
            });
            Thread.sleep(6000L);
            int i = 0;
            while (true) {
                if (i >= fixBytes.size()) {
                    break;
                }
                try {
                    fixByte = fixBytes.get(i);
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger4 -> {
                        if (isCloudPinCode) {
                            String start = fixByte.substring(0, 2);
                            String end = fixByte.substring(fixByte.length() - 2);
                            logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "PINCODE:" + start + "******" + end);
                        } else {
                            logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "PINCODE:" + getDsaLogCommandHex(fixByte));
                        }
                        logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Unlock ECU"));
                        logger4.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Unlock ECU Reqest:27 01"));
                    });
                    String fixByteCopy = fixByte;
                    if (fixByte.length() > 4) {
                        fixByteCopy = fixByte.substring(0, 2) + "******" + fixByte.substring(fixByte.length() - 2);
                    }
                    LOG.info("安全算法开始使用fixByte：{}", fixByteCopy);
                    if (i == 2) {
                        Thread.sleep(15000L);
                    }
                    String udsString = getUdsString(ecuAddress, "2701", pClientVehicleMax, p2ServerMax, p4ServerMax);
                    String udsData = udsData(udsString);
                    seed = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString().substring(4);
                    String finalSeed = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger5 -> {
                        logger5.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Unlock ECU Response") + getDsaLogCommand(finalSeed));
                    });
                    if (seed.length() == 6) {
                        platform2 = ConstantEnum.GEEA2;
                    } else if (seed.length() == 32) {
                        platform2 = ConstantEnum.GEEA3;
                    } else {
                        platform2 = "";
                    }
                    checkFixByte = checkFixByte(fixByte, platform2);
                } catch (Exception e) {
                    if (i == fixBytes.size() - 1) {
                        throw e;
                    }
                }
                if (!checkFixByte) {
                    Optional.ofNullable(this.fdDsaLogger).ifPresent(logger6 -> {
                        logger6.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), MessageUtils.getMessage("pincode length doesn’t match current vehicle platform, please change vehicle platform"));
                    });
                    return false;
                }
                if ("000000".equals(seed)) {
                    succes = true;
                    break;
                }
                String securityKey = "";
                if (ConstantEnum.GEEA2.equals(platform2)) {
                    securityKey = SecurityUtils.securityAccess(fixByte, seed);
                } else if (ConstantEnum.GEEA3.equals(platform2)) {
                    securityKey = SecurityUtils.cmac(fixByte, seed);
                }
                String dllOutput = "2702" + securityKey;
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger7 -> {
                    logger7.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Unlock ECU"));
                    logger7.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Unlock ECU Reqest") + getDsaLogCommand(dllOutput));
                });
                String s = udsData(getUdsString(ecuAddress, dllOutput, pClientVehicleMax, p2ServerMax, p4ServerMax));
                String key = JSONPath.eval(s, "$.ECU_Response_value.Data_Value").toString();
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger8 -> {
                    logger8.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Unlock ECU Response") + getDsaLogCommand(key));
                });
                if ("7F2735".contains(key) || "7F2736".contains(key) || "7F2737".contains(key)) {
                    i++;
                } else {
                    succes = true;
                    break;
                }
            }
        } catch (Exception e2) {
            succes = false;
        }
        boolean finalSucces = succes;
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger9 -> {
            if (finalSucces) {
                logger9.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("ECU unlock OK"));
            } else {
                logger9.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("ECU unlock Fail"));
            }
        });
        return succes;
    }

    public void a(Object ecuNames, EcuDsaDto ecuDto) {
        try {
            LOG.info("swdlSortEcu排序入参ECU列表：{}", ecuDto);
            LOG.info("swdlSortEcu排序入参ECU名称列表：{}", ecuNames);
            JSONArray names = new JSONArray();
            if (ecuNames instanceof JSONArray) {
                names = (JSONArray) ecuNames;
            } else if (ecuNames instanceof String) {
                names = JSONObject.parseArray((String) ecuNames);
            }
            List<VbfParseDto> softwaresSort = new ArrayList<>();
            List<VbfParseDto> softwares = ecuDto.getVbfList();
            for (VbfParseDto softwareDto : softwares) {
                String softwareType = softwareDto.getSoftwareType();
                Iterator it = names.iterator();
                while (true) {
                    if (it.hasNext()) {
                        Object obj = it.next();
                        String type = (String) obj;
                        if (softwareType.startsWith(type)) {
                            softwaresSort.add(softwareDto);
                            break;
                        }
                    }
                }
            }
            for (VbfParseDto softwareDto2 : softwares) {
                if (!softwaresSort.contains(softwareDto2)) {
                    softwaresSort.add(softwareDto2);
                }
            }
            ecuDto.setVbfList(softwaresSort);
            LOG.info("swdlSortEcu排序后的ECU列表：{}", ecuDto);
        } catch (Exception e) {
        }
    }

    private void a(String ecuAddress, List<List<String>> erase, String swPartNumber, String p4ServerMax) throws Exception {
        if (!CollectionUtils.isEmpty(erase)) {
            a(DsaLogTypeEnum.FLUSH_STATUS.getValue(), (Integer) null, LanguageEnum.LANGUAGE_ENUM.valueByLanguage(), swPartNumber, ecuAddress);
            for (List list : erase) {
                String eraseUds = "3101FF00" + ((Object) list.get(0)) + ((Object) list.get(1));
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
                    logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Enter EraseMemory"));
                    logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Enter EraseMemory Reqest") + getDsaLogCommand(eraseUds));
                });
                String udsData = udsData(getUdsString(ecuAddress, eraseUds));
                Optional.ofNullable(this.fdDsaLogger).ifPresent(logger2 -> {
                    String sub = JSONPath.eval(udsData, "$.ECU_Response_value.Data_Value").toString();
                    logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Enter EraseMemory Response") + getDsaLogCommand(sub));
                    logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "P4Client:" + p4ServerMax);
                    logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "earse ok");
                });
            }
        }
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r33v2 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r33v2 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r34v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r34v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 33, insn: 0x034f: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r33 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('reader' java.io.FileInputStream)]) A[TRY_LEAVE], block:B:55:0x034f */
    /* JADX WARN: Not initialized variable reg: 34, insn: 0x0354: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r34 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:57:0x0354 */
    /* JADX WARN: Type inference failed for: r33v2, names: [reader], types: [java.io.FileInputStream] */
    /* JADX WARN: Type inference failed for: r34v0, types: [java.lang.Throwable] */
    public void a(int asciiCount, String filePath, String dataFormatIdentifier, String ecuAddress, long vbfSize, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, Map size, String swPartNumber, UdsQopManager qop) throws Exception {
        long downloadSize = 0;
        long updateSize = vbfSize / 100;
        long updateDownloadSize = 0;
        File file = new File(filePath);
        try {
            try {
                FileInputStream fileInputStream = new FileInputStream(file);
                Throwable th = null;
                BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
                Throwable th2 = null;
                try {
                    try {
                        long j = asciiCount;
                        byte[] bArr = new byte[4];
                        byte[] bArr2 = new byte[4];
                        byte[] bArr3 = new byte[2];
                        bufferedInputStream.read(new byte[asciiCount], 0, asciiCount);
                        int i = 0;
                        a(DsaLogTypeEnum.FLUSH_Progress.getValue(), (Integer) (-1), (String) null, swPartNumber, ecuAddress);
                        while (j < fileInputStream.getChannel().size()) {
                            i++;
                            bufferedInputStream.read(bArr, 0, 4);
                            String strBytesToHexString2 = this.doipUtil.bytesToHexString2(bArr, bArr.length);
                            bufferedInputStream.read(bArr2, 0, 4);
                            long j2 = j + 4 + 4;
                            long j3 = Long.parseLong(this.doipUtil.bytesToHexString2(bArr2, bArr2.length), 16);
                            a(DsaLogTypeEnum.FLUSH_STATUS.getValue(), (Integer) null, LanguageEnum.START_DOWNLOADING_SOFTWARE.valueByLanguage(), swPartNumber, ecuAddress);
                            int iA = a(filePath, dataFormatIdentifier, ecuAddress, bArr2, i, strBytesToHexString2, j3);
                            long j4 = 0;
                            byte[] bArr4 = new byte[iA];
                            int i2 = 1;
                            a(DsaLogTypeEnum.FLUSH_STATUS.getValue(), (Integer) null, LanguageEnum.SOFTWARE_DOWNLOAD.valueByLanguage(), swPartNumber, ecuAddress);
                            while (j3 > 0) {
                                String str = String.format("%02x", Integer.valueOf(i2));
                                if (j3 < iA) {
                                    int i3 = (int) j3;
                                    byte[] bArr5 = new byte[i3];
                                    bufferedInputStream.read(bArr5, 0, i3);
                                    a(getUdsString(ecuAddress, "36" + str + bytesToHexString2(bArr5, bArr5.length), pClientVehicleMax, p2ServerMax, p4ServerMax), qop);
                                    downloadSize += j3;
                                    size.put("downloadSize", Long.valueOf(downloadSize));
                                    updateDownloadSize = a(vbfSize, downloadSize, updateSize, updateDownloadSize, i3, swPartNumber, ecuAddress);
                                    j4 += j3;
                                    j3 = 0;
                                } else {
                                    bufferedInputStream.read(bArr4, 0, iA);
                                    a(getUdsString(ecuAddress, "36" + str + bytesToHexString2(bArr4, bArr4.length), pClientVehicleMax, p2ServerMax, p4ServerMax), qop);
                                    downloadSize += iA;
                                    size.put("downloadSize", Long.valueOf(downloadSize));
                                    updateDownloadSize = a(vbfSize, downloadSize, updateSize, updateDownloadSize, iA, swPartNumber, ecuAddress);
                                    j4 += iA;
                                    j3 -= iA;
                                }
                                i2 = i2 == 255 ? 0 : i2 + 1;
                            }
                            Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger -> {
                                fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Downloading DataBlock") + " OK: " + MessageUtils.getMessage("startaddress") + "=0x" + strBytesToHexString2 + " Length=" + j3);
                                fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("TransferExit Reqest:37"));
                            });
                            long jA = a(filePath, ecuAddress, j2, i, j3, pClientVehicleMax, p2ServerMax, p4ServerMax, qop);
                            a(DsaLogTypeEnum.FLUSH_STATUS.getValue(), (Integer) null, LanguageEnum.TRANSMISSION_COMPLETION.valueByLanguage(), swPartNumber, ecuAddress);
                            bufferedInputStream.read(bArr3, 0, 2);
                            j = jA + 2;
                        }
                        a(DsaLogTypeEnum.FLUSH_Progress.getValue(), (Integer) 100, "", swPartNumber, ecuAddress);
                        if (bufferedInputStream != null) {
                            if (0 != 0) {
                                try {
                                    bufferedInputStream.close();
                                } catch (Throwable th3) {
                                    th2.addSuppressed(th3);
                                }
                            } else {
                                bufferedInputStream.close();
                            }
                        }
                        if (fileInputStream != null) {
                            if (0 != 0) {
                                try {
                                    fileInputStream.close();
                                } catch (Throwable th4) {
                                    th.addSuppressed(th4);
                                }
                            } else {
                                fileInputStream.close();
                            }
                        }
                    } catch (Throwable th5) {
                        if (bufferedInputStream != null) {
                            if (th2 != null) {
                                try {
                                    bufferedInputStream.close();
                                } catch (Throwable th6) {
                                    th2.addSuppressed(th6);
                                }
                            } else {
                                bufferedInputStream.close();
                            }
                        }
                        throw th5;
                    }
                } finally {
                }
            } catch (DoipException e) {
                throw e;
            } catch (IOException e2) {
                LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00071), e2);
                throw e2;
            }
        } finally {
        }
    }

    private String bytesToHexString2(byte[] bytes, int count) {
        char[] hexArray = "0123456789ABCDEF".toCharArray();
        if (bytes.length == 0 || count == 0) {
            return "";
        }
        if (bytes.length < count) {
            count = bytes.length;
        }
        char[] hexChars = new char[count * 2];
        for (int j = 0; j < count; j++) {
            int v = bytes[j] & 255;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[(j * 2) + 1] = hexArray[v & 15];
        }
        return new String(hexChars);
    }

    private int a(String name, String dataFormatIdentifier, String ecuAddress, byte[] blockLengh, int j, String startAdd, long bl) throws Exception {
        String logger = "Block_DL;File: " + name + ";block:" + j + ";Start_Add:" + startAdd + ";Length:" + bl;
        LOG.info(logger);
        String startTransUds = "34" + dataFormatIdentifier + "44" + startAdd + this.doipUtil.bytesToHexString2(blockLengh, blockLengh.length);
        LOG.info("发送34指令：" + startTransUds);
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger -> {
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Send RequestDownload"));
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("RequestDownload Reqest") + getDsaLogCommand(startTransUds));
        });
        String s = udsData(getUdsString(ecuAddress, startTransUds));
        String maxNumberOfBlockLength = JSONPath.eval(s, "$.ECU_Response_value.Data_Value").toString().substring(4);
        String s1 = JSONPath.eval(s, "$.ECU_Response_value.Data_Value").toString();
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger2 -> {
            fdDsaLogger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("RequestDownload Response") + getDsaLogCommand(s1));
        });
        int maxLength = new BigInteger(maxNumberOfBlockLength, 16).intValue();
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger3 -> {
            fdDsaLogger3.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("maxNumberOfBlockLength") + "=" + maxLength);
        });
        return maxLength - 2;
    }

    private long a(float vbfTotalSize, long downloadSize, long updateSize, long updateDownloadSize, int bufLen, String swPartNumber, String ecuAddress) {
        long updateDownloadSize2 = updateDownloadSize + bufLen;
        if (updateDownloadSize2 > updateSize) {
            updateDownloadSize2 = 0;
            float progress = (downloadSize / vbfTotalSize) * 100.0f;
            if (progress >= 100) {
                progress = 100.0f;
            }
            int progressInt = StrictMath.round(progress);
            a(DsaLogTypeEnum.FLUSH_Progress.getValue(), Integer.valueOf(progressInt), "", swPartNumber, ecuAddress);
        }
        return updateDownloadSize2;
    }

    private long a(String name, String ecuAddress, long index, int j, long bl, String pClientVehicleMax, String p2ServerMax, String p4ServerMax, UdsQopManager qop) throws Exception {
        LOG.info("发送37开始");
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger -> {
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Send TransferExit"));
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("TransferExit Reqest:37"));
        });
        if (qop != null && qop.isSupportQop()) {
            qop.waitQopComplete();
        }
        String s = udsData(getUdsString(ecuAddress, "37", pClientVehicleMax, p2ServerMax, p4ServerMax));
        String res = "";
        if (StringUtils.isNotBlank(s)) {
            Object eval = JSONPath.eval(s, "$.ECU_Response_value.Data_Value");
            res = eval.toString();
        }
        String finalRes = res;
        Optional.ofNullable(this.fdDsaLogger).ifPresent(fdDsaLogger2 -> {
            fdDsaLogger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("TransferExit Response") + getDsaLogCommand(finalRes));
        });
        long index2 = index + bl;
        String logger = "第" + j + "Block读取结束;File: " + name;
        LOG.info(logger);
        return index2;
    }

    public int byteArrayToInt(byte[] bytes) {
        int value = 0;
        for (int i = 0; i < ConstantEnum.FORE.intValue(); i++) {
            int shift = (3 - i) * 8;
            value += (bytes[i] & 255) << shift;
        }
        return value;
    }

    public String udsData(String inputJsonStr) throws Exception {
        return udsData(inputJsonStr, null);
    }

    @Override // com.geely.gnds.dsa.component.SoftwareDownload, com.geely.gnds.tester.seq.IQopUdsProcessor
    public String udsData(String inputJsonStr, IQopUdsDataQueuer queuer) throws Exception {
        JSONObject inputObj = JSONObject.parseObject(inputJsonStr);
        String doipInstructionCode = inputObj.getString("Data");
        String udsData = doipInstructionCode;
        if (udsData.length() > 20) {
            udsData = udsData.substring(0, 20);
        }
        LOG.info("线程【{}-{}】发送udsData：【{}】", new Object[]{Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName(), udsData});
        String targetAddress = inputObj.getString("Target_address");
        int errorHandlingType = 2;
        String type = inputObj.getString("ErrorHandlingType");
        int retries = Integer.parseInt(inputObj.getString("Retries"));
        int delay = Integer.parseInt(inputObj.getString("Delay"));
        if (StringUtils.isNotBlank(type)) {
            errorHandlingType = Integer.parseInt(type);
        }
        int pClientVehicleMax = 8000;
        int p2ServerMax = 2000;
        int p4ServerMax = 1000000;
        String pClient2 = inputObj.getString("P_Client2_VehicleMax");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(pClient2)) {
            pClientVehicleMax = Integer.parseInt(pClient2);
        }
        String p2Max = inputObj.getString("P2ServerMax");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(p2Max)) {
            p2ServerMax = Integer.parseInt(p2Max);
        }
        String p4Max = inputObj.getString("P4ServerMax");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(p4Max)) {
            p4ServerMax = Integer.parseInt(p4Max);
        }
        int targetAddressInt = this.doipUtil.hexString2Int(targetAddress);
        boolean needRetries = true;
        String send2 = doipInstructionCode.length() > 9 ? doipInstructionCode.substring(0, 10) : doipInstructionCode;
        while (true) {
            if (retries >= 0 || needRetries) {
                needRetries = false;
                DoipMessageContanier contanier = new DoipMessageContanier();
                try {
                    if ("1FFF".equalsIgnoreCase(targetAddress)) {
                        this.fdTcpClient.sendDoipMessage(targetAddress, doipInstructionCode, new FdFunctionalAddressingReceiveListener(doipInstructionCode, targetAddressInt, contanier), queuer, false);
                        return contanier.getFunctionalAddressingResponse();
                    }
                    String udsType = doipInstructionCode.substring(0, 2);
                    boolean is36 = false;
                    FdDoipTcpReceiveListener listener = this.fdTcpClient.getIdleReceiveListener();
                    if ("36".equalsIgnoreCase(udsType)) {
                        is36 = true;
                        listener.setDoipInfo(doipInstructionCode, targetAddressInt, contanier, send2, pClientVehicleMax, p2ServerMax, p4ServerMax, targetAddress, true, 6000, false);
                    } else {
                        listener.setDoipInfo(doipInstructionCode, targetAddressInt, contanier, send2, pClientVehicleMax, p2ServerMax, p4ServerMax, targetAddress, false, 6000, false);
                    }
                    this.fdTcpClient.sendDoipMessage(targetAddress, doipInstructionCode, listener, queuer, is36);
                    LOG.info("线程【{}-{}】等待udsData：【{}】结束", new Object[]{Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName(), udsData});
                    contanier.getXmlMessageString();
                    DoipException e = listener.getLastDoipException();
                    if (e != null) {
                        TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(e.getMessage());
                        if (enumByCodeOrStr == null) {
                            TesterErrorCodeEnum testerErrorCodeEnum = TesterErrorCodeEnum.SG00152;
                        }
                        if (e instanceof CancelByUserException) {
                        }
                        if (e instanceof DoipRetryException) {
                        }
                        throw e;
                    }
                    if (contanier.isDiagnosticMessageNegAck()) {
                        throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00161));
                    }
                    String response = contanier.getResponse(udsType);
                    return response;
                } catch (CancelByUserException e2) {
                    throw e2;
                } catch (DoipException e3) {
                    LOG.warn("发送uds指令异常{}", e3.getMessage());
                    if (0 != 0) {
                        Thread.sleep(150L);
                    } else if (retries == 0) {
                        if (errorHandlingType == ConstantEnum.TWO.intValue()) {
                            if (retries == 0) {
                                throw e3;
                            }
                        } else {
                            if (errorHandlingType == 1) {
                                return getNegRes(e3.getMessage());
                            }
                            return getNegRes(e3.getMessage());
                        }
                    } else {
                        Thread.sleep(delay);
                        retries--;
                    }
                }
            } else {
                return "";
            }
        }
    }

    private String getNegRes(String msg) {
        Map<String, Object> data = new HashMap<>(2);
        Map<String, Object> value = new HashMap<>(2);
        Map<String, Object> type = new HashMap<>(2);
        value.put("Data_Type", ConstantEnum.STRING);
        type.put("Data_Type", ConstantEnum.STRING);
        type.put("Data_Value", "Negative");
        data.put("ECU_Response_value", value);
        data.put("ECU_Response_Type", type);
        return ObjectMapperUtils.obj2JsonStr(data);
    }

    public String getUdsString(String targetAddress, String data, String pClientVehicleMax, String p2ServerMax, String p4ServerMax) {
        String res = "{\"Source_address\": \"0E80\",\"Target_address\": \"" + targetAddress + "\",\"Data\": \"" + data + "\",\"Retries\": \"0\",\"Delay\": \"500\",\"ErrorHandlingType\": \"2\",\"P_Client2_VehicleMax\": \"" + pClientVehicleMax + "\",\"P2ServerMax\": \"" + p2ServerMax + "\",\"P4ServerMax\": \"" + p4ServerMax + "\"}";
        return res;
    }

    public String a(String inputJsonStr, UdsQopManager qop) throws Exception {
        if (qop == null || !qop.isSupportQop()) {
            return udsData(inputJsonStr);
        }
        UdsQopThread thread = qop.getQopThread();
        if (qop.isClosing() || thread == null) {
            DoipException doipException = qop.getLastDoipException();
            if (doipException == null) {
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00076));
            }
            throw doipException;
        }
        while (!thread.isWaiting()) {
            Thread.sleep(1L);
        }
        thread.setUsdData(inputJsonStr);
        return "";
    }

    @Override // com.geely.gnds.dsa.component.SoftwareDownload
    public void writeFlushData(String type, Integer progress, String status, String swPartNumber) {
        a(type, progress, status, swPartNumber, "");
    }

    public void a(String type, Integer progress, String status, String swPartNumber, String ecuAddress) {
        DsaFlushData dsaFlushData = new DsaFlushData(type, progress, status, swPartNumber, "Transparent", ecuAddress, this.eb);
        try {
            if (DsaLogWebSocket.hasLogSession()) {
                CopyOnWriteArrayList<Session> logSessionList = DsaLogWebSocket.getLogSessionList();
                Iterator<Session> it = logSessionList.iterator();
                while (it.hasNext()) {
                    Session session = it.next();
                    synchronized (session) {
                        String data = ObjectMapperUtils.obj2JsonStr(dsaFlushData);
                        LOG.info("DSA日志socket推送刷写消息:{}", data);
                        session.getBasicRemote().sendText(data);
                    }
                }
            } else {
                LOG.info("DSA日志socket没有初始化，无法推送");
            }
        } catch (Exception e) {
            LOG.error("DSA日志记录失败", e);
        }
    }

    private void b(String type, Integer progress, String status, String swPartNumber, String ecuAddress) {
        DsaFlushData dsaFlushData = new DsaFlushData(type, progress, status, swPartNumber, "Red", ecuAddress, this.eb);
        try {
            if (DsaLogWebSocket.hasLogSession()) {
                CopyOnWriteArrayList<Session> logSessionList = DsaLogWebSocket.getLogSessionList();
                Iterator<Session> it = logSessionList.iterator();
                while (it.hasNext()) {
                    Session session = it.next();
                    synchronized (session) {
                        String data = ObjectMapperUtils.obj2JsonStr(dsaFlushData);
                        LOG.info("DSA日志socket推送刷写消息:{}", data);
                        session.getBasicRemote().sendText(data);
                    }
                }
            } else {
                LOG.info("DSA日志socket没有初始化，无法推送");
            }
        } catch (Exception e) {
            LOG.error("DSA日志记录失败", e);
        }
    }

    private boolean a(String filePath, int asciiCount, String checkSum) throws IOException {
        try {
            FileInputStream fileInputStream = new FileInputStream(filePath);
            Throwable th = null;
            try {
                try {
                    fileInputStream.skip(asciiCount);
                    long check = IoUtil.checksumCRC32(fileInputStream);
                    if (check == Long.parseLong(checkSum, 16)) {
                        if (fileInputStream != null) {
                            if (0 != 0) {
                                try {
                                    fileInputStream.close();
                                } catch (Throwable th2) {
                                    th.addSuppressed(th2);
                                }
                            } else {
                                fileInputStream.close();
                            }
                        }
                        return true;
                    }
                    if (fileInputStream != null) {
                        if (0 != 0) {
                            try {
                                fileInputStream.close();
                            } catch (Throwable th3) {
                                th.addSuppressed(th3);
                            }
                        } else {
                            fileInputStream.close();
                        }
                    }
                    return false;
                } finally {
                }
            } finally {
            }
        } catch (Exception e) {
            LOG.error("checkCRC异常{}", filePath, e);
            return false;
        }
        LOG.error("checkCRC异常{}", filePath, e);
        return false;
    }

    private boolean a(PinCodeDTO ecuPinCodeDto, List<String> fixByte) {
        boolean isCloudPinCode = false;
        if (ecuPinCodeDto != null) {
            if (StringUtils.isNotBlank(ecuPinCodeDto.getCloudPinCode())) {
                fixByte.add(ecuPinCodeDto.getCloudPinCode());
                fixByte.add(ecuPinCodeDto.getCloudPinCode());
                isCloudPinCode = true;
            } else if (StringUtils.isNotBlank(ecuPinCodeDto.getLocalPinCode())) {
                fixByte.add(ecuPinCodeDto.getLocalPinCode());
                fixByte.add(ecuPinCodeDto.getLocalPinCode());
            }
        }
        if (fixByte.size() == 0) {
            fixByte.add("FFFFFFFFFF");
            fixByte.add("FFFFFFFFFF");
        }
        return isCloudPinCode;
    }

    public String getUdsString(String targetAddress, String data) {
        String res = "{\"Source_address\": \"0E80\",\"Target_address\": \"" + targetAddress + "\",\"Data\": \"" + data + "\",\"Retries\": \"0\",\"Delay\": \"500\",\"ErrorHandlingType\": \"2\"}";
        return res;
    }

    public String a(String targetAddress, String data, int errorHandlingType) {
        String res = "{\"Source_address\": \"0E80\",\"Target_address\": \"" + targetAddress + "\",\"Data\": \"" + data + "\",\"Retries\": \"0\",\"Delay\": \"500\",\"ErrorHandlingType\": \"" + errorHandlingType + "\"}";
        return res;
    }

    public List<EcuDsaDto> b(List<VbfParseDto> list) {
        Map<String, EcuDsaDto> didMap = new LinkedHashMap<>();
        for (VbfParseDto dto : list) {
            dto.setFlushSuccess(false);
            String address = dto.getAddress();
            if (didMap.containsKey(address)) {
                List<VbfParseDto> vbfList = didMap.get(address).getVbfList();
                if (CollectionUtils.isEmpty(vbfList)) {
                    vbfList = new ArrayList();
                }
                vbfList.add(dto);
            } else {
                EcuDsaDto ecuDsaDto = new EcuDsaDto();
                ecuDsaDto.setAddress(address);
                List<VbfParseDto> vbfList2 = new ArrayList<>();
                vbfList2.add(dto);
                ecuDsaDto.setVbfList(vbfList2);
                didMap.put(address, ecuDsaDto);
            }
        }
        List<EcuDsaDto> ecuDsaDtos = (List) didMap.values().stream().collect(Collectors.toList());
        List<EcuDsaDto> ecusDsaCopy = new ArrayList<>(ecuDsaDtos.size());
        for (EcuDsaDto ecuDsaDto2 : ecuDsaDtos) {
            if (a(ecuDsaDto2)) {
                ecusDsaCopy.add(ecuDsaDto2);
            }
        }
        return ecusDsaCopy;
    }

    public String getDsaLogCommand(String commandAfter) {
        StringBuilder sb = new StringBuilder();
        int length = commandAfter.length();
        for (int i = 0; i < length / 2; i++) {
            String substring = commandAfter.substring(i * 2, (i * 2) + 2);
            sb.append(substring);
            if (i < (length / 2) - 1) {
                sb.append(ConstantEnum.EMPTY);
            }
        }
        return sb.toString();
    }

    public String getDsaLogCommandHex(String commandAfter) {
        StringBuilder sb = new StringBuilder();
        int length = commandAfter.length();
        for (int i = 0; i < length / 2; i++) {
            String substring = commandAfter.substring(i * 2, (i * 2) + 2);
            sb.append("0x").append(substring);
            if (i < (length / 2) - 1) {
                sb.append(ConstantEnum.EMPTY);
            }
        }
        return sb.toString();
    }
}
