package com.geely.gnds.dsa.service;

import com.geely.gnds.dsa.dto.DsaHistoricRecordsDTO;
import java.util.List;

/* loaded from: DsaHistoricRecordsService.class */
public interface DsaHistoricRecordsService {
    List<DsaHistoricRecordsDTO> getList();

    DsaHistoricRecordsDTO queryById(Integer num);

    DsaHistoricRecordsDTO insert(DsaHistoricRecordsDTO dsaHistoricRecordsDTO);

    DsaHistoricRecordsDTO update(DsaHistoricRecordsDTO dsaHistoricRecordsDTO);

    boolean deleteById(Integer num);

    void saveOrUpdate(DsaHistoricRecordsDTO dsaHistoricRecordsDTO);
}
