package com.geely.gnds.dsa.service;

import com.geely.gnds.dsa.dto.DiaDtcDTO;
import com.geely.gnds.dsa.dto.DsaDtcRespDTO;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import java.util.List;

/* loaded from: DsaDataAnalysisService.class */
public interface DsaDataAnalysisService {
    List<DiagResItemParseResultDto> analysisDidData(String str, List<DiagResItemGroupDto> list, String str2);

    List<DsaDtcRespDTO> analysisDtcStatusMask(String str, List<DiaDtcDTO> list, String str2);

    void parseResponseByDid(List<DiagResItemParseResultDto> list, List<DiagResItemGroupDto> list2, String str, List<String> list3);
}
