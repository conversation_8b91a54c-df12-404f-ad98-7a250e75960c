package com.geely.gnds.dsa.service;

import com.geely.gnds.dsa.dto.BssIdDTO;
import com.geely.gnds.dsa.dto.CalculationEcuDTO;
import com.geely.gnds.dsa.dto.SoftwareDownloadDto;
import com.geely.gnds.dsa.dto.TestOrderDTO;
import com.geely.gnds.dsa.dto.TesterOrderParamDTO;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.vo.UploadVo;
import java.util.List;

/* loaded from: IntegrationTestService.class */
public interface IntegrationTestService {
    void uploadLocalVdn(UploadVo uploadVo) throws Exception;

    BssIdDTO uploadLocalBss(UploadVo uploadVo) throws Exception;

    void getOnlineVdn(String str, String str2) throws Exception;

    List<CalculationEcuDTO> calculating(String str, List<String> list) throws Exception;

    void download(SoftwareDownloadDto softwareDownloadDto) throws Exception;

    void testerOrder(TesterOrderParamDTO testerOrderParamDTO, String str, FdDsaLogger fdDsaLogger, TestOrderDTO testOrderDTO);

    List getDsaIntegrationTestSeq(String str, String str2, String str3) throws Exception;
}
