package com.geely.gnds.dsa.service.impl;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geely.gnds.dsa.dto.PinCodeCloudValueDTO;
import com.geely.gnds.dsa.dto.PinCodeDTO;
import com.geely.gnds.dsa.dto.SaveDsaPinCodeReqDTO;
import com.geely.gnds.dsa.service.PinCodeService;
import com.geely.gnds.dsa.utils.JsonUtil;
import com.geely.gnds.ruoyi.common.utils.bean.BeanUtils;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: PinCodeServiceImpl.class */
public class PinCodeServiceImpl implements PinCodeService {
    private static final Logger log = LoggerFactory.getLogger(PinCodeServiceImpl.class);
    private static final Map<String, List<PinCodeDTO>> eT = new ConcurrentHashMap();

    @Autowired
    private Cloud cloud;

    @Override // com.geely.gnds.dsa.service.PinCodeService
    public List<PinCodeDTO> getLocalPinCode(String vin) throws IOException {
        List<PinCodeDTO> pinCodes = eT.get(vin);
        if (ObjectUtils.isEmpty(pinCodes)) {
            File base = new File(new File(ConstantEnum.POINT), "config");
            if (!base.exists()) {
                base.mkdirs();
            }
            File checkFile = new File(base, "pinCode.json");
            if (!checkFile.exists()) {
                checkFile.createNewFile();
                return new ArrayList();
            }
            String jsonStr = JsonUtil.u(checkFile.getPath());
            if (ObjectUtils.isEmpty(jsonStr)) {
                return new ArrayList();
            }
            pinCodes = ObjectMapperUtils.jsonStr2List(jsonStr, PinCodeDTO.class);
            if (CollectionUtils.isEmpty(pinCodes)) {
                return new ArrayList();
            }
            eT.put(vin, pinCodes);
        }
        if (CollectionUtils.isNotEmpty(pinCodes)) {
            pinCodes = (List) pinCodes.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                return new TreeSet(Comparator.comparing((v0) -> {
                    return v0.getEcuAddress();
                }));
            }), (v1) -> {
                return new ArrayList(v1);
            }));
        }
        return pinCodes;
    }

    @Override // com.geely.gnds.dsa.service.PinCodeService
    public Boolean savePinCode(String vin, SaveDsaPinCodeReqDTO dto) throws IOException {
        List<PinCodeDTO> pinCodes = dto.getPinCodeList();
        List<PinCodeDTO> cachePinCodeList = eT.get(vin);
        if (ObjectUtils.isEmpty(cachePinCodeList)) {
            cachePinCodeList = new ArrayList();
        }
        Map<String, PinCodeDTO> cacheMap = (Map) cachePinCodeList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getEcuAddress();
        }, Function.identity(), (key1, key2) -> {
            return key2;
        }));
        pinCodes.forEach(p -> {
            String ecuAddress = p.getEcuAddress().replaceAll("\\s", "");
            p.setEcuAddress(ecuAddress);
            p.setLocalPinCode(p.getLocalPinCode().replaceAll("\\s", ""));
            if (cacheMap.containsKey(ecuAddress)) {
                PinCodeDTO pinCodeDTO = (PinCodeDTO) cacheMap.get(ecuAddress);
                if (pinCodeDTO.getEcuName().equals(p.getEcuName())) {
                    p.setEcuName(pinCodeDTO.getEcuName());
                    p.setCloudPinCode(pinCodeDTO.getCloudPinCode());
                    p.setPrivacyCloudPinCode(pinCodeDTO.getPrivacyCloudPinCode());
                    p.setCodeId(pinCodeDTO.getCodeId());
                }
            }
        });
        eT.put(vin, pinCodes);
        ObjectMapper instance = ObjectMapperUtils.getInstance();
        instance.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        List<PinCodeDTO> saveCodeList = (List) pinCodes.stream().map(e -> {
            PinCodeDTO d = new PinCodeDTO();
            BeanUtils.copyProperties(e, d);
            return d;
        }).collect(Collectors.toList());
        saveCodeList.forEach(c -> {
            c.setCloudPinCode(null);
            c.setPrivacyCloudPinCode(null);
        });
        File base = new File(new File(ConstantEnum.POINT), "config/pinCode.json");
        String filePath = base.getPath();
        JsonUtil.saveJsonFile(filePath, instance.writeValueAsString(saveCodeList));
        return true;
    }

    @Override // com.geely.gnds.dsa.service.PinCodeService
    public void cleanCache(String vin) {
        try {
            eT.remove(vin);
            log.info("清除pinCode,vin={}", vin);
        } catch (Exception e) {
            log.error("清除pinCode出现异常！vin={},e={}", vin, e);
        }
    }

    @Override // com.geely.gnds.dsa.service.PinCodeService
    public List<PinCodeDTO> getCloudPinCode(String vin, List<PinCodeDTO> pinCodes) throws Exception {
        List<String> codeIds = (List) pinCodes.stream().map((v0) -> {
            return v0.getCodeId();
        }).collect(Collectors.toList());
        String cloudPinCode = this.cloud.b(vin, codeIds);
        PinCodeCloudValueDTO pinCodeCloudValueDTO = (PinCodeCloudValueDTO) ObjectMapperUtils.jsonStr2Clazz(cloudPinCode, PinCodeCloudValueDTO.class);
        Map<String, String> valueMap = (Map) pinCodeCloudValueDTO.getPinCodes().stream().collect(Collectors.toMap((v0) -> {
            return v0.getPinCodeName();
        }, (v0) -> {
            return v0.getPinCodeValue();
        }, (k1, k2) -> {
            return k1;
        }));
        pinCodes.forEach(p -> {
            if (valueMap.containsKey(p.getCodeId())) {
                p.setCloudPinCode((String) valueMap.get(p.getCodeId()));
                p.setPrivacyCloudPinCode((String) valueMap.get(p.getCodeId()));
            }
        });
        eT.put(vin, pinCodes);
        return pinCodes;
    }

    @Override // com.geely.gnds.dsa.service.PinCodeService
    public Boolean clearCloudPinCode(String vin, PinCodeDTO pinCode) {
        List<PinCodeDTO> pinCodes = eT.get(vin);
        if (ObjectUtils.isNotEmpty(pinCodes)) {
            for (PinCodeDTO p : pinCodes) {
                if (p.getEcuAddress().equals(pinCode.getEcuAddress())) {
                    p.setCloudPinCode(null);
                    p.setPrivacyCloudPinCode(null);
                }
            }
            return true;
        }
        return false;
    }
}
