package com.geely.gnds.dsa.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.dsa.dto.EcuInfoDTO;
import com.geely.gnds.dsa.dto.VbfParseDto;
import com.geely.gnds.dsa.service.DsaParamService;
import com.geely.gnds.dsa.service.DsaVbfService;
import com.geely.gnds.dsa.service.SddbCacheService;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.dsa.service.UdsService;
import com.geely.gnds.dsa.vo.GetEcuInfoVo;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.util.CommonFunctionsUtils;
import com.geely.gnds.tester.util.VbfParseUtils;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;

@Service
/* loaded from: DsaVbfServiceImpl.class */
public class DsaVbfServiceImpl implements DsaVbfService {
    private static final Logger log = LoggerFactory.getLogger(DsaVbfServiceImpl.class);
    private static String eI = "\\config\\dsaCommand.json";

    @Autowired
    private UdsService es;

    @Autowired
    private SddbCacheService eH;

    @Autowired
    private SddbService eo;

    @Autowired
    private DsaParamService eJ;

    @Autowired
    private VbfParseUtils vbfParseUtils;

    @Override // com.geely.gnds.dsa.service.DsaVbfService
    public List<EcuInfoDTO> getEcuInfo(GetEcuInfoVo vo) throws Exception {
        log.info("开始识别ECU，{}", vo);
        String vin = vo.getVin();
        List<String> addressList = vo.getAddressList();
        if (CollectionUtils.isEmpty(addressList)) {
            return new ArrayList();
        }
        List<EcuInfoDTO> list = new ArrayList<>();
        JSONObject jsonObject = s();
        String duNumber = jsonObject.getString("duNumber");
        String appVersion = jsonObject.getString("appVersion");
        String hwsdOrHwkn = jsonObject.getString("hwsdOrHwkn");
        String sxdiSwlm = jsonObject.getString("sxdiSwlm");
        for (String address : addressList) {
            log.info("识别单个ECU：{}", address);
            EcuInfoDTO dto = new EcuInfoDTO();
            dto.setAddress(address);
            String duNumberResponse = this.es.sendUds(address, duNumber, vin);
            String appVersionResponse = this.es.sendUds(address, appVersion, vin);
            String hwsdOrHwknResponse = this.es.sendUds(address, hwsdOrHwkn, vin);
            String sxdiSwlmResponse = this.es.sendUds(address, sxdiSwlm, vin);
            dto.setAppVersion(Arrays.asList(appVersionResponse));
            if (StringUtils.isNotBlank(appVersionResponse)) {
                String str = appVersionResponse.substring(2);
                List<String> array = CommonFunctionsUtils.splitString(str, 16);
                List<String> resultList = new ArrayList<>();
                for (String s : array) {
                    resultList.add(CommonFunctionsUtils.analysisParam(s));
                }
                dto.setAppVersion(resultList);
            }
            dto.setDuNumber(CommonFunctionsUtils.analysisParam(duNumberResponse));
            dto.setHwsdOrHwkn(CommonFunctionsUtils.analysisParam(hwsdOrHwknResponse));
            dto.setSxdiSwlm(CommonFunctionsUtils.analysisParam(sxdiSwlmResponse));
            list.add(dto);
        }
        log.info("识别ECU完成");
        return list;
    }

    private JSONObject s() throws IOException {
        log.info("读取指令-地址：{}", AppConfig.getAppDataDir().getAbsolutePath() + eI);
        File file = ResourceUtils.getFile(AppConfig.getAppDataDir().getAbsolutePath() + eI);
        String json = FileUtils.readFileToString(file, "UTF-8");
        JSONObject jsonObject = JSONObject.parseObject(json);
        return jsonObject;
    }

    public String getValue(String input) {
        String number = input.substring(0, 10);
        String version = input.substring(14);
        char ascii = (char) Integer.parseInt(version, 16);
        String version2 = String.valueOf(ascii);
        return number + "  " + version2;
    }

    @Override // com.geely.gnds.dsa.service.DsaVbfService
    public VbfParseDto getVbfParseDto(String filePath) throws Exception {
        if (StringUtils.isBlank(filePath)) {
            return new VbfParseDto();
        }
        Map map = this.vbfParseUtils.parsePlusDsa(filePath);
        VbfParseDto vbfParseDto = new VbfParseDto();
        vbfParseDto.setFilePath(filePath);
        Map vbfHeader = (Map) map.get("VBF_Header");
        Map dataValue = (Map) vbfHeader.get("Data_Value");
        vbfParseDto.setAddress(dataValue.get("ecu_address").toString());
        vbfParseDto.setSoftwareType(dataValue.get("sw_part_type").toString());
        vbfParseDto.setVbfSize(Long.valueOf(dataValue.get("sw_size").toString()));
        vbfParseDto.setSwPartNumber(dataValue.get("sw_part_number").toString());
        vbfParseDto.setSwVersion(dataValue.get("sw_version").toString());
        vbfParseDto.setSwPartType(dataValue.get("sw_part_type").toString());
        vbfParseDto.setFileChecksum(dataValue.get("file_checksum").toString());
        vbfParseDto.setAsciiCount(((Integer) map.get("asciiCount")).intValue());
        return vbfParseDto;
    }
}
