package com.geely.gnds.dsa.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.dto.DiaEcuSwDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticRunResultDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticSeqFileDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticSeqLineDTO;
import com.geely.gnds.dsa.dto.DsaHistoricRecordsDTO;
import com.geely.gnds.dsa.dto.PowerModeDto;
import com.geely.gnds.dsa.dto.SwitchPowerModeDto;
import com.geely.gnds.dsa.enums.DiagnosticSeqHexTypeEnum;
import com.geely.gnds.dsa.enums.DsaAddressConst;
import com.geely.gnds.dsa.enums.DsaHistoryRecordEnum;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.enums.PowerModeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.DsaDiagnosticService;
import com.geely.gnds.dsa.service.DsaHistoricRecordsService;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.dsa.service.UdsService;
import com.geely.gnds.dsa.task.DiagnosticRunTask;
import com.geely.gnds.ruoyi.common.exception.CustomException;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.SecurityUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.tester.cache.FileCache;
import com.geely.gnds.tester.dao.TesterConfigDao;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.TesterConfigDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.util.MessageUtils;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
/* loaded from: DsaDiagnosticServiceImpl.class */
public class DsaDiagnosticServiceImpl implements DsaDiagnosticService {
    private static final ConcurrentHashMap<String, DiagnosticRunTask> eB = new ConcurrentHashMap<>();
    private static final Logger log = LoggerFactory.getLogger(DsaDiagnosticServiceImpl.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @Autowired
    private FileCache fileCache;

    @Autowired
    private TesterConfigDao eC;

    @Autowired
    private SddbService eo;

    @Autowired
    private UdsService es;

    @Autowired
    private DsaHistoricRecordsService eD;

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public String getSeqPath() {
        TesterConfigDto config = this.eC.getConfig();
        if (!ObjectUtils.isEmpty(config)) {
            String dbDsaSeqPath = config.getDsaSeqPath();
            if (StringUtils.isNotBlank(dbDsaSeqPath)) {
                this.fileCache.setDsaSeqBase(dbDsaSeqPath);
            }
        }
        return this.fileCache.getDsaSeqBase().getAbsolutePath();
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public List<String> getSeqFileList() {
        String seqPath = getSeqPath();
        File seqFile = new File(seqPath);
        if (seqFile.exists()) {
            return Arrays.asList(seqFile.list());
        }
        return new ArrayList();
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public Boolean setSeqPath(String filePath) {
        TesterConfigDto config = this.eC.getConfig();
        config.setDsaSeqPath(filePath);
        this.eC.update(config);
        this.fileCache.setDsaSeqBase(config.getDsaSeqPath());
        return true;
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public String saveSeqFile(DsaDiagnosticSeqFileDTO fileDto) throws IOException {
        String fileName = fileDto.getFileName();
        String filePath = fileDto.getFilePath();
        List<DsaDiagnosticSeqLineDTO> lines = fileDto.getLines();
        if (CollectionUtils.isEmpty(lines)) {
            throw new CustomException(MessageUtils.getMessage("The file content is empty"));
        }
        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdir();
        }
        Path path = Paths.get(filePath + "\\" + fileName, new String[0]);
        BufferedWriter writer = Files.newBufferedWriter(path, StandardCharsets.UTF_8, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        Throwable th = null;
        try {
            try {
                for (DsaDiagnosticSeqLineDTO l : lines) {
                    StringBuffer sb = new StringBuffer();
                    sb.append(l.getEcu()).append(ConstantEnum.EMPTY).append(l.getHexString()).append(StringUtils.isNotEmpty(l.getSecurityPinCode()) ? ConstantEnum.EMPTY + l.getSecurityPinCode() : "").append(";").append(ObjectUtils.isEmpty(l.getDelay()) ? "" : l.getDelay()).append(";").append(l.getNote());
                    writer.write(sb.toString());
                    writer.newLine();
                }
                if (writer != null) {
                    if (0 != 0) {
                        try {
                            writer.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        writer.close();
                    }
                }
                return filePath + "\\" + fileName;
            } finally {
            }
        } catch (Throwable th3) {
            if (writer != null) {
                if (th != null) {
                    try {
                        writer.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    writer.close();
                }
            }
            throw th3;
        }
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public List<DsaDiagnosticSeqLineDTO> load(String filePath) throws IOException {
        List<DsaDiagnosticSeqLineDTO> lines = new ArrayList<>();
        File file = new File(filePath);
        if (!file.exists()) {
            return lines;
        }
        Path path = Paths.get(filePath, new String[0]);
        BufferedReader reader = Files.newBufferedReader(path, StandardCharsets.UTF_8);
        Throwable th = null;
        try {
            try {
                AtomicInteger numbers = new AtomicInteger(1);
                reader.lines().forEach(l -> {
                    String[] split = l.split(";");
                    if (split.length > 2) {
                        String temp = split[0].replaceAll("\\s*", "");
                        DsaDiagnosticSeqLineDTO line = new DsaDiagnosticSeqLineDTO();
                        line.setNumber(Integer.valueOf(numbers.getAndIncrement()));
                        line.setEcu(temp.substring(0, 4));
                        String hex = temp.substring(4);
                        if (hex.startsWith("27")) {
                            line.setHexString(hex.substring(0, 4));
                            line.setSecurityPinCode(hex.substring(4));
                        } else {
                            line.setHexString(hex);
                        }
                        line.setDelay(StringUtils.isNotEmpty(split[1]) ? Long.valueOf(Long.parseLong(split[1])) : null);
                        line.setNote(split[2]);
                        lines.add(line);
                    }
                });
                if (reader != null) {
                    if (0 != 0) {
                        try {
                            reader.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        reader.close();
                    }
                }
                LoginUser loginUser = SecurityUtils.getLoginUser();
                DsaHistoricRecordsDTO dsaHistoricRecords = new DsaHistoricRecordsDTO();
                dsaHistoricRecords.setType(Integer.valueOf(DsaHistoryRecordEnum.SEQ.getValue()));
                dsaHistoricRecords.setFileName(filePath);
                dsaHistoricRecords.setUserName(loginUser.getUsername());
                dsaHistoricRecords.setCreateTime(DateUtils.getTime());
                this.eD.saveOrUpdate(dsaHistoricRecords);
                return lines;
            } finally {
            }
        } catch (Throwable th3) {
            if (reader != null) {
                if (th != null) {
                    try {
                        reader.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    reader.close();
                }
            }
            throw th3;
        }
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public List<DsaDiagnosticSeqLineDTO> load(MultipartFile file) throws IOException {
        List<DsaDiagnosticSeqLineDTO> lines = new ArrayList<>();
        BufferedReader in = new BufferedReader(new InputStreamReader(file.getInputStream()));
        Throwable th = null;
        try {
            try {
                AtomicInteger numbers = new AtomicInteger(1);
                in.lines().forEach(l -> {
                    String[] split = l.split(";");
                    if (split.length > 2) {
                        String temp = split[0].replaceAll("\\s*", "");
                        DsaDiagnosticSeqLineDTO line = new DsaDiagnosticSeqLineDTO();
                        line.setNumber(Integer.valueOf(numbers.getAndIncrement()));
                        line.setEcu(temp.substring(0, 4));
                        String hex = temp.substring(4);
                        if (hex.startsWith("27")) {
                            line.setHexString(hex.substring(0, 4));
                            line.setSecurityPinCode(hex.substring(4));
                        } else {
                            line.setHexString(hex);
                        }
                        line.setDelay(StringUtils.isNotEmpty(split[1]) ? Long.valueOf(Long.parseLong(split[1])) : null);
                        line.setNote(split[2]);
                        lines.add(line);
                    }
                });
                if (in != null) {
                    if (0 != 0) {
                        try {
                            in.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        in.close();
                    }
                }
                return lines;
            } finally {
            }
        } catch (Throwable th3) {
            if (in != null) {
                if (th != null) {
                    try {
                        in.close();
                    } catch (Throwable th4) {
                        th.addSuppressed(th4);
                    }
                } else {
                    in.close();
                }
            }
            throw th3;
        }
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public DsaDiagnosticRunResultDTO sequenceCheck(String vin, String runType, List<DsaDiagnosticSeqLineDTO> lines) {
        DsaDiagnosticRunResultDTO runResult = new DsaDiagnosticRunResultDTO();
        try {
            List<String> allEcu = this.eo.getEcuAddressList();
            Set<String> notExistEcu = (Set) lines.stream().filter(l -> {
                return !allEcu.contains(l.getEcu());
            }).map((v0) -> {
                return v0.getEcu();
            }).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(notExistEcu)) {
                String message = StringUtils.strip(notExistEcu.toString(), "[]");
                runResult.setErrorMessage("ECU: " + message);
            } else {
                runResult.setMessage("success");
            }
        } catch (Exception e) {
            runResult.setErrorMessage(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.getEnumByCodeOrStr(e.getMessage())));
            log.error("DSA序列检查异常!vin={},lines={},e={}", new Object[]{vin, lines, e});
        }
        return runResult;
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public DsaDiagnosticRunResultDTO sequenceRun(String vin, String runType, String platform, List<DsaDiagnosticSeqLineDTO> lines) {
        DsaDiagnosticRunResultDTO runResult = sequenceCheck(vin, runType, lines);
        runResult.setTaskId(UUID.randomUUID().toString());
        try {
            List<String> allEcu = this.eo.getEcuAddressList();
            List<DsaDiagnosticSeqLineDTO> filterLines = (List) lines.stream().filter(l -> {
                return allEcu.contains(l.getEcu());
            }).collect(Collectors.toList());
            a(runResult.getTaskId(), vin, filterLines, runType, platform);
        } catch (Exception e) {
            runResult.setErrorMessage("DSA序列执行异常！");
            log.error("DSA序列执行异常!vin={},lines={},e={}", new Object[]{vin, lines, e});
        }
        return runResult;
    }

    private void a(String taskId, String vin, List<DsaDiagnosticSeqLineDTO> lines, String runType, String platform) throws InterruptedException {
        if (CollectionUtils.isEmpty(lines)) {
            return;
        }
        BlockingQueue<DsaDiagnosticSeqLineDTO> queue = new ArrayBlockingQueue<>(lines.size());
        for (DsaDiagnosticSeqLineDTO line : lines) {
            if (StringUtils.isNotEmpty(line.getSecurityPinCode())) {
                line.setHexType(Integer.valueOf(DiagnosticSeqHexTypeEnum.SECURITY_HEX.getValue()));
            } else {
                line.setHexType(Integer.valueOf(DiagnosticSeqHexTypeEnum.GENERAL_HEX.getValue()));
            }
            queue.put(line);
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        DiagnosticRunTask task = new DiagnosticRunTask(taskId, loginUser.getUser().getUserId(), queue, vin, runType, platform);
        eB.put(taskId, task);
        new Thread(task).start();
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public Boolean stopRun(String vin, String taskId) {
        if (eB.containsKey(taskId)) {
            log.info("DSA序列执行停止，vin={},taskId={}", vin, taskId);
            DiagnosticRunTask task = eB.get(taskId);
            task.fh = true;
            eB.remove(taskId);
            return Boolean.valueOf(task.fh);
        }
        return false;
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public void showMessage(String vin, String taskId, String message) {
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        FdDsaLogger fdDsaDsaTxtLogger = client.getFdDsaLogger();
        log.info("DSA序列执行显示数据，vin={},taskId={},message={}", new Object[]{vin, taskId, message});
        Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
            logger.write(DsaLogTypeEnum.ERROR.getValue(), message);
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
        });
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public List<PowerModeDto> getPowerMode() {
        List<PowerModeDto> list = new ArrayList<>();
        list.add(new PowerModeDto(PowerModeEnum.ACTIVATE.valueByLanguage(), PowerModeEnum.ACTIVATE.command(), PowerModeEnum.ACTIVATE.valueEn()));
        list.add(new PowerModeDto(PowerModeEnum.CONVENIENCE.valueByLanguage(), PowerModeEnum.CONVENIENCE.command(), PowerModeEnum.CONVENIENCE.valueEn()));
        list.add(new PowerModeDto(PowerModeEnum.UNACTIVATED.valueByLanguage(), PowerModeEnum.UNACTIVATED.command(), PowerModeEnum.UNACTIVATED.valueEn()));
        list.add(new PowerModeDto(PowerModeEnum.RETURN_CONTROL.valueByLanguage(), PowerModeEnum.RETURN_CONTROL.command(), PowerModeEnum.RETURN_CONTROL.valueEn()));
        return list;
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public void switchPowerMode(SwitchPowerModeDto switchPowerModeDto) throws InterruptedException {
        PowerModeDto powerModeDto = switchPowerModeDto.getPowerMode();
        String vin = switchPowerModeDto.getVin();
        if (com.geely.gnds.ruoyi.common.utils.StringUtils.isEmpty(vin)) {
            throw new CustomException(MessageUtils.getMessage("Vehicle VIN number cannot be empty"));
        }
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        FdDsaLogger fdDsaDsaTxtLogger = client.getFdDsaLogger();
        String command = powerModeDto.getCommand();
        String address = "1A01";
        String pincode = "43454D3033";
        String pincode3 = "AA2677C70BAA2677C70BAA2677C70B11";
        try {
            int i = e(address, vin, fdDsaDsaTxtLogger);
            if (i == 2) {
                int j = e(address, vin, fdDsaDsaTxtLogger);
                if (j == 2) {
                    address = "1001";
                    pincode = "AA2677C70B";
                    pincode3 = "AA2677C70BAA2677C70BAA2677C70B12";
                    int k = e(address, vin, fdDsaDsaTxtLogger);
                    if (k == 3) {
                        Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
                            logger.write(DsaLogTypeEnum.ERROR.getValue(), "会话切换失败");
                            logger.write(DsaLogTypeEnum.SWITCH_POWER_MODE_FAILED.getValue(), "");
                        });
                        return;
                    } else if (k == 2) {
                        Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger2 -> {
                            logger2.write(DsaLogTypeEnum.ERROR.getValue(), "切换失败,无应答消息");
                            logger2.write(DsaLogTypeEnum.SWITCH_POWER_MODE_FAILED.getValue(), "");
                        });
                        return;
                    }
                } else if (j == 3) {
                    Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger3 -> {
                        logger3.write(DsaLogTypeEnum.ERROR.getValue(), "会话切换失败");
                    });
                    return;
                }
            } else if (i == 3) {
                Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger4 -> {
                    logger4.write(DsaLogTypeEnum.ERROR.getValue(), "会话切换失败");
                });
                return;
            }
            if (ConstantEnum.GEEA3.equals(ConstantEnum.GEEA2)) {
                pincode = pincode3;
            }
            Boolean securityAccess = this.es.getSecurityAccess(address, "03", vin, pincode);
            if (!securityAccess.booleanValue()) {
                Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger5 -> {
                    logger5.write(DsaLogTypeEnum.ERROR.getValue(), "安全访问失败");
                });
            }
            this.es.udsData(address, "2FDD0A03" + command, vin);
            Thread.sleep(500L);
            String udsData = this.es.udsData(DsaAddressConst.FUNCTIONAL_ADDRESSING, "22DD0A", vin);
            log.info("切换电源模式 22DD0A响应：" + udsData);
            JSONObject object = JSONObject.parseObject(udsData);
            if (!CollectionUtils.isEmpty(object)) {
                for (String key : object.keySet()) {
                    String res = object.getString(key);
                    if (!res.startsWith("62DD0A" + command)) {
                        Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger6 -> {
                            logger6.write(DsaLogTypeEnum.ERROR.getValue(), "ECU：" + key + " switch power mode failed");
                        });
                    }
                }
            }
        } catch (Exception e) {
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger7 -> {
                logger7.write(DsaLogTypeEnum.ERROR.getValue(), "switch power mode failed：" + e.getMessage());
            });
        }
    }

    private int e(String aderess, String vin, FdDsaLogger fdDsaDsaTxtLogger) {
        int res;
        try {
            this.es.udsData(aderess, "1003", vin);
            res = 1;
        } catch (Exception e) {
            if (e.getMessage().contains(TesterErrorCodeEnum.SG00145.code()) || e.getMessage().contains(TesterErrorCodeEnum.SG00146.code()) || e.getMessage().contains(TesterErrorCodeEnum.SG00161.code())) {
                res = 2;
            } else {
                res = 3;
                Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
                    logger.write(DsaLogTypeEnum.ERROR.getValue(), "会话切换失败");
                });
            }
            log.error("没有1A01", e);
        }
        return res;
    }

    @Override // com.geely.gnds.dsa.service.DsaDiagnosticService
    public DsaDiagnosticRunResultDTO allDidRun(String vin, String runType, String ecuAddress, String platform) throws Exception {
        List<DiaEcuSwDTO> ecuSwList = this.eo.getTargetEcu(ecuAddress);
        if (CollectionUtils.isEmpty(ecuSwList)) {
            throw new CustomException(MessageUtils.getMessage("ECU SW data not found"));
        }
        String diagnosticPartNumber = "";
        if (!CollectionUtils.isEmpty(ecuSwList)) {
            DiaEcuSwDTO diaEcuSwDTO = ecuSwList.stream().filter(e -> {
                return "APP".equalsIgnoreCase(e.getType());
            }).findFirst().orElseGet(() -> {
                return e(ecuSwList);
            });
            if (!org.apache.commons.lang3.ObjectUtils.isEmpty(diaEcuSwDTO)) {
                diagnosticPartNumber = diaEcuSwDTO.getDiagnosticPartNumber();
            }
        }
        List<DiagResItemGroupDto> didList = this.eo.getDidList(diagnosticPartNumber, "22");
        if (CollectionUtils.isEmpty(didList)) {
            throw new CustomException(MessageUtils.getMessage("There is no 22 service under this ECU, please confirm"));
        }
        List<DsaDiagnosticSeqLineDTO> filterLines = new ArrayList<>();
        for (DiagResItemGroupDto groupDto : didList) {
            DsaDiagnosticSeqLineDTO lineDto = new DsaDiagnosticSeqLineDTO();
            lineDto.setDelay(0L);
            lineDto.setNote("");
            lineDto.setRunStatus(0);
            lineDto.setEcu(ecuAddress);
            lineDto.setHexString("22" + groupDto.getDataIdentifierId());
            lineDto.setDidSessions(groupDto.getSessionList());
            filterLines.add(lineDto);
        }
        DsaDiagnosticRunResultDTO runResult = new DsaDiagnosticRunResultDTO();
        runResult.setTaskId(UUID.randomUUID().toString());
        runResult.setMessage("success");
        a(runResult.getTaskId(), vin, filterLines, runType, platform);
        return runResult;
    }

    private DiaEcuSwDTO e(List<DiaEcuSwDTO> ecuSwList) {
        return ecuSwList.stream().filter(e -> {
            return "PBL".equalsIgnoreCase(e.getType());
        }).findFirst().orElse(null);
    }
}
