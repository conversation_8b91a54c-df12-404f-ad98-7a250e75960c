package com.geely.gnds.dsa.service.impl;

import com.geely.gnds.dsa.dao.DsaCustomButtonDao;
import com.geely.gnds.dsa.dto.DsaCustomButtonDTO;
import com.geely.gnds.dsa.service.DsaCustomButtonService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
/* loaded from: DsaCustomButtonServiceImpl.class */
public class DsaCustomButtonServiceImpl implements DsaCustomButtonService {

    @Resource
    private DsaCustomButtonDao ey;

    @Override // com.geely.gnds.dsa.service.DsaCustomButtonService
    public DsaCustomButtonDTO queryById(Integer id) {
        return this.ey.queryById(id);
    }

    @Override // com.geely.gnds.dsa.service.DsaCustomButtonService
    public List<DsaCustomButtonDTO> queryAll() {
        return this.ey.getList();
    }

    @Override // com.geely.gnds.dsa.service.DsaCustomButtonService
    public boolean insert(DsaCustomButtonDTO dsaCustomCommand) {
        return this.ey.insert(dsaCustomCommand) > 0;
    }

    @Override // com.geely.gnds.dsa.service.DsaCustomButtonService
    public boolean update(DsaCustomButtonDTO dsaCustomCommand) {
        return this.ey.update(dsaCustomCommand) > 0;
    }
}
