package com.geely.gnds.dsa.service.impl;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.dto.DiaDtcDTO;
import com.geely.gnds.dsa.dto.DiaEcuSwDTO;
import com.geely.gnds.dsa.dto.DiaRoutineIdentifierDTO;
import com.geely.gnds.dsa.dto.DiaSessionDTO;
import com.geely.gnds.dsa.dto.DiaSubfunctionDTO;
import com.geely.gnds.dsa.dto.DiaTreeNode;
import com.geely.gnds.dsa.enums.DiagnosisTypeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.SddbCacheService;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.dsa.vo.SddbUploadVo;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.util.Result;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeSet;
import java.util.stream.Collectors;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: SddbServiceImpl.class */
public class SddbServiceImpl implements SddbService {
    private static final Logger log = LoggerFactory.getLogger(SddbServiceImpl.class);
    private static String eq = "sddbFilePath";
    private SingletonManager manager = SingletonManager.getInstance();

    @Autowired
    private SddbCacheService eH;

    @Override // com.geely.gnds.dsa.service.SddbService
    public List<DiaEcuSwDTO> importSddb(SddbUploadVo vo) throws Exception {
        this.eH.clearCache();
        String vin = vo.getVin();
        log.info("sddb解析-文件路径：{}，vin：{}", vo.getFilePath(), vin);
        System.out.println("sddb解析-文件路径：" + vo.getFilePath());
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        if (client != null) {
            FdDsaLogger fdDsaDsaTxtLogger = client.getFdDsaLogger();
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
                logger.writeToLog("# Loaded database:" + vo.getFilePath());
                logger.writeToLog("#");
            });
        }
        List<DiaEcuSwDTO> list = new ArrayList<>();
        List<Element> ecuElementList = this.eH.getEcuEleList();
        log.info("sddb解析-开始获取诊断零件号dto数据列表");
        System.out.println("sddb解析-开始获取诊断零件号dto数据列表");
        for (Element ecu : ecuElementList) {
            String ecuName = ecu.attributeValue("Name");
            String address = ecu.attributeValue("address");
            List<Element> swList = ecu.element("SWs").elements();
            for (Element sw : swList) {
                DiaEcuSwDTO ecuSw = a(sw, ecuName, address);
                list.add(ecuSw);
            }
        }
        log.info("sddb解析-获取诊断零件号dto数据列表完成");
        System.out.println("sddb解析-获取诊断零件号dto数据列表完成");
        this.eH.getEcuSwEleList();
        return list;
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public List<DiagResItemGroupDto> getDidList(String diagnosticPartNum, String serviceId) throws Exception {
        log.info("sddb解析-开始获取did，诊断零件号：{}，服务id：{}", diagnosticPartNum, serviceId);
        System.out.println("sddb解析-开始获取did");
        List<DiagResItemGroupDto> list = new ArrayList<>();
        Element service = f(diagnosticPartNum, serviceId);
        if (ObjectUtils.isEmpty(service)) {
            log.info("sddb解析-获取did- service为空");
            return list;
        }
        Element dataIdentifiers = service.element("DataIdentifiers");
        if (ObjectUtils.isEmpty(dataIdentifiers)) {
            log.info("sddb解析-获取did- DataIdentifiers为空");
            return list;
        }
        List<Element> dataIdentifierList = dataIdentifiers.elements();
        for (Element e : dataIdentifierList) {
            DiagResItemGroupDto dto = a(e, diagnosticPartNum);
            list.add(dto);
        }
        log.info("sddb解析-获取did完成");
        System.out.println("sddb解析-获取did完成");
        return list;
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public List<DiaRoutineIdentifierDTO> getRoutineIdentifierList(String diagnosticPartNum, String serviceId) throws Exception {
        log.info("sddb解析-开始获取31服务 routine control，诊断零件号：{}，服务id：{}", diagnosticPartNum, serviceId);
        System.out.println("sddb解析-开始获取31服务 routine control");
        List<DiaRoutineIdentifierDTO> list = new ArrayList<>();
        Element service = f(diagnosticPartNum, serviceId);
        if (ObjectUtils.isEmpty(service)) {
            log.info("sddb解析-开始获取31服务- service为空");
            return list;
        }
        Element subfunctions = service.element("Subfunctions");
        if (ObjectUtils.isEmpty(subfunctions)) {
            log.info("sddb解析-开始获取31服务- Subfunctions为空");
            return list;
        }
        List<Element> subfunctionList = subfunctions.elements();
        for (Element sub : subfunctionList) {
            DiaSubfunctionDTO subfunctionDto = b(sub, diagnosticPartNum);
            List<Element> routineIdentifierList = sub.element("RoutineIdentifiers").elements();
            for (Element rout : routineIdentifierList) {
                DiaRoutineIdentifierDTO routineIdentifierDto = a(rout, subfunctionDto);
                list.add(routineIdentifierDto);
            }
        }
        log.info("sddb解析-获取RoutineIdentifier完成");
        System.out.println("sddb解析-获取RoutineIdentifier完成");
        return list;
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public Result<List<DiaEcuSwDTO>> getEcuList() {
        Result<List<DiaEcuSwDTO>> result = new Result<>();
        try {
            List<Element> ecuEleList = this.eH.getEcuEleList();
            List<DiaEcuSwDTO> list = new ArrayList<>();
            for (Element ecuElement : ecuEleList) {
                DiaEcuSwDTO dto = new DiaEcuSwDTO();
                dto.setEcuName(ecuElement.attributeValue("Name"));
                dto.setAddress(ecuElement.attributeValue("address"));
                List<Element> swElementList = ecuElement.element("SWs").elements();
                String diagnosticPartNumber = (String) swElementList.stream().filter(swElement -> {
                    return "APP".equals(swElement.attributeValue("Type"));
                }).map(swElement2 -> {
                    return swElement2.attributeValue("DiagnosticPartNumber").replaceAll("\\s+", "");
                }).findFirst().orElse("");
                dto.setDiagnosticPartNumber(diagnosticPartNumber);
                list.add(dto);
            }
            result.ok(list);
        } catch (Exception e) {
            log.error("sddb解析-获取ECU列表失败");
            result.error();
        }
        return result;
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public List<String> getEcuAddressList() throws Exception {
        List<Element> ecuEleList = this.eH.getEcuEleList();
        List<String> list = new ArrayList<>();
        for (Element e : ecuEleList) {
            list.add(e.attributeValue("address"));
        }
        return list;
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public Result<Object> getSecurityAreaList(String targetAddress, String ecuName) {
        Result<Object> result = new Result<>();
        try {
            log.info("sddb解析-根据ecuAddress获取SecurityArea开始，ecuAddress:{},ecuName:{}", targetAddress, ecuName);
            System.out.println("sddb解析-根据ecuAddress获取SecurityArea开始，ecuAddress:" + targetAddress);
            List<DiaSubfunctionDTO> subfunctionList = new ArrayList<>();
            List<Element> ecuEleList = this.eH.getEcuEleList();
            Element ecuEle = null;
            Iterator<Element> it = ecuEleList.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                Element e = it.next();
                String address = e.attributeValue("address");
                String name = e.attributeValue("Name");
                if (targetAddress.equals(address) && ecuName.equals(name)) {
                    ecuEle = e;
                    break;
                }
            }
            log.info("sddb解析-根据ecuAddress获取SecurityArea，获取到目标ecu，name:{}，address:{}", ecuEle.attributeValue("Name"), ecuEle.attributeValue("address"));
            System.out.println("sddb解析-根据ecuAddress获取SecurityArea，获取到目标ecu，" + ecuEle.attributeValue("Name") + ConstantEnum.COMMA + ecuEle.attributeValue("address"));
            List<Element> swList = ecuEle.element("SWs").elements();
            List<Element> targetSwList = new ArrayList<>();
            for (Element e2 : swList) {
                String type = e2.attributeValue("Type");
                if ("APP".equals(type) || "PBL".equalsIgnoreCase(type)) {
                    targetSwList.add(e2);
                }
            }
            log.info("sddb解析-根据ecuAddress获取SecurityArea，获取到type为APP的诊断零件号");
            System.out.println("sddb解析-根据ecuAddress获取SecurityArea，获取到type为APP的诊断零件号");
            for (Element e3 : targetSwList) {
                Element servicesEle = e3.element("Services");
                if (ObjectUtils.isEmpty(servicesEle)) {
                    log.info("sddb解析-根据ecuAddress获取SecurityArea-Services为空");
                    System.out.println("sddb解析-根据ecuAddress获取SecurityArea-Services为空");
                } else {
                    List<Element> serviceList = servicesEle.elements();
                    Element service27Ele = null;
                    Iterator<Element> it2 = serviceList.iterator();
                    while (true) {
                        if (!it2.hasNext()) {
                            break;
                        }
                        Element serviceEle = it2.next();
                        String serviceId = serviceEle.attributeValue("ID");
                        if ("27".equals(serviceId)) {
                            service27Ele = serviceEle;
                            break;
                        }
                    }
                    if (ObjectUtils.isEmpty(service27Ele)) {
                        log.info("sddb解析-根据ecuAddress获取SecurityArea-27服务为空");
                        System.out.println("sddb解析-根据ecuAddress获取SecurityArea-27服务为空");
                    } else {
                        Element subfunctions = service27Ele.element("Subfunctions");
                        if (ObjectUtils.isEmpty(subfunctions)) {
                            log.info("sddb解析-根据ecuAddress获取SecurityArea-Subfunctions为空");
                            System.out.println("sddb解析-根据ecuAddress获取SecurityArea-Subfunctions为空");
                        } else {
                            List<Element> subfunctionEleList = subfunctions.elements();
                            for (Element subEle : subfunctionEleList) {
                                String name2 = subEle.attributeValue("Name");
                                if ("request seed".equals(name2.toLowerCase())) {
                                    DiaSubfunctionDTO subfunctionDto = b(subEle, (String) null);
                                    subfunctionList.add(subfunctionDto);
                                }
                            }
                        }
                    }
                }
            }
            log.info("sddb解析-根据ecuAddress获取SecurityArea完成");
            System.out.println("sddb解析-根据ecuAddress获取SecurityArea完成");
            List<DiaSubfunctionDTO> list = (List) subfunctionList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                return new TreeSet(Comparator.comparing(o -> {
                    return o.getSubfunctionId();
                }));
            }), (v1) -> {
                return new ArrayList(v1);
            }));
            result.ok((List) list.stream().sorted(Comparator.comparing((v0) -> {
                return v0.getSubfunctionId();
            })).collect(Collectors.toList()));
        } catch (Exception e4) {
            log.error("sddb解析-根据ecuAddress获取SecurityArea失败", e4);
            result.error();
        }
        return result;
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public List<DiagResItemGroupDto> getDidByDiagnosisNumAndDid(String diagnosisPartNum, String serviceId, List<String> identifierIdList) {
        try {
            log.info("sddb解析-获取did数据开始，{}", identifierIdList);
            System.out.println("sddb解析-获取did数据开始" + identifierIdList.toString());
            List<DiagResItemGroupDto> list = new ArrayList<>();
            Element swEle = t(diagnosisPartNum);
            List<Element> masterSessionEleList = swEle.element("SessionLayer").elements();
            List<DiaSessionDTO> masterSessionList = a(masterSessionEleList, diagnosisPartNum);
            Element servicesEle = swEle.element("Services");
            if (ObjectUtils.isEmpty(servicesEle)) {
                log.info("sddb解析-获取did数据开始-Services为空");
                return list;
            }
            List<Element> serviceEleList = servicesEle.elements();
            Element targetService = null;
            Iterator<Element> it = serviceEleList.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                Element service = it.next();
                if (serviceId.equals(service.attributeValue("ID"))) {
                    targetService = service;
                    log.info("sddb解析-获取did数据-匹配到对应的service");
                    System.out.println("sddb解析-获取did数据-匹配到对应的service");
                    break;
                }
            }
            Element dataIdentifiersEle = targetService.element("DataIdentifiers");
            if (ObjectUtils.isEmpty(dataIdentifiersEle)) {
                log.info("sddb解析-获取did数据-DataIdentifiers为空");
                return list;
            }
            List<Element> dataIdentifierEleList = dataIdentifiersEle.elements();
            List<Element> targetDataIdentifierEleList = new ArrayList<>();
            Iterator<Element> it2 = dataIdentifierEleList.iterator();
            while (true) {
                if (!it2.hasNext()) {
                    break;
                }
                Element e = it2.next();
                String id = e.attributeValue("ID");
                if (identifierIdList.contains(id)) {
                    targetDataIdentifierEleList.add(e);
                    log.info("sddb解析-获取did数据-匹配到对应的dataIdentifier，{}", id);
                    System.out.println("sddb解析-获取did数据-匹配到对应的dataIdentifier," + id);
                    if (targetDataIdentifierEleList.size() == identifierIdList.size()) {
                        log.info("sddb解析-获取did数据-identifierIdList中数据已全部查出，跳出循环");
                        System.out.println("sddb解析-identifierIdList中数据已全部查出，跳出循环");
                        break;
                    }
                }
            }
            for (Element dataIdentifierEle : targetDataIdentifierEleList) {
                DiagResItemGroupDto dataIdentifierDto = a(dataIdentifierEle, diagnosisPartNum);
                int i = b(dataIdentifierEle);
                dataIdentifierDto.setResponseItemNum(Integer.valueOf(i));
                log.info("sddb解析-获取did数据-获取到dataIdentifierDto");
                System.out.println("sddb解析-获取did数据-获取到dataIdentifierDto");
                DiaSessionDTO sessionDto = masterSessionList.get(0);
                dataIdentifierDto.setMaxNumberOfDidsForReadDid(sessionDto.getMaxNumberOfDidsForReadDid());
                Element sessionsEle = dataIdentifierEle.element("Sessions");
                if (ObjectUtils.isNotEmpty(sessionsEle)) {
                    List<Element> sessionEleList = sessionsEle.elements();
                    List<DiaSessionDTO> sessionList = a(sessionEleList, diagnosisPartNum);
                    log.info("sddb解析-获取did数据-获取到DataIdentifier下的session，size:{}", Integer.valueOf(sessionList.size()));
                    System.out.println("sddb解析-获取did数据-获取到DataIdentifier下的session");
                    if (!CollectionUtils.isEmpty(sessionList)) {
                        dataIdentifierDto.setSessionId(sessionList.get(0).getSessionId());
                    }
                }
                Element responseItemsEle = dataIdentifierEle.element("ResponseItems");
                if (ObjectUtils.isNotEmpty(responseItemsEle)) {
                    List<Element> responseItemEleList = responseItemsEle.elements();
                    HashMap<String, Object> responseItemNameMap = new HashMap<>();
                    List<DiagResItemDto> responseItemList = a(responseItemEleList, diagnosisPartNum, responseItemNameMap);
                    dataIdentifierDto.setResponseItemDtoList(responseItemList);
                    log.info("sddb解析-获取did数据-获取到responseItem");
                    System.out.println("sddb解析-获取did数据-获取到responseItem");
                }
                list.add(dataIdentifierDto);
            }
            return list;
        } catch (Exception e2) {
            log.error("sddb解析-根据ecuAddress获取SecurityArea失败", e2);
            return null;
        }
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public List<DiaDtcDTO> getDtcList(String diagnosisPartNum) throws Exception {
        log.info("sddb解析-获取dtc列表开始，{}", diagnosisPartNum);
        Element serviceEle = f(diagnosisPartNum, "19");
        if (ObjectUtils.isEmpty(serviceEle)) {
            return new ArrayList();
        }
        Element dtcsEle = serviceEle.element("DTCS");
        if (ObjectUtils.isNotEmpty(dtcsEle)) {
            List<Element> dtcEleList = dtcsEle.elements();
            List<DiaDtcDTO> dtcList = b(dtcEleList, diagnosisPartNum);
            return dtcList;
        }
        return new ArrayList();
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public String getDiagPartNumByAddress(String targetAddress) throws Exception {
        log.info("根据ECU地址获取诊断零件号开始-{}", targetAddress);
        System.out.println("根据ECU地址获取诊断零件号开始-" + targetAddress);
        List<Element> ecuEleList = this.eH.getEcuEleList();
        log.info("根据ECU地址获取诊断零件号-查找目标ECU");
        System.out.println("根据ECU地址获取诊断零件号-查找目标ECU");
        Element targetEcuEle = null;
        Iterator<Element> it = ecuEleList.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Element e = it.next();
            String address = e.attributeValue("address");
            if (targetAddress.equals(address)) {
                targetEcuEle = e;
                break;
            }
        }
        log.info("根据ECU地址获取诊断零件号-获取诊断零件号");
        System.out.println("根据ECU地址获取诊断零件号-获取诊断零件号");
        if (ObjectUtils.isEmpty(targetEcuEle)) {
            return null;
        }
        Element swsEle = targetEcuEle.element("SWs");
        if (ObjectUtils.isEmpty(swsEle)) {
            log.info("根据ECU地址获取诊断零件号-SWs为空");
            System.out.println("根据ECU地址获取诊断零件号-SWs为空");
            return null;
        }
        List<Element> swEleList = swsEle.elements();
        Element targetSwEle = null;
        for (Element e2 : swEleList) {
            String type = e2.attributeValue("Type");
            if ("APP".equals(type)) {
                targetSwEle = e2;
            }
        }
        if (ObjectUtils.isEmpty(targetSwEle)) {
            log.info("根据ECU地址获取诊断零件号-没有APP类型的诊断零件号，继续查询PBL类型的诊断零件号");
            System.out.println("根据ECU地址获取诊断零件号-没有APP类型的诊断零件号，继续查询PBL类型的诊断零件号");
            for (Element e3 : swEleList) {
                String type2 = e3.attributeValue("Type");
                if ("PBL".equals(type2)) {
                    targetSwEle = e3;
                }
            }
        }
        String diagnosticPartNum = null;
        if (ObjectUtils.isNotEmpty(targetSwEle)) {
            diagnosticPartNum = targetSwEle.attributeValue("DiagnosticPartNumber").replaceAll(ConstantEnum.EMPTY, "");
        }
        return diagnosticPartNum;
    }

    private int b(Element element) {
        LinkedHashSet<String> hashSet = new LinkedHashSet<>();
        Element responseItemsEle = element.element("ResponseItems");
        if (ObjectUtils.isNotEmpty(responseItemsEle)) {
            List<Element> responseItemEleList = responseItemsEle.elements();
            for (Element e : responseItemEleList) {
                String name = e.attributeValue("Name");
                if (StringUtils.isNotBlank(name)) {
                    hashSet.add(name);
                }
            }
        }
        return hashSet.size();
    }

    private Element f(String diagnosticPartNum, String serviceId) throws Exception {
        log.info("根据诊断零件号和服务id获取service开始");
        System.out.println("根据诊断零件号和服务id获取service开始");
        log.info("sddb解析-根据诊断零件号和服务id获取service-从缓存方法中获取ecuSw节点");
        System.out.println("sddb解析-根据诊断零件号和服务id获取service-从缓存方法中获取ecuSw节点");
        List<Element> ecuSwEleList = this.eH.getEcuSwEleList();
        log.info("sddb解析-根据诊断零件号和服务id获取service-从缓存方法中获取ecuSw节点完成");
        System.out.println("sddb解析-根据诊断零件号和服务id获取service-从缓存方法中获取ecuSw节点完成");
        Element targetSwEle = null;
        if (CollectionUtils.isEmpty(ecuSwEleList)) {
            return null;
        }
        for (Element e : ecuSwEleList) {
            String diagPartNum = e.attributeValue("DiagnosticPartNumber");
            if (diagnosticPartNum.equals(diagPartNum.replaceAll(ConstantEnum.EMPTY, ""))) {
                targetSwEle = e;
            }
        }
        Element targetService = null;
        Element servicesEle = targetSwEle.element("Services");
        if (ObjectUtils.isEmpty(servicesEle)) {
            return null;
        }
        List<Element> serviceEleList = servicesEle.elements();
        Iterator<Element> it = serviceEleList.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Element service = it.next();
            if (serviceId.equals(service.attributeValue("ID"))) {
                targetService = service;
                log.info("sddb解析-匹配到对应的service");
                System.out.println("sddb解析-匹配到对应的service");
                break;
            }
        }
        log.info("sddb解析-根据诊断零件号和服务id获取service完成");
        System.out.println("sddb解析-根据诊断零件号和服务id获取service完成");
        if (ObjectUtils.isEmpty(targetService)) {
            log.error("sddb解析-根据诊断零件号和服务id获取service失败，查不到service，诊断零件号：{}，serviceId：{}", diagnosticPartNum, serviceId);
            return null;
        }
        return targetService;
    }

    private Element t(String diagnosticPartNum) throws Exception {
        List<Element> ecuEleList = this.eH.getEcuEleList();
        Element targetSwEle = null;
        log.info("sddb解析-开始根据诊断零件号获取sw");
        System.out.println("sddb解析-开始根据诊断零件号获取sw");
        for (Element ecu : ecuEleList) {
            Element sws = ecu.element("SWs");
            if (!ObjectUtils.isEmpty(sws)) {
                List<Element> swEleList = sws.elements("SW");
                Iterator<Element> it = swEleList.iterator();
                while (true) {
                    if (!it.hasNext()) {
                        break;
                    }
                    Element sw = it.next();
                    DiaEcuSwDTO ecuSw = a(sw, "", "");
                    if (diagnosticPartNum.equals(ecuSw.getDiagnosticPartNumber())) {
                        targetSwEle = sw;
                        log.info("sddb解析-匹配到对应的诊断零件号");
                        System.out.println("sddb解析-匹配到对应的诊断零件号");
                        break;
                    }
                }
                if (ObjectUtils.isNotEmpty(targetSwEle)) {
                    break;
                }
            }
        }
        log.info("sddb解析-根据诊断零件号获取sw完成");
        System.out.println("sddb解析-根据诊断零件号获取sw完成");
        return targetSwEle;
    }

    private DiaEcuSwDTO a(Element element, String ecuName, String address) {
        DiaEcuSwDTO ecuSwDTO = new DiaEcuSwDTO();
        ecuSwDTO.setEcuName(ecuName);
        ecuSwDTO.setAddress(address);
        ecuSwDTO.setSwName(element.attributeValue("Name"));
        String diagnosticPartNumber = element.attributeValue("DiagnosticPartNumber").replace(ConstantEnum.EMPTY, "");
        ecuSwDTO.setDiagnosticPartNumber(diagnosticPartNumber);
        ecuSwDTO.setType(element.attributeValue("Type"));
        return ecuSwDTO;
    }

    private DiagResItemGroupDto a(Element element, String diagnosticPartNumber) {
        DiagResItemGroupDto resItemGroupDto = new DiagResItemGroupDto();
        Element sessionsEle = element.element("Sessions");
        if (ObjectUtils.isNotEmpty(sessionsEle)) {
            List<Element> sessionEleList = sessionsEle.elements();
            resItemGroupDto.setSessionList(a(sessionEleList, diagnosticPartNumber));
        }
        resItemGroupDto.setDataIdentifierId(element.attributeValue("ID"));
        resItemGroupDto.setName(element.attributeValue("Name"));
        resItemGroupDto.setSize(element.attributeValue("Size"));
        resItemGroupDto.setSecurityAccessRefs(element.elementText("SecurityAccessRefs"));
        if (null != diagnosticPartNumber) {
            resItemGroupDto.setDiagnosticPartNumber(diagnosticPartNumber);
        }
        return resItemGroupDto;
    }

    private DiaSubfunctionDTO b(Element element, String diagnosticPartNumber) {
        DiaSubfunctionDTO dto = new DiaSubfunctionDTO();
        dto.setDiagnosticPartNumber(diagnosticPartNumber);
        dto.setSubfunctionId(element.attributeValue("ID"));
        dto.setName(element.attributeValue("Name"));
        return dto;
    }

    private DiaRoutineIdentifierDTO a(Element element, DiaSubfunctionDTO subfunctionDto) {
        DiaRoutineIdentifierDTO dto = new DiaRoutineIdentifierDTO();
        dto.setDiagnosticPartNumber(subfunctionDto.getDiagnosticPartNumber());
        dto.setSubfunctionIdStr(subfunctionDto.getSubfunctionId());
        dto.setSubfunctionName(subfunctionDto.getName());
        dto.setRoutineIdentifierIdStr(element.attributeValue("ID"));
        dto.setRoutineIdentifierName(element.attributeValue("Name"));
        return dto;
    }

    private List<DiaSessionDTO> a(List<Element> elementList, String diagnosticPartNumber) {
        ArrayList<DiaSessionDTO> list = new ArrayList<>();
        for (Element element : elementList) {
            DiaSessionDTO sessionDTO = new DiaSessionDTO();
            sessionDTO.setSessionName(element.attributeValue("Name"));
            sessionDTO.setSessionId(element.attributeValue("ID"));
            sessionDTO.setS3Server(element.attributeValue("S3Server"));
            sessionDTO.setP2ServerMaxDefault(element.attributeValue("P2ServerMax_default"));
            sessionDTO.setP2StarServerMaxDefault(element.attributeValue("P2StarServerMax_default"));
            sessionDTO.setP4ServerMaxDefault(element.attributeValue("P4ServerMax_default"));
            sessionDTO.setDeltaP2VehicleSingleframe(element.attributeValue("DeltaP2Vehicle_singleframe"));
            sessionDTO.setDeltaP2VehicleMultiframe(element.attributeValue("DeltaP2Vehicle_multiframe"));
            sessionDTO.setDeltaP2VehicleMultichannel(element.attributeValue("DeltaP2Vehicle_multichannel"));
            sessionDTO.setSupportOfQueuedRequests(element.attributeValue("SupportOfQueuedRequests"));
            sessionDTO.setMaxNumberOfDidsForReadDid(element.attributeValue("MaxNumberOfDIDsForReadDID"));
            sessionDTO.setP2ServerMax(element.attributeValue("P2ServerMax"));
            sessionDTO.setP4ServerMax(element.attributeValue("P4ServerMax"));
            sessionDTO.setDiagnosticPartNumber(diagnosticPartNumber);
            list.add(sessionDTO);
        }
        return list;
    }

    private List<DiagResItemDto> a(List<Element> elementList, String diagnosticPartNumber, Map<String, Object> responseItemNameMap) {
        ArrayList<DiagResItemDto> list = new ArrayList<>();
        for (Element element : elementList) {
            DiagResItemDto responseItemDTO = new DiagResItemDto();
            String name = element.attributeValue("Name");
            responseItemDTO.setName(name);
            responseItemNameMap.put(name, name);
            responseItemDTO.setSort(Integer.valueOf(responseItemNameMap.size()));
            responseItemDTO.setInDataType(element.attributeValue("InDataType"));
            responseItemDTO.setOutDataType(element.attributeValue("OutDataType"));
            responseItemDTO.setOffset(element.attributeValue("Offset"));
            responseItemDTO.setSize(element.attributeValue("Size"));
            responseItemDTO.setResultPrecision(element.attributeValue("ResultPrecision"));
            responseItemDTO.setFormula(element.elementText("Formula"));
            String unit = element.elementText("Unit");
            responseItemDTO.setUnit(unit);
            String compareValue = element.elementText("CompareValue");
            if (StringUtils.isNotBlank(compareValue)) {
                responseItemDTO.setCompareValue(compareValue.replaceAll(ConstantEnum.COMMA, ConstantEnum.POINT));
            }
            list.add(responseItemDTO);
        }
        return list;
    }

    private List<DiaDtcDTO> b(List<Element> elementList, String diagnosisPartNum) {
        List<DiaDtcDTO> list = new ArrayList<>();
        for (Element element : elementList) {
            DiaDtcDTO dtcDTO = new DiaDtcDTO();
            dtcDTO.setDtcId(element.attributeValue("ID").substring(2));
            dtcDTO.setName(element.attributeValue("Name"));
            dtcDTO.setSwLabel(element.attributeValue("SWLabel"));
            dtcDTO.setConfirmedDtcLimit(element.attributeValue("ConfirmedDTCLimit"));
            dtcDTO.setUnConfirmedDtcLimit(element.attributeValue("UnconfirmedDTCLimit"));
            dtcDTO.setAgedDtcLimit(element.attributeValue("AgedDTCLimit"));
            dtcDTO.setAgingCounter(element.attributeValue("AgingCounter"));
            dtcDTO.setWarningIndicator(element.attributeValue("WarningIndicator"));
            dtcDTO.setDtceventPriority(element.attributeValue("DTCEventPriority"));
            dtcDTO.setFailedThreshold(element.attributeValue("FailedThreshold"));
            dtcDTO.setPassedThreshold(element.attributeValue("PassedThreshold"));
            dtcDTO.setIncrementStepSize(element.attributeValue("IncrementStepSize"));
            dtcDTO.setDecrementStepSize(element.attributeValue("DecrementStepSize"));
            dtcDTO.setJumpDown(element.attributeValue("JumpDown"));
            dtcDTO.setDiagnosticPartNumber(diagnosisPartNum);
            list.add(dtcDTO);
        }
        return list;
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public List<DiaTreeNode> getElement(DiaTreeNode node) throws Exception {
        String type = node.getType();
        if (!EnumUtils.isValidEnum(DiagnosisTypeEnum.class, type)) {
            type = "OTHER";
        }
        List<DiaTreeNode> list = null;
        switch (DiagnosisTypeEnum.valueOf(type)) {
            case Root:
                list = getProject();
                break;
            case Project:
                list = getSystem();
                break;
            case System:
                list = getEcu();
                break;
            case ECU:
                list = b(node);
                break;
            case SW:
                list = c(node);
                break;
            case Service:
                list = d(node);
                break;
            case DataIdentifier:
                list = e(node);
                break;
            case DataParameter:
                list = f(node);
                break;
        }
        return list;
    }

    @Override // com.geely.gnds.dsa.service.SddbService
    public List<DiaEcuSwDTO> getTargetEcu(String address) throws Exception {
        List<DiaEcuSwDTO> result = new ArrayList<>();
        List<Element> ecuElementList = this.eH.getEcuEleList();
        ecuElementList.stream().filter(e -> {
            return e.attributeValue("address").equals(address);
        }).findFirst().ifPresent(t -> {
            String ecuName = t.attributeValue("Name");
            List<Element> swList = t.element("SWs").elements();
            for (Element sw : swList) {
                DiaEcuSwDTO ecuSw = a(sw, ecuName, address);
                result.add(ecuSw);
            }
        });
        return result;
    }

    private List<DiaTreeNode> getProject() throws Exception {
        List<DiaTreeNode> list = new ArrayList<>();
        log.info("获取sddb节点-获取Project");
        System.out.println("获取sddb节点-获取Project");
        DiaTreeNode node = new DiaTreeNode();
        Document document = this.eH.getDocument();
        Element root = document.getRootElement();
        node.setName(root.attributeValue("Name"));
        node.setType(DiagnosisTypeEnum.Project.name());
        node.setLeaf(false);
        list.add(node);
        return list;
    }

    private List<DiaTreeNode> getSystem() throws Exception {
        List<DiaTreeNode> list = new ArrayList<>();
        log.info("获取sddb节点-获取System");
        System.out.println("获取sddb节点-获取System");
        Document document = this.eH.getDocument();
        Element root = document.getRootElement();
        List<Element> elementList = root.elements("System");
        for (Element e : elementList) {
            DiaTreeNode node = new DiaTreeNode();
            node.setName(e.attributeValue("Name"));
            node.setType(DiagnosisTypeEnum.System.name());
            node.setLeaf(false);
            list.add(node);
        }
        return list;
    }

    private List<DiaTreeNode> getEcu() throws Exception {
        List<DiaTreeNode> list = new ArrayList<>();
        log.info("获取sddb节点-获取ECU");
        System.out.println("获取sddb节点-获取ECU");
        List<Element> ecuEleList = getEcuEleList();
        for (Element e : ecuEleList) {
            DiaTreeNode node = new DiaTreeNode();
            node.setEcuName(e.attributeValue("Name"));
            node.setName(e.attributeValue("Name"));
            node.setAddress(e.attributeValue("address"));
            node.setType(DiagnosisTypeEnum.ECU.name());
            node.setLeaf(false);
            list.add(node);
        }
        return list;
    }

    private List<Element> getEcuEleList() throws Exception {
        log.info("获取sddb节点-获取ECUEle");
        System.out.println("获取sddb节点-获取ECUEle");
        Document document = this.eH.getDocument();
        Element root = document.getRootElement();
        Element systemEle = root.element("System");
        Element ecusEle = systemEle.element("ECUs");
        List<Element> ecuEleList = ecusEle.elements();
        return ecuEleList;
    }

    private List<DiaTreeNode> b(DiaTreeNode paramNode) throws Exception {
        List<DiaTreeNode> list = new ArrayList<>();
        log.info("获取sddb节点-获取SW-获取获取目标ECU");
        System.out.println("获取sddb节点-获取SW-获取目标ECU");
        Element ecuEle = g(paramNode.getEcuName(), paramNode.getAddress());
        if (ObjectUtils.isEmpty(ecuEle)) {
            return list;
        }
        log.info("获取sddb节点-获取SW-获取目标ECU下的SW");
        System.out.println("获取sddb节点-获取SW-目标ECU下的SW");
        Element swsEl = ecuEle.element("SWs");
        List<Element> swEleList = swsEl.elements();
        for (Element e : swEleList) {
            DiaTreeNode node = new DiaTreeNode();
            node.setEcuName(paramNode.getEcuName());
            node.setAddress(paramNode.getAddress());
            node.setName(e.attributeValue("Name"));
            node.setSwName(e.attributeValue("Name"));
            node.setDiagnosticPartNum(e.attributeValue("DiagnosticPartNumber").replaceAll(ConstantEnum.EMPTY, ""));
            node.setType(DiagnosisTypeEnum.SW.name());
            node.setLeaf(false);
            list.add(node);
        }
        return list;
    }

    private List<DiaTreeNode> c(DiaTreeNode paramNode) throws Exception {
        List<DiaTreeNode> list = new ArrayList<>();
        log.info("获取sddb节点-获取Service-获取目标诊断零件号");
        System.out.println("获取sddb节点-获取Service-获取目标诊断零件号");
        Element swEle = g(paramNode.getEcuName(), paramNode.getAddress(), paramNode.getDiagnosticPartNum());
        log.info("获取sddb节点-获取service-获取目标诊断零件号下的service");
        System.out.println("获取sddb节点-获取service-获取目标诊断零件号下的service");
        List<Element> serviceEleList = swEle.element("Services").elements();
        for (Element s : serviceEleList) {
            DiaTreeNode node = new DiaTreeNode();
            node.setEcuName(paramNode.getEcuName());
            node.setAddress(paramNode.getAddress());
            node.setSwName(paramNode.getSwName());
            node.setDiagnosticPartNum(paramNode.getDiagnosticPartNum());
            node.setServiceId(s.attributeValue("ID"));
            node.setServiceName(s.attributeValue("Name"));
            node.setName(s.attributeValue("Name"));
            node.setStrId(s.attributeValue("ID"));
            node.setType(DiagnosisTypeEnum.Service.name());
            node.setLeaf(false);
            list.add(node);
        }
        return list;
    }

    private List<DiaTreeNode> d(DiaTreeNode paramNode) throws Exception {
        List<DiaTreeNode> list = new ArrayList<>();
        log.info("获取sddb节点-获取Subfunction或者DataIdentifier-获取目标诊断零件号");
        System.out.println("获取sddb节点-获取Subfunction或者DataIdentifier-获取目标诊断零件号");
        Element swEle = g(paramNode.getEcuName(), paramNode.getAddress(), paramNode.getDiagnosticPartNum());
        log.info("获取sddb节点-获取Subfunction或者DataIdentifier-获取目标service");
        System.out.println("获取sddb节点-获取Subfunction或者DataIdentifier-获取目标service");
        Element serviceEle = b(paramNode.getServiceId(), swEle);
        log.info("获取sddb节点-获取Subfunction或者DataIdentifier-获取Subfunction");
        System.out.println("获取sddb节点-获取Subfunction或者DataIdentifier-获取Subfunction");
        Element subfunctionsEle = serviceEle.element("Subfunctions");
        if (ObjectUtils.isNotEmpty(subfunctionsEle)) {
            List<Element> subfunctionEleList = subfunctionsEle.elements();
            for (Element sub : subfunctionEleList) {
                DiaTreeNode node = new DiaTreeNode();
                node.setEcuName(paramNode.getEcuName());
                node.setSwName(paramNode.getSwName());
                node.setAddress(paramNode.getAddress());
                node.setDiagnosticPartNum(paramNode.getDiagnosticPartNum());
                node.setServiceName(paramNode.getServiceName());
                node.setServiceId(paramNode.getServiceId());
                node.setStrId(sub.attributeValue("ID"));
                node.setName(sub.attributeValue("Name"));
                node.setType(DiagnosisTypeEnum.Subfunction.name());
                node.setLeaf(false);
                list.add(node);
            }
        }
        log.info("获取sddb节点-获取Subfunction或者DataIdentifier-获取DataIdentifier");
        System.out.println("获取sddb节点-获取Subfunction或者DataIdentifier-获取DataIdentifier");
        Element dataIdentifiersEle = serviceEle.element("DataIdentifiers");
        if (ObjectUtils.isNotEmpty(dataIdentifiersEle)) {
            List<Element> dataIdentifierEleList = dataIdentifiersEle.elements();
            for (Element d : dataIdentifierEleList) {
                DiaTreeNode node2 = new DiaTreeNode();
                node2.setEcuName(paramNode.getEcuName());
                node2.setSwName(paramNode.getSwName());
                node2.setAddress(paramNode.getAddress());
                node2.setDiagnosticPartNum(paramNode.getDiagnosticPartNum());
                node2.setServiceName(paramNode.getServiceName());
                node2.setServiceId(paramNode.getServiceId());
                node2.setStrId(d.attributeValue("ID"));
                node2.setName(d.attributeValue("Name"));
                node2.setType(DiagnosisTypeEnum.DataIdentifier.name());
                node2.setLeaf(false);
                list.add(node2);
            }
        }
        return list;
    }

    private List<DiaTreeNode> e(DiaTreeNode paramNode) throws Exception {
        List<DiaTreeNode> list = new ArrayList<>();
        log.info("获取sddb节点-获取ResponseItem或者DataParameter-获取目标诊断零件号");
        System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-获取目标诊断零件号");
        Element swEle = g(paramNode.getEcuName(), paramNode.getAddress(), paramNode.getDiagnosticPartNum());
        log.info("获取sddb节点-获取ResponseItem或者DataParameter-获取目标service");
        System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-获取目标service");
        Element serviceEle = b(paramNode.getServiceId(), swEle);
        log.info("获取sddb节点-获取ResponseItem或者DataParameter-获取目标DataIdentifier");
        System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-获取目标DataIdentifier");
        Element dataIdentifierEle = a(paramNode.getStrId(), serviceEle);
        if (ObjectUtils.isNotEmpty(dataIdentifierEle)) {
            log.info("获取sddb节点-获取ResponseItem或者DataParameter-获取DataParameter");
            System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-获取DataParameter");
            Element dataParametersEle = dataIdentifierEle.element("DataParameters");
            if (ObjectUtils.isNotEmpty(dataParametersEle)) {
                List<Element> dataParameterEleList = dataParametersEle.elements();
                for (Element e : dataParameterEleList) {
                    DiaTreeNode node = new DiaTreeNode();
                    node.setEcuName(paramNode.getEcuName());
                    node.setSwName(paramNode.getSwName());
                    node.setAddress(paramNode.getAddress());
                    node.setDiagnosticPartNum(paramNode.getDiagnosticPartNum());
                    node.setServiceName(paramNode.getServiceName());
                    node.setServiceId(paramNode.getServiceId());
                    node.setDataIdentifierId(paramNode.getStrId());
                    node.setStrId(e.attributeValue("ID"));
                    node.setName(e.attributeValue("Name"));
                    node.setType(DiagnosisTypeEnum.DataParameter.name());
                    node.setLeaf(false);
                    list.add(node);
                }
            }
        }
        return list;
    }

    private List<DiaTreeNode> f(DiaTreeNode paramNode) throws Exception {
        List<DiaTreeNode> list = new ArrayList<>();
        log.info("获取sddb节点-获取ResponseItem或者DataParameter-获取目标诊断零件号");
        System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-获取目标诊断零件号");
        Element swEle = g(paramNode.getEcuName(), paramNode.getAddress(), paramNode.getDiagnosticPartNum());
        log.info("获取sddb节点-获取ResponseItem或者DataParameter-获取目标service");
        System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-获取目标service");
        Element serviceEle = b(paramNode.getServiceId(), swEle);
        log.info("获取sddb节点-获取ResponseItem或者DataParameter-获取目标DataIdentifier");
        System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-获取目标DataIdentifier");
        Element dataIdentifierEle = a(paramNode.getDataIdentifierId(), serviceEle);
        if (ObjectUtils.isNotEmpty(dataIdentifierEle)) {
            log.info("获取sddb节点-获取ResponseItem或者DataParameter-获取DataParameter");
            System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-获取DataParameter");
            Element dataParametersEle = dataIdentifierEle.element("DataParameters");
            if (ObjectUtils.isNotEmpty(dataParametersEle)) {
                List<Element> dataParameterEleList = dataParametersEle.elements();
                a(dataParameterEleList, paramNode, list);
            }
        }
        return list;
    }

    private void a(List<Element> dataParameterEleList, DiaTreeNode paramNode, List<DiaTreeNode> list) {
        for (Element e : dataParameterEleList) {
            String id = e.attributeValue("ID");
            String name = e.attributeValue("Name");
            Element dataParametersEle = e.element("DataParameters");
            if (id.equals(paramNode.getStrId()) && name.equals(paramNode.getName())) {
                if (ObjectUtils.isNotEmpty(dataParametersEle)) {
                    List<Element> newDataParameterEleList = dataParametersEle.elements();
                    for (Element ele : newDataParameterEleList) {
                        DiaTreeNode node = new DiaTreeNode();
                        node.setEcuName(paramNode.getEcuName());
                        node.setSwName(paramNode.getSwName());
                        node.setAddress(paramNode.getAddress());
                        node.setDiagnosticPartNum(paramNode.getDiagnosticPartNum());
                        node.setServiceName(paramNode.getServiceName());
                        node.setServiceId(paramNode.getServiceId());
                        node.setDataIdentifierId(paramNode.getDataIdentifierId());
                        node.setStrId(ele.attributeValue("ID"));
                        node.setName(ele.attributeValue("Name"));
                        node.setType(DiagnosisTypeEnum.DataParameter.name());
                        node.setLeaf(false);
                        list.add(node);
                    }
                }
            } else if (ObjectUtils.isNotEmpty(dataParametersEle)) {
                log.info("获取sddb节点-获取ResponseItem或者DataParameter-递归获取DataParameter");
                System.out.println("获取sddb节点-获取ResponseItem或者DataParameter-递归获取DataParameter");
                List<Element> newDataParameterEleList2 = dataParametersEle.elements();
                a(newDataParameterEleList2, paramNode, list);
            }
        }
    }

    private Element a(String strId, Element serviceEle) {
        Element dataIdentifiersEle = serviceEle.element("DataIdentifiers");
        Element dataIdentifierEle = null;
        if (ObjectUtils.isNotEmpty(dataIdentifiersEle)) {
            List<Element> dataIdentifierEleList = dataIdentifiersEle.elements();
            Iterator<Element> it = dataIdentifierEleList.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                Element d = it.next();
                String id = d.attributeValue("ID");
                if (id.equals(strId)) {
                    dataIdentifierEle = d;
                    break;
                }
            }
        }
        return dataIdentifierEle;
    }

    private Element b(String targetServiceId, Element swEle) {
        Element serviceEle = null;
        List<Element> serviceEleList = swEle.element("Services").elements();
        Iterator<Element> it = serviceEleList.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Element s = it.next();
            String serviceId = s.attributeValue("ID");
            if (serviceId.equals(targetServiceId)) {
                serviceEle = s;
                break;
            }
        }
        return serviceEle;
    }

    private Element g(String ecuName, String address) throws Exception {
        log.info("获取sddb节点-获取目标ECU：{}", address);
        System.out.println("获取sddb节点--获取目标诊断零件号:" + address);
        Element ecuEle = null;
        List<Element> ecuEleList = getEcuEleList();
        Iterator<Element> it = ecuEleList.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Element ecu = it.next();
            String name = ecu.attributeValue("Name");
            String ecuAddress = ecu.attributeValue("address");
            if (name.equals(ecuName) && ecuAddress.equals(address)) {
                ecuEle = ecu;
                break;
            }
        }
        return ecuEle;
    }

    private Element g(String ecuName, String address, String diagPartNum) throws Exception {
        log.info("获取sddb节点-获取目标诊断零件号-{}-{}", address, diagPartNum);
        System.out.println("获取sddb节点-获取Subfunction或者DataIdentifier-" + address + "-" + diagPartNum);
        Element targetEcuEle = g(ecuName, address);
        Element swEle = null;
        List<Element> swEleList = targetEcuEle.element("SWs").elements();
        Iterator<Element> it = swEleList.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Element sw = it.next();
            String diagnosticPartNum = sw.attributeValue("DiagnosticPartNumber").replaceAll(ConstantEnum.EMPTY, "");
            if (diagnosticPartNum.equals(diagPartNum)) {
                swEle = sw;
                break;
            }
        }
        return swEle;
    }
}
