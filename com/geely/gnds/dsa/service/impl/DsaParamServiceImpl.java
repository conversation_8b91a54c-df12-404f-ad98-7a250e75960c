package com.geely.gnds.dsa.service.impl;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.enums.LanguageEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.DsaDataAnalysisService;
import com.geely.gnds.dsa.service.DsaParamService;
import com.geely.gnds.dsa.service.SddbCacheService;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.dsa.service.UdsService;
import com.geely.gnds.dsa.utils.DsaStringUtils;
import com.geely.gnds.ruoyi.common.constant.ScheduleConstants;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: DsaParamServiceImpl.class */
public class DsaParamServiceImpl implements DsaParamService {
    private static final Logger log = LoggerFactory.getLogger(DsaParamServiceImpl.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @Autowired
    private SddbService eo;

    @Autowired
    private DsaDataAnalysisService eE;

    @Autowired
    private UdsService es;

    @Autowired
    private SddbCacheService eH;

    @Override // com.geely.gnds.dsa.service.DsaParamService
    public void readParam(String diagnosticPartNumber, String serviceId, String command, String response, String address, String vin) throws Exception {
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        FdDsaLogger fdDsaDsaTxtLogger = client.getFdDsaLogger();
        List<String> readList = DsaStringUtils.a(command.substring(2), 4);
        List<DiagResItemGroupDto> list = this.eo.getDidByDiagnosisNumAndDid(diagnosticPartNumber, serviceId, readList);
        if (CollectionUtils.isEmpty(list)) {
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
                logger.write(DsaLogTypeEnum.ERROR.getValue(), LanguageEnum.NOT_DIAGNOSTIC_DATA.valueByLanguage());
            });
            log.error("DSA获取参数列表失败");
        } else {
            List<DiagResItemParseResultDto> result = this.eE.analysisDidData(vin, list, response);
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger2 -> {
                logger2.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
                if (CollectionUtils.isEmpty(result)) {
                    logger2.write(DsaLogTypeEnum.ERROR.getValue(), "未解析到数据");
                } else {
                    StringBuilder builder = new StringBuilder();
                    result.forEach(r -> {
                        builder.append(r.toString()).append(System.getProperty("line.separator"));
                    });
                    logger2.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), builder.toString());
                }
                logger2.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
            });
        }
    }

    @Override // com.geely.gnds.dsa.service.DsaParamService
    public String analysisParam(String serviceId, String command, String address, String vin) throws Exception {
        log.info("ECU识别-vin:{},ECU地址：{}，诊断零件号：{}，服务：{}，指令：{}");
        String response = this.es.udsDataNoLog(address, command, vin);
        String sddbFilePath = this.eH.getSddbFilePath();
        if (ObjectUtils.isNotEmpty(sddbFilePath)) {
            String diagPartNum = this.eo.getDiagPartNumByAddress(address);
            if (StringUtils.isBlank(diagPartNum)) {
                log.error("DSA获取诊断零件号为空");
                if (StringUtils.isNotBlank(response)) {
                    return response.substring(6);
                }
                return response;
            }
            String did = command.replaceAll(ScheduleConstants.MISFIRE_FIRE_AND_PROCEED, "").replaceAll(ConstantEnum.EMPTY, "");
            List<DiagResItemGroupDto> list = this.eo.getDidByDiagnosisNumAndDid(diagPartNum, serviceId, Arrays.asList(did));
            if (CollectionUtils.isEmpty(list)) {
                log.error("DSA获取参数列表失败");
                if (StringUtils.isNotBlank(response)) {
                    return response.substring(6);
                }
                return response;
            }
            List<DiagResItemParseResultDto> result = this.eE.analysisDidData(vin, list, response);
            if (!CollectionUtils.isEmpty(result)) {
                String value = result.get(0).getValue();
                if (ObjectUtils.isNotEmpty(value)) {
                    return value;
                }
            }
        }
        if (StringUtils.isNotBlank(response)) {
            return response.substring(6);
        }
        return response;
    }
}
