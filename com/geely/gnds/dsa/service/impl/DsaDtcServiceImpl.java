package com.geely.gnds.dsa.service.impl;

import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.dto.DiaDtcDTO;
import com.geely.gnds.dsa.dto.DsaDtcRespDTO;
import com.geely.gnds.dsa.dto.DsaDtcStatusMaskDTO;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.DsaDataAnalysisService;
import com.geely.gnds.dsa.service.DsaDtcService;
import com.geely.gnds.dsa.service.SddbService;
import com.geely.gnds.dsa.service.UdsService;
import com.geely.gnds.tester.seq.SingletonManager;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: DsaDtcServiceImpl.class */
public class DsaDtcServiceImpl implements DsaDtcService {
    private static final Logger log = LoggerFactory.getLogger(DsaParamServiceImpl.class);
    private SingletonManager manager = SingletonManager.getInstance();

    @Autowired
    private SddbService eo;

    @Autowired
    private DsaDataAnalysisService eE;

    @Autowired
    private UdsService es;

    @Override // com.geely.gnds.dsa.service.DsaDtcService
    public void readDtc(String diagnosticPartNumber, String serviceId, String response, String address, String vin) throws Exception {
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        FdDsaLogger fdDsaDsaTxtLogger = client.getFdDsaLogger();
        List<DiaDtcDTO> dtcList = this.eo.getDtcList(diagnosticPartNumber);
        if (CollectionUtils.isEmpty(dtcList)) {
            log.error("DSA获取DTC列表失败");
        } else {
            List<DsaDtcRespDTO> dsaDtcRespList = this.eE.analysisDtcStatusMask(vin, dtcList, response);
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
                logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
                if (response.length() >= 6) {
                    logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), response.substring(4, 6) + "                      DTC Status Availability Mask");
                }
                if (CollectionUtils.isEmpty(dsaDtcRespList)) {
                    logger.write(DsaLogTypeEnum.ERROR.getValue(), "未解析到数据");
                } else {
                    dsaDtcRespList.forEach(r -> {
                        logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
                        logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), r.getDtcId() + "                        " + r.getName());
                        DsaDtcStatusMaskDTO dtcStatus = r.getDtcStatus();
                        if (dtcStatus.getTestFailed().booleanValue()) {
                            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "01 Test failed");
                        }
                        if (dtcStatus.getTestFailedThisOperationCycle().booleanValue()) {
                            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "02 Test failed during this monitoring or operational cycle");
                        }
                        if (dtcStatus.getPendingDtc().booleanValue()) {
                            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "04 Pending DTC");
                        }
                        if (dtcStatus.getConfirmedDtc().booleanValue()) {
                            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "08 Confirmed DTC");
                        }
                        if (dtcStatus.getTestNotCompletedSinceLastClear().booleanValue()) {
                            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "10 Test not completed");
                        }
                        if (dtcStatus.getTestFailedSinceLastClear().booleanValue()) {
                            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "20 Test failed since last clear");
                        }
                        if (dtcStatus.getTestNotCompletedThisOperationCycle().booleanValue()) {
                            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "40 Test not completed this operational cycle");
                        }
                        if (dtcStatus.getWarningIndicatorRequested().booleanValue()) {
                            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "80 Warning indicator requested");
                        }
                        logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
                    });
                }
                logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
            });
        }
    }
}
