package com.geely.gnds.dsa.service.impl;

import cn.hutool.core.date.DateUtil;
import com.geely.gnds.doip.client.pcap.PcapParam;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.DsaLogService;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: DsaLogServiceImpl.class */
public class DsaLogServiceImpl implements DsaLogService {
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(DsaLogServiceImpl.class);

    @Autowired
    private TokenService tokenService;
    public static final String eF = "log";
    public static final String eG = "pcap";

    @Override // com.geely.gnds.dsa.service.DsaLogService
    public void startLog(String vin, String logPath) throws Exception {
        FdTcpClient fdTcpClient = this.manager.getFdTcpClientAndDsa(vin);
        if (fdTcpClient == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115));
        }
        File file = new File(logPath);
        if (file.exists()) {
            file.delete();
        }
        FdDsaLogger dsaTxtLogger = new FdDsaLogger(logPath, vin);
        fdTcpClient.setFdDsaDsaTxtLogger(dsaTxtLogger);
        dsaTxtLogger.writeToLog("#");
        dsaTxtLogger.writeToLog("# " + vin);
        dsaTxtLogger.writeToLog("# Program version:1.36.0.2");
        dsaTxtLogger.writeToLog("# Create Time:" + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS2, new Date()));
    }

    @Override // com.geely.gnds.dsa.service.DsaLogService
    public String getLogPath(String type) throws Exception {
        String childPath;
        File base = new File(ConstantEnum.POINT);
        if (eF.equalsIgnoreCase(type)) {
            childPath = base.getCanonicalPath() + File.separator + "GNDS_" + DateUtils.parseDateToStr(DateUtils.YYYYMMDD_HHMMSS, new Date()) + ConstantEnum.POINT + eF;
        } else if (eG.equalsIgnoreCase(type)) {
            childPath = base.getCanonicalPath() + File.separator + "GNDS_SWDL" + DateUtils.parseDateToStr(DateUtils.YYYYMMDD_HHMMSS, new Date()) + ConstantEnum.POINT + eG;
        } else {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00222));
        }
        return childPath;
    }

    public static void main(String[] args) throws IOException {
        File base = new File(ConstantEnum.POINT);
        System.out.println(base.getCanonicalPath() + File.separator);
    }

    @Override // com.geely.gnds.dsa.service.DsaLogService
    public void addComment(String vin, String content) throws Exception {
        FdTcpClient fdTcpClient = this.manager.getFdTcpClientAndDsa(vin);
        if (fdTcpClient == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115));
        }
        FdDsaLogger dsaTxtLogger = fdTcpClient.getFdDsaLogger();
        if (dsaTxtLogger == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00211));
        }
        dsaTxtLogger.write(DsaLogTypeEnum.COMMENT.getValue(), content);
    }

    @Override // com.geely.gnds.dsa.service.DsaLogService
    public void startPcapLog(String vin, String logPath) throws Exception {
        FdTcpClient fdTcpClient = this.manager.getFdTcpClientAndDsa(vin);
        if (fdTcpClient == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115));
        }
        File file = new File(logPath);
        if (file.exists()) {
            file.delete();
        }
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String start = DateUtil.format(new Date(), DateUtils.YYYYMMDDHHMMSSSSS);
        String username = loginUser.getUsername();
        PcapParam pcapParam = new PcapParam(logPath, vin, username, start);
        fdTcpClient.startPcap(pcapParam);
    }
}
