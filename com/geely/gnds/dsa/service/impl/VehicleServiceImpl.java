package com.geely.gnds.dsa.service.impl;

import cn.hutool.core.date.DateUtil;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.dro.document.root.DroResult;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.message.DoipUdpVehicleAnnouncementMessage;
import com.geely.gnds.doip.client.pcap.PcapThread;
import com.geely.gnds.doip.client.pcap.PcapWrite;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.doip.client.txt.FdTxtLogger;
import com.geely.gnds.doip.client.udp.FdDoipUdpReceiver;
import com.geely.gnds.doip.client.udp.UdpClient;
import com.geely.gnds.doip.client.xml.FdXmlLogger;
import com.geely.gnds.dsa.service.PinCodeService;
import com.geely.gnds.dsa.service.VehicleService;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.common.TesterContext;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dto.DsaPermissionDTO;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SeqManager;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.impl.ConnectedServiceImpl;
import com.geely.gnds.tester.util.IpUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import java.io.IOException;
import java.net.Socket;
import java.text.MessageFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import javax.servlet.ServletContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.WebApplicationContext;

@Service
/* loaded from: VehicleServiceImpl.class */
public class VehicleServiceImpl implements VehicleService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private EhcacheClient ehcacheClient;

    @Autowired
    private PcapWrite eV;

    @Autowired
    private PinCodeService en;
    private SingletonManager manager = SingletonManager.getInstance();
    private static final Logger log = LoggerFactory.getLogger(VehicleServiceImpl.class);
    private static final String eW = "Vehicle({0}:{1}) => VIN:{2}; EID:{3}; GID:{4}; TA:{5}";

    @Value("${doip.broadcast.address}")
    private String broadcast;

    @Value("${tester.tenantName}")
    private String tenantName;

    @Override // com.geely.gnds.dsa.service.VehicleService
    public List<VehicleDto> query(String permissionFlag) throws Exception {
        final List<VehicleDto> vehicles = new LinkedList<>();
        log.debug("进入DSA搜车");
        System.currentTimeMillis();
        final DoipUtil doipUtil = DoipUtil.getInstance();
        UdpClient client = new UdpClient(this.broadcast, 13400, null);
        final SingletonManager instance = SingletonManager.getInstance();
        final List<String> vins = new LinkedList<>();
        FdDoipUdpReceiver receiver = new FdDoipUdpReceiver() { // from class: com.geely.gnds.dsa.service.impl.VehicleServiceImpl.1
            @Override // com.geely.gnds.doip.client.udp.FdDoipUdpReceiver, com.geely.gnds.doip.client.udp.FdDoipUdpReceiveListener
            public void onDoipUdpVehicleAnnouncementMessage(DoipUdpVehicleAnnouncementMessage message, String udpServerIp, int udpServerPort) throws Exception {
                VehicleServiceImpl.log.info("搜索到的车辆信息---->{}", MessageFormat.format(VehicleServiceImpl.eW, udpServerIp, "" + udpServerPort, new String(message.getVin()), doipUtil.toHexString(message.getEid()), doipUtil.toHexString(message.getGid()), "" + message.getLogicalAddress()));
                m();
                String vinSearch = new String(message.getVin());
                String vin = SingletonManager.getContrastVin(vinSearch);
                VehicleDto vehicle = new VehicleDto();
                vehicle.setIp(udpServerIp);
                vehicle.setNeedTls(message.isNeedTls());
                vehicle.setPort(udpServerPort);
                vehicle.setConnectType(IpUtils.getConnectType(udpServerIp));
                vehicle.setSourceAddress(3712);
                vehicle.setGatewayAddress(message.getLogicalAddress());
                vehicle.setVin(vin);
                vehicle.setEid(doipUtil.toHexString(message.getEid()));
                vehicle.setGid(doipUtil.toHexString(message.getGid()));
                vehicle.setConnect(Boolean.valueOf(instance.getFdTcpClient(vin) != null));
                VehicleDto vehicle2 = instance.getVehicle(vin);
                if (vehicle2 != null) {
                    vehicle.setVbfswdl(vehicle2.isVbfswdl());
                }
                if (!vins.contains(vin)) {
                    vins.add(vin);
                    vehicles.add(vehicle);
                }
                synchronized (ConnectedServiceImpl.class) {
                    long now = System.currentTimeMillis() / 1000;
                    String key = String.valueOf(now);
                    String keyVehicle = "vehicle" + key;
                    Set<String> vins2 = (Set) VehicleServiceImpl.this.ehcacheClient.w(key);
                    List<VehicleDto> vehicleList = (List) VehicleServiceImpl.this.ehcacheClient.w(keyVehicle);
                    if (vins2 != null) {
                        vins2.add(vin);
                    } else {
                        vins2 = new HashSet<>();
                        vins2.add(vin);
                    }
                    if (vehicleList != null) {
                        vehicleList.add(vehicle);
                    } else {
                        vehicleList = new LinkedList<>();
                        vehicleList.add(vehicle);
                    }
                    vehicleList.add(vehicle);
                    VehicleServiceImpl.log.info("query:{},vehicleList:{}", keyVehicle, vehicleList);
                    VehicleServiceImpl.log.info("query:{},vin:{}", key, vin);
                    VehicleServiceImpl.this.ehcacheClient.a(key, 6, vins2);
                    VehicleServiceImpl.this.ehcacheClient.a(keyVehicle, 8, vehicleList);
                }
            }
        };
        client.a(receiver);
        b(vehicles, vins);
        if (!CollectionUtils.isEmpty(vins) && "1".equals(permissionFlag)) {
            String res = this.cloud.k(vins);
            log.info("云端返回车辆权限信息:{}", res);
            if (StringUtils.isNotBlank(res)) {
                Map<String, DsaPermissionDTO> map = ObjectMapperUtils.jsonStr2Map(res, DsaPermissionDTO.class);
                if (!CollectionUtils.isEmpty(map)) {
                    for (VehicleDto vehicle : vehicles) {
                        String vin = vehicle.getVin();
                        if (map.containsKey(vin)) {
                            DsaPermissionDTO dsaPermissionDTO = map.get(vin);
                            vehicle.setHasConnected(dsaPermissionDTO.isHasConnected());
                            vehicle.setHasPermission(dsaPermissionDTO.isHasPermission());
                        } else {
                            vehicle.setHasConnected(false);
                            vehicle.setHasPermission(false);
                        }
                    }
                }
            }
        }
        return vehicles;
    }

    private void b(List<VehicleDto> vehicles, List<String> vins) throws Exception {
        log.info("后台定时任务搜车setBroadcast入参：vehicles【{}】,vins【{}】", vehicles, vins);
        Map<String, VehicleDto> vehicleMap = SingletonManager.getVehicleMap();
        if (!vehicleMap.isEmpty()) {
            for (String key : vehicleMap.keySet()) {
                VehicleDto vehicle = vehicleMap.get(key);
                String vin = vehicle.getVin();
                if (vehicle != null && (CollectionUtils.isEmpty(vins) || !vins.contains(vin))) {
                    vins.add(vin);
                    vehicles.add(vehicle);
                }
            }
        }
        SingletonManager instance = SingletonManager.getInstance();
        if (!CollectionUtils.isEmpty(vehicles)) {
            for (VehicleDto vehicleDto : vehicles) {
                vehicleDto.setConnect(Boolean.valueOf(instance.getFdTcpClient(vehicleDto.getVin()) != null));
            }
        }
    }

    @Override // com.geely.gnds.dsa.service.VehicleService
    public void connectTcp(VehicleDto vehicleDto, SysUser user) throws Exception {
        log.info("DSA connectTcp------开始");
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        String testerId = servletContext.getAttribute("testerID").toString();
        String heartBeatId = vehicleDto.getHeartBeatId();
        String vin = vehicleDto.getVin();
        this.cloud.bb(vin);
        if (this.manager.getFdTcpClientAndDsa(vin) != null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00179));
        }
        VehicleDto vehicle = this.manager.getVehicle(vin);
        if (vehicle != null && vehicle.getStatus() == 2) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00180));
        }
        vehicleDto.setStatus(2);
        this.manager.addVehicle(vin, vehicleDto);
        this.manager.tryConnect(vin);
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String username = loginUser.getUsername();
        String start = DateUtil.format(new Date(), DateUtils.YYYYMMDDHHMMSSSSS);
        String testerModelName = "";
        try {
            testerModelName = Optional.ofNullable(vehicleDto.getBroadcastMap().getOrDefault("vehicle", "")).orElse("").toString();
        } catch (Exception e) {
            log.warn("获取testerModelName失败");
        }
        FdTxtLogger txtLogger = new FdTxtLogger(OssUtils.getFullLogPath(LogTypeEnum.TXT_PATH), vin, username, start);
        String content = "Log start: " + vin + " ,user: " + username + ",version:********";
        txtLogger.write(new Date(), "Main", Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
        FdXmlLogger xmlLogger = new FdXmlLogger(this.cloud.getTesterTenantCode(), testerModelName, username, testerId, OssUtils.getFullLogPath(LogTypeEnum.XML_PATH), vin, start, this.tenantName);
        FdTcpClient client = new FdTcpClient(vehicleDto.getIp(), vehicleDto.getSourceAddress(), vehicleDto.getGatewayAddress(), vehicleDto.getPort(), username, heartBeatId, xmlLogger, txtLogger, vehicleDto.isNeedTls());
        log.info("connectTcp------TesterContext.init开始");
        TesterContext.init(vin, client);
        log.info("connectTcp------TesterContext.init结束");
        SeqManager.getInstance().initSeqMap(vin);
        try {
            log.info("connectTcp------client.connectDoip开始");
            client.connectDoip(vehicleDto.getVin(), false);
            log.info("connectTcp------client.connectDoip结束");
            vehicleDto.setStatus(1);
            this.manager.addVehicle(vin, vehicleDto);
            this.manager.addLock(vin);
            client.setVehicleDto(vehicleDto);
            this.manager.addFdTcpClient(vin, client);
            this.manager.addVehiclesBindUser(username, vin);
            SingletonManager.addVinContrast(vehicleDto.getOldVin(), vin);
            TesterLoginUtils.setLoginUserByVin(vin, loginUser);
            client.setChildPath(username + "-" + vin + "-" + start);
            this.manager.addLogUse(username + "-" + vin + "-" + start);
            client.setDroResult(new DroResult());
            if (!vehicleDto.isHasConnected() && vehicleDto.isHasPermission() && "1".equals(vehicleDto.getPermissionFlag())) {
                String res = this.cloud.a(vin, user);
                log.info("vin号：{},首次连接时间记录保存：{}", vin, res);
            }
        } catch (DoipException e2) {
            this.manager.removeVehicle(vin);
            log.error("FdTcpClient connectDoip异常---->{}", e2.getMessage());
            Socket socket = client.getSocket();
            if (socket != null) {
                try {
                    socket.close();
                } catch (IOException e3) {
                    log.error("FdTcpClient socket关闭异常---->{}", e2.getMessage());
                }
            }
            throw e2;
        } catch (Throwable th) {
            this.manager.removeVehicle(vin);
        }
        log.info("DSAconnectTcp------client.connectDoip结束");
    }

    @Override // com.geely.gnds.dsa.service.VehicleService
    public Boolean checkConnectStatus(String vin) {
        boolean isClose;
        FdTcpClient fdTcpClient = this.manager.getFdTcpClientAndDsa(vin);
        if (fdTcpClient != null) {
            isClose = fdTcpClient.isCloseNoLock();
        } else {
            isClose = true;
        }
        return Boolean.valueOf(isClose);
    }

    @Override // com.geely.gnds.dsa.service.VehicleService
    public void disconnect(String username, String vin) throws Exception {
        log.info("======> 进入disconnect断开车辆连接方法，入参username：{}，vin：{}，adminUsername：{} <======", username, vin);
        FdTcpClient fdTcpClient = this.manager.getFdTcpClientAndDsa(vin);
        this.manager.removeVehicle(vin);
        if (fdTcpClient != null) {
            log.info("======> 开始执行fdTcpClient.closeDoip方法 <======");
            PcapThread pcapThread = fdTcpClient.getPcapThread();
            if (pcapThread != null) {
                this.eV.a(pcapThread);
                log.info("======> 关闭pcapThread线程 <======");
            }
            fdTcpClient.closeDoip();
            log.info("======> 执行fdTcpClient.closeDoip方法结束 <======");
        }
        this.manager.removeLogUse(fdTcpClient.getChildPath());
        SingletonManager.removeVinContrast(vin);
        this.manager.removeVehiclesByUser(username, vin);
        this.manager.removeFdTcpClient(vin);
        this.manager.cleanGlobal(vin);
        this.manager.removeLock(vin);
        this.en.cleanCache(vin);
        log.info("======> 断开车辆连接成功 <======");
        TesterLoginUtils.clearLoginUserByVin(vin);
        log.info("======> 断开成功，清除用户信息 <======");
    }
}
