package com.geely.gnds.dsa.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.common.DsaSingletonManager;
import com.geely.gnds.dsa.component.SoftwareDownload;
import com.geely.gnds.dsa.dto.BssIdDTO;
import com.geely.gnds.dsa.dto.CalculationEcuDTO;
import com.geely.gnds.dsa.dto.DsaSoftwareDownloadConfigDto;
import com.geely.gnds.dsa.dto.DsaSoftwareDownloadDTO;
import com.geely.gnds.dsa.dto.HardwareDTO;
import com.geely.gnds.dsa.dto.HtmlChapterDTO;
import com.geely.gnds.dsa.dto.HtmlRowDTO;
import com.geely.gnds.dsa.dto.HtmlTableDTO;
import com.geely.gnds.dsa.dto.NodeDTO;
import com.geely.gnds.dsa.dto.PartNumberConsistencyDTO;
import com.geely.gnds.dsa.dto.QualifierDTO;
import com.geely.gnds.dsa.dto.SoftwareBssDTO;
import com.geely.gnds.dsa.dto.SoftwareDownloadDto;
import com.geely.gnds.dsa.dto.StringSplitDto;
import com.geely.gnds.dsa.dto.TestCaseDTO;
import com.geely.gnds.dsa.dto.TestEcuDTO;
import com.geely.gnds.dsa.dto.TestOrderCheckDTO;
import com.geely.gnds.dsa.dto.TestOrderCheckDidDTO;
import com.geely.gnds.dsa.dto.TestOrderDTO;
import com.geely.gnds.dsa.dto.TestOrderEcuDTO;
import com.geely.gnds.dsa.dto.TestOrderItemDTO;
import com.geely.gnds.dsa.dto.TesterOrderParamDTO;
import com.geely.gnds.dsa.dto.VbfParseDto;
import com.geely.gnds.dsa.dto.VehicleModelDTO;
import com.geely.gnds.dsa.enums.DsaAddressConst;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.DsaVbfService;
import com.geely.gnds.dsa.service.IntegrationTestService;
import com.geely.gnds.dsa.service.UdsService;
import com.geely.gnds.dsa.vo.UploadVo;
import com.geely.gnds.ruoyi.common.constant.ScheduleConstants;
import com.geely.gnds.ruoyi.common.constant.UserConstants;
import com.geely.gnds.ruoyi.common.exception.file.Base64Utils;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.compile.MemoryClassLoader;
import com.geely.gnds.tester.component.Cloud;
import com.geely.gnds.tester.dao.DsaSequenceDao;
import com.geely.gnds.tester.dto.DsaSeqDTO;
import com.geely.gnds.tester.dto.ReloadDto;
import com.geely.gnds.tester.entity.DsaSequenceEntity;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.SeqButtonTypeEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.util.CommonFunctionsUtils;
import com.geely.gnds.tester.util.MessageUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import freemarker.template.Configuration;
import freemarker.template.Template;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: IntegrationTestServiceImpl.class */
public class IntegrationTestServiceImpl implements IntegrationTestService {

    @Autowired
    private Cloud cloud;

    @Autowired
    private UdsService es;

    @Autowired
    private TesterThread testerThread;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private DsaSequenceDao eK;

    @Autowired
    private DsaVbfService eL;
    public static final String eN = "Programming Session";
    public static final String eO = "Default Session";
    public static final String eP = "Extended Session";
    private static JSONArray eQ = JSONArray.parseArray("[\"SWTC\",\"SWK1\",\"SWK2\",\"SWK3\",\"SWKX\"]");
    private static final Logger LOGGER = LoggerFactory.getLogger(IntegrationTestServiceImpl.class);
    private static final Pattern eS = Pattern.compile("^[-\\+]?[\\d]*$");
    private Class<?> eM = null;
    private String className = "";
    private DoipUtil doipUtil = DoipUtil.getInstance();
    private DsaSingletonManager eR = DsaSingletonManager.getInstance();
    private SingletonManager cv = SingletonManager.getInstance();

    /* JADX WARN: Finally extract failed */
    @Override // com.geely.gnds.dsa.service.IntegrationTestService
    public void uploadLocalVdn(UploadVo uploadVo) throws Exception {
        String filePath = uploadVo.getFilePath();
        String vin = uploadVo.getVin();
        File file = new File(filePath);
        if (!file.exists()) {
            throw new Exception(MessageUtils.getMessage("The file does not exist, please check if the path is correct"));
        }
        String strJson = FileUtils.readFileToString(file, "UTF-8");
        List<String> vehicleVdns = new ArrayList<>();
        if (strJson.contains(ConstantEnum.COMMA)) {
            String[] split = strJson.split(ConstantEnum.COMMA);
            for (String s : split) {
                String vdn = s.replaceAll("\\s*", "");
                if (StringUtils.isNotBlank(vdn)) {
                    vehicleVdns.add(vdn);
                }
            }
        } else if (strJson.contains(";")) {
            String[] split2 = strJson.split(";");
            for (String s2 : split2) {
                String vdn2 = s2.replaceAll("\\s*", "");
                if (StringUtils.isNotBlank(vdn2)) {
                    vehicleVdns.add(vdn2);
                }
            }
        } else {
            try {
                FileInputStream input = new FileInputStream(file);
                Throwable th = null;
                try {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(input));
                    Throwable th2 = null;
                    while (true) {
                        try {
                            try {
                                String tempString = reader.readLine();
                                if (tempString == null) {
                                    break;
                                } else {
                                    vehicleVdns.add(tempString.replaceAll("\\s*", ""));
                                }
                            } catch (Throwable th3) {
                                if (reader != null) {
                                    if (th2 != null) {
                                        try {
                                            reader.close();
                                        } catch (Throwable th4) {
                                            th2.addSuppressed(th4);
                                        }
                                    } else {
                                        reader.close();
                                    }
                                }
                                throw th3;
                            }
                        } finally {
                        }
                    }
                    if (reader != null) {
                        if (0 != 0) {
                            try {
                                reader.close();
                            } catch (Throwable th5) {
                                th2.addSuppressed(th5);
                            }
                        } else {
                            reader.close();
                        }
                    }
                    if (input != null) {
                        if (0 != 0) {
                            try {
                                input.close();
                            } catch (Throwable th6) {
                                th.addSuppressed(th6);
                            }
                        } else {
                            input.close();
                        }
                    }
                } catch (Throwable th7) {
                    if (input != null) {
                        if (0 != 0) {
                            try {
                                input.close();
                            } catch (Throwable th8) {
                                th.addSuppressed(th8);
                            }
                        } else {
                            input.close();
                        }
                    }
                    throw th7;
                }
            } catch (Exception e) {
                LOGGER.error("VDN文件解析失败", e);
                throw e;
            }
        }
        this.eR.setVdn(vin, vehicleVdns);
    }

    @Override // com.geely.gnds.dsa.service.IntegrationTestService
    public BssIdDTO uploadLocalBss(UploadVo uploadVo) throws Exception {
        String filePath = uploadVo.getFilePath();
        String vin = uploadVo.getVin();
        String fileToString = FileUtils.readFileToString(new File(filePath), "UTF-8");
        BssIdDTO bss = (BssIdDTO) ObjectMapperUtils.jsonStr2Clazz(fileToString, BssIdDTO.class);
        this.eR.setBss(vin, bss);
        return bss;
    }

    @Override // com.geely.gnds.dsa.service.IntegrationTestService
    public void getOnlineVdn(String vin, String username) throws Exception {
        List<String> vehicleVdns = this.cloud.d(Collections.singletonList(vin), username);
        this.eR.setVdn(vin, vehicleVdns);
    }

    private static String a(List<String> dirPaths, String vbfName, String ecuDirPath, List<String> ignorePath, boolean swlc) {
        String filePath = "";
        if (StringUtils.isNotBlank(ecuDirPath)) {
            File dir = new File(ecuDirPath);
            if (!dir.exists()) {
                return "";
            }
            for (File file : dir.listFiles()) {
                if (file.getName().contains(vbfName)) {
                    filePath = file.getAbsolutePath();
                }
            }
        } else {
            for (String path : dirPaths) {
                if (!path.contains("Rejected") && !ignorePath.contains(path)) {
                    filePath = a(vbfName, path, ignorePath, swlc);
                    if (!"-1".equals(filePath) && StringUtils.isNotBlank(filePath)) {
                        break;
                    }
                }
            }
        }
        return filePath;
    }

    private static String a(String vbfName, String ecuDirPath, List<String> ignorePath, boolean swlc) {
        String filePath = "";
        File dir = new File(ecuDirPath);
        for (File file : dir.listFiles()) {
            if (swlc && !ecuDirPath.toUpperCase().contains("CARC")) {
                break;
            }
            if (file.isFile() && file.getName().contains(vbfName)) {
                filePath = file.getAbsolutePath();
            }
        }
        if (StringUtils.isBlank(filePath)) {
            for (File file2 : c(dir)) {
                if (!file2.getName().contains("Rejected") && file2.isDirectory()) {
                    String absolutePath = file2.getAbsolutePath();
                    if (!ignorePath.contains(absolutePath)) {
                        filePath = a(vbfName, absolutePath, ignorePath, swlc);
                        if (StringUtils.isNotBlank(filePath)) {
                            break;
                        }
                    } else {
                        continue;
                    }
                }
            }
        }
        return filePath;
    }

    private static String n(String ecuDirPath) {
        String filePath = "";
        if (StringUtils.isNotBlank(ecuDirPath)) {
            File dir = new File(ecuDirPath);
            File[] fileArrListFiles = dir.listFiles();
            int length = fileArrListFiles.length;
            int i = 0;
            while (true) {
                if (i >= length) {
                    break;
                }
                File file = fileArrListFiles[i];
                if (!file.isFile() || ((!file.getName().contains("xls") && !file.getName().contains("xlsx")) || file.getName().startsWith("~$"))) {
                    i++;
                } else {
                    filePath = file.getAbsolutePath();
                    break;
                }
            }
        }
        return filePath;
    }

    @Override // com.geely.gnds.dsa.service.IntegrationTestService
    public List<CalculationEcuDTO> calculating(String vin, List<String> dirPaths) throws Exception {
        List<CalculationEcuDTO> res = new ArrayList<>();
        String udsData = this.es.udsDataNoLog(DsaAddressConst.FUNCTIONAL_ADDRESSING, "22F1AA", vin);
        File config = new File(ConstantEnum.POINT, "config" + File.separator + "1FFF.json");
        if (config.exists()) {
            udsData = FileUtils.readFileToString(config, "UTF-8");
        }
        LOGGER.info("功能寻址22F1AA收到响应--{}", udsData);
        List<JSONObject> list = ObjectMapperUtils.jsonStr2List(udsData, JSONObject.class);
        for (JSONObject jsonObject : list) {
            CalculationEcuDTO calculationEcuDTO = new CalculationEcuDTO();
            String address = jsonObject.getString("ECU_address");
            calculationEcuDTO.setEcuAddress(address.toUpperCase());
            JSONObject type = jsonObject.getJSONObject("ECU_Response_Type");
            JSONObject value = jsonObject.getJSONObject("ECU_Response_value");
            String typeValue = type.getString("Data_Value");
            String dateValue = value.getString("Data_Value");
            if ("Negative".equals(typeValue)) {
                calculationEcuDTO.setComment("收到负响应" + dateValue);
            } else {
                calculationEcuDTO.setHwsd(CommonFunctionsUtils.analysisParam(dateValue.substring(6)));
            }
            String appVersionResponse = this.es.sendUds(address, "22F1AE", vin);
            calculationEcuDTO.setCurrentSoftwareNumber(Arrays.asList(appVersionResponse));
            if (org.apache.commons.lang3.StringUtils.isNotBlank(appVersionResponse)) {
                String str = appVersionResponse.substring(2);
                List<String> array = CommonFunctionsUtils.splitString(str, 16);
                List<String> resultList = new ArrayList<>();
                for (String s : array) {
                    resultList.add(CommonFunctionsUtils.analysisParam(s));
                }
                calculationEcuDTO.setCurrentSoftwareNumber(resultList);
            }
            res.add(calculationEcuDTO);
        }
        a(res, vin, dirPaths);
        LOGGER.info("结算的ECU列表：{}", JSON.toJSONString(res));
        return res;
    }

    private void a(List<CalculationEcuDTO> list, String vin, List<String> dirPaths) throws Exception {
        BssIdDTO bss = this.eR.getBss(vin);
        List<String> vdn = this.eR.getVdn(vin);
        List<VehicleModelDTO> vehicleModels = bss.getVehicleModels();
        Collections.sort(vehicleModels);
        FdTcpClient client = SingletonManager.getInstance().getFdTcpClientAndDsa(vin);
        if (client == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115));
        }
        FdDsaLogger fdDsaLogger = client.getFdDsaLogger();
        for (CalculationEcuDTO ecu : list) {
            a(dirPaths, vdn, vehicleModels, fdDsaLogger, ecu);
        }
    }

    private void a(List<String> dirPaths, List<String> vdn, List<VehicleModelDTO> vehicleModels, FdDsaLogger fdDsaLogger, CalculationEcuDTO ecu) {
        List<VbfParseDto> targetSoftwareNumber = new ArrayList<>();
        String hwsd = ecu.getHwsd();
        String ecuAddress = ecu.getEcuAddress();
        boolean ecuCalculationFlag = false;
        boolean ecuAddressFlag = false;
        try {
            List<String> targets = new ArrayList<>();
            if (StringUtils.isNotBlank(hwsd)) {
                for (VehicleModelDTO vehicleModelDTO : vehicleModels) {
                    for (NodeDTO node : vehicleModelDTO.getNodes()) {
                        String nodeAddress = node.getNodeAddress();
                        String nodeName = node.getNodeName();
                        if (ecuAddress.equalsIgnoreCase(nodeAddress)) {
                            ecu.setEcuName(nodeName);
                            ecuAddressFlag = true;
                            List<HardwareDTO> hardWares = node.getHardwares();
                            for (HardwareDTO hardWare : hardWares) {
                                String partNum = hardWare.getPartNum();
                                String partVersion = hardWare.getPartVersion();
                                hwsd = hwsd.replaceAll("\\s*", "");
                                if (hwsd.equalsIgnoreCase(partNum + partVersion)) {
                                    List<QualifierDTO> qualifiers = hardWare.getQualifiers();
                                    if (a(vdn, qualifiers)) {
                                        ecuCalculationFlag = true;
                                        List<String> vbfNames = new ArrayList<>();
                                        List<SoftwareBssDTO> target = new ArrayList<>();
                                        List<String> ignorePath = new ArrayList<>();
                                        List<String> swlcName = new ArrayList<>();
                                        for (SoftwareBssDTO software : hardWare.getSoftwares()) {
                                            String partType = software.getPartType();
                                            if (!eQ.contains(partType)) {
                                                boolean vdnFlag = a(vdn, software.getQualifiers());
                                                if (vdnFlag) {
                                                    String num = software.getPartNum();
                                                    String version = software.getPartVersion();
                                                    target.add(software);
                                                    if ("SWCL".equalsIgnoreCase(partType)) {
                                                        swlcName.add(num + version);
                                                    } else {
                                                        vbfNames.add(num + version);
                                                    }
                                                }
                                            }
                                        }
                                        Map<String, String> map = new HashMap<>();
                                        String res = a(dirPaths, vbfNames, ignorePath, map, "", false);
                                        if (!CollectionUtils.isEmpty(swlcName)) {
                                            ignorePath.clear();
                                            String res2 = a(dirPaths, swlcName, ignorePath, map, "", true);
                                            if (StringUtils.isBlank(res)) {
                                                res = res2;
                                            }
                                        }
                                        if (StringUtils.isNotBlank(res)) {
                                            ecu.setComment(res);
                                            if ("".equals(res)) {
                                                String error = "ECU address：" + ecuAddress + "，ECU Name: " + nodeName + "，" + res;
                                                Optional.ofNullable(fdDsaLogger).ifPresent(logger -> {
                                                    logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), "Error：" + error);
                                                });
                                            } else {
                                                String finalRes = res;
                                                Optional.ofNullable(fdDsaLogger).ifPresent(logger2 -> {
                                                    logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), finalRes);
                                                });
                                            }
                                        }
                                        for (SoftwareBssDTO software2 : target) {
                                            String num2 = software2.getPartNum();
                                            String version2 = software2.getPartVersion();
                                            String type = software2.getType();
                                            String name = num2 + version2;
                                            vbfNames.add(name);
                                            String filePath = map.get(name);
                                            VbfParseDto vbfParseDto = this.eL.getVbfParseDto(filePath);
                                            vbfParseDto.setVbfName(name);
                                            vbfParseDto.setFilePath(filePath);
                                            vbfParseDto.setAddress(nodeAddress);
                                            vbfParseDto.setSwPartNumber(num2);
                                            vbfParseDto.setSwVersion(version2);
                                            vbfParseDto.setSoftwareType(type);
                                            targetSoftwareNumber.add(vbfParseDto);
                                            if (!"SWBL".equals(type)) {
                                                targets.add(vbfParseDto.getSwPartNumber() + "  " + vbfParseDto.getSwVersion());
                                            }
                                        }
                                        ecu.setTargetSoftwareNumber(targetSoftwareNumber);
                                    }
                                    if (ecuCalculationFlag) {
                                        break;
                                    }
                                }
                            }
                            if (ecuCalculationFlag) {
                                break;
                            }
                        }
                    }
                    if (ecuCalculationFlag) {
                        break;
                    }
                }
            }
            if (!ecuAddressFlag) {
                ecu.setComment("匹配失败，" + ecuAddress + "不在BSS内");
                ecu.setNeedSwdl("N/A");
            } else if (ecuCalculationFlag) {
                String comment = ecu.getComment();
                if (StringUtils.isNotBlank(comment)) {
                    ecu.setNeedSwdl("N/A");
                } else {
                    List<String> currentSoftwareNumber = ecu.getCurrentSoftwareNumber();
                    if (CollectionUtils.isEmpty(targets) || currentSoftwareNumber.containsAll(targets)) {
                        ecu.setNeedSwdl("N");
                    } else {
                        ecu.setNeedSwdl(UserConstants.YES);
                    }
                }
            } else {
                ecu.setComment("HWSD doesn‘t match with currnent  BSS");
                ecu.setNeedSwdl("N/A");
            }
        } catch (Exception e) {
            LOGGER.error("结算失败：【{}】", ecuAddress, e);
            ecu.setComment(e.getMessage());
            ecu.setNeedSwdl("N/A");
        }
    }

    private static String a(List<String> dirPaths, List<String> vbfNames, List<String> ignorePath, Map<String, String> res, String finalName, boolean swlc) {
        if (!swlc) {
            res.clear();
        }
        String ecuPath = "";
        for (int i = 0; i < vbfNames.size(); i++) {
            String vbfName = vbfNames.get(i);
            String filePath = a(dirPaths, vbfName, ecuPath, ignorePath, swlc);
            if (StringUtils.isBlank(filePath)) {
                if (i == 0) {
                    if (StringUtils.isNotBlank(finalName)) {
                        return finalName + ".vbf is not exists, please check again";
                    }
                    return vbfName + ".vbf is not exists, please check again";
                }
                return a(dirPaths, vbfNames, ignorePath, res, vbfName, swlc);
            }
            res.put(vbfName, filePath);
            if (StringUtils.isBlank(ecuPath)) {
                File file = new File(filePath);
                ecuPath = file.getParent();
                ignorePath.add(ecuPath);
            }
        }
        String testOrderPath = n(ecuPath);
        if (StringUtils.isBlank(testOrderPath)) {
            return "VBF path, please check. Can’t find test order file in current";
        }
        return "";
    }

    private boolean a(List<String> vdn, List<QualifierDTO> qualifiers) {
        boolean vdnFlag = false;
        Iterator<QualifierDTO> it = qualifiers.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            QualifierDTO qualifierDTO = it.next();
            List<String> vdns = qualifierDTO.getVdns();
            if (vdn.containsAll(vdns)) {
                vdnFlag = true;
                break;
            }
        }
        return vdnFlag;
    }

    @Override // com.geely.gnds.dsa.service.IntegrationTestService
    public void download(SoftwareDownloadDto softwareDownloadDto) throws Exception {
        String className;
        String javaCode;
        String vin = softwareDownloadDto.getVin();
        FdTcpClient fdTcpClient = SingletonManager.getInstance().getFdTcpClientAndDsa(vin);
        if (fdTcpClient == null) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115));
        }
        FdDsaLogger fdDsaDsaTxtLogger = fdTcpClient.getFdDsaLogger();
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String username = loginUser.getUsername();
        if (TesterLoginUtils.isOnLine()) {
            Integer userStatus = loginUser.getUser().getSeqStatus();
            String seqStatus = ScheduleConstants.MISFIRE_DO_NOTHING;
            if (userStatus != null) {
                seqStatus = userStatus.toString();
            }
            String querySoftwareDownload = this.cloud.F(username, seqStatus);
            DsaSoftwareDownloadDTO dsaSoftwareDownloadDTO = (DsaSoftwareDownloadDTO) ObjectMapperUtils.jsonStr2Clazz(querySoftwareDownload, DsaSoftwareDownloadDTO.class);
            if (dsaSoftwareDownloadDTO == null) {
                throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00263));
            }
            className = dsaSoftwareDownloadDTO.getVersion();
            javaCode = Base64Utils.base64Decode(dsaSoftwareDownloadDTO.getCode());
            if (ScheduleConstants.MISFIRE_DO_NOTHING.equals(seqStatus)) {
                e(username, javaCode, className);
            }
        } else {
            DsaSequenceEntity dsaSequenceEntity = this.eK.selectByBtnCode(SeqButtonTypeEnum.DSA_SOFTWARE_DOWNLOAD.getValue());
            if (dsaSequenceEntity == null) {
                throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00263));
            }
            className = dsaSequenceEntity.getVersion();
            javaCode = dsaSequenceEntity.getSeqcontentCn();
        }
        try {
            MemoryClassLoader memoryClassLoader = MemoryClassLoader.genInstance();
            Class<?> klass = this.eM;
            if (!className.equalsIgnoreCase(this.className)) {
                klass = memoryClassLoader.registerJava2(className, javaCode);
                this.eM = klass;
                this.className = className;
            }
            Class<?> finalKlass = klass;
            this.testerThread.getPool().execute(() -> {
                SoftwareDownload softwareDownload = null;
                TesterOrderParamDTO testerOrderParamDTO = softwareDownloadDto.getTesterOrderParam();
                try {
                    try {
                        int downloadCount = 1;
                        if (1 == softwareDownloadDto.getType()) {
                            DsaSoftwareDownloadConfigDto dsaSoftwareDownloadConfig = softwareDownloadDto.getDsaSoftwareDownloadConfig();
                            downloadCount = dsaSoftwareDownloadConfig.getDownloadCount();
                            if (downloadCount < 1) {
                                downloadCount = 1;
                            }
                        }
                        LOGGER.info("刷写次数：{}", Integer.valueOf(downloadCount));
                        for (int i = 0; i < downloadCount; i++) {
                            LOGGER.info("开始刷写第{}次", Integer.valueOf(i));
                            int finali = i + 1;
                            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
                                logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Stress test begins", String.valueOf(finali)));
                            });
                            softwareDownload = (SoftwareDownload) finalKlass.newInstance();
                            softwareDownload.initSoftwareDownload(vin, fdTcpClient, fdDsaDsaTxtLogger);
                            this.cv.addSoftwareDownload(vin, softwareDownload);
                            softwareDownload.softwareDownload(softwareDownloadDto);
                            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger2 -> {
                                logger2.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), MessageUtils.getMessage("Stress test ends", String.valueOf(finali)));
                            });
                            LOGGER.info("刷写第{}次结束", Integer.valueOf(i));
                            if (softwareDownload.isAbort()) {
                                LOGGER.info("刷写第{}次时中止", Integer.valueOf(i));
                                throw new Exception(MessageUtils.getMessage("User interrupts DSA flashing"));
                            }
                        }
                        if (2 == softwareDownloadDto.getType() && testerOrderParamDTO != null) {
                            Map<String, String> didReadBeforeFlushMap = softwareDownload.getDidReadBeforeFlushMap();
                            TestOrderDTO testOrderDTO = new TestOrderDTO();
                            testerOrderParamDTO.setEcus(softwareDownloadDto.getCalculationEcu());
                            testOrderDTO.setTesterOrderParam(testerOrderParamDTO);
                            Map<String, String> didRead = a(testerOrderParamDTO, vin);
                            testOrderDTO.setDidRead(didRead);
                            testOrderDTO.setDidReadBeforeFlushMap(didReadBeforeFlushMap);
                            this.cv.addTestOrder(vin, testOrderDTO);
                        }
                        this.cv.removeSoftwareDownload(vin);
                        if (softwareDownload != null) {
                            softwareDownload.writeFlushData(DsaLogTypeEnum.FLUSH_END.getValue(), null, null, null);
                        }
                    } catch (Exception e) {
                        LOGGER.error("DSA刷写异常:", e);
                        this.cv.removeSoftwareDownload(vin);
                        if (0 != 0) {
                            softwareDownload.writeFlushData(DsaLogTypeEnum.FLUSH_END.getValue(), null, null, null);
                        }
                    }
                } catch (Throwable th) {
                    this.cv.removeSoftwareDownload(vin);
                    if (0 != 0) {
                        softwareDownload.writeFlushData(DsaLogTypeEnum.FLUSH_END.getValue(), null, null, null);
                    }
                    throw th;
                }
            });
        } catch (Exception e) {
            throw new Exception(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00010));
        }
    }

    private void e(String username, String javaCode, String className) {
        try {
            DsaSequenceEntity dsaSequenceEntity = this.eK.selectByBtnCode(SeqButtonTypeEnum.DSA_SOFTWARE_DOWNLOAD.getValue());
            if (dsaSequenceEntity == null) {
                DsaSequenceEntity dsaSequenceEntity2 = new DsaSequenceEntity();
                dsaSequenceEntity2.setButtonName(SeqButtonTypeEnum.DSA_SOFTWARE_DOWNLOAD.getValue());
                dsaSequenceEntity2.setSequenceCode(className);
                dsaSequenceEntity2.setSeqcontentCn(javaCode);
                dsaSequenceEntity2.setSeqcontentEn(javaCode);
                dsaSequenceEntity2.setVersion(className);
                dsaSequenceEntity2.setUpdateTime(new Date());
                dsaSequenceEntity2.setUpdateBy(username);
                dsaSequenceEntity2.setCreateTime(new Date());
                dsaSequenceEntity2.setCreateBy(username);
                this.eK.create(dsaSequenceEntity2);
            } else if (!className.equalsIgnoreCase(dsaSequenceEntity.getVersion())) {
                dsaSequenceEntity.setSequenceCode(className);
                dsaSequenceEntity.setSeqcontentCn(javaCode);
                dsaSequenceEntity.setSeqcontentEn(javaCode);
                dsaSequenceEntity.setVersion(className);
                dsaSequenceEntity.setUpdateTime(new Date());
                dsaSequenceEntity.setUpdateBy(username);
                this.eK.update(dsaSequenceEntity);
            }
        } catch (Exception e) {
            LOGGER.error("更新数据库失败");
        }
    }

    private static TestOrderEcuDTO o(String path) throws Exception {
        FileInputStream fis = new FileInputStream(path);
        Sheet sheet = new XSSFWorkbook(fis).getSheetAt(0);
        int last = sheet.getLastRowNum();
        String project = a(sheet.getRow(4).getCell(0));
        String supplierName = a(sheet.getRow(6).getCell(6));
        int startRow = a(sheet, last);
        if (startRow == -1) {
            throw new Exception("Diagnostic Information not find");
        }
        List<TestOrderItemDTO> list = new ArrayList<>();
        int i = startRow;
        int i2 = 3;
        while (true) {
            int i3 = i + i2;
            if (i3 < last) {
                Row row = sheet.getRow(i3);
                Row row2 = sheet.getRow(i3 + 1);
                if (row != null && row2 != null) {
                    TestOrderItemDTO testOrderItem = new TestOrderItemDTO();
                    String type = a(row.getCell(0));
                    if (StringUtils.isNotBlank(type)) {
                        testOrderItem.setType(type);
                        testOrderItem.setDescription(a(row.getCell(1)));
                        testOrderItem.setGeelyDid(a(row.getCell(3)));
                        testOrderItem.setGeelyPartNo(a(row.getCell(4)));
                        testOrderItem.setGeelyDidReadOut(a(row2.getCell(4)));
                        testOrderItem.setVolvoDid(a(row.getCell(5)));
                        testOrderItem.setVolvoPartNo(a(row.getCell(6)));
                        testOrderItem.setVolvoDidReadOut(a(row2.getCell(6)));
                        String comment = a(row.getCell(7));
                        if (StringUtils.isBlank(comment)) {
                            comment = a(row2.getCell(7));
                            if (StringUtils.isBlank(comment)) {
                                comment = "None";
                            }
                        }
                        testOrderItem.setComment(comment);
                        list.add(testOrderItem);
                    }
                }
                i = i3;
                i2 = 2;
            } else {
                TestOrderEcuDTO testOrderEcuDTO = new TestOrderEcuDTO(project, supplierName, list);
                return testOrderEcuDTO;
            }
        }
    }

    private static int a(Sheet sheet, int last) {
        for (int i = 0; i < last; i++) {
            Row row = sheet.getRow(i);
            int lastCellNum = row.getLastCellNum();
            for (int j = 0; j < lastCellNum; j++) {
                String value = a(row.getCell(j));
                if (value != null && value.contains("3. Diagnostic Information, needed for integration test")) {
                    return i;
                }
            }
        }
        return -1;
    }

    private static String a(Cell cell) {
        if (cell != null) {
            cell.setCellType(CellType.STRING);
            return cell.getStringCellValue().trim();
        }
        return null;
    }

    public static File[] c(File file) {
        File[] files = file.listFiles();
        Arrays.sort(files, new Comparator<File>() { // from class: com.geely.gnds.dsa.service.impl.IntegrationTestServiceImpl.1
            @Override // java.util.Comparator
            /* renamed from: a, reason: merged with bridge method [inline-methods] */
            public int compare(File f1, File f2) {
                long diff = f1.lastModified() - f2.lastModified();
                if (diff > 0) {
                    return -1;
                }
                if (diff == 0) {
                    return 0;
                }
                return 1;
            }

            @Override // java.util.Comparator
            public boolean equals(Object obj) {
                return true;
            }
        });
        return files;
    }

    private String f(String address, String command, String vin) {
        String res = "";
        try {
            res = this.es.udsDataNoLog(address, command, vin, 0);
        } catch (Exception e) {
            LOGGER.error("读取{}-{}失败", new Object[]{address, command, e});
        }
        return res;
    }

    public Map<String, String> a(TesterOrderParamDTO testerOrderParamDTO, String vin) throws Exception {
        LOGGER.info("发送DID开始:{}-vin:{}", JSON.toJSONString(testerOrderParamDTO), vin);
        Map map = new HashMap();
        Map mapEd20 = new HashMap();
        Map mapEda0 = new HashMap();
        String platformVehicle = testerOrderParamDTO.getPlatformVehicle();
        String platform = testerOrderParamDTO.getPlatform();
        boolean isGeely = false;
        boolean isVcc = false;
        if (AppConfig.COMPANYNAME.equals(platform) || "Geely&VCC".equals(platform)) {
            isGeely = true;
        }
        if ("VCC".equals(platform) || "Geely&VCC".equals(platform)) {
            isVcc = true;
        }
        f(DsaAddressConst.FUNCTIONAL_ADDRESSING, "1002", vin);
        Thread.sleep(60000L);
        f(DsaAddressConst.FUNCTIONAL_ADDRESSING, "1002", vin);
        Thread.sleep(60000L);
        for (CalculationEcuDTO ecu : testerOrderParamDTO.getEcus()) {
            String ecuAddress = ecu.getEcuAddress();
            if (isGeely) {
                map.put("22F1A1" + ecuAddress + "1002", f(ecuAddress, "22F1A1", vin));
                map.put("22F1A5" + ecuAddress + "1002", f(ecuAddress, "22F1A5", vin));
                map.put("22F1AA" + ecuAddress + "1002", f(ecuAddress, "22F1AA", vin));
                map.put("22F1AB" + ecuAddress + "1002", f(ecuAddress, "22F1AB", vin));
                map.put("22F18C" + ecuAddress + "1002", f(ecuAddress, "22F18C", vin));
                if (StringUtils.isNotBlank(platformVehicle) && ConstantEnum.GEEA3.equals(platformVehicle)) {
                    map.put("22F18A" + ecuAddress + "1002", f(ecuAddress, "22F18C", vin));
                }
                String ed20 = f(ecuAddress, "22ED20", vin);
                map.put("22ED20" + ecuAddress + "1002", ed20);
                a(ed20, (Map<String, String>) mapEd20, ecuAddress, "1002", false);
            }
            if (isVcc) {
                map.put("22F121" + ecuAddress + "1002", f(ecuAddress, "22F121", vin));
                map.put("22F125" + ecuAddress + "1002", f(ecuAddress, "22F125", vin));
                map.put("22F12A" + ecuAddress + "1002", f(ecuAddress, "22F12A", vin));
                map.put("22F12B" + ecuAddress + "1002", f(ecuAddress, "22F12B", vin));
                map.put("22F18C" + ecuAddress + "1002", f(ecuAddress, "22F18C", vin));
                if (StringUtils.isNotBlank(platformVehicle) && ConstantEnum.GEEA3.equals(platformVehicle)) {
                    map.put("22F18A" + ecuAddress + "1002", f(ecuAddress, "22F18C", vin));
                }
                String ed202 = f(ecuAddress, "22EDA0", vin);
                map.put("22EDA0" + ecuAddress + "1002", ed202);
                a(ed202, (Map<String, String>) mapEda0, ecuAddress, "1002", true);
            }
        }
        f(DsaAddressConst.FUNCTIONAL_ADDRESSING, "1001", vin);
        Thread.sleep(60000L);
        for (CalculationEcuDTO ecu2 : testerOrderParamDTO.getEcus()) {
            String ecuAddress2 = ecu2.getEcuAddress();
            if (isGeely) {
                map.put("22F1A0" + ecuAddress2 + "1001", f(ecuAddress2, "22F1A0", vin));
                map.put("22F1AA" + ecuAddress2 + "1001", f(ecuAddress2, "22F1AA", vin));
                map.put("22F1AB" + ecuAddress2 + "1001", f(ecuAddress2, "22F1AB", vin));
                map.put("22F1AE" + ecuAddress2 + "1001", f(ecuAddress2, "22F1AE", vin));
                map.put("22F18C" + ecuAddress2 + "1001", f(ecuAddress2, "22F18C", vin));
                if (StringUtils.isNotBlank(platformVehicle) && ConstantEnum.GEEA3.equals(platformVehicle)) {
                    map.put("22F18A" + ecuAddress2 + "1001", f(ecuAddress2, "22F18C", vin));
                }
                String ed203 = f(ecuAddress2, "22ED20", vin);
                map.put("22ED20" + ecuAddress2 + "1001", ed203);
                a(ed203, (Map<String, String>) mapEd20, ecuAddress2, "1001", false);
            }
            if (isVcc) {
                map.put("22F120" + ecuAddress2 + "1001", f(ecuAddress2, "22F120", vin));
                map.put("22F12A" + ecuAddress2 + "1001", f(ecuAddress2, "22F12A", vin));
                map.put("22F12B" + ecuAddress2 + "1001", f(ecuAddress2, "22F12B", vin));
                map.put("22F12E" + ecuAddress2 + "1001", f(ecuAddress2, "22F12E", vin));
                map.put("22F18C" + ecuAddress2 + "1001", f(ecuAddress2, "22F18C", vin));
                if (StringUtils.isNotBlank(platformVehicle) && ConstantEnum.GEEA3.equals(platformVehicle)) {
                    map.put("22F18A" + ecuAddress2 + "1001", f(ecuAddress2, "22F18C", vin));
                }
                String ed204 = f(ecuAddress2, "22EDA0", vin);
                map.put("22EDA0" + ecuAddress2 + "1001", ed204);
                a(ed204, (Map<String, String>) mapEda0, ecuAddress2, "1001", true);
            }
        }
        f(DsaAddressConst.FUNCTIONAL_ADDRESSING, "1003", vin);
        Thread.sleep(500L);
        for (CalculationEcuDTO ecu3 : testerOrderParamDTO.getEcus()) {
            String ecuAddress3 = ecu3.getEcuAddress();
            if (isGeely) {
                map.put("22F1A0" + ecuAddress3 + "1003", f(ecuAddress3, "22F1A0", vin));
                map.put("22F1AA" + ecuAddress3 + "1003", f(ecuAddress3, "22F1AA", vin));
                map.put("22F1AB" + ecuAddress3 + "1003", f(ecuAddress3, "22F1AB", vin));
                map.put("22F1AE" + ecuAddress3 + "1003", f(ecuAddress3, "22F1AE", vin));
                map.put("22F18C" + ecuAddress3 + "1003", f(ecuAddress3, "22F18C", vin));
                if (StringUtils.isNotBlank(platformVehicle) && ConstantEnum.GEEA3.equals(platformVehicle)) {
                    map.put("22F18A" + ecuAddress3 + "1003", f(ecuAddress3, "22F18C", vin));
                }
                String ed205 = f(ecuAddress3, "22ED20", vin);
                map.put("22ED20" + ecuAddress3 + "1003", ed205);
                a(ed205, (Map<String, String>) mapEd20, ecuAddress3, "1003", false);
            }
            if (isVcc) {
                map.put("22F120" + ecuAddress3 + "1003", f(ecuAddress3, "22F120", vin));
                map.put("22F12A" + ecuAddress3 + "1003", f(ecuAddress3, "22F12A", vin));
                map.put("22F12B" + ecuAddress3 + "1003", f(ecuAddress3, "22F12B", vin));
                map.put("22F12E" + ecuAddress3 + "1003", f(ecuAddress3, "22F12E", vin));
                map.put("22F18C" + ecuAddress3 + "1003", f(ecuAddress3, "22F18C", vin));
                if (StringUtils.isNotBlank(platformVehicle) && ConstantEnum.GEEA3.equals(platformVehicle)) {
                    map.put("22F18A" + ecuAddress3 + "1003", f(ecuAddress3, "22F18C", vin));
                }
                String ed206 = f(ecuAddress3, "22EDA0", vin);
                map.put("22EDA0" + ecuAddress3 + "1003", ed206);
                a(ed206, (Map<String, String>) mapEda0, ecuAddress3, "1003", true);
            }
        }
        map.put("22ED20", JSON.toJSONString(mapEd20));
        map.put("22EDA0", JSON.toJSONString(mapEda0));
        LOGGER.info("testOrder指令响应集合：{}", JSON.toJSONString(map));
        return map;
    }

    public List<TestOrderCheckDTO> a(CalculationEcuDTO calculationEcuDTO, FdDsaLogger fdDsaLogger, Map<String, String> didRead, TesterOrderParamDTO testerOrderParamDTO, List<PartNumberConsistencyDTO> consistencyList, Map<String, String> didReadBeforeFlushMap) throws Exception {
        String ecuAddress = calculationEcuDTO.getEcuAddress();
        String ecuName = calculationEcuDTO.getEcuName();
        List<TestOrderCheckDTO> list = new ArrayList<>();
        try {
            String testOrderPath = "";
            List<VbfParseDto> targetSoftwareNumber = calculationEcuDTO.getTargetSoftwareNumber();
            String filePath = "";
            String filePathSwcl = "";
            for (VbfParseDto vbfParseDto : targetSoftwareNumber) {
                if ("SWCL".equalsIgnoreCase(vbfParseDto.getSoftwareType())) {
                    filePathSwcl = vbfParseDto.getFilePath();
                } else {
                    filePath = vbfParseDto.getFilePath();
                }
            }
            File vbf = new File(filePath);
            if (vbf.exists()) {
                testOrderPath = vbf.getParent();
            }
            String orderPath = n(testOrderPath);
            if (StringUtils.isBlank(orderPath)) {
                String finalTestOrderPath = testOrderPath;
                Optional.ofNullable(fdDsaLogger).ifPresent(logger -> {
                    String error = "Error：" + ecuName + "(" + ecuAddress + "), can’t find Test Order file in " + finalTestOrderPath + " path.";
                    logger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), error);
                });
            }
            TestOrderEcuDTO testOrderEcuDTO = o(orderPath);
            if (StringUtils.isNotBlank(filePathSwcl)) {
                File swlc = new File(filePathSwcl);
                if (swlc.exists()) {
                    filePathSwcl = swlc.getParent();
                }
                TestOrderEcuDTO testOrderEcuSwcl = o(n(filePathSwcl));
                for (TestOrderItemDTO testOrderItem : testOrderEcuSwcl.getTestOrderItems()) {
                    String type = testOrderItem.getType();
                    if (StringUtils.isNotBlank(type) && "SWCL".equalsIgnoreCase(type)) {
                        testOrderEcuDTO.getTestOrderItems().add(testOrderItem);
                    }
                }
            }
            String platform = testerOrderParamDTO.getPlatform();
            Boolean afterVp = testerOrderParamDTO.getAfterVp();
            String platformVehicle = testerOrderParamDTO.getPlatformVehicle();
            if (AppConfig.COMPANYNAME.equals(platform) || "Geely&VCC".equals(platform)) {
                a(didRead, ecuAddress, testOrderEcuDTO, eN, ecuName, AppConfig.COMPANYNAME, list, afterVp, platformVehicle);
                a(didRead, ecuAddress, testOrderEcuDTO, eO, ecuName, AppConfig.COMPANYNAME, list, afterVp, platformVehicle);
                a(didRead, ecuAddress, testOrderEcuDTO, eP, ecuName, AppConfig.COMPANYNAME, list, afterVp, platformVehicle);
            }
            if ("VCC".equals(platform) || "Geely&VCC".equals(platform)) {
                a(didRead, ecuAddress, testOrderEcuDTO, eN, ecuName, "VCC", list, afterVp, platformVehicle);
                a(didRead, ecuAddress, testOrderEcuDTO, eO, ecuName, "VCC", list, afterVp, platformVehicle);
                a(didRead, ecuAddress, testOrderEcuDTO, eP, ecuName, "VCC", list, afterVp, platformVehicle);
            }
            if (testerOrderParamDTO.isPartNumberConsistency()) {
                a(consistencyList, ecuAddress, ecuName, testOrderEcuDTO, didRead, didReadBeforeFlushMap);
            }
        } catch (Exception e) {
            String error = "Error：" + ecuName + "(" + ecuAddress + "), 处理异常：" + e.getMessage();
            LOGGER.error(error, e);
            fdDsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), error);
        }
        return list;
    }

    private void a(List<PartNumberConsistencyDTO> consistencyList, String ecuAddress, String ecuName, TestOrderEcuDTO testOrderEcuDTO, Map<String, String> didRead, Map<String, String> didReadBeforeFlushMap) {
        PartNumberConsistencyDTO partNumberConsistency = new PartNumberConsistencyDTO();
        partNumberConsistency.setEcuAddress(ecuAddress);
        partNumberConsistency.setEcuName(ecuName);
        partNumberConsistency.setProject(testOrderEcuDTO.getProject());
        partNumberConsistency.setSupplier(testOrderEcuDTO.getSupplierName());
        partNumberConsistency.setTitle(ecuName + " HW Part Number Consistency");
        partNumberConsistency.setSoftwareVersion("Complete Response:" + ecuAddress + ConstantEnum.EMPTY + DoipUtil.getDsaLogCommand(didRead.getOrDefault("22F1AE" + ecuAddress + "1001", "")));
        if (didReadBeforeFlushMap != null) {
            LOGGER.info("didReadBeforeFlushMap变量的值：{}", JSON.toJSONString(didReadBeforeFlushMap));
            if (didReadBeforeFlushMap.containsKey("1001" + ecuAddress + "F1AA")) {
                partNumberConsistency.setHwsdBeforeDefault(DoipUtil.getDsaLogCommand(didReadBeforeFlushMap.getOrDefault("1001" + ecuAddress + "F1AA", "")));
            }
            if (didReadBeforeFlushMap.containsKey("1002" + ecuAddress + "F1AA")) {
                partNumberConsistency.setHwsdBeforeProgramming(DoipUtil.getDsaLogCommand(didReadBeforeFlushMap.getOrDefault("1002" + ecuAddress + "F1AA", "")));
            }
            if (didReadBeforeFlushMap.containsKey("1003" + ecuAddress + "F1AA")) {
                partNumberConsistency.setHwsdBeforeExtended(DoipUtil.getDsaLogCommand(didReadBeforeFlushMap.getOrDefault("1003" + ecuAddress + "F1AA", "")));
            }
            if (didReadBeforeFlushMap.containsKey("1001" + ecuAddress + "F1AB")) {
                partNumberConsistency.setDuBeforeDefault(DoipUtil.getDsaLogCommand(didReadBeforeFlushMap.getOrDefault("1001" + ecuAddress + "F1AB", "")));
            }
            if (didReadBeforeFlushMap.containsKey("1002" + ecuAddress + "F1AB")) {
                partNumberConsistency.setDuBeforeProgramming(DoipUtil.getDsaLogCommand(didReadBeforeFlushMap.getOrDefault("1002" + ecuAddress + "F1AB", "")));
            }
            if (didReadBeforeFlushMap.containsKey("1003" + ecuAddress + "F1AB")) {
                partNumberConsistency.setDuBeforeExtended(DoipUtil.getDsaLogCommand(didReadBeforeFlushMap.getOrDefault("1003" + ecuAddress + "F1AB", "")));
            }
        }
        partNumberConsistency.setHwsdAfterDefault(DoipUtil.getDsaLogCommand(didRead.getOrDefault("22F1AA" + ecuAddress + "1001", "")));
        partNumberConsistency.setHwsdAfterProgramming(DoipUtil.getDsaLogCommand(didRead.getOrDefault("22F1AA" + ecuAddress + "1002", "")));
        partNumberConsistency.setHwsdAfterExtended(DoipUtil.getDsaLogCommand(didRead.getOrDefault("22F1AA" + ecuAddress + "1003", "")));
        partNumberConsistency.setDuAfterDefault(DoipUtil.getDsaLogCommand(didRead.getOrDefault("22F1AB" + ecuAddress + "1001", "")));
        partNumberConsistency.setDuAfterProgramming(DoipUtil.getDsaLogCommand(didRead.getOrDefault("22F1AB" + ecuAddress + "1002", "")));
        partNumberConsistency.setDuAfterExtended(DoipUtil.getDsaLogCommand(didRead.getOrDefault("22F1AB" + ecuAddress + "1003", "")));
        boolean flag = a(partNumberConsistency);
        if (flag) {
            partNumberConsistency.setResult("pass");
        } else {
            partNumberConsistency.setResult("fail");
        }
        consistencyList.add(partNumberConsistency);
    }

    private boolean a(PartNumberConsistencyDTO partNumberConsistency) {
        String after = partNumberConsistency.getDuAfterDefault();
        String before = partNumberConsistency.getDuBeforeDefault();
        if ((StringUtils.isBlank(after) && StringUtils.isBlank(before)) || !after.equalsIgnoreCase(before)) {
            return false;
        }
        String after2 = partNumberConsistency.getDuAfterExtended();
        String before2 = partNumberConsistency.getDuBeforeExtended();
        if ((StringUtils.isBlank(after2) && StringUtils.isBlank(before2)) || !after2.equalsIgnoreCase(before2)) {
            return false;
        }
        String after3 = partNumberConsistency.getDuAfterProgramming();
        String before3 = partNumberConsistency.getDuBeforeProgramming();
        if ((StringUtils.isBlank(after3) && StringUtils.isBlank(before3)) || !after3.equalsIgnoreCase(before3)) {
            return false;
        }
        String after4 = partNumberConsistency.getHwsdAfterProgramming();
        String before4 = partNumberConsistency.getHwsdBeforeProgramming();
        if ((StringUtils.isBlank(after4) && StringUtils.isBlank(before4)) || !after4.equalsIgnoreCase(before4)) {
            return false;
        }
        String after5 = partNumberConsistency.getHwsdAfterProgramming();
        String before5 = partNumberConsistency.getHwsdBeforeProgramming();
        if ((StringUtils.isBlank(after5) && StringUtils.isBlank(before5)) || !after5.equalsIgnoreCase(before5)) {
            return false;
        }
        String after6 = partNumberConsistency.getHwsdAfterProgramming();
        String before6 = partNumberConsistency.getHwsdBeforeProgramming();
        if ((StringUtils.isBlank(after6) && StringUtils.isBlank(before6)) || !after6.equalsIgnoreCase(before6)) {
            return false;
        }
        return true;
    }

    public static String a(String hex, boolean isGeely) {
        return a(hex, true, isGeely);
    }

    public static String a(String hex, boolean needSub, boolean isGeely) {
        String hex2;
        String bcd;
        String asc1;
        String asc2;
        String asc3;
        if (needSub) {
            try {
                hex = hex.substring(6);
            } catch (Exception e) {
                return "";
            }
        }
        if (isGeely) {
            if (hex.length() >= 16) {
                hex2 = hex.substring(0, 16);
                bcd = hex2.substring(0, 10);
                asc1 = String.valueOf((char) Integer.parseInt(hex2.substring(10, 12), 16));
                asc2 = String.valueOf((char) Integer.parseInt(hex2.substring(12, 14), 16));
                asc3 = String.valueOf((char) Integer.parseInt(hex2.substring(14, 16), 16));
            } else {
                return hex;
            }
        } else if (hex.length() >= 14) {
            hex2 = hex.substring(0, 14);
            bcd = hex2.substring(0, 8);
            asc1 = String.valueOf((char) Integer.parseInt(hex2.substring(8, 10), 16));
            asc2 = String.valueOf((char) Integer.parseInt(hex2.substring(10, 12), 16));
            asc3 = String.valueOf((char) Integer.parseInt(hex2.substring(12, 14), 16));
        } else {
            return hex;
        }
        StringBuilder sb = new StringBuilder(bcd);
        String res = sb.append(asc1).append(asc2).append(asc3).toString();
        LOGGER.info("analysisParam方法入参hex：{},needSub:{},isGeely:{},返回值：res【{}】", new Object[]{hex2, Boolean.valueOf(needSub), Boolean.valueOf(isGeely), res});
        return res;
    }

    private void a(Map<String, String> didRead, String ecuAddress, TestOrderEcuDTO testOrderEcuDTO, String type, String ecuName, String platform, List<TestOrderCheckDTO> list, Boolean afterVp, String platformVehicle) throws NumberFormatException {
        LOGGER.info("getTestOrderCheckDTO方法入参：ecuAddress【{}】，testOrderEcuDTO【{}】，type【{}】，ecuName【{}】,platform【{}】，list【{}】", new Object[]{ecuAddress, JSON.toJSONString(testOrderEcuDTO), type, ecuName, platform, JSON.toJSONString(list)});
        TestOrderCheckDTO testOrderCheckDTO = new TestOrderCheckDTO();
        testOrderCheckDTO.setPlatform(platform);
        testOrderCheckDTO.setProject(testOrderEcuDTO.getProject());
        testOrderCheckDTO.setSupplier(testOrderEcuDTO.getSupplierName());
        testOrderCheckDTO.setEcuAddress(ecuAddress);
        testOrderCheckDTO.setType(type);
        testOrderCheckDTO.setTitle(ecuName + " test order check in " + type + " -" + platform);
        List<TestOrderCheckDidDTO> testOrderCheckDidList = new ArrayList<>();
        String mode = "1003";
        if (eN.equals(type)) {
            mode = "1002";
        } else if (eO.equals(type)) {
            mode = "1001";
        }
        String didEd = "ED20";
        boolean isGeely = true;
        if ("VCC".equalsIgnoreCase(platform)) {
            isGeely = false;
            didEd = "EDA0";
        }
        Map<String, List<TestOrderCheckDidDTO>> map = a(testOrderEcuDTO, type, platform, false, didEd);
        a(didRead, ecuAddress, type, map, testOrderCheckDidList, mode, false, afterVp, didRead, isGeely, didEd);
        if (StringUtils.isNotBlank(platformVehicle) && ConstantEnum.GEEA3.equals(platformVehicle)) {
            a("22F18A", didRead, ecuAddress, type, map, testOrderCheckDidList, mode);
        }
        String read = didRead.getOrDefault("22" + didEd + ecuAddress + mode, "");
        String dsaRead = DoipUtil.getDsaLogCommand(read);
        if (StringUtils.isNotBlank(read) && read.startsWith("62")) {
            testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + didEd, type, "", dsaRead, "pass"));
        } else {
            testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + didEd, type, "", dsaRead, "fail"));
        }
        Map<String, String> stringObjectMap = ObjectMapperUtils.jsonStr2MapStr(didRead.get("22" + didEd));
        if (stringObjectMap.size() > 0) {
            Map<String, List<TestOrderCheckDidDTO>> mapEd20 = a(testOrderEcuDTO, type, platform, true, didEd);
            a(stringObjectMap, ecuAddress, type, mapEd20, testOrderCheckDidList, mode, true, afterVp, didRead, isGeely, didEd);
        }
        String res = "pass";
        Iterator<TestOrderCheckDidDTO> it = testOrderCheckDidList.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            TestOrderCheckDidDTO t = it.next();
            if ("fail".equals(t.getResult())) {
                res = "fail";
                break;
            }
        }
        testOrderCheckDTO.setResult(res);
        testOrderCheckDTO.setTestOrderCheckDidList(testOrderCheckDidList);
        list.add(testOrderCheckDTO);
    }

    private static Map<String, List<TestOrderCheckDidDTO>> a(TestOrderEcuDTO testOrderEcuDTO, String type, String platform, boolean isEd20, String didEd) {
        String did;
        String didReadOut;
        List<TestOrderCheckDidDTO> testOrderCheckDidList;
        Map<String, List<TestOrderCheckDidDTO>> map = new HashMap<>();
        for (TestOrderItemDTO testOrderItem : testOrderEcuDTO.getTestOrderItems()) {
            TestOrderCheckDidDTO testOrderCheckDidDTO = new TestOrderCheckDidDTO();
            if (AppConfig.COMPANYNAME.equals(platform)) {
                did = testOrderItem.getGeelyDid();
                didReadOut = testOrderItem.getGeelyDidReadOut();
            } else {
                did = testOrderItem.getVolvoDid();
                didReadOut = testOrderItem.getVolvoDidReadOut();
            }
            if (!StringUtils.isBlank(did) && !"NA".equals(did)) {
                String did2 = did.replaceAll("\\s*", "").toUpperCase();
                if (map.containsKey(did2)) {
                    testOrderCheckDidList = map.get(did2);
                } else {
                    testOrderCheckDidList = new ArrayList<>();
                }
                if (isEd20) {
                    if (!"22F18C".equals(did2)) {
                        testOrderCheckDidDTO.setDid("0x" + didEd + "(0x" + did2.substring(2) + ")");
                    }
                } else {
                    testOrderCheckDidDTO.setDid("0x" + did2.substring(2));
                }
                testOrderCheckDidDTO.setComment(testOrderItem.getComment());
                testOrderCheckDidDTO.setSwName(testOrderItem.getType());
                testOrderCheckDidDTO.setDiagSession(type);
                testOrderCheckDidDTO.setExpectValue(didReadOut);
                testOrderCheckDidList.add(testOrderCheckDidDTO);
                map.put(did2, testOrderCheckDidList);
            }
        }
        return map;
    }

    private void a(Map<String, String> didRead, String ecuAddress, String type, Map<String, List<TestOrderCheckDidDTO>> map, List<TestOrderCheckDidDTO> testOrderCheckDidList, String mode, boolean idEd20, Boolean afterVp, Map<String, String> stringObjectMap, boolean isGeely, String didEd) throws NumberFormatException {
        LOGGER.info("handleED20入参：didRead={},ecuAddress={},type={},map={},testOrderCheckDidList={},mode={},idEd20={},stringObjectMap={},isGeely={}", new Object[]{JSON.toJSONString(didRead), ecuAddress, type, JSON.toJSONString(map), JSON.toJSONString(testOrderCheckDidList), mode, Boolean.valueOf(idEd20), JSON.toJSONString(stringObjectMap), Boolean.valueOf(isGeely)});
        if (isGeely) {
            a("22F1AA", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, true, stringObjectMap, didEd, true);
            a("22F1AB", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, true, stringObjectMap, didEd, true);
            if ("1002".equals(mode)) {
                a("22F1A1", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, false, stringObjectMap, didEd, true);
                a("22F1A5", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, false, stringObjectMap, didEd, true);
            } else {
                a("22F1A0", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, false, stringObjectMap, didEd, true);
                a("22F1AE", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, didEd, true);
            }
        } else {
            a("22F12A", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, true, stringObjectMap, didEd, false);
            a("22F12B", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, true, stringObjectMap, didEd, false);
            if ("1002".equals(mode)) {
                a("22F121", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, false, stringObjectMap, didEd, false);
                a("22F125", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, false, stringObjectMap, didEd, false);
            } else {
                a("22F120", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, false, stringObjectMap, didEd, false);
                a("22F12E", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, idEd20, didEd, false);
            }
        }
        a("22F18C", didRead, ecuAddress, type, map, testOrderCheckDidList, mode, afterVp, idEd20, stringObjectMap, didEd);
    }

    private void a(String did, Map<String, String> didRead, String ecuAddress, String type, Map<String, List<TestOrderCheckDidDTO>> map, List<TestOrderCheckDidDTO> testOrderCheckDidList, String mode, Boolean afterVp, boolean isEd20, Map<String, String> stringObjectMap, String didEd) {
        try {
            String read = didRead.getOrDefault(did + ecuAddress + mode, "");
            String dsaRead = DoipUtil.getDsaLogCommand(read);
            if (StringUtils.isNotBlank(read)) {
                String substring = read;
                if (!isEd20) {
                    substring = read.substring(6);
                }
                boolean flag = p(substring);
                if (!afterVp.booleanValue()) {
                    String s1 = "";
                    String s2 = "";
                    for (int i = 0; i < substring.length(); i++) {
                        s1 = s1 + UserConstants.TYPE_BUTTON;
                        s2 = s2 + "0";
                    }
                    if (substring.equals(s1) || substring.equals(s2)) {
                        flag = true;
                    }
                }
                if (flag) {
                    String read1001 = stringObjectMap.getOrDefault(did + ecuAddress + "1001", "");
                    String read1002 = stringObjectMap.getOrDefault(did + ecuAddress + "1002", "");
                    String read1003 = stringObjectMap.getOrDefault(did + ecuAddress + "1003", "");
                    if (read1001.equals(read1002) && read1001.equals(read1003)) {
                        flag = true;
                    } else {
                        flag = false;
                    }
                }
                String res = "fail";
                if (flag) {
                    res = "pass";
                }
                if (isEd20) {
                    testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + didEd + "(0x" + did.substring(2) + ")", type, "", dsaRead, res));
                } else {
                    testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + did.substring(2), type, "", dsaRead, res));
                }
            } else if (isEd20) {
                testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + didEd + "(0x" + did.substring(2) + ")", type, "", dsaRead, "fail"));
            } else {
                testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + did.substring(2), type, "", dsaRead, "fail"));
            }
        } catch (Exception e) {
            LOGGER.error("F18C处理失败", e);
        }
    }

    public static boolean p(String str) {
        return eS.matcher(str).matches();
    }

    public static boolean q(String str) {
        Matcher m = Pattern.compile(".*[a-zA-Z]+.*").matcher(str);
        return m.matches();
    }

    private void a(String did, Map<String, String> didRead, String ecuAddress, String type, Map<String, List<TestOrderCheckDidDTO>> map, List<TestOrderCheckDidDTO> testOrderCheckDidList, String mode) {
        try {
            String read = didRead.getOrDefault(did + ecuAddress + mode, "");
            String dsaRead = DoipUtil.getDsaLogCommand(read);
            if (map.containsKey(did) && StringUtils.isNotBlank(read)) {
                List<TestOrderCheckDidDTO> testOrderCheckDids = map.get(did);
                boolean flag = false;
                for (TestOrderCheckDidDTO testOrderCheckDidDTO : testOrderCheckDids) {
                    if (read.startsWith("7F")) {
                        testOrderCheckDidDTO.setReadHex(dsaRead);
                        testOrderCheckDidDTO.setResult("fail");
                    } else {
                        testOrderCheckDidDTO.setReadHex(dsaRead);
                        if (read.length() == 18) {
                            testOrderCheckDidDTO.setResult("pass");
                            flag = true;
                        } else {
                            testOrderCheckDidDTO.setResult("fail");
                        }
                    }
                }
                if (testOrderCheckDids.size() > 1 && flag) {
                    for (TestOrderCheckDidDTO testOrderCheckDidDTO2 : testOrderCheckDids) {
                        if ("fail".equals(testOrderCheckDidDTO2.getResult())) {
                            testOrderCheckDidDTO2.setResult("warning");
                        }
                    }
                }
                testOrderCheckDidList.addAll(testOrderCheckDids);
            } else {
                testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + did.substring(2), type, "", dsaRead, "fail"));
            }
        } catch (Exception e) {
            LOGGER.error("handF18A处理失败", e);
        }
    }

    private void a(String did, Map<String, String> didRead, String ecuAddress, String type, Map<String, List<TestOrderCheckDidDTO>> map, List<TestOrderCheckDidDTO> testOrderCheckDidList, String mode, boolean idEd20, boolean checkMode, Map<String, String> stringObjectMap, String didEd, boolean isGeely) {
        LOGGER.info("handCommon入参did【{}】，didRead【{}】，ecuAddress【{}】，type【{}】，map【{}】，testOrderCheckDidList【{}】，mode【{}】，idEd20【{}】，didEd【{}】，isGeely【{}】，checkMode【{}】", new Object[]{did, JSON.toJSONString(didRead), ecuAddress, type, JSON.toJSONString(map), testOrderCheckDidList, mode, Boolean.valueOf(idEd20), didEd, Boolean.valueOf(isGeely), Boolean.valueOf(checkMode)});
        try {
            String read = didRead.getOrDefault(did + ecuAddress + mode, "");
            String dsaRead = DoipUtil.getDsaLogCommand(read);
            if (map.containsKey(did) && StringUtils.isNotBlank(read)) {
                List<TestOrderCheckDidDTO> testOrderCheckDids = map.get(did);
                boolean flag = false;
                for (TestOrderCheckDidDTO testOrderCheckDidDTO : testOrderCheckDids) {
                    if (read.startsWith("7F")) {
                        testOrderCheckDidDTO.setReadHex(dsaRead);
                        testOrderCheckDidDTO.setResult("fail");
                    } else {
                        testOrderCheckDidDTO.setReadHex(dsaRead);
                        String expectValue = testOrderCheckDidDTO.getExpectValue();
                        if (read.toUpperCase().contains(expectValue.replaceAll("\\s*", "").toUpperCase())) {
                            testOrderCheckDidDTO.setResult("pass");
                            flag = true;
                        } else {
                            testOrderCheckDidDTO.setResult("fail");
                        }
                        if (idEd20) {
                            testOrderCheckDidDTO.setRead(a(read, false, isGeely));
                        } else {
                            testOrderCheckDidDTO.setRead(a(read, isGeely));
                        }
                    }
                }
                if (testOrderCheckDids.size() > 1 && flag) {
                    for (TestOrderCheckDidDTO testOrderCheckDidDTO2 : testOrderCheckDids) {
                        if ("fail".equals(testOrderCheckDidDTO2.getResult())) {
                            testOrderCheckDidDTO2.setResult("warning");
                        }
                    }
                }
                if (checkMode) {
                    String read1001 = stringObjectMap.getOrDefault(did + ecuAddress + "1001", "");
                    String read1002 = stringObjectMap.getOrDefault(did + ecuAddress + "1002", "");
                    String read1003 = stringObjectMap.getOrDefault(did + ecuAddress + "1003", "");
                    if (!read1001.equals(read1002) || !read1001.equals(read1003)) {
                        Iterator<TestOrderCheckDidDTO> it = testOrderCheckDids.iterator();
                        while (it.hasNext()) {
                            it.next().setResult("fail");
                        }
                    }
                }
                testOrderCheckDidList.addAll(testOrderCheckDids);
            } else if (idEd20) {
                testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + didEd + "(0x" + did.substring(2) + ")", type, "", dsaRead, "fail"));
            } else {
                testOrderCheckDidList.add(new TestOrderCheckDidDTO("0x" + did.substring(2), type, "", dsaRead, "fail"));
            }
        } catch (Exception e) {
            LOGGER.error("handCommon处理失败", e);
        }
    }

    private void a(String did, Map<String, String> didRead, String ecuAddress, String type, Map<String, List<TestOrderCheckDidDTO>> map, List<TestOrderCheckDidDTO> testOrderCheckDidList, String mode, boolean idEd20, String didEd, boolean isGeely) throws NumberFormatException {
        List<TestOrderCheckDidDTO> level;
        LOGGER.info("handF1AE入参did【{}】，didRead【{}】，ecuAddress【{}】，type【{}】，map【{}】，testOrderCheckDidList【{}】，mode【{}】，idEd20【{}】，didEd【{}】，isGeely【{}】", new Object[]{did, JSON.toJSONString(didRead), ecuAddress, type, JSON.toJSONString(map), testOrderCheckDidList, mode, Boolean.valueOf(idEd20), didEd, Boolean.valueOf(isGeely)});
        try {
            String read = didRead.getOrDefault(did + ecuAddress + mode, "");
            String didLog = "0x" + did.substring(2);
            if (idEd20) {
                didLog = "0x" + didEd + "(0x" + did.substring(2) + ")";
            }
            String dsaRead = DoipUtil.getDsaLogCommand(read);
            int length = 16;
            if (!isGeely) {
                length = 14;
            }
            if (map.containsKey(did) && StringUtils.isNotBlank(read)) {
                List<TestOrderCheckDidDTO> testOrderCheckDids = map.get(did);
                List<String> vbfs = new ArrayList<>();
                StringBuilder sb = new StringBuilder();
                String copy = read;
                if (!idEd20) {
                    copy = copy.substring(6);
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(copy)) {
                    String str = copy.substring(2);
                    List<String> array = CommonFunctionsUtils.splitString(str, length);
                    for (int i = 0; i < array.size(); i++) {
                        String s = array.get(i);
                        vbfs.add(s.toUpperCase());
                        if (i == array.size() - 1) {
                            sb.append(a(s, false, isGeely)).append("\n");
                        } else {
                            sb.append(a(s, false, isGeely)).append(",\n");
                        }
                    }
                }
                Map<String, List<TestOrderCheckDidDTO>> levels = new HashMap<>();
                for (TestOrderCheckDidDTO testOrderCheckDidDTO : testOrderCheckDids) {
                    String softwareLevel = testOrderCheckDidDTO.getSwName();
                    if (levels.containsKey(softwareLevel)) {
                        level = levels.get(softwareLevel);
                    } else {
                        level = new ArrayList<>();
                    }
                    level.add(testOrderCheckDidDTO);
                    levels.put(softwareLevel, level);
                }
                for (Map.Entry<String, List<TestOrderCheckDidDTO>> entry : levels.entrySet()) {
                    boolean flag = false;
                    List<TestOrderCheckDidDTO> value = entry.getValue();
                    for (TestOrderCheckDidDTO testOrderCheckDidDTO2 : value) {
                        if (read.startsWith("7F")) {
                            testOrderCheckDidDTO2.setReadHex(dsaRead);
                            testOrderCheckDidDTO2.setResult("fail");
                        } else {
                            String expectValue = testOrderCheckDidDTO2.getExpectValue();
                            if (StringUtils.isNotBlank(expectValue)) {
                                expectValue = expectValue.replaceAll("\\s*", "").toUpperCase();
                            }
                            testOrderCheckDidDTO2.setReadHex(dsaRead);
                            if (vbfs.contains(expectValue)) {
                                testOrderCheckDidDTO2.setResult("pass");
                                flag = true;
                            } else {
                                testOrderCheckDidDTO2.setResult("fail");
                            }
                            testOrderCheckDidDTO2.setRead(sb.toString());
                        }
                    }
                    if (value.size() > 1 && flag) {
                        for (TestOrderCheckDidDTO testOrderCheckDidDTO3 : value) {
                            if ("fail".equals(testOrderCheckDidDTO3.getResult())) {
                                testOrderCheckDidDTO3.setResult("warning");
                            }
                        }
                    }
                    testOrderCheckDidList.addAll(value);
                }
                String countString = copy.substring(0, 2);
                int count = Integer.parseInt(countString, length);
                boolean countFlag = count == levels.size();
                StringBuilder softwareLevel2 = new StringBuilder("");
                if (countFlag) {
                    softwareLevel2.append("层级数OK\n层级数（excel):");
                } else {
                    softwareLevel2.append("层级数NOK\n层级数（excel):");
                }
                softwareLevel2.append(levels.size()).append("\n层级数(响应中62F1AE XX):").append(count).append("\n层级(实际响应长度):").append(count);
                String read1001 = didRead.getOrDefault(did + ecuAddress + "1001", "");
                String read1003 = didRead.getOrDefault(did + ecuAddress + "1003", "");
                if (read1001.equals(read1003) && countFlag) {
                    testOrderCheckDidList.add(new TestOrderCheckDidDTO(didLog, type, "", "", "pass", softwareLevel2.toString()));
                } else {
                    testOrderCheckDidList.add(new TestOrderCheckDidDTO(didLog, type, "", dsaRead, "fail", softwareLevel2.toString()));
                }
            } else if (idEd20) {
                testOrderCheckDidList.add(new TestOrderCheckDidDTO(didLog, type, "", dsaRead, "fail"));
            } else {
                testOrderCheckDidList.add(new TestOrderCheckDidDTO(didLog, type, "", dsaRead, "fail"));
            }
        } catch (Exception e) {
            LOGGER.error("F1AE处理失败", e);
        }
    }

    @Override // com.geely.gnds.dsa.service.IntegrationTestService
    public void testerOrder(TesterOrderParamDTO testerOrderParamDTO, String username, FdDsaLogger dsaLogger, TestOrderDTO testOrderDTO) {
        String vin = testerOrderParamDTO.getVin();
        try {
            try {
                dsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "开始生成测试报告");
                Map<String, String> didReadBeforeFlushMap = testOrderDTO.getDidReadBeforeFlushMap();
                testOrderDTO.setTitle("BHC_Diagnostics test report");
                Boolean afterVp = testerOrderParamDTO.getAfterVp();
                if (afterVp.booleanValue()) {
                    testOrderDTO.setComment("当前阶段判断F18C不允许出现全FF或全0");
                } else {
                    testOrderDTO.setComment("当前阶段判断F18C允许出现全FF或全0");
                }
                testOrderDTO.setUesrname(username);
                SimpleDateFormat sdf = new SimpleDateFormat("E MMM dd HH:mm:ss yyyy", Locale.ENGLISH);
                testOrderDTO.setTime(sdf.format(new Date()));
                Map<String, String> didMap = testOrderDTO.getDidRead();
                if (CollectionUtils.isEmpty(didMap)) {
                    LOGGER.info("didMap为空,重新发送did");
                    didMap = a(testerOrderParamDTO, vin);
                }
                List<TestOrderCheckDTO> testOrderProgramming = new ArrayList<>();
                List<TestOrderCheckDTO> testOrderDefault = new ArrayList<>();
                List<TestOrderCheckDTO> testOrderExtended = new ArrayList<>();
                int passed = 0;
                int failed = 0;
                int executed = 0;
                int warning = 0;
                List<TestEcuDTO> testEcus = new ArrayList<>();
                int summary = 0;
                List<PartNumberConsistencyDTO> consistencyList = new ArrayList<>();
                for (CalculationEcuDTO test : testerOrderParamDTO.getEcus()) {
                    List<TestOrderCheckDTO> testOrderCheck = a(test, dsaLogger, didMap, testerOrderParamDTO, consistencyList, didReadBeforeFlushMap);
                    TestEcuDTO testEcuDTO = new TestEcuDTO();
                    testEcuDTO.setEcuName(test.getEcuName());
                    List<TestCaseDTO> testCases = new ArrayList<>();
                    for (TestOrderCheckDTO testOrderCheckDTO : testOrderCheck) {
                        executed++;
                        String type = testOrderCheckDTO.getType();
                        if (eN.equals(type)) {
                            testOrderProgramming.add(testOrderCheckDTO);
                        } else if (eO.equals(type)) {
                            testOrderDefault.add(testOrderCheckDTO);
                        } else {
                            testOrderExtended.add(testOrderCheckDTO);
                        }
                        String result = testOrderCheckDTO.getResult();
                        if ("fail".equals(result)) {
                            failed++;
                        } else if ("warning".equals(result)) {
                            warning++;
                        } else {
                            passed++;
                        }
                        int warningCount = 0;
                        for (TestOrderCheckDidDTO did : testOrderCheckDTO.getTestOrderCheckDidList()) {
                            String resultDid = did.getResult();
                            if ("warning".equals(resultDid)) {
                                summary++;
                                warningCount++;
                            }
                        }
                        TestCaseDTO testCaseDTO = new TestCaseDTO();
                        if (warningCount == 0) {
                            testCaseDTO.setWarning("/");
                        } else {
                            testCaseDTO.setWarning(warningCount + "");
                        }
                        testCaseDTO.setResult(testOrderCheckDTO.getResult());
                        testCaseDTO.setExecutedTestCase(testOrderCheckDTO.getTitle());
                        testCases.add(testCaseDTO);
                    }
                    testEcuDTO.setTestCases(testCases);
                    testEcus.add(testEcuDTO);
                }
                if (!CollectionUtils.isEmpty(consistencyList)) {
                    Map<String, PartNumberConsistencyDTO> mapEcuDto = (Map) consistencyList.stream().collect(Collectors.toMap((v0) -> {
                        return v0.getEcuName();
                    }, Function.identity(), (k1, k2) -> {
                        return k1;
                    }));
                    for (TestEcuDTO testEcuDTO2 : testEcus) {
                        String ecuName = testEcuDTO2.getEcuName();
                        if (mapEcuDto.containsKey(ecuName)) {
                            PartNumberConsistencyDTO partNumberConsistencyDTO = mapEcuDto.get(ecuName);
                            List<TestCaseDTO> testCases2 = testEcuDTO2.getTestCases();
                            TestCaseDTO testCaseDTO2 = new TestCaseDTO();
                            testCaseDTO2.setResult(partNumberConsistencyDTO.getResult());
                            testCaseDTO2.setExecutedTestCase(partNumberConsistencyDTO.getTitle());
                            testCaseDTO2.setWarning("/");
                            testCases2.add(testCaseDTO2);
                        }
                    }
                }
                testOrderDTO.setTestResult(testEcus);
                if (summary == 0) {
                    testOrderDTO.setSummary("/");
                } else {
                    testOrderDTO.setSummary(summary + "");
                }
                a(testOrderDTO);
                testOrderDTO.setPartNumberConsistencyList(consistencyList);
                testOrderDTO.setPassed(Integer.valueOf(passed));
                testOrderDTO.setWarning(Integer.valueOf(warning));
                testOrderDTO.setFailed(Integer.valueOf(failed));
                testOrderDTO.setExecuted(Integer.valueOf(executed));
                testOrderDTO.setTestOrderProgramming(testOrderProgramming);
                testOrderDTO.setTestOrderDefault(testOrderDefault);
                testOrderDTO.setTestOrderExtended(testOrderExtended);
                LOGGER.info("生成测试报告:{}", JSONObject.toJSONString(testOrderDTO));
                a(vin, testOrderDTO);
                dsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD.getValue(), "测试报告生成成功");
                this.cv.removeTestOrder(vin);
            } catch (Exception e) {
                LOGGER.error("生成测试报告失败", e);
                dsaLogger.write(DsaLogTypeEnum.SOFTWARE_DOWNLOAD_ERROR.getValue(), "Error：生成测试报告失败" + e.getMessage());
                this.cv.removeTestOrder(vin);
            }
        } catch (Throwable th) {
            this.cv.removeTestOrder(vin);
            throw th;
        }
    }

    private static void a(TestOrderDTO testOrderDTO) {
        List<HtmlChapterDTO> chapters = testOrderDTO.getChapters();
        if (CollectionUtils.isEmpty(chapters)) {
            return;
        }
        for (HtmlChapterDTO chapter : testOrderDTO.getChapters()) {
            List<HtmlTableDTO> tables = chapter.getTables();
            if (!CollectionUtils.isEmpty(tables)) {
                for (HtmlTableDTO table : tables) {
                    if (table.getLines() == null) {
                        table.setLines(new ArrayList());
                    }
                    List<HtmlRowDTO> rows = table.getRows();
                    if (CollectionUtils.isEmpty(rows)) {
                        table.setRows(new ArrayList());
                    } else {
                        String res = "pass";
                        Iterator<HtmlRowDTO> it = rows.iterator();
                        while (true) {
                            if (!it.hasNext()) {
                                break;
                            }
                            HtmlRowDTO row = it.next();
                            String result = row.getResult();
                            if ("fail".equals(result)) {
                                res = "fail";
                                break;
                            }
                        }
                        table.setResult(res);
                    }
                }
            }
        }
    }

    private static void a(String vin, TestOrderDTO testOrderDTO) throws Exception {
        File file = new File(AppConfig.getAppDataDir(), "applog" + File.separator + "test_report");
        if (!file.exists()) {
            file.mkdirs();
        }
        String format = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss").format(new Date());
        File html = new File(file, "BHC_Diagnostics test report _" + vin + "_" + format + ".html");
        Configuration configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        configuration.setDirectoryForTemplateLoading(new File(ConstantEnum.POINT, "config"));
        configuration.setDefaultEncoding("utf-8");
        Template template = configuration.getTemplate("IntegrationTestReports.ftl");
        FileWriter fileWriter = new FileWriter(html);
        template.process(testOrderDTO, fileWriter);
        fileWriter.close();
    }

    private static void a(String input, Map<String, String> didRead, String ecuAddress, String mode, boolean isVcc) {
        String strSubstring;
        String[] keys = {"F1A1", "F1A5", "F1AA", "F1AB", "F1A0", "F1AE", "F18C", "F13F"};
        if (isVcc) {
            keys = new String[]{"F121", "F125", "F12A", "F12B", "F120", "F12E", "F18C"};
        }
        if (StringUtils.isBlank(input) || input.startsWith("7F")) {
            return;
        }
        String input2 = input.toUpperCase();
        List<StringSplitDto> splits = new ArrayList<>();
        for (String key : keys) {
            int i = input2.indexOf(key);
            if (i != -1) {
                StringSplitDto stringSplitDTO = new StringSplitDto();
                stringSplitDTO.setIndex(i);
                stringSplitDTO.setDid(key);
                splits.add(stringSplitDTO);
            } else {
                didRead.put("22" + key + ecuAddress + mode, "");
            }
        }
        if (!CollectionUtils.isEmpty(splits)) {
            Collections.sort(splits);
            for (int i2 = 0; i2 < splits.size(); i2++) {
                StringSplitDto stringSplitDto = splits.get(i2);
                if (i2 < splits.size() - 1) {
                    StringSplitDto stringSplitDto2 = splits.get(i2 + 1);
                    strSubstring = input2.substring(stringSplitDto.getIndex() + 4, stringSplitDto2.getIndex());
                } else {
                    strSubstring = input2.substring(stringSplitDto.getIndex() + 4);
                }
                String value = strSubstring;
                didRead.put("22" + stringSplitDto.getDid() + ecuAddress + mode, value);
            }
        }
    }

    @Override // com.geely.gnds.dsa.service.IntegrationTestService
    public List getDsaIntegrationTestSeq(String vin, String platform, String category) throws Exception {
        List<DsaSeqDTO> list = new ArrayList<>();
        String reload = this.cloud.E(platform, vin, category);
        JsonNode readValue = (JsonNode) ObjectMapperUtils.getInstance().readValue(reload, JsonNode.class);
        Map<String, List<ReloadDto>> map = new HashMap<>();
        if (readValue.isArray()) {
            readValue.forEach(obj -> {
                ReloadDto reloadDto = (ReloadDto) ObjectMapperUtils.getInstance().convertValue(obj, ReloadDto.class);
                String categoryDto = reloadDto.getCategory();
                if (map.containsKey(categoryDto)) {
                    ((List) map.get(categoryDto)).add(reloadDto);
                    return;
                }
                ArrayList arrayList = new ArrayList();
                arrayList.add(reloadDto);
                map.put(categoryDto, arrayList);
            });
        }
        for (Map.Entry<String, List<ReloadDto>> stringListEntry : map.entrySet()) {
            DsaSeqDTO dsaSeqDTO = new DsaSeqDTO();
            dsaSeqDTO.setCategory(stringListEntry.getKey());
            dsaSeqDTO.setDsaSeqs(stringListEntry.getValue());
            list.add(dsaSeqDTO);
        }
        return list;
    }
}
