package com.geely.gnds.dsa.service.impl;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.dsa.service.SddbCacheService;
import com.geely.gnds.ruoyi.common.utils.ServletUtils;
import com.geely.gnds.ruoyi.framework.security.LoginUser;
import com.geely.gnds.ruoyi.framework.security.service.TokenService;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.ThreadLocalUtils;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.ServletContext;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.context.WebApplicationContext;

@Service
/* loaded from: SddbCacheServiceImpl.class */
public class SddbCacheServiceImpl implements SddbCacheService {
    private static final Logger log = LoggerFactory.getLogger(SddbServiceImpl.class);
    private static String eq = "sddbFilePath";
    private static String er = "sddbFileRealPath";

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SddbCacheService eH;

    @Override // com.geely.gnds.dsa.service.SddbCacheService
    @Cacheable({"getEcuSwEleList"})
    public List<Element> getEcuSwEleList() throws Exception {
        List<Element> list = new ArrayList<>();
        List<Element> ecuElementList = this.eH.getEcuEleList();
        log.info("sddb解析-缓存方法-开始获取诊断零件号列表");
        System.out.println("sddb解析-缓存方法-开始获取诊断零件号列表");
        for (Element ecu : ecuElementList) {
            List<Element> swList = ecu.element("SWs").elements();
            list.addAll(swList);
        }
        log.info("sddb解析-缓存方法-获取诊断零件号列表完成");
        System.out.println("sddb解析-缓存方法-获取诊断零件号列表完成");
        return list;
    }

    @Override // com.geely.gnds.dsa.service.SddbCacheService
    @Cacheable({"getEcuEleList"})
    public List<Element> getEcuEleList() throws Exception {
        Document document = this.eH.getDocument();
        log.info("sddb解析-开始获取ECU列表");
        System.out.println("sddb解析-开始获取ECU列表");
        Element root = document.getRootElement();
        Element systemElement = root.element("System");
        List<Element> ecuElementList = systemElement.element("ECUs").elements();
        log.info("sddb解析-获取ECU列表完成");
        System.out.println("sddb解析-获取ECU列表完成");
        return ecuElementList;
    }

    @Override // com.geely.gnds.dsa.service.SddbCacheService
    @Cacheable({"getDocument"})
    public Document getDocument() throws Exception {
        Document document;
        String filePath = getSddbFilePath();
        if (StringUtils.isBlank(filePath)) {
            throw new Exception(TesterErrorCodeEnum.SG00238.code());
        }
        try {
            document = s(filePath);
        } catch (Exception e) {
            log.info("sddb解析-sddb文件读取为Document失败，{}", e);
            throw new Exception();
        } catch (DocumentException documentException) {
            log.info("sddb解析-捕获DocumentException，{}", documentException);
            System.out.println("sddb解析-捕获DocumentException，" + documentException);
            String newFilePath = r(filePath);
            document = s(newFilePath);
        }
        return document;
    }

    /* JADX WARN: Failed to apply debug info
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.applyWithWiderIgnoreUnknown(TypeUpdate.java:74)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:137)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:133)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.searchAndApplyVarDebugInfo(DebugInfoApplyVisitor.java:75)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.lambda$applyDebugInfo$0(DebugInfoApplyVisitor.java:68)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.applyDebugInfo(DebugInfoApplyVisitor.java:68)
    	at jadx.core.dex.visitors.debuginfo.DebugInfoApplyVisitor.visit(DebugInfoApplyVisitor.java:55)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r14v1 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Failed to calculate best type for var: r15v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.calculateFromBounds(FixTypesVisitor.java:156)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.setBestType(FixTypesVisitor.java:133)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.deduceType(FixTypesVisitor.java:238)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.tryDeduceTypes(FixTypesVisitor.java:221)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Failed to calculate best type for var: r15v0 ??
    java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.InsnArg.getType()" because "changeArg" is null
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.moveListener(TypeUpdate.java:439)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.runListeners(TypeUpdate.java:232)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.requestUpdate(TypeUpdate.java:212)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeForSsaVar(TypeUpdate.java:183)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.updateTypeChecked(TypeUpdate.java:112)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:83)
    	at jadx.core.dex.visitors.typeinference.TypeUpdate.apply(TypeUpdate.java:56)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.calculateFromBounds(TypeInferenceVisitor.java:145)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.setBestType(TypeInferenceVisitor.java:123)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.lambda$runTypePropagation$2(TypeInferenceVisitor.java:101)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.runTypePropagation(TypeInferenceVisitor.java:101)
    	at jadx.core.dex.visitors.typeinference.TypeInferenceVisitor.visit(TypeInferenceVisitor.java:75)
     */
    /* JADX WARN: Finally extract failed */
    /* JADX WARN: Multi-variable type inference failed. Error: java.lang.NullPointerException: Cannot invoke "jadx.core.dex.instructions.args.RegisterArg.getSVar()" because the return value of "jadx.core.dex.nodes.InsnNode.getResult()" is null
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.collectRelatedVars(AbstractTypeConstraint.java:31)
    	at jadx.core.dex.visitors.typeinference.AbstractTypeConstraint.<init>(AbstractTypeConstraint.java:19)
    	at jadx.core.dex.visitors.typeinference.TypeSearch$1.<init>(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeMoveConstraint(TypeSearch.java:376)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.makeConstraint(TypeSearch.java:361)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.collectConstraints(TypeSearch.java:341)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.typeinference.TypeSearch.run(TypeSearch.java:60)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.runMultiVariableSearch(FixTypesVisitor.java:116)
    	at jadx.core.dex.visitors.typeinference.FixTypesVisitor.visit(FixTypesVisitor.java:91)
     */
    /* JADX WARN: Not initialized variable reg: 14, insn: 0x0159: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r14 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY] A[D('inputStreamReader' java.io.InputStreamReader)]) A[TRY_LEAVE], block:B:46:0x0159 */
    /* JADX WARN: Not initialized variable reg: 15, insn: 0x015e: MOVE (r0 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]) = (r15 I:??[int, float, boolean, short, byte, char, OBJECT, ARRAY]), block:B:48:0x015e */
    /* JADX WARN: Type inference failed for: r14v1, names: [inputStreamReader], types: [java.io.InputStreamReader] */
    /* JADX WARN: Type inference failed for: r15v0, types: [java.lang.Throwable] */
    private String r(String filePath) throws Exception {
        File base = new File(ConstantEnum.POINT);
        File sddb = new File(base, "sddb_browser");
        if (!sddb.exists()) {
            sddb.mkdirs();
        }
        String targetPath = sddb.getCanonicalPath() + filePath.substring(filePath.lastIndexOf("\\"));
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String key = loginUser.getUsername() + "-" + er;
        servletContext.setAttribute(key, targetPath);
        try {
            try {
                InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(filePath), "UTF-8");
                Throwable th = null;
                OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(targetPath), "UTF-8");
                Throwable th2 = null;
                try {
                    char[] cArr = new char[DoipUtil.MAX_BYTE_ARRAY_SIZE];
                    while (true) {
                        int i = inputStreamReader.read(cArr);
                        if (i == -1) {
                            break;
                        }
                        outputStreamWriter.write(cArr, 0, i);
                    }
                    if (outputStreamWriter != null) {
                        if (0 != 0) {
                            try {
                                outputStreamWriter.close();
                            } catch (Throwable th3) {
                                th2.addSuppressed(th3);
                            }
                        } else {
                            outputStreamWriter.close();
                        }
                    }
                    if (inputStreamReader != null) {
                        if (0 != 0) {
                            try {
                                inputStreamReader.close();
                            } catch (Throwable th4) {
                                th.addSuppressed(th4);
                            }
                        } else {
                            inputStreamReader.close();
                        }
                    }
                    return targetPath;
                } catch (Throwable th5) {
                    if (outputStreamWriter != null) {
                        if (0 != 0) {
                            try {
                                outputStreamWriter.close();
                            } catch (Throwable th6) {
                                th2.addSuppressed(th6);
                            }
                        } else {
                            outputStreamWriter.close();
                        }
                    }
                    throw th5;
                }
            } catch (Exception e) {
                log.info("上传sddb-sddb编码转换失败", e);
                throw new RuntimeException(e);
            }
        } finally {
        }
    }

    @Override // com.geely.gnds.dsa.service.SddbCacheService
    @CacheEvict(cacheNames = {"getEcuSwEleList", "getEcuEleList", "getDocument"})
    public void clearCache() {
        log.info("sddb解析-删除sddb文件缓存");
        System.out.println("sddb解析-删除sddb文件缓存");
    }

    @Override // com.geely.gnds.dsa.service.SddbCacheService
    public String getSddbFilePath() {
        ServletContext servletContext = this.webApplicationContext.getServletContext();
        LoginUser loginUser = this.tokenService.c(ServletUtils.getRequest());
        String username = "";
        if (loginUser != null) {
            username = loginUser.getUsername();
        } else {
            log.info("获取loginUser失败：{}", loginUser);
        }
        if (StringUtils.isBlank(username)) {
            username = ThreadLocalUtils.CURRENT_USER_NAME.get();
        }
        String key = username + "-" + eq;
        Object sddbFilePath = servletContext.getAttribute(key);
        log.info("sddb解析-sddb文件路径：{}", sddbFilePath);
        System.out.println("sddb解析-sddb文件路径：" + sddbFilePath);
        if (ObjectUtils.isEmpty(sddbFilePath)) {
            log.error("sddb解析-sddb文件路径为空");
            return null;
        }
        return sddbFilePath.toString();
    }

    private Document s(String filePath) throws Exception {
        log.info("sddb解析-开始读取sddb文件");
        System.out.println("sddb解析-开始读取sddb文件");
        SAXReader reader = new SAXReader();
        File file = new File(filePath);
        Document document = reader.read(new FileInputStream(file));
        log.info("sddb解析-sddb文件读取完成");
        System.out.println("sddb解析-sddb文件读取完成");
        return document;
    }
}
