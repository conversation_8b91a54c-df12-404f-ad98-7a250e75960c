package com.geely.gnds.dsa.service.impl;

import com.geely.gnds.dsa.dto.DiaDtcDTO;
import com.geely.gnds.dsa.dto.DsaDtcRespDTO;
import com.geely.gnds.dsa.dto.DsaDtcStatusMaskDTO;
import com.geely.gnds.dsa.enums.LanguageEnum;
import com.geely.gnds.dsa.service.DsaDataAnalysisService;
import com.geely.gnds.dsa.utils.DsaStringUtils;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.tester.dto.DiagResItemDto;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.dto.DiagResItemParseResultDto;
import com.geely.gnds.tester.service.DtcDataAnalysisService;
import com.geely.gnds.tester.service.DtcService;
import com.geely.gnds.tester.util.ParseUtils;
import com.geely.gnds.tester.util.TesterVehicleResponseUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
/* loaded from: DsaDataAnalysisServiceImpl.class */
public class DsaDataAnalysisServiceImpl implements DsaDataAnalysisService {

    @Autowired
    private DtcDataAnalysisService ez;

    @Autowired
    private DtcService eA;
    private static final Logger log = LoggerFactory.getLogger(DsaDataAnalysisServiceImpl.class);

    @Override // com.geely.gnds.dsa.service.DsaDataAnalysisService
    public List<DiagResItemParseResultDto> analysisDidData(String vin, List<DiagResItemGroupDto> diagParamDtos, String response) {
        List<DiagResItemGroupDto> diagParamDtos2 = getResItemGroupDtos(diagParamDtos);
        diagParamDtos2.sort(Comparator.comparing((v0) -> {
            return v0.getDataIdentifierId();
        }));
        List<DiagResItemParseResultDto> result = new ArrayList<>();
        List<String> readList = (List) diagParamDtos2.stream().map((v0) -> {
            return v0.getDataIdentifierId();
        }).collect(Collectors.toList());
        parseResponseByDid(result, diagParamDtos2, response, readList);
        return result;
    }

    @Override // com.geely.gnds.dsa.service.DsaDataAnalysisService
    public List<DsaDtcRespDTO> analysisDtcStatusMask(String vin, List<DiaDtcDTO> dtcList, String response) {
        List<DsaDtcRespDTO> result = new ArrayList<>();
        if (response.length() <= 6) {
            return result;
        }
        String respData = response.substring(6);
        Map<String, DiaDtcDTO> sddbDtc = (Map) dtcList.stream().collect(Collectors.toMap((v0) -> {
            return v0.getDtcId();
        }, Function.identity(), (k1, k2) -> {
            return k1;
        }));
        List<String> dtcData = DsaStringUtils.a(respData, 8);
        dtcData.forEach(d -> {
            String dtcId = d.substring(0, 6);
            byte[] statusMaskBits = ParseUtils.byteToBitOfArray((byte) Integer.parseInt(d.substring(6, d.length()), 16));
            DsaDtcStatusMaskDTO statusMask = new DsaDtcStatusMaskDTO();
            statusMask.setWarningIndicatorRequested(Boolean.valueOf(statusMaskBits[0] == 1));
            statusMask.setTestNotCompletedThisOperationCycle(Boolean.valueOf(statusMaskBits[1] == 1));
            statusMask.setTestFailedSinceLastClear(Boolean.valueOf(statusMaskBits[2] == 1));
            statusMask.setTestNotCompletedSinceLastClear(Boolean.valueOf(statusMaskBits[3] == 1));
            statusMask.setConfirmedDtc(Boolean.valueOf(statusMaskBits[4] == 1));
            statusMask.setPendingDtc(Boolean.valueOf(statusMaskBits[5] == 1));
            statusMask.setTestFailedThisOperationCycle(Boolean.valueOf(statusMaskBits[6] == 1));
            statusMask.setTestFailed(Boolean.valueOf(statusMaskBits[7] == 1));
            DsaDtcRespDTO temp = new DsaDtcRespDTO();
            temp.setDtcId(dtcId);
            temp.setName("");
            if (sddbDtc.containsKey(dtcId)) {
                DiaDtcDTO diaDtcDTO = (DiaDtcDTO) sddbDtc.get(dtcId);
                temp.setDiagnosticPartNumber(diaDtcDTO.getDiagnosticPartNumber());
                temp.setServiceId(diaDtcDTO.getServiceId());
                temp.setName(diaDtcDTO.getName());
            }
            temp.setDtcStatus(statusMask);
            result.add(temp);
        });
        return result;
    }

    @Override // com.geely.gnds.dsa.service.DsaDataAnalysisService
    public void parseResponseByDid(List<DiagResItemParseResultDto> result, List<DiagResItemGroupDto> list, String response, List<String> readList) {
        for (int i = 0; i < list.size(); i++) {
            DiagResItemGroupDto diagParamDto = list.get(i);
            String did = diagParamDto.getDataIdentifierId();
            if (readList.contains(did)) {
                List<DiagResItemDto> items = diagParamDto.getResponseItemDtoList();
                Collections.sort(items, (a, b) -> {
                    if (StringUtils.isNotBlank(a.getCompareValue())) {
                        return -1;
                    }
                    return 1;
                });
                boolean flag = false;
                String nameTemp = "";
                String originalName = "";
                String originalUnit = "";
                String outDataType = "";
                try {
                    String didSize = diagParamDto.getSize();
                    Iterator<DiagResItemDto> it = items.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            break;
                        }
                        DiagResItemDto diagParamItemDto = it.next();
                        nameTemp = diagParamItemDto.getName();
                        originalName = diagParamItemDto.getOriginalName();
                        outDataType = diagParamItemDto.getOutDataType();
                        originalUnit = diagParamItemDto.getOriginalUnit();
                        String parseParam = TesterVehicleResponseUtils.parseParam(response, did, didSize, diagParamItemDto.getOutDataType(), diagParamItemDto.getInDataType(), diagParamItemDto.getOffset(), diagParamItemDto.getSize(), diagParamItemDto.getFormula(), diagParamItemDto.getCompareValue());
                        if (StringUtils.isNotBlank(parseParam)) {
                            DiagResItemParseResultDto diagParamResultDto = new DiagResItemParseResultDto();
                            diagParamResultDto.setDataIdentifierId(did);
                            diagParamResultDto.setName(diagParamItemDto.getName());
                            diagParamResultDto.setOriginalName(originalName);
                            diagParamResultDto.setOriginalUnit(originalUnit);
                            diagParamResultDto.setOutDataType(outDataType);
                            diagParamResultDto.setUnit(diagParamItemDto.getUnit());
                            diagParamResultDto.setValue(parseParam);
                            diagParamResultDto.setReadFlag(true);
                            diagParamResultDto.setDiagnosticPartNumber(diagParamDto.getDiagnosticPartNumber());
                            result.add(diagParamResultDto);
                            flag = true;
                            break;
                        }
                    }
                    response = subNextDidRes(list, response, i, did, didSize);
                    if (!flag) {
                        DiagResItemParseResultDto diagParamResultDto2 = new DiagResItemParseResultDto();
                        diagParamResultDto2.setDataIdentifierId(did);
                        diagParamResultDto2.setName(nameTemp);
                        diagParamResultDto2.setOriginalName(originalName);
                        diagParamResultDto2.setOriginalUnit(originalUnit);
                        diagParamResultDto2.setOutDataType(outDataType);
                        diagParamResultDto2.setReadFlag(false);
                        diagParamResultDto2.setErrorMsg(LanguageEnum.ERROR7.valueByLanguage());
                        result.add(diagParamResultDto2);
                    }
                } catch (Exception e) {
                    DiagResItemParseResultDto diagParamResultDto3 = new DiagResItemParseResultDto();
                    diagParamResultDto3.setDataIdentifierId(did);
                    diagParamResultDto3.setName(nameTemp);
                    diagParamResultDto3.setOriginalName(originalName);
                    diagParamResultDto3.setOriginalUnit(originalUnit);
                    diagParamResultDto3.setOutDataType(outDataType);
                    diagParamResultDto3.setReadFlag(false);
                    diagParamResultDto3.setErrorMsg(e.getMessage());
                    diagParamResultDto3.setDiagnosticPartNumber(diagParamDto.getDiagnosticPartNumber());
                    result.add(diagParamResultDto3);
                }
            }
        }
    }

    private String subNextDidRes(List<DiagResItemGroupDto> list, String response, int i, String did, String didSize) throws NumberFormatException {
        if (i + 1 < list.size()) {
            String nextDid = list.get(i + 1).getDataIdentifierId();
            if (!did.equals(nextDid)) {
                int didLength = Integer.parseInt(didSize);
                int didBegin = response.indexOf(did);
                int resBegin = didBegin + did.length();
                String substring = response.substring(didBegin, resBegin + (didLength * 2));
                response = response.replaceFirst(substring, "");
            }
        }
        return response;
    }

    private static List<DiagResItemGroupDto> getResItemGroupDtos(List<DiagResItemGroupDto> cloudGroups) {
        List<DiagResItemGroupDto> result = new ArrayList<>();
        for (DiagResItemGroupDto diagResItemGroupDto : cloudGroups) {
            List<DiagResItemDto> items = diagResItemGroupDto.getResponseItemDtoList();
            if (!CollectionUtils.isEmpty(items)) {
                Map<String, List<DiagResItemDto>> collect = (Map) items.stream().collect(Collectors.groupingBy(dto -> {
                    return dto.getOffset() + Constants.GROUP_SPLIT + dto.getSize() + Constants.GROUP_SPLIT + dto.getName();
                }, TreeMap::new, Collectors.toList()));
                Set<String> keySet = collect.keySet();
                if (keySet.size() > 1) {
                    diagResItemGroupDto.setResponseItemDtoList(null);
                    for (String key : keySet) {
                        DiagResItemGroupDto group = new DiagResItemGroupDto();
                        BeanUtils.copyProperties(diagResItemGroupDto, group);
                        group.setResponseItemDtoList(collect.get(key));
                        result.add(group);
                    }
                } else {
                    result.add(diagResItemGroupDto);
                }
            }
        }
        return result;
    }
}
