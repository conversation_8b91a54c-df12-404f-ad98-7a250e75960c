package com.geely.gnds.dsa.service.impl;

import com.geely.gnds.dsa.dao.DsaHistoricRecordsDao;
import com.geely.gnds.dsa.dto.DsaHistoricRecordsDTO;
import com.geely.gnds.dsa.service.DsaHistoricRecordsService;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
/* loaded from: DsaHistoricRecordsServiceImpl.class */
public class DsaHistoricRecordsServiceImpl implements DsaHistoricRecordsService {

    @Autowired
    private DsaHistoricRecordsDao ep;

    @Override // com.geely.gnds.dsa.service.DsaHistoricRecordsService
    public List<DsaHistoricRecordsDTO> getList() {
        List<DsaHistoricRecordsDTO> list = this.ep.getList();
        return list;
    }

    @Override // com.geely.gnds.dsa.service.DsaHistoricRecordsService
    public DsaHistoricRecordsDTO queryById(Integer id) {
        return this.ep.queryById(id);
    }

    @Override // com.geely.gnds.dsa.service.DsaHistoricRecordsService
    public DsaHistoricRecordsDTO insert(DsaHistoricRecordsDTO dsaHistoricRecords) {
        this.ep.insert(dsaHistoricRecords);
        return dsaHistoricRecords;
    }

    @Override // com.geely.gnds.dsa.service.DsaHistoricRecordsService
    public DsaHistoricRecordsDTO update(DsaHistoricRecordsDTO dsaHistoricRecords) {
        this.ep.update(dsaHistoricRecords);
        return queryById(dsaHistoricRecords.getId());
    }

    @Override // com.geely.gnds.dsa.service.DsaHistoricRecordsService
    public boolean deleteById(Integer id) {
        return this.ep.deleteById(id) > 0;
    }

    @Override // com.geely.gnds.dsa.service.DsaHistoricRecordsService
    public void saveOrUpdate(DsaHistoricRecordsDTO dsaHistoricRecords) {
        DsaHistoricRecordsDTO dto = this.ep.queryBy(dsaHistoricRecords);
        if (ObjectUtils.isEmpty(dto)) {
            List<DsaHistoricRecordsDTO> hisList = this.ep.getList();
            if (hisList.size() >= 10) {
                DsaHistoricRecordsDTO recordsDto = hisList.get(hisList.size() - 1);
                this.ep.deleteById(recordsDto.getId());
            }
            this.ep.insert(dsaHistoricRecords);
            return;
        }
        dto.setCreateTime(DateUtils.getTime());
        this.ep.update(dto);
    }
}
