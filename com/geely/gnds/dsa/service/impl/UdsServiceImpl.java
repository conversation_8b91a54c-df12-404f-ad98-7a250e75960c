package com.geely.gnds.dsa.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.tcp.DoipMessageContanier;
import com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener;
import com.geely.gnds.doip.client.tcp.FdFunctionalAddressingReceiveListener;
import com.geely.gnds.doip.client.tcp.FdTcpClient;
import com.geely.gnds.dsa.enums.DsaAddressConst;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.dsa.service.UdsService;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.enums.TesterNegativeResEnum;
import com.geely.gnds.tester.seq.IQopUdsDataQueuer;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.util.CommonFunctionsUtils;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import com.geely.gnds.tester.util.SecurityUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
/* loaded from: UdsServiceImpl.class */
public class UdsServiceImpl implements UdsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UdsServiceImpl.class);
    private SingletonManager manager = SingletonManager.getInstance();
    private DoipUtil doipUtil = DoipUtil.getInstance();

    @Override // com.geely.gnds.dsa.service.UdsService
    public String udsData(String targetAdd, String commandAfter, String vin) throws Exception {
        LOGGER.info("线程【{}-{}】发送UDS指令：{}", new Object[]{Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName(), commandAfter});
        String command = commandAfter;
        if (commandAfter.contains(ConstantEnum.EMPTY)) {
            command = command.replaceAll(ConstantEnum.EMPTY, "");
        }
        String udsString = getUdsString(targetAdd, command);
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        if (client == null) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115);
            LOGGER.error("{}【{}】", formatMsg, vin);
            throw new DoipException(formatMsg);
        }
        FdDsaLogger fdDsaDsaTxtLogger = client.getFdDsaLogger();
        String finalCommand = DoipUtil.getDsaLogCommand(command);
        Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
            logger.write(DsaLogTypeEnum.COMMENT.getValue(), "# Sending Request：Tester -> " + targetAdd + ConstantEnum.EMPTY + finalCommand);
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
        });
        JSONObject inputObj = JSONObject.parseObject(udsString);
        String doipInstructionCode = inputObj.getString("Data");
        String targetAddress = inputObj.getString("Target_address");
        String send = doipInstructionCode.length() > 9 ? doipInstructionCode.substring(0, 10) : doipInstructionCode;
        int targetAddressInt = this.doipUtil.hexString2Int(targetAddress);
        DoipMessageContanier contanier = new DoipMessageContanier();
        contanier.setFdDsaLogger(client.getFdDsaLogger());
        try {
            if (DsaAddressConst.FUNCTIONAL_ADDRESSING.equalsIgnoreCase(targetAddress)) {
                FdDoipTcpReceiveListener listener = new FdFunctionalAddressingReceiveListener(doipInstructionCode, targetAddressInt, contanier);
                contanier.setStartTime(Long.valueOf(System.currentTimeMillis()));
                contanier.setSendData(finalCommand);
                client.sendDoipMessage(targetAddress, doipInstructionCode, listener, (IQopUdsDataQueuer) null, false);
                String received = contanier.getFunctionalAddressingMessagesAndAddress();
                if (StringUtils.isBlank(received)) {
                    Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger2 -> {
                        logger2.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "No ECU Responding");
                        logger2.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
                    });
                }
                return contanier.getFunctionalAddressingResponse();
            }
            FdDoipTcpReceiveListener listener2 = client.getIdleReceiveListener();
            listener2.setFdDsaLogger(fdDsaDsaTxtLogger);
            listener2.setDoipInfo(true, doipInstructionCode, targetAddressInt, contanier, send, targetAddress, client.isSwitch(targetAddress));
            long start = System.currentTimeMillis();
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger3 -> {
                logger3.write(DsaLogTypeEnum.SEND_UDS.getValue(), "Tester -> " + targetAdd + ConstantEnum.EMPTY + finalCommand);
            });
            client.sendDoipMessage(inputObj.getString("Target_address"), doipInstructionCode, listener2, (IQopUdsDataQueuer) null, true);
            String response = contanier.getResponse(doipInstructionCode.substring(2));
            String strByJsonNodeExpr = ObjectMapperUtils.findStrByJsonNodeExpr(response, "/ECU_Response_value/Data_Value");
            long end = System.currentTimeMillis();
            DoipException e = listener2.getLastDoipException();
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger4 -> {
                if (e == null) {
                    logger4.write(DsaLogTypeEnum.SEND_UDS.getValue(), "Complete Response：" + targetAdd + ConstantEnum.EMPTY + DoipUtil.getDsaLogCommand(strByJsonNodeExpr));
                }
                logger4.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "Time stamp：" + end);
                logger4.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "Time between request and response(P2 time)：" + (end - start) + " ms");
                logger4.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
            });
            if (e != null) {
                throw e;
            }
            if (contanier.isDiagnosticMessageNegAck()) {
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00161));
            }
            return strByJsonNodeExpr;
        } catch (Exception e2) {
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger5 -> {
                logger5.write(DsaLogTypeEnum.ERROR.getValue(), e2.getMessage());
            });
            throw e2;
        }
    }

    @Override // com.geely.gnds.dsa.service.UdsService
    public String udsDataNoLog(String targetAdd, String commandAfter, String vin) throws Exception {
        return udsDataNoLog(targetAdd, commandAfter, vin, 2);
    }

    @Override // com.geely.gnds.dsa.service.UdsService
    public String udsDataNoLog(String targetAdd, String commandAfter, String vin, int errorHandlingType) throws Exception {
        LOGGER.info("线程【{}-{}】发送UDS指令：{}", new Object[]{Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName(), commandAfter});
        String command = commandAfter;
        if (commandAfter.contains(ConstantEnum.EMPTY)) {
            command = command.replaceAll(ConstantEnum.EMPTY, "");
        }
        String udsString = getUdsString(targetAdd, command);
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        if (client == null) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115);
            LOGGER.error("{}【{}】", formatMsg, vin);
            throw new DoipException(formatMsg);
        }
        FdDsaLogger fdDsaDsaTxtLogger = client.getFdDsaLogger();
        String finalCommand = DoipUtil.getDsaLogCommand(command);
        JSONObject inputObj = JSONObject.parseObject(udsString);
        String doipInstructionCode = inputObj.getString("Data");
        String targetAddress = inputObj.getString("Target_address");
        String send = doipInstructionCode.length() > 9 ? doipInstructionCode.substring(0, 10) : doipInstructionCode;
        int targetAddressInt = this.doipUtil.hexString2Int(targetAddress);
        DoipMessageContanier contanier = new DoipMessageContanier();
        contanier.setFdDsaLogger(client.getFdDsaLogger());
        try {
            if (DsaAddressConst.FUNCTIONAL_ADDRESSING.equalsIgnoreCase(targetAddress)) {
                FdDoipTcpReceiveListener listener = new FdFunctionalAddressingReceiveListener(doipInstructionCode, targetAddressInt, contanier);
                contanier.setStartTime(Long.valueOf(System.currentTimeMillis()));
                contanier.setSendData(finalCommand);
                client.sendDoipMessage(targetAddress, doipInstructionCode, listener, (IQopUdsDataQueuer) null, false);
                return contanier.getFunctionalAddressingResponse();
            }
            FdDoipTcpReceiveListener listener2 = client.getIdleReceiveListener();
            listener2.setFdDsaLogger(fdDsaDsaTxtLogger);
            listener2.setDoipInfo(true, doipInstructionCode, targetAddressInt, contanier, send, targetAddress, client.isSwitch(targetAddress));
            client.sendDoipMessage(inputObj.getString("Target_address"), doipInstructionCode, listener2, (IQopUdsDataQueuer) null, true);
            String response = contanier.getResponse(doipInstructionCode.substring(2));
            String strByJsonNodeExpr = ObjectMapperUtils.findStrByJsonNodeExpr(response, "/ECU_Response_value/Data_Value");
            DoipException e = listener2.getLastDoipException();
            if (e != null) {
                throw e;
            }
            if (contanier.isDiagnosticMessageNegAck()) {
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00161));
            }
            return strByJsonNodeExpr;
        } catch (DoipException e2) {
            LOGGER.warn("发送uds指令异常{}", e2.getMessage());
            if (errorHandlingType == ConstantEnum.TWO.intValue()) {
                throw e2;
            }
            TesterErrorCodeEnum enumByCodeOrStr = TesterErrorCodeEnum.getEnumByCodeOrStr(e2.getMessage());
            String code = TesterNegativeResEnum.a(enumByCodeOrStr);
            if (com.geely.gnds.ruoyi.common.utils.StringUtils.isNotBlank(code)) {
                return "7F" + doipInstructionCode.substring(0, 2) + code;
            }
            return "";
        }
    }

    public String getUdsString(String targetAddress, String data) {
        String res = "{\"Source_address\": \"0E80\",\"Target_address\": \"" + targetAddress + "\",\"Data\": \"" + data + "\",\"Retries\": \"0\",\"Delay\": \"500\",\"ErrorHandlingType\": \"2\"}";
        return res;
    }

    @Override // com.geely.gnds.dsa.service.UdsService
    public Boolean getSecurityAccess(String targetAdd, String commandAa, String vin, String fixByte, boolean needLog) throws Exception {
        String platform;
        int parseInt = Integer.parseInt(commandAa, 16);
        String commandBb = CommonFunctionsUtils.intToHexString(parseInt + 1, 1);
        FdTcpClient client = this.manager.getFdTcpClientAndDsa(vin);
        if (client == null) {
            String formatMsg = TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00115);
            LOGGER.error("{}【{}】", formatMsg, vin);
            throw new DoipException(formatMsg);
        }
        LOGGER.info("DSA 27安全算法，指令1【{}】指令2【{}】fixBytes【{}】", new Object[]{commandAa, commandBb, fixByte});
        FdDsaLogger fdDsaDsaTxtLogger = client.getFdDsaLogger();
        Boolean succes = false;
        try {
            String udsData = udsData(targetAdd, "27" + commandAa, vin);
            String seed = udsData.substring(4);
            Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger -> {
                if (needLog) {
                    logger.write(DsaLogTypeEnum.SECURITY_ACCESS.getValue(), "Seed is received from ECU.");
                }
            });
            if ("000000".equals(seed)) {
                succes = true;
            } else {
                if (seed.length() == 6) {
                    platform = ConstantEnum.GEEA2;
                } else if (seed.length() == 32) {
                    platform = ConstantEnum.GEEA3;
                } else {
                    platform = "";
                }
                boolean check = checkFixByte(fixByte, platform);
                if (!check) {
                    Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger2 -> {
                        logger2.write(DsaLogTypeEnum.ERROR.getValue(), "pincode length doesn’t match current vehicle platform, please change vehicle platform.");
                    });
                    if (needLog) {
                        Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger3 -> {
                            logger3.write(DsaLogTypeEnum.SECURITY_ACCESS.getValue(), "pincode length doesn’t match current vehicle platform, please change vehicle platform.");
                        });
                    }
                    return false;
                }
                LOGGER.info("DSA 27安全算法，fixByte【{}】seed【{}】", fixByte, seed);
                String securityKey = "";
                if (ConstantEnum.GEEA2.equals(platform)) {
                    securityKey = SecurityUtils.securityAccess(fixByte, seed);
                } else if (ConstantEnum.GEEA3.equals(platform)) {
                    securityKey = SecurityUtils.cmac(fixByte, seed);
                }
                LOGGER.info("DSA 27安全算法，securityKey【{}】", securityKey);
                String dllOutput = "27" + commandBb + securityKey;
                String key = udsData(targetAdd, dllOutput, vin);
                if (!"7F2735".contains(key) && !"7F2736".contains(key) && !"7F2737".contains(key)) {
                    succes = true;
                }
            }
        } catch (Exception e) {
            if (e instanceof DoipException) {
                LOGGER.error("安全算法失败：", e.getMessage());
            } else {
                Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger4 -> {
                    logger4.write(DsaLogTypeEnum.ERROR.getValue(), e.getMessage());
                });
            }
        }
        Boolean finalSucces = succes;
        Optional.ofNullable(fdDsaDsaTxtLogger).ifPresent(logger5 -> {
            if (needLog) {
                if (finalSucces.booleanValue()) {
                    logger5.write(DsaLogTypeEnum.SECURITY_ACCESS.getValue(), "ECU is unlocked.");
                } else {
                    logger5.write(DsaLogTypeEnum.SECURITY_ACCESS.getValue(), "ECU unlock fail.");
                }
            }
        });
        return succes;
    }

    @Override // com.geely.gnds.dsa.service.UdsService
    public Boolean getSecurityAccess(String targetAdd, String commandAa, String vin, String fixByte) throws Exception {
        return getSecurityAccess(targetAdd, commandAa, vin, fixByte, false);
    }

    private boolean checkFixByte(String fixByte, String platform) {
        if (ConstantEnum.GEEA2.equals(platform)) {
            if (fixByte.length() == 10) {
                return true;
            }
            return false;
        }
        if (ConstantEnum.GEEA3.equals(platform) && fixByte.length() == 32) {
            return true;
        }
        return false;
    }

    @Override // com.geely.gnds.dsa.service.UdsService
    public String sendUds(String address, String command, String vin) {
        try {
            String response = udsDataNoLog(address, command, vin);
            return response.substring(6);
        } catch (Exception e) {
            return "";
        }
    }

    private String getNegRes(String msg) {
        Map<String, Object> data = new HashMap<>(2);
        Map<String, Object> value = new HashMap<>(2);
        Map<String, Object> type = new HashMap<>(2);
        value.put("Data_Type", ConstantEnum.STRING);
        type.put("Data_Type", ConstantEnum.STRING);
        type.put("Data_Value", "Negative");
        data.put("ECU_Response_value", value);
        data.put("ECU_Response_Type", type);
        return ObjectMapperUtils.obj2JsonStr(data);
    }
}
