package com.geely.gnds.dsa.service;

import com.geely.gnds.dsa.dto.DsaCustomButtonDTO;
import java.util.List;

/* loaded from: DsaCustomButtonService.class */
public interface DsaCustomButtonService {
    DsaCustomButtonDTO queryById(Integer num);

    List<DsaCustomButtonDTO> queryAll();

    boolean insert(DsaCustomButtonDTO dsaCustomButtonDTO);

    boolean update(DsaCustomButtonDTO dsaCustomButtonDTO);
}
