package com.geely.gnds.dsa.service;

import com.geely.gnds.ruoyi.project.system.domain.SysUser;
import com.geely.gnds.tester.dto.VehicleDto;
import java.util.List;

/* loaded from: VehicleService.class */
public interface VehicleService {
    void connectTcp(VehicleDto vehicleDto, SysUser sysUser) throws Exception;

    List<VehicleDto> query(String str) throws Exception;

    Boolean checkConnectStatus(String str);

    void disconnect(String str, String str2) throws Exception;
}
