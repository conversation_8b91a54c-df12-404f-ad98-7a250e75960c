package com.geely.gnds.dsa.service;

import com.geely.gnds.dsa.dto.DsaDiagnosticRunResultDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticSeqFileDTO;
import com.geely.gnds.dsa.dto.DsaDiagnosticSeqLineDTO;
import com.geely.gnds.dsa.dto.PowerModeDto;
import com.geely.gnds.dsa.dto.SwitchPowerModeDto;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/* loaded from: DsaDiagnosticService.class */
public interface DsaDiagnosticService {
    String getSeqPath();

    List<String> getSeqFileList();

    Boolean setSeqPath(String str);

    String saveSeqFile(DsaDiagnosticSeqFileDTO dsaDiagnosticSeqFileDTO) throws IOException;

    List<DsaDiagnosticSeqLineDTO> load(String str) throws IOException;

    List<DsaDiagnosticSeqLineDTO> load(MultipartFile multipartFile) throws IOException;

    DsaDiagnosticRunResultDTO sequenceCheck(String str, String str2, List<DsaDiagnosticSeqLineDTO> list);

    DsaDiagnosticRunResultDTO sequenceRun(String str, String str2, String str3, List<DsaDiagnosticSeqLineDTO> list);

    Boolean stopRun(String str, String str2);

    void showMessage(String str, String str2, String str3);

    List<PowerModeDto> getPowerMode();

    void switchPowerMode(SwitchPowerModeDto switchPowerModeDto);

    DsaDiagnosticRunResultDTO allDidRun(String str, String str2, String str3, String str4) throws Exception;
}
