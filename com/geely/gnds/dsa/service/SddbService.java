package com.geely.gnds.dsa.service;

import com.geely.gnds.dsa.dto.DiaDtcDTO;
import com.geely.gnds.dsa.dto.DiaEcuSwDTO;
import com.geely.gnds.dsa.dto.DiaRoutineIdentifierDTO;
import com.geely.gnds.dsa.dto.DiaTreeNode;
import com.geely.gnds.dsa.vo.SddbUploadVo;
import com.geely.gnds.tester.dto.DiagResItemGroupDto;
import com.geely.gnds.tester.util.Result;
import java.util.List;

/* loaded from: SddbService.class */
public interface SddbService {
    List<DiaEcuSwDTO> importSddb(SddbUploadVo sddbUploadVo) throws Exception;

    List<DiagResItemGroupDto> getDidList(String str, String str2) throws Exception;

    List<DiaRoutineIdentifierDTO> getRoutineIdentifierList(String str, String str2) throws Exception;

    Result<List<DiaEcuSwDTO>> getEcuList();

    List<String> getEcuAddressList() throws Exception;

    Result<Object> getSecurityAreaList(String str, String str2);

    List<DiagResItemGroupDto> getDidByDiagnosisNumAndDid(String str, String str2, List<String> list);

    List<DiaDtcDTO> getDtcList(String str) throws Exception;

    String getDiagPartNumByAddress(String str) throws Exception;

    List<DiaTreeNode> getElement(DiaTreeNode diaTreeNode) throws Exception;

    List<DiaEcuSwDTO> getTargetEcu(String str) throws Exception;
}
