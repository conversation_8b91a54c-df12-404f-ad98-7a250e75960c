package com.geely.gnds.dsa.service;

import com.geely.gnds.dsa.dto.PinCodeDTO;
import com.geely.gnds.dsa.dto.SaveDsaPinCodeReqDTO;
import java.io.IOException;
import java.util.List;

/* loaded from: PinCodeService.class */
public interface PinCodeService {
    List<PinCodeDTO> getLocalPinCode(String str) throws IOException;

    Boolean savePinCode(String str, SaveDsaPinCodeReqDTO saveDsaPinCodeReqDTO) throws IOException;

    void cleanCache(String str);

    List<PinCodeDTO> getCloudPinCode(String str, List<PinCodeDTO> list) throws Exception;

    Boolean clearCloudPinCode(String str, PinCodeDTO pinCodeDTO);
}
