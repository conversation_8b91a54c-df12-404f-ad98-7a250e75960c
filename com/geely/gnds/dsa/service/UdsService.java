package com.geely.gnds.dsa.service;

/* loaded from: UdsService.class */
public interface UdsService {
    String udsData(String str, String str2, String str3) throws Exception;

    String udsDataNoLog(String str, String str2, String str3) throws Exception;

    Boolean getSecurityAccess(String str, String str2, String str3, String str4) throws Exception;

    String udsDataNoLog(String str, String str2, String str3, int i) throws Exception;

    Boolean getSecurityAccess(String str, String str2, String str3, String str4, boolean z) throws Exception;

    String sendUds(String str, String str2, String str3);
}
