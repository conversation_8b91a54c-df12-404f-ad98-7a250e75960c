package com.geely.gnds;

import cn.hutool.core.io.FileUtil;
import com.aliyuncs.utils.StringUtils;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.pcap.DoipAddress;
import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.cache.MapDBUtil;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.common.AppConfig;
import com.geely.gnds.tester.compile.MemoryClassLoader;
import com.geely.gnds.tester.component.Gc;
import com.geely.gnds.tester.dto.LoginDto;
import com.geely.gnds.tester.enums.I18nKeyEnums;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.service.ReadoutCacheService;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.OpenOption;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Bean;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

@EnableScheduling
@ServletComponentScan
@SpringBootApplication(scanBasePackages = {"com.geely.gnds"})
/* loaded from: TesterApplication.class */
public class TesterApplication {
    private static final Logger LOG = LoggerFactory.getLogger(TesterApplication.class);

    static {
        System.setProperty("GNDS_LOG_HOME", FileUtil.file(AppConfig.getAppDataDir(), "applog").getAbsolutePath());
        System.setProperty("spring.devtools.restart.enabled", "false");
    }

    public static void main(String[] args) throws IOException {
        long startTime = System.currentTimeMillis();
        LOG.info("诊断仪客户端启动时间：{}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS, new Date(startTime)));
        LOG.info(I18nKeyEnums.LOADING_TENANT.getKey());
        String gcId = System.getenv("gc.id");
        if (!StringUtils.isEmpty(gcId)) {
            Gc.getInstance().init(gcId);
        }
        SpringApplicationBuilder builder = new SpringApplicationBuilder(new Class[]{TesterApplication.class});
        builder.headless(false).run(args);
        a();
        String name = ManagementFactory.getRuntimeMXBean().getName();
        System.out.println(name);
        String pid = name.split("@")[0];
        LOG.info("诊断仪客户端版本：********");
        a(pid);
        try {
            MemoryClassLoader.genInstance();
        } catch (Exception e) {
            System.out.println("预加载jar失败");
        }
        LOG.info("诊断仪客户端启动完成！耗时：{} ms", Optional.of(Long.valueOf(System.currentTimeMillis() - startTime)));
    }

    private static void a() {
        ((TaskExecutor) SpringUtils.getBean(TaskExecutor.class)).execute(TesterApplication::b);
    }

    private static void b() throws UnknownHostException {
        LOG.info(I18nKeyEnums.INITIALIZE_NIC.getKey());
        LOG.info("网卡初始化开始：{}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS, new Date()));
        DoipAddressManager manager = DoipAddressManager.getInstance();
        try {
            manager.init();
        } catch (SocketException e) {
            LOG.error(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00135), e);
        }
        LOG.info("网卡初始化结束：{}", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS, new Date()));
        DoipAddress local = manager.getSelectedLocalDoipAddress();
        if (local != null) {
            String mac = DoipUtil.getInstance().macToHexString(local.getMac());
            System.out.println("诊断仪客户端IP地址：" + local.getIp());
            System.out.println("诊断仪客户端MAC地址：" + mac);
            LoginDto.setMac(mac);
            TokenManager tokenManager = (TokenManager) SpringUtils.getBean(TokenManager.class);
            tokenManager.init(mac);
            d();
        }
        c();
    }

    private static void c() {
        try {
            ReadoutCacheService readoutCacheService = (ReadoutCacheService) SpringUtils.getBean(ReadoutCacheService.class);
            readoutCacheService.deleteReadoutCache();
        } catch (Exception e) {
            LOG.error("清除七天缓存失败", e);
        }
        try {
            DoipAddressManager.getInstance().getInetAddress();
        } catch (Exception e2) {
            LOG.error("更新网卡列表失败", e2);
        }
    }

    private static void a(String pid) throws IOException {
        String baseDir = AppConfig.getAppDataDir().getAbsolutePath();
        String fileName = baseDir + "\\pid.txt";
        Path path = Paths.get(fileName, new String[0]);
        File file = new File(baseDir, fileName);
        if (file.exists()) {
            file.delete();
        }
        try {
            BufferedWriter writer = Files.newBufferedWriter(path, StandardCharsets.UTF_8, new OpenOption[0]);
            Throwable th = null;
            try {
                writer.write(pid);
                if (writer != null) {
                    if (0 != 0) {
                        try {
                            writer.close();
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                        }
                    } else {
                        writer.close();
                    }
                }
            } finally {
            }
        } catch (IOException e) {
            System.out.println(e);
        }
    }

    private static void d() {
        try {
            ((MapDBUtil) SpringUtils.getBean(MapDBUtil.class)).init();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    @Bean
    public ServerEndpointExporter e() {
        return new ServerEndpointExporter();
    }
}
