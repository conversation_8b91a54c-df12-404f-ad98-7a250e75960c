package com.geely.gnds.doip.client;

import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: StreamBuffer.class */
public class StreamBuffer {
    private static final Logger logger = LoggerFactory.getLogger(StreamBuffer.class);
    private final DoipUtil util = DoipUtil.getInstance();
    private volatile byte[] data = new byte[0];

    public synchronized byte[] getData() {
        return this.data;
    }

    public synchronized int getLength() {
        return this.data.length;
    }

    public synchronized void append(byte[] newData) {
        logger.debug("Append: " + newData.length + " bytes: " + this.util.byteArrayToHexString(newData));
        int newLength = this.data.length + newData.length;
        byte[] newBuffer = new byte[newLength];
        System.arraycopy(this.data, 0, newBuffer, 0, this.data.length);
        System.arraycopy(newData, 0, newBuffer, this.data.length, newData.length);
        this.data = newBuffer;
    }

    public synchronized byte[] remove(int length) {
        logger.debug("Remove " + length + " bytes");
        if (length >= this.data.length) {
            byte[] ret = this.data;
            this.data = new byte[0];
            return ret;
        }
        byte[] ret2 = Arrays.copyOf(this.data, length);
        int remainingLength = this.data.length - length;
        byte[] remaining = new byte[remainingLength];
        System.arraycopy(this.data, length, remaining, 0, remainingLength);
        this.data = remaining;
        return ret2;
    }

    public synchronized void clear() {
        logger.debug("Remove all bytes");
        this.data = new byte[0];
    }
}
