package com.geely.gnds.doip.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessageNegAck;
import com.geely.gnds.doip.client.message.response.DoipTcpRoutingActivationResponse;
import com.geely.gnds.doip.client.pcap.DoipAddress;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.sun.jna.Function;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.CRC32;
import java.util.zip.Checksum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUtil.class */
public class DoipUtil {
    public static final int MAX_BYTE_ARRAY_SIZE = 1024;
    private final int MAX_DATA_LENGTH_LOG = 30;
    private final String FORMART_DOIP_INSTRUCTION_TYPE_ERROR = "DOIP指令格式({0})错误！";
    private final String FORMART_DOIP_INSTRUCTION_TYPE_NO = "DOIP指令(0x{0})错误！";
    private final String FORMART_DOIP_ADDRESS = "DOIP地址(0x{0})错误！";
    public final byte[] MAC_BROADCAST = {-1, -1, -1, -1, -1, -1};
    public final byte[] MAC_LOCAL = {0, 0, 0, 0, 0, 0};
    public final byte[] MAC_REMOTE = {0, 12, 41, -115, 111, -22};
    private Map<Integer, DoipInstructionType> map = new HashMap();
    private final String HEX_DIGITAL = "0123456789ABCDEF";
    private final char[] hexArray = "0123456789ABCDEF".toCharArray();
    private final String DOIP_RESULT_SUCCESS = "Successful";
    private final String DOIP_RESULT_PARAMETER = "Parameters";
    private final String DOIP_RESULT_RAW = "Raw";
    private final String DOIP_SUCCESS_TRUE = ConstantEnum.TRUE;
    private final String DOIP_SUCCESS_FALSE = ConstantEnum.FALSE;
    private final String SPLIT_DOT = "\\.";
    private final String REG_BRACKET_LEFT = "\\[";
    private final String REG_BRACKET_RIGHT = "\\]";
    private final int PAYLOAD_DOIP_LENGTH = 8;
    private static final Logger logger = LoggerFactory.getLogger(DoipUtil.class);
    private static final DoipUtil INSTANCE = new DoipUtil();

    private DoipUtil() {
        addDoipInstructionType(DoipInstructionType.ClearDiagnosticInformation);
        addDoipInstructionType(DoipInstructionType.CommunicationControl);
        addDoipInstructionType(DoipInstructionType.ControlDTCSetting);
        addDoipInstructionType(DoipInstructionType.DiagnosticSessionControl);
        addDoipInstructionType(DoipInstructionType.ECUReset);
        addDoipInstructionType(DoipInstructionType.ReadDataByIdentifier);
        addDoipInstructionType(DoipInstructionType.ReadDTCInformation);
        addDoipInstructionType(DoipInstructionType.RequestDownload);
        addDoipInstructionType(DoipInstructionType.RequestFileTransfer);
        addDoipInstructionType(DoipInstructionType.RequestTransferExit);
        addDoipInstructionType(DoipInstructionType.RequestUpload);
        addDoipInstructionType(DoipInstructionType.RoutineControl);
        addDoipInstructionType(DoipInstructionType.RoutingActivation);
        addDoipInstructionType(DoipInstructionType.SecurityAccess);
        addDoipInstructionType(DoipInstructionType.TesterPresent);
        addDoipInstructionType(DoipInstructionType.TransferData);
        addDoipInstructionType(DoipInstructionType.WriteDataByIdentifier);
        addDoipInstructionType(DoipInstructionType.InputOutputControlByIdentifier);
        addDoipInstructionType(DoipInstructionType.ReadMemoryByAddress);
        addDoipInstructionType(DoipInstructionType.ReadDataByPeriodicldentifier);
        addDoipInstructionType(DoipInstructionType.DynamicallyDefineDataIdentifier);
        addDoipInstructionType(DoipInstructionType.WriteMemoryByAddress);
    }

    public static DoipUtil getInstance() {
        return INSTANCE;
    }

    private void addDoipInstructionType(DoipInstructionType type) {
        this.map.put(Integer.valueOf(type.getInstructionVaue()), type);
    }

    public DoipInstructionType getDoipInstructionType(String code) throws DoipException {
        return getDoipInstructionType(hexString2Byte(code));
    }

    public DoipInstructionType getDoipInstructionType(int code) throws DoipException {
        DoipInstructionType type = this.map.get(Integer.valueOf(code));
        if (type == null) {
            if (code < 0) {
                code += Function.MAX_NARGS;
            }
            type = DoipInstructionType.Default;
            String codeHex = Integer.toHexString(code).toUpperCase();
            String postCodeHex = Integer.toHexString(code + 64).toUpperCase();
            type.initValue(code, postCodeHex, codeHex);
        }
        return type;
    }

    public String int2String(int value) {
        String hex = Integer.toHexString(value & 255);
        if (hex.length() == 1) {
            return "0" + hex;
        }
        return hex;
    }

    public String int2HexString(int value) {
        byte[] data = {(byte) ((value >> 8) & 255), (byte) (value & 255)};
        return bytesToHexString2(data, 2);
    }

    public byte[] int2Byte4(int val) {
        byte[] b = {(byte) (val & 255), (byte) ((val >> 8) & 255), (byte) ((val >> 16) & 255), (byte) ((val >> 24) & 255)};
        return b;
    }

    public byte[] int2Byte2(int val) {
        byte[] b = {(byte) (val & 255), (byte) ((val >> 8) & 255)};
        return b;
    }

    public byte int2Byte(int value) {
        return new Integer(value).byteValue();
    }

    private byte[] hexString2Bytes(String hexString, int preLength) {
        if (hexString == null || hexString.length() < 1) {
            return preLength > 0 ? new byte[preLength] : new byte[0];
        }
        int len = hexString.length();
        int preLength2 = preLength < 0 ? 0 : preLength;
        byte[] data = new byte[preLength2 + (len / 2)];
        int iIntValue = 0;
        while (true) {
            int i = iIntValue;
            if (i < len) {
                data[preLength2 + (i / 2)] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4) + Character.digit(hexString.charAt(i + 1), 16));
                iIntValue = i + ConstantEnum.TWO.intValue();
            } else {
                return data;
            }
        }
    }

    public byte hexString2Byte(String hexString) throws DoipException {
        if (hexString == null || hexString.length() != ConstantEnum.TWO.intValue()) {
            throw new DoipException(MessageFormat.format("DOIP指令格式({0})错误！", hexString));
        }
        String hexString2 = hexString.toUpperCase();
        char[] hexs = hexString2.toCharArray();
        int hight = "0123456789ABCDEF".indexOf(hexs[0]);
        int low = "0123456789ABCDEF".indexOf(hexs[1]);
        if (hight < 0 || low < 0) {
            throw new DoipException(MessageFormat.format("DOIP指令格式({0})错误！", hexString2));
        }
        return Integer.valueOf((hight * 16) + low).byteValue();
    }

    public int hexString2Int(String hexString) throws DoipException {
        if (hexString == null || hexString.length() != ConstantEnum.FORE.intValue()) {
            throw new DoipException(MessageFormat.format("DOIP地址(0x{0})错误！", hexString));
        }
        String hexString2 = hexString.toUpperCase();
        char[] hexs = hexString2.toCharArray();
        int b0 = "0123456789ABCDEF".indexOf(hexs[3]);
        int b1 = "0123456789ABCDEF".indexOf(hexs[2]);
        int b2 = "0123456789ABCDEF".indexOf(hexs[1]);
        int b3 = "0123456789ABCDEF".indexOf(hexs[0]);
        if (b0 < 0 || b1 < 0 || b2 < 0 || b3 < 0) {
            throw new DoipException(MessageFormat.format("DOIP地址(0x{0})错误！", hexString2));
        }
        return Integer.valueOf((b3 * 16 * 16 * 16) + (b2 * 16 * 16) + (b1 * 16) + b0).intValue();
    }

    public byte[] hexString2Bytes(String hexString) {
        return hexString2Bytes(hexString, 0);
    }

    public byte[] hexString2DoipMessage(String hexString) {
        return hexString2Bytes(hexString, 2);
    }

    public byte[] getRequestMessage(DoipInstructionType instructionType, byte sfByte, String parameter) {
        switch (instructionType) {
            case RequestTransferExit:
                return new byte[]{int2Byte(instructionType.getInstructionVaue())};
            case SecurityAccess:
                if (sfByte == 2) {
                    byte[] message = hexString2DoipMessage(parameter);
                    message[0] = int2Byte(instructionType.getInstructionVaue());
                    message[1] = sfByte;
                    return message;
                }
                break;
        }
        if (parameter != null && parameter.length() > 0) {
            byte[] message2 = hexString2DoipMessage(parameter);
            message2[0] = int2Byte(instructionType.getInstructionVaue());
            message2[1] = sfByte;
            return message2;
        }
        return new byte[]{int2Byte(instructionType.getInstructionVaue()), sfByte};
    }

    public String checkDoipPosAck(String message, int subFunction, DoipInstructionType type) {
        if (message == null) {
            return "";
        }
        switch (type) {
            case RequestTransferExit:
            case ClearDiagnosticInformation:
            case RequestUpload:
            case RequestFileTransfer:
                return getSuccessJson(type.getPosAckCode().equalsIgnoreCase(message)).toJSONString();
            case SecurityAccess:
            default:
                JSONObject data = getSuccessJson(message.toLowerCase().startsWith(type.getPosAckCode().toLowerCase() + ConstantEnum.EMPTY + int2String(subFunction).toLowerCase()));
                JSONArray parameters = new JSONArray();
                String[] datas = message.split(ConstantEnum.EMPTY);
                for (int i = 2; i < datas.length; i++) {
                    parameters.add(datas[i]);
                }
                data.put("Parameters", parameters);
                data.put("message", message);
                data.put("posAckCode", type.getPosAckCode());
                data.put("subFunction", int2String(subFunction));
                data.put("typeName", type.name());
                return data.toJSONString();
            case RequestDownload:
                return getSuccessJson(message.startsWith(type.getPosAckCode())).toJSONString();
        }
    }

    private JSONObject getSuccessJson(boolean success) {
        JSONObject data = new JSONObject();
        data.put("Successful", success ? ConstantEnum.TRUE : ConstantEnum.FALSE);
        return data;
    }

    private JSONObject toJson(String data) {
        return JSONObject.parseObject(data);
    }

    private String getValueFromObject(String[] names, Object parent, int current) throws DoipException, NumberFormatException {
        String[] arrName = names[current].replaceAll("\\]", "").split("\\[");
        if (!(parent instanceof JSONObject)) {
            throw new DoipException("子表达式的父数据" + parent.toString() + "的类型是" + parent.getClass().getName() + "，格式错误，应为JSONObject类型。");
        }
        if (arrName.length < ConstantEnum.TWO.intValue()) {
            Object value = ((JSONObject) parent).get(arrName[0]);
            if (value == null) {
                throw new DoipException("父数据" + parent.toString() + "中子表达式<" + arrName[0] + ">无数据。");
            }
            if (current < names.length - 1) {
                if ("Raw".equals(names[current + 1])) {
                    return getRawData((JSONObject) parent, value, names[current]);
                }
                return getValueFromObject(names, value, current + 1);
            }
            return getRawData((JSONObject) parent, value, names[current]);
        }
        int i = Integer.parseInt(arrName[1]);
        Object element = getElementFromParent((JSONObject) parent, arrName[0], i);
        if (element == null) {
            throw new DoipException("父数据" + parent.toString() + "中<" + arrName[0] + ">数组下标(" + i + ")无数据。");
        }
        if (current < names.length - 1) {
            if ("Raw".equals(names[current + 1])) {
                return getRawData((JSONObject) parent, element, names[current]);
            }
            return getValueFromObject(names, element, current + 1);
        }
        return getRawData((JSONObject) parent, element, names[current]);
    }

    private String getRawData(JSONObject parent, Object value, String expression) throws DoipException {
        if ((value instanceof JSONObject) || (value instanceof JSONArray)) {
            throw new DoipException("数据中子表达式<" + expression + ">的结果" + value.toString() + "不可以是JSONObject、JSONArray。");
        }
        return value instanceof String ? (String) value : value.toString();
    }

    private Object getElementFromParent(JSONObject parent, String elementName, int index) throws DoipException {
        Object element = parent.get(elementName);
        if (element == null) {
            throw new DoipException("父数据" + parent.toJSONString() + "中未包含<" + elementName + ">数组。");
        }
        if (!(element instanceof JSONArray)) {
            throw new DoipException("父数据" + parent.toJSONString() + "中<" + elementName + ">数据不是数组。");
        }
        JSONArray arr = (JSONArray) element;
        if (index >= arr.size() || index < 0) {
            throw new DoipException("父数据" + parent.toJSONString() + "中<" + elementName + ">数组下标(" + index + ")越界。");
        }
        return arr.get(index);
    }

    private String calcExpression(JSONObject data, String expression) throws DoipException {
        String[] names = expression.split("\\.");
        if (names.length < ConstantEnum.TWO.intValue()) {
            return expression;
        }
        return getValueFromObject(names, data, 1);
    }

    public boolean validateMessage(String methodResult, String leftExpression, String rightExpression) throws DoipException {
        if (methodResult == null || methodResult.length() < 1) {
            return false;
        }
        JSONObject data = toJson(methodResult);
        String left = calcExpression(data, leftExpression);
        String right = calcExpression(data, rightExpression);
        return left.equalsIgnoreCase(right);
    }

    public String toHexString(byte[] data) {
        return toHexString(data, 0);
    }

    public String toHexStringShort(byte[] data) {
        if (data.length > 30) {
            byte[] shortData = Arrays.copyOf(data, 30);
            return toHexString(shortData, 0) + " ... ...";
        }
        return toHexString(data, 0);
    }

    public String toHexString(byte data) {
        String hex = Integer.toHexString(data & 255);
        return hex.length() == 1 ? "0" + hex : hex;
    }

    public byte[] ip2Bytes(String ip) throws DoipException {
        if (ip == null || !ip.matches("[0-9]{1,3}[.][0-9]{1,3}[.][0-9]{1,3}[.][0-9]{1,3}")) {
            throw new DoipException("IP地址(" + ip + ")格式错误。");
        }
        String[] datas = ip.split("\\.");
        byte[] ipBytes = new byte[4];
        ipBytes[0] = 0;
        ipBytes[1] = 0;
        ipBytes[2] = 0;
        ipBytes[3] = 0;
        for (int i = 0; i < 4; i++) {
            try {
                ipBytes[i] = Integer.valueOf(datas[i]).byteValue();
            } catch (NumberFormatException e) {
                throw new DoipException("IP地址(" + ip + ")格式错误。");
            }
        }
        return ipBytes;
    }

    public String toHexString(byte[] data, int srcPos) {
        StringBuffer sb = new StringBuffer();
        for (int i = srcPos; i < data.length; i++) {
            if (i > srcPos) {
                sb.append(ConstantEnum.EMPTY);
            }
            String hex = Integer.toHexString(data[i] & 255);
            if (hex.length() == 1) {
                sb.append("0");
            }
            sb.append(hex);
        }
        return sb.toString();
    }

    public int getSourceAddress(byte[] data) {
        if (data.length < 12) {
            return 0;
        }
        return byte2Int(data[8], data[9]);
    }

    public String getSourceAddressHex(byte[] data) {
        if (data.length < 12) {
            return "0000";
        }
        return toHexString(data[8]) + toHexString(data[9]);
    }

    private int byte2Int(byte high, byte low) {
        return ((high & 255) << 8) + (low & 255);
    }

    public int getTargetAddress(byte[] data) {
        if (data.length < 12) {
            return 0;
        }
        return byte2Int(data[10], data[11]);
    }

    public String getTargetAddressHex(byte[] data) {
        if (data.length < 12) {
            return "0000";
        }
        return toHexString(data[10]) + toHexString(data[11]);
    }

    public String byteArrayToHexString(byte[] bytes) {
        return byteArrayToHexStringShort(bytes, bytes.length);
    }

    public String macToHexString(byte[] bytes) {
        return byteArrayToHexStringShort(bytes, bytes.length, '-');
    }

    public String byteArrayToHexStringShort(byte[] bytes, int count) {
        return byteArrayToHexStringShort(bytes, count, ' ');
    }

    private String byteArrayToHexStringShort(byte[] bytes, int count, char split) {
        if (bytes.length == 0 || count == 0) {
            return "";
        }
        if (bytes.length < count) {
            count = bytes.length;
        }
        char[] hexChars = new char[(count * 3) - 1];
        for (int j = 0; j < count; j++) {
            int v = bytes[j] & 255;
            hexChars[j * 3] = this.hexArray[v >>> 4];
            hexChars[(j * 3) + 1] = this.hexArray[v & 15];
            if (j < count - 1) {
                hexChars[(j * 3) + 2] = split;
            }
        }
        return new String(hexChars);
    }

    public String byteArrayToHexStringShortDotted(byte[] bytes, int count) {
        if (bytes.length <= count) {
            return byteArrayToHexString(bytes);
        }
        return byteArrayToHexStringShort(bytes, count) + "...";
    }

    public String bytesToHexString2(byte[] bytes, int count) {
        if (bytes.length == 0 || count == 0) {
            return "";
        }
        if (bytes.length < count) {
            count = bytes.length;
        }
        char[] hexChars = new char[count * 2];
        for (int j = 0; j < count; j++) {
            int v = bytes[j] & 255;
            hexChars[j * 2] = this.hexArray[v >>> 4];
            hexChars[(j * 2) + 1] = this.hexArray[v & 15];
        }
        return new String(hexChars);
    }

    public boolean isResponse4Doip(AbstractDoipMessage message, int targetAddress, DoipInstructionType type, byte subFunction) {
        byte[] data = message.getMessage();
        if ((message instanceof DoipTcpRoutingActivationResponse) && type == DoipInstructionType.RoutingActivation) {
            return true;
        }
        if (getSourceAddress(data) != targetAddress) {
            if (logger.isDebugEnabled()) {
                logger.debug("getSourceAddress(data)=0x" + int2HexString(getSourceAddress(data)) + ";targetAddress=0x" + int2HexString(targetAddress));
                return false;
            }
            return false;
        }
        if (message instanceof DoipTcpDiagnosticMessageNegAck) {
            if (data.length < 14) {
                if (logger.isDebugEnabled()) {
                    logger.debug("data.length=" + data.length);
                    return false;
                }
                return false;
            }
            if (logger.isDebugEnabled()) {
                logger.debug("data[12]=" + toHexString(data[12]) + ";type.getInstructionCode()=" + type.getInstructionCode() + ";toHexString(data[13])=" + toHexString(data[13]));
            }
            return data[12] == Byte.MAX_VALUE && type.getInstructionCode().equalsIgnoreCase(toHexString(data[13]));
        }
        if (data.length < 13) {
            if (logger.isDebugEnabled()) {
                logger.debug("data.length=" + data.length);
                return false;
            }
            return false;
        }
        if (data[12] == Byte.MAX_VALUE) {
            if (logger.isDebugEnabled()) {
                logger.debug("type.getInstructionCode=" + type.getInstructionCode() + ";toHexString(data[13])=" + toHexString(data[13]));
            }
            return type.getInstructionCode().equalsIgnoreCase(toHexString(data[13]));
        }
        if (!type.getPosAckCode().equalsIgnoreCase(toHexString(data[12]))) {
            if (logger.isDebugEnabled()) {
                logger.debug("type.getPosAckCode()=" + type.getPosAckCode() + ";data[12]=" + toHexString(data[12]));
                return false;
            }
            return false;
        }
        return true;
    }

    public boolean isResponse4DoipCheckSubFunction(AbstractDoipMessage message, int targetAddress, DoipInstructionType type, byte subFunction) {
        byte[] data = message.getMessage();
        if ((message instanceof DoipTcpRoutingActivationResponse) && type == DoipInstructionType.RoutingActivation) {
            return true;
        }
        if (getSourceAddress(data) != targetAddress) {
            if (logger.isDebugEnabled()) {
                logger.debug("getSourceAddress(data)=0x" + int2HexString(getSourceAddress(data)) + ";targetAddress=0x" + int2HexString(targetAddress));
                return false;
            }
            return false;
        }
        if (message instanceof DoipTcpDiagnosticMessageNegAck) {
            if (data.length < 14) {
                if (logger.isDebugEnabled()) {
                    logger.debug("data.length=" + data.length);
                    return false;
                }
                return false;
            }
            if (logger.isDebugEnabled()) {
                logger.debug("data[12]=" + toHexString(data[12]) + ";type.getInstructionCode()=" + type.getInstructionCode() + ";toHexString(data[13])=" + toHexString(data[13]));
            }
            return data[12] == Byte.MAX_VALUE && type.getInstructionCode().equalsIgnoreCase(toHexString(data[13]));
        }
        if (data.length < 13) {
            if (logger.isDebugEnabled()) {
                logger.debug("data.length=" + data.length);
                return false;
            }
            return false;
        }
        if (data[12] == Byte.MAX_VALUE) {
            if (logger.isDebugEnabled()) {
                logger.debug("type.getInstructionCode=" + type.getInstructionCode() + ";toHexString(data[13])=" + toHexString(data[13]));
            }
            return type.getInstructionCode().equalsIgnoreCase(toHexString(data[13]));
        }
        if (type.getPosAckCode().equalsIgnoreCase(toHexString(data[12]))) {
            return data.length < 14 ? subFunction == 0 : subFunction == data[13];
        }
        if (logger.isDebugEnabled()) {
            logger.debug("type.getPosAckCode()=" + type.getPosAckCode() + ";data[12]=" + toHexString(data[12]));
            return false;
        }
        return false;
    }

    public boolean isFunctionalAddressingResponse(AbstractDoipMessage message, DoipInstructionType type) {
        byte[] data = message.getMessage();
        if (data.length < 13) {
            if (logger.isDebugEnabled()) {
                logger.debug("data.length=" + data.length);
                return false;
            }
            return false;
        }
        if (!type.getPosAckCode().equalsIgnoreCase(toHexString(data[12]))) {
            if (logger.isDebugEnabled()) {
                logger.debug("type.getPosAckCode()=" + type.getPosAckCode() + ";data[12]=" + toHexString(data[12]));
                return false;
            }
            return false;
        }
        return true;
    }

    public boolean isMyResponseAdress(AbstractDoipMessage message, int targetAddress, DoipInstructionType type) {
        byte[] data = message.getMessage();
        if ((!(message instanceof DoipTcpRoutingActivationResponse) || type != DoipInstructionType.RoutingActivation) && getSourceAddress(data) != targetAddress) {
            if (logger.isDebugEnabled()) {
                logger.debug("getSourceAddress(data)=0x" + int2HexString(getSourceAddress(data)) + ";targetAddress=0x" + int2HexString(targetAddress));
                return false;
            }
            return false;
        }
        return true;
    }

    public boolean isMessageWithResponse(DoipInstructionType type, byte subFunction) {
        return ((type == DoipInstructionType.DiagnosticSessionControl && subFunction == -126) || (type == DoipInstructionType.DiagnosticSessionControl && subFunction == -125) || (type == DoipInstructionType.ECUReset && subFunction == -127)) ? false : true;
    }

    public byte[] int2Byte2LowLast(int val) {
        byte[] b = {(byte) ((val >> 8) & 255), (byte) (val & 255)};
        return b;
    }

    public byte[] int2Byte4LowLast(int val) {
        byte[] b = {(byte) ((val >> 24) & 255), (byte) ((val >> 16) & 255), (byte) ((val >> 8) & 255), (byte) (val & 255)};
        return b;
    }

    public long getCheckSumCrc32(byte[] data) {
        Checksum crc32 = new CRC32();
        crc32.update(data, 0, data.length);
        return crc32.getValue();
    }

    public Checksum createCheckSumCrc32() {
        return new CRC32();
    }

    public void addChecksum(Checksum crc32, byte[] data) {
        crc32.update(data, 0, data.length);
    }

    public byte[] long2Bytes(long values) {
        byte[] buffer = new byte[8];
        for (int i = 0; i < 8; i++) {
            int offset = 64 - ((i + 1) * 8);
            buffer[i] = (byte) ((values >> offset) & 255);
        }
        return buffer;
    }

    public DoipAddress getDoipAddress(InetAddress address, int port, byte[] defaultMac) throws SocketException {
        byte[] mac = null;
        try {
            NetworkInterface ni = NetworkInterface.getByInetAddress(address);
            if (ni != null && ni.getHardwareAddress() == null) {
                mac = ni.getHardwareAddress();
            }
        } catch (SocketException e) {
            logger.error("SocketException:" + e.getMessage());
        }
        if (mac == null) {
            mac = defaultMac;
        }
        return new DoipAddress(address, port, address.getHostAddress(), mac);
    }

    public byte[] getShortData(byte[] data) {
        if (data.length > 30) {
            return Arrays.copyOf(data, 30);
        }
        return data;
    }

    public static String getDsaLogCommand(String commandAfter) {
        StringBuilder sb = new StringBuilder();
        int length = commandAfter.length();
        for (int i = 0; i < length / 2; i++) {
            String substring = commandAfter.substring(i * 2, (i * 2) + 2);
            sb.append(substring);
            if (i < (length / 2) - 1) {
                sb.append(ConstantEnum.EMPTY);
            }
        }
        if (length % 2 == 1) {
            sb.append(ConstantEnum.EMPTY).append(commandAfter.substring(length - 1));
        }
        return sb.toString();
    }

    public static String getDsaLogCommandHex(String commandAfter) {
        StringBuilder sb = new StringBuilder();
        int length = commandAfter.length();
        for (int i = 0; i < length / 2; i++) {
            String substring = commandAfter.substring(i * 2, (i * 2) + 2);
            sb.append("0x").append(substring);
            if (i < (length / 2) - 1) {
                sb.append(ConstantEnum.EMPTY);
            }
        }
        return sb.toString();
    }
}
