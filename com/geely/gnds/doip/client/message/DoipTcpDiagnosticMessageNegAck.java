package com.geely.gnds.doip.client.message;

import com.geely.gnds.doip.client.DoipUtil;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpDiagnosticMessageNegAck.class */
public class DoipTcpDiagnosticMessageNegAck extends AbstractDoipTcpDiagnosticMessageAck {
    public static final int aA = 2;
    public static final int aB = 3;
    public static final int aC = 4;
    public static final int aD = 5;
    public static final int aE = 6;
    public static final int aF = 7;
    public static final int aG = 8;
    private final DoipUtil util;
    private static final Logger logger = LoggerFactory.getLogger(DoipTcpDiagnosticMessageNegAck.class);

    private DoipTcpDiagnosticMessageNegAck() {
        this.util = DoipUtil.getInstance();
    }

    public DoipTcpDiagnosticMessageNegAck(int sourceAddress, int targetAddress, int ackCode, byte[] message) {
        super(sourceAddress, targetAddress, ackCode, message);
        this.util = DoipUtil.getInstance();
        a(Level.INFO);
    }

    public String getNackCodeAsString(int code) {
        switch (code) {
            case 2:
                return "0x02 (invalid source address)";
            case 3:
                return "0x03 (unknown target address)";
            case 4:
                return "0x04 (diagnostic message too large)";
            case 5:
                return "0x05 (out of memory)";
            case 6:
                return "0x06 (target unreachable)";
            case 7:
                return "0x07 (unknown network)";
            case 8:
                return "0x08 (transport protocol error)";
            default:
                return String.format("%02X (???)", Integer.valueOf(code));
        }
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP diagnostic message negative acknowledgement:");
        a(logger, level, "    Source address = " + getSourceAddress());
        a(logger, level, "    Target address = " + getTargetAddress());
        a(logger, level, "    NACK code      = " + getNackCodeAsString(getAckCode()));
        a(logger, level, "    Message        = " + this.util.byteArrayToHexStringShortDotted(getDiagnosticMessage(), 64));
        a(logger, level, "----------------------------------------");
    }

    public static DoipTcpDiagnosticMessageNegAck d(byte[] payload) {
        DoipTcpDiagnosticMessageNegAck doipTcpDiagnosticMessageNegAck = new DoipTcpDiagnosticMessageNegAck();
        doipTcpDiagnosticMessageNegAck.a(payload);
        doipTcpDiagnosticMessageNegAck.a(Level.INFO);
        return doipTcpDiagnosticMessageNegAck;
    }
}
