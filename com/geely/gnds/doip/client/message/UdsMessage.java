package com.geely.gnds.doip.client.message;

/* loaded from: UdsMessage.class */
public class UdsMessage {
    public static final int aN = 0;
    public static final int aO = 1;
    private int aP;
    private int targetAddress;
    private int aQ;
    private byte[] aR;

    private UdsMessage() {
        this.aP = 0;
        this.targetAddress = 0;
        this.aQ = 0;
    }

    public UdsMessage(int sourceAddress, int targetAddress, int targetAddressType, byte[] message) {
        this.aP = 0;
        this.targetAddress = 0;
        this.aQ = 0;
        this.aP = sourceAddress;
        this.targetAddress = targetAddress;
        this.aQ = targetAddressType;
        this.aR = message;
    }

    public UdsMessage(int sourceAddress, int targetAddress, byte[] message) {
        this.aP = 0;
        this.targetAddress = 0;
        this.aQ = 0;
        this.aP = sourceAddress;
        this.targetAddress = targetAddress;
        this.aQ = 0;
        this.aR = message;
    }

    public int getSourceAdrress() {
        return this.aP;
    }

    public void setSourceAdrress(int sourceAdrress) {
        this.aP = sourceAdrress;
    }

    public int getTargetAddress() {
        return this.targetAddress;
    }

    public void setTargetAddress(int targetAddress) {
        this.targetAddress = targetAddress;
    }

    public int getTargetAddressType() {
        return this.aQ;
    }

    public void setTargetAddressType(int targetAddressType) {
        this.aQ = targetAddressType;
    }

    public byte[] getMessage() {
        return this.aR;
    }

    public void setMessage(byte[] message) {
        this.aR = message;
    }
}
