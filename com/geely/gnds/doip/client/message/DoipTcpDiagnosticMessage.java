package com.geely.gnds.doip.client.message;

import com.geely.gnds.doip.client.DoipUtil;
import java.util.Arrays;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpDiagnosticMessage.class */
public class DoipTcpDiagnosticMessage extends AbstractDoipTcpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipTcpDiagnosticMessage.class);
    private final DoipUtil util;
    private int sourceAddress;
    private int targetAddress;
    private byte[] az;

    private DoipTcpDiagnosticMessage() {
        this.util = DoipUtil.getInstance();
        this.sourceAddress = 0;
        this.targetAddress = 0;
        this.az = new byte[0];
    }

    public DoipTcpDiagnosticMessage(int sourceAddress, int targetAddress, byte[] message) {
        this.util = DoipUtil.getInstance();
        this.sourceAddress = 0;
        this.targetAddress = 0;
        this.az = new byte[0];
        this.sourceAddress = sourceAddress;
        this.targetAddress = targetAddress;
        this.az = message;
        if (logger.isInfoEnabled()) {
            a(Level.INFO);
        }
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipTcpMessage
    public void a(byte[] payload) {
        int high = payload[0] & 255;
        int low = payload[1] & 255;
        this.sourceAddress = (high << 8) | low;
        int high2 = payload[2] & 255;
        int low2 = payload[3] & 255;
        this.targetAddress = (high2 << 8) | low2;
        this.az = Arrays.copyOfRange(payload, 4, payload.length);
    }

    public static DoipTcpDiagnosticMessage c(byte[] payload) {
        DoipTcpDiagnosticMessage doipTcpDiagnosticMessage = new DoipTcpDiagnosticMessage();
        doipTcpDiagnosticMessage.a(payload);
        doipTcpDiagnosticMessage.a(Level.INFO);
        return doipTcpDiagnosticMessage;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] data = new byte[12 + this.az.length];
        data[0] = 2;
        data[1] = -3;
        data[2] = Byte.MIN_VALUE;
        data[3] = 1;
        data[4] = (byte) ((this.az.length + 4) >> 24);
        data[5] = (byte) ((this.az.length + 4) >> 16);
        data[6] = (byte) ((this.az.length + 4) >> 8);
        data[7] = (byte) (this.az.length + 4);
        data[8] = (byte) (this.sourceAddress >> 8);
        data[9] = (byte) this.sourceAddress;
        data[10] = (byte) (this.targetAddress >> 8);
        data[11] = (byte) this.targetAddress;
        System.arraycopy(this.az, 0, data, 12, this.az.length);
        return data;
    }

    public int getSourceAddress() {
        return this.sourceAddress;
    }

    public void setSourceAddress(int sourceAddress) {
        this.sourceAddress = sourceAddress;
    }

    public int getTargetAddress() {
        return this.targetAddress;
    }

    public void setTargetAddress(int targetAddress) {
        this.targetAddress = targetAddress;
    }

    public byte[] getDiagnosticMessage() {
        return this.az;
    }

    public void setDiagnosticMessage(byte[] diagnosticMessage) {
        this.az = diagnosticMessage;
    }
}
