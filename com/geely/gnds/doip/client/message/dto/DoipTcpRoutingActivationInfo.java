package com.geely.gnds.doip.client.message.dto;

/* loaded from: DoipTcpRoutingActivationInfo.class */
public class DoipTcpRoutingActivationInfo {
    private int responseCode = 32;
    private String responseString = "没有接收到激活响应消息！";

    public int getResponseCode() {
        return this.responseCode;
    }

    public void setResponseCode(int responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseString() {
        return this.responseString;
    }

    public void setResponseString(String responseString) {
        this.responseString = responseString;
    }
}
