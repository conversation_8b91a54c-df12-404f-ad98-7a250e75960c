package com.geely.gnds.doip.client.message;

import org.apache.logging.log4j.Level;
import org.slf4j.Logger;

/* loaded from: AbstractDoipMessage.class */
public abstract class AbstractDoipMessage {
    public static final int ai = 0;
    public static final int aj = 1;
    public static final int ak = 2;
    public static final int al = 3;
    public static final int am = 4;
    public static final int an = 5;
    public static final int ao = 6;
    public static final int ap = 7;
    public static final int aq = 8;
    public static final int ar = 16385;
    public static final int as = 16386;
    public static final int at = 16387;
    public static final int au = 16388;
    public static final int av = 32769;
    public static final int aw = 32770;
    public static final int ax = 32771;

    public abstract byte[] getMessage();

    public abstract void a(Level level);

    protected AbstractDoipMessage() {
    }

    protected void a(Logger logger, Level level, String content) {
        if (level == Level.INFO) {
            logger.info(content);
        } else {
            logger.debug(content);
        }
    }

    public static String getPayloadTypeAsString(int type) {
        switch (type) {
            case 0:
                return "Generic DoIP Header Negative Acknowledge";
            case 1:
                return "Vehicle Identification Request Message";
            case 2:
                return "Vehicle Identification Request Message with EID";
            case 3:
                return "Vehicle Identification Request Message with VIN";
            case 4:
                return "Vehicle Announcement Message";
            case 5:
                return "Routing Activation Request";
            case 6:
                return "Routing Activation Response";
            case 7:
                return "Alive Check Request";
            case 8:
                return "Alive Check Response";
            case ar /* 16385 */:
                return "DoIP Entity Status Request";
            case as /* 16386 */:
                return "DoIP Entity Status Response";
            case at /* 16387 */:
                return "Diagnostic Power Mode Information Request";
            case au /* 16388 */:
                return "Diagnostic Power Mode Information Response";
            case av /* 32769 */:
                return "Diagnostic Message";
            case aw /* 32770 */:
                return "Diagnostic Message Positive Acknowledgement";
            case ax /* 32771 */:
                return "Diagnostic Message Negative Acknowledgement";
            default:
                return null;
        }
    }
}
