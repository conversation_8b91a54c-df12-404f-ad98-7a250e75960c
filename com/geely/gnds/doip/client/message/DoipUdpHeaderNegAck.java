package com.geely.gnds.doip.client.message;

import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpHeaderNegAck.class */
public class DoipUdpHeaderNegAck extends AbstractDoipUdpMessage implements DoipHeaderNegAck {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpHeaderNegAck.class);
    private int code;

    private DoipUdpHeaderNegAck() {
        this.code = -1;
    }

    public DoipUdpHeaderNegAck(int code) {
        this.code = -1;
        this.code = code;
        a(Level.INFO);
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP header negative acknowledgement (UDP):");
        a(logger, level, "    Code = " + this.code);
        a(logger, level, "");
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] message = {2, -3, 0, 0, 0, 0, 0, 1, 0};
        message[8] = (byte) (this.code & 255);
        return message;
    }
}
