package com.geely.gnds.doip.client.message;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.tester.enums.ConstantEnum;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpVehicleAnnouncementMessage.class */
public class DoipUdpVehicleAnnouncementMessage extends AbstractDoipUdpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpVehicleAnnouncementMessage.class);
    private final DoipUtil util = DoipUtil.getInstance();
    private byte[] aH;
    private boolean needTls;
    private int aI;
    private byte[] aJ;
    private byte[] aK;
    private int aL;
    private int aM;

    public DoipUdpVehicleAnnouncementMessage(byte[] vin, int logicalAddress, byte[] eid, byte[] gid, int furtherActionRequired, int syncStatus, byte datum) {
        this.aH = null;
        this.needTls = false;
        this.aI = 0;
        this.aJ = new byte[6];
        this.aK = new byte[6];
        this.aL = 0;
        this.aM = 0;
        byte[] vin0 = {48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48};
        if (vin.length != ConstantEnum.SEVENTEEN.intValue()) {
            this.aH = vin0;
        } else {
            this.aH = vin;
            boolean flag = false;
            int i = 0;
            while (true) {
                if (i >= ConstantEnum.SEVENTEEN.intValue()) {
                    break;
                }
                if (this.aH[i] == 0) {
                    this.aH[i] = 48;
                }
                if (a(this.aH[i])) {
                    i++;
                } else {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                this.aH = vin0;
            }
        }
        this.aI = logicalAddress;
        this.aJ = eid;
        this.aK = gid;
        this.aL = furtherActionRequired;
        this.aM = syncStatus;
        if ((datum & 1) == 1) {
            this.needTls = true;
        }
        a(Level.INFO);
    }

    private static boolean a(byte b) {
        if (b >= 48 && b <= 57) {
            return true;
        }
        if (b >= 65 && b <= 90) {
            return true;
        }
        return false;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] msg = new byte[41];
        msg[0] = 2;
        msg[1] = -3;
        msg[2] = 0;
        msg[3] = 4;
        msg[4] = 0;
        msg[5] = 0;
        msg[6] = 0;
        msg[7] = 33;
        System.arraycopy(this.aH, 0, msg, 8, 17);
        msg[25] = (byte) (this.aI >> 8);
        msg[26] = (byte) this.aI;
        System.arraycopy(this.aJ, 0, msg, 27, 6);
        System.arraycopy(this.aK, 0, msg, 33, 6);
        msg[40] = (byte) this.aM;
        return msg;
    }

    public byte[] getVin() {
        return this.aH;
    }

    public boolean isNeedTls() {
        return this.needTls;
    }

    public void setVin(byte[] vin) {
        this.aH = vin;
    }

    public int getLogicalAddress() {
        return this.aI;
    }

    public void setLogicalAddress(int logicalAddress) {
        this.aI = logicalAddress;
    }

    public byte[] getEid() {
        return this.aJ;
    }

    public void setEid(byte[] eid) {
        this.aJ = eid;
    }

    public byte[] getGid() {
        return this.aK;
    }

    public void setGid(byte[] gid) {
        this.aK = gid;
    }

    public int getFurtherActionRequired() {
        return this.aL;
    }

    public void setFurtherActionRequired(int furtherActionRequired) {
        this.aL = furtherActionRequired;
    }

    public int getSyncStatus() {
        return this.aM;
    }

    public void setSyncStatus(int syncStatus) {
        this.aM = syncStatus;
    }
}
