package com.geely.gnds.doip.client.message.request;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.message.AbstractDoipUdpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpVehicleIdentRequestWithVin.class */
public class DoipUdpVehicleIdentRequestWithVin extends AbstractDoipUdpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpVehicleIdentRequestWithVin.class);
    private final DoipUtil util = DoipUtil.getInstance();
    private byte[] aH;

    public DoipUdpVehicleIdentRequestWithVin(byte[] vin) {
        this.aH = null;
        this.aH = vin;
        a(Level.INFO);
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP vehicle ident. request with VIN:");
        a(logger, level, "    VIN = " + this.util.byteArrayToHexString(this.aH));
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] msg = new byte[25];
        msg[0] = -1;
        msg[1] = 0;
        msg[2] = 0;
        msg[3] = 3;
        msg[4] = 0;
        msg[5] = 0;
        msg[6] = 0;
        msg[7] = 17;
        System.arraycopy(this.aH, 0, msg, 8, 17);
        return msg;
    }

    public byte[] getVin() {
        return this.aH;
    }

    public void setVin(byte[] vin) {
        this.aH = vin;
    }
}
