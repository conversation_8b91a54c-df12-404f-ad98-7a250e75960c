package com.geely.gnds.doip.client.message.request;

import com.geely.gnds.doip.client.message.AbstractDoipTcpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpAliveCheckRequest.class */
public class DoipTcpAliveCheckRequest extends AbstractDoipTcpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipTcpAliveCheckRequest.class);

    public DoipTcpAliveCheckRequest() {
        if (logger.isInfoEnabled()) {
            a(Level.INFO);
        }
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP alive check request");
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipTcpMessage
    public void a(byte[] payload) {
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] message = {2, -3, 0, 7, 0, 0, 0, 0};
        return message;
    }
}
