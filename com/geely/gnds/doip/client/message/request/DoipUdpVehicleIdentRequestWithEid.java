package com.geely.gnds.doip.client.message.request;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.message.AbstractDoipUdpMessage;
import java.util.Arrays;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpVehicleIdentRequestWithEid.class */
public class DoipUdpVehicleIdentRequestWithEid extends AbstractDoipUdpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpVehicleIdentRequestWithEid.class);
    private final DoipUtil util = DoipUtil.getInstance();
    private byte[] aJ;

    public DoipUdpVehicleIdentRequestWithEid(byte[] eid) {
        this.aJ = Arrays.copyOf(eid, 6);
        a(Level.INFO);
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP vehicle ident. request with EID");
        a(logger, level, "    EID = " + this.util.byteArrayToHexString(this.aJ));
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] msg = {-1, 0, 0, 2, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0};
        System.arraycopy(this.aJ, 0, msg, 8, 6);
        return msg;
    }

    public byte[] getEid() {
        return this.aJ;
    }

    public void setEid(byte[] eid) {
        this.aJ = eid;
    }
}
