package com.geely.gnds.doip.client.message.request;

import com.geely.gnds.doip.client.message.AbstractDoipUdpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpEntityStatusRequest.class */
public class DoipUdpEntityStatusRequest extends AbstractDoipUdpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpEntityStatusRequest.class);

    public DoipUdpEntityStatusRequest() {
        a(Level.INFO);
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP entity status request.");
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] msg = {2, -3, 64, 1, 0, 0, 0, 0};
        return msg;
    }
}
