package com.geely.gnds.doip.client.message.request;

import com.geely.gnds.doip.client.message.AbstractDoipTcpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpRoutingActivationRequest.class */
public class DoipTcpRoutingActivationRequest extends AbstractDoipTcpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipTcpRoutingActivationRequest.class);
    int sourceAddress;
    int aS;
    long aT;

    public DoipTcpRoutingActivationRequest(int sourceAddress, int activationType, long oemData) {
        this.sourceAddress = 0;
        this.aS = 0;
        this.aT = -1L;
        this.sourceAddress = sourceAddress;
        this.aS = activationType;
        this.aT = oemData;
        a(Level.INFO);
    }

    private DoipTcpRoutingActivationRequest() {
        this.sourceAddress = 0;
        this.aS = 0;
        this.aT = -1L;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP routing activation request:");
        a(logger, level, "    Source address  = " + this.sourceAddress);
        a(logger, level, "    Activation Type = " + this.aS);
        a(logger, level, "    OEM data        = " + getOemData());
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipTcpMessage
    public void a(byte[] payload) {
        int high = payload[0] & 255;
        int low = payload[1] & 255;
        this.sourceAddress = (high << 8) | low;
        this.aS = payload[2] & 255;
        if (payload.length == 11) {
            long highhigh = payload[3] & 255;
            long highlow = payload[4] & 255;
            long lowhigh = payload[5] & 255;
            long lowlow = payload[6] & 255;
            this.aT = (highhigh << 24) | (highlow << 16) | (lowhigh << 8) | lowlow;
        }
    }

    public static DoipTcpRoutingActivationRequest g(byte[] payload) {
        DoipTcpRoutingActivationRequest doipMessage = new DoipTcpRoutingActivationRequest();
        doipMessage.a(payload);
        doipMessage.a(Level.INFO);
        return doipMessage;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] message;
        if (this.aT == -1) {
            message = new byte[15];
            message[7] = 7;
        } else {
            message = new byte[19];
            message[7] = 11;
            message[15] = (byte) (this.aT >> 24);
            message[16] = (byte) (this.aT >> 16);
            message[17] = (byte) (this.aT >> 8);
            message[18] = (byte) this.aT;
        }
        message[0] = 2;
        message[1] = -3;
        message[2] = 0;
        message[3] = 5;
        message[4] = 0;
        message[5] = 0;
        message[6] = 0;
        message[8] = (byte) (this.sourceAddress >> 8);
        message[9] = (byte) this.sourceAddress;
        message[10] = (byte) this.aS;
        message[11] = 0;
        message[12] = 0;
        message[13] = 0;
        message[14] = 0;
        return message;
    }

    public int getSourceAddress() {
        return this.sourceAddress;
    }

    public void setSourceAddress(int sourceAddress) {
        this.sourceAddress = sourceAddress;
    }

    public int getActivationType() {
        return this.aS;
    }

    public void setActivationType(int activationType) {
        this.aS = activationType;
    }

    public long getOemData() {
        return this.aT;
    }

    public void setOemData(long oemData) {
        this.aT = oemData;
    }
}
