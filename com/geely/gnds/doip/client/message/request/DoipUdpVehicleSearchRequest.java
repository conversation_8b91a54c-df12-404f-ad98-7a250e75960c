package com.geely.gnds.doip.client.message.request;

import com.geely.gnds.doip.client.message.AbstractDoipUdpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpVehicleSearchRequest.class */
public class DoipUdpVehicleSearchRequest extends AbstractDoipUdpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpVehicleSearchRequest.class);

    public DoipUdpVehicleSearchRequest() {
        a(Level.INFO);
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP vehicle identification request.");
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] message = {2, -3, 0, 1, 0, 0, 0, 0};
        return message;
    }
}
