package com.geely.gnds.doip.client.message.request;

import com.geely.gnds.doip.client.message.AbstractDoipUdpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpDiagnosticPowerModeRequest.class */
public class DoipUdpDiagnosticPowerModeRequest extends AbstractDoipUdpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpDiagnosticPowerModeRequest.class);

    public DoipUdpDiagnosticPowerModeRequest() {
        a(Level.INFO);
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP diagnostic power mode request.");
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] message = {2, -3, 64, 3, 0, 0, 0, 0};
        return message;
    }
}
