package com.geely.gnds.doip.client.message;

import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpHeaderNegAck.class */
public class DoipTcpHeaderNegAck extends AbstractDoipTcpMessage implements DoipHeaderNegAck {
    private static final Logger logger = LoggerFactory.getLogger(DoipTcpHeaderNegAck.class);
    private int code;

    private DoipTcpHeaderNegAck() {
        this.code = -1;
    }

    public DoipTcpHeaderNegAck(int code) {
        this.code = -1;
        this.code = code;
        a(Level.INFO);
    }

    public static DoipTcpHeaderNegAck f(byte[] payload) {
        DoipTcpHeaderNegAck doipHeaderNegAck = new DoipTcpHeaderNegAck();
        doipHeaderNegAck.a(payload);
        doipHeaderNegAck.a(Level.INFO);
        return doipHeaderNegAck;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
    }

    public int getCode() {
        return this.code;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] message = {2, -3, 0, 0, 0, 0, 0, 1, 0};
        message[8] = (byte) (this.code & 255);
        return message;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipTcpMessage
    public void a(byte[] payload) {
        this.code = payload[0] & 255;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
