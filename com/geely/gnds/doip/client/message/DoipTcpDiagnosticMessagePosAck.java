package com.geely.gnds.doip.client.message;

import com.geely.gnds.doip.client.DoipUtil;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpDiagnosticMessagePosAck.class */
public class DoipTcpDiagnosticMessagePosAck extends AbstractDoipTcpDiagnosticMessageAck {
    private static final Logger LOGGER = LoggerFactory.getLogger(DoipTcpDiagnosticMessagePosAck.class);
    private final DoipUtil util;

    private DoipTcpDiagnosticMessagePosAck() {
        this.util = DoipUtil.getInstance();
    }

    public DoipTcpDiagnosticMessagePosAck(int sourceAddress, int targetAddress, int ackCode, byte[] message) {
        super(sourceAddress, targetAddress, ackCode, message);
        this.util = DoipUtil.getInstance();
        a(Level.INFO);
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
    }

    public static DoipTcpDiagnosticMessagePosAck e(byte[] payload) {
        DoipTcpDiagnosticMessagePosAck doipTcpDiagnosticMessagePosAck = new DoipTcpDiagnosticMessagePosAck();
        doipTcpDiagnosticMessagePosAck.a(payload);
        doipTcpDiagnosticMessagePosAck.a(Level.INFO);
        return doipTcpDiagnosticMessagePosAck;
    }
}
