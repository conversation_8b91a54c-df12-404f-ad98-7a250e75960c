package com.geely.gnds.doip.client.message;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.exception.HeaderTooShort;
import com.geely.gnds.doip.client.exception.IncorrectPatternFormat;
import com.geely.gnds.doip.client.exception.InvalidPayloadLength;
import com.geely.gnds.doip.client.exception.InvalidPayloadType;
import com.geely.gnds.doip.client.message.request.DoipUdpDiagnosticPowerModeRequest;
import com.geely.gnds.doip.client.message.request.DoipUdpEntityStatusRequest;
import com.geely.gnds.doip.client.message.request.DoipUdpVehicleIdentRequest;
import com.geely.gnds.doip.client.message.request.DoipUdpVehicleIdentRequestWithEid;
import com.geely.gnds.doip.client.message.request.DoipUdpVehicleIdentRequestWithVin;
import com.geely.gnds.doip.client.message.response.DoipUdpDiagnosticPowerModeResponse;
import com.geely.gnds.doip.client.message.response.DoipUdpEntityStatusResponse;
import com.geely.gnds.tester.enums.ConstantEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: AbstractDoipUdpMessage.class */
public abstract class AbstractDoipUdpMessage extends AbstractDoipMessage {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractDoipUdpMessage.class);

    public static AbstractDoipUdpMessage b(byte[] data) throws IncorrectPatternFormat, InvalidPayloadType, InvalidPayloadLength, HeaderTooShort {
        if (LOGGER.isTraceEnabled()) {
            LOGGER.trace(">>> DoipMessage parseUDP(byte[] data)");
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Parse UDP message ...");
            LOGGER.debug("\tMessage Length           = " + data.length);
        }
        if (data.length < ConstantEnum.EIGHT.intValue()) {
            LOGGER.trace("<<< DoipMessage parseUDP(byte[] data) return with HeaderTooShort");
            throw new HeaderTooShort("DoIP UDP message too short for interpretation");
        }
        int protocolVersion = data[0] & 255;
        int inverseProtocolVersion = data[1] & 255;
        int xorProtocolVersion = protocolVersion ^ 255;
        if (xorProtocolVersion != inverseProtocolVersion) {
            LOGGER.trace("<<< DoipMessage parseUDP(byte[] data) return with IncorrectPatternFormat");
            throw new IncorrectPatternFormat();
        }
        int high = data[2] & 255;
        int low = data[3] & 255;
        int payloadType = (high << 8) | low;
        String text = getPayloadTypeAsString(payloadType);
        if (text == null) {
            text = "???";
        }
        LOGGER.debug("\tPayload Type             = " + String.format("0x%04X", Integer.valueOf(payloadType)) + ": " + text);
        long highhigh = data[4] & 255;
        long highlow = data[5] & 255;
        long lowhigh = data[6] & 255;
        long lowlow = data[7] & 255;
        long payloadLength = (highhigh << 24) | (highlow << 16) | (lowhigh << 8) | lowlow;
        LOGGER.debug("\tPayload Length in Header = " + payloadLength);
        return a(data, payloadType, payloadLength);
    }

    private static AbstractDoipUdpMessage a(byte[] data, int payloadType, long payloadLength) throws InvalidPayloadType, InvalidPayloadLength {
        switch (payloadType) {
            case 0:
                a(1L, payloadLength, data.length);
                LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)");
                return new DoipUdpHeaderNegAck(data[8] & 255);
            case 1:
                a(0L, payloadLength, data.length);
                LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)");
                return new DoipUdpVehicleIdentRequest();
            case 2:
                a(6L, payloadLength, data.length);
                byte[] eid = new byte[6];
                System.arraycopy(data, 8, eid, 0, 6);
                LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)");
                return new DoipUdpVehicleIdentRequestWithEid(eid);
            case 3:
                a(17L, payloadLength, data.length);
                byte[] vin = new byte[17];
                System.arraycopy(data, 8, vin, 0, 17);
                LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)");
                return new DoipUdpVehicleIdentRequestWithVin(vin);
            case 4:
                return b(data, payloadLength);
            case AbstractDoipMessage.ar /* 16385 */:
                a(0L, payloadLength, data.length);
                LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)");
                return new DoipUdpEntityStatusRequest();
            case AbstractDoipMessage.as /* 16386 */:
                return a(data, payloadLength);
            case AbstractDoipMessage.at /* 16387 */:
                a(0L, payloadLength, data.length);
                LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)");
                return new DoipUdpDiagnosticPowerModeRequest();
            case AbstractDoipMessage.au /* 16388 */:
                a(1L, payloadLength, data.length);
                byte diagPowerMode = data[8];
                LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)");
                return new DoipUdpDiagnosticPowerModeResponse(diagPowerMode);
            default:
                LOGGER.trace("<<< DoipMessage parseUDP(byte[] data) return with InvalidPayloadType");
                throw new InvalidPayloadType();
        }
    }

    private static AbstractDoipUdpMessage a(byte[] data, long payloadLength) throws InvalidPayloadLength {
        a(7L, payloadLength, data.length);
        byte nodeType = data[8];
        int maxNumberOfSockets = data[9] & 255;
        int currentNumberOfSockets = data[10] & 255;
        int maxDataSize = (data[11] << 24) | (data[12] << 16) | (data[13] << 8) | data[14];
        LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)");
        return new DoipUdpEntityStatusResponse(nodeType, maxNumberOfSockets, currentNumberOfSockets, maxDataSize);
    }

    private static AbstractDoipUdpMessage b(byte[] data, long payloadLength) throws InvalidPayloadLength {
        boolean isGeely = data.length == 40;
        if (isGeely && payloadLength == 33) {
            LOGGER.error("标记数据长度：" + payloadLength + "；实际数据长度：32。进行容错处理！");
            payloadLength = 32;
        }
        a(isGeely ? 32L : 33L, payloadLength, data.length);
        byte[] vin = new byte[17];
        System.arraycopy(data, 8, vin, 0, 17);
        int high = data[25] & 255;
        int low = data[26] & 255;
        int logicalAddress = isGeely ? 0 : (high << 8) + low;
        byte[] eid = new byte[6];
        System.arraycopy(data, 27, eid, 0, 6);
        byte[] gid = new byte[6];
        System.arraycopy(data, 33, gid, 0, 6);
        byte datum = data[39];
        int furtherActionRequired = datum & 255;
        byte syncStatus = isGeely ? (byte) 0 : data[40];
        String s = DoipUtil.getInstance().toHexString(datum);
        LOGGER.trace("<<< DoipMessage parseUDP(byte[] data)" + s);
        return new DoipUdpVehicleAnnouncementMessage(vin, logicalAddress, eid, gid, furtherActionRequired, syncStatus, datum);
    }

    private static void a(long expectedLength, long payloadLength, long dataLength) throws InvalidPayloadLength {
        LOGGER.trace(">>> void checkPayloadLength(long expectedLength, long payloadLength, long dataLength)");
        LOGGER.debug("\tExpected Payload Length  = " + expectedLength + ", Actual Payload Length  = " + payloadLength + ", Data length(payloadLength+8)=" + dataLength);
        if (payloadLength != expectedLength || payloadLength != dataLength - ConstantEnum.EIGHT.intValue()) {
            LOGGER.warn("Invalid payload length.");
            throw new InvalidPayloadLength();
        }
        LOGGER.trace("<<< void checkPayloadLength(long expectedLength, long payloadLength, long dataLength)");
    }
}
