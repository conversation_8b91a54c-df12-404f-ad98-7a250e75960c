package com.geely.gnds.doip.client.message.response;

import com.geely.gnds.doip.client.message.AbstractDoipTcpMessage;
import com.geely.gnds.dsa.enums.LanguageEnum;
import com.sun.jna.Platform;
import com.sun.jna.win32.DLLCallback;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpRoutingActivationResponse.class */
public class DoipTcpRoutingActivationResponse extends AbstractDoipTcpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipTcpRoutingActivationResponse.class);
    int aU;
    int aV;
    int responseCode;
    long aT;

    private DoipTcpRoutingActivationResponse() {
        this.aU = 0;
        this.aV = 0;
        this.responseCode = 0;
        this.aT = -1L;
    }

    public DoipTcpRoutingActivationResponse(int testerAddress, int entityAddress, int responseCode, long oemData) {
        this.aU = 0;
        this.aV = 0;
        this.responseCode = 0;
        this.aT = -1L;
        this.aU = testerAddress;
        this.aV = entityAddress;
        this.responseCode = responseCode;
        this.aT = oemData;
        a(Level.INFO);
    }

    public String getResponseCodeAsString(int code) {
        switch (code) {
            case 0:
                return LanguageEnum.ACTIVATION_00.valueByLanguage();
            case 1:
                return LanguageEnum.ACTIVATION_01.valueByLanguage();
            case 2:
                return LanguageEnum.ACTIVATION_02.valueByLanguage();
            case 3:
                return LanguageEnum.ACTIVATION_03.valueByLanguage();
            case 4:
                return LanguageEnum.ACTIVATION_04.valueByLanguage();
            case 5:
                return LanguageEnum.ACTIVATION_05.valueByLanguage();
            case 6:
                return LanguageEnum.ACTIVATION_06.valueByLanguage();
            case 7:
            case 8:
            case Platform.GNU /* 9 */:
            case Platform.KFREEBSD /* 10 */:
            case Platform.NETBSD /* 11 */:
            case 12:
            case 13:
            case 14:
            case 15:
            default:
                return String.format("0x02X (???)", Integer.valueOf(code));
            case DLLCallback.DLL_FPTRS /* 16 */:
                return LanguageEnum.ACTIVATION_10.valueByLanguage();
            case 17:
                return LanguageEnum.ACTIVATION_11.valueByLanguage();
        }
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP routing activation response:");
        a(logger, level, "    Tester address = " + this.aU);
        a(logger, level, "    Entity address = " + this.aV);
        a(logger, level, "    Response code  = " + getResponseCodeAsString(this.responseCode));
        if (this.aT == -1) {
            a(logger, level, "    OEM data       = n/a");
        } else {
            a(logger, level, "    OEM data       = " + this.aT);
        }
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipTcpMessage
    public void a(byte[] payload) {
        int high = payload[0] & 255;
        int low = payload[1] & 255;
        this.aU = (high << 8) | low;
        int high2 = payload[2] & 255;
        int low2 = payload[3] & 255;
        this.aV = (high2 << 8) | low2;
        this.responseCode = payload[4] & 255;
    }

    public static DoipTcpRoutingActivationResponse i(byte[] payload) {
        DoipTcpRoutingActivationResponse doipTcpRoutingActivationResponse = new DoipTcpRoutingActivationResponse();
        doipTcpRoutingActivationResponse.a(payload);
        doipTcpRoutingActivationResponse.a(Level.INFO);
        return doipTcpRoutingActivationResponse;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] msg;
        if (this.aT == -1) {
            msg = new byte[17];
            msg[7] = 9;
        } else {
            msg = new byte[21];
            msg[7] = 13;
            msg[17] = (byte) (this.aT >> 24);
            msg[18] = (byte) (this.aT >> 16);
            msg[19] = (byte) (this.aT >> 8);
            msg[20] = (byte) this.aT;
        }
        msg[0] = 2;
        msg[1] = -3;
        msg[2] = 0;
        msg[3] = 6;
        msg[4] = 0;
        msg[5] = 0;
        msg[6] = 0;
        msg[8] = (byte) (this.aU >> 8);
        msg[9] = (byte) this.aU;
        msg[10] = (byte) (this.aV >> 8);
        msg[11] = (byte) this.aV;
        msg[12] = (byte) this.responseCode;
        msg[13] = 0;
        msg[14] = 0;
        msg[15] = 0;
        msg[16] = 0;
        return msg;
    }

    public int getTesterAddress() {
        return this.aU;
    }

    public void setTesterAddress(int testerAddress) {
        this.aU = testerAddress;
    }

    public int getEntityAddress() {
        return this.aV;
    }

    public void setEntityAddress(int entityAddress) {
        this.aV = entityAddress;
    }

    public int getResponseCode() {
        return this.responseCode;
    }

    public void setResponseCode(int responseCode) {
        this.responseCode = responseCode;
    }
}
