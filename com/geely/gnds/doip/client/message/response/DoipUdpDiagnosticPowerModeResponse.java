package com.geely.gnds.doip.client.message.response;

import com.geely.gnds.doip.client.message.AbstractDoipUdpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpDiagnosticPowerModeResponse.class */
public class DoipUdpDiagnosticPowerModeResponse extends AbstractDoipUdpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpDiagnosticPowerModeResponse.class);
    int aW;

    public DoipUdpDiagnosticPowerModeResponse(int diagnosticPowerMode) {
        this.aW = 0;
        this.aW = diagnosticPowerMode;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP diagnostic power mode response:");
        a(logger, level, "    Diagnostic power mode = " + this.aW);
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] message = {2, -3, 64, 4, 0, 0, 0, 1, 0};
        message[8] = (byte) this.aW;
        return message;
    }
}
