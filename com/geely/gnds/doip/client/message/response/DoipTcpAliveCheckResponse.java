package com.geely.gnds.doip.client.message.response;

import com.geely.gnds.doip.client.message.AbstractDoipTcpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpAliveCheckResponse.class */
public class DoipTcpAliveCheckResponse extends AbstractDoipTcpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipTcpAliveCheckResponse.class);
    private int sourceAddress;

    private DoipTcpAliveCheckResponse() {
        this.sourceAddress = 0;
    }

    public DoipTcpAliveCheckResponse(int sourceAddress) {
        this.sourceAddress = 0;
        this.sourceAddress = sourceAddress;
        if (logger.isInfoEnabled()) {
            a(Level.INFO);
        }
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP alive check response:");
        a(logger, level, "    Source address = " + this.sourceAddress);
        a(logger, level, "----------------------------------------");
    }

    public static DoipTcpAliveCheckResponse h(byte[] payload) {
        DoipTcpAliveCheckResponse doipMessage = new DoipTcpAliveCheckResponse();
        doipMessage.a(payload);
        doipMessage.a(Level.INFO);
        return doipMessage;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipTcpMessage
    public void a(byte[] payload) {
        int high = payload[0] & 255;
        int low = payload[1] & 255;
        this.sourceAddress = (high << 8) | low;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] message = {2, -3, 0, 8, 0, 0, 0, 2, 0, 0};
        message[8] = (byte) (this.sourceAddress >> 8);
        message[9] = (byte) this.sourceAddress;
        return message;
    }
}
