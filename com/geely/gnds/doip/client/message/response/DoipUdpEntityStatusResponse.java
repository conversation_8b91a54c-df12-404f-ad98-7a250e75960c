package com.geely.gnds.doip.client.message.response;

import com.geely.gnds.doip.client.message.AbstractDoipUdpMessage;
import org.apache.logging.log4j.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipUdpEntityStatusResponse.class */
public class DoipUdpEntityStatusResponse extends AbstractDoipUdpMessage {
    private static final Logger logger = LoggerFactory.getLogger(DoipUdpEntityStatusResponse.class);
    private int aX;
    private int aY;
    private int aZ;
    private long ba;

    public DoipUdpEntityStatusResponse(int nodeType, int maxNumberOfSockets, int currentNumberOfSockets, long maxDataSize) {
        this.aX = 0;
        this.aY = 0;
        this.aZ = 0;
        this.ba = 0L;
        this.aX = nodeType;
        this.aY = maxNumberOfSockets;
        this.aZ = currentNumberOfSockets;
        this.ba = maxDataSize;
        a(Level.INFO);
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public void a(Level level) {
        a(logger, level, "----------------------------------------");
        a(logger, level, "DoIP entity status response:");
        a(logger, level, "    Node type                 = " + this.aX);
        a(logger, level, "    Maximum number of sockets = " + this.aY);
        a(logger, level, "    Current number of sockets = " + this.aZ);
        a(logger, level, "    Maximum data size         = " + this.ba);
        a(logger, level, "----------------------------------------");
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] msg = {2, -3, 64, 2, 0, 0, 0, 7, (byte) this.aX, (byte) this.aY, (byte) this.aZ, (byte) (this.ba >> 24), (byte) (this.ba >> 16), (byte) (this.ba >> 8), (byte) this.ba};
        return msg;
    }

    public int getNodeType() {
        return this.aX;
    }

    public void setNodeType(int nodeType) {
        this.aX = nodeType;
    }

    public int getMaxNumberOfSockets() {
        return this.aY;
    }

    public void setMaxNumberOfSockets(int maxNumberOfSockets) {
        this.aY = maxNumberOfSockets;
    }

    public int getCurrentNumberOfSockets() {
        return this.aZ;
    }

    public void setCurrentNumberOfSockets(int currentNumberOfSockets) {
        this.aZ = currentNumberOfSockets;
    }

    public long getMaxDataSize() {
        return this.ba;
    }

    public void setMaxDataSize(long maxDataSize) {
        this.ba = maxDataSize;
    }
}
