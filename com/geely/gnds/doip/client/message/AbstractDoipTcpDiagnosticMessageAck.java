package com.geely.gnds.doip.client.message;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.Arrays;

/* loaded from: AbstractDoipTcpDiagnosticMessageAck.class */
public abstract class AbstractDoipTcpDiagnosticMessageAck extends AbstractDoipTcpMessage {
    private int sourceAddress;
    private int targetAddress;
    private int ay;
    private byte[] az;

    protected AbstractDoipTcpDiagnosticMessageAck() {
        this.sourceAddress = 0;
        this.targetAddress = 0;
        this.ay = 0;
        this.az = new byte[0];
    }

    public AbstractDoipTcpDiagnosticMessageAck(int sourceAddress, int targetAddress, int ackCode, byte[] message) {
        this.sourceAddress = 0;
        this.targetAddress = 0;
        this.ay = 0;
        this.az = new byte[0];
        this.sourceAddress = sourceAddress;
        this.targetAddress = targetAddress;
        this.ay = ackCode;
        this.az = message;
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipTcpMessage
    public void a(byte[] payload) {
        int high = payload[0] & 255;
        int low = payload[1] & 255;
        this.sourceAddress = (high << 8) | low;
        int high2 = payload[2] & 255;
        int low2 = payload[3] & 255;
        this.targetAddress = (high2 << 8) | low2;
        this.ay = payload[4] & 255;
        if (payload.length > ConstantEnum.FIVE.intValue()) {
            this.az = Arrays.copyOfRange(payload, 5, payload.length);
        } else {
            this.az = new byte[0];
        }
    }

    @Override // com.geely.gnds.doip.client.message.AbstractDoipMessage
    public byte[] getMessage() {
        byte[] data = new byte[13 + this.az.length];
        data[0] = 2;
        data[1] = -3;
        data[2] = Byte.MIN_VALUE;
        if (this.ay == 0) {
            data[3] = 2;
        } else {
            data[3] = 3;
        }
        data[4] = (byte) ((this.az.length + 5) >> 24);
        data[5] = (byte) ((this.az.length + 5) >> 16);
        data[6] = (byte) ((this.az.length + 5) >> 8);
        data[7] = (byte) (this.az.length + 5);
        data[8] = (byte) (this.sourceAddress >> 8);
        data[9] = (byte) this.sourceAddress;
        data[10] = (byte) (this.targetAddress >> 8);
        data[11] = (byte) this.targetAddress;
        data[12] = (byte) this.ay;
        if (this.az.length > 0) {
            System.arraycopy(this.az, 0, data, 13, this.az.length);
        }
        return data;
    }

    public int getSourceAddress() {
        return this.sourceAddress;
    }

    public void setSourceAddress(int sourceAddress) {
        this.sourceAddress = sourceAddress;
    }

    public int getTargetAddress() {
        return this.targetAddress;
    }

    public void setTargetAddress(int targetAddress) {
        this.targetAddress = targetAddress;
    }

    public int getAckCode() {
        return this.ay;
    }

    public void setAckCode(int ackCode) {
        this.ay = ackCode;
    }

    public byte[] getDiagnosticMessage() {
        return this.az;
    }

    public void setDiagnosticMessage(byte[] diagnosticMessage) {
        this.az = diagnosticMessage;
    }
}
