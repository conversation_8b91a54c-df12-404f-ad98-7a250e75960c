package com.geely.gnds.doip.client.txt;

import cn.hutool.core.io.FileUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.FdThread;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.service.LogUpLoadService;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.concurrent.ConcurrentLinkedQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: FdTxtLogger.class */
public class FdTxtLogger implements IFdTxtLogger {
    private static final Logger logger = LoggerFactory.getLogger(FdTxtLogger.class);
    private static final long TIME_SLEEP = 1000;
    private static final String LOGGER_NAME = "TXT日志记录器";
    private final File file;
    private String vin;
    private String username;
    private ConcurrentLinkedQueue<TxtData> txtQueue = new ConcurrentLinkedQueue<>();
    private FdThread thread = null;
    private TxtFile cm = null;
    private Object awakeObject = new Object();

    public FdTxtLogger(String path, String vin, String username, String start) throws InterruptedException {
        String childPath = username + "-" + vin + "-" + start + ".txt";
        this.file = FileUtil.file(path, childPath);
        this.vin = vin;
        this.username = username;
        createInstance("txtLogger-" + vin + "-" + start);
    }

    private void createInstance(String threadName) throws InterruptedException {
        this.thread = new FdThread(threadName) { // from class: com.geely.gnds.doip.client.txt.FdTxtLogger.1
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() throws InterruptedException {
                FdTxtLogger.logger.info("===TXT日志记录器启动完成===");
                while (isFdAlived()) {
                    TxtData msg = (TxtData) FdTxtLogger.this.txtQueue.poll();
                    long startTime = System.currentTimeMillis() + FdTxtLogger.TIME_SLEEP;
                    while (msg != null) {
                        if (FdTxtLogger.this.cm != null) {
                            try {
                                FdTxtLogger.this.cm.write(msg.getCurrent(), msg.getType(), msg.getId(), msg.getLevel(), msg.getContent());
                            } catch (Exception e) {
                                FdTxtLogger.logger.error("===TXT日志记录器填写日志失败===");
                                FdTxtLogger.logger.error(FdHelper.getExceptionAsString(e));
                            }
                            if (isFdAlived() && System.currentTimeMillis() > startTime) {
                                try {
                                    Thread.sleep(FdTxtLogger.TIME_SLEEP);
                                } catch (InterruptedException e2) {
                                    FdTxtLogger.logger.error("TXT日志记录器连续处理时间超过1s停顿失败!!!");
                                    FdTxtLogger.logger.error(FdHelper.getExceptionAsString(e2));
                                }
                                startTime = System.currentTimeMillis() + FdTxtLogger.TIME_SLEEP;
                            }
                        }
                        msg = (TxtData) FdTxtLogger.this.txtQueue.poll();
                    }
                    if (isFdAlived()) {
                        FdTxtLogger.this.wait4Data();
                    }
                }
                FdTxtLogger.logger.info("===TXT日志记录器关闭===");
            }
        };
        this.cm = new TxtFile();
        try {
            this.cm.a(this.file);
        } catch (IOException e) {
            logger.error("TXT日志记录器启动失败,日志文件名:" + this.file.getAbsolutePath());
            logger.error(FdHelper.getExceptionAsString(e));
            this.cm = null;
        }
        this.thread.start();
        try {
            Thread.sleep(100L);
        } catch (InterruptedException e2) {
            logger.error("TXT日志记录器启动Sleep中断,日志文件名:" + this.file.getAbsolutePath());
            logger.error(FdHelper.getExceptionAsString(e2));
        }
    }

    @Override // com.geely.gnds.doip.client.txt.IFdTxtLogger
    public void write(Date current, String type, Long id, String level, byte[] content) {
        TxtData d = new TxtData(current, type, id, level, content);
        this.txtQueue.add(d);
        notify4Data();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void wait4Data() {
        try {
            synchronized (this.awakeObject) {
                this.awakeObject.wait();
            }
        } catch (InterruptedException e) {
            logger.info("TXT日志记录器沉睡失败!!!");
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    private void notify4Data() {
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }

    @Override // com.geely.gnds.doip.client.txt.IFdTxtLogger
    public void close() throws IOException {
        LogUpLoadService logUpLoadService = (LogUpLoadService) SpringUtils.getBean(LogUpLoadService.class);
        TesterThread testerThread = (TesterThread) SpringUtils.getBean(TesterThread.class);
        this.thread.close();
        notify4Data();
        try {
            this.thread.join();
        } catch (InterruptedException e) {
            logger.error("TXT日志记录器关闭异常,异常原因:" + e.getMessage());
            logger.error(FdHelper.getExceptionAsString(e));
        }
        if (this.cm != null) {
            this.cm.close();
            if (TesterLoginUtils.isOnLine()) {
                testerThread.getPool().execute(() -> {
                    try {
                        if (this.vin != null) {
                            logUpLoadService.upload(this.vin, this.username, this.file.getAbsolutePath(), LogTypeEnum.TXT_PATH);
                        }
                    } catch (Exception e2) {
                        logger.error("TXT日志上传失败,失败原因->{}", e2.getMessage());
                    }
                });
            }
        }
    }

    public File getTxtFile() {
        return this.file;
    }
}
