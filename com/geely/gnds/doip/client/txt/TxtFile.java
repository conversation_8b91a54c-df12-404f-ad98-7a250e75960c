package com.geely.gnds.doip.client.txt;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.StreamCloseUtil;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: TxtFile.class */
public class TxtFile {
    private static final Logger LOGGER = LoggerFactory.getLogger(TxtFile.class);
    private FileOutputStream bI = null;
    private OutputStream bJ = null;

    public void a(File file) throws IOException {
        File parent = file.getParentFile();
        if (!parent.exists() && !parent.mkdirs()) {
            throw new IOException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00160) + file.getAbsolutePath());
        }
        try {
            this.bI = new FileOutputStream(file);
            this.bJ = new BufferedOutputStream(this.bI);
            this.bJ.flush();
        } catch (IOException e) {
            StreamCloseUtil.close(LOGGER, this.bJ, this.bI);
            throw e;
        }
    }

    public void close() throws IOException {
        StreamCloseUtil.closeAfterFlush(LOGGER, this.bJ, this.bI);
    }

    public String a(Object obj) {
        return " [" + obj + "]";
    }

    public void write(Date current, String type, Long id, String level, byte[] content) throws IOException {
        this.bJ.write(" - ".getBytes());
        this.bJ.write(a(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS, current)).getBytes());
        this.bJ.write(a(type).getBytes());
        this.bJ.write(a(id).getBytes());
        this.bJ.write(a(level).getBytes());
        this.bJ.write("[".getBytes());
        this.bJ.write(content);
        this.bJ.write("]".getBytes());
        this.bJ.write(System.getProperty("line.separator").getBytes());
        this.bJ.flush();
    }
}
