package com.geely.gnds.doip.client.txt;

import java.util.Date;

/* loaded from: TxtData.class */
public class TxtData {
    private final Date bo;
    private final String type;
    private final Long id;
    private final String co;
    private final byte[] cp;

    public TxtData(Date current, String type, Long id, String level, byte[] content) {
        this.bo = current;
        this.type = type;
        this.id = id;
        this.co = level;
        this.cp = content;
    }

    public Date getCurrent() {
        return this.bo;
    }

    public String getType() {
        return this.type;
    }

    public Long getId() {
        return this.id;
    }

    public String getLevel() {
        return this.co;
    }

    public byte[] getContent() {
        return this.cp;
    }
}
