package com.geely.gnds.doip.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: FdThread.class */
public class FdThread extends Thread {
    private volatile boolean threadAlive;
    private static final Logger logger = LoggerFactory.getLogger(FdThread.class);
    private Object lock;

    public FdThread(String name) {
        super(name);
        this.threadAlive = true;
        this.lock = new Object();
    }

    public void close() {
        this.threadAlive = false;
        notify4Data();
    }

    public boolean isFdAlived() {
        return this.threadAlive;
    }

    public void wait4Data() {
        try {
            synchronized (this.lock) {
                this.lock.wait();
            }
        } catch (InterruptedException e) {
            logger.error("等待接收数据失败！");
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    private void wait4Data(long timeout, String msg) {
        try {
            synchronized (this.lock) {
                this.lock.wait(timeout);
            }
        } catch (InterruptedException e) {
            logger.error(msg);
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    public void wait4BackProcessor(long timeout) {
        wait4Data(timeout, "等待接收BackProcessor数据失败！");
    }

    public void wait4TesterPresent(long timeout) {
        wait4Data(timeout, "TesterPresentThread暂停操作失败！");
    }

    public void notify4Data() {
        synchronized (this.lock) {
            this.lock.notify();
        }
    }
}
