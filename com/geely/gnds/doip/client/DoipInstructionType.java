package com.geely.gnds.doip.client;

/* loaded from: DoipInstructionType.class */
public enum DoipInstructionType {
    RoutingActivation(0, "10", "00"),
    DiagnosticSessionControl(16, "50", "10"),
    ECUReset(17, "51", "11"),
    ClearDiagnosticInformation(20, "54", "14"),
    ReadDTCInformation(25, "59", "19"),
    ReadDataByIdentifier(34, "62", "22"),
    SecurityAccess(39, "67", "27"),
    CommunicationControl(40, "68", "28"),
    WriteDataByIdentifier(46, "6E", "2E"),
    InputOutputControlByIdentifier(47, "6F", "2F"),
    RoutineControl(49, "71", "31"),
    TesterPresent(62, "7E", "3E"),
    ControlDTCSetting(133, "C5", "85"),
    RequestDownload(52, "74", "34"),
    RequestUpload(53, "75", "35"),
    TransferData(54, "76", "36"),
    RequestTransferExit(55, "77", "37"),
    RequestFileTransfer(56, "78", "38"),
    ReadMemoryByAddress(35, "63", "23"),
    ReadDataByPeriodicldentifier(42, "6A", "2A"),
    DynamicallyDefineDataIdentifier(44, "6C", "2C"),
    WriteMemoryByAddress(61, "7D", "3D"),
    Default(-1, "", "");

    private int value;
    private String posAckCode;
    private String code;

    public int getInstructionVaue() {
        return this.value;
    }

    public String getPosAckCode() {
        return this.posAckCode;
    }

    public String getInstructionCode() {
        return this.code;
    }

    DoipInstructionType(int value, String posAckCode, String code) {
        this.value = value;
        this.posAckCode = posAckCode;
        this.code = code;
    }

    public void initValue(int value, String posAckCode, String code) {
        this.value = value;
        this.posAckCode = posAckCode;
        this.code = code;
    }
}
