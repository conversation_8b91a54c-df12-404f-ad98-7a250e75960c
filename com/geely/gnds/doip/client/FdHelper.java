package com.geely.gnds.doip.client;

/* loaded from: FdHelper.class */
public class FdHelper {
    public static String getExceptionAsString(Throwable e) {
        StringBuilder s = new StringBuilder(4096);
        String message = e.getMessage();
        if (message != null) {
            s.append(message);
        }
        s.append("\n");
        s.append(e.getClass().getName());
        s.append("\n");
        StackTraceElement[] elements = e.getStackTrace();
        for (StackTraceElement element : elements) {
            s.append("    ");
            s.append(element);
            s.append("\n");
        }
        Throwable cause = e.getCause();
        if (cause != null) {
            s.append("Caused by :");
            s.append(getExceptionAsString(cause));
        }
        return s.toString();
    }
}
