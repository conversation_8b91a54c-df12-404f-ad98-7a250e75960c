package com.geely.gnds.doip.client.dro.document.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: VehicleInformation.class */
public class VehicleInformation {

    @XmlElement(name = "VIN")
    private String vin;

    @XmlElement(name = "FYON")
    private String fyon;

    @XmlElement(name = "ChassisNumber")
    private String T;

    @XmlElement(name = "VehicleType")
    private String vehicleType;

    @XmlElement(name = "StructureWeek")
    private String structureWeek;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getFyon() {
        return this.fyon;
    }

    public void setFyon(String fyon) {
        this.fyon = fyon;
    }

    public String getClassisNumber() {
        return this.T;
    }

    public void setClassisNumber(String classisNumber) {
        this.T = classisNumber;
    }

    public String getVehicleType() {
        return this.vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getStructureWeek() {
        return this.structureWeek;
    }

    public void setStructureWeek(String structureWeek) {
        this.structureWeek = structureWeek;
    }
}
