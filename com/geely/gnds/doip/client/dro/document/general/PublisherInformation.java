package com.geely.gnds.doip.client.dro.document.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: PublisherInformation.class */
public class PublisherInformation {

    @XmlElement(name = "ParmaId")
    private String O;

    @XmlElement(name = "PartnerId")
    private String P;

    @XmlElement(name = "DeviceId")
    private String Q;

    @XmlElement(name = "UserId")
    private String R;

    public String getParmaId() {
        return this.O;
    }

    public void setParmaId(String parmaId) {
        this.O = parmaId;
    }

    public String getPartnerId() {
        return this.P;
    }

    public void setPartnerId(String partnerId) {
        this.P = partnerId;
    }

    public String getDeviceId() {
        return this.Q;
    }

    public void setDeviceId(String deviceId) {
        this.Q = deviceId;
    }

    public String getUserId() {
        return this.R;
    }

    public void setUserId(String userId) {
        this.R = userId;
    }
}
