package com.geely.gnds.doip.client.dro.document.general;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: VehicleProfile.class */
public class VehicleProfile {

    @XmlElement
    @XmlElementWrapper(name = "ProfileIds")
    private List<ProfileIds> U;

    @XmlElement(name = "Model")
    private Model V;

    @XmlElement(name = "ModelYear")
    private ModelYear W;

    @XmlElement(name = "Engine")
    private Engine X;

    @XmlElement(name = "Transmission")
    private Transmission Y;

    public List<ProfileIds> getProfileIds() {
        return this.U;
    }

    public void setProfileIds(List<ProfileIds> profileIds) {
        this.U = profileIds;
    }

    public Model getModel() {
        return this.V;
    }

    public void setModel(Model model) {
        this.V = model;
    }

    public ModelYear getModelYear() {
        return this.W;
    }

    public void setModelYear(ModelYear modelYear) {
        this.W = modelYear;
    }

    public Engine getEngine() {
        return this.X;
    }

    public void setEngine(Engine engine) {
        this.X = engine;
    }

    public Transmission getTransmission() {
        return this.Y;
    }

    public void setTransmission(Transmission transmission) {
        this.Y = transmission;
    }
}
