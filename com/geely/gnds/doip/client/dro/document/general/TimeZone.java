package com.geely.gnds.doip.client.dro.document.general;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: TimeZone.class */
public class TimeZone {

    @XmlElement(name = "Value")
    private String value = DateUtils.getUtcZoneTime();

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
