package com.geely.gnds.doip.client.dro.document.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: General.class */
public class General {

    @XmlElement(name = "VehicleInformation")
    private VehicleInformation I;

    @XmlElement(name = "PublisherInformation")
    private PublisherInformation J;

    @XmlElement(name = "ApplicationInformation")
    private ApplicationInformation K;

    @XmlElement(name = "TimeZone")
    private TimeZone L;

    @XmlElement(name = "VehicleProfile")
    private VehicleProfile M;

    public VehicleInformation getVehicleInformation() {
        return this.I;
    }

    public void setVehicleInformation(VehicleInformation vehicleInformation) {
        this.I = vehicleInformation;
    }

    public PublisherInformation getPublisherInformation() {
        return this.J;
    }

    public void setPublisherInformation(PublisherInformation publisherInformation) {
        this.J = publisherInformation;
    }

    public ApplicationInformation getApplicationInformation() {
        return this.K;
    }

    public void setApplicationInformation(ApplicationInformation applicationInformation) {
        this.K = applicationInformation;
    }

    public TimeZone getTimeZone() {
        return this.L;
    }

    public void setTimeZone(TimeZone timeZone) {
        this.L = timeZone;
    }

    public VehicleProfile getVehicleProfile() {
        return this.M;
    }

    public void setVehicleProfile(VehicleProfile vehicleProfile) {
        this.M = vehicleProfile;
    }
}
