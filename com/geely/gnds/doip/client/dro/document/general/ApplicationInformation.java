package com.geely.gnds.doip.client.dro.document.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: ApplicationInformation.class */
public class ApplicationInformation {

    @XmlElement(name = "Type")
    private String type;

    @XmlElement(name = "Version")
    private String version;

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
