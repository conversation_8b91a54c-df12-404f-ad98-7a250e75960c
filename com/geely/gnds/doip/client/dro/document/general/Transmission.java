package com.geely.gnds.doip.client.dro.document.general;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: Transmission.class */
public class Transmission {

    @XmlElement(name = "Cid")
    private String H;

    @XmlElement(name = "Description")
    private String description;

    public String getCid() {
        return this.H;
    }

    public void setCid(String cid) {
        this.H = cid;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
