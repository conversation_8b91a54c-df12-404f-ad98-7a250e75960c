package com.geely.gnds.doip.client.dro.document.root;

import com.geely.gnds.doip.client.dro.document.event.DiagnosticReadoutEvent;
import com.geely.gnds.doip.client.dro.document.general.General;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "DroResult")
/* loaded from: DroResult.class */
public class DroResult {

    @XmlAttribute(name = "xmlns:xsi")
    private String Z = "http://www.w3.org/2001/XMLSchema-instance";

    @XmlAttribute(name = "xmlns:xsd")
    private String aa = "http://www.w3.org/2001/XMLSchema";

    @XmlAttribute(name = "Version")
    private String version = "2.0";

    @XmlElement(name = "General")
    private General ab;

    @XmlElement(name = "DiagnosticReadoutEvent")
    private DiagnosticReadoutEvent diagnosticReadoutEvent;

    public String getXsi() {
        return this.Z;
    }

    public void setXsi(String xsi) {
        this.Z = xsi;
    }

    public String getXsd() {
        return this.aa;
    }

    public void setXsd(String xsd) {
        this.aa = xsd;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public General getGeneral() {
        return this.ab;
    }

    public void setGeneral(General general) {
        this.ab = general;
    }

    public DiagnosticReadoutEvent getDiagnosticReadoutEvent() {
        return this.diagnosticReadoutEvent;
    }

    public void setDiagnosticReadoutEvent(DiagnosticReadoutEvent diagnosticReadoutEvent) {
        this.diagnosticReadoutEvent = diagnosticReadoutEvent;
    }
}
