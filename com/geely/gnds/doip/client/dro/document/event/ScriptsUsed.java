package com.geely.gnds.doip.client.dro.document.event;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: ScriptsUsed.class */
public class ScriptsUsed {

    @XmlElement
    @XmlElementWrapper(name = "ScriptInformation")
    private List<ScriptInformation> k;

    public List<ScriptInformation> getScriptInformation() {
        return this.k;
    }

    public void setScriptInformation(List<ScriptInformation> scriptInformation) {
        this.k = scriptInformation;
    }
}
