package com.geely.gnds.doip.client.dro.document.event;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlTransient;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: Item.class */
public class Item {

    @XmlElement(name = "Name")
    private String name;

    @XmlElement(name = "Value")
    private String value;

    @XmlElement(name = "CarcomTextId")
    private int t = 0;

    @XmlElement(name = "DataType")
    private String dataType;

    @XmlElement(name = "Parameter")
    @XmlElementWrapper(name = "ParameterCollection")
    private List<Parameter> u;

    @XmlTransient
    private String dtcId;

    @XmlTransient
    private String status;

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDtcId() {
        return this.dtcId;
    }

    public void setDtcId(String dtcId) {
        this.dtcId = dtcId;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getCarcomTextId() {
        return this.t;
    }

    public void setCarcomTextId(int carcomTextId) {
        this.t = carcomTextId;
    }

    public String getDataType() {
        return this.dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public List<Parameter> getParameter() {
        return this.u;
    }

    public void setParameter(List<Parameter> parameter) {
        this.u = parameter;
    }
}
