package com.geely.gnds.doip.client.dro.document.event;

import com.geely.gnds.tester.enums.ConstantEnum;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: VersionInformation.class */
public class VersionInformation {

    @XmlElement(name = "CarcomDbVersion")
    private String G = ConstantEnum.COMMA;

    public String getCarcomDbVersion() {
        return this.G;
    }

    public void setCarcomDbVersion(String carcomDbVersion) {
        this.G = carcomDbVersion;
    }
}
