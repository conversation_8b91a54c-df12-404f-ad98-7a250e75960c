package com.geely.gnds.doip.client.dro.document.event;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: DiagnosticReadoutEvent.class */
public class DiagnosticReadoutEvent {

    @XmlElement(name = "LocalReadOutDateAndTimeStamp")
    private String f = DateUtils.getTime();

    @XmlElement(name = "UTCReadOutDateAndTimeStamp")
    private String g = DateUtils.getUtcTime(DateUtils.YYYY_MM_DD_HH_MM_SS_TWELVE);

    @XmlElement(name = "OdometerValue")
    private String h;

    @XmlElement(name = "VehicleGlobalTime")
    private String i;

    @XmlElement(name = "PowerMode")
    private String j;

    @XmlElement(name = "ScriptInformation")
    @XmlElementWrapper(name = "ScriptsUsed")
    private List<ScriptInformation> k;

    @XmlElement(name = "VersionInformation")
    private VersionInformation l;

    @XmlElement(name = "Ecu")
    @XmlElementWrapper(name = "Ecus")
    private List<Ecu> m;

    public VersionInformation getVersionInformation() {
        return this.l;
    }

    public void setVersionInformation(VersionInformation versionInformation) {
        this.l = versionInformation;
    }

    public List<Ecu> getEcu() {
        return this.m;
    }

    public void setEcu(List<Ecu> ecu) {
        this.m = ecu;
    }

    public String getLocalReadOutDateAndTimeStamp() {
        return this.f;
    }

    public void setLocalReadOutDateAndTimeStamp(String localReadOutDateAndTimeStamp) {
        this.f = localReadOutDateAndTimeStamp;
    }

    public String getUtcReadOutDateAndTimeStamp() {
        return this.g;
    }

    public void setUtcReadOutDateAndTimeStamp(String utcReadOutDateAndTimeStamp) {
        this.g = utcReadOutDateAndTimeStamp;
    }

    public String getOdometerValue() {
        return this.h;
    }

    public void setOdometerValue(String odometerValue) {
        this.h = odometerValue;
    }

    public String getVehicleGlobalTime() {
        return this.i;
    }

    public void setVehicleGlobalTime(String vehicleGlobalTime) {
        this.i = vehicleGlobalTime;
    }

    public String getPowerMode() {
        return this.j;
    }

    public void setPowerMode(String powerMode) {
        this.j = powerMode;
    }

    public List<ScriptInformation> getScriptInformation() {
        return this.k;
    }

    public void setScriptInformation(List<ScriptInformation> scriptInformation) {
        this.k = scriptInformation;
    }
}
