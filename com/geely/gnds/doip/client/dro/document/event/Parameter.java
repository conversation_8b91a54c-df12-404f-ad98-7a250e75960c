package com.geely.gnds.doip.client.dro.document.event;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: Parameter.class */
public class Parameter {

    @XmlElement(name = "Name")
    private String name;

    @XmlElement(name = "Value")
    private String value;

    @XmlElement(name = "Unit")
    private String unit;

    @XmlElement(name = "DataType")
    private String dataType;

    @XmlElement(name = "CarcomTextId")
    private int t = 0;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getDataType() {
        return this.dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public int getCarcomTextId() {
        return this.t;
    }

    public void setCarcomTextId(int carcomTextId) {
        this.t = carcomTextId;
    }
}
