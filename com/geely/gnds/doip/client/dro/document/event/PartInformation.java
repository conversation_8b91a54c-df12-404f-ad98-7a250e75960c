package com.geely.gnds.doip.client.dro.document.event;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: PartInformation.class */
public class PartInformation {

    @XmlElement(name = "HardwarePartNumber")
    private String hardwarePartNumber;

    @XmlElement(name = "DiagnosticPartNumber")
    private String diagnosticPartNumber;

    @XmlElement(name = "SerialNumber")
    private String v;

    @XmlElement(name = "SoftwarePartNumbers")
    private SoftwarePartNumbers w;

    public String getHardwarePartNumber() {
        return this.hardwarePartNumber;
    }

    public void setHardwarePartNumber(String hardwarePartNumber) {
        this.hardwarePartNumber = hardwarePartNumber;
    }

    public String getDiagnosticPartNumber() {
        return this.diagnosticPartNumber;
    }

    public void setDiagnosticPartNumber(String diagnosticPartNumber) {
        this.diagnosticPartNumber = diagnosticPartNumber;
    }

    public String getSerialNumber() {
        return this.v;
    }

    public void setSerialNumber(String serialNumber) {
        this.v = serialNumber;
    }

    public SoftwarePartNumbers getSoftwarePartNumbers() {
        return this.w;
    }

    public void setSoftwarePartNumbers(SoftwarePartNumbers softwarePartNumbers) {
        this.w = softwarePartNumbers;
    }
}
