package com.geely.gnds.doip.client.dro.document.event;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: ScriptInformation.class */
public class ScriptInformation {

    @XmlElement(name = "ScriptId")
    private String D = "GRI-00000001";

    @XmlElement(name = "VccNumber")
    private String E = "GRI-00000001";

    @XmlElement(name = "Type")
    private String type = "Readout";

    @XmlElement(name = "Name")
    private String name = "Vehicle_Readout";

    public String getScriptId() {
        return this.D;
    }

    public void setScriptId(String scriptId) {
        this.D = scriptId;
    }

    public String getVccNumber() {
        return this.E;
    }

    public void setVccNumber(String vccNumber) {
        this.E = vccNumber;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
