package com.geely.gnds.doip.client.dro.document.event;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlTransient;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: Request.class */
public class Request {

    @XmlElement(name = "DiagnosticRequest")
    private String x;

    @XmlElement(name = "UnparsedResponse")
    private String unparsedResponse;

    @XmlElement(name = "Item")
    @XmlElementWrapper(name = "ParsedData")
    private List<Item> z;

    @XmlTransient
    private String udsType;

    @XmlTransient
    private String A;

    @XmlTransient
    private String did;

    @XmlTransient
    private Set<String> C;

    @XmlElement(name = "OrderPosition")
    private int y = 1;

    @XmlTransient
    private Boolean B = true;

    public String getDid() {
        return this.did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public Set<String> getResponseSplitData() {
        return this.C;
    }

    public void setResponseSplitData(Set<String> responseSplitData) {
        this.C = responseSplitData;
    }

    public Boolean getHasParsedData() {
        return this.B;
    }

    public void setHasParsedData(Boolean hasParsedData) {
        this.B = hasParsedData;
    }

    public String getCategory19() {
        return this.A;
    }

    public void setCategory19(String category19) {
        this.A = category19;
    }

    public String getUdsType() {
        return this.udsType;
    }

    public void setUdsType(String udsType) {
        this.udsType = udsType;
    }

    public String getDiagnosticRequest() {
        return this.x;
    }

    public void setDiagnosticRequest(String diagnosticRequest) {
        this.x = diagnosticRequest;
    }

    public int getOrderPosition() {
        return this.y;
    }

    public void setOrderPosition(int orderPosition) {
        this.y = orderPosition;
    }

    public String getUnparsedResponse() {
        return this.unparsedResponse;
    }

    public void setUnparsedResponse(String unparsedResponse) {
        this.unparsedResponse = unparsedResponse;
    }

    public List<Item> getItem() {
        return this.z;
    }

    public void setItem(List<Item> item) {
        this.z = item;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Request request = (Request) o;
        return this.x.equals(request.x);
    }

    public int hashCode() {
        return Objects.hash(this.x);
    }
}
