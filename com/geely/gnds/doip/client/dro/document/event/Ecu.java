package com.geely.gnds.doip.client.dro.document.event;

import java.util.Set;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

@XmlAccessorType(XmlAccessType.FIELD)
/* loaded from: Ecu.class */
public class Ecu {

    @XmlElement(name = "ShortName")
    private String shortName;

    @XmlElement(name = "LongName")
    private String n;

    @XmlElement(name = "Address")
    private String address;

    @XmlElement(name = "EcuType")
    private int o = 0;

    @XmlElement(name = "CanId")
    private String q = "00000000";

    @XmlElement(name = "PartInformation")
    private PartInformation r;

    @XmlElement(name = "Request")
    @XmlElementWrapper(name = "DiagnosticInformation")
    private Set<Request> s;

    public String getShortName() {
        return this.shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getLongName() {
        return this.n;
    }

    public void setLongName(String longName) {
        this.n = longName;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getEcuType() {
        return this.o;
    }

    public void setEcuType(int ecuType) {
        this.o = ecuType;
    }

    public String getCanId() {
        return this.q;
    }

    public void setCanId(String canId) {
        this.q = canId;
    }

    public PartInformation getPartInformation() {
        return this.r;
    }

    public void setPartInformation(PartInformation partInformation) {
        this.r = partInformation;
    }

    public Set<Request> getRequest() {
        return this.s;
    }

    public void setRequest(Set<Request> request) {
        this.s = request;
    }
}
