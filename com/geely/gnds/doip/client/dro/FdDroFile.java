package com.geely.gnds.doip.client.dro;

import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.common.UploadCloudBean;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import java.text.SimpleDateFormat;
import java.util.Date;

/* loaded from: FdDroFile.class */
public class FdDroFile {
    private static final String a = "_";
    private static final String b = "GNDS-GRI";
    private static final String c = "GNDS-Geely";
    private static final String d = "GNDS-lynkco";
    private String vin;
    private String tenantName;
    private String username;
    private String testerId;
    private String fileName;
    private String ossPath;
    private String e;

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getTenantName() {
        return this.tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getTesterId() {
        return this.testerId;
    }

    public void setTesterId(String testerId) {
        this.testerId = testerId;
    }

    public FdDroFile(String vin, String tenantName, String username) {
        this.fileName = "";
        this.ossPath = "";
        this.vin = vin;
        this.tenantName = tenantName;
        this.username = username;
        SimpleDateFormat df = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS);
        String dateTime = df.format(new Date());
        this.fileName = vin + a + dateTime + a + "00000000";
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileNameDro(String mile) {
        String tName;
        SimpleDateFormat df = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS);
        String dateTime = df.format(new Date());
        this.fileName = this.vin + a + dateTime + a + mile;
        if (b.equalsIgnoreCase(this.tenantName)) {
            tName = "GRI";
        } else if (c.equalsIgnoreCase(this.tenantName)) {
            tName = "GDMP";
        } else if (d.equalsIgnoreCase(this.tenantName)) {
            tName = "CEP";
        } else {
            tName = this.tenantName;
        }
        String mTime = new SimpleDateFormat("yyyy/MMdd").format(new Date());
        this.ossPath = "DRO/" + tName + "/" + mTime + "/" + this.fileName + ConstantEnum.EXT_ZIP;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOssPath() {
        return this.ossPath;
    }

    public void setOssPath(String ossPath) {
        this.ossPath = ossPath;
    }

    public UploadCloudBean f() {
        UploadCloudBean bean = new UploadCloudBean(this.vin, this.username, this.tenantName, "", this.ossPath, this.testerId, TestLogTypeEnum.dro);
        return bean;
    }

    public FdDroFile() {
        this.fileName = "";
        this.ossPath = "";
    }

    public String getMile() {
        return this.e;
    }

    public void setMile(String mile) {
        this.e = mile;
    }
}
