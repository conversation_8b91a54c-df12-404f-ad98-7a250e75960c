package com.geely.gnds.doip.client.xml;

import com.geely.gnds.doip.client.enums.XmlStatusEnum;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.Optional;
import java.util.concurrent.ConcurrentLinkedQueue;
import org.dom4j.Element;

/* loaded from: XmlSeq.class */
public class XmlSeq {
    private static final String cY = "yyyy-MM-dd'T'HH:mm:ss";
    private static final String cZ = XmlStatusEnum.STATUS_SUCCESS.getValue();
    private static final String dG = XmlStatusEnum.MASKED.getValue();
    private static final String da = XmlStatusEnum.STATUS_FAIL.getValue();
    private static final String db = "TEST";
    private static final String de = "TEST_ATTRIBUTES";
    private static final String df = "TEST_ATTRIBUTE";
    private static final String dg = "logger_XMLS";
    private static final String dH = "logger_XML";
    private static final String dc = "FAULT_CODES";
    private static final String dd = "FAULT_CODE";
    private static final String dI = "FAULT_DTC_DME";
    private static final String dJ = "DME";
    private static final String dh = "Test";
    private static final String di = "TestTime";
    private static final String dj = "Status";
    private static final String dk = "Test_ID";
    private static final String dl = "Version";
    private static final String dK = "TEST_Variant_Name";
    private static final String dL = "Val";
    private static final String dM = "LogicStatement";
    private static final String dN = "ComponentId";
    private static final String dO = "log_name";
    private static final String dP = "Val";
    private static final String dQ = "ComponentId";
    private static final String dR = "URI";
    private static final String dn = "Status";

    /* renamed from: do, reason: not valid java name */
    private static final String f1do = "FaultCode";
    private static final String dp = "ShortDesc";
    private static final String dq = "ComponentId";
    private static final String ECU_ADDRESS = "ecuAddress";
    private static final String ECU_NAME = "ecuName";
    private static final String dS = "Code";
    private static final String dT = "Status";
    private static final String dU = "DiagnosticNumber";
    private static final String dV = "DtcId";
    private static final String dW = "describe";
    private static final String dt = "SendUDS";
    private static final String dv = "ReceiveUDS";
    private final String name;
    private final Date time;
    private final String id;
    private final String version;
    private ConcurrentLinkedQueue<XmlFault> cX = new ConcurrentLinkedQueue<>();
    private ConcurrentLinkedQueue<XmlAttribute> dC = new ConcurrentLinkedQueue<>();
    private ConcurrentLinkedQueue<XmlBuriedPoint> dD = new ConcurrentLinkedQueue<>();
    private ConcurrentLinkedQueue<XmlDtc> dE = new ConcurrentLinkedQueue<>();
    private ConcurrentLinkedQueue<XmlFault> dF = new ConcurrentLinkedQueue<>();
    private boolean success = true;
    private String status = XmlStatusEnum.NOT_STARTED.getValue();
    private boolean dX = false;

    public ConcurrentLinkedQueue<XmlFault> getFaults() {
        return this.cX;
    }

    public void setFaults(ConcurrentLinkedQueue<XmlFault> faults) {
        this.cX = faults;
    }

    public ConcurrentLinkedQueue<XmlBuriedPoint> getPoints() {
        return this.dD;
    }

    public void setPoints(ConcurrentLinkedQueue<XmlBuriedPoint> points) {
        this.dD = points;
    }

    public boolean isResultSetBySeq() {
        return this.dX;
    }

    public void setResultSetBySeq(boolean resultSetBySeq) {
        this.dX = resultSetBySeq;
    }

    public void setStatus(String status) {
        if (XmlStatusEnum.STATUS_FAIL.getValue().equalsIgnoreCase(status)) {
            this.success = false;
        } else {
            this.success = true;
        }
        this.status = status;
    }

    public void b(XmlFault fault) {
        if (!fault.isSuccess()) {
            this.success = false;
            this.status = XmlStatusEnum.STATUS_FAIL.getValue();
        }
        this.cX.add(fault);
    }

    public void a(XmlAttribute attr) {
        this.dC.add(attr);
    }

    public void a(XmlBuriedPoint point) {
        this.dD.add(point);
    }

    public void a(XmlDtc dtc) {
        this.dE.add(dtc);
    }

    public void c(XmlFault xmlFault) {
        this.dF.add(xmlFault);
    }

    public ConcurrentLinkedQueue<XmlFault> getXmldids() {
        return this.dF;
    }

    public void fail() {
        this.success = false;
        this.status = XmlStatusEnum.STATUS_FAIL.getValue();
    }

    public String getStatus() {
        return this.status;
    }

    public XmlSeq(String name, Date time, String id, String version) {
        this.name = name;
        this.time = time;
        this.id = id;
        this.version = version;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public String getName() {
        return this.name;
    }

    public Date getTime() {
        return this.time;
    }

    public String getId() {
        return this.id;
    }

    public String getVersion() {
        return this.version;
    }

    public Element a(Element processNode) {
        Element testElement = processNode.addElement(db);
        testElement.addAttribute(dh, this.name);
        testElement.addAttribute(di, a(this.time));
        testElement.addAttribute(dk, this.id);
        testElement.addAttribute(dl, this.version);
        Element xmlsElement = testElement.addElement(dg);
        Iterator<XmlBuriedPoint> it = this.dD.iterator();
        while (it.hasNext()) {
            XmlBuriedPoint point = it.next();
            Element xmlElement = xmlsElement.addElement(dH);
            xmlElement.addAttribute(dO, point.getLogName());
            xmlElement.addAttribute("Val", point.getValue());
            xmlElement.addAttribute("ComponentId", point.getComponentId());
            xmlElement.addAttribute(dR, point.getUri());
        }
        Element faultsElement = testElement.addElement(dc);
        boolean failFalg = false;
        Iterator<XmlFault> it2 = this.cX.iterator();
        while (it2.hasNext()) {
            XmlFault fault = it2.next();
            if (!fault.isSuccess()) {
                if (XmlStatusEnum.STATUS_FAIL.getValue().equals(fault.getStatus())) {
                    failFalg = true;
                }
                Element faultElement = faultsElement.addElement(dd);
                faultElement.addAttribute("Status", fault.getStatus());
                faultElement.addAttribute(f1do, fault.getCode());
                faultElement.addAttribute(dp, fault.getShortDesc());
                faultElement.addAttribute(dt, fault.getSended());
                faultElement.addAttribute(dv, fault.getReceived());
                faultElement.addAttribute("ComponentId", fault.getComponentId());
                Optional.ofNullable(fault.getEcuAddress()).ifPresent(s -> {
                    faultElement.addAttribute(ECU_ADDRESS, fault.getEcuAddress());
                });
                faultElement.addAttribute(ECU_NAME, fault.getEcuName());
            }
        }
        if (!this.success && !failFalg && this.cX.size() > 0) {
            this.status = XmlStatusEnum.MASKED.getValue();
        }
        testElement.addAttribute("Status", this.status);
        Element dtcsElement = testElement.addElement(dI);
        Iterator<XmlDtc> it3 = this.dE.iterator();
        while (it3.hasNext()) {
            XmlDtc dtc = it3.next();
            Element dtcElement = dtcsElement.addElement(dJ);
            dtcElement.addAttribute(dS, dtc.getCode());
            dtcElement.addAttribute("Status", dtc.isSuccess() ? cZ : da);
            dtcElement.addAttribute(dU, dtc.getDiagnosticNumber());
            dtcElement.addAttribute(dV, dtc.getDtcId());
            dtcElement.addAttribute(dW, dtc.getDescribe());
        }
        testElement.addElement(dc);
        Iterator<XmlFault> it4 = this.dF.iterator();
        while (it4.hasNext()) {
            XmlFault fault2 = it4.next();
            Element faultElement2 = faultsElement.addElement(dd);
            faultElement2.addAttribute("Status", fault2.getStatus());
            faultElement2.addAttribute(f1do, fault2.getCode());
            faultElement2.addAttribute(dp, fault2.getShortDesc());
            faultElement2.addAttribute(dt, fault2.getSended());
            faultElement2.addAttribute(dv, fault2.getReceived());
            faultElement2.addAttribute("ComponentId", fault2.getComponentId());
            Optional.ofNullable(fault2.getEcuAddress()).ifPresent(s2 -> {
                faultElement2.addAttribute(ECU_ADDRESS, fault2.getEcuAddress());
            });
            faultElement2.addAttribute(ECU_NAME, fault2.getEcuName());
        }
        return processNode;
    }

    private String a(Date dateTime) {
        SimpleDateFormat format = new SimpleDateFormat(cY);
        return format.format(dateTime);
    }
}
