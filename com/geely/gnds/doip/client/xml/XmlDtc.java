package com.geely.gnds.doip.client.xml;

/* loaded from: XmlDtc.class */
public class XmlDtc {
    private final String code;
    private final boolean success;
    private final String diagnosticNumber;
    private final String dtcId;
    private final String dx;

    public XmlDtc(String code, boolean success, String diagnosticNumber, String dtcId, String describe) {
        this.code = code;
        this.success = success;
        this.diagnosticNumber = diagnosticNumber;
        this.dtcId = dtcId;
        this.dx = describe;
    }

    public String getCode() {
        return this.code;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public String getDiagnosticNumber() {
        return this.diagnosticNumber;
    }

    public String getDtcId() {
        return this.dtcId;
    }

    public String getDescribe() {
        return this.dx;
    }
}
