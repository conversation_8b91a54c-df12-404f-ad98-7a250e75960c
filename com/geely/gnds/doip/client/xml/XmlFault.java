package com.geely.gnds.doip.client.xml;

import com.geely.gnds.ruoyi.common.constant.Constants;
import java.util.Date;

/* loaded from: XmlFault.class */
public class XmlFault {
    private final boolean success;
    private final String status;
    private final String code;
    private final String dy;
    private final String cW;
    private final String dz;
    private final String dA;
    private final Date dB;
    private final String uri;
    private final String ecuAddress;
    private final String ecuName;

    public XmlFault(boolean success, String code, String shortDesc, Date time, String uri) {
        this.success = success;
        this.code = code;
        this.dy = shortDesc;
        this.dz = null;
        this.cW = null;
        this.dA = null;
        this.uri = uri;
        this.dB = time;
        this.ecuAddress = null;
        this.ecuName = null;
        if (success) {
            this.status = Constants.LOGIN_SUCCESS;
        } else {
            this.status = "Fail";
        }
    }

    public XmlFault(boolean success, String code, String shortDesc, String componentId, String sended, String received, boolean masked, String ecuAddress, String ecuName) {
        this.success = success;
        this.code = code;
        this.dy = shortDesc;
        this.cW = componentId;
        this.dz = sended;
        this.dA = received;
        this.dB = new Date();
        this.uri = null;
        this.ecuAddress = ecuAddress;
        this.ecuName = ecuName;
        if (masked) {
            this.status = "Masked";
        } else if (success) {
            this.status = Constants.LOGIN_SUCCESS;
        } else {
            this.status = "Fail";
        }
    }

    public String getCode() {
        return this.code;
    }

    public String getComponentId() {
        return this.cW;
    }

    public String getShortDesc() {
        return this.dy;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public String getSended() {
        return this.dz;
    }

    public String getReceived() {
        return this.dA;
    }

    public String getStatus() {
        return this.status;
    }

    public Date getFaultTime() {
        return this.dB;
    }

    public String getUri() {
        return this.uri;
    }

    public String getEcuAddress() {
        return this.ecuAddress;
    }

    public String getEcuName() {
        return this.ecuName;
    }
}
