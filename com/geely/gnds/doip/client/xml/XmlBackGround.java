package com.geely.gnds.doip.client.xml;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.Optional;
import java.util.concurrent.ConcurrentLinkedQueue;
import org.dom4j.Element;

/* loaded from: XmlBackGround.class */
public class XmlBackGround {
    private static final String cY = "yyyy-MM-dd'T'HH:mm:ss";
    private static final String cZ = "Success";
    private static final String da = "Fail";
    private static final String db = "TEST";
    private static final String dc = "FAULT_CODES";
    private static final String dd = "FAULT_CODE";
    private static final String de = "TEST_ATTRIBUTES";
    private static final String df = "TEST_ATTRIBUTE";
    private static final String dg = "logger_XMLS";
    private static final String dh = "Test";
    private static final String di = "TestTime";
    private static final String dj = "Status";
    private static final String dk = "Test_ID";
    private static final String dl = "Version";
    private static final String dm = "URI";
    private static final String dn = "Status";

    /* renamed from: do, reason: not valid java name */
    private static final String f0do = "FaultCode";
    private static final String dp = "ShortDesc";
    private static final String dq = "ComponentId";
    private static final String dr = "URI";
    private static final String ds = "faultTime";
    private static final String dt = "SendUDS";
    private static final String dv = "ReceiveUDS";
    private final String name;
    private final Date time;
    private final String id;
    private final String version;
    private ConcurrentLinkedQueue<XmlFault> cX = new ConcurrentLinkedQueue<>();
    private boolean success = true;
    private String status = "Success";

    public XmlBackGround(String name, Date time, String id, String version, String uri) {
        this.name = name;
        this.time = time;
        this.id = id;
        this.version = version;
    }

    public Element a(Element processNode) {
        if (!this.success) {
            Element testElement = processNode.addElement(db);
            testElement.addAttribute(dh, this.name);
            testElement.addAttribute(di, a(this.time));
            testElement.addAttribute("Status", this.success ? "Success" : da);
            testElement.addAttribute(dk, this.id);
            testElement.addAttribute(dl, this.version);
            testElement.addElement(de);
            testElement.addElement(dg);
            Element faultsElement = testElement.addElement(dc);
            Iterator<XmlFault> it = this.cX.iterator();
            while (it.hasNext()) {
                XmlFault fault = it.next();
                if (!fault.isSuccess()) {
                    Element faultElement = faultsElement.addElement(dd);
                    faultElement.addAttribute("Status", fault.isSuccess() ? "Success" : da);
                    Optional.ofNullable(fault.getCode()).ifPresent(s -> {
                        faultElement.addAttribute(f0do, fault.getCode());
                    });
                    Optional.ofNullable(fault.getShortDesc()).ifPresent(s2 -> {
                        faultElement.addAttribute(dp, fault.getShortDesc());
                    });
                    Optional.ofNullable(fault.getSended()).ifPresent(s3 -> {
                        faultElement.addAttribute(dt, fault.getSended());
                    });
                    Optional.ofNullable(fault.getReceived()).ifPresent(s4 -> {
                        faultElement.addAttribute(dv, fault.getReceived());
                    });
                    Optional.ofNullable(fault.getComponentId()).ifPresent(s5 -> {
                        faultElement.addAttribute(dq, fault.getComponentId());
                    });
                    Optional.ofNullable(fault.getUri()).ifPresent(s6 -> {
                        faultElement.addAttribute("URI", fault.getUri());
                    });
                    Optional.ofNullable(fault.getFaultTime()).ifPresent(s7 -> {
                        faultElement.addAttribute(ds, a(fault.getFaultTime()));
                    });
                }
            }
        }
        return processNode;
    }

    private String a(Date dateTime) {
        SimpleDateFormat format = new SimpleDateFormat(cY);
        return format.format(dateTime);
    }

    public void b(XmlFault fault) {
        if (!fault.isSuccess()) {
            this.success = false;
        }
        this.cX.add(fault);
    }
}
