package com.geely.gnds.doip.client.xml;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.common.OssUtils;
import com.geely.gnds.tester.common.UploadCloudBean;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import com.geely.gnds.tester.service.LogUpLoadService;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/* loaded from: FdXmlLogger.class */
public class FdXmlLogger {
    private static final Logger logger = LoggerFactory.getLogger(FdXmlLogger.class);
    private static final String cB = "NG_GLDS_Result_Log";
    private static final String cC = "account_info";
    private static final String cD = "PROCESS";
    private static final String cE = "Account";
    private static final String cF = "Mac";
    private static final String cG = "Model";
    private static final String cH = "TENANT";
    private static final String cI = "ClintVersion";
    private static final String cJ = "UTF-8";
    private static final String cK = "诊断序列XML日志输出失败（{0}）！";
    private static final String cL = "诊断序列XML日志关闭失败（{0}）！";
    private final File cM;
    private final File cN;
    private final String cO;
    private final String cP;
    private final String tenantName;
    private final String cQ;
    private final String cR;
    private String vin;
    private List<XmlSeq> cS;
    private XmlBackGround xmlBackGround;
    private static final String b = "GNDS-GRI";
    private static final String c = "GNDS-Geely";
    private static final String d = "GNDS-lynkco";
    private String fileName;
    private Date cT;

    public File getXmlFile() {
        return this.cM;
    }

    public File getTempFile() {
        return this.cN;
    }

    public String getTesterModel() {
        return this.cO;
    }

    public String getTenant() {
        return this.cP;
    }

    public String getTenantName() {
        return this.tenantName;
    }

    public String getAccount() {
        return this.cQ;
    }

    public String getTesterMac() {
        return this.cR;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public List<XmlSeq> getSeqs() {
        return this.cS;
    }

    public void setSeqs(List<XmlSeq> seqs) {
        this.cS = seqs;
    }

    public XmlBackGround getXmlBackGround() {
        return this.xmlBackGround;
    }

    public void setXmlBackGround(XmlBackGround xmlBackGround) {
        this.xmlBackGround = xmlBackGround;
    }

    public Date getFileTime() {
        return this.cT;
    }

    public String getFileName() {
        return this.fileName;
    }

    public FdXmlLogger(String account, String testerMac, File xmlFile) {
        this.cS = new CopyOnWriteArrayList();
        this.xmlBackGround = null;
        this.fileName = "";
        this.cT = new Date();
        this.cP = "";
        this.tenantName = "";
        this.cO = "";
        this.cR = testerMac;
        this.cQ = account;
        this.cM = xmlFile;
        this.cN = xmlFile;
        this.xmlBackGround = new XmlBackGround("GNDS_Background", new Date(), "GRI-********", "1.36.0.2", "");
    }

    public FdXmlLogger(String tenant, String testerModel, String account, String testerMac, String path, String vin, String start, String tenantName) {
        this.cS = new CopyOnWriteArrayList();
        this.xmlBackGround = null;
        this.fileName = "";
        this.cT = new Date();
        this.cP = tenant;
        this.tenantName = tenantName;
        this.cO = testerModel;
        this.cR = testerMac;
        this.cQ = account;
        this.vin = vin;
        File temp = new File(path + File.separator + "Temp");
        if (!temp.exists()) {
            temp.mkdirs();
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.YYYYMMDDHHMMSSSSS);
            Date date = sdf.parse(start);
            this.cT = date;
        } catch (Exception e) {
        }
        this.fileName = account + "-" + vin + "-" + start + ConstantEnum.XML;
        this.cN = FileUtil.file(temp, StringUtils.cleanPath(this.fileName));
        this.cM = FileUtil.file(path, StringUtils.cleanPath(this.fileName));
        this.xmlBackGround = new XmlBackGround("GNDS_Background", new Date(), "GRI-********", "1.36.0.2", "");
    }

    public void close() {
        LogUpLoadService logUpLoadService = (LogUpLoadService) SpringUtils.getBean(LogUpLoadService.class);
        TesterThread testerThread = (TesterThread) SpringUtils.getBean(TesterThread.class);
        if (this.cM == null) {
            return;
        }
        File parent = this.cM.getParentFile();
        if (parent != null && !parent.exists()) {
            parent.mkdirs();
        }
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(cB);
        Element accountElement = root.addElement(cC);
        accountElement.addAttribute(cE, this.cQ);
        accountElement.addAttribute(cF, this.cR);
        accountElement.addAttribute(cG, this.cO);
        accountElement.addAttribute(cH, this.cP);
        accountElement.addAttribute(cI, "1.36.0.2");
        Element processElement = root.addElement(cD);
        for (XmlSeq seq : this.cS) {
            seq.a(processElement);
        }
        this.xmlBackGround.a(processElement);
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setEncoding("UTF-8");
        XMLWriter writer = null;
        try {
            try {
                writer = new XMLWriter(new FileOutputStream(this.cM), format);
                writer.setEscapeText(false);
                writer.write(doc);
                if (this.cN.exists()) {
                    this.cN.delete();
                }
                if (writer != null) {
                    try {
                        writer.close();
                        if (TesterLoginUtils.isOnLine()) {
                            testerThread.getPool().execute(() -> {
                                try {
                                    if (this.vin != null) {
                                        logUpLoadService.upload(this.vin, this.cQ, this.cM.getAbsolutePath(), LogTypeEnum.XML_PATH);
                                    }
                                } catch (Exception e) {
                                    logger.error("诊断日志上传失败,失败原因->{}", e.getMessage());
                                }
                            });
                        }
                    } catch (IOException e) {
                        logger.error(MessageFormat.format(cL, this.cM.getAbsolutePath()), e);
                    }
                }
            } catch (FileNotFoundException | UnsupportedEncodingException e2) {
                logger.error(MessageFormat.format(cK, this.cM.getAbsolutePath()), e2);
                if (writer != null) {
                    try {
                        writer.close();
                        if (TesterLoginUtils.isOnLine()) {
                            testerThread.getPool().execute(() -> {
                                try {
                                    if (this.vin != null) {
                                        logUpLoadService.upload(this.vin, this.cQ, this.cM.getAbsolutePath(), LogTypeEnum.XML_PATH);
                                    }
                                } catch (Exception e3) {
                                    logger.error("诊断日志上传失败,失败原因->{}", e3.getMessage());
                                }
                            });
                        }
                    } catch (IOException e3) {
                        logger.error(MessageFormat.format(cL, this.cM.getAbsolutePath()), e3);
                    }
                }
            } catch (IOException e4) {
                logger.error(MessageFormat.format(cK, this.cM.getAbsolutePath()), e4);
                if (writer != null) {
                    try {
                        writer.close();
                        if (TesterLoginUtils.isOnLine()) {
                            testerThread.getPool().execute(() -> {
                                try {
                                    if (this.vin != null) {
                                        logUpLoadService.upload(this.vin, this.cQ, this.cM.getAbsolutePath(), LogTypeEnum.XML_PATH);
                                    }
                                } catch (Exception e32) {
                                    logger.error("诊断日志上传失败,失败原因->{}", e32.getMessage());
                                }
                            });
                        }
                    } catch (IOException e5) {
                        logger.error(MessageFormat.format(cL, this.cM.getAbsolutePath()), e5);
                    }
                }
            }
        } catch (Throwable th) {
            if (writer != null) {
                try {
                    writer.close();
                    if (TesterLoginUtils.isOnLine()) {
                        testerThread.getPool().execute(() -> {
                            try {
                                if (this.vin != null) {
                                    logUpLoadService.upload(this.vin, this.cQ, this.cM.getAbsolutePath(), LogTypeEnum.XML_PATH);
                                }
                            } catch (Exception e32) {
                                logger.error("诊断日志上传失败,失败原因->{}", e32.getMessage());
                            }
                        });
                    }
                } catch (IOException e6) {
                    logger.error(MessageFormat.format(cL, this.cM.getAbsolutePath()), e6);
                }
            }
            throw th;
        }
    }

    public File o() {
        if (this.cM == null) {
            return null;
        }
        File parent = this.cM.getParentFile();
        if (parent != null && !parent.exists()) {
            parent.mkdirs();
        }
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(cB);
        Element accountElement = root.addElement(cC);
        accountElement.addAttribute(cE, this.cQ);
        accountElement.addAttribute(cF, this.cR);
        accountElement.addAttribute(cG, this.cO);
        accountElement.addAttribute(cH, this.cP);
        accountElement.addAttribute(cI, "1.36.0.2");
        Element processElement = root.addElement(cD);
        for (XmlSeq seq : this.cS) {
            seq.a(processElement);
        }
        this.xmlBackGround.a(processElement);
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setEncoding("UTF-8");
        XMLWriter writer = null;
        try {
            try {
                writer = new XMLWriter(new FileOutputStream(this.cM), format);
                writer.setEscapeText(false);
                writer.write(doc);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e) {
                        logger.error(MessageFormat.format(cL, this.cM.getAbsolutePath()), e);
                    }
                }
            } catch (FileNotFoundException | UnsupportedEncodingException e2) {
                logger.error(MessageFormat.format(cK, this.cM.getAbsolutePath()), e2);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e3) {
                        logger.error(MessageFormat.format(cL, this.cM.getAbsolutePath()), e3);
                    }
                }
            } catch (IOException e4) {
                logger.error(MessageFormat.format(cK, this.cM.getAbsolutePath()), e4);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e5) {
                        logger.error(MessageFormat.format(cL, this.cM.getAbsolutePath()), e5);
                    }
                }
            }
            return this.cM;
        } catch (Throwable th) {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e6) {
                    logger.error(MessageFormat.format(cL, this.cM.getAbsolutePath()), e6);
                }
            }
            throw th;
        }
    }

    public void p() {
        if (this.cN == null) {
            return;
        }
        File parent = this.cN.getParentFile();
        if (parent != null && !parent.exists()) {
            parent.mkdirs();
        }
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(cB);
        Element accountElement = root.addElement(cC);
        accountElement.addAttribute(cE, this.cQ);
        accountElement.addAttribute(cF, this.cR);
        accountElement.addAttribute(cG, this.cO);
        accountElement.addAttribute(cH, this.cP);
        accountElement.addAttribute(cI, "1.36.0.2");
        Element processElement = root.addElement(cD);
        for (XmlSeq seq : this.cS) {
            seq.a(processElement);
        }
        this.xmlBackGround.a(processElement);
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setEncoding("UTF-8");
        XMLWriter writer = null;
        try {
            try {
                writer = new XMLWriter(new FileOutputStream(this.cN), format);
                writer.setEscapeText(false);
                writer.write(doc);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e) {
                        logger.error(MessageFormat.format(cL, this.cN.getAbsolutePath()), e);
                    }
                }
            } catch (FileNotFoundException | UnsupportedEncodingException e2) {
                logger.error(MessageFormat.format(cK, this.cN.getAbsolutePath()), e2);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e3) {
                        logger.error(MessageFormat.format(cL, this.cN.getAbsolutePath()), e3);
                    }
                }
            } catch (IOException e4) {
                logger.error(MessageFormat.format(cK, this.cN.getAbsolutePath()), e4);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e5) {
                        logger.error(MessageFormat.format(cL, this.cN.getAbsolutePath()), e5);
                    }
                }
            }
        } catch (Throwable th) {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e6) {
                    logger.error(MessageFormat.format(cL, this.cN.getAbsolutePath()), e6);
                }
            }
            throw th;
        }
    }

    public void a(XmlSeq seq) {
        this.cS.add(seq);
    }

    public void a(XmlFault xmlFault) {
        this.xmlBackGround.b(xmlFault);
    }

    public void a(XmlSeq seq, String name) {
        File xmlFile = b(seq, name);
        b(xmlFile);
    }

    @NotNull
    private File b(XmlSeq seq, String name) {
        SimpleDateFormat df = new SimpleDateFormat(DateUtils.YYYYMMDD_HHMMSS);
        String dateTime = df.format(new Date());
        File xmlFile = FileUtil.file(OssUtils.getFullLogPath(LogTypeEnum.XML_PATH), StringUtils.cleanPath(this.vin + "_" + dateTime + "_" + name + ConstantEnum.XML));
        File parent = xmlFile.getParentFile();
        if (parent != null && !parent.exists()) {
            parent.mkdirs();
        }
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(cB);
        Element accountElement = root.addElement(cC);
        accountElement.addAttribute(cE, this.cQ);
        accountElement.addAttribute(cF, this.cR);
        accountElement.addAttribute(cG, this.cO);
        accountElement.addAttribute(cH, this.cP);
        accountElement.addAttribute(cI, "1.36.0.2");
        Element processElement = root.addElement(cD);
        if (seq != null) {
            seq.a(processElement);
        }
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setEncoding("UTF-8");
        XMLWriter writer = null;
        try {
            try {
                writer = new XMLWriter(new FileOutputStream(xmlFile), format);
                writer.setEscapeText(false);
                writer.write(doc);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e) {
                        logger.error(MessageFormat.format(cL, xmlFile.getAbsolutePath()), e);
                    }
                }
            } catch (FileNotFoundException | UnsupportedEncodingException e2) {
                logger.error(MessageFormat.format(cK, xmlFile.getAbsolutePath()), e2);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e3) {
                        logger.error(MessageFormat.format(cL, xmlFile.getAbsolutePath()), e3);
                    }
                }
            } catch (IOException e4) {
                logger.error(MessageFormat.format(cK, xmlFile.getAbsolutePath()), e4);
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e5) {
                        logger.error(MessageFormat.format(cL, xmlFile.getAbsolutePath()), e5);
                    }
                }
            }
            return xmlFile;
        } catch (Throwable th) {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e6) {
                    logger.error(MessageFormat.format(cL, xmlFile.getAbsolutePath()), e6);
                }
            }
            throw th;
        }
    }

    private void b(File file) {
        try {
            String mTime = new SimpleDateFormat("yyyy/MMdd").format(new Date());
            String absolutePath = file.getAbsolutePath();
            String zipPath = absolutePath.replace(ConstantEnum.XML, ConstantEnum.EXT_ZIP);
            File zip = ZipUtil.zip(absolutePath, zipPath);
            String tName = "default";
            if (b.equalsIgnoreCase(this.tenantName)) {
                tName = "GRI";
            } else if (c.equalsIgnoreCase(this.tenantName)) {
                tName = "GDMP";
            }
            if (d.equalsIgnoreCase(this.tenantName)) {
                tName = "CEP";
            }
            String ossPath = "EDR/" + tName + "/" + mTime + "/" + zip.getName();
            if (file.exists()) {
                file.delete();
            }
            logger.info("开始上传EDR：{}", zip.getAbsolutePath());
            UploadCloudBean bean = new UploadCloudBean(this.vin, this.cQ, this.tenantName, zip.getAbsolutePath(), ossPath, this.cR, TestLogTypeEnum.edr);
            OssUtils ossUtils = (OssUtils) SpringUtils.getBean(OssUtils.class);
            ossUtils.uploadLogToCloud(bean);
            logger.info("EDR上传成功：{}", zip.getAbsolutePath());
        } catch (Exception e) {
            logger.error("EDR上传失败：", e);
        }
    }
}
