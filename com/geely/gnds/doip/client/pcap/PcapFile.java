package com.geely.gnds.doip.client.pcap;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.util.StreamCloseUtil;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetAddress;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: PcapFile.class */
public class PcapFile {
    private static final DoipUtil bu = DoipUtil.getInstance();
    private static final Logger LOGGER = LoggerFactory.getLogger(PcapFile.class);
    private static final int bv = -1582119980;
    private static final int bw = 2;
    private static final int bx = 4;
    private static final int by = 0;
    private static final int bz = 0;
    private static final int bA = 4194304;
    private static final int bB = 1;
    private static final int bC = 14;
    private static final int bD = 40;
    private static final int bE = 54;
    private static final int bF = 8;
    private static final int bG = 28;
    private static final int bH = 42;
    private FileOutputStream bI = null;
    private OutputStream bJ = null;
    private int bK = 1;
    private int bL = 1;
    private int bM = 1;
    private int bN = 1;
    private int bO = 0;

    public void a(File file) throws IOException {
        File parent = file.getParentFile();
        if (!parent.exists() && !parent.mkdirs()) {
            throw new IOException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00098) + file.getAbsolutePath());
        }
        try {
            this.bI = new FileOutputStream(file);
            this.bJ = new BufferedOutputStream(this.bI);
            a(this.bJ);
        } catch (IOException e) {
            StreamCloseUtil.close(LOGGER, this.bJ, this.bI);
            throw e;
        }
    }

    private void a(OutputStream os) throws IOException {
        os.write(bu.int2Byte4(bv), 0, 4);
        os.write(bu.int2Byte2(2), 0, 2);
        os.write(bu.int2Byte2(4), 0, 2);
        os.write(bu.int2Byte4(0), 0, 4);
        os.write(bu.int2Byte4(4), 0, 4);
        os.write(bu.int2Byte4(bA), 0, 4);
        os.write(bu.int2Byte4(1), 0, 4);
        os.flush();
    }

    public void write(Date date, byte[] data, DoipAddress targetAddress, DoipAddress sourceAddress, boolean send) throws DoipException, IOException {
        a(date, data, targetAddress, sourceAddress, send, false, (String) null);
    }

    public void a(Date date, DoipAddress targetAddress, DoipAddress sourceAddress, boolean send, String localhostName) throws DoipException, IOException {
        byte[] data = new byte[0];
        a(date, data, targetAddress, sourceAddress, send, false, localhostName);
    }

    private void a(String sourceIp, String destIp, byte[] data, long checkSumCrc32, int protocal, int headBaseLength, int flag) throws DoipException, IOException {
        this.bJ.write(69);
        this.bJ.write(0);
        this.bJ.write(bu.int2Byte2LowLast(headBaseLength + data.length), 0, 2);
        this.bJ.write(bu.int2Byte2LowLast(getId()), 0, 2);
        this.bJ.write(bu.int2Byte2(flag), 0, 2);
        this.bJ.write(bu.int2Byte(64));
        this.bJ.write(bu.int2Byte(protocal));
        this.bJ.write(bu.int2Byte4(Long.valueOf(checkSumCrc32).intValue()), 0, 2);
        this.bJ.write(bu.ip2Bytes(sourceIp), 0, 4);
        this.bJ.write(bu.ip2Bytes(destIp), 0, 4);
    }

    private long a(long current, int dataLen) throws IOException {
        byte[] time = bu.int2Byte4(Long.valueOf(current / 1000).intValue());
        long em = Math.floorMod(current, 1000L) * 1000;
        byte[] ms = bu.int2Byte4(Long.valueOf(em).intValue());
        byte[] len = bu.int2Byte4(bE + dataLen);
        this.bJ.write(time, 0, 4);
        this.bJ.write(ms, 0, 4);
        this.bJ.write(len, 0, 4);
        this.bJ.write(len, 0, 4);
        byte[] header = new byte[16];
        for (int i = 0; i < ConstantEnum.FORE.intValue(); i++) {
            header[i] = time[i];
            header[4 + i] = ms[i];
            header[8 + i] = len[i];
            header[12 + i] = len[i];
        }
        return bu.getCheckSumCrc32(header);
    }

    private long b(long current, int dataLen) throws IOException {
        byte[] time = bu.int2Byte4(Long.valueOf(current / 1000).intValue());
        long em = Math.floorMod(current, 1000L) * 1000;
        byte[] ms = bu.int2Byte4(Long.valueOf(em).intValue());
        byte[] len = bu.int2Byte4(bH + dataLen);
        this.bJ.write(time, 0, 4);
        this.bJ.write(ms, 0, 4);
        this.bJ.write(len, 0, 4);
        this.bJ.write(len, 0, 4);
        byte[] header = new byte[16];
        for (int i = 0; i < ConstantEnum.FORE.intValue(); i++) {
            header[i] = time[i];
            header[4 + i] = ms[i];
            header[8 + i] = len[i];
            header[12 + i] = len[i];
        }
        return bu.getCheckSumCrc32(header);
    }

    private void a(DoipAddress targetAddress, DoipAddress sourceAddress, boolean send) throws DoipException, IOException {
        byte[] tcpTarget = targetAddress.getMac();
        byte[] tcpSource = sourceAddress.getMac();
        this.bJ.write(send ? tcpTarget : tcpSource, 0, 6);
        this.bJ.write(send ? tcpSource : tcpTarget, 0, 6);
        byte[] type = {8, 0};
        this.bJ.write(type, 0, 2);
    }

    private void a(InetAddress target, byte[] udpTarget, InetAddress src, byte[] udpSource, boolean send) throws DoipException, IOException {
        this.bJ.write(send ? udpTarget : udpSource, 0, 6);
        this.bJ.write(send ? udpSource : udpTarget, 0, 6);
        byte[] type = {8, 0};
        this.bJ.write(type, 0, 2);
    }

    private void a(int sourcePort, int destPort, byte[] data, int flag, boolean send) throws DoipException, IOException {
        this.bJ.write(bu.int2Byte2LowLast(sourcePort), 0, 2);
        this.bJ.write(bu.int2Byte2LowLast(destPort), 0, 2);
        if (send) {
            this.bJ.write(bu.int2Byte4LowLast(this.bK), 0, 4);
            this.bK += data.length;
            this.bJ.write(bu.int2Byte4LowLast(this.bM), 0, 4);
            this.bM += 0;
        } else {
            this.bJ.write(bu.int2Byte4LowLast(this.bL), 0, 4);
            this.bL += data.length;
            this.bJ.write(bu.int2Byte4LowLast(this.bN), 0, 4);
            this.bN += 0;
        }
        this.bJ.write(bu.int2Byte2(flag), 0, 2);
        this.bJ.write(bu.int2Byte2(2576), 0, 2);
        long checkSumCrc32 = bu.getCheckSumCrc32(data);
        this.bJ.write(bu.int2Byte2(Long.valueOf(checkSumCrc32).intValue()), 0, 2);
        this.bJ.write(bu.int2Byte2(0), 0, 2);
    }

    private void a(int sourcePort, int destPort, byte[] data) throws IOException {
        this.bJ.write(bu.int2Byte2LowLast(sourcePort), 0, 2);
        this.bJ.write(bu.int2Byte2LowLast(destPort), 0, 2);
        this.bJ.write(bu.int2Byte2LowLast(8 + data.length), 0, 2);
        long checkSumCrc32 = bu.getCheckSumCrc32(data);
        this.bJ.write(bu.int2Byte2(Long.valueOf(checkSumCrc32).intValue()), 0, 2);
    }

    private String a(String targetIp, String sourceIp, String localhostName, boolean send, boolean reset) {
        String destIp = localhostName;
        if (!reset) {
            destIp = send ? targetIp : sourceIp;
        }
        return destIp;
    }

    private void a(Date date, byte[] data, DoipAddress targetAddress, DoipAddress sourceAddress, boolean send, boolean reset, String localhostName) throws DoipException, IOException {
        long checkSumCrc32 = a(date.getTime(), data.length);
        a(targetAddress, sourceAddress, send);
        a(send ? sourceAddress.getIp() : targetAddress.getIp(), a(targetAddress.getIp(), sourceAddress.getIp(), localhostName, send, reset), data, checkSumCrc32, 6, bD, 64);
        a(send ? sourceAddress.getPort() : targetAddress.getPort(), send ? targetAddress.getPort() : sourceAddress.getPort(), data, reset ? 1104 : 6224, send);
        if (data.length > 0) {
            this.bJ.write(data, 0, data.length);
        }
        this.bJ.flush();
    }

    public void a(Date date, byte[] data, DoipAddress target, DoipAddress source, boolean send) throws DoipException, IOException {
        long checkSumCrc32 = b(date.getTime(), data.length);
        a(target.getAddress(), target.getMac(), source.getAddress(), source.getMac(), send);
        a(send ? source.getIp() : target.getIp(), send ? target.getIp() : source.getIp(), data, checkSumCrc32, 17, bG, 0);
        a(send ? source.getPort() : target.getPort(), send ? target.getPort() : source.getPort(), data);
        if (data.length > 0) {
            this.bJ.write(data, 0, data.length);
        }
        this.bJ.flush();
    }

    public void close() throws IOException {
        StreamCloseUtil.closeAfterFlush(LOGGER, this.bJ, this.bI);
    }

    private int getId() {
        this.bO++;
        if (this.bO > 60000) {
            this.bO = 0;
        }
        return this.bO;
    }
}
