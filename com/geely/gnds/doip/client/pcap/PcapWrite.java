package com.geely.gnds.doip.client.pcap;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.common.LogTypeEnum;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.service.LogUpLoadService;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import java.io.File;
import java.net.InetAddress;
import java.util.List;
import java.util.Optional;
import org.pcap4j.core.PcapNativeException;
import org.pcap4j.core.PcapNetworkInterface;
import org.pcap4j.core.Pcaps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Configurable
@Component
/* loaded from: PcapWrite.class */
public class PcapWrite {

    @Autowired
    private ThreadPoolTaskExecutor ce;
    private static final Logger logger = LoggerFactory.getLogger(PcapWrite.class);

    public PcapThread2 a(InetAddress address, PcapParam pcapParam) {
        return a(address, pcapParam.getPath(), pcapParam.getVin(), pcapParam.getUsername(), pcapParam.getStart());
    }

    public PcapThread2 a(InetAddress address, String path, String vin, String username, String start) {
        String childPath = StringUtils.cleanPath(username + "-" + vin + "-" + start + ".pcap");
        String fullPath = path + childPath;
        File pcapFile = new File(fullPath);
        logger.info("pcap文件路径：{}", pcapFile.getAbsolutePath());
        try {
            try {
                PcapNetworkInterface nif = a(address);
                if (nif == null || StrUtil.contains(address.getHostAddress(), ":")) {
                    logger.error("Can not find PcapNetwork inteface,pcap log write fail.");
                    return null;
                }
                String name = nif.getName();
                logger.info("连车网卡name：{}", name);
                logger.info("Pcap file write is start,IP Address:{}", address.getHostAddress());
                PcapThread2 pcapThread = PcapThreadGlobal.c(name);
                PcapThread2 thread = new PcapThread2(nif, username, pcapFile, -1, "port 13400 or port 3496");
                if (pcapThread != null) {
                    logger.info("获取到pcapThread");
                    thread.setQueue(pcapThread.getQueue());
                } else {
                    logger.info("没有获取到pcapThread，需要创建新的");
                }
                thread.setName("gnds-pcap-thread-" + username + "-" + thread.getId());
                thread.setVin(vin);
                this.ce.execute(thread);
                PcapThreadGlobal.a(name, thread);
                return thread;
            } catch (PcapNativeException e) {
                logger.error("Unable to get NIC IP address,Failed to monitor network card:{}", e.getMessage());
                return null;
            }
        } catch (Exception e2) {
            logger.error("Pcap日志无法记录，原因：{}", e2.getMessage());
            return null;
        }
    }

    public PcapThread2 a(InetAddress address, String path, String vin, String username) {
        File pcapFile = new File(path);
        logger.info("pcap文件路径：{}", pcapFile.getAbsolutePath());
        try {
            PcapNetworkInterface nif = a(address);
            if (nif == null || StrUtil.contains(address.getHostAddress(), ":")) {
                logger.error("Can not find PcapNetwork inteface,pcap log write fail.");
                return null;
            }
            logger.info("Pcap file write is start,IP Address:{}", address.getHostAddress());
            PcapThread2 thread = new PcapThread2(nif, username, pcapFile, -1, "port 13400 or port 3496");
            thread.setName("gnds-pcap-thread-" + username + "-" + thread.getId());
            thread.setVin(vin);
            this.ce.execute(thread);
            return thread;
        } catch (Exception e) {
            logger.error("Pcap日志无法记录，原因：{}", e.getMessage());
            return null;
        } catch (PcapNativeException e2) {
            logger.error("Unable to get NIC IP address,Failed to monitor network card:{}", e2.getMessage());
            return null;
        }
    }

    public static PcapNetworkInterface a(InetAddress addr) throws PcapNativeException {
        if (addr == null) {
            StringBuilder sb = new StringBuilder();
            sb.append("addr: ").append(addr);
            throw new NullPointerException(sb.toString());
        }
        String localIp = addr.getHostAddress();
        if (StrUtil.equalsIgnoreCase(localIp, ConstantEnum.LOCAL) || StrUtil.equalsIgnoreCase(localIp, ConstantEnum.IP_ALL)) {
            return (PcapNetworkInterface) Pcaps.findAllDevs().stream().filter(x -> {
                return x.isLoopBack();
            }).findFirst().orElseThrow(() -> {
                return new NullPointerException("未找到回环网卡");
            });
        }
        PcapNetworkInterface anInterface = Pcaps.getDevByAddress(addr);
        if (anInterface == null) {
            logger.error("未能在 Pcap 网卡列表找到对应 IP {} 对应的网卡。", addr.getHostAddress());
            List<PcapNetworkInterface> allDevs = Pcaps.findAllDevs();
            logger.error("Pcap 可用网卡：\n{}", JSONUtil.toJsonPrettyStr(allDevs));
        }
        return anInterface;
    }

    public void a(PcapThread pcapThread) {
        Optional.ofNullable(pcapThread).ifPresent((v0) -> {
            v0.stopLog();
        });
        logger.info("Pcap 文件写入关闭成功<<<<<<<");
        b(pcapThread);
    }

    public static void b(PcapThread pcapThread) {
        logger.warn("准备上传pcap");
        LogUpLoadService logUpLoadService = (LogUpLoadService) SpringUtils.getBean(LogUpLoadService.class);
        TesterThread testerThread = (TesterThread) SpringUtils.getBean(TesterThread.class);
        if (TesterLoginUtils.isOnLine()) {
            testerThread.getPool().execute(() -> {
                try {
                    if (pcapThread.getVin() != null) {
                        logUpLoadService.upload(pcapThread.getVin(), pcapThread.getUsername(), pcapThread.getPcapFile().getAbsolutePath(), LogTypeEnum.PCAP_PATH);
                    }
                } catch (Exception e) {
                    logger.error("通讯日志上传失败,失败原因->{}", e.getMessage());
                }
            });
        }
    }
}
