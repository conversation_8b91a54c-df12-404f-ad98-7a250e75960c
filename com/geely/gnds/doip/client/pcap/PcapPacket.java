package com.geely.gnds.doip.client.pcap;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Objects;
import org.pcap4j.packet.Packet;

/* loaded from: PcapPacket.class */
public class PcapPacket implements Serializable {
    private Packet bP;
    private Timestamp bQ;

    public Packet getPacket() {
        return this.bP;
    }

    public void setPacket(Packet packet) {
        this.bP = packet;
    }

    public Timestamp getTimestamp() {
        return this.bQ;
    }

    public void setTimestamp(Timestamp timestamp) {
        this.bQ = timestamp;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PcapPacket that = (PcapPacket) o;
        return Objects.equals(this.bP, that.bP) && Objects.equals(this.bQ, that.bQ);
    }

    public int hashCode() {
        return Objects.hash(this.bP, this.bQ);
    }

    public String toString() {
        return "PcapPacket{packet=" + this.bP + ", timestamp=" + this.bQ + '}';
    }

    public PcapPacket(Packet packet, Timestamp timestamp) {
        this.bP = packet;
        this.bQ = timestamp;
    }
}
