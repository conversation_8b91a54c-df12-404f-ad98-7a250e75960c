package com.geely.gnds.doip.client.pcap;

import java.util.Date;

/* loaded from: PcapData.class */
public class PcapData {
    private final Date bo;
    private final byte[] data;
    private final boolean bp;
    private final DoipAddress bq;
    private final DoipAddress br;
    private final String bt;
    private final int type;

    public PcapData(Date current, byte[] data, DoipAddress targetAddress, DoipAddress sourceAddress, boolean send, boolean isTcp) {
        this.bo = current;
        this.data = data;
        this.bp = send;
        this.type = isTcp ? 1 : 2;
        this.bq = sourceAddress;
        this.br = targetAddress;
        this.bt = null;
    }

    public PcapData(Date current, DoipAddress targetAddress, DoipAddress sourceAddress, String localhostName) {
        this.bo = current;
        byte[] data = new byte[0];
        this.data = data;
        this.bp = false;
        this.type = 4;
        this.bq = sourceAddress;
        this.br = targetAddress;
        this.bt = localhostName;
    }

    public int getType() {
        return this.type;
    }

    public Date getCurrent() {
        return this.bo;
    }

    public byte[] getData() {
        return this.data;
    }

    public boolean isSend() {
        return this.bp;
    }

    public String getLocalhostName() {
        return this.bt;
    }

    public DoipAddress getSourceAddress() {
        return this.bq;
    }

    public DoipAddress getTargetAddress() {
        return this.br;
    }
}
