package com.geely.gnds.doip.client.pcap;

import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.FdThread;
import com.geely.gnds.doip.client.IFdPcapLogger;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.util.TesterLoginUtils;
import com.geely.gnds.tester.util.TesterThread;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ConcurrentLinkedQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/* loaded from: FdPcapLogger.class */
public class FdPcapLogger implements IFdPcapLogger {
    private static final Logger logger = LoggerFactory.getLogger(FdPcapLogger.class);
    private static final long TIME_SLEEP = 1000;
    private final File file;
    private String vin;
    private static final String LOGGER_NAME = "PCAP日志记录器";
    private ConcurrentLinkedQueue<PcapData> bl = new ConcurrentLinkedQueue<>();
    private FdThread thread = null;
    private PcapFile bm = null;
    private Object awakeObject = new Object();

    public FdPcapLogger(File file) throws InterruptedException {
        this.file = file;
        createInstance("pcapLogger-" + System.currentTimeMillis());
    }

    public FdPcapLogger(String path) throws InterruptedException {
        this.file = new File(new File(ConstantEnum.POINT, path), "doip-" + System.currentTimeMillis() + ".pcap");
        createInstance("pcapLogger-" + System.currentTimeMillis());
    }

    public FdPcapLogger(String path, String vin, String username) throws InterruptedException {
        this.file = new File(new File(ConstantEnum.POINT, path), username + "-" + vin + "-" + System.currentTimeMillis() + ".pcap");
        createInstance("pcapLogger-" + vin);
    }

    public FdPcapLogger(String path, String vin, String username, String start) throws InterruptedException {
        String childPath = username + "-" + vin + "-" + start + ".pcap";
        this.file = new File(new File(ConstantEnum.POINT, path), (String) Objects.requireNonNull(StringUtils.cleanPath(childPath)));
        this.vin = vin;
        createInstance("pcapLogger-" + vin);
    }

    private void createInstance(String threadName) throws InterruptedException {
        this.thread = new FdThread(threadName) { // from class: com.geely.gnds.doip.client.pcap.FdPcapLogger.1
            /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() throws InterruptedException {
                FdPcapLogger.logger.info("PCAP日志记录器启动完成。");
                while (isFdAlived()) {
                    PcapData msg = (PcapData) FdPcapLogger.this.bl.poll();
                    long startTime = System.currentTimeMillis() + FdPcapLogger.TIME_SLEEP;
                    while (msg != null) {
                        if (FdPcapLogger.this.bm != null) {
                            try {
                                switch (msg.getType()) {
                                    case 1:
                                        FdPcapLogger.this.bm.write(msg.getCurrent(), msg.getData(), msg.getTargetAddress(), msg.getSourceAddress(), msg.isSend());
                                        break;
                                    case 2:
                                        FdPcapLogger.this.bm.a(msg.getCurrent(), msg.getData(), msg.getTargetAddress(), msg.getSourceAddress(), msg.isSend());
                                        break;
                                    case 4:
                                        FdPcapLogger.this.bm.a(msg.getCurrent(), msg.getTargetAddress(), msg.getSourceAddress(), msg.isSend(), msg.getLocalhostName());
                                        break;
                                }
                            } catch (Exception e) {
                                FdPcapLogger.logger.error("PCAP日志记录器填写日志失败！");
                                FdPcapLogger.logger.error(FdHelper.getExceptionAsString(e));
                            }
                            if (isFdAlived() && System.currentTimeMillis() > startTime) {
                                try {
                                    Thread.sleep(FdPcapLogger.TIME_SLEEP);
                                } catch (InterruptedException e2) {
                                    FdPcapLogger.logger.error("PCAP日志记录器连续处理时间超过1s停顿失败！");
                                    FdPcapLogger.logger.error(FdHelper.getExceptionAsString(e2));
                                }
                                startTime = System.currentTimeMillis() + FdPcapLogger.TIME_SLEEP;
                            }
                        }
                        msg = (PcapData) FdPcapLogger.this.bl.poll();
                    }
                    if (isFdAlived()) {
                        FdPcapLogger.this.wait4Data();
                    }
                }
                FdPcapLogger.logger.info("PCAP日志记录器关闭。");
            }
        };
        this.bm = new PcapFile();
        try {
            this.bm.a(this.file);
        } catch (IOException e) {
            logger.error("PCAP日志记录器启动失败，日志文件名:" + this.file.getAbsolutePath());
            logger.error(FdHelper.getExceptionAsString(e));
            this.bm = null;
        }
        this.thread.start();
        try {
            Thread.sleep(100L);
        } catch (InterruptedException e2) {
            logger.error("PCAP日志记录器启动Sleep中断，日志文件名:" + this.file.getAbsolutePath());
            logger.error(FdHelper.getExceptionAsString(e2));
        }
    }

    @Override // com.geely.gnds.doip.client.IFdPcapLogger
    public void write(Date current, byte[] data, DoipAddress targetAddress, DoipAddress sourceAddress, boolean send) {
        PcapData d = new PcapData(current, data, targetAddress, sourceAddress, send, true);
        this.bl.add(d);
        notify4Data();
    }

    @Override // com.geely.gnds.doip.client.IFdPcapLogger
    public void reset(Date current, DoipAddress targetAddress, DoipAddress sourceAddress, String localhostName) {
        PcapData d = new PcapData(current, targetAddress, sourceAddress, localhostName);
        this.bl.add(d);
        notify4Data();
    }

    @Override // com.geely.gnds.doip.client.IFdPcapLogger
    public void writeUdpSended(Date current, byte[] data, DoipAddress targetAddress, DoipAddress sourceAddress) {
        PcapData d = new PcapData(current, data, targetAddress, sourceAddress, true, false);
        this.bl.add(d);
        notify4Data();
    }

    @Override // com.geely.gnds.doip.client.IFdPcapLogger
    public void writeUdpReceived(Date current, byte[] data, DoipAddress targetAddress, DoipAddress sourceAddress) {
        PcapData d = new PcapData(current, data, targetAddress, sourceAddress, false, false);
        this.bl.add(d);
        notify4Data();
    }

    @Override // com.geely.gnds.doip.client.IFdPcapLogger
    public void close() throws IOException {
        TesterThread testerThread = (TesterThread) SpringUtils.getBean(TesterThread.class);
        this.thread.close();
        notify4Data();
        try {
            this.thread.join();
        } catch (InterruptedException e) {
            logger.error(e.getMessage());
            logger.error(FdHelper.getExceptionAsString(e));
        }
        if (this.bm != null) {
            this.bm.close();
            if (TesterLoginUtils.isOnLine()) {
                testerThread.getPool().execute(() -> {
                    try {
                        if (this.vin != null) {
                        }
                    } catch (Exception e2) {
                        logger.error("通讯日志上传失败,失败原因->{}", e2.getMessage());
                    }
                });
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void wait4Data() {
        try {
            synchronized (this.awakeObject) {
                this.awakeObject.wait();
            }
        } catch (InterruptedException e) {
            logger.info("PCAP日志记录器沉睡失败。");
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    private void notify4Data() {
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }
}
