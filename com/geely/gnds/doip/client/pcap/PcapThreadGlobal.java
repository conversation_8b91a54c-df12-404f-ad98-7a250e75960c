package com.geely.gnds.doip.client.pcap;

import cn.hutool.system.oshi.OshiUtil;
import com.geely.gnds.tester.service.TesterConfigService;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;
import org.pcap4j.core.NotOpenException;
import org.pcap4j.core.PcapNativeException;
import org.pcap4j.core.PcapNetworkInterface;
import org.pcap4j.core.Pcaps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import oshi.hardware.NetworkIF;

@Component
/* loaded from: PcapThreadGlobal.class */
public class PcapThreadGlobal {
    private Set<PcapThread> cb = new CopyOnWriteArraySet();

    @Autowired
    private TesterConfigService cd;

    @Autowired
    private ThreadPoolTaskExecutor ce;
    private static final String cf = "system";
    private static final Logger logger = LoggerFactory.getLogger(PcapThreadGlobal.class);
    private static Map<String, PcapNetworkInterface> ca = new ConcurrentHashMap();
    private static Map<String, PcapThread2> cc = new ConcurrentHashMap();
    private static volatile boolean runing = false;

    public void stop() {
        logger.info("Pcap全局日志开始停止抓包……");
        Optional.ofNullable(this.cd.getConfig()).ifPresent(cfg -> {
            for (PcapThread pcapThread : this.cb) {
                pcapThread.stopLog();
            }
            this.cb = new CopyOnWriteArraySet();
            cc = new ConcurrentHashMap();
        });
    }

    /* JADX WARN: Finally extract failed */
    public void start() {
        if (runing) {
            return;
        }
        try {
            try {
                i();
                for (Map.Entry<String, PcapNetworkInterface> map : ca.entrySet()) {
                    try {
                        PcapNetworkInterface pcapNetworkInterface = map.getValue();
                        String name = pcapNetworkInterface.getName();
                        if (cc == null || !cc.containsKey(name)) {
                            PcapThread2 thread2 = new PcapThread2(pcapNetworkInterface, cf, null, -1, "port 13400 or port 3496", 20000L);
                            thread2.setName("gnds-pcap-plus-".concat(cf).concat("-").concat(String.valueOf(thread2.getId())));
                            this.ce.execute(thread2);
                            this.cb.add(thread2);
                            logger.info("pcapNetworkInterface的名称：{}", name);
                            cc.put(name, thread2);
                        }
                    } catch (Exception e) {
                        logger.error("pcap网卡记录失败", e);
                    }
                }
                runing = false;
            } catch (Exception e2) {
                logger.error("无法找到pcap组件，请先安装pcap", e2);
                runing = false;
            }
        } catch (Throwable th) {
            runing = false;
            throw th;
        }
    }

    private void i() throws PcapNativeException {
        if (!isNpcapInstalled()) {
            logger.warn("未安装npcap，请先安装npcap");
            return;
        }
        Map<String, String> nifMap = (Map) OshiUtil.getNetworkIFs().stream().filter(n -> {
            return n.getIfOperStatus().compareTo(NetworkIF.IfOperStatus.UP) == 0 && n.isConnectorPresent() && (n.getIfType() == 6 || n.getIfType() == 71);
        }).collect(Collectors.toMap((v0) -> {
            return v0.getMacaddr();
        }, (v0) -> {
            return v0.getName();
        }, (o, n2) -> {
            return o;
        }));
        List<PcapNetworkInterface> allDevs = Pcaps.findAllDevs();
        nifMap.forEach((k, v) -> {
            PcapNetworkInterface p = (PcapNetworkInterface) allDevs.stream().filter(x -> {
                return x.isUp() && x.isRunning();
            }).filter(x2 -> {
                return x2.getLinkLayerAddresses().stream().anyMatch(ee -> {
                    return ee.toString().equalsIgnoreCase(k);
                });
            }).findFirst().orElse(null);
            Optional.ofNullable(p).ifPresent(g -> {
                ca.put(v, g);
            });
        });
    }

    public static boolean isNpcapInstalled() {
        try {
            System.loadLibrary("wpcap");
            return true;
        } catch (UnsatisfiedLinkError e) {
            return false;
        }
    }

    public static PcapThread2 c(String name) {
        if (cc.containsKey(name)) {
            return cc.get(name);
        }
        return null;
    }

    public static void a(String name, PcapThread2 thread) {
        cc.put(name, thread);
    }

    public static PcapThread2 d(String name) {
        logger.info("开始关闭pcap线程{}", name);
        if (cc.containsKey(name)) {
            return cc.remove(name);
        }
        return null;
    }

    public static void a(String path, String vin, String username) throws PcapNativeException, NotOpenException {
        for (Map.Entry<String, PcapThread2> map : cc.entrySet()) {
            PcapThread2 pcapThread2 = map.getValue();
            pcapThread2.stopLogForGlobal(path);
            pcapThread2.setVin(vin);
            pcapThread2.setUsername(username);
            PcapWrite.b(pcapThread2);
        }
    }
}
