package com.geely.gnds.doip.client.pcap;

import java.net.InetAddress;
import java.util.Arrays;

/* loaded from: DoipAddress.class */
public class DoipAddress {
    private final InetAddress bb;
    private final int port;
    private final String ip;
    private final byte[] bc;

    public DoipAddress a(int port) {
        return new DoipAddress(this.bb, port, this.ip, this.bc);
    }

    public DoipAddress(InetAddress address, int port, String ip, byte[] mac) {
        this.bb = address;
        this.port = port;
        this.ip = ip;
        this.bc = mac;
    }

    public InetAddress getAddress() {
        return this.bb;
    }

    public int getPort() {
        return this.port;
    }

    public String getIp() {
        return this.ip;
    }

    public byte[] getMac() {
        return this.bc;
    }

    public String toString() {
        return "DoipAddress{address=" + this.bb + ", port=" + this.port + ", ip='" + this.ip + "', mac=" + Arrays.toString(this.bc) + '}';
    }
}
