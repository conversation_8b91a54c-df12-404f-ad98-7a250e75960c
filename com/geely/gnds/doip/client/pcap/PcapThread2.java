package com.geely.gnds.doip.client.pcap;

import cn.hutool.core.io.FileUtil;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.tester.common.TimeQueue;
import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;
import org.pcap4j.core.BpfProgram;
import org.pcap4j.core.NotOpenException;
import org.pcap4j.core.PcapDumper;
import org.pcap4j.core.PcapHandle;
import org.pcap4j.core.PcapNativeException;
import org.pcap4j.core.PcapNetworkInterface;
import org.pcap4j.packet.Packet;
import org.pcap4j.util.LinkLayerAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: PcapThread2.class */
public class PcapThread2 extends Thread implements PcapThread {
    private static final Logger log = LoggerFactory.getLogger(PcapThread2.class);
    private long bS;
    private File bT;
    private String bU;
    private PcapNetworkInterface bV;
    private PcapHandle bW;
    private PcapDumper bX;
    private boolean runing;
    private long bY;
    private String username;
    private String vin;
    TimeQueue<PcapPacket> queue;
    private boolean bZ;
    private volatile Boolean start;

    public void setGlobal(boolean global) {
        this.bZ = global;
    }

    public PcapThread2(PcapNetworkInterface networkInterface, String username, File pcapFie, Integer packetCount, String filter) {
        this.bS = -1L;
        this.runing = true;
        this.bY = 0L;
        this.queue = null;
        this.bZ = false;
        this.start = true;
        this.bV = networkInterface;
        this.username = username;
        this.bT = pcapFie;
        Optional.ofNullable(packetCount).ifPresent(p -> {
            this.bS = p.intValue();
        });
        this.bU = filter;
        this.bZ = false;
    }

    public PcapThread2(PcapNetworkInterface networkInterface, String username, File pcapFie, Integer packetCount, String filter, long timeout) {
        this.bS = -1L;
        this.runing = true;
        this.bY = 0L;
        this.queue = null;
        this.bZ = false;
        this.start = true;
        this.bV = networkInterface;
        this.username = username;
        this.bT = pcapFie;
        Optional.ofNullable(packetCount).ifPresent(p -> {
            this.bS = p.intValue();
        });
        this.bU = filter;
        this.queue = new TimeQueue<>(timeout);
        this.bZ = true;
    }

    @Override // com.geely.gnds.doip.client.pcap.PcapThread
    public File getPcapFile() {
        log.info("pcapFile路径{}", this.bT.getAbsolutePath());
        return this.bT;
    }

    private PcapDumper a(PcapHandle handle, File pcapFile) throws PcapNativeException, NotOpenException {
        FileUtil.mkParentDirs(pcapFile);
        PcapDumper dumper = handle.dumpOpen(pcapFile.getAbsolutePath());
        return dumper;
    }

    @Override // com.geely.gnds.doip.client.pcap.PcapThread
    public void stopLog() {
        log.info("Pcap 文件写入开始关闭>>>>>>>>");
        this.runing = false;
        if (null != this.bX && this.bX.isOpen()) {
            this.bX.close();
            log.info("Pcap 文件写入关闭 dumper 成功！！");
        }
        if (this.bW != null && this.bW.isOpen()) {
            this.bW.close();
            log.info("Pcap 文件写入关闭 handle 成功！！");
        }
        PcapThreadGlobal.d(this.bV.getName());
    }

    @Override // com.geely.gnds.doip.client.pcap.PcapThread
    public void stopLogForGlobal(String path) throws PcapNativeException, NotOpenException {
        if (this.bZ) {
            try {
                String mac = "";
                ArrayList<LinkLayerAddress> linkLayerAddresses = this.bV.getLinkLayerAddresses();
                LinkLayerAddress linkLayerAddress = linkLayerAddresses.get(0);
                if (linkLayerAddress != null) {
                    mac = DoipUtil.getInstance().macToHexString(linkLayerAddress.getAddress());
                }
                this.bT = new File(path + mac + "-global.pcap");
                h();
                this.bX = a(this.bW, this.bT);
                if (this.queue != null) {
                    this.start = false;
                    while (!this.queue.isEmpty()) {
                        log.info("开始拼接20s日志");
                        a(this.queue.poll());
                    }
                    this.start = true;
                }
            } catch (Exception e) {
                log.error("开始生成20s日志失败", e);
            }
            PcapThreadGlobal.d(this.bV.getName());
        }
    }

    @Override // java.lang.Thread, java.lang.Runnable
    public void run() throws PcapNativeException, NotOpenException {
        try {
            h();
            if (!this.bZ) {
                this.bX = a(this.bW, this.bT);
            }
            if (!this.bZ && this.queue != null) {
                this.start = false;
                while (!this.queue.isEmpty()) {
                    log.info("开始拼接20s日志");
                    a(this.queue.poll());
                }
                this.start = true;
            }
            while (this.runing) {
                if (this.bZ) {
                    Optional.ofNullable(this.bW.getNextPacket()).ifPresent(this::b);
                } else {
                    Optional.ofNullable(this.bW.getNextPacket()).ifPresent(this::a);
                }
            }
        } catch (Exception e) {
            log.error("记录pcap失败", e);
        }
    }

    private void h() throws PcapNativeException, NotOpenException {
        PcapHandle.Builder phb = new PcapHandle.Builder(this.bV.getName()).snaplen(65536).promiscuousMode(PcapNetworkInterface.PromiscuousMode.NONPROMISCUOUS).timeoutMillis(10).bufferSize(1048576);
        this.bW = phb.build();
        if (null != this.bU && !"".equalsIgnoreCase(this.bU)) {
            this.bW.setFilter(this.bU, BpfProgram.BpfCompileMode.OPTIMIZE);
        }
    }

    private void a(Packet packet) {
        try {
            if (this.bS > 0 && this.bY >= this.bS) {
                this.bY = 0L;
                this.bX.flush();
            }
            this.bY++;
            this.bX.dump(packet, this.bW.getTimestamp());
        } catch (NotOpenException | PcapNativeException e) {
            log.error("Unable to write pcap file, file cannot be opened.Exception={}", e);
            try {
                this.bW.breakLoop();
                this.bW.close();
            } catch (NotOpenException ex) {
                log.error("Pcap write loop stop fail:Exception{}", ex);
            }
        }
    }

    private void b(Packet packet) {
        try {
            if (this.start.booleanValue()) {
                this.queue.add(new PcapPacket(packet, this.bW.getTimestamp()));
            }
        } catch (Exception e) {
            log.warn("Unable to write pcap file, file cannot be opened.Exception={}", e);
            log.error("Unable to write pcap file, file cannot be opened.Exception={}", e);
        }
    }

    private void a(PcapPacket pcapPacket) {
        Timestamp timestamp = pcapPacket.getTimestamp();
        Packet packet = pcapPacket.getPacket();
        try {
            this.bX.dump(packet, timestamp);
        } catch (Exception e) {
            log.warn("Unable to write pcap file, file cannot be opened.Exception={}", e);
            log.error("Unable to write pcap file, file cannot be opened.Exception={}", e);
        }
    }

    @Override // com.geely.gnds.doip.client.pcap.PcapThread
    public String getUsername() {
        return (String) Optional.ofNullable(this.username).orElse("unkonw_user");
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    @Override // com.geely.gnds.doip.client.pcap.PcapThread
    public String getVin() {
        return (String) Optional.ofNullable(this.vin).orElse("");
    }

    public TimeQueue<PcapPacket> getQueue() {
        return this.queue;
    }

    public void setQueue(TimeQueue<PcapPacket> queue) {
        this.queue = queue;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PcapThread2 that = (PcapThread2) o;
        return this.bS == that.bS && this.runing == that.runing && this.bY == that.bY && this.bZ == that.bZ && Objects.equals(this.bT, that.bT) && Objects.equals(this.bU, that.bU) && Objects.equals(this.bV, that.bV) && Objects.equals(this.bW, that.bW) && Objects.equals(this.bX, that.bX) && Objects.equals(this.username, that.username) && Objects.equals(this.vin, that.vin) && Objects.equals(this.queue, that.queue) && Objects.equals(this.start, that.start);
    }

    public int hashCode() {
        return Objects.hash(Long.valueOf(this.bS), this.bT, this.bU, this.bV, this.bW, this.bX, Boolean.valueOf(this.runing), Long.valueOf(this.bY), this.username, this.vin, this.queue, Boolean.valueOf(this.bZ), this.start);
    }
}
