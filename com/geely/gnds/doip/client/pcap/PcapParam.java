package com.geely.gnds.doip.client.pcap;

import com.geely.gnds.tester.enums.ConstantEnum;
import java.io.Serializable;
import org.springframework.util.Assert;

/* loaded from: PcapParam.class */
public class PcapParam implements Serializable {
    private String path;
    private String vin;
    private String username;
    private String bR;

    private PcapParam() {
    }

    public PcapParam(String path, String vin, String username, String start) {
        this.path = path;
        this.vin = vin;
        this.username = username;
        this.bR = start;
        Assert.hasText("path", "参数 path 不能空");
        Assert.hasText("vin", "参数 vin 不能空");
        Assert.hasText(ConstantEnum.USERNAME_STR, "参数 username 不能空");
        Assert.hasText("start", "参数 start 不能空");
    }

    public String getPath() {
        return this.path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getVin() {
        return this.vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getUsername() {
        return this.username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getStart() {
        return this.bR;
    }

    public void setStart(String start) {
        this.bR = start;
    }
}
