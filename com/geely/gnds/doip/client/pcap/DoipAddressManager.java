package com.geely.gnds.doip.client.pcap;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.hardware.NetworkIF;

/* loaded from: DoipAddressManager.class */
public class DoipAddressManager {
    private static final int bh = 100;
    private static final String bi = "; ";
    public static final String UNKNOWN = "unknown";
    private static final Logger logger = LoggerFactory.getLogger(DoipAddressManager.class);
    private static DoipAddressManager bg = new DoipAddressManager();
    private Map<String, DoipAddress> bd = new HashMap(5);
    private Map<String, DoipAddress> be = new HashMap(5);
    private DoipAddress bf = null;
    private DoipAddress local = null;
    private DoipUtil util = DoipUtil.getInstance();
    private List<NetworkIF> bj = null;
    private List<InetAddress> bk = null;

    public List<InetAddress> getCacheInetAddress() {
        return this.bk;
    }

    public void setCacheInetAddress(List<InetAddress> cacheInetAddress) {
        this.bk = cacheInetAddress;
    }

    private DoipAddressManager() {
    }

    public static DoipAddressManager getInstance() {
        return bg;
    }

    public DoipAddress b(String ip) {
        DoipAddress me = this.bd.get(ip);
        return me == null ? this.bf : me;
    }

    public DoipAddress getSelectedLocalDoipAddress() throws UnknownHostException {
        if (this.local == null) {
            try {
                InetAddress localhost = InetAddress.getByName(ConstantEnum.LOCAL);
                this.local = new DoipAddress(localhost, 0, localhost.getHostAddress(), this.util.MAC_LOCAL);
            } catch (UnknownHostException e) {
                logger.error(e.getMessage());
            }
        }
        if (this.bf == null) {
            return this.local;
        }
        return this.bf;
    }

    public String getIps() {
        List<DoipAddress> list = new ArrayList<>();
        Collection<DoipAddress> doipAddresses = getDoipAddresses();
        Iterator<DoipAddress> it = doipAddresses.iterator();
        while (it.hasNext()) {
            list.add(it.next());
        }
        StringBuffer sb = new StringBuffer("本地网卡的IP地址：");
        boolean isFirst = true;
        for (DoipAddress da : list) {
            if (!ConstantEnum.LOCAL.equals(da.getIp())) {
                if (isFirst) {
                    isFirst = false;
                } else {
                    sb.append(ConstantEnum.COMMA);
                }
                sb.append(da.getIp());
            }
        }
        return sb.toString();
    }

    private void a(String ip, DoipAddress doipAddress) {
        if (this.bf == null && !ConstantEnum.LOCAL.equals(ip)) {
            this.bf = doipAddress;
        }
        this.bd.put(ip, doipAddress);
    }

    public Collection<DoipAddress> getDoipAddresses() {
        return Collections.unmodifiableCollection(this.bd.values());
    }

    public synchronized void setNetList(List<NetworkIF> nets) {
        this.bj = nets;
    }

    private synchronized List<NetworkIF> getNetList() {
        return this.bj;
    }

    public List<NetworkIF> getInetAddress() throws UnknownHostException {
        logger.info("主动搜车开始获取网卡");
        List<NetworkIF> networkList = new ArrayList<>();
        try {
            SystemInfo si = new SystemInfo();
            HardwareAbstractionLayer hw = si.getHardware();
            getInstance().setNetList(hw.getNetworkIFs());
            logger.info("主动搜车初始化网卡结束");
            List<InetAddress> list = new ArrayList<>();
            List<NetworkIF> networkIfList = getNetList();
            if (networkIfList == null) {
                networkIfList = new SystemInfo().getHardware().getNetworkIFs(true);
            }
            Map<NetworkIF, Integer> intfSortValueMap = new HashMap<>(100);
            for (NetworkIF intf : networkIfList) {
                intfSortValueMap.put(intf, Integer.valueOf(intf.getIndex()));
            }
            List<Map.Entry<NetworkIF, Integer>> intfList = new ArrayList<>(intfSortValueMap.entrySet());
            intfList.sort(Map.Entry.comparingByValue());
            for (Map.Entry<NetworkIF, Integer> e : intfList) {
                NetworkIF intf2 = e.getKey();
                String ip = a(intf2.getIPv4addr());
                if (ip.trim().length() > 0) {
                    InetAddress ia = InetAddress.getByName(ip);
                    list.add(ia);
                    networkList.add(intf2);
                }
            }
            logger.info("主动搜车获取网卡结束{}", list);
            setCacheInetAddress(list);
        } catch (Exception e2) {
            logger.error("主动搜车获取网卡异常", e2);
        }
        return networkList;
    }

    public List<NetworkIF> getInetAddressList() {
        List<NetworkIF> networkIfList = new ArrayList();
        logger.info("主动搜车开始获取网卡");
        try {
            SystemInfo si = new SystemInfo();
            HardwareAbstractionLayer hw = si.getHardware();
            getInstance().setNetList(hw.getNetworkIFs());
            networkIfList = getNetList();
        } catch (Exception e) {
            logger.error("获取网卡异常", e);
        }
        return networkIfList;
    }

    public void init() throws SocketException, UnknownHostException {
        byte[] mac;
        List<NetworkIF> networkIfList = getNetList();
        if (networkIfList == null) {
            networkIfList = new SystemInfo().getHardware().getNetworkIFs(true);
        }
        Map<NetworkIF, Integer> intfSortValueMap = new HashMap<>(100);
        for (NetworkIF intf : networkIfList) {
            intfSortValueMap.put(intf, Integer.valueOf(intf.getIndex()));
        }
        List<Map.Entry<NetworkIF, Integer>> intfList = new ArrayList<>(intfSortValueMap.entrySet());
        intfList.sort(Map.Entry.comparingByValue());
        for (Map.Entry<NetworkIF, Integer> e : intfList) {
            NetworkIF intf2 = e.getKey();
            String netName = intf2.getName();
            try {
                String ip = a(intf2.getIPv4addr());
                if (ip.trim().length() > 0) {
                    InetAddress ia = InetAddress.getByName(ip);
                    NetworkInterface ni = NetworkInterface.getByInetAddress(ia);
                    List<InterfaceAddress> list = ni.getInterfaceAddresses();
                    StringBuilder maskStr = new StringBuilder();
                    if (list.size() > 0) {
                        int mask = list.get(0).getNetworkPrefixLength();
                        int[] maskIp = new int[4];
                        for (int i = 0; i < maskIp.length; i++) {
                            maskIp[i] = mask >= 8 ? 255 : mask > 0 ? mask & 255 : 0;
                            mask -= 8;
                            maskStr.append(maskIp[i]);
                            if (i < maskIp.length - 1) {
                                maskStr.append(bi);
                            }
                        }
                    }
                    if (!ConstantEnum.LOCAL.equals(ip)) {
                        mac = ni.getHardwareAddress();
                        logger.info("查到本地网卡IP={},MAC={},子网掩码={}", new Object[]{ip, intf2.getMacaddr(), maskStr});
                    } else {
                        mac = this.util.MAC_LOCAL;
                        logger.info("查到回环IP={}", ip);
                    }
                    if (mac != null && !netName.toLowerCase().contains(ConstantEnum.WLAN)) {
                        logger.info("连车网卡={}", ip);
                        a(ip, new DoipAddress(ia, 0, ip, mac));
                    }
                }
            } catch (Exception ex) {
                logger.error(ex.getMessage());
            }
        }
    }

    private static String a(String[] ipAddressArr) {
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (String ipAddress : ipAddressArr) {
            if (first) {
                first = false;
            } else {
                sb.append(bi);
            }
            sb.append(ipAddress);
        }
        return sb.toString();
    }
}
