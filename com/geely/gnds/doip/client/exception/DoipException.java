package com.geely.gnds.doip.client.exception;

/* loaded from: DoipException.class */
public class <PERSON><PERSON>Exception extends Exception {
    private static final long serialVersionUID = -7058151104436249307L;

    public DoipException() {
    }

    public DoipException(String string) {
        super(string);
    }

    public DoipException(String string, Throwable cause) {
        super(string, cause);
    }

    public DoipException(HeaderTooShort e) {
        super(e.getMessage(), e);
    }

    public DoipException(IncorrectPatternFormat e) {
        super("Protocol Version is incorrect", e);
    }

    public DoipException(InvalidPayloadLength e) {
        super("Payload length is invalid", e);
    }

    public DoipException(InvalidPayloadType e) {
        super("Payload type is invalid", e);
    }
}
