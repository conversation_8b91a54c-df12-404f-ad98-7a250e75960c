package com.geely.gnds.doip.client;

import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.tester.enums.ConstantEnum;
import java.util.Iterator;
import java.util.LinkedList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoipTcpStreamBuffer.class */
public class DoipTcpStreamBuffer extends StreamBuffer {
    private static final Logger logger = LoggerFactory.getLogger(DoipTcpStreamBuffer.class);
    public static final int STATE_HEADER_NOT_COMPLETED = 1;
    public static final int STATE_PAYLOAD_NOT_COMPLETED = 2;
    public static final int STATE_SHREDDER_NOT_COMPLETED = 3;
    LinkedList<DoipTcpStreamBufferListener> listeners = new LinkedList<>();
    private long maxPayloadLength = 4294967295L;
    private int payloadType = -1;
    private long payloadLength = -1;
    private long numberOfRemainingBytesToShredder = 0;
    private byte[] lastHeader = null;
    private int state = 1;
    private final int[] payloadTypeCollection = {0, 5, 6, 7, 8, AbstractDoipMessage.av, AbstractDoipMessage.aw, AbstractDoipMessage.ax};

    public void addListener(DoipTcpStreamBufferListener listener) {
        logger.trace(">>> public void addListener(DoipStreamBufferListener listener)");
        this.listeners.add(listener);
        logger.trace("<<< public void addListener(DoipStreamBufferListener listener)");
    }

    /* JADX WARN: Incorrect condition in loop: B:4:0x0036 */
    @Override // com.geely.gnds.doip.client.StreamBuffer
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public void append(byte[] r5) {
        /*
            r4 = this;
            org.slf4j.Logger r0 = com.geely.gnds.doip.client.DoipTcpStreamBuffer.logger
            java.lang.String r1 = ">>> void append(byte[] newData)"
            r0.trace(r1)
            org.slf4j.Logger r0 = com.geely.gnds.doip.client.DoipTcpStreamBuffer.logger
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r2 = r1
            r2.<init>()
            java.lang.String r2 = "Append "
            java.lang.StringBuilder r1 = r1.append(r2)
            r2 = r5
            int r2 = r2.length
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r2 = " bytes to the buffer"
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r1 = r1.toString()
            r0.debug(r1)
            r0 = r4
            r1 = r5
            super.append(r1)
            r0 = r4
            boolean r0 = r0.processBuffer()
            r6 = r0
        L35:
            r0 = r6
            if (r0 == 0) goto L64
            org.slf4j.Logger r0 = com.geely.gnds.doip.client.DoipTcpStreamBuffer.logger
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r2 = r1
            r2.<init>()
            java.lang.String r2 = "After processing the buffer there are still "
            java.lang.StringBuilder r1 = r1.append(r2)
            r2 = r4
            int r2 = r2.getLength()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r2 = " bytes in the buffer which needs to get processed. Calling processBuffer() again."
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r1 = r1.toString()
            r0.debug(r1)
            r0 = r4
            boolean r0 = r0.processBuffer()
            r6 = r0
            goto L35
        L64:
            org.slf4j.Logger r0 = com.geely.gnds.doip.client.DoipTcpStreamBuffer.logger
            java.lang.String r1 = "<<< void append(byte[] newData)"
            r0.trace(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.doip.client.DoipTcpStreamBuffer.append(byte[]):void");
    }

    public boolean checkPayloadType(int payloadType) {
        for (int i : this.payloadTypeCollection) {
            if (i == payloadType) {
                return true;
            }
        }
        return false;
    }

    public boolean checkPayloadTypeSpecificLength() {
        logger.trace(">>> boolean checkPayloadTypeSpecificLength()");
        boolean ret = false;
        switch (this.payloadType) {
            case 0:
                if (this.payloadLength == 1) {
                    ret = true;
                    break;
                }
                break;
            case 5:
                if (this.payloadLength == ConstantEnum.SEVEN.intValue() || this.payloadLength == ConstantEnum.ELEVEN.intValue()) {
                    ret = true;
                    break;
                }
                break;
            case 6:
                if (this.payloadLength == ConstantEnum.NINE.intValue() || this.payloadLength == ConstantEnum.THIRTEEN.intValue()) {
                    ret = true;
                    break;
                }
                break;
            case 7:
                if (this.payloadLength == 0) {
                    ret = true;
                    break;
                }
                break;
            case 8:
                if (this.payloadLength == ConstantEnum.TWO.intValue()) {
                    ret = true;
                    break;
                }
                break;
            case AbstractDoipMessage.av /* 32769 */:
                if (this.payloadLength >= ConstantEnum.FORE.intValue()) {
                    ret = true;
                    break;
                }
                break;
            case AbstractDoipMessage.aw /* 32770 */:
                if (this.payloadLength >= ConstantEnum.FIVE.intValue()) {
                    ret = true;
                    break;
                }
                break;
            case AbstractDoipMessage.ax /* 32771 */:
                if (this.payloadLength >= ConstantEnum.FIVE.intValue()) {
                    ret = true;
                    break;
                }
                break;
        }
        logger.trace("<<< boolean checkPayloadTypeSpecificLength()");
        return ret;
    }

    public boolean checkSyncPattern(byte[] data) {
        logger.trace(">>> boolean checkSyncPattern(byte[] data)");
        int protocolVersion = data[0] & 255;
        int inverseProtocolVersion = data[1] & 255;
        int xorProtocolVersion = protocolVersion ^ 255;
        if (xorProtocolVersion != inverseProtocolVersion) {
            logger.info("Invalid sync pattern");
            logger.trace("<<< boolean checkSyncPattern(byte[] data)");
            return false;
        }
        logger.trace("<<< boolean checkSyncPattern(byte[] data)");
        return true;
    }

    public long getMaxPayloadLength() {
        return this.maxPayloadLength;
    }

    public int getState() {
        return this.state;
    }

    public void handleTcpHeader() {
        logger.trace(">>> void handleTcpHeader()");
        if (this.state != 1) {
            throw new IllegalStateException("void handleTcpHeader() had been called in illegal state STATE_HEADER_NOT_COMPLETE");
        }
        byte[] data = getData();
        logger.debug("Parse TCP message ...");
        logger.debug("\tMessage Length           = " + data.length);
        if (data.length < ConstantEnum.EIGHT.intValue()) {
            throw new IllegalStateException("void handleTcpHeader() had been called, bute there are less than 8 bytes in the buffer");
        }
        if (!checkSyncPattern(data)) {
            logger.warn("Invalid sync pattern");
            onHeaderIncorrectPatternFormat();
            clear();
            logger.trace("<<< void handleTcpHeader()");
            return;
        }
        int high = data[2] & 255;
        int low = data[3] & 255;
        this.payloadType = (high << 8) | low;
        String text = AbstractDoipMessage.getPayloadTypeAsString(this.payloadType);
        if (text == null) {
            text = "???";
        }
        logger.debug("\tPayload Type             = " + String.format("0x%04X", Integer.valueOf(this.payloadType)) + ": " + text);
        if (handle(data)) {
            return;
        }
        logger.debug("Remove header (8 bytes)");
        this.lastHeader = remove(8);
        if (this.payloadLength == 0) {
            logger.info("Payload completed");
            onPayloadCompleted(this.payloadType, new byte[0]);
        } else {
            this.state = 2;
        }
        logger.debug("After removing header there are " + getLength() + " bytes in the buffer");
        logger.trace("<<< void handleTcpHeader()");
    }

    private boolean handle(byte[] data) {
        long highhigh = data[4] & 255;
        long highlow = data[5] & 255;
        long lowhigh = data[6] & 255;
        long lowlow = data[7] & 255;
        this.payloadLength = (highhigh << 24) | (highlow << 16) | (lowhigh << 8) | lowlow;
        logger.debug("\tPayload Length in Header = " + this.payloadLength);
        logger.debug("\thighhigh << 24 = " + (highhigh << 24));
        logger.debug("\t(highlow << 16) = " + (highlow << 16));
        logger.debug("\tlowhigh << 8 = " + (lowhigh << 8));
        logger.debug("\tlowlow = " + lowlow);
        logger.debug("\t" + DoipUtil.getInstance().toHexString(data));
        if (this.payloadLength == 431) {
            this.payloadLength = 289L;
            data[4] = 0;
            data[5] = 0;
            data[6] = 1;
            data[7] = 33;
            long highhigh1 = data[4] & 255;
            long highlow1 = data[5] & 255;
            long lowhigh1 = data[6] & 255;
            long lowlow1 = data[7] & 255;
            this.payloadLength = (highhigh1 << 24) | (highlow1 << 16) | (lowhigh1 << 8) | lowlow1;
            logger.debug("\thighhigh << 24 = " + (highhigh1 << 24));
            logger.debug("\t(highlow << 16) = " + (highlow1 << 16));
            logger.debug("\tlowhigh << 8 = " + (lowhigh1 << 8));
            logger.debug("\tlowlow = " + lowlow1);
            logger.debug("\tPayload Length in Header = " + this.payloadLength);
            logger.debug("\t" + DoipUtil.getInstance().toHexString(data));
        }
        if (!checkPayloadType(this.payloadType)) {
            logger.warn("Invalid payload type");
            onHeaderUnknownPayloadType();
            this.state = 3;
            this.numberOfRemainingBytesToShredder = this.payloadLength;
            this.lastHeader = remove(8);
            logger.trace("<<< void handleTcpHeader()");
            return true;
        }
        if (this.payloadLength > this.maxPayloadLength) {
            logger.warn("Payload length (= " + this.payloadLength + ") exceeds max payload length (= " + this.maxPayloadLength + ")");
            onHeaderMessageTooLarge();
            this.state = 3;
            this.numberOfRemainingBytesToShredder = this.payloadLength;
            this.lastHeader = remove(8);
            logger.trace("<<< void handleTcpHeader()");
            return true;
        }
        if (!checkPayloadTypeSpecificLength()) {
            logger.warn("Invalid payload length");
            onHeaderInvalidPayloadLength();
            clear();
            logger.trace("<<< void handleTcpHeader()");
            return true;
        }
        return false;
    }

    private void onHeaderIncorrectPatternFormat() {
        logger.trace(">>> private void onHeaderIncorrectPatternFormat()");
        Iterator<DoipTcpStreamBufferListener> iter = this.listeners.iterator();
        while (iter.hasNext()) {
            DoipTcpStreamBufferListener listener = iter.next();
            listener.onHeaderIncorrectPatternFormat();
        }
        logger.trace("<<< private void onHeaderIncorrectPatternFormat()");
    }

    private void onHeaderInvalidPayloadLength() {
        logger.trace("private void onHeaderInvalidPayloadLength()");
        Iterator<DoipTcpStreamBufferListener> iter = this.listeners.iterator();
        while (iter.hasNext()) {
            DoipTcpStreamBufferListener listener = iter.next();
            listener.onHeaderInvalidPayloadLength();
        }
        logger.trace("<<< private void onHeaderInvalidPayloadLength()");
    }

    private void onHeaderMessageTooLarge() {
        logger.trace(">>> private void onHeaderMessageTooLarge()");
        Iterator<DoipTcpStreamBufferListener> iter = this.listeners.iterator();
        while (iter.hasNext()) {
            DoipTcpStreamBufferListener listener = iter.next();
            listener.onHeaderMessageTooLarge();
        }
        logger.trace("<<< private void onHeaderMessageTooLarge()");
    }

    private void onHeaderUnknownPayloadType() {
        logger.trace(">>> private void onHeaderUnknownPayloadType()");
        Iterator<DoipTcpStreamBufferListener> iter = this.listeners.iterator();
        while (iter.hasNext()) {
            DoipTcpStreamBufferListener listener = iter.next();
            listener.onHeaderUnknownPayloadType();
        }
        logger.trace("private void onHeaderUnknownPayloadType()");
    }

    private void onPayloadCompleted(int payloadType, byte[] data) {
        Iterator<DoipTcpStreamBufferListener> iter = this.listeners.iterator();
        while (iter.hasNext()) {
            DoipTcpStreamBufferListener listener = iter.next();
            listener.onPayloadCompleted(this.lastHeader, payloadType, data);
        }
    }

    public boolean processBuffer() {
        boolean ret;
        logger.trace(">>> boolean processBuffer()");
        switch (this.state) {
            case 1:
                logger.debug("Process buffer in state STATE_HEADER_NOT_COMPLETED");
                ret = processBufferInStateHeaderNotCompleted();
                break;
            case 2:
                logger.debug("Process buffer in state STATE_PAYLOAD_NOT_COMPLETED");
                ret = processBufferInStatePayloadNotCompleted();
                break;
            case 3:
                logger.debug("Process buffer in state STATE_SHREDDER_NOT_COMPLETED");
                ret = processBufferInStateShredderNotCompleted();
                break;
            default:
                throw new IllegalStateException();
        }
        logger.trace("<<< boolean processBuffer()");
        return ret;
    }

    public boolean processBufferInStateHeaderNotCompleted() {
        logger.trace(">>> boolean processBufferInStateHeaderNotCompleted()");
        boolean ret = false;
        if (getLength() >= ConstantEnum.EIGHT.intValue()) {
            handleTcpHeader();
            if (getLength() > 0) {
                ret = true;
            }
        }
        logger.trace("<<< boolean processBufferInStateHeaderNotCompleted()");
        return ret;
    }

    public boolean processBufferInStatePayloadNotCompleted() {
        logger.trace(">>> boolean processBufferInStatePayloadNotCompleted()");
        boolean ret = false;
        byte[] data = getData();
        if (this.payloadLength == data.length) {
            clear();
            onPayloadCompleted(this.payloadType, data);
            this.state = 1;
        } else if (this.payloadLength < data.length) {
            byte[] payload = remove((int) this.payloadLength);
            onPayloadCompleted(this.payloadType, payload);
            this.state = 1;
            ret = true;
        }
        logger.trace("<<< boolean processBufferInStatePayloadNotCompleted()");
        return ret;
    }

    private void onShredderCompleted(long payloadLength) {
        Iterator<DoipTcpStreamBufferListener> iter = this.listeners.iterator();
        while (iter.hasNext()) {
            DoipTcpStreamBufferListener listener = iter.next();
            listener.onShredderCompleted(this.payloadLength);
        }
    }

    public boolean processBufferInStateShredderNotCompleted() {
        logger.trace(">>> boolean processBufferInStateShredderNotCompleted()");
        byte[] data = getData();
        if (data.length > this.numberOfRemainingBytesToShredder) {
            remove((int) this.numberOfRemainingBytesToShredder);
            this.numberOfRemainingBytesToShredder = 0L;
            this.state = 1;
            onShredderCompleted(this.payloadLength);
            logger.trace("<<< boolean processBufferInStateShredderNotCompleted()");
            return true;
        }
        if (data.length == this.numberOfRemainingBytesToShredder) {
            clear();
            this.numberOfRemainingBytesToShredder = 0L;
            this.state = 1;
            onShredderCompleted(this.payloadLength);
            logger.trace("<<< boolean processBufferInStateShredderNotCompleted()");
            return false;
        }
        clear();
        this.numberOfRemainingBytesToShredder -= data.length;
        logger.trace("<<< boolean processBufferInStateShredderNotCompleted()");
        return false;
    }

    public void removeListener(DoipTcpStreamBufferListener listener) {
        logger.trace(">>> public void removeListener(DoipStreamBufferListener listener)");
        this.listeners.remove(listener);
        logger.trace("<<< public void removeListener(DoipStreamBufferListener listener)");
    }

    public void setMaxPayloadLength(long maxPayloadLength) {
        this.maxPayloadLength = maxPayloadLength;
    }
}
