package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.FdThread;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessageNegAck;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessagePosAck;
import com.geely.gnds.doip.client.txt.FdTxtLogger;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import java.net.Socket;
import java.util.Date;
import java.util.Iterator;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CopyOnWriteArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: DoIpMessageDispatcher.class */
public class DoIpMessageDispatcher {
    private FdThread thread;
    private FdTxtLogger txtLogger;
    private static final Logger LOGGER = LoggerFactory.getLogger(DoIpMessageDispatcher.class);
    private static DoipUtil util = DoipUtil.getInstance();
    private CopyOnWriteArrayList<FdDoipTcpReceiveListener> listeners = new CopyOnWriteArrayList<>();
    private Object lock = new Object();
    private ConcurrentLinkedQueue<FdDispatchMessage> bl = new ConcurrentLinkedQueue<>();
    private Object awakeObject = new Object();
    private ConcurrentLinkedQueue<FdDoipTcpReceiveListener> cg = new ConcurrentLinkedQueue<>();

    public FdDoipTcpReceiveListener getIdleReceiveListener() {
        FdDoipTcpReceiveListener fdDoipTcpReceiveListener = this.cg.poll();
        if (fdDoipTcpReceiveListener == null) {
            fdDoipTcpReceiveListener = new FdDoipTcpReceiveListener();
        }
        return fdDoipTcpReceiveListener;
    }

    public DoIpMessageDispatcher(String threadName, FdTxtLogger txtLogger) throws InterruptedException {
        this.thread = null;
        this.txtLogger = txtLogger;
        FdDoipTcpReceiveListener fdDoipTcpReceiveListener = new FdDoipTcpReceiveListener();
        this.cg.add(fdDoipTcpReceiveListener);
        this.thread = new FdThread(threadName) { // from class: com.geely.gnds.doip.client.tcp.DoIpMessageDispatcher.1
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                DoIpMessageDispatcher.LOGGER.info("DoIP消息调度器启动完成。");
                while (isFdAlived()) {
                    FdDispatchMessage msg = (FdDispatchMessage) DoIpMessageDispatcher.this.bl.poll();
                    while (msg != null && isFdAlived()) {
                        try {
                            DoIpMessageDispatcher.this.b(msg.getSocket(), msg.getMessage());
                            msg = (FdDispatchMessage) DoIpMessageDispatcher.this.bl.poll();
                        } catch (Exception e) {
                            DoIpMessageDispatcher.LOGGER.error("消费一条DoipMessage message异常", e);
                        }
                    }
                    if (isFdAlived()) {
                        DoIpMessageDispatcher.this.wait4Data();
                    }
                }
                DoIpMessageDispatcher.LOGGER.info("DoIP消息调度器关闭。");
            }
        };
        this.thread.start();
        try {
            Thread.sleep(100L);
        } catch (InterruptedException e) {
            LOGGER.info("DoIP消息调度器启动失败。");
            LOGGER.error(FdHelper.getExceptionAsString(e));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void wait4Data() {
        try {
            synchronized (this.awakeObject) {
                this.awakeObject.wait();
            }
        } catch (InterruptedException e) {
            LOGGER.info("DoIP消息调度器沉睡失败。");
            LOGGER.error(FdHelper.getExceptionAsString(e));
        }
    }

    private void notify4Data() {
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }

    public void close() {
        cancelByUserException();
        this.thread.close();
        notify4Data();
    }

    public void a(FdDoipTcpReceiveListener listenser) {
        listenser.setDispatcher(this);
        synchronized (this.lock) {
            this.listeners.add(listenser);
        }
    }

    public void b(FdDoipTcpReceiveListener listenser) {
        synchronized (this.lock) {
            this.listeners.remove(listenser);
        }
        listenser.setDispatcher(null);
        if (listenser.isRecycled()) {
            this.cg.add(listenser);
        }
    }

    public void a(Socket socket, AbstractDoipMessage message) {
        this.bl.add(new FdDispatchMessage(socket, message));
        notify4Data();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void b(Socket socket, AbstractDoipMessage message) throws Exception {
        Iterator<FdDoipTcpReceiveListener> iter;
        LOGGER.info("准备消费消息：" + util.byteArrayToHexString(message.getMessage()));
        synchronized (this.lock) {
            iter = this.listeners.iterator();
        }
        a(message);
        while (iter.hasNext()) {
            FdDoipTcpReceiveListener listener = iter.next();
            if (listener.isMyResponseAdress(message)) {
                if (message instanceof DoipTcpDiagnosticMessagePosAck) {
                    listener.handlePosAckRes(message);
                } else if (message instanceof DoipTcpDiagnosticMessageNegAck) {
                    listener.handleNegAckRes(socket, message);
                } else if (message instanceof DoipTcpDiagnosticMessage) {
                    if (listener.isNegativeMessage(message)) {
                        if (listener instanceof FdFunctionalAddressingReceiveListener) {
                            if (listener.isDelayMessage(message)) {
                                listener.dealDelayMessage(message);
                            } else {
                                listener.handleNegativeRes(message);
                                listener.onDoipTcpMessage(socket, message);
                            }
                        } else if (listener.isDelayMessage(message)) {
                            listener.dealDelayMessage(message);
                        } else if (listener.isRetriesMessage(message)) {
                            listener.setRetryException(message);
                        } else {
                            listener.handleNegativeRes(message);
                        }
                    } else if (listener.isMyResponse(message)) {
                        listener.onDoipTcpMessage(socket, message);
                    } else {
                        listener.debugIgnore(message);
                    }
                } else if (listener.isMyResponse(message)) {
                    listener.onDoipTcpMessage(socket, message);
                } else {
                    listener.debugIgnore(message);
                }
            }
        }
        try {
            Thread.sleep(1L);
        } catch (InterruptedException e) {
        }
    }

    public void cancelByUserException() {
        Iterator<FdDoipTcpReceiveListener> iter;
        synchronized (this.lock) {
            iter = this.listeners.iterator();
        }
        while (iter.hasNext()) {
            FdDoipTcpReceiveListener listener = iter.next();
            listener.cancelByUserException();
        }
    }

    public void closeVehicle() {
        Iterator<FdDoipTcpReceiveListener> iter;
        synchronized (this.lock) {
            iter = this.listeners.iterator();
        }
        while (iter.hasNext()) {
            FdDoipTcpReceiveListener listener = iter.next();
            listener.closeVehicle();
        }
    }

    private void a(AbstractDoipMessage message) {
        DoipUtil instance = DoipUtil.getInstance();
        if (this.txtLogger != null && (message instanceof DoipTcpDiagnosticMessage)) {
            byte[] mesData = message.getMessage();
            String messageStr = DoipUtil.getInstance().bytesToHexString2(mesData, mesData.length);
            String content = "DOIP response:Message" + (0 != 0 ? "(截取前100位字符)" : ConstantEnum.EMPTY) + messageStr;
            this.txtLogger.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
            String messageStr2 = messageStr.length() <= 24 ? "未处理的" + messageStr : messageStr.substring(24);
            String sourceAddressHex = instance.getSourceAddressHex(mesData);
            if (this.txtLogger != null) {
                String content2 = "VehComm  response: Ecu " + sourceAddressHex + ",Message" + (0 != 0 ? "(截取前100位字符)" : ConstantEnum.EMPTY) + messageStr2;
                this.txtLogger.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", content2.getBytes());
            }
        }
    }
}
