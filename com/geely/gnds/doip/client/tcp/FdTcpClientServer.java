package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.DoipInstructionType;
import com.geely.gnds.doip.client.DoipTcpStreamBuffer;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.FdThread;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessage;
import com.geely.gnds.doip.client.pcap.DoipAddress;
import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.dsa.enums.DsaAddressConst;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.IQopUdsDataQueuer;
import com.geely.gnds.tester.util.MessageUtils;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/*  JADX ERROR: NullPointerException in pass: ClassModifier
    java.lang.NullPointerException: Cannot invoke "java.util.List.forEach(java.util.function.Consumer)" because "blocks" is null
    	at jadx.core.utils.BlockUtils.collectAllInsns(BlockUtils.java:1029)
    	at jadx.core.dex.visitors.ClassModifier.removeBridgeMethod(ClassModifier.java:245)
    	at jadx.core.dex.visitors.ClassModifier.removeSyntheticMethods(ClassModifier.java:160)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.ClassModifier.visit(ClassModifier.java:65)
    */
/* loaded from: FdTcpClientServer.class */
public class FdTcpClientServer {
    public FdTcpClient fdTcpClient;
    private String address;
    private static final String ERROR_PROP_VALUE = "向目标地址({0})发送的指令{1}的属性({2})的值({3})的格式错误！";
    private static final String MSG_RECEIVE_DATA = "诊断仪发出的{0}指令({1})准备接收车辆回复消息！";
    private static final String MSG_CLOSE = "关闭与车辆({0}:{1})的连接！";
    private static final String MSG_AWAKE = "诊断仪于{1}被唤醒！设定的自动自动时间戳为{0}。唤醒后停留50ms，并于{2}继续！";
    private volatile Boolean sendLock;
    private Integer platformCode;
    private static final int MAX_THREAD_NUM = 60000;
    private static final int TESTER_PRESENT_SLEEP = 2000;
    private String serverHostName;
    private int instanceCounter;
    private final DoipUtil util;
    private String prefixThreadName;
    private String vin;
    private int sourceAddress;
    private volatile long testerPresentNext;
    private volatile Socket socket;
    private volatile FdThread testerPresentThread;
    public volatile DoIpMessageDispatcher messageDispatcher;
    private volatile FdThread receiverThread;
    private volatile long sendLastTime;
    private DoipAddress local;
    private DoipAddress vehicle;
    private boolean testerTimeOut;
    private DoipUtil doipUtil;
    private String username;
    private AtomicInteger retryCount;
    private VehicleDto vehicleDto;
    private static final Logger logger = LoggerFactory.getLogger(FdTcpClientServer.class);
    private static final DoipAddressManager ADDRESS_MANAGER = DoipAddressManager.getInstance();
    private static int retryCountMax = 450;

    /*  JADX ERROR: Failed to decode insn: 0x0002: MOVE_MULTI
        java.lang.ArrayIndexOutOfBoundsException: arraycopy: source index -1 out of bounds for object array[6]
        	at java.base/java.lang.System.arraycopy(Native Method)
        	at jadx.plugins.input.java.data.code.StackState.insert(StackState.java:52)
        	at jadx.plugins.input.java.data.code.CodeDecodeState.insert(CodeDecodeState.java:137)
        	at jadx.plugins.input.java.data.code.JavaInsnsRegister.dup2x1(JavaInsnsRegister.java:313)
        	at jadx.plugins.input.java.data.code.JavaInsnData.decode(JavaInsnData.java:46)
        	at jadx.core.dex.instructions.InsnDecoder.lambda$process$0(InsnDecoder.java:50)
        	at jadx.plugins.input.java.data.code.JavaCodeReader.visitInstructions(JavaCodeReader.java:85)
        	at jadx.core.dex.instructions.InsnDecoder.process(InsnDecoder.java:46)
        	at jadx.core.dex.nodes.MethodNode.load(MethodNode.java:158)
        	at jadx.core.dex.nodes.ClassNode.load(ClassNode.java:458)
        	at jadx.core.ProcessClass.process(ProcessClass.java:69)
        	at jadx.core.ProcessClass.generateCode(ProcessClass.java:109)
        	at jadx.core.dex.nodes.ClassNode.generateClassCode(ClassNode.java:401)
        	at jadx.core.dex.nodes.ClassNode.decompile(ClassNode.java:389)
        	at jadx.core.dex.nodes.ClassNode.decompile(ClassNode.java:309)
        */
    static /* synthetic */ long a(com.geely.gnds.doip.client.tcp.FdTcpClientServer r6, long r7) {
        /*
            r0 = r6
            r1 = r7
            // decode failed: arraycopy: source index -1 out of bounds for object array[6]
            r0.testerPresentNext = r1
            return r-1
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.doip.client.tcp.FdTcpClientServer.a(com.geely.gnds.doip.client.tcp.FdTcpClientServer, long):long");
    }

    static {
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getPlatformCode() {
        return this.platformCode;
    }

    public void setPlatformCode(Integer platformCode) {
        this.platformCode = platformCode;
    }

    public void setServerHostName(String ip) {
        this.serverHostName = ip;
    }

    public Socket getSocket() {
        return this.socket;
    }

    public VehicleDto getVehicleDto() {
        return this.vehicleDto;
    }

    public void setVehicleDto(VehicleDto vehicleDto) {
        this.vehicleDto = vehicleDto;
    }

    public void connectDoip(String vin, boolean flag) throws DoipException, IOException {
        this.vin = vin;
        resetSocket(flag);
    }

    public FdDoipTcpReceiveListener getIdleReceiveListener() throws DoipException {
        if (this.messageDispatcher != null) {
            return this.messageDispatcher.getIdleReceiveListener();
        }
        throw new DoipException(MessageUtils.getMessage("Vehicle connection initialization exception"));
    }

    public FdTcpClientServer() {
        this.fdTcpClient = null;
        this.sendLock = true;
        this.platformCode = 0;
        this.instanceCounter = 0;
        this.util = DoipUtil.getInstance();
        this.testerPresentNext = -1L;
        this.socket = null;
        this.testerPresentThread = null;
        this.messageDispatcher = null;
        this.receiverThread = null;
        this.sendLastTime = -1L;
        this.local = null;
        this.vehicle = null;
        this.testerTimeOut = false;
        this.doipUtil = DoipUtil.getInstance();
    }

    public FdTcpClientServer(String serverHostName, int sourceAddress, String username) {
        this.fdTcpClient = null;
        this.sendLock = true;
        this.platformCode = 0;
        this.instanceCounter = 0;
        this.util = DoipUtil.getInstance();
        this.testerPresentNext = -1L;
        this.socket = null;
        this.testerPresentThread = null;
        this.messageDispatcher = null;
        this.receiverThread = null;
        this.sendLastTime = -1L;
        this.local = null;
        this.vehicle = null;
        this.testerTimeOut = false;
        this.doipUtil = DoipUtil.getInstance();
        this.serverHostName = serverHostName;
        this.sourceAddress = sourceAddress;
        this.prefixThreadName = "DoIP-TCP(" + serverHostName + "-" + this.address + ")";
        this.username = username;
        this.retryCount = new AtomicInteger(0);
    }

    private synchronized int getNextCount() {
        this.instanceCounter++;
        if (this.instanceCounter > MAX_THREAD_NUM) {
            this.instanceCounter = 1;
        }
        return this.instanceCounter;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void initDoipAddress(InetAddress target, int localPort) {
        if (this.vehicle == null) {
            this.vehicle = this.util.getDoipAddress(target, 13400, this.util.MAC_REMOTE);
        }
        if (this.local == null) {
            this.local = ADDRESS_MANAGER.getSelectedLocalDoipAddress().a(localPort);
        }
    }

    private void startReceiverThread() throws InterruptedException {
        if (this.receiverThread == null) {
            this.receiverThread = new FdThread(this.prefixThreadName + ":Receiver-" + this.vin + "-" + getNextCount()) { // from class: com.geely.gnds.doip.client.tcp.FdTcpClientServer.1
                @Override // java.lang.Thread, java.lang.Runnable
                public void run() throws IOException {
                    FdTcpClientServer.logger.info("消息接收子线程开始打开！");
                    Socket socket = FdTcpClientServer.this.getSocket();
                    InputStream inputStream = null;
                    try {
                        inputStream = socket.getInputStream();
                    } catch (Exception e) {
                        FdTcpClientServer.logger.error("消息接收子线程创建失败！");
                        FdTcpClientServer.logger.error(FdHelper.getExceptionAsString(e));
                    }
                    FdTcpClientServer.this.initDoipAddress(socket.getInetAddress(), socket.getLocalPort());
                    FdDoipTcpConnection conn = new FdDoipTcpConnection(socket, FdTcpClientServer.this.vehicle, FdTcpClientServer.this.local, FdTcpClientServer.this.messageDispatcher, null);
                    int errorCount = 0;
                    byte[] data = new byte[DoipUtil.MAX_BYTE_ARRAY_SIZE];
                    DoipTcpStreamBuffer streamBuffer = new DoipTcpStreamBuffer();
                    streamBuffer.addListener(conn);
                    while (true) {
                        if (!isFdAlived()) {
                            break;
                        }
                        while (true) {
                            try {
                                int count = inputStream.read(data);
                                if (count <= 0) {
                                    break;
                                }
                                byte[] receivedData = Arrays.copyOf(data, count);
                                if (FdTcpClientServer.logger.isInfoEnabled()) {
                                    FdTcpClientServer.logger.info("从车辆收到数据" + count + "：" + FdTcpClientServer.this.util.toHexStringShort(receivedData));
                                }
                                streamBuffer.append(receivedData);
                                FdTcpClientServer.this.sleep(10);
                            } catch (SocketException e2) {
                                FdTcpClientServer.logger.info(FdHelper.getExceptionAsString(e2));
                            } catch (Exception e3) {
                                FdTcpClientServer.logger.info(FdHelper.getExceptionAsString(e3));
                                if (errorCount > 10) {
                                    FdTcpClientServer.logger.error("连续出错" + errorCount + "，退出线程！");
                                    break;
                                }
                                errorCount++;
                            }
                        }
                        socket.close();
                        if (FdTcpClientServer.logger.isTraceEnabled()) {
                            FdTcpClientServer.logger.trace("No more data to receive. Thread will terminate.");
                        }
                        errorCount = 0;
                        wait4Data();
                    }
                    FdTcpClientServer.logger.info("消息接收子线程关闭！");
                }
            };
            this.receiverThread.start();
            sleep(100);
        }
    }

    public void openTesterPresentThread() throws InterruptedException {
        if (this.testerPresentThread == null) {
            this.testerPresentThread = new FdThread(this.prefixThreadName + ":TesterPresent-" + this.vin + "-" + getNextCount()) { // from class: com.geely.gnds.doip.client.tcp.FdTcpClientServer.2
                /* JADX WARN: Failed to check method for inline after forced processcom.geely.gnds.doip.client.tcp.FdTcpClientServer.a(com.geely.gnds.doip.client.tcp.FdTcpClientServer, long):long */
                @Override // java.lang.Thread, java.lang.Runnable
                public void run() {
                    FdTcpClientServer.this.testerTimeOut = false;
                    FdTcpClientServer.logger.info("守护子线程启动完成。");
                    try {
                        FdTcpClientServer.this.doipUtil.hexString2Int(DsaAddressConst.FUNCTIONAL_ADDRESSING);
                        long lastTime = System.currentTimeMillis();
                        while (isFdAlived()) {
                            long current = System.currentTimeMillis();
                            if (current - lastTime > 5000) {
                                FdTcpClientServer.this.testerTimeOut = true;
                            }
                            lastTime = current;
                            try {
                                FdTcpClientServer.this.sendDoipMessage(true, DsaAddressConst.FUNCTIONAL_ADDRESSING, "3E80", null, null);
                            } catch (Exception e) {
                                FdTcpClientServer.logger.error("发送3E80失败", e);
                            }
                            FdTcpClientServer.a(FdTcpClientServer.this, current + 2000);
                            long sleepTime = (2000 + current) - System.currentTimeMillis();
                            wait4TesterPresent(sleepTime > 0 ? sleepTime : 1L);
                        }
                        FdTcpClientServer.logger.info("守护子线程关闭。");
                    } catch (DoipException e2) {
                        throw new RuntimeException(e2);
                    }
                }
            };
            this.testerPresentThread.setPriority(10);
            this.testerPresentThread.start();
            sleep(100);
        }
    }

    private void closeTesterPresentThread() {
        if (this.testerPresentThread != null) {
            this.testerPresentThread.close();
            this.testerPresentThread = null;
            logger.info("守护子线程被关闭了。" + Thread.currentThread().getName());
        }
    }

    private void sendMessage(boolean isTester, int targetAddress, DoipInstructionType type, byte subFunction, String parameter, int maxRetries, int delay, FdDoipTcpReceiveListener listener, String doipInstructionCode, IQopUdsDataQueuer queuer, boolean is36) throws DoipException {
        boolean needWaitResponse = false;
        if (listener != null) {
            needWaitResponse = this.util.isMessageWithResponse(type, subFunction);
        }
        if (needWaitResponse) {
            this.messageDispatcher.a(listener);
            String data = doipInstructionCode.length() > 10 ? doipInstructionCode.substring(0, 10) + "... ..." : doipInstructionCode;
            logger.debug(MessageFormat.format(MSG_RECEIVE_DATA, type.name(), data));
        }
        if (queuer != null) {
            queuer.checkQopSender();
        }
        byte[] message = this.util.getRequestMessage(type, subFunction, parameter);
        DoipTcpDiagnosticMessage doipTcpDiagnosticMessage = new DoipTcpDiagnosticMessage(this.sourceAddress, targetAddress, message);
        boolean sended = false;
        int retried = -1;
        while (!sended && retried < 1) {
            try {
                if (System.currentTimeMillis() < this.sendLastTime) {
                    sleep(10);
                }
                if (isTester) {
                    send(doipTcpDiagnosticMessage, false, false);
                } else {
                    send(doipTcpDiagnosticMessage, true, is36);
                }
                sended = true;
            } catch (Exception e) {
                logger.error("发送UDS指令SocketByAddress异常", e);
                retried = 1;
                this.fdTcpClient.removeSocketByAddress(this.address);
            }
        }
        if (queuer != null) {
            queuer.releaseQopSender();
        }
        if (!sended) {
            logger.info("线程【{}-{}】sendMessage失败", Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName());
            listener.removeListener();
            if (this.testerTimeOut) {
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00258));
            }
            throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00178));
        }
        if (needWaitResponse && sended) {
            listener.wait4Data();
        }
    }

    private void initSocket(boolean needRetry) throws DoipException {
        try {
            logger.info("initSocket开始------------------------------------------------>>>>>>>>>>>>>>>>>>>>>>>>>>");
            try {
                this.testerTimeOut = false;
                logger.info("socket建立开始");
                this.socket = k();
                if (this.messageDispatcher == null) {
                    this.messageDispatcher = new DoIpMessageDispatcher(this.prefixThreadName + ":Dispatcher-" + getNextCount(), null);
                }
                logger.info("socket建立成功");
                startReceiverThread();
                openTesterPresentThread();
                logger.info("initSocket结束------------------------------------------------>>>>>>>>>>>>>>>>>>>>>>>>>>");
            } catch (Throwable e) {
                logger.error("socket异常", e);
                throw new DoipException(e.getMessage());
            }
        } catch (DoipException e2) {
            throw e2;
        }
    }

    private void send(AbstractDoipMessage abstractDoipMessage, boolean needWriteTxt, boolean is36) throws IOException {
        long send;
        byte[] message = abstractDoipMessage.getMessage();
        initDoipAddress(this.socket.getInetAddress(), this.socket.getLocalPort());
        long start = System.currentTimeMillis();
        synchronized (this.sendLock) {
            send = System.currentTimeMillis();
            OutputStream stream = this.socket.getOutputStream();
            stream.write(abstractDoipMessage.getMessage());
            stream.flush();
        }
        long end = System.currentTimeMillis();
        if (!needWriteTxt) {
            logger.info("发送3E80开始start时间【{}】；send时间【{}】；发送3E80结束end时间【{}】；message【{}】", new Object[]{Long.valueOf(start), Long.valueOf(send), Long.valueOf(end), message});
        }
        if (end - start > 2000) {
            logger.error("start时间【{}】；send时间【{}】；end时间【{}】；message【{}】；不是守护线程【{}】；", new Object[]{Long.valueOf(start), Long.valueOf(send), Long.valueOf(end), message, Boolean.valueOf(needWriteTxt)});
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void sleep(int millis) throws InterruptedException {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            logger.error("Sleep in thread with name \"" + Thread.currentThread().getName() + "\" has been interrupted");
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    private synchronized void resetSocket(boolean flag) throws DoipException, IOException {
        logger.info("resetSocket------------------------------------------------>>>>>>>>>>>>>>>>>>>>>>>>>>");
        if (this.socket != null) {
            this.local = null;
            try {
                this.socket.close();
                this.socket = null;
                this.receiverThread.close();
                this.receiverThread = null;
            } catch (IOException e) {
                logger.error(e.getMessage());
                logger.error(FdHelper.getExceptionAsString(e));
            }
        }
        initSocket(true);
        logger.info("resetSocket方法释放connectChecklock");
        TokenManager.getPool().execute(() -> {
            try {
                DoipAddressManager.getInstance().getInetAddress();
            } catch (Exception e2) {
                logger.error("更新网卡列表失败", e2);
            }
        });
    }

    public void resetSocket2(boolean flag) throws DoipException, IOException {
        logger.info("resetSocket2方法准备获取connectChecklock");
        if (this.socket != null) {
            this.local = null;
            try {
                this.socket.close();
                this.socket = null;
                this.receiverThread.close();
                this.receiverThread = null;
            } catch (IOException e) {
                logger.error(e.getMessage());
                logger.error(FdHelper.getExceptionAsString(e));
            }
        }
        checkConnectStatus();
        logger.info("resetSocket2方法释放connectChecklock");
    }

    private void closeSocket(Socket socket) throws InterruptedException, IOException {
        if (this.receiverThread != null) {
            this.receiverThread.close();
            sleep(100);
            this.receiverThread = null;
        }
        if (socket != null) {
            try {
                boolean isMe = this.socket == socket;
                socket.close();
                if (isMe) {
                    this.socket = null;
                } else {
                    logger.error("socket不是我！");
                }
                this.local = null;
            } catch (IOException e) {
                logger.error(e.getMessage());
                logger.error(FdHelper.getExceptionAsString(e));
            }
        }
    }

    public void closeDoip() {
        try {
            closeTesterPresentThread();
            sleep(100);
            closeSocket(this.socket);
            if (this.messageDispatcher != null) {
                this.messageDispatcher.closeVehicle();
                this.messageDispatcher.close();
                sleep(100);
            }
            logger.info(MessageFormat.format(MSG_CLOSE, this.vin, this.serverHostName));
        } catch (Exception e) {
            logger.error("关闭doip异常", e);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void sendDoipMessage(boolean isTester, String targetAddress, String doipInstructionCode, FdDoipTcpReceiveListener listener, IQopUdsDataQueuer queuer) throws DoipException {
        sendDoipMessage(isTester, targetAddress, doipInstructionCode, "0", "500", listener, queuer, false);
    }

    public void sendDoipMessage(boolean isTester, String targetAddress, String doipInstructionCode, String maxRetriesStr, String delayStr, FdDoipTcpReceiveListener listener, IQopUdsDataQueuer queuer, boolean is36) throws DoipException {
        String typeCode = doipInstructionCode.substring(0, 2);
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        String parameter = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(4);
        int intTargetAddress = this.util.hexString2Int(targetAddress);
        String errorKey = "Retries";
        String errorValue = maxRetriesStr;
        try {
            int maxRetries = Integer.parseInt(maxRetriesStr);
            int maxRetries2 = maxRetries < 0 ? 0 : maxRetries;
            errorKey = "Delay";
            errorValue = delayStr;
            int delay = Integer.parseInt(delayStr);
            DoipInstructionType type = this.util.getDoipInstructionType(typeCode);
            byte subFunction = sf.length() < 1 ? (byte) 0 : this.util.hexString2Byte(sf);
            sendMessage(isTester, intTargetAddress, type, subFunction, parameter, maxRetries2, delay, listener, doipInstructionCode, queuer, is36);
        } catch (Exception e) {
            throw new DoipException(MessageFormat.format(ERROR_PROP_VALUE, targetAddress, doipInstructionCode, errorKey, errorValue));
        }
    }

    public boolean checkConnectStatus() throws DoipException {
        boolean isClose = false;
        logger.info("checkConnectStatus方法获取到connectChecklock");
        try {
            isClose = this.socket == null || this.socket.isClosed() || !this.socket.getKeepAlive();
            if (isClose) {
                int count = this.retryCount.get();
                logger.info("开始第{}次重连车辆", Integer.valueOf(count + 1));
                this.retryCount.incrementAndGet();
                initSocket(false);
            }
        } catch (SocketException e) {
            logger.error(e.getMessage());
            logger.error(FdHelper.getExceptionAsString(e));
            throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015), e);
        } catch (Exception e2) {
            logger.error(e2.getMessage());
        }
        logger.info("checkConnectStatus方法释放connectChecklock");
        return isClose;
    }

    public String getUsername() {
        return this.username;
    }

    public Socket k() throws DoipException, IOException {
        try {
            ServerSocket serverSocket = new ServerSocket(13400);
            Throwable th = null;
            try {
                serverSocket.setSoTimeout(20000);
                try {
                    Socket clientSocket = serverSocket.accept();
                    logger.info("Accepted connection from " + clientSocket.getInetAddress());
                    if (serverSocket != null) {
                        if (0 != 0) {
                            try {
                                serverSocket.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        } else {
                            serverSocket.close();
                        }
                    }
                    return clientSocket;
                } catch (IOException e) {
                    logger.info("createServerSocket IOException: " + e.getMessage());
                    throw e;
                }
            } finally {
            }
        } catch (IOException e2) {
            logger.error("createServerSocket IOException: " + e2.getMessage());
            throw new DoipException("createServerSocket IOException");
        }
    }

    public FdTcpClient getFdTcpClient() {
        return this.fdTcpClient;
    }

    public void setFdTcpClient(FdTcpClient fdTcpClient) {
        this.fdTcpClient = fdTcpClient;
    }

    public void setSourceAddress(int sourceAddress) {
        this.sourceAddress = sourceAddress;
    }
}
