package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.DoipTcpStreamBufferListener;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.IFdPcapLogger;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessageNegAck;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessagePosAck;
import com.geely.gnds.doip.client.message.DoipTcpHeaderNegAck;
import com.geely.gnds.doip.client.message.request.DoipTcpRoutingActivationRequest;
import com.geely.gnds.doip.client.message.response.DoipTcpAliveCheckResponse;
import com.geely.gnds.doip.client.message.response.DoipTcpRoutingActivationResponse;
import com.geely.gnds.doip.client.pcap.DoipAddress;
import java.net.Socket;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: FdDoipTcpConnection.class */
public class FdDoipTcpConnection implements DoipTcpStreamBufferListener {
    private final DoIpMessageDispatcher dispatcher;
    private final Socket socket;
    private final DoipAddress br;
    private final DoipAddress bq;
    private static final Logger logger = LoggerFactory.getLogger(FdDoipTcpConnection.class);
    private final DoipUtil util = DoipUtil.getInstance();
    private final IFdPcapLogger pcapLogger;

    public FdDoipTcpConnection(Socket socket, DoipAddress targetAddress, DoipAddress sourceAddress, DoIpMessageDispatcher dispatcher, IFdPcapLogger pcapLogger) {
        this.dispatcher = dispatcher;
        this.br = targetAddress;
        this.bq = sourceAddress;
        this.pcapLogger = pcapLogger;
        this.socket = socket;
    }

    @Override // com.geely.gnds.doip.client.DoipTcpStreamBufferListener
    public void onHeaderIncorrectPatternFormat() {
        logger.error("onHeaderIncorrectPatternFormat");
    }

    @Override // com.geely.gnds.doip.client.DoipTcpStreamBufferListener
    public void onHeaderMessageTooLarge() {
        logger.error("onHeaderMessageTooLarge");
    }

    @Override // com.geely.gnds.doip.client.DoipTcpStreamBufferListener
    public void onHeaderUnknownPayloadType() {
        logger.error("onHeaderUnknownPayloadType");
    }

    @Override // com.geely.gnds.doip.client.DoipTcpStreamBufferListener
    public void onHeaderInvalidPayloadLength() {
        logger.error("onHeaderInvalidPayloadLength");
    }

    @Override // com.geely.gnds.doip.client.DoipTcpStreamBufferListener
    public void onPayloadCompleted(byte[] header, int payloadType, byte[] payload) {
        AbstractDoipMessage abstractDoipMessage;
        switch (payloadType) {
            case 0:
                abstractDoipMessage = DoipTcpHeaderNegAck.f(payload);
                break;
            case 5:
                abstractDoipMessage = DoipTcpRoutingActivationRequest.g(payload);
                break;
            case 6:
                abstractDoipMessage = DoipTcpRoutingActivationResponse.i(payload);
                break;
            case 8:
                abstractDoipMessage = DoipTcpAliveCheckResponse.h(payload);
                break;
            case AbstractDoipMessage.av /* 32769 */:
                abstractDoipMessage = DoipTcpDiagnosticMessage.c(payload);
                break;
            case AbstractDoipMessage.aw /* 32770 */:
                abstractDoipMessage = DoipTcpDiagnosticMessagePosAck.e(payload);
                break;
            case AbstractDoipMessage.ax /* 32771 */:
                abstractDoipMessage = DoipTcpDiagnosticMessageNegAck.d(payload);
                break;
            default:
                logger.error("收到无法处理的消息: onPayloadCompleted(" + this.util.toHexString(header) + ", " + payloadType + ", " + this.util.toHexString(payload) + ")");
                return;
        }
        if (this.pcapLogger != null) {
            this.pcapLogger.write(new Date(), abstractDoipMessage.getMessage(), this.br, this.bq, false);
        }
        this.dispatcher.a(this.socket, abstractDoipMessage);
    }

    @Override // com.geely.gnds.doip.client.DoipTcpStreamBufferListener
    public void onShredderCompleted(long payloadLength) {
        logger.error("onShredderCompleted(" + payloadLength + ")");
    }
}
