package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.DoipInstructionType;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.exception.CancelByUserException;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessage;
import com.geely.gnds.doip.client.message.response.DoipTcpRoutingActivationResponse;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.net.Socket;
import java.text.MessageFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: FdTesterReceiveListener.class */
public class FdTesterReceiveListener extends FdDoipTcpReceiveListener {
    private static final String MESSAGE_FORMAT = "诊断仪(0x{2})从车辆(0x{3})收到{0}消息：{1}";
    private static final String MESSAGE_IGNORE_FORMAT = "诊断仪(0x{2})从车辆(0x{3})放弃了{0}消息：{1}";
    private static final String MESSAGE_NEG_ACK_FORMAT = "诊断仪(0x{2})从车辆(0x{3})收到{0}消息的负响应：{1}";
    private static final String MESSAGE_WAITING_FORMAT = "{0}指令开始等待接收从车辆(0x{1})发出的响应... ...";
    private DoipInstructionType type;
    private byte subFunction;
    private int targetAddress;
    private static final int cl = 1000;
    private volatile boolean receivedPosAck;
    private final boolean recycled;
    private boolean closeVehicle;
    private static final Logger logger = LoggerFactory.getLogger(FdTesterReceiveListener.class);
    private static DoipUtil util = DoipUtil.getInstance();
    private long curentTotalTimeOut = 0;
    private volatile long current = 0;
    private volatile Object awakeObject = new Object();
    private DoipException doipException = null;
    private DoIpMessageDispatcher dispatcher = null;
    private volatile boolean received = false;
    private DoipMessageContanier doipMessageContanier = null;
    private volatile Object awakePosAck = new Object();
    private int waitTime = 2000;

    public boolean isReceivedPosAck() {
        return this.receivedPosAck;
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isRecycled() {
        return this.recycled;
    }

    public FdTesterReceiveListener(String doipInstructionCode, int targetAddress) throws DoipException {
        this.receivedPosAck = false;
        this.type = util.getDoipInstructionType(doipInstructionCode.substring(0, 2));
        this.targetAddress = targetAddress;
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        this.subFunction = sf.length() < 1 ? (byte) 0 : util.hexString2Byte(sf);
        this.recycled = false;
        this.closeVehicle = false;
        this.receivedPosAck = false;
    }

    private void notify4Data() {
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void onDoipTcpMessage(Socket socket, AbstractDoipMessage message) {
        byte[] data = message.getMessage();
        if (logger.isInfoEnabled()) {
            logger.info("DoipMessage消息的class=" + message.getClass().getName());
            boolean isRar = message instanceof DoipTcpRoutingActivationResponse;
            String ta = util.int2HexString(util.getTargetAddress(data));
            String sa = util.int2HexString(util.getSourceAddress(data));
            Logger logger2 = logger;
            Object[] objArr = new Object[4];
            objArr[0] = this.type.name();
            objArr[1] = util.toHexStringShort(message.getMessage());
            objArr[2] = isRar ? sa : ta;
            objArr[3] = isRar ? ta : sa;
            logger2.info(MessageFormat.format(MESSAGE_FORMAT, objArr));
        }
        String tcpServerIp = socket.getInetAddress().getHostAddress();
        int tcpServerPort = socket.getPort();
        if (message instanceof DoipTcpDiagnosticMessage) {
            onReceiveData(this.type, message, tcpServerIp, tcpServerPort);
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void onReceiveData(DoipInstructionType type, AbstractDoipMessage message, String tcpServerIp, int tcpServerPort) {
        if (this.doipMessageContanier != null) {
            this.doipMessageContanier.setDoipMessageFun(message, type.getPosAckCode());
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void wait4Data() {
        try {
            if (logger.isInfoEnabled()) {
                logger.info(MessageFormat.format(MESSAGE_WAITING_FORMAT, this.type.name(), util.int2HexString(this.targetAddress)));
            }
            synchronized (this.awakePosAck) {
                if (!this.receivedPosAck) {
                    try {
                        this.awakePosAck.wait(1000L);
                    } catch (InterruptedException interruptedException) {
                        interruptedException.printStackTrace();
                    }
                }
                if (!this.receivedPosAck) {
                    removeListener();
                } else {
                    removeListener();
                }
            }
        } catch (Exception e) {
            logger.error(FdHelper.getExceptionAsString(e));
            this.doipException = new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00099), e);
            removeListener();
        }
    }

    private void setDoipException(String msg) {
        logger.error(msg);
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isMyResponse(AbstractDoipMessage message) {
        if (logger.isInfoEnabled()) {
            logger.info("当前指令：TA=0x" + util.int2HexString(this.targetAddress) + ";type=" + this.type.name() + ";正响应=" + this.type.getPosAckCode() + ConstantEnum.EMPTY + util.toHexString(this.subFunction));
            logger.info("收到的消息：" + util.byteArrayToHexString(message.getMessage()));
        }
        return util.isFunctionalAddressingResponse(message, this.type);
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isMyResponseAdress(AbstractDoipMessage message) {
        return util.isMyResponseAdress(message, this.targetAddress, this.type);
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void handleNegativeRes(AbstractDoipMessage message) {
        byte[] data = message.getMessage();
        String messageStr = util.bytesToHexString2(data, data.length);
        logger.error("收到负响应：" + messageStr);
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void handlePosAckRes(AbstractDoipMessage message) {
        synchronized (this.awakePosAck) {
            this.receivedPosAck = true;
            this.awakePosAck.notify();
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void handleNegAckRes(Socket socket, AbstractDoipMessage message) {
        synchronized (this.awakePosAck) {
            this.receivedPosAck = true;
            this.awakePosAck.notify();
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isNegativeMessage(AbstractDoipMessage message) {
        if (message instanceof DoipTcpDiagnosticMessage) {
            byte[] data = ((DoipTcpDiagnosticMessage) message).getMessage();
            return data.length >= ConstantEnum.FIFTEEN.intValue() && data[12] == Byte.MAX_VALUE && this.type.getInstructionCode().equalsIgnoreCase(this.doipUtil.toHexString(data[13]));
        }
        return false;
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void cancelByUserException() {
        this.doipException = new CancelByUserException();
        closeVehicle();
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void closeVehicle() {
        this.closeVehicle = true;
        synchronized (this.awakePosAck) {
            this.awakePosAck.notify();
        }
        notify4Data();
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void debugIgnore(AbstractDoipMessage message) {
        byte[] data = message.getMessage();
        boolean isRar = message instanceof DoipTcpRoutingActivationResponse;
        String ta = util.int2HexString(util.getTargetAddress(data));
        String sa = util.int2HexString(util.getSourceAddress(data));
        Logger logger2 = logger;
        Object[] objArr = new Object[4];
        objArr[0] = this.type.name();
        objArr[1] = util.toHexString(message.getMessage());
        objArr[2] = isRar ? sa : ta;
        objArr[3] = isRar ? ta : sa;
        logger2.info(MessageFormat.format(MESSAGE_IGNORE_FORMAT, objArr));
    }
}
