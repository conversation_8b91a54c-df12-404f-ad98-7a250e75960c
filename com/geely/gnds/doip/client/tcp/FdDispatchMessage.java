package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import java.net.Socket;

/* loaded from: FdDispatchMessage.class */
public class FdDispatchMessage {
    private final Socket socket;
    private final AbstractDoipMessage message;

    public FdDispatchMessage(Socket socket, AbstractDoipMessage message) {
        this.socket = socket;
        this.message = message;
    }

    public Socket getSocket() {
        return this.socket;
    }

    public AbstractDoipMessage getMessage() {
        return this.message;
    }
}
