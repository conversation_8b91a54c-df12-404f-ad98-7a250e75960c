package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.DoipInstructionType;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.exception.CancelByUserException;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessage;
import com.geely.gnds.doip.client.message.response.DoipTcpRoutingActivationResponse;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import java.net.Socket;
import java.text.MessageFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: FdFunctionalAddressingReceiveListener.class */
public class FdFunctionalAddressingReceiveListener extends FdDoipTcpReceiveListener {
    private static final String MESSAGE_FORMAT = "诊断仪(0x{2})从车辆(0x{3})收到{0}消息：{1}";
    private static final String MESSAGE_IGNORE_FORMAT = "诊断仪(0x{2})从车辆(0x{3})放弃了{0}消息：{1}";
    private static final String MESSAGE_NEG_ACK_FORMAT = "诊断仪(0x{2})从车辆(0x{3})收到{0}消息的负响应：{1}";
    private static final String MESSAGE_WAITING_FORMAT = "{0}指令开始等待接收从车辆(0x{1})发出的响应... ...";
    private int totalTimeout;
    private long curentTotalTimeOut;
    private volatile long current;
    private DoipInstructionType type;
    private int posAckTimeout;
    private byte subFunction;
    private int targetAddress;
    private volatile Object awakeObject;
    private volatile boolean openDelay;
    private DoipException doipException;
    private DoIpMessageDispatcher dispatcher;
    private volatile boolean received;
    private DoipMessageContanier doipMessageContanier;
    private volatile boolean receivedPosAck;
    private int delayedTimeOut;
    private volatile Object awakePosAck;
    private int waitTime;
    private final boolean recycled;
    private boolean closeVehicle;
    private static final Logger logger = LoggerFactory.getLogger(FdFunctionalAddressingReceiveListener.class);
    private static DoipUtil util = DoipUtil.getInstance();

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isRecycled() {
        return this.recycled;
    }

    public FdFunctionalAddressingReceiveListener(String doipInstructionCode, int targetAddress, DoipMessageContanier doipMessageContanier) throws DoipException {
        this.totalTimeout = 1000000;
        this.curentTotalTimeOut = 0L;
        this.current = 0L;
        this.posAckTimeout = 8000;
        this.awakeObject = new Object();
        this.openDelay = false;
        this.doipException = null;
        this.dispatcher = null;
        this.received = false;
        this.doipMessageContanier = null;
        this.receivedPosAck = false;
        this.delayedTimeOut = 6000;
        this.awakePosAck = new Object();
        this.waitTime = 2000;
        this.type = util.getDoipInstructionType(doipInstructionCode.substring(0, 2));
        this.targetAddress = targetAddress;
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        this.subFunction = sf.length() < 1 ? (byte) 0 : util.hexString2Byte(sf);
        this.recycled = false;
        this.doipMessageContanier = doipMessageContanier;
        this.closeVehicle = false;
        this.receivedPosAck = false;
        this.openDelay = false;
        this.curentTotalTimeOut = 0L;
        this.current = 0L;
    }

    public FdFunctionalAddressingReceiveListener(String doipInstructionCode, int targetAddress, DoipMessageContanier doipMessageContanier, int pClientVehicleMax, int p2ServerMax, int p4ServerMax, int p2Client) throws DoipException {
        this.totalTimeout = 1000000;
        this.curentTotalTimeOut = 0L;
        this.current = 0L;
        this.posAckTimeout = 8000;
        this.awakeObject = new Object();
        this.openDelay = false;
        this.doipException = null;
        this.dispatcher = null;
        this.received = false;
        this.doipMessageContanier = null;
        this.receivedPosAck = false;
        this.delayedTimeOut = 6000;
        this.awakePosAck = new Object();
        this.waitTime = 2000;
        this.type = util.getDoipInstructionType(doipInstructionCode.substring(0, 2));
        this.targetAddress = targetAddress;
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        this.subFunction = sf.length() < 1 ? (byte) 0 : util.hexString2Byte(sf);
        this.recycled = false;
        this.doipMessageContanier = doipMessageContanier;
        this.closeVehicle = false;
        this.openDelay = false;
        this.receivedPosAck = false;
        this.waitTime = p2ServerMax;
        this.posAckTimeout = pClientVehicleMax;
        this.totalTimeout = p4ServerMax;
        this.delayedTimeOut = p2Client;
    }

    private void notify4Data() {
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void onDoipTcpMessage(Socket socket, AbstractDoipMessage message) {
        byte[] data = message.getMessage();
        if (logger.isInfoEnabled()) {
            logger.info("DoipMessage消息的class=" + message.getClass().getName());
            boolean isRar = message instanceof DoipTcpRoutingActivationResponse;
            String ta = util.int2HexString(util.getTargetAddress(data));
            String sa = util.int2HexString(util.getSourceAddress(data));
            Logger logger2 = logger;
            Object[] objArr = new Object[4];
            objArr[0] = this.type.name();
            objArr[1] = util.toHexStringShort(message.getMessage());
            objArr[2] = isRar ? sa : ta;
            objArr[3] = isRar ? ta : sa;
            logger2.info(MessageFormat.format(MESSAGE_FORMAT, objArr));
        }
        String tcpServerIp = socket.getInetAddress().getHostAddress();
        int tcpServerPort = socket.getPort();
        if (message instanceof DoipTcpDiagnosticMessage) {
            onReceiveData(this.type, message, tcpServerIp, tcpServerPort);
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void onReceiveData(DoipInstructionType type, AbstractDoipMessage message, String tcpServerIp, int tcpServerPort) {
        if (this.doipMessageContanier != null) {
            this.doipMessageContanier.setDoipMessageFun(message, type.getPosAckCode());
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void wait4Data() {
        try {
            if (logger.isInfoEnabled()) {
                logger.info(MessageFormat.format(MESSAGE_WAITING_FORMAT, this.type.name(), util.int2HexString(this.targetAddress)));
            }
            synchronized (this.awakePosAck) {
                if (!this.receivedPosAck) {
                    try {
                        this.awakePosAck.wait(this.posAckTimeout);
                    } catch (InterruptedException interruptedException) {
                        interruptedException.printStackTrace();
                    }
                }
                if (!this.receivedPosAck) {
                    setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00162));
                    removeListener();
                    return;
                }
                synchronized (this.awakeObject) {
                    this.awakeObject.wait(this.waitTime);
                }
                if (this.openDelay) {
                    boolean waiting = (isTimeOutByTotal() || this.received) ? false : true;
                    while (waiting) {
                        this.openDelay = false;
                        synchronized (this.awakeObject) {
                            try {
                                this.awakeObject.wait(this.delayedTimeOut);
                            } catch (InterruptedException e) {
                                throw e;
                            }
                        }
                        waiting = !isTimeOutByTotal() && this.openDelay;
                    }
                }
                removeListener();
            }
        } catch (InterruptedException e2) {
            logger.error(FdHelper.getExceptionAsString(e2));
            this.doipException = new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00099), e2);
            removeListener();
        }
    }

    private void setDoipException(String msg) {
        logger.error(msg);
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isMyResponse(AbstractDoipMessage message) {
        if (logger.isInfoEnabled()) {
            logger.info("当前指令：TA=0x" + util.int2HexString(this.targetAddress) + ";type=" + this.type.name() + ";正响应=" + this.type.getPosAckCode() + ConstantEnum.EMPTY + util.toHexString(this.subFunction));
            logger.info("收到的消息：" + util.byteArrayToHexString(message.getMessage()));
        }
        return util.isFunctionalAddressingResponse(message, this.type);
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void handleNegativeRes(AbstractDoipMessage message) {
        byte[] data = message.getMessage();
        String messageStr = util.bytesToHexString2(data, data.length);
        logger.error("收到负响应：" + messageStr);
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void handlePosAckRes(AbstractDoipMessage message) {
        synchronized (this.awakePosAck) {
            this.receivedPosAck = true;
            this.awakePosAck.notify();
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isMyResponseAdress(AbstractDoipMessage message) {
        return true;
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void handleNegAckRes(Socket socket, AbstractDoipMessage message) {
        synchronized (this.awakePosAck) {
            this.receivedPosAck = true;
            this.awakePosAck.notify();
        }
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isNegativeMessage(AbstractDoipMessage message) {
        if (message instanceof DoipTcpDiagnosticMessage) {
            byte[] data = ((DoipTcpDiagnosticMessage) message).getMessage();
            return data.length >= ConstantEnum.FIFTEEN.intValue() && data[12] == Byte.MAX_VALUE && this.type.getInstructionCode().equalsIgnoreCase(this.doipUtil.toHexString(data[13]));
        }
        return false;
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void cancelByUserException() {
        this.doipException = new CancelByUserException();
        closeVehicle();
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void closeVehicle() {
        this.closeVehicle = true;
        synchronized (this.awakePosAck) {
            this.awakePosAck.notify();
        }
        notify4Data();
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void debugIgnore(AbstractDoipMessage message) {
        byte[] data = message.getMessage();
        boolean isRar = message instanceof DoipTcpRoutingActivationResponse;
        String ta = util.int2HexString(util.getTargetAddress(data));
        String sa = util.int2HexString(util.getSourceAddress(data));
        Logger logger2 = logger;
        Object[] objArr = new Object[4];
        objArr[0] = this.type.name();
        objArr[1] = util.toHexString(message.getMessage());
        objArr[2] = isRar ? sa : ta;
        objArr[3] = isRar ? ta : sa;
        logger2.info(MessageFormat.format(MESSAGE_IGNORE_FORMAT, objArr));
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public void dealDelayMessage(AbstractDoipMessage message) {
        this.openDelay = true;
        this.current = this.current == 0 ? System.currentTimeMillis() : this.current;
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }

    private synchronized boolean isTimeOutByTotal() {
        long now = System.currentTimeMillis();
        if (this.current > 0) {
            this.curentTotalTimeOut = (this.curentTotalTimeOut + now) - this.current;
        }
        this.current = now;
        return this.curentTotalTimeOut > ((long) this.totalTimeout);
    }

    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
    public boolean isDelayMessage(AbstractDoipMessage message) {
        if (message instanceof DoipTcpDiagnosticMessage) {
            byte[] data = ((DoipTcpDiagnosticMessage) message).getMessage();
            return data.length >= ConstantEnum.FIFTEEN.intValue() && data[12] == Byte.MAX_VALUE && data[14] == 120;
        }
        return false;
    }
}
