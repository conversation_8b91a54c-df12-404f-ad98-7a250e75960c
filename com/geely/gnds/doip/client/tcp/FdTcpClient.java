package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.DoipInstructionType;
import com.geely.gnds.doip.client.DoipTcpStreamBuffer;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.FdThread;
import com.geely.gnds.doip.client.IFdPcapLogger;
import com.geely.gnds.doip.client.dro.document.event.DiagnosticReadoutEvent;
import com.geely.gnds.doip.client.dro.document.event.Ecu;
import com.geely.gnds.doip.client.dro.document.root.DroResult;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessage;
import com.geely.gnds.doip.client.message.dto.DoipTcpRoutingActivationInfo;
import com.geely.gnds.doip.client.message.request.DoipTcpRoutingActivationRequest;
import com.geely.gnds.doip.client.message.response.DoipTcpRoutingActivationResponse;
import com.geely.gnds.doip.client.pcap.DoipAddress;
import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.doip.client.pcap.PcapParam;
import com.geely.gnds.doip.client.pcap.PcapThread;
import com.geely.gnds.doip.client.pcap.PcapWrite;
import com.geely.gnds.doip.client.txt.FdTxtLogger;
import com.geely.gnds.doip.client.xml.FdXmlLogger;
import com.geely.gnds.doip.client.xml.XmlBackGround;
import com.geely.gnds.dsa.enums.DsaAddressConst;
import com.geely.gnds.dsa.enums.LanguageEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.ruoyi.common.constant.Constants;
import com.geely.gnds.ruoyi.common.utils.StringUtils;
import com.geely.gnds.ruoyi.common.utils.spring.SpringUtils;
import com.geely.gnds.ruoyi.framework.cache.EhcacheClient;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.common.ActionContext;
import com.geely.gnds.tester.dto.DroUdsDto;
import com.geely.gnds.tester.dto.VehicleDto;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TestLogTypeEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.seq.IQopUdsDataQueuer;
import com.geely.gnds.tester.seq.SingletonManager;
import com.geely.gnds.tester.service.ConnectedService;
import com.geely.gnds.tester.service.impl.SeqServiceImpl;
import com.geely.gnds.tester.util.MessageUtils;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketException;
import java.security.KeyStore;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/*  JADX ERROR: NullPointerException in pass: ClassModifier
    java.lang.NullPointerException: Cannot invoke "java.util.List.forEach(java.util.function.Consumer)" because "blocks" is null
    	at jadx.core.utils.BlockUtils.collectAllInsns(BlockUtils.java:1029)
    	at jadx.core.dex.visitors.ClassModifier.removeBridgeMethod(ClassModifier.java:245)
    	at jadx.core.dex.visitors.ClassModifier.removeSyntheticMethods(ClassModifier.java:160)
    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
    	at jadx.core.dex.visitors.ClassModifier.visit(ClassModifier.java:65)
    */
/* loaded from: FdTcpClient.class */
public class FdTcpClient {
    private Map<String, FdTcpClientServer> serverSocketMap;
    public String localIp;
    private boolean needTls;
    private static final int TLS_PORT = 3496;
    private static final String ERROR_PROP_VALUE = "向目标地址({0})发送的指令{1}的属性({2})的值({3})的格式错误！";
    private static final String MSG_ACTIVATION = "诊断仪({0})成功注册到车辆({1}:{2})！";
    private static final String MSG_RECEIVE_DATA = "诊断仪发出的{0}指令({1})准备接收车辆回复消息！";
    private static final String MSG_CLOSE = "关闭与车辆({0}:{1})的连接！";
    private static final String MSG_AWAKE = "诊断仪于{1}被唤醒！设定的自动自动时间戳为{0}。唤醒后停留50ms，并于{2}继续！";
    private volatile Boolean connectChecklock;
    private volatile Boolean sendLock;
    private Integer platformCode;
    private static final int MAX_THREAD_NUM = 60000;
    private static final int SOCKET_TIME_OUT = 0;
    private static final int TESTER_PRESENT_SLEEP = 2000;
    private static final int SOCKET_RETRY_SLEEP = 2000;
    private String serverHostName;
    private int instanceCounter;
    private boolean recvFailed;
    private final DoipUtil util;
    private final String prefixThreadName;
    private String vin;
    private final int sourceAddress;
    private final int gatewayAddress;
    private volatile long testerPresentNext;
    private static final int GEELY_GATEWAY_ADDRESS = 0;
    private final int doipPort;
    private volatile Socket socket;
    private volatile FdThread testerPresentThread;
    private volatile DoIpMessageDispatcher messageDispatcher;
    private volatile FdThread receiverThread;
    private volatile long sendLastTime;
    private final IFdPcapLogger pcapLogger;
    private XmlBackGround xmlBackGround;
    private final FdXmlLogger fdXmlLogger;
    private PcapParam pcapParam;
    private PcapThread pcapThread;
    private FdTxtLogger txtLogger;
    private DroResult droResult;
    private List<Ecu> ecuList;
    private DiagnosticReadoutEvent diagnosticReadoutEvent;
    private DoipAddress local;
    private DoipAddress vehicle;
    private volatile boolean initSocket;
    private boolean openPcapLog;
    private boolean testerTimeOut;
    private boolean isVirtualVehicle;
    private boolean isDisConnectedByAdmin;
    private boolean isDsaVehicle;
    private DoipUtil doipUtil;
    private String username;
    private AtomicInteger retryCount;
    private long connectTime;
    private AtomicInteger noAckTesterCount;
    private String seqCode;
    private FdDsaLogger fdDsaLogger;
    private AtomicInteger pcapCount;
    private VehicleDto vehicleDto;
    private String heartBeatId;
    public List<DroUdsDto> droUdsList;
    private volatile boolean sendUrgentData;
    private String childPath;
    private static final Logger logger = LoggerFactory.getLogger(FdTcpClient.class);
    private static final DoipAddressManager ADDRESS_MANAGER = DoipAddressManager.getInstance();
    private static int retryCountMax = 450;

    /*  JADX ERROR: Failed to decode insn: 0x0002: MOVE_MULTI
        java.lang.ArrayIndexOutOfBoundsException: arraycopy: source index -1 out of bounds for object array[6]
        	at java.base/java.lang.System.arraycopy(Native Method)
        	at jadx.plugins.input.java.data.code.StackState.insert(StackState.java:52)
        	at jadx.plugins.input.java.data.code.CodeDecodeState.insert(CodeDecodeState.java:137)
        	at jadx.plugins.input.java.data.code.JavaInsnsRegister.dup2x1(JavaInsnsRegister.java:313)
        	at jadx.plugins.input.java.data.code.JavaInsnData.decode(JavaInsnData.java:46)
        	at jadx.core.dex.instructions.InsnDecoder.lambda$process$0(InsnDecoder.java:50)
        	at jadx.plugins.input.java.data.code.JavaCodeReader.visitInstructions(JavaCodeReader.java:85)
        	at jadx.core.dex.instructions.InsnDecoder.process(InsnDecoder.java:46)
        	at jadx.core.dex.nodes.MethodNode.load(MethodNode.java:158)
        	at jadx.core.dex.nodes.ClassNode.load(ClassNode.java:458)
        	at jadx.core.ProcessClass.process(ProcessClass.java:69)
        	at jadx.core.ProcessClass.generateCode(ProcessClass.java:109)
        	at jadx.core.dex.nodes.ClassNode.generateClassCode(ClassNode.java:401)
        	at jadx.core.dex.nodes.ClassNode.decompile(ClassNode.java:389)
        	at jadx.core.dex.nodes.ClassNode.decompile(ClassNode.java:309)
        */
    static /* synthetic */ long access$1402(com.geely.gnds.doip.client.tcp.FdTcpClient r6, long r7) {
        /*
            r0 = r6
            r1 = r7
            // decode failed: arraycopy: source index -1 out of bounds for object array[6]
            r0.testerPresentNext = r1
            return r-1
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.doip.client.tcp.FdTcpClient.access$1402(com.geely.gnds.doip.client.tcp.FdTcpClient, long):long");
    }

    static {
    }

    public FdTcpClientServer getSocketByAddress(String address) {
        return this.serverSocketMap.getOrDefault(address, null);
    }

    public void setSocketByAddress(String address, FdTcpClientServer socket) {
        this.serverSocketMap.put(address, socket);
    }

    public void removeSocketByAddress(String address) {
        FdTcpClientServer socketByAddress = getSocketByAddress(address);
        if (socketByAddress != null) {
            socketByAddress.closeDoip();
            this.serverSocketMap.remove(address);
        }
    }

    public Integer getPlatformCode() {
        return this.platformCode;
    }

    public void setPlatformCode(Integer platformCode) {
        this.platformCode = platformCode;
    }

    public void setServerHostName(String ip) {
        this.serverHostName = ip;
    }

    public boolean isInitSocket() {
        return this.initSocket;
    }

    public boolean isDisConnectedByAdmin() {
        return this.isDisConnectedByAdmin;
    }

    public void setDisConnectedByAdmin(boolean disConnectedByAdmin) {
        this.isDisConnectedByAdmin = disConnectedByAdmin;
    }

    public String getSeqCode() {
        return this.seqCode;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public void cleanSeqCode() {
        this.seqCode = "";
    }

    public Socket getSocket() {
        return this.socket;
    }

    public VehicleDto getVehicleDto() {
        return this.vehicleDto;
    }

    public void setVehicleDto(VehicleDto vehicleDto) {
        this.vehicleDto = vehicleDto;
    }

    public boolean isVirtualVehicle() {
        return this.isVirtualVehicle;
    }

    public String getHeartBeatId() {
        return this.heartBeatId;
    }

    public void setHeartBeatId(String heartBeatId) {
        this.heartBeatId = heartBeatId;
    }

    public DroResult getDroResult() {
        return this.droResult;
    }

    public void setDroResult(DroResult droResult) {
        this.droResult = droResult;
    }

    public List<Ecu> getEcuList() {
        return this.ecuList;
    }

    public void setEcuList(List<Ecu> ecuList) {
        this.ecuList = ecuList;
    }

    public void addDroUds(DroUdsDto droUdsDto) {
        this.droUdsList.add(droUdsDto);
    }

    public List<DroUdsDto> getDroUdsList() {
        return this.droUdsList;
    }

    public void cleanDroUdsList() {
        this.droUdsList.clear();
    }

    public DiagnosticReadoutEvent getDiagnosticReadoutEvent() {
        return this.diagnosticReadoutEvent;
    }

    public void setDiagnosticReadoutEvent(DiagnosticReadoutEvent diagnosticReadoutEvent) {
        this.diagnosticReadoutEvent = diagnosticReadoutEvent;
    }

    public boolean isDsaVehicle() {
        return this.isDsaVehicle;
    }

    public String getChildPath() {
        return this.childPath;
    }

    public void setChildPath(String childPath) {
        this.childPath = childPath;
    }

    public void setSendUrgentData(boolean sendUrgentData) {
        logger.info("设置是否发送紧急数据【{}】", Boolean.valueOf(sendUrgentData));
        this.sendUrgentData = sendUrgentData;
    }

    public void connectDoip(String vin, boolean flag) throws DoipException, IOException {
        this.vin = vin;
        resetSocket(flag);
    }

    public FdDoipTcpReceiveListener getIdleReceiveListener() throws DoipException {
        if (this.messageDispatcher != null) {
            return this.messageDispatcher.getIdleReceiveListener();
        }
        throw new DoipException(MessageUtils.getMessage("Vehicle connection initialization exception"));
    }

    public FdTcpClient(String serverHostName, int sourceAddress, int gatewayAddress, int port, IFdPcapLogger pcapLogger, FdXmlLogger fdXmlLogger) {
        this.serverSocketMap = new HashMap();
        this.localIp = "";
        this.needTls = false;
        this.connectChecklock = true;
        this.sendLock = true;
        this.platformCode = 0;
        this.instanceCounter = 0;
        this.recvFailed = false;
        this.util = DoipUtil.getInstance();
        this.testerPresentNext = -1L;
        this.socket = null;
        this.testerPresentThread = null;
        this.messageDispatcher = null;
        this.receiverThread = null;
        this.sendLastTime = -1L;
        this.pcapLogger = null;
        this.pcapParam = null;
        this.droResult = new DroResult();
        this.ecuList = new ArrayList();
        this.diagnosticReadoutEvent = new DiagnosticReadoutEvent();
        this.local = null;
        this.vehicle = null;
        this.initSocket = false;
        this.openPcapLog = false;
        this.testerTimeOut = false;
        this.isVirtualVehicle = false;
        this.isDisConnectedByAdmin = false;
        this.isDsaVehicle = false;
        this.doipUtil = DoipUtil.getInstance();
        this.connectTime = System.currentTimeMillis() / 1000;
        this.noAckTesterCount = new AtomicInteger(0);
        this.seqCode = "";
        this.pcapCount = new AtomicInteger();
        this.droUdsList = new ArrayList();
        this.sendUrgentData = false;
        this.serverHostName = serverHostName;
        this.doipPort = port;
        this.sourceAddress = sourceAddress;
        this.gatewayAddress = gatewayAddress;
        this.prefixThreadName = "DoIP-TCP(" + serverHostName + ")";
        this.fdXmlLogger = fdXmlLogger;
    }

    public FdTcpClient(String serverHostName, int sourceAddress, int gatewayAddress, int port, FdXmlLogger fdXmlLogger, PcapParam pcapParam, FdTxtLogger txtLogger, String username, String heartBeatId, boolean needTls) {
        this.serverSocketMap = new HashMap();
        this.localIp = "";
        this.needTls = false;
        this.connectChecklock = true;
        this.sendLock = true;
        this.platformCode = 0;
        this.instanceCounter = 0;
        this.recvFailed = false;
        this.util = DoipUtil.getInstance();
        this.testerPresentNext = -1L;
        this.socket = null;
        this.testerPresentThread = null;
        this.messageDispatcher = null;
        this.receiverThread = null;
        this.sendLastTime = -1L;
        this.pcapLogger = null;
        this.pcapParam = null;
        this.droResult = new DroResult();
        this.ecuList = new ArrayList();
        this.diagnosticReadoutEvent = new DiagnosticReadoutEvent();
        this.local = null;
        this.vehicle = null;
        this.initSocket = false;
        this.openPcapLog = false;
        this.testerTimeOut = false;
        this.isVirtualVehicle = false;
        this.isDisConnectedByAdmin = false;
        this.isDsaVehicle = false;
        this.doipUtil = DoipUtil.getInstance();
        this.connectTime = System.currentTimeMillis() / 1000;
        this.noAckTesterCount = new AtomicInteger(0);
        this.seqCode = "";
        this.pcapCount = new AtomicInteger();
        this.droUdsList = new ArrayList();
        this.sendUrgentData = false;
        this.serverHostName = serverHostName;
        this.doipPort = port;
        this.sourceAddress = sourceAddress;
        this.gatewayAddress = gatewayAddress;
        this.prefixThreadName = "DoIP-TCP(" + serverHostName + ")";
        this.fdXmlLogger = fdXmlLogger;
        this.pcapParam = pcapParam;
        this.txtLogger = txtLogger;
        this.xmlBackGround = new XmlBackGround("GNDS_Background", new Date(), "GRI-00000000", "********", (String) Optional.ofNullable(ActionContext.getRequest()).map((v0) -> {
            return v0.getRequestURI();
        }).orElse(""));
        this.username = username;
        this.heartBeatId = heartBeatId;
        this.retryCount = new AtomicInteger(0);
        this.needTls = needTls;
    }

    public FdTcpClient(FdXmlLogger fdXmlLogger, FdTxtLogger txtLogger, String vin, String username, String heartBeatId) {
        this.serverSocketMap = new HashMap();
        this.localIp = "";
        this.needTls = false;
        this.connectChecklock = true;
        this.sendLock = true;
        this.platformCode = 0;
        this.instanceCounter = 0;
        this.recvFailed = false;
        this.util = DoipUtil.getInstance();
        this.testerPresentNext = -1L;
        this.socket = null;
        this.testerPresentThread = null;
        this.messageDispatcher = null;
        this.receiverThread = null;
        this.sendLastTime = -1L;
        this.pcapLogger = null;
        this.pcapParam = null;
        this.droResult = new DroResult();
        this.ecuList = new ArrayList();
        this.diagnosticReadoutEvent = new DiagnosticReadoutEvent();
        this.local = null;
        this.vehicle = null;
        this.initSocket = false;
        this.openPcapLog = false;
        this.testerTimeOut = false;
        this.isVirtualVehicle = false;
        this.isDisConnectedByAdmin = false;
        this.isDsaVehicle = false;
        this.doipUtil = DoipUtil.getInstance();
        this.connectTime = System.currentTimeMillis() / 1000;
        this.noAckTesterCount = new AtomicInteger(0);
        this.seqCode = "";
        this.pcapCount = new AtomicInteger();
        this.droUdsList = new ArrayList();
        this.sendUrgentData = false;
        this.isVirtualVehicle = true;
        this.serverHostName = "readFromXml";
        this.doipPort = 0;
        this.sourceAddress = 0;
        this.gatewayAddress = 0;
        this.vin = vin;
        this.prefixThreadName = "DoIP-TCP(" + this.serverHostName + ")";
        this.fdXmlLogger = fdXmlLogger;
        this.txtLogger = txtLogger;
        this.xmlBackGround = new XmlBackGround("GNDS_Background", new Date(), "GRI-00000000", "********", (String) Optional.ofNullable(ActionContext.getRequest()).map((v0) -> {
            return v0.getRequestURI();
        }).orElse(""));
        this.username = username;
        this.heartBeatId = heartBeatId;
        this.retryCount = new AtomicInteger(0);
    }

    public FdTcpClient(String serverHostName, int sourceAddress, int gatewayAddress, int port, String username, String heartBeatId, FdXmlLogger fdXmlLogger, FdTxtLogger txtLogger, boolean needTls) {
        this.serverSocketMap = new HashMap();
        this.localIp = "";
        this.needTls = false;
        this.connectChecklock = true;
        this.sendLock = true;
        this.platformCode = 0;
        this.instanceCounter = 0;
        this.recvFailed = false;
        this.util = DoipUtil.getInstance();
        this.testerPresentNext = -1L;
        this.socket = null;
        this.testerPresentThread = null;
        this.messageDispatcher = null;
        this.receiverThread = null;
        this.sendLastTime = -1L;
        this.pcapLogger = null;
        this.pcapParam = null;
        this.droResult = new DroResult();
        this.ecuList = new ArrayList();
        this.diagnosticReadoutEvent = new DiagnosticReadoutEvent();
        this.local = null;
        this.vehicle = null;
        this.initSocket = false;
        this.openPcapLog = false;
        this.testerTimeOut = false;
        this.isVirtualVehicle = false;
        this.isDisConnectedByAdmin = false;
        this.isDsaVehicle = false;
        this.doipUtil = DoipUtil.getInstance();
        this.connectTime = System.currentTimeMillis() / 1000;
        this.noAckTesterCount = new AtomicInteger(0);
        this.seqCode = "";
        this.pcapCount = new AtomicInteger();
        this.droUdsList = new ArrayList();
        this.sendUrgentData = false;
        this.serverHostName = serverHostName;
        this.doipPort = port;
        this.sourceAddress = sourceAddress;
        this.gatewayAddress = gatewayAddress;
        this.prefixThreadName = "DoIP-TCP(" + serverHostName + ")";
        this.username = username;
        this.heartBeatId = heartBeatId;
        this.retryCount = new AtomicInteger(0);
        this.isDsaVehicle = true;
        this.fdXmlLogger = fdXmlLogger;
        this.txtLogger = txtLogger;
        this.needTls = needTls;
    }

    public FdXmlLogger getXmlLogger() {
        return this.fdXmlLogger;
    }

    public XmlBackGround getXmlBackGround() {
        return this.xmlBackGround;
    }

    public FdTxtLogger geTxtLogger() {
        return this.txtLogger;
    }

    public FdDsaLogger getFdDsaLogger() {
        return this.fdDsaLogger;
    }

    public void setFdDsaDsaTxtLogger(FdDsaLogger fdDsaLogger) {
        this.fdDsaLogger = fdDsaLogger;
    }

    public PcapThread getPcapThread() {
        return this.pcapThread;
    }

    private synchronized int getNextCount() {
        this.instanceCounter++;
        if (this.instanceCounter > MAX_THREAD_NUM) {
            this.instanceCounter = 1;
        }
        return this.instanceCounter;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void initDoipAddress(InetAddress target, int localPort) {
        if (this.vehicle == null) {
            this.vehicle = this.util.getDoipAddress(target, 13400, this.util.MAC_REMOTE);
        }
        if (this.local == null) {
            this.local = ADDRESS_MANAGER.getSelectedLocalDoipAddress().a(localPort);
        }
    }

    private void startReceiverThread() throws InterruptedException {
        if (this.receiverThread == null) {
            this.receiverThread = new FdThread(this.prefixThreadName + ":Receiver-" + this.vin + "-" + getNextCount()) { // from class: com.geely.gnds.doip.client.tcp.FdTcpClient.1
                @Override // java.lang.Thread, java.lang.Runnable
                public void run() throws IOException {
                    FdTcpClient.logger.info("消息接收子线程开始打开！");
                    Optional.ofNullable(FdTcpClient.this.txtLogger).ifPresent(logger2 -> {
                        FdTcpClient.this.txtLogger.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", "消息接收子线程开始打开！".getBytes());
                    });
                    Socket socket = FdTcpClient.this.getSocket();
                    InputStream inputStream = null;
                    try {
                        inputStream = socket.getInputStream();
                    } catch (Exception e) {
                        FdTcpClient.logger.error("消息接收子线程创建失败！");
                        FdTcpClient.logger.error(FdHelper.getExceptionAsString(e));
                        Optional.ofNullable(FdTcpClient.this.txtLogger).ifPresent(logger3 -> {
                            logger3.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), Constants.LOGIN_FAIL, ("消息接收子线程创建失败,原因:" + FdHelper.getExceptionAsString(e)).getBytes());
                        });
                    }
                    FdTcpClient.this.initDoipAddress(socket.getInetAddress(), socket.getLocalPort());
                    FdDoipTcpConnection conn = new FdDoipTcpConnection(socket, FdTcpClient.this.vehicle, FdTcpClient.this.local, FdTcpClient.this.messageDispatcher, FdTcpClient.this.pcapLogger);
                    int errorCount = 0;
                    byte[] data = new byte[DoipUtil.MAX_BYTE_ARRAY_SIZE];
                    DoipTcpStreamBuffer streamBuffer = new DoipTcpStreamBuffer();
                    streamBuffer.addListener(conn);
                    while (true) {
                        if (!isFdAlived()) {
                            break;
                        }
                        while (true) {
                            try {
                                int count = inputStream.read(data);
                                if (count <= 0) {
                                    break;
                                }
                                byte[] receivedData = Arrays.copyOf(data, count);
                                if (FdTcpClient.logger.isInfoEnabled()) {
                                    FdTcpClient.logger.info("从车辆收到数据" + count + "：" + FdTcpClient.this.util.toHexStringShort(receivedData));
                                }
                                streamBuffer.append(receivedData);
                                FdTcpClient.this.sleep(10);
                            } catch (SocketException e2) {
                                FdTcpClient.logger.info(FdHelper.getExceptionAsString(e2));
                                String message = e2.getMessage();
                                if (StringUtils.isNotBlank(message) && message.contains("Software caused connection abort: recv failed")) {
                                    FdTcpClient.this.recvFailed = true;
                                }
                            } catch (Exception e3) {
                                FdTcpClient.logger.info(FdHelper.getExceptionAsString(e3));
                                if (errorCount > 10) {
                                    FdTcpClient.logger.error("连续出错" + errorCount + "，退出线程！");
                                    break;
                                }
                                errorCount++;
                            }
                        }
                        socket.close();
                        if (FdTcpClient.logger.isTraceEnabled()) {
                            FdTcpClient.logger.trace("No more data to receive. Thread will terminate.");
                        }
                        errorCount = 0;
                        wait4Data();
                    }
                    FdTcpClient.logger.info("消息接收子线程关闭！");
                }
            };
            this.receiverThread.start();
            sleep(100);
        }
    }

    public void openTesterPresentThread() throws InterruptedException {
        if (this.testerPresentThread == null) {
            this.testerPresentThread = new FdThread(this.prefixThreadName + ":TesterPresent-" + this.vin + "-" + getNextCount()) { // from class: com.geely.gnds.doip.client.tcp.FdTcpClient.2
                /* JADX WARN: Failed to check method for inline after forced processcom.geely.gnds.doip.client.tcp.FdTcpClient.access$1402(com.geely.gnds.doip.client.tcp.FdTcpClient, long):long */
                @Override // java.lang.Thread, java.lang.Runnable
                public void run() {
                    FdTesterReceiveListener listener;
                    FdTcpClient.this.testerTimeOut = false;
                    FdTcpClient.logger.info("守护子线程启动完成。");
                    Optional.ofNullable(FdTcpClient.this.txtLogger).ifPresent(logger2 -> {
                        logger2.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", "=== 守护子线程启动完成 ===".getBytes());
                    });
                    try {
                        int targetAddressInt = FdTcpClient.this.doipUtil.hexString2Int(DsaAddressConst.FUNCTIONAL_ADDRESSING);
                        long lastTime = System.currentTimeMillis();
                        while (isFdAlived()) {
                            long current = System.currentTimeMillis();
                            if (current - lastTime > 5000) {
                                FdTcpClient.this.testerTimeOut = true;
                            }
                            lastTime = current;
                            try {
                                if (FdTcpClient.this.noAckTesterCount.get() == 3) {
                                    FdTcpClient.logger.error("发送3E80连续3次没有收到8002,开始重连");
                                    FdTcpClient.this.resetSocket2(false);
                                }
                                listener = new FdTesterReceiveListener("3E80", targetAddressInt);
                                FdTcpClient.this.sendDoipMessage(true, DsaAddressConst.FUNCTIONAL_ADDRESSING, "3E80", (FdDoipTcpReceiveListener) listener, (IQopUdsDataQueuer) null);
                            } catch (Exception e) {
                                FdTcpClient.logger.error("发送3E80失败", e);
                            }
                            if (listener.isReceivedPosAck()) {
                                FdTcpClient.this.noAckTesterCount.set(0);
                                FdTcpClient.access$1402(FdTcpClient.this, current + 2000);
                                long sleepTime = (2000 + current) - System.currentTimeMillis();
                                wait4TesterPresent(sleepTime > 0 ? sleepTime : 1L);
                            } else {
                                int get = FdTcpClient.this.noAckTesterCount.incrementAndGet();
                                FdTcpClient.logger.error("发送3E80第【{}】次没有收到8002", Integer.valueOf(get));
                            }
                        }
                        FdTcpClient.logger.info("守护子线程关闭。");
                        Optional.ofNullable(FdTcpClient.this.txtLogger).ifPresent(logger3 -> {
                            logger3.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", "=== 守护子线程关闭 ===".getBytes());
                        });
                    } catch (DoipException e2) {
                        throw new RuntimeException(e2);
                    }
                }
            };
            this.testerPresentThread.setPriority(10);
            this.testerPresentThread.start();
            sleep(100);
        }
    }

    private void closeTesterPresentThread() {
        if (this.testerPresentThread != null) {
            this.testerPresentThread.close();
            logger.info("守护子线程被关闭了。" + Thread.currentThread().getName());
        }
    }

    private void awakeTesterPresent(boolean isTester) throws InterruptedException {
        if (!isTester) {
            long current = System.currentTimeMillis();
            if (current > this.testerPresentNext && this.testerPresentThread != null) {
                this.testerPresentThread.notify4Data();
                sleep(50);
                logger.info("3E80激活守护线程，当前时间");
                if (logger.isDebugEnabled()) {
                    logger.debug(MessageFormat.format(MSG_AWAKE, Long.valueOf(this.testerPresentNext), Long.valueOf(current), Long.valueOf(System.currentTimeMillis())));
                }
            }
        }
    }

    private void sendMessage(boolean isTester, int targetAddress, DoipInstructionType type, byte subFunction, String parameter, int maxRetries, int delay, FdDoipTcpReceiveListener listener, String doipInstructionCode, IQopUdsDataQueuer queuer, boolean is36) throws DoipException, InterruptedException {
        boolean needWaitResponse = false;
        if (listener != null) {
            needWaitResponse = this.util.isMessageWithResponse(type, subFunction);
        }
        if (needWaitResponse) {
            this.messageDispatcher.a(listener);
            String data = doipInstructionCode.length() > 10 ? doipInstructionCode.substring(0, 10) + "... ..." : doipInstructionCode;
            logger.debug(MessageFormat.format(MSG_RECEIVE_DATA, type.name(), data));
        }
        if (queuer != null) {
            queuer.checkQopSender();
        }
        byte[] message = this.util.getRequestMessage(type, subFunction, parameter);
        DoipTcpDiagnosticMessage doipTcpDiagnosticMessage = new DoipTcpDiagnosticMessage(this.sourceAddress, targetAddress, message);
        boolean sended = false;
        int retried = -1;
        while (!sended && retried < 1) {
            try {
                if (System.currentTimeMillis() < this.sendLastTime) {
                    sleep(10);
                }
                if (isTester) {
                    send(doipTcpDiagnosticMessage, false, false);
                } else {
                    send(doipTcpDiagnosticMessage, true, is36);
                }
                sended = true;
            } catch (Exception e) {
                logger.error("发送UDS指令异常", e);
                if (isTester) {
                    resetSocket2(false);
                    retried = 1;
                } else {
                    retried++;
                    try {
                        Thread.sleep(2000L);
                    } catch (InterruptedException e2) {
                    }
                }
            }
        }
        if (queuer != null) {
            queuer.releaseQopSender();
        }
        if (!sended) {
            logger.info("线程【{}-{}】sendMessage失败", Long.valueOf(Thread.currentThread().getId()), Thread.currentThread().getName());
            listener.removeListener();
            if (this.testerTimeOut) {
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00258));
            }
            throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00178));
        }
        if (needWaitResponse && sended) {
            listener.wait4Data();
        }
        awakeTesterPresent(isTester);
    }

    private void initSocket(boolean needRetry) throws InterruptedException, DoipException, IOException {
        int retry = 0;
        while (retry < ConstantEnum.FIVE.intValue()) {
            try {
                logger.info("initSocket开始------------------------------------------------>>>>>>>>>>>>>>>>>>>>>>>>>>");
                Optional.ofNullable(this.txtLogger).ifPresent(logger2 -> {
                    logger2.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", "======= 进行Socket初始化 ======= ".getBytes());
                });
                final DoipTcpRoutingActivationInfo info = new DoipTcpRoutingActivationInfo();
                FdDoipTcpReceiveListener activationListener = new FdDoipTcpReceiveListener(DoipInstructionType.RoutingActivation.getInstructionCode(), this.gatewayAddress) { // from class: com.geely.gnds.doip.client.tcp.FdTcpClient.3
                    @Override // com.geely.gnds.doip.client.tcp.FdDoipTcpReceiveListener
                    public void onReceiveRoutingActivation(DoipInstructionType type, DoipTcpRoutingActivationResponse message, String tcpServerIp, int tcpServerPort) {
                        FdTcpClient.this.initDoipAddress(FdTcpClient.this.socket.getInetAddress(), FdTcpClient.this.socket.getLocalPort());
                        info.setResponseCode(message.getResponseCode());
                        info.setResponseString(message.getResponseCodeAsString(info.getResponseCode()));
                    }
                };
                try {
                    this.testerTimeOut = false;
                    logger.info("socket建立开始");
                    initSocket();
                    logger.info("socket建立成功");
                    if (this.openPcapLog) {
                        InetAddress localAddress = this.socket.getLocalAddress();
                        PcapWrite pcapWrite = (PcapWrite) SpringUtils.getBean(PcapWrite.class);
                        this.pcapThread = pcapWrite.a(localAddress, this.pcapParam);
                        this.openPcapLog = false;
                    }
                    DoipTcpRoutingActivationRequest doipTcpRoutingActivationRequest = new DoipTcpRoutingActivationRequest(this.sourceAddress, this.gatewayAddress, -1L);
                    if (this.messageDispatcher == null) {
                        this.messageDispatcher = new DoIpMessageDispatcher(this.prefixThreadName + ":Dispatcher-" + getNextCount(), this.txtLogger);
                    }
                    this.messageDispatcher.a(activationListener);
                    startReceiverThread();
                    send(doipTcpRoutingActivationRequest, false, false);
                    logger.info("send结束");
                    activationListener.wait4Data();
                    logger.info("activationListener.wait4Data()结束");
                    if (info.getResponseCode() == 16) {
                        logger.info(MessageFormat.format(MSG_ACTIVATION, this.util.int2HexString(this.sourceAddress), this.vin, this.serverHostName));
                        openTesterPresentThread();
                        logger.info("initSocket结束------------------------------------------------>>>>>>>>>>>>>>>>>>>>>>>>>>");
                        this.noAckTesterCount.set(0);
                        return;
                    }
                    if (info.getResponseCode() == 32) {
                        throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00274));
                    }
                    throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00275) + info.getResponseString());
                } catch (Throwable e) {
                    logger.error(FdHelper.getExceptionAsString(e));
                    closeSocket(this.socket);
                    removeReceiveListener(activationListener);
                    String message = e.getMessage();
                    if (StringUtils.isNotBlank(message)) {
                        if (message.contains(LanguageEnum.NETWORK_UNREACHABLE.valueEn())) {
                            message = LanguageEnum.NETWORK_UNREACHABLE.valueByLanguage();
                        }
                        if (message.contains(LanguageEnum.CONNECTED_TIMED_OUT.valueEn())) {
                            message = LanguageEnum.CONNECTED_TIMED_OUT.valueByLanguage();
                        }
                    }
                    throw new DoipException(message);
                }
            } catch (DoipException e2) {
                retry++;
                if (needRetry) {
                    if (retry == 5) {
                        throw e2;
                    }
                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException interruptedException) {
                        interruptedException.printStackTrace();
                    }
                } else {
                    throw e2;
                }
            }
        }
    }

    private void initSocket() throws Exception {
        logger.info("initSocket的needTls:{}", Boolean.valueOf(this.needTls));
        if (this.needTls) {
            logger.info("Tls建立socket:{}", Boolean.valueOf(this.needTls));
            CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
            byte[] certByte = SingletonManager.getTlsCert();
            ByteArrayInputStream certInputStream = new ByteArrayInputStream(certByte);
            X509Certificate cert = (X509Certificate) certFactory.generateCertificate(certInputStream);
            certInputStream.close();
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, null);
            keyStore.setCertificateEntry("server", cert);
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init(keyStore);
            TrustManager[] trustAllCerts = {new X509TrustManager() { // from class: com.geely.gnds.doip.client.tcp.FdTcpClient.4
                @Override // javax.net.ssl.X509TrustManager
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }

                @Override // javax.net.ssl.X509TrustManager
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }

                @Override // javax.net.ssl.X509TrustManager
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }};
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, null);
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            this.socket = sslSocketFactory.createSocket(this.serverHostName, TLS_PORT);
            HostnameVerifier allHostsValid = new HostnameVerifier() { // from class: com.geely.gnds.doip.client.tcp.FdTcpClient.5
                @Override // javax.net.ssl.HostnameVerifier
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            this.socket.setKeepAlive(true);
            logger.info("Tls建立socket完成", Boolean.valueOf(this.needTls));
        } else {
            this.socket = new Socket();
            this.socket.setKeepAlive(true);
            this.socket.connect(new InetSocketAddress(this.serverHostName, this.doipPort), 650);
            this.socket.setSoTimeout(0);
            logger.info("socket完成", Boolean.valueOf(this.needTls));
        }
        InetAddress localAddress = this.socket.getLocalAddress();
        byte[] addressBytes = localAddress.getAddress();
        this.localIp = bytesToHex(addressBytes);
        logger.info("车辆socket完成localIP：{}", this.localIp);
    }

    private void removeReceiveListener(FdDoipTcpReceiveListener listenser) {
        if (this.messageDispatcher != null && listenser != null) {
            this.messageDispatcher.b(listenser);
        }
    }

    private void send(AbstractDoipMessage abstractDoipMessage, boolean needWriteTxt, boolean is36) throws IOException {
        long send;
        String content;
        byte[] message = abstractDoipMessage.getMessage();
        if (needWriteTxt) {
            String messageStr = this.doipUtil.bytesToHexString2(message, message.length);
            if (this.txtLogger != null) {
                if (is36) {
                    boolean sub = false;
                    if (messageStr.length() > 40) {
                        messageStr = messageStr.substring(0, 40);
                        sub = true;
                    }
                    content = "DOIP request:Message" + (sub ? "(截取前40位字符)" : ConstantEnum.EMPTY) + messageStr;
                } else {
                    content = "DOIP request:Message " + messageStr;
                }
                this.txtLogger.write(new Date(), TestLogTypeEnum.communication.name(), Long.valueOf(System.currentTimeMillis()), "Info", content.getBytes());
            }
        }
        initDoipAddress(this.socket.getInetAddress(), this.socket.getLocalPort());
        long start = System.currentTimeMillis();
        synchronized (this.sendLock) {
            send = System.currentTimeMillis();
            OutputStream stream = this.socket.getOutputStream();
            stream.write(abstractDoipMessage.getMessage());
            stream.flush();
        }
        long end = System.currentTimeMillis();
        if (!needWriteTxt) {
            logger.info("发送3E80开始start时间【{}】；send时间【{}】；发送3E80结束end时间【{}】；message【{}】", new Object[]{Long.valueOf(start), Long.valueOf(send), Long.valueOf(end), message});
        }
        if (end - start > 2000) {
            logger.error("start时间【{}】；send时间【{}】；end时间【{}】；message【{}】；不是守护线程【{}】；", new Object[]{Long.valueOf(start), Long.valueOf(send), Long.valueOf(end), message, Boolean.valueOf(needWriteTxt)});
        }
    }

    public Boolean isServerClose() throws IOException {
        try {
            this.socket.sendUrgentData(255);
            logger.info("socket状态 isServerClose【false】");
            return false;
        } catch (Exception e) {
            logger.info("socket状态 isServerClose【true】");
            return true;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void sleep(int millis) throws InterruptedException {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            logger.error("Sleep in thread with name \"" + Thread.currentThread().getName() + "\" has been interrupted");
            logger.error(FdHelper.getExceptionAsString(e));
        }
    }

    private synchronized void resetSocket(boolean flag) throws DoipException, IOException {
        this.openPcapLog = flag;
        logger.info("resetSocket------------------------------------------------>>>>>>>>>>>>>>>>>>>>>>>>>>");
        if (this.socket != null) {
            this.local = null;
            try {
                this.socket.close();
                this.socket = null;
                this.receiverThread.close();
                this.receiverThread = null;
            } catch (IOException e) {
                logger.error(e.getMessage());
                logger.error(FdHelper.getExceptionAsString(e));
            }
        }
        this.initSocket = true;
        try {
            initSocket(true);
            logger.info("resetSocket方法释放connectChecklock");
            TokenManager.getPool().execute(() -> {
                try {
                    DoipAddressManager.getInstance().getInetAddress();
                } catch (Exception e2) {
                    logger.error("更新网卡列表失败", e2);
                }
            });
        } finally {
            this.initSocket = false;
        }
    }

    public void resetSocket2(boolean flag) throws DoipException {
        this.openPcapLog = flag;
        logger.info("resetSocket2方法准备获取connectChecklock");
        synchronized (this.connectChecklock) {
            logger.info("resetSocket2方法获取到connectChecklock");
            if (this.socket != null) {
                this.local = null;
                try {
                    this.socket.close();
                    this.socket = null;
                    this.receiverThread.close();
                    this.receiverThread = null;
                } catch (IOException e) {
                    logger.error(e.getMessage());
                    logger.error(FdHelper.getExceptionAsString(e));
                }
                checkConnectStatus2();
            } else {
                checkConnectStatus2();
            }
        }
        logger.info("resetSocket2方法释放connectChecklock");
    }

    private void closeSocket(Socket socket) throws InterruptedException, IOException {
        if (this.receiverThread != null) {
            this.receiverThread.close();
            sleep(100);
            this.receiverThread = null;
        }
        if (socket != null) {
            try {
                boolean isMe = this.socket == socket;
                socket.close();
                if (isMe) {
                    this.socket = null;
                } else {
                    logger.error("socket不是我！");
                }
                this.local = null;
            } catch (IOException e) {
                logger.error(e.getMessage());
                logger.error(FdHelper.getExceptionAsString(e));
            }
        }
    }

    public void closeDoip() {
        if (!this.isVirtualVehicle) {
            closeTesterPresentThread();
            sleep(100);
            closeSocket(this.socket);
            if (this.messageDispatcher != null) {
                this.messageDispatcher.closeVehicle();
                this.messageDispatcher.close();
                sleep(100);
            }
            if (this.pcapLogger != null) {
                this.pcapLogger.close();
            }
        }
        if (this.fdXmlLogger != null) {
            this.fdXmlLogger.close();
        }
        if (this.txtLogger != null) {
            this.txtLogger.close();
        }
        if (this.fdDsaLogger != null) {
            this.fdDsaLogger.close();
        }
        logger.info(MessageFormat.format(MSG_CLOSE, this.vin, this.serverHostName));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void sendDoipMessage(boolean isTester, String targetAddress, String doipInstructionCode, FdDoipTcpReceiveListener listener, IQopUdsDataQueuer queuer) throws DoipException, InterruptedException, NumberFormatException {
        FdTcpClientServer socketByAddress = getSocketByAddress(targetAddress);
        if (socketByAddress == null) {
            sendDoipMessage(isTester, targetAddress, doipInstructionCode, "0", "500", listener, queuer, false);
        } else {
            socketByAddress.sendDoipMessage(isTester, targetAddress, doipInstructionCode, "0", "500", listener, queuer, false);
        }
    }

    public void sendDoipMessage(String targetAddress, String doipInstructionCode, FdDoipTcpReceiveListener listener, IQopUdsDataQueuer queuer, boolean is36) throws DoipException, InterruptedException, NumberFormatException {
        FdTcpClientServer socketByAddress = getSocketByAddress(targetAddress);
        if (socketByAddress == null) {
            sendDoipMessage(false, targetAddress, doipInstructionCode, "0", "500", listener, queuer, is36);
        } else {
            socketByAddress.sendDoipMessage(false, targetAddress, doipInstructionCode, "0", "500", listener, queuer, is36);
        }
    }

    private void sendDoipMessage(boolean isTester, String targetAddress, String doipInstructionCode, String maxRetriesStr, String delayStr, FdDoipTcpReceiveListener listener, IQopUdsDataQueuer queuer, boolean is36) throws DoipException, InterruptedException, NumberFormatException {
        String typeCode = doipInstructionCode.substring(0, 2);
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        String parameter = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(4);
        int intTargetAddress = this.util.hexString2Int(targetAddress);
        String errorKey = "Retries";
        String errorValue = maxRetriesStr;
        try {
            int maxRetries = Integer.parseInt(maxRetriesStr);
            int maxRetries2 = maxRetries < 0 ? 0 : maxRetries;
            errorKey = "Delay";
            errorValue = delayStr;
            int delay = Integer.parseInt(delayStr);
            DoipInstructionType type = this.util.getDoipInstructionType(typeCode);
            byte subFunction = sf.length() < 1 ? (byte) 0 : this.util.hexString2Byte(sf);
            sendMessage(isTester, intTargetAddress, type, subFunction, parameter, maxRetries2, delay, listener, doipInstructionCode, queuer, is36);
        } catch (Exception e) {
            throw new DoipException(MessageFormat.format(ERROR_PROP_VALUE, targetAddress, doipInstructionCode, errorKey, errorValue));
        }
    }

    /* JADX WARN: Finally extract failed */
    public boolean checkConnectStatus() throws DoipException {
        boolean isClose = false;
        logger.info("checkConnectStatus方法准备获取connectChecklock");
        synchronized (this.connectChecklock) {
            logger.info("checkConnectStatus方法获取到connectChecklock");
            try {
                try {
                    isClose = this.socket == null || this.socket.isClosed() || !this.socket.getKeepAlive();
                    if (isClose) {
                        this.initSocket = true;
                        int count = this.retryCount.get();
                        if (count == 0) {
                            this.connectTime = System.currentTimeMillis() / 1000;
                        }
                        logger.info("开始第{}次重连车辆", Integer.valueOf(count + 1));
                        long max = 900;
                        Object timeoutAutoClose = SingletonManager.getInstance().getGlobal("timeoutAutoCloseVINSecond", this.vin);
                        if (timeoutAutoClose != null) {
                            max = Long.parseLong(timeoutAutoClose.toString());
                        }
                        if ((System.currentTimeMillis() / 1000) - this.connectTime > max) {
                            this.retryCount.incrementAndGet();
                            SingletonManager.getInstance().tryConnectNot(this.vin);
                            ConnectedService connectedService = (ConnectedService) SpringUtils.getBean(ConnectedService.class);
                            connectedService.disconnect(this.username, this.vin);
                        } else {
                            logger.info("车辆连接断开,checkConnectStatus尝试连接车辆");
                            search();
                        }
                    }
                    this.initSocket = false;
                } catch (SocketException e) {
                    logger.error(e.getMessage());
                    logger.error(FdHelper.getExceptionAsString(e));
                    throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015), e);
                } catch (Exception e2) {
                    logger.error(e2.getMessage());
                    this.initSocket = false;
                }
            } catch (Throwable th) {
                this.initSocket = false;
                throw th;
            }
        }
        logger.info("checkConnectStatus方法释放connectChecklock");
        return isClose;
    }

    public boolean checkConnectStatus2() throws DoipException {
        boolean isClose = false;
        try {
            try {
                isClose = this.socket == null || this.socket.isClosed() || !this.socket.getKeepAlive();
                if (isClose) {
                    this.initSocket = true;
                    int count = this.retryCount.get();
                    if (count == 0) {
                        this.connectTime = System.currentTimeMillis() / 1000;
                    }
                    logger.info("开始第{}次重连车辆", Integer.valueOf(count + 1));
                    long max = 900;
                    Object timeoutAutoClose = SingletonManager.getInstance().getGlobal("timeoutAutoCloseVINSecond", this.vin);
                    if (timeoutAutoClose != null) {
                        max = Long.parseLong(timeoutAutoClose.toString());
                    }
                    if ((System.currentTimeMillis() / 1000) - this.connectTime > max) {
                        SingletonManager.getInstance().tryConnectNot(this.vin);
                        this.retryCount.incrementAndGet();
                        ConnectedService connectedService = (ConnectedService) SpringUtils.getBean(ConnectedService.class);
                        connectedService.disconnect(this.username, this.vin);
                    } else {
                        logger.info("车辆连接断开,checkConnectStatus2尝试连接车辆");
                        search();
                    }
                }
                this.initSocket = false;
            } catch (SocketException e) {
                logger.error(e.getMessage());
                logger.error(FdHelper.getExceptionAsString(e));
                throw new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00015), e);
            } catch (Exception e2) {
                e2.printStackTrace();
                this.initSocket = false;
            }
            return isClose;
        } catch (Throwable th) {
            this.initSocket = false;
            throw th;
        }
    }

    private void search() throws Exception {
        EhcacheClient ehcacheClient = (EhcacheClient) SpringUtils.getBean(EhcacheClient.class);
        long start = System.currentTimeMillis() / 1000;
        boolean success = false;
        VehicleDto vehicle = null;
        for (int i = ConstantEnum.TWO.intValue(); i >= 0; i--) {
            String key = String.valueOf(start - i);
            Set<String> vins = (Set) ehcacheClient.w(key);
            String keyVehicle = "vehicle" + key;
            List<VehicleDto> vehicleList = (List) ehcacheClient.w(keyVehicle);
            logger.info("缓存key：{},vins列表：{},vehicleList列表：{}", new Object[]{key, vins, vehicleList});
            if (vehicleList != null) {
                for (VehicleDto v : vehicleList) {
                    if (v.getVin().equals(this.vin) || v.getVin().equals("00000000000000000")) {
                        success = true;
                        vehicle = v;
                        break;
                    }
                }
            }
        }
        if (success) {
            if (vehicle != null) {
                try {
                    setServerHostName(vehicle.getIp());
                } catch (Exception e) {
                    this.retryCount.incrementAndGet();
                    throw e;
                }
            }
            initSocket(false);
            logger.info("checkConnectStatus成功连接车辆，或者车辆连接意外断开，自动重新连接成功。");
            SeqServiceImpl sequenceService = (SeqServiceImpl) SpringUtils.getBean(SeqServiceImpl.class);
            sequenceService.a(this.vehicleDto.getVin(), this.username, this.platformCode, this.isDsaVehicle);
            this.retryCount = new AtomicInteger(0);
            return;
        }
        this.retryCount.incrementAndGet();
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public boolean isClose() throws com.geely.gnds.doip.client.exception.DoipException {
        /*
            r3 = this;
            r0 = 0
            r4 = r0
            r0 = r3
            java.lang.Boolean r0 = r0.connectChecklock
            r1 = r0
            r5 = r1
            monitor-enter(r0)
            r0 = r3
            java.net.Socket r0 = r0.socket     // Catch: java.net.SocketException -> L2d java.lang.Throwable -> L4d
            if (r0 == 0) goto L24
            r0 = r3
            java.net.Socket r0 = r0.socket     // Catch: java.net.SocketException -> L2d java.lang.Throwable -> L4d
            boolean r0 = r0.isClosed()     // Catch: java.net.SocketException -> L2d java.lang.Throwable -> L4d
            if (r0 != 0) goto L24
            r0 = r3
            java.net.Socket r0 = r0.socket     // Catch: java.net.SocketException -> L2d java.lang.Throwable -> L4d
            boolean r0 = r0.getKeepAlive()     // Catch: java.net.SocketException -> L2d java.lang.Throwable -> L4d
            if (r0 != 0) goto L28
        L24:
            r0 = 1
            goto L29
        L28:
            r0 = 0
        L29:
            r4 = r0
            goto L48
        L2d:
            r6 = move-exception
            org.slf4j.Logger r0 = com.geely.gnds.doip.client.tcp.FdTcpClient.logger     // Catch: java.lang.Throwable -> L4d
            r1 = r6
            java.lang.String r1 = r1.getMessage()     // Catch: java.lang.Throwable -> L4d
            r0.error(r1)     // Catch: java.lang.Throwable -> L4d
            org.slf4j.Logger r0 = com.geely.gnds.doip.client.tcp.FdTcpClient.logger     // Catch: java.lang.Throwable -> L4d
            r1 = r6
            java.lang.String r1 = com.geely.gnds.doip.client.FdHelper.getExceptionAsString(r1)     // Catch: java.lang.Throwable -> L4d
            r0.error(r1)     // Catch: java.lang.Throwable -> L4d
            r0 = 1
            r4 = r0
        L48:
            r0 = r5
            monitor-exit(r0)     // Catch: java.lang.Throwable -> L4d
            goto L54
        L4d:
            r7 = move-exception
            r0 = r5
            monitor-exit(r0)     // Catch: java.lang.Throwable -> L4d
            r0 = r7
            throw r0
        L54:
            r0 = r4
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.doip.client.tcp.FdTcpClient.isClose():boolean");
    }

    public void cancelByUserException() {
        if (this.messageDispatcher != null) {
            this.messageDispatcher.cancelByUserException();
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0026  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public boolean isCloseNoLock() {
        /*
            r3 = this;
            r0 = 0
            r4 = r0
            r0 = r3
            boolean r0 = r0.isVirtualVehicle
            if (r0 == 0) goto Lb
            r0 = 0
            return r0
        Lb:
            r0 = r3
            java.net.Socket r0 = r0.socket     // Catch: java.net.SocketException -> L2f
            if (r0 == 0) goto L26
            r0 = r3
            java.net.Socket r0 = r0.socket     // Catch: java.net.SocketException -> L2f
            boolean r0 = r0.isClosed()     // Catch: java.net.SocketException -> L2f
            if (r0 != 0) goto L26
            r0 = r3
            java.net.Socket r0 = r0.socket     // Catch: java.net.SocketException -> L2f
            boolean r0 = r0.getKeepAlive()     // Catch: java.net.SocketException -> L2f
            if (r0 != 0) goto L2a
        L26:
            r0 = 1
            goto L2b
        L2a:
            r0 = 0
        L2b:
            r4 = r0
            goto L4a
        L2f:
            r5 = move-exception
            org.slf4j.Logger r0 = com.geely.gnds.doip.client.tcp.FdTcpClient.logger
            r1 = r5
            java.lang.String r1 = r1.getMessage()
            r0.error(r1)
            org.slf4j.Logger r0 = com.geely.gnds.doip.client.tcp.FdTcpClient.logger
            r1 = r5
            java.lang.String r1 = com.geely.gnds.doip.client.FdHelper.getExceptionAsString(r1)
            r0.error(r1)
            r0 = 1
            return r0
        L4a:
            r0 = r4
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: com.geely.gnds.doip.client.tcp.FdTcpClient.isCloseNoLock():boolean");
    }

    public String getUsername() {
        return this.username;
    }

    public void startPcap(PcapParam pcapParam) {
        InetAddress localAddress = this.socket.getLocalAddress();
        PcapWrite pcapWrite = (PcapWrite) SpringUtils.getBean(PcapWrite.class);
        this.pcapThread = pcapWrite.a(localAddress, pcapParam.getPath(), pcapParam.getVin(), pcapParam.getUsername());
    }

    public boolean isRecvFailed() {
        return this.recvFailed;
    }

    public File getTxtFile() {
        if (this.txtLogger == null) {
            return null;
        }
        return this.txtLogger.getTxtFile();
    }

    public File getPcapFile() {
        if (this.pcapThread == null) {
            return null;
        }
        return this.pcapThread.getPcapFile();
    }

    public void pcapFragment() {
        logger.info("pcap日志分片开始");
        PcapWrite pcapWrite = (PcapWrite) SpringUtils.getBean(PcapWrite.class);
        if (this.pcapThread != null) {
            pcapWrite.a(this.pcapThread);
        }
        InetAddress localAddress = this.socket.getLocalAddress();
        this.pcapThread = pcapWrite.a(localAddress, this.pcapParam.getPath(), this.pcapParam.getVin(), this.pcapParam.getUsername(), this.pcapParam.getStart() + "_" + this.pcapCount.incrementAndGet());
        logger.info("pcap日志分片结束");
    }

    public FdDoipTcpReceiveListener getIdleReceiveListener(String targetAddress) throws DoipException {
        logger.info("getIdleReceiveListener 【{}】", targetAddress);
        FdTcpClientServer socketByAddress = getSocketByAddress(targetAddress);
        if (socketByAddress == null) {
            if (this.messageDispatcher != null) {
                return this.messageDispatcher.getIdleReceiveListener();
            }
            throw new DoipException(MessageUtils.getMessage("Vehicle connection initialization exception"));
        }
        if (socketByAddress.messageDispatcher != null) {
            return socketByAddress.messageDispatcher.getIdleReceiveListener();
        }
        throw new DoipException(MessageUtils.getMessage("Vehicle connection initialization exception"));
    }

    public String getLocalIp() {
        return this.localIp;
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(b & 255);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString().trim();
    }

    public boolean isSwitch(String targetAddress) {
        FdTcpClientServer socketByAddress = getSocketByAddress(targetAddress);
        if (socketByAddress == null) {
            return false;
        }
        return true;
    }
}
