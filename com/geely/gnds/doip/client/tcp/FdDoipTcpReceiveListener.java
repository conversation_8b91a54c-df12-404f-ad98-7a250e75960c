package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.DoipInstructionType;
import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.exception.CancelByUserException;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.exception.DoipNegativeException;
import com.geely.gnds.doip.client.exception.DoipRetryException;
import com.geely.gnds.doip.client.exception.DoipTimeoutException;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessageNegAck;
import com.geely.gnds.doip.client.message.response.DoipTcpRoutingActivationResponse;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.ruoyi.common.utils.http.HttpUtils;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.enums.TesterErrorCodeEnum;
import com.geely.gnds.tester.enums.TesterNegativeResEnum;
import com.geely.gnds.tester.util.MessageUtils;
import java.net.Socket;
import java.text.MessageFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: FdDoipTcpReceiveListener.class */
public class FdDoipTcpReceiveListener {
    private static final String MESSAGE_FORMAT = "诊断仪(0x{2})从车辆(0x{3})收到{0}消息：{1}";
    private static final String MESSAGE_IGNORE_FORMAT = "诊断仪(0x{2})从车辆(0x{3})放弃了{0}消息：{1}";
    private static final String MESSAGE_NEG_ACK_FORMAT = "诊断仪(0x{2})从车辆(0x{3})收到{0}消息的负响应：{1}";
    private static final String MESSAGE_WAITING_FORMAT = "{0}指令开始等待接收从车辆(0x{1})发出的响应... ...";
    private static final int DELAYED_TIMEOUT = 6000;
    private int delayedTimeOut;
    private int totalTimeout;
    private long curentTotalTimeOut;
    private volatile long current;
    private DoipInstructionType type;
    private byte subFunction;
    private int targetAddress;
    private String targetAddressSting;
    private volatile Object awakeObject;
    private volatile boolean openDelay;
    private DoipException doipException;
    private DoIpMessageDispatcher dispatcher;
    private volatile boolean received;
    private DoipMessageContanier doipMessageContanier;
    private static final int ACTIVE_WAIT = 2000;
    private int waitTime;
    private int posAckTimeout;
    private volatile boolean receivedPosAck;
    private volatile Object awakePosAck;
    private final boolean recycled;
    private boolean closeVehicle;
    private boolean dsaLog;
    DoipUtil doipUtil;
    private volatile boolean needCheckSubFunction;
    private FdDsaLogger fdDsaLogger;
    private String send;
    private static final Logger logger = LoggerFactory.getLogger(FdDoipTcpReceiveListener.class);
    private static DoipUtil util = DoipUtil.getInstance();

    public boolean isRecycled() {
        return this.recycled;
    }

    public void setFdDsaLogger(FdDsaLogger fdDsaLogger) {
        this.fdDsaLogger = fdDsaLogger;
    }

    public FdDoipTcpReceiveListener(String doipInstructionCode, int targetAddress) throws DoipException {
        this.delayedTimeOut = DELAYED_TIMEOUT;
        this.totalTimeout = 1000000;
        this.curentTotalTimeOut = 0L;
        this.current = 0L;
        this.awakeObject = new Object();
        this.openDelay = false;
        this.doipException = null;
        this.dispatcher = null;
        this.received = false;
        this.doipMessageContanier = null;
        this.waitTime = ACTIVE_WAIT;
        this.posAckTimeout = 8000;
        this.receivedPosAck = false;
        this.awakePosAck = new Object();
        this.doipUtil = DoipUtil.getInstance();
        this.needCheckSubFunction = false;
        this.fdDsaLogger = null;
        this.type = util.getDoipInstructionType(doipInstructionCode.substring(0, 2));
        this.targetAddress = targetAddress;
        this.waitTime = ACTIVE_WAIT;
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        this.subFunction = sf.length() < 1 ? (byte) 0 : util.hexString2Byte(sf);
        this.recycled = false;
        this.closeVehicle = false;
    }

    public FdDoipTcpReceiveListener() {
        this.delayedTimeOut = DELAYED_TIMEOUT;
        this.totalTimeout = 1000000;
        this.curentTotalTimeOut = 0L;
        this.current = 0L;
        this.awakeObject = new Object();
        this.openDelay = false;
        this.doipException = null;
        this.dispatcher = null;
        this.received = false;
        this.doipMessageContanier = null;
        this.waitTime = ACTIVE_WAIT;
        this.posAckTimeout = 8000;
        this.receivedPosAck = false;
        this.awakePosAck = new Object();
        this.doipUtil = DoipUtil.getInstance();
        this.needCheckSubFunction = false;
        this.fdDsaLogger = null;
        this.recycled = true;
    }

    public void setDoipInfo(String doipInstructionCode, int targetAddress, DoipMessageContanier doipMessageContanier, String send, String targetAddressSting, boolean receivedPosAck) throws DoipException {
        this.type = util.getDoipInstructionType(doipInstructionCode.substring(0, 2));
        this.targetAddress = targetAddress;
        this.targetAddressSting = targetAddressSting;
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        this.subFunction = sf.length() < 1 ? (byte) 0 : util.hexString2Byte(sf);
        this.doipMessageContanier = doipMessageContanier;
        this.curentTotalTimeOut = 0L;
        this.current = 0L;
        this.openDelay = false;
        this.doipException = null;
        this.received = false;
        this.send = send;
        this.closeVehicle = false;
        this.receivedPosAck = false;
        this.needCheckSubFunction = false;
        this.dsaLog = false;
        this.receivedPosAck = receivedPosAck;
    }

    public void setDoipInfo(boolean dsaLog, String doipInstructionCode, int targetAddress, DoipMessageContanier doipMessageContanier, String send, String targetAddressSting, boolean receivedPosAck) throws DoipException {
        this.type = util.getDoipInstructionType(doipInstructionCode.substring(0, 2));
        this.targetAddress = targetAddress;
        this.targetAddressSting = targetAddressSting;
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        this.subFunction = sf.length() < 1 ? (byte) 0 : util.hexString2Byte(sf);
        this.doipMessageContanier = doipMessageContanier;
        this.curentTotalTimeOut = 0L;
        this.current = 0L;
        this.openDelay = false;
        this.doipException = null;
        this.received = false;
        this.send = send;
        this.closeVehicle = false;
        this.receivedPosAck = false;
        this.needCheckSubFunction = false;
        this.dsaLog = dsaLog;
        this.receivedPosAck = receivedPosAck;
    }

    public void setDoipInfo(String doipInstructionCode, int targetAddress, DoipMessageContanier doipMessageContanier, String send, int pClientVehicleMax, int p2ServerMax, int p4ServerMax, String targetAddressSting, boolean needCheckSubFunction) throws DoipException {
        setDoipInfo(doipInstructionCode, targetAddress, doipMessageContanier, send, pClientVehicleMax, p2ServerMax, p4ServerMax, targetAddressSting, needCheckSubFunction, 5000, false);
    }

    public void setDoipInfo(String doipInstructionCode, int targetAddress, DoipMessageContanier doipMessageContanier, String send, int pClientVehicleMax, int p2ServerMax, int p4ServerMax, String targetAddressSting, boolean needCheckSubFunction, int p2Client, boolean receivedPosAck) throws DoipException {
        this.type = util.getDoipInstructionType(doipInstructionCode.substring(0, 2));
        this.targetAddress = targetAddress;
        this.targetAddressSting = targetAddressSting;
        String sf = doipInstructionCode.length() < 4 ? "" : doipInstructionCode.substring(2, 4);
        this.subFunction = sf.length() < 1 ? (byte) 0 : util.hexString2Byte(sf);
        this.doipMessageContanier = doipMessageContanier;
        this.curentTotalTimeOut = 0L;
        this.current = 0L;
        this.openDelay = false;
        this.doipException = null;
        this.received = false;
        this.send = send;
        this.waitTime = p2ServerMax;
        this.posAckTimeout = pClientVehicleMax;
        this.totalTimeout = p4ServerMax;
        this.closeVehicle = false;
        this.needCheckSubFunction = needCheckSubFunction;
        this.dsaLog = false;
        logger.warn("发送{}的p4{}", send, Integer.valueOf(this.totalTimeout));
        this.delayedTimeOut = p2Client;
        this.receivedPosAck = receivedPosAck;
    }

    private void notify4Data(boolean receivedFlag) {
        synchronized (this.awakeObject) {
            if (receivedFlag) {
                this.received = true;
                this.awakeObject.notify();
            } else {
                this.awakeObject.notify();
            }
        }
    }

    public DoipException getLastDoipException() {
        return this.doipException;
    }

    public void setDispatcher(DoIpMessageDispatcher dispatcher) {
        this.dispatcher = dispatcher;
    }

    public boolean isDelayMessage(AbstractDoipMessage message) {
        if (message instanceof DoipTcpDiagnosticMessage) {
            byte[] data = ((DoipTcpDiagnosticMessage) message).getMessage();
            return data.length >= ConstantEnum.FIFTEEN.intValue() && data[12] == Byte.MAX_VALUE && data[14] == 120;
        }
        return false;
    }

    public boolean isRetriesMessage(AbstractDoipMessage message) {
        if (message instanceof DoipTcpDiagnosticMessage) {
            byte[] data = ((DoipTcpDiagnosticMessage) message).getMessage();
            return data.length >= ConstantEnum.FIFTEEN.intValue() && data[12] == Byte.MAX_VALUE && data[14] == 33;
        }
        return false;
    }

    public boolean isNegativeMessage(AbstractDoipMessage message) {
        if (message instanceof DoipTcpDiagnosticMessage) {
            byte[] data = ((DoipTcpDiagnosticMessage) message).getMessage();
            return data.length >= ConstantEnum.FIFTEEN.intValue() && data[12] == Byte.MAX_VALUE && this.type.getInstructionCode().equalsIgnoreCase(this.doipUtil.toHexString(data[13]));
        }
        return false;
    }

    public void dealDelayMessage(AbstractDoipMessage message) {
        this.openDelay = true;
        this.current = this.current == 0 ? System.currentTimeMillis() : this.current;
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }

    private synchronized boolean isTimeOutByTotal() {
        long now = System.currentTimeMillis();
        if (this.current > 0) {
            this.curentTotalTimeOut = (this.curentTotalTimeOut + now) - this.current;
        }
        this.current = now;
        return this.curentTotalTimeOut > ((long) this.totalTimeout);
    }

    public void onDoipTcpMessage(Socket socket, AbstractDoipMessage message) {
        String tcpServerIp = socket.getInetAddress().getHostAddress();
        int tcpServerPort = socket.getPort();
        boolean receivedFlag = (message instanceof DoipTcpDiagnosticMessageNegAck) || (message instanceof DoipTcpRoutingActivationResponse) || (message instanceof DoipTcpDiagnosticMessage);
        if (message instanceof DoipTcpRoutingActivationResponse) {
            onReceiveRoutingActivation(this.type, (DoipTcpRoutingActivationResponse) message, tcpServerIp, tcpServerPort);
        } else {
            onReceiveData(this.type, message, tcpServerIp, tcpServerPort);
        }
        if (message instanceof DoipTcpDiagnosticMessageNegAck) {
            DoipTcpDiagnosticMessageNegAck ack = (DoipTcpDiagnosticMessageNegAck) message;
            logger.error(MessageFormat.format(MESSAGE_NEG_ACK_FORMAT, this.type.name(), ack.getNackCodeAsString(ack.getAckCode()), util.int2HexString(ack.getSourceAddress()), util.int2HexString(ack.getTargetAddress())));
        }
        if (receivedFlag) {
            notify4Data(true);
        }
    }

    public void onReceiveData(DoipInstructionType type, AbstractDoipMessage message, String tcpServerIp, int tcpServerPort) {
        if (this.doipMessageContanier != null) {
            this.doipMessageContanier.setDoipMessage(message, type.getPosAckCode());
        }
    }

    public void onReceiveRoutingActivation(DoipInstructionType type, DoipTcpRoutingActivationResponse message, String tcpServerIp, int tcpServerPort) {
    }

    public void wait4Data() {
        try {
            if (this.recycled) {
                synchronized (this.awakePosAck) {
                    if (!this.receivedPosAck) {
                        try {
                            this.awakePosAck.wait(this.posAckTimeout);
                        } catch (InterruptedException interruptedException) {
                            interruptedException.printStackTrace();
                        }
                        if (!this.receivedPosAck) {
                            float time = this.posAckTimeout / 1000;
                            if ("zh-CN".equals(HttpUtils.getLanguage())) {
                                setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00162) + time + "秒");
                            } else {
                                setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00162) + time + " seconds");
                            }
                            removeListener();
                            return;
                        }
                    }
                }
            }
            synchronized (this.awakeObject) {
                if (!this.received) {
                    this.awakeObject.wait(this.waitTime);
                }
            }
        } catch (InterruptedException e) {
            logger.error(FdHelper.getExceptionAsString(e));
            this.doipException = new DoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00099), e);
        }
        if (this.closeVehicle) {
            removeListener();
            return;
        }
        if (this.received) {
            removeListener();
            return;
        }
        if (this.openDelay) {
            boolean waiting = (isTimeOutByTotal() || this.received) ? false : true;
            while (true) {
                if (!waiting) {
                    break;
                }
                this.openDelay = false;
                synchronized (this.awakeObject) {
                    try {
                        this.awakeObject.wait(this.delayedTimeOut);
                    } catch (InterruptedException e2) {
                        throw e2;
                    }
                }
                waiting = (isTimeOutByTotal() || this.received) ? false : true;
                if (!this.openDelay && !this.received) {
                    float time2 = this.delayedTimeOut / 1000;
                    if ("zh-CN".equals(HttpUtils.getLanguage())) {
                        setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00143) + time2 + "秒");
                    } else {
                        setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00143) + time2 + " seconds");
                    }
                }
            }
            if (!this.received) {
                float time3 = this.totalTimeout / 1000;
                if ("zh-CN".equals(HttpUtils.getLanguage())) {
                    setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00144) + time3 + "秒");
                } else {
                    setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00144) + time3 + " seconds");
                }
            }
        }
        if (!this.received) {
            float time4 = this.waitTime / 1000;
            if ("zh-CN".equals(HttpUtils.getLanguage())) {
                setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00145) + time4 + "秒");
            } else {
                setDoipException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.SG00145) + time4 + " seconds");
            }
        }
        removeListener();
    }

    private void setDoipException(String msg) {
        String msg2 = msg + "; " + MessageUtils.getMessage("ECU address") + ":" + this.targetAddressSting + "; " + MessageUtils.getMessage("command") + ":" + this.send;
        logger.error(msg2);
        if (this.doipException == null && !this.received) {
            this.doipException = new DoipTimeoutException(msg2);
        }
    }

    public void setRetryException(AbstractDoipMessage message) {
        byte[] data = message.getMessage();
        if (!this.type.getInstructionCode().equalsIgnoreCase(util.toHexString(data[ConstantEnum.THIRTEEN.intValue()]))) {
            logger.info("21响应不适用于本指令！");
            return;
        }
        if (this.doipException == null && !this.received) {
            this.doipException = new DoipRetryException(TesterErrorCodeEnum.formatMsg(TesterErrorCodeEnum.S50033));
        }
        notify4Data(true);
    }

    public void setNegativeException(String msg) {
        logger.error("收到负响应：" + msg);
        if (this.doipException == null && !this.received) {
            this.doipException = new DoipNegativeException(msg);
        }
        notify4Data(true);
    }

    public void handleNegativeRes(AbstractDoipMessage message) throws Exception {
        byte[] data = message.getMessage();
        String msg = "未定义的负响应";
        String messageStr = util.bytesToHexString2(data, data.length);
        int length = messageStr.length();
        String code = messageStr.substring(length - 2);
        TesterNegativeResEnum[] testerNegativeResEnumArrValues = TesterNegativeResEnum.values();
        int length2 = testerNegativeResEnumArrValues.length;
        int i = 0;
        while (true) {
            if (i >= length2) {
                break;
            }
            TesterNegativeResEnum testerNegativeResEnum = testerNegativeResEnumArrValues[i];
            if (!code.equals(testerNegativeResEnum.getCode())) {
                i++;
            } else {
                TesterErrorCodeEnum errorEnum = testerNegativeResEnum.getErrorEnum();
                msg = TesterErrorCodeEnum.formatMsg(errorEnum);
                break;
            }
        }
        if (this.fdDsaLogger != null && this.dsaLog) {
            messageStr = messageStr.length() <= 24 ? messageStr : messageStr.substring(24);
            this.fdDsaLogger.write(DsaLogTypeEnum.SEND_UDS.getValue(), "Complete Response：" + this.targetAddressSting + ConstantEnum.EMPTY + DoipUtil.getDsaLogCommand(messageStr));
        }
        if ("未定义的负响应".equals(msg)) {
            setNegativeException(msg + ";" + MessageUtils.getMessage("ECU address") + ":" + this.targetAddressSting + ";" + MessageUtils.getMessage("command") + ":" + this.send + ";" + MessageUtils.getMessage("receive response") + ":" + messageStr);
        } else {
            setNegativeException(msg + ";" + MessageUtils.getMessage("ECU address") + ":" + this.targetAddressSting + ";" + MessageUtils.getMessage("command") + ":" + this.send);
        }
    }

    public void handleRoutingActivationRes() {
        this.receivedPosAck = true;
        synchronized (this.awakeObject) {
            this.awakeObject.notify();
        }
    }

    public void handleNegAckRes(Socket socket, AbstractDoipMessage message) {
        synchronized (this.awakePosAck) {
            this.receivedPosAck = true;
            this.awakePosAck.notify();
        }
        onDoipTcpMessage(socket, message);
    }

    public void handlePosAckRes(AbstractDoipMessage message) {
        synchronized (this.awakePosAck) {
            this.receivedPosAck = true;
            this.awakePosAck.notify();
        }
    }

    protected void removeListener() {
        DoIpMessageDispatcher myDispatcher = this.dispatcher;
        if (myDispatcher != null) {
            myDispatcher.b(this);
        }
    }

    public boolean isMyResponse(AbstractDoipMessage message) {
        if (this.needCheckSubFunction) {
            return util.isResponse4DoipCheckSubFunction(message, this.targetAddress, this.type, this.subFunction);
        }
        return util.isResponse4Doip(message, this.targetAddress, this.type, this.subFunction);
    }

    public boolean isMyResponseAdress(AbstractDoipMessage message) {
        return util.isMyResponseAdress(message, this.targetAddress, this.type);
    }

    public void debugIgnore(AbstractDoipMessage message) {
        byte[] data = message.getMessage();
        boolean isRar = message instanceof DoipTcpRoutingActivationResponse;
        String ta = util.int2HexString(util.getTargetAddress(data));
        String sa = util.int2HexString(util.getSourceAddress(data));
        Logger logger2 = logger;
        Object[] objArr = new Object[4];
        objArr[0] = this.type.name();
        objArr[1] = util.toHexString(message.getMessage());
        objArr[2] = isRar ? sa : ta;
        objArr[3] = isRar ? ta : sa;
        logger2.info(MessageFormat.format(MESSAGE_IGNORE_FORMAT, objArr));
    }

    public void cancelByUserException() {
        this.doipException = new CancelByUserException();
        closeVehicle();
    }

    public void closeVehicle() {
        this.closeVehicle = true;
        synchronized (this.awakePosAck) {
            this.awakePosAck.notify();
        }
        notify4Data(false);
    }
}
