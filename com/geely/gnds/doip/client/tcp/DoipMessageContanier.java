package com.geely.gnds.doip.client.tcp;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.message.AbstractDoipMessage;
import com.geely.gnds.doip.client.message.DoipTcpDiagnosticMessageNegAck;
import com.geely.gnds.doip.client.txt.FdTxtLogger;
import com.geely.gnds.dsa.enums.DsaLogTypeEnum;
import com.geely.gnds.dsa.log.FdDsaLogger;
import com.geely.gnds.ruoyi.common.core.text.StrFormatter;
import com.geely.gnds.tester.enums.ConstantEnum;
import com.geely.gnds.tester.util.ObjectMapperUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

/* loaded from: DoipMessageContanier.class */
public class DoipMessageContanier {
    private static final Logger log = LoggerFactory.getLogger(DoipMessageContanier.class);
    private AbstractDoipMessage message;
    private String posCode;
    private List<AbstractDoipMessage> messages;
    private String sendData;
    private final String HEX_DIGITAL = "0123456789ABCDEF";
    private final char[] hexArray = "0123456789ABCDEF".toCharArray();
    private FdTxtLogger txtLogger = null;
    private FdDsaLogger fdDsaLogger = null;
    private Long startTime = Long.valueOf(System.currentTimeMillis());

    public void setTxtLogger(FdTxtLogger txtLogger) {
        this.txtLogger = txtLogger;
    }

    public void setFdDsaLogger(FdDsaLogger fdDsaLogger) {
        this.fdDsaLogger = fdDsaLogger;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setSendData(String sendData) {
        this.sendData = sendData;
    }

    public String getXmlMessageString() {
        byte[] mesData;
        String messageStr;
        if (this.message == null || (messageStr = bytesToHexString2((mesData = this.message.getMessage()), mesData.length)) == null || messageStr.length() <= 24) {
            return "";
        }
        return messageStr.length() > 54 ? messageStr.substring(24, 55) : messageStr.substring(24);
    }

    public String getFunctionalAddressingMessages() {
        if (CollectionUtils.isEmpty(this.messages)) {
            return "";
        }
        List list = new ArrayList(this.messages.size());
        for (int i = 0; i < this.messages.size(); i++) {
            byte[] mesData = this.messages.get(i).getMessage();
            String messageStr = bytesToHexString2(mesData, mesData.length);
            if (messageStr != null && messageStr.length() > 24) {
                String res = messageStr.length() > 54 ? messageStr.substring(24, 55) : messageStr.substring(24);
                list.add(res);
            }
        }
        return ObjectMapperUtils.obj2JsonStr(list);
    }

    public String getFunctionalAddressingMessagesAndAddress() {
        DoipUtil instance = DoipUtil.getInstance();
        if (CollectionUtils.isEmpty(this.messages)) {
            return "";
        }
        Map map = new HashMap(this.messages.size());
        for (int i = 0; i < this.messages.size(); i++) {
            byte[] mesData = this.messages.get(i).getMessage();
            String messageStr = bytesToHexString2(mesData, mesData.length);
            String sourceAddressHex = instance.getSourceAddressHex(mesData);
            if (messageStr != null && messageStr.length() > 24) {
                String res = messageStr.length() > 54 ? messageStr.substring(24, 55) : messageStr.substring(24);
                map.put(sourceAddressHex, res);
            }
        }
        return ObjectMapperUtils.obj2JsonStr(map);
    }

    public String getFunctionalAddressingResponse() throws DoipException {
        DoipUtil instance = DoipUtil.getInstance();
        if (this.messages == null) {
            return "[]";
        }
        List<Map> res = new ArrayList<>(this.messages.size());
        for (int i = 0; i < this.messages.size(); i++) {
            AbstractDoipMessage message = this.messages.get(i);
            byte[] mesData = message.getMessage();
            String messageStr = bytesToHexString2(mesData, mesData.length);
            if (!StringUtils.isBlank(messageStr)) {
                Object sourceAddressHex = instance.getSourceAddressHex(mesData);
                String messageStr2 = messageStr.length() <= 24 ? "未处理的" + messageStr : messageStr.substring(24);
                log.debug("收到message:::::{}", messageStr2);
                boolean success = messageStr2.startsWith(this.posCode);
                Map<String, Object> data = new HashMap<>(2);
                Map<String, Object> value = new HashMap<>(2);
                Map<String, Object> type = new HashMap<>(2);
                value.put("Data_Type", ConstantEnum.STRING);
                value.put("Data_Value", messageStr2);
                type.put("Data_Type", ConstantEnum.STRING);
                type.put("Data_Value", success ? "Positive" : "Negative");
                data.put("ECU_Response_value", value);
                data.put("ECU_Response_Type", type);
                data.put("ECU_address", sourceAddressHex);
                res.add(data);
            }
        }
        String obj2JsonStr = ObjectMapperUtils.obj2JsonStr(res);
        log.info("              getFunctionalAddressingResponse={}", obj2JsonStr);
        return obj2JsonStr;
    }

    public String getResponse(String subFunction) throws DoipException {
        if (this.message == null) {
            return StrFormatter.EMPTY_JSON;
        }
        byte[] mesData = this.message.getMessage();
        String messageStr = bytesToHexString2(mesData, mesData.length);
        if (StringUtils.isBlank(messageStr)) {
            return StrFormatter.EMPTY_JSON;
        }
        String messageStr2 = messageStr.length() <= 24 ? "未处理的" + messageStr : messageStr.substring(24);
        log.debug("收到message:::::{}", messageStr2);
        boolean success = messageStr2.startsWith(this.posCode);
        Map<String, Object> data = new HashMap<>(2);
        Map<String, Object> value = new HashMap<>(2);
        Map<String, Object> type = new HashMap<>(2);
        value.put("Data_Type", ConstantEnum.STRING);
        value.put("Data_Value", messageStr2);
        type.put("Data_Type", ConstantEnum.STRING);
        type.put("Data_Value", success ? "Positive" : "Negative");
        data.put("ECU_Response_value", value);
        data.put("ECU_Response_Type", type);
        String obj2JsonStr = ObjectMapperUtils.obj2JsonStr(data);
        log.debug("              getResponse={}", obj2JsonStr);
        return obj2JsonStr;
    }

    public void setDoipMessage(AbstractDoipMessage message, String posCode) {
        this.message = message;
        this.posCode = posCode;
    }

    public void setDoipMessageFun(AbstractDoipMessage message, String posCode) {
        DoipUtil instance = DoipUtil.getInstance();
        if (this.messages == null) {
            this.messages = new ArrayList(5);
        }
        Optional.ofNullable(this.fdDsaLogger).ifPresent(logger -> {
            byte[] mesData = message.getMessage();
            String sourceAddressHex = instance.getSourceAddressHex(mesData);
            long end = System.currentTimeMillis();
            String messageStr = bytesToHexString2(mesData, mesData.length);
            String messageStr2 = messageStr.length() <= 24 ? messageStr : messageStr.substring(24);
            logger.write(DsaLogTypeEnum.SEND_UDS.getValue(), "Tester -> 1FFF " + this.sendData);
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "Complete Response：" + sourceAddressHex + ConstantEnum.EMPTY + DoipUtil.getDsaLogCommand(messageStr2));
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "Time stamp：" + end);
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "Time between request and response(P2 time)：" + (end - this.startTime.longValue()) + " ms");
            logger.write(DsaLogTypeEnum.ORDINARY_TEXT.getValue(), "");
        });
        this.messages.add(message);
        this.posCode = posCode;
    }

    public String getPosCode() {
        return this.posCode;
    }

    public boolean isDiagnosticMessageNegAck() {
        return this.message instanceof DoipTcpDiagnosticMessageNegAck;
    }

    private String bytesToHexString2(byte[] bytes, int count) {
        if (bytes.length == 0 || count == 0) {
            return "";
        }
        if (bytes.length < count) {
            count = bytes.length;
        }
        char[] hexChars = new char[count * 2];
        for (int j = 0; j < count; j++) {
            int v = bytes[j] & 255;
            hexChars[j * 2] = this.hexArray[v >>> 4];
            hexChars[(j * 2) + 1] = this.hexArray[v & 15];
        }
        return new String(hexChars);
    }
}
