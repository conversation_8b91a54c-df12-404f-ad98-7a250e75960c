package com.geely.gnds.doip.client.udp;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.message.DoipUdpVehicleAnnouncementMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: FdDoipUdpReceiver.class */
public class FdDoipUdpReceiver implements FdDoipUdpReceiveListener {
    private static final Logger logger = LoggerFactory.getLogger(FdDoipUdpReceiver.class);
    private final DoipUtil util = DoipUtil.getInstance();
    private volatile boolean received = false;

    @Override // com.geely.gnds.doip.client.udp.FdDoipUdpReceiveListener
    public void onDoipUdpVehicleAnnouncementMessage(DoipUdpVehicleAnnouncementMessage message, String udpServerIp, int udpServerPort) throws Exception {
        logger.info(this.util.toHexString(message.getMessage()));
    }

    public void m() {
        this.received = true;
    }

    public boolean isReceived() {
        return this.received;
    }
}
