package com.geely.gnds.doip.client.udp;

import com.geely.gnds.doip.client.DoipUtil;
import com.geely.gnds.doip.client.FdHelper;
import com.geely.gnds.doip.client.FdThread;
import com.geely.gnds.doip.client.IFdPcapLogger;
import com.geely.gnds.doip.client.exception.DoipException;
import com.geely.gnds.doip.client.exception.HeaderTooShort;
import com.geely.gnds.doip.client.exception.IncorrectPatternFormat;
import com.geely.gnds.doip.client.exception.InvalidPayloadLength;
import com.geely.gnds.doip.client.exception.InvalidPayloadType;
import com.geely.gnds.doip.client.message.AbstractDoipUdpMessage;
import com.geely.gnds.doip.client.message.DoipUdpVehicleAnnouncementMessage;
import com.geely.gnds.doip.client.message.request.DoipUdpVehicleIdentRequest;
import com.geely.gnds.doip.client.message.request.DoipUdpVehicleIdentRequestWithEid;
import com.geely.gnds.doip.client.message.request.DoipUdpVehicleIdentRequestWithVin;
import com.geely.gnds.doip.client.message.request.DoipUdpVehicleSearchRequest;
import com.geely.gnds.doip.client.pcap.DoipAddress;
import com.geely.gnds.doip.client.pcap.DoipAddressManager;
import com.geely.gnds.ruoyi.common.utils.DateUtils;
import com.geely.gnds.tester.cache.TokenManager;
import com.geely.gnds.tester.seq.SingletonManager;
import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.MulticastSocket;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: UdpClient.class */
public class UdpClient {
    private static volatile boolean cq = false;
    private static final Logger logger = LoggerFactory.getLogger(UdpClient.class);
    private final DoipAddress cr;
    private DoipAddress local;
    private static final int ct = 2000;
    private final IFdPcapLogger pcapLogger;
    private volatile int cs = ct;
    private DoipAddressManager cu = DoipAddressManager.getInstance();
    private SingletonManager cv = SingletonManager.getInstance();
    private final DoipUtil util = DoipUtil.getInstance();

    public UdpClient(String broadcast, int doipPort, IFdPcapLogger pcapLogger) throws UnknownHostException {
        this.cr = new DoipAddress(Inet4Address.getByName(broadcast), doipPort, broadcast, this.util.MAC_BROADCAST);
        this.pcapLogger = pcapLogger;
    }

    public void setUdpSockTimeout(int sockTimeout) {
        this.cs = sockTimeout;
    }

    public void a(FdDoipUdpReceiver listener) {
        a(new DoipUdpVehicleIdentRequest(), listener);
    }

    public void a(String vin, FdDoipUdpReceiver listener) {
        a(new DoipUdpVehicleIdentRequestWithVin(vin.toUpperCase().getBytes()), listener);
    }

    public void b(String eid, FdDoipUdpReceiver listener) {
        byte[] eidData = this.util.hexString2Bytes(eid);
        a(new DoipUdpVehicleIdentRequestWithEid(eidData), listener);
    }

    private void a(AbstractDoipUdpMessage message, FdDoipUdpReceiver listener) {
        MulticastSocket udpSocket = null;
        logger.info("开始查找广播中的车辆... ...");
        try {
            try {
                udpSocket = new MulticastSocket(this.cr.getPort());
                udpSocket.setBroadcast(true);
                udpSocket.setSoTimeout(ct);
                long now = System.currentTimeMillis();
                long timeout = now + 2000;
                while (now < timeout) {
                    a(udpSocket, listener);
                    now = System.currentTimeMillis();
                }
                b(listener);
                if (udpSocket != null) {
                    udpSocket.close();
                }
            } catch (SocketTimeoutException e) {
                b(listener);
                if (udpSocket != null) {
                    udpSocket.close();
                }
            } catch (Exception e2) {
                logger.error("查找广播中车辆失败!", e2);
                if (udpSocket != null) {
                    udpSocket.close();
                }
            }
        } catch (Throwable th) {
            if (udpSocket != null) {
                udpSocket.close();
            }
            throw th;
        }
    }

    private void b(FdDoipUdpReceiver listener) {
        Map<String, Long> vinMap = SingletonManager.vehicleConnectedTimeMap();
        boolean send = true;
        Long queryTime = SingletonManager.getQueryTime();
        long now = System.currentTimeMillis();
        logger.info("广播中没有找到车辆,用户主动搜车时间【{}】,车辆连接列表时间【{}】", queryTime, vinMap);
        if ((queryTime == null || now - queryTime.longValue() >= 30000) && vinMap.size() > 0) {
            for (Map.Entry<String, Long> entry : vinMap.entrySet()) {
                logger.info("广播中没有找到车辆!开始主动搜车");
                Long value = entry.getValue();
                if (now - value.longValue() > 15000) {
                    logger.info("vin【{}】连车时间小于15s");
                    send = false;
                }
            }
        }
        logger.info("广播中没有找到车辆 主动搜车？【{}】", Boolean.valueOf(send));
        if (send) {
            for (int retry = 1; !listener.isReceived() && retry > 0; retry--) {
                logger.info("广播中没有找到车辆!开始主动搜车");
                DoipUdpVehicleSearchRequest doipUdpVehicleSearchRequest = new DoipUdpVehicleSearchRequest();
                TokenManager.getPool().execute(() -> {
                    logger.info("发送UDP消息：" + this.util.toHexString(doipUdpVehicleSearchRequest.getMessage()));
                    try {
                        a(doipUdpVehicleSearchRequest, (FdDoipUdpReceiveListener) listener);
                    } catch (Exception e) {
                        logger.error("主动搜车异常:", e);
                    }
                });
            }
        }
    }

    private void sleep(int millis) throws InterruptedException {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            logger.info("Sleep in thread with name \"" + Thread.currentThread().getName() + "\" has been interrupted");
            logger.info(FdHelper.getExceptionAsString(e));
        }
    }

    public void stop() {
    }

    private void a(DatagramSocket socket, FdDoipUdpReceiveListener listener) throws DoipException, IOException {
        byte[] data = new byte[DoipUtil.MAX_BYTE_ARRAY_SIZE];
        DatagramPacket packet = new DatagramPacket(data, data.length);
        socket.receive(packet);
        byte[] receivedData = Arrays.copyOf(packet.getData(), packet.getLength());
        DoipAddress target = this.cr;
        Date now = new Date();
        try {
            try {
                try {
                    try {
                        InetAddress serverAddress = packet.getAddress();
                        String udpServerIp = serverAddress.getHostAddress();
                        AbstractDoipUdpMessage message = AbstractDoipUdpMessage.b(receivedData);
                        if (message instanceof DoipUdpVehicleAnnouncementMessage) {
                            logger.info("收到车辆消息：" + this.util.byteArrayToHexString(receivedData));
                            listener.onDoipUdpVehicleAnnouncementMessage((DoipUdpVehicleAnnouncementMessage) message, udpServerIp, 13400);
                        }
                        if (this.pcapLogger != null) {
                            this.pcapLogger.writeUdpReceived(now, receivedData, target, this.local);
                        }
                    } catch (IncorrectPatternFormat e) {
                        throw new DoipException(e);
                    } catch (InvalidPayloadLength e2) {
                        logger.error(FdHelper.getExceptionAsString(e2));
                        throw new DoipException(e2);
                    }
                } catch (InvalidPayloadType e3) {
                    throw new DoipException(e3);
                } catch (Exception e4) {
                    logger.error("接收数据异常", e4);
                    if (this.pcapLogger != null) {
                        this.pcapLogger.writeUdpReceived(now, receivedData, target, this.local);
                    }
                }
            } catch (HeaderTooShort e5) {
                throw new DoipException(e5);
            }
        } catch (Throwable th) {
            if (this.pcapLogger != null) {
                this.pcapLogger.writeUdpReceived(now, receivedData, target, this.local);
            }
            throw th;
        }
    }

    /* JADX WARN: Finally extract failed */
    private void a(AbstractDoipUdpMessage message) {
        DatagramSocket socket = null;
        if (cq) {
            return;
        }
        try {
            try {
                cq = true;
                byte[] data = message.getMessage();
                DatagramPacket packet = new DatagramPacket(data, data.length, this.cr.getAddress(), this.cr.getPort());
                for (InetAddress da : this.cu.getCacheInetAddress()) {
                    InetSocketAddress inetAddr = new InetSocketAddress(da, 0);
                    try {
                        socket = new DatagramSocket(inetAddr);
                        socket.setSoTimeout(this.cs);
                        socket.send(packet);
                        InetAddress localAddress = socket.getLocalAddress();
                        this.local = new DoipAddress(localAddress, 0, localAddress.getHostAddress(), this.util.MAC_LOCAL);
                        logger.info("开始查找网卡：" + this.local.getIp() + "下的车辆... ...");
                    } catch (Exception e) {
                        logger.error("查找车辆失败！", e);
                    }
                }
                cq = false;
                if (socket != null) {
                    socket.close();
                }
            } catch (Exception e2) {
                logger.error("主动搜车获取网卡列表失败", e2);
                cq = false;
                if (socket != null) {
                    socket.close();
                }
            }
        } catch (Throwable th) {
            cq = false;
            if (socket != null) {
                socket.close();
            }
            throw th;
        }
    }

    private void a(DoipUdpVehicleSearchRequest message, FdDoipUdpReceiveListener listener) {
        if (cq) {
            return;
        }
        Object lock = new Object();
        try {
            try {
                cq = true;
                byte[] data = message.getMessage();
                DatagramPacket packet = new DatagramPacket(data, data.length, this.cr.getAddress(), this.cr.getPort());
                for (InetAddress da : this.cu.getCacheInetAddress()) {
                    DatagramSocket socket = null;
                    String ip = null;
                    InetSocketAddress inetAddr = new InetSocketAddress(da, 0);
                    try {
                        try {
                            socket = new DatagramSocket(inetAddr);
                            socket.setSoTimeout(this.cs);
                            InetAddress localAddress = socket.getLocalAddress();
                            ip = localAddress.getHostAddress();
                            logger.info("基于IP(" + ip + ")发送UDP消息：" + this.util.toHexString(message.getMessage()) + System.currentTimeMillis());
                            a(lock, socket, listener, this.cs);
                            socket.send(packet);
                            synchronized (lock) {
                                lock.wait();
                            }
                            logger.info("收到IP(" + ip + ")的UDP消息的时间：" + System.currentTimeMillis());
                            this.local = new DoipAddress(localAddress, 0, localAddress.getHostAddress(), this.util.MAC_LOCAL);
                            if (socket != null) {
                                socket.close();
                            }
                        } catch (SocketTimeoutException e) {
                            logger.info("网卡：" + ip + "下的车辆搜索结束！");
                            if (socket != null) {
                                socket.close();
                            }
                        } catch (Exception e2) {
                            logger.error("查找车辆失败！", e2);
                            if (socket != null) {
                                socket.close();
                            }
                        }
                    } catch (Throwable th) {
                        if (socket != null) {
                            socket.close();
                        }
                        throw th;
                    }
                }
                cq = false;
            } catch (Exception e3) {
                logger.error("主动给搜车异常:", e3);
                cq = false;
            }
        } catch (Throwable th2) {
            cq = false;
            throw th2;
        }
    }

    private void a(final Object lock, final DatagramSocket socket, final FdDoipUdpReceiveListener listener, final long timeoutDuration) throws InterruptedException {
        FdThread thread = new FdThread("DoIP-UDP-Search:" + System.currentTimeMillis()) { // from class: com.geely.gnds.doip.client.udp.UdpClient.1
            @Override // java.lang.Thread, java.lang.Runnable
            public void run() {
                try {
                    UdpClient.this.a(socket, listener, timeoutDuration);
                } catch (SocketTimeoutException e) {
                    UdpClient.logger.debug("主动搜车结束！");
                } catch (Exception e2) {
                    UdpClient.logger.error("接收UDP消息失败！", e2);
                }
                synchronized (lock) {
                    lock.notify();
                }
            }
        };
        thread.start();
        sleep(10);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void a(DatagramSocket socket, FdDoipUdpReceiveListener listener, long timeoutDuration) throws DoipException, IOException {
        long now = System.currentTimeMillis();
        long timeout = now + timeoutDuration;
        while (now < timeout) {
            a(socket, listener);
            now = System.currentTimeMillis();
        }
        Date end = new Date();
        logger.info("搜车从 {} 开始, 于 {} 结束，耗时 {}毫秒 ", new Object[]{new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS).format(new Date(now)), new SimpleDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS_SSS).format(end), Long.valueOf(end.getTime() - now)});
    }
}
