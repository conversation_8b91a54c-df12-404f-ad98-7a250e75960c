package com.geely.gnds.doip.client;

/* loaded from: DoipTcpStreamBufferListener.class */
public interface DoipTcpStreamBufferListener {
    void onHeaderIncorrectPatternFormat();

    void onHeaderMessageTooLarge();

    void onHeaderUnknownPayloadType();

    void onHeaderInvalidPayloadLength();

    void onPayloadCompleted(byte[] bArr, int i, byte[] bArr2);

    void onShredderCompleted(long j);
}
