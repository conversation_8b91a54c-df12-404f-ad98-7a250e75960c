package com.geely.gnds.doip.client;

import com.geely.gnds.doip.client.pcap.DoipAddress;
import java.util.Date;

/* loaded from: IFdPcapLogger.class */
public interface IFdPcapLogger {
    void write(Date date, byte[] bArr, <PERSON><PERSON><PERSON><PERSON><PERSON> doipAddress, <PERSON>ip<PERSON>ddress doipAddress2, boolean z);

    void reset(Date date, DoipAddress doipAddress, DoipAddress doipAddress2, String str);

    void writeUdpSended(Date date, byte[] bArr, <PERSON>ip<PERSON>ddress doipAddress, DoipAddress doipAddress2);

    void writeUdpReceived(Date date, byte[] bArr, <PERSON><PERSON><PERSON>dd<PERSON> doipAddress, <PERSON><PERSON><PERSON><PERSON>ress doipAddress2);

    void close();
}
