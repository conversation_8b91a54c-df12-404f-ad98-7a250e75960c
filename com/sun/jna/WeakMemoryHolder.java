package com.sun.jna;

import java.lang.ref.Reference;
import java.lang.ref.ReferenceQueue;
import java.lang.ref.WeakReference;
import java.util.IdentityHashMap;

/* loaded from: jna-5.12.1.jar:com/sun/jna/WeakMemoryHolder.class */
public class WeakMemoryHolder {
    ReferenceQueue<Object> referenceQueue = new ReferenceQueue<>();
    IdentityHashMap<Reference<Object>, Memory> backingMap = new IdentityHashMap<>();

    public synchronized void put(Object o, Memory m) {
        clean();
        Reference<Object> reference = new WeakReference<>(o, this.referenceQueue);
        this.backingMap.put(reference, m);
    }

    public synchronized void clean() {
        Reference referencePoll = this.referenceQueue.poll();
        while (true) {
            Reference ref = referencePoll;
            if (ref != null) {
                this.backingMap.remove(ref);
                referencePoll = this.referenceQueue.poll();
            } else {
                return;
            }
        }
    }
}
